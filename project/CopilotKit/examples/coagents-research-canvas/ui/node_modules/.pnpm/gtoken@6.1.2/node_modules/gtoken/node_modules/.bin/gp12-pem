#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/merge/project/CopilotKit/examples/coagents-research-canvas/ui/node_modules/.pnpm/google-p12-pem@4.0.1/node_modules/google-p12-pem/build/src/bin/node_modules:/merge/project/CopilotKit/examples/coagents-research-canvas/ui/node_modules/.pnpm/google-p12-pem@4.0.1/node_modules/google-p12-pem/build/src/node_modules:/merge/project/CopilotKit/examples/coagents-research-canvas/ui/node_modules/.pnpm/google-p12-pem@4.0.1/node_modules/google-p12-pem/build/node_modules:/merge/project/CopilotKit/examples/coagents-research-canvas/ui/node_modules/.pnpm/google-p12-pem@4.0.1/node_modules/google-p12-pem/node_modules:/merge/project/CopilotKit/examples/coagents-research-canvas/ui/node_modules/.pnpm/google-p12-pem@4.0.1/node_modules:/merge/project/CopilotKit/examples/coagents-research-canvas/ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/merge/project/CopilotKit/examples/coagents-research-canvas/ui/node_modules/.pnpm/google-p12-pem@4.0.1/node_modules/google-p12-pem/build/src/bin/node_modules:/merge/project/CopilotKit/examples/coagents-research-canvas/ui/node_modules/.pnpm/google-p12-pem@4.0.1/node_modules/google-p12-pem/build/src/node_modules:/merge/project/CopilotKit/examples/coagents-research-canvas/ui/node_modules/.pnpm/google-p12-pem@4.0.1/node_modules/google-p12-pem/build/node_modules:/merge/project/CopilotKit/examples/coagents-research-canvas/ui/node_modules/.pnpm/google-p12-pem@4.0.1/node_modules/google-p12-pem/node_modules:/merge/project/CopilotKit/examples/coagents-research-canvas/ui/node_modules/.pnpm/google-p12-pem@4.0.1/node_modules:/merge/project/CopilotKit/examples/coagents-research-canvas/ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../google-p12-pem@4.0.1/node_modules/google-p12-pem/build/src/bin/gp12-pem.js" "$@"
else
  exec node  "$basedir/../../../../../google-p12-pem@4.0.1/node_modules/google-p12-pem/build/src/bin/gp12-pem.js" "$@"
fi
