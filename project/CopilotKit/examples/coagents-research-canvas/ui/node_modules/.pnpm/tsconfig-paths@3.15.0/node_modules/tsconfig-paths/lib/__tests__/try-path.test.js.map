{"version": 3, "file": "try-path.test.js", "sourceRoot": "", "sources": ["../../src/__tests__/try-path.test.ts"], "names": [], "mappings": ";;AAAA,wCAA4C;AAC5C,6BAA4B;AAE5B,QAAQ,CAAC,eAAe,EAAE;IACxB,IAAM,qBAAqB,GAAG;QAC5B;YACE,OAAO,EAAE,mBAAmB;YAC5B,KAAK,EAAE,CAAC,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;SACzD;QACD,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE;QAC3E,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE;KACpE,CAAC;IACF,IAAM,oCAAoC,GAAG;QAC3C;YACE,OAAO,EAAE,QAAQ;YACjB,KAAK,EAAE,CAAC,IAAA,WAAI,EAAC,WAAW,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;SAC/C;QACD;YACE,OAAO,EAAE,GAAG;YACZ,KAAK,EAAE,CAAC,IAAA,WAAI,EAAC,WAAW,EAAE,KAAK,CAAC,CAAC;SAClC;KACF,CAAC;IACF,EAAE,CAAC,sDAAsD,EAAE;QACzD,IAAM,MAAM,GAAG,IAAA,wBAAa,EAC1B,CAAC,KAAK,EAAE,KAAK,CAAC,EACd,qBAAqB,EACrB,oBAAoB,CACrB,CAAC;QACF,uCAAuC;QACvC,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,iEAAiE,EAAE;QACpE,IAAM,MAAM,GAAG,IAAA,wBAAa,EAC1B,CAAC,KAAK,EAAE,KAAK,CAAC,EACd;YACE;gBACE,OAAO,EAAE,mBAAmB;gBAC5B,KAAK,EAAE,CAAC,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;aACzD;YACD;gBACE,OAAO,EAAE,WAAW;gBACpB,KAAK,EAAE,CAAC,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;aAClD;SACF,EACD,kBAAkB,CACnB,CAAC;QACF,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,oDAAoD,EAAE;QACvD,IAAM,MAAM,GAAG,IAAA,wBAAa,EAC1B,CAAC,KAAK,EAAE,MAAM,CAAC,EACf,qBAAqB,EACrB,kCAAkC,CACnC,CAAC;QACF,6BAA6B;QAC7B,2BAA2B;QAC3B,6EAA6E;QAC7E,MAAM;QACN,yBAAyB;QACzB,gEAAgE;QAChE,OAAO;QACP,MAAM;QACN,yBAAyB;QACzB,iEAAiE;QACjE,OAAO;QACP,MAAM;QACN,uBAAuB;QACvB,6EAA6E;QAC7E,OAAO;QACP,MAAM;QACN,qBAAqB;QACrB,yEAAyE;QACzE,OAAO;QACP,MAAM;QACN,qBAAqB;QACrB,0EAA0E;QAC1E,OAAO;QACP,WAAW;QACX,sEAAsE;QACtE,8EAA8E;QAC9E,+EAA+E;QAC/E,MAAM;QACN,uBAAuB;QACvB,sEAAsE;QACtE,OAAO;QACP,MAAM;QACN,qBAAqB;QACrB,kEAAkE;QAClE,OAAO;QACP,MAAM;QACN,qBAAqB;QACrB,mEAAmE;QACnE,OAAO;QACP,MAAM;QACN,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;YACrB,sBAAsB;YACtB,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE;YACvE;gBACE,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC;aACzD;YACD;gBACE,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC;aAC1D;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,cAAc,CAAC;aACtE;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC;aAClE;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,CAAC;aACnE;YACD,MAAM;YACN,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE;YAChE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE;YACxE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,EAAE;YACzE;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc,CAAC;aAC/D;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC;aAC3D;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,CAAC;aAC5D;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4CAA4C,EAAE;QAC/C,IAAM,MAAM,GAAG,IAAA,wBAAa,EAC1B,CAAC,KAAK,CAAC,EACP,oCAAoC,EACpC,YAAY,CACb,CAAC;QACF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC;YACrB,UAAU;YACV;gBACE,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,KAAK,EAAE,WAAW,CAAC;gBAC3C,IAAI,EAAE,MAAM;aACb;YACD;gBACE,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,KAAK,EAAE,cAAc,CAAC;gBAC9C,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,cAAc,CAAC;gBAC3D,IAAI,EAAE,SAAS;aAChB;YACD;gBACE,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,CAAC;gBACvD,IAAI,EAAE,OAAO;aACd;YACD,MAAM;YACN;gBACE,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,KAAK,CAAC;gBAC9B,IAAI,EAAE,MAAM;aACb;YACD;gBACE,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,QAAQ,CAAC;gBACjC,IAAI,EAAE,WAAW;aAClB;YACD;gBACE,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,KAAK,EAAE,cAAc,CAAC;gBAC9C,IAAI,EAAE,SAAS;aAChB;YACD;gBACE,IAAI,EAAE,IAAA,WAAI,EAAC,WAAW,EAAE,KAAK,EAAE,UAAU,CAAC;gBAC1C,IAAI,EAAE,OAAO;aACd;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,iCAAiC;AACjC,qDAAqD;AACrD,sDAAsD;AACtD,qCAAqC;AACrC,QAAQ;AACR,sDAAsD;AACtD,sDAAsD;AACtD,qCAAqC;AACrC,QAAQ;AACR,MAAM"}