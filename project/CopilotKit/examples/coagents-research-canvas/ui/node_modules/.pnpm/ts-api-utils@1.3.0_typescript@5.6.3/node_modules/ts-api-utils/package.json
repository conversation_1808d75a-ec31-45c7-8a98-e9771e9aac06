{"name": "ts-api-utils", "version": "1.3.0", "description": "Utility functions for working with TypeScript's API. Successor to the wonderful tsutils. 🛠️️", "repository": {"type": "git", "url": "https://github.com/JoshuaKGoldberg/ts-api-utils"}, "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "type": "module", "exports": {".": {"types": {"import": "./lib/index.d.ts", "require": "./lib/index.d.cts"}, "import": "./lib/index.js", "require": "./lib/index.cjs"}}, "main": "./lib/index.js", "files": ["lib/", "package.json", "LICENSE.md", "README.md"], "scripts": {"build": "tsup src/index.ts && cp lib/index.d.ts lib/index.d.cts", "docs": "typedoc", "docs:serve": "npx --yes http-server docs/generated", "format": "prettier \"**/*\" --ignore-unknown", "lint": "eslint . .*js --max-warnings 0 --report-unused-disable-directives", "lint:docs": "typedoc --validation --treatValidationWarningsAsErrors", "lint:knip": "knip", "lint:knip:production": "knip --config knip.production.jsonc --production", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\" --rules sentences-per-line", "lint:package-json": "npmPkgJsonLint .", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "prepare": "husky install", "should-semantic-release": "should-semantic-release --verbose", "test": "vitest", "tsc": "tsc"}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "devDependencies": {"@phenomnomnominal/tsquery": "^6.1.3", "@release-it/conventional-changelog": "^8.0.1", "@types/eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "6.20.0", "@typescript/vfs": "^1.5.0", "@vitest/coverage-v8": "^1.1.0", "console-fail-test": "^0.2.3", "cspell": "^8.2.3", "eslint": "^8.56.0", "eslint-plugin-deprecation": "^2.0.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsdoc": "^48.0.0", "eslint-plugin-jsonc": "^2.11.2", "eslint-plugin-markdown": "^3.0.1", "eslint-plugin-n": "^16.5.0", "eslint-plugin-no-only-tests": "^3.1.0", "eslint-plugin-perfectionist": "^2.5.0", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-vitest": "^0.3.18", "eslint-plugin-yml": "^1.11.0", "husky": "^9.0.0", "jsonc-eslint-parser": "^2.4.0", "knip": "^4.0.0", "lint-staged": "^15.2.0", "markdownlint": "^0.33.0", "markdownlint-cli": "^0.39.0", "npm-package-json-lint": "^7.1.0", "npm-package-json-lint-config-default": "^6.0.0", "prettier": "^3.1.1", "prettier-plugin-curly": "^0.1.3", "prettier-plugin-packagejson": "^2.4.7", "release-it": "^17.0.1", "sentences-per-line": "^0.2.1", "should-semantic-release": "^0.2.1", "tsup": "^8.0.1", "typedoc": "^0.24.8", "typedoc-plugin-coverage": "^2.2.0", "typedoc-plugin-custom-validation": "^1.1.1", "typedoc-plugin-konamimojisplosion": "^0.0.2", "typedoc-plugin-mdn-links": "^3.1.8", "typedoc-plugin-versions": "^0.2.4", "typescript": "^5.3.3", "vitest": "^1.1.0", "yaml-eslint-parser": "^1.2.2"}, "peerDependencies": {"typescript": ">=4.2.0"}, "packageManager": "pnpm@8.15.1", "engines": {"node": ">=16"}, "publishConfig": {"provenance": true}}