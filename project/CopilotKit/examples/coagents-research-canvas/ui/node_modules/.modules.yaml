hoistPattern:
  - '*'
hoistedDependencies:
  '@0no-co/graphql.web@1.0.8(graphql@16.9.0)':
    '@0no-co/graphql.web': private
  '@ag-ui/client@0.0.28':
    '@ag-ui/client': private
  '@ag-ui/core@0.0.28':
    '@ag-ui/core': private
  '@ag-ui/encoder@0.0.28':
    '@ag-ui/encoder': private
  '@ag-ui/langgraph@0.0.4(openai@4.85.1(ws@8.18.0)(zod@3.23.8))(react@19.0.0)':
    '@ag-ui/langgraph': private
  '@ag-ui/proto@0.0.28':
    '@ag-ui/proto': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@anthropic-ai/sdk@0.27.3':
    '@anthropic-ai/sdk': private
  '@aws-crypto/crc32@5.2.0':
    '@aws-crypto/crc32': private
  '@aws-crypto/sha256-browser@5.2.0':
    '@aws-crypto/sha256-browser': private
  '@aws-crypto/sha256-js@5.2.0':
    '@aws-crypto/sha256-js': private
  '@aws-crypto/supports-web-crypto@5.2.0':
    '@aws-crypto/supports-web-crypto': private
  '@aws-crypto/util@5.2.0':
    '@aws-crypto/util': private
  '@aws-sdk/client-bedrock-agent-runtime@3.830.0':
    '@aws-sdk/client-bedrock-agent-runtime': private
  '@aws-sdk/client-bedrock-runtime@3.830.0':
    '@aws-sdk/client-bedrock-runtime': private
  '@aws-sdk/client-kendra@3.830.0':
    '@aws-sdk/client-kendra': private
  '@aws-sdk/client-sso@3.830.0':
    '@aws-sdk/client-sso': private
  '@aws-sdk/core@3.826.0':
    '@aws-sdk/core': private
  '@aws-sdk/credential-provider-env@3.826.0':
    '@aws-sdk/credential-provider-env': private
  '@aws-sdk/credential-provider-http@3.826.0':
    '@aws-sdk/credential-provider-http': private
  '@aws-sdk/credential-provider-ini@3.830.0':
    '@aws-sdk/credential-provider-ini': private
  '@aws-sdk/credential-provider-node@3.830.0':
    '@aws-sdk/credential-provider-node': private
  '@aws-sdk/credential-provider-process@3.826.0':
    '@aws-sdk/credential-provider-process': private
  '@aws-sdk/credential-provider-sso@3.830.0':
    '@aws-sdk/credential-provider-sso': private
  '@aws-sdk/credential-provider-web-identity@3.830.0':
    '@aws-sdk/credential-provider-web-identity': private
  '@aws-sdk/eventstream-handler-node@3.821.0':
    '@aws-sdk/eventstream-handler-node': private
  '@aws-sdk/middleware-eventstream@3.821.0':
    '@aws-sdk/middleware-eventstream': private
  '@aws-sdk/middleware-host-header@3.821.0':
    '@aws-sdk/middleware-host-header': private
  '@aws-sdk/middleware-logger@3.821.0':
    '@aws-sdk/middleware-logger': private
  '@aws-sdk/middleware-recursion-detection@3.821.0':
    '@aws-sdk/middleware-recursion-detection': private
  '@aws-sdk/middleware-user-agent@3.828.0':
    '@aws-sdk/middleware-user-agent': private
  '@aws-sdk/nested-clients@3.830.0':
    '@aws-sdk/nested-clients': private
  '@aws-sdk/region-config-resolver@3.821.0':
    '@aws-sdk/region-config-resolver': private
  '@aws-sdk/token-providers@3.830.0':
    '@aws-sdk/token-providers': private
  '@aws-sdk/types@3.821.0':
    '@aws-sdk/types': private
  '@aws-sdk/util-endpoints@3.828.0':
    '@aws-sdk/util-endpoints': private
  '@aws-sdk/util-locate-window@3.804.0':
    '@aws-sdk/util-locate-window': private
  '@aws-sdk/util-user-agent-browser@3.821.0':
    '@aws-sdk/util-user-agent-browser': private
  '@aws-sdk/util-user-agent-node@3.828.0':
    '@aws-sdk/util-user-agent-node': private
  '@aws-sdk/xml-builder@3.821.0':
    '@aws-sdk/xml-builder': private
  '@babel/runtime@7.25.7':
    '@babel/runtime': private
  '@browserbasehq/sdk@2.2.0':
    '@browserbasehq/sdk': private
  '@browserbasehq/stagehand@1.12.0(@playwright/test@1.50.1)(deepmerge@4.3.1)(dotenv@16.4.7)(openai@4.85.1(ws@8.18.0)(zod@3.23.8))(zod@3.23.8)':
    '@browserbasehq/stagehand': private
  '@bufbuild/protobuf@2.2.5':
    '@bufbuild/protobuf': private
  '@cfworker/json-schema@4.1.1':
    '@cfworker/json-schema': private
  '@copilotkit/runtime-client-gql@1.9.2-next.7(graphql@16.9.0)(react@19.0.0)':
    '@copilotkit/runtime-client-gql': private
  '@copilotkit/shared@1.9.2-next.7':
    '@copilotkit/shared': private
  '@envelop/core@5.0.2':
    '@envelop/core': private
  '@envelop/types@5.0.0':
    '@envelop/types': private
  '@eslint-community/eslint-utils@4.4.0(eslint@9.16.0(jiti@1.21.6))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.19.1':
    '@eslint/config-array': private
  '@eslint/core@0.9.1':
    '@eslint/core': private
  '@eslint/eslintrc@3.2.0':
    '@eslint/eslintrc': private
  '@eslint/js@9.16.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.5':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.2.4':
    '@eslint/plugin-kit': private
  '@floating-ui/core@1.6.8':
    '@floating-ui/core': private
  '@floating-ui/dom@1.6.11':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/react@0.26.24(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@floating-ui/react': private
  '@floating-ui/utils@0.2.8':
    '@floating-ui/utils': private
  '@graphql-tools/executor@1.3.1(graphql@16.9.0)':
    '@graphql-tools/executor': private
  '@graphql-tools/merge@9.0.7(graphql@16.9.0)':
    '@graphql-tools/merge': private
  '@graphql-tools/schema@10.0.6(graphql@16.9.0)':
    '@graphql-tools/schema': private
  '@graphql-tools/utils@10.5.4(graphql@16.9.0)':
    '@graphql-tools/utils': private
  '@graphql-typed-document-node/core@3.2.0(graphql@16.9.0)':
    '@graphql-typed-document-node/core': private
  '@graphql-yoga/logger@2.0.0':
    '@graphql-yoga/logger': private
  '@graphql-yoga/plugin-defer-stream@3.7.0(graphql-yoga@5.7.0(graphql@16.9.0))(graphql@16.9.0)':
    '@graphql-yoga/plugin-defer-stream': private
  '@graphql-yoga/subscription@5.0.1':
    '@graphql-yoga/subscription': private
  '@graphql-yoga/typed-event-target@3.0.0':
    '@graphql-yoga/typed-event-target': private
  '@headlessui/react@2.1.9(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@headlessui/react': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.1':
    '@humanwhocodes/retry': private
  '@ibm-cloud/watsonx-ai@1.5.0(@langchain/core@0.3.39(openai@4.85.1(ws@8.18.0)(zod@3.23.8)))':
    '@ibm-cloud/watsonx-ai': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.5':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@kamilkisiela/fast-url-parser@1.1.4':
    '@kamilkisiela/fast-url-parser': private
  '@langchain/aws@0.1.11(@langchain/core@0.3.39(openai@4.85.1(ws@8.18.0)(zod@3.23.8)))':
    '@langchain/aws': private
  '@langchain/community@0.3.29(@aws-crypto/sha256-js@5.2.0)(@aws-sdk/client-bedrock-agent-runtime@3.830.0)(@aws-sdk/client-bedrock-runtime@3.830.0)(@aws-sdk/client-kendra@3.830.0)(@aws-sdk/credential-provider-node@3.830.0)(@browserbasehq/sdk@2.2.0)(@browserbasehq/stagehand@1.12.0(@playwright/test@1.50.1)(deepmerge@4.3.1)(dotenv@16.4.7)(openai@4.85.1(ws@8.18.0)(zod@3.23.8))(zod@3.23.8))(@ibm-cloud/watsonx-ai@1.5.0(@langchain/core@0.3.39(openai@4.85.1(ws@8.18.0)(zod@3.23.8))))(@langchain/aws@0.1.11(@langchain/core@0.3.39(openai@4.85.1(ws@8.18.0)(zod@3.23.8))))(@langchain/core@0.3.39(openai@4.85.1(ws@8.18.0)(zod@3.23.8)))(@smithy/util-utf8@2.3.0)(axios@1.7.9)(fast-xml-parser@4.4.1)(google-auth-library@8.9.0)(ibm-cloud-sdk-core@5.1.2)(ignore@5.3.2)(jsonwebtoken@9.0.2)(openai@4.85.1(ws@8.18.0)(zod@3.23.8))(playwright@1.50.1)(ws@8.18.0)':
    '@langchain/community': private
  '@langchain/core@0.3.39(openai@4.85.1(ws@8.18.0)(zod@3.23.8))':
    '@langchain/core': private
  '@langchain/google-common@0.1.1(@langchain/core@0.3.39(openai@4.85.1(ws@8.18.0)(zod@3.23.8)))(zod@3.23.8)':
    '@langchain/google-common': private
  '@langchain/google-gauth@0.1.0(@langchain/core@0.3.39(openai@4.85.1(ws@8.18.0)(zod@3.23.8)))(zod@3.23.8)':
    '@langchain/google-gauth': private
  '@langchain/langgraph-sdk@0.0.70(@langchain/core@0.3.39(openai@4.85.1(ws@8.18.0)(zod@3.23.8)))(react@19.0.0)':
    '@langchain/langgraph-sdk': private
  '@langchain/openai@0.4.3(@langchain/core@0.3.39(openai@4.85.1(ws@8.18.0)(zod@3.23.8)))(ws@8.18.0)':
    '@langchain/openai': private
  '@langchain/textsplitters@0.1.0(@langchain/core@0.3.39(openai@4.85.1(ws@8.18.0)(zod@3.23.8)))':
    '@langchain/textsplitters': private
  '@lukeed/csprng@1.1.0':
    '@lukeed/csprng': private
  '@lukeed/uuid@2.0.1':
    '@lukeed/uuid': private
  '@next/env@15.1.0':
    '@next/env': private
  '@next/eslint-plugin-next@15.1.0':
    '@next/eslint-plugin-next': private
  '@next/swc-linux-x64-gnu@15.1.0':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.1.0':
    '@next/swc-linux-x64-musl': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@playwright/test@1.50.1':
    '@playwright/test': private
  '@radix-ui/number@1.1.0':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.0':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.0(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.0(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.1(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.1(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.1(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.0(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-popper@1.2.0(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.2(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.1(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.0.0(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-use-callback-ref@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-escape-keydown@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.0(@types/react@19.0.1)(react@19.0.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.1.0(@types/react-dom@19.0.2(@types/react@19.0.1))(@types/react@19.0.1)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.0':
    '@radix-ui/rect': private
  '@react-aria/focus@3.18.3(react@19.0.0)':
    '@react-aria/focus': private
  '@react-aria/interactions@3.22.3(react@19.0.0)':
    '@react-aria/interactions': private
  '@react-aria/ssr@3.9.6(react@19.0.0)':
    '@react-aria/ssr': private
  '@react-aria/utils@3.25.3(react@19.0.0)':
    '@react-aria/utils': private
  '@react-stately/utils@3.10.4(react@19.0.0)':
    '@react-stately/utils': private
  '@react-types/shared@3.25.0(react@19.0.0)':
    '@react-types/shared': private
  '@repeaterjs/repeater@3.0.6':
    '@repeaterjs/repeater': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.10.4':
    '@rushstack/eslint-patch': private
  '@scarf/scarf@1.3.0':
    '@scarf/scarf': private
  '@segment/analytics-core@1.8.0':
    '@segment/analytics-core': private
  '@segment/analytics-generic-utils@1.2.0':
    '@segment/analytics-generic-utils': private
  '@segment/analytics-node@2.2.0':
    '@segment/analytics-node': private
  '@smithy/abort-controller@4.0.4':
    '@smithy/abort-controller': private
  '@smithy/config-resolver@4.1.4':
    '@smithy/config-resolver': private
  '@smithy/core@3.5.3':
    '@smithy/core': private
  '@smithy/credential-provider-imds@4.0.6':
    '@smithy/credential-provider-imds': private
  '@smithy/eventstream-codec@4.0.4':
    '@smithy/eventstream-codec': private
  '@smithy/eventstream-serde-browser@4.0.4':
    '@smithy/eventstream-serde-browser': private
  '@smithy/eventstream-serde-config-resolver@4.1.2':
    '@smithy/eventstream-serde-config-resolver': private
  '@smithy/eventstream-serde-node@4.0.4':
    '@smithy/eventstream-serde-node': private
  '@smithy/eventstream-serde-universal@4.0.4':
    '@smithy/eventstream-serde-universal': private
  '@smithy/fetch-http-handler@5.0.4':
    '@smithy/fetch-http-handler': private
  '@smithy/hash-node@4.0.4':
    '@smithy/hash-node': private
  '@smithy/invalid-dependency@4.0.4':
    '@smithy/invalid-dependency': private
  '@smithy/is-array-buffer@2.2.0':
    '@smithy/is-array-buffer': private
  '@smithy/middleware-content-length@4.0.4':
    '@smithy/middleware-content-length': private
  '@smithy/middleware-endpoint@4.1.11':
    '@smithy/middleware-endpoint': private
  '@smithy/middleware-retry@4.1.12':
    '@smithy/middleware-retry': private
  '@smithy/middleware-serde@4.0.8':
    '@smithy/middleware-serde': private
  '@smithy/middleware-stack@4.0.4':
    '@smithy/middleware-stack': private
  '@smithy/node-config-provider@4.1.3':
    '@smithy/node-config-provider': private
  '@smithy/node-http-handler@4.0.6':
    '@smithy/node-http-handler': private
  '@smithy/property-provider@4.0.4':
    '@smithy/property-provider': private
  '@smithy/protocol-http@5.1.2':
    '@smithy/protocol-http': private
  '@smithy/querystring-builder@4.0.4':
    '@smithy/querystring-builder': private
  '@smithy/querystring-parser@4.0.4':
    '@smithy/querystring-parser': private
  '@smithy/service-error-classification@4.0.5':
    '@smithy/service-error-classification': private
  '@smithy/shared-ini-file-loader@4.0.4':
    '@smithy/shared-ini-file-loader': private
  '@smithy/signature-v4@5.1.2':
    '@smithy/signature-v4': private
  '@smithy/smithy-client@4.4.3':
    '@smithy/smithy-client': private
  '@smithy/types@4.3.1':
    '@smithy/types': private
  '@smithy/url-parser@4.0.4':
    '@smithy/url-parser': private
  '@smithy/util-base64@4.0.0':
    '@smithy/util-base64': private
  '@smithy/util-body-length-browser@4.0.0':
    '@smithy/util-body-length-browser': private
  '@smithy/util-body-length-node@4.0.0':
    '@smithy/util-body-length-node': private
  '@smithy/util-buffer-from@2.2.0':
    '@smithy/util-buffer-from': private
  '@smithy/util-config-provider@4.0.0':
    '@smithy/util-config-provider': private
  '@smithy/util-defaults-mode-browser@4.0.19':
    '@smithy/util-defaults-mode-browser': private
  '@smithy/util-defaults-mode-node@4.0.19':
    '@smithy/util-defaults-mode-node': private
  '@smithy/util-endpoints@3.0.6':
    '@smithy/util-endpoints': private
  '@smithy/util-hex-encoding@4.0.0':
    '@smithy/util-hex-encoding': private
  '@smithy/util-middleware@4.0.4':
    '@smithy/util-middleware': private
  '@smithy/util-retry@4.0.5':
    '@smithy/util-retry': private
  '@smithy/util-stream@4.2.2':
    '@smithy/util-stream': private
  '@smithy/util-uri-escape@4.0.0':
    '@smithy/util-uri-escape': private
  '@smithy/util-utf8@2.3.0':
    '@smithy/util-utf8': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@tanstack/react-virtual@3.10.8(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@tanstack/react-virtual': private
  '@tanstack/virtual-core@3.10.8':
    '@tanstack/virtual-core': private
  '@tokenizer/token@0.3.0':
    '@tokenizer/token': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.6':
    '@types/estree': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/katex@0.16.7':
    '@types/katex': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/ms@0.7.34':
    '@types/ms': private
  '@types/node-fetch@2.6.11':
    '@types/node-fetch': private
  '@types/prop-types@15.7.13':
    '@types/prop-types': private
  '@types/retry@0.12.0':
    '@types/retry': private
  '@types/semver@7.5.8':
    '@types/semver': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/unist@2.0.11':
    '@types/unist': private
  '@types/uuid@10.0.0':
    '@types/uuid': private
  '@types/validator@13.12.2':
    '@types/validator': private
  '@typescript-eslint/eslint-plugin@8.8.1(@typescript-eslint/parser@8.8.1(eslint@9.16.0(jiti@1.21.6))(typescript@5.6.3))(eslint@9.16.0(jiti@1.21.6))(typescript@5.6.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.8.1(eslint@9.16.0(jiti@1.21.6))(typescript@5.6.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@8.8.1':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@8.8.1(eslint@9.16.0(jiti@1.21.6))(typescript@5.6.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.8.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.8.1(typescript@5.6.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.8.1(eslint@9.16.0(jiti@1.21.6))(typescript@5.6.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.8.1':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@urql/core@5.0.6(graphql@16.9.0)':
    '@urql/core': private
  '@whatwg-node/events@0.1.2':
    '@whatwg-node/events': private
  '@whatwg-node/fetch@0.9.21':
    '@whatwg-node/fetch': private
  '@whatwg-node/node-fetch@0.5.26':
    '@whatwg-node/node-fetch': private
  '@whatwg-node/server@0.9.49':
    '@whatwg-node/server': private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.14.0):
    acorn-jsx: private
  acorn@8.14.0:
    acorn: private
  agent-base@6.0.2:
    agent-base: private
  agentkeepalive@4.5.0:
    agentkeepalive: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@5.2.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.4:
    aria-hidden: private
  aria-query@5.1.3:
    aria-query: private
  array-buffer-byte-length@1.0.1:
    array-buffer-byte-length: private
  array-flatten@1.1.1:
    array-flatten: private
  array-includes@3.1.8:
    array-includes: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.5:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.2:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.2:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.3:
    arraybuffer.prototype.slice: private
  arrify@2.0.1:
    arrify: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  asynckit@0.4.0:
    asynckit: private
  atomic-sleep@1.0.0:
    atomic-sleep: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.0:
    axe-core: private
  axios@1.7.9(debug@4.3.7):
    axios: private
  axobject-query@4.1.0:
    axobject-query: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  bignumber.js@9.1.2:
    bignumber.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  body-parser@1.20.3:
    body-parser: private
  bowser@2.11.0:
    bowser: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer@6.0.3:
    buffer: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  call-bind@1.0.7:
    call-bind: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001667:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@1.1.4:
    character-entities-legacy: private
  character-entities@1.2.4:
    character-entities: private
  character-reference-invalid@1.1.4:
    character-reference-invalid: private
  chokidar@3.6.0:
    chokidar: private
  class-transformer@0.5.1:
    class-transformer: private
  class-validator@0.14.1:
    class-validator: private
  client-only@0.0.1:
    client-only: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colorette@2.0.20:
    colorette: private
  combined-stream@1.0.8:
    combined-stream: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  commander@4.1.1:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  console-table-printer@2.12.1:
    console-table-printer: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  cross-inspect@1.0.1:
    cross-inspect: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-view-buffer@1.0.1:
    data-view-buffer: private
  data-view-byte-length@1.0.1:
    data-view-byte-length: private
  data-view-byte-offset@1.0.0:
    data-view-byte-offset: private
  dateformat@4.6.3:
    dateformat: private
  debug@4.3.7:
    debug: private
  decamelize@1.2.0:
    decamelize: private
  decode-named-character-reference@1.0.2:
    decode-named-character-reference: private
  deep-equal@2.2.3:
    deep-equal: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  dequal@2.0.3:
    dequal: private
  destroy@1.2.0:
    destroy: private
  detect-node-es@1.1.0:
    detect-node-es: private
  devlop@1.1.0:
    devlop: private
  didyoumean@1.2.2:
    didyoumean: private
  diff@5.2.0:
    diff: private
  dlv@1.1.3:
    dlv: private
  doctrine@2.1.0:
    doctrine: private
  dotenv@16.4.7:
    dotenv: private
  dset@3.1.4:
    dset: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  emoji-regex@9.2.2:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  end-of-stream@1.4.4:
    end-of-stream: private
  enhanced-resolve@5.17.1:
    enhanced-resolve: private
  entities@6.0.0:
    entities: private
  es-abstract@1.23.3:
    es-abstract: private
  es-define-property@1.0.0:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-get-iterator@1.1.3:
    es-get-iterator: private
  es-iterator-helpers@1.1.0:
    es-iterator-helpers: private
  es-object-atoms@1.0.0:
    es-object-atoms: private
  es-set-tostringtag@2.0.3:
    es-set-tostringtag: private
  es-shim-unscopables@1.0.2:
    es-shim-unscopables: private
  es-to-primitive@1.2.1:
    es-to-primitive: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-import-resolver-typescript@3.6.3(@typescript-eslint/parser@8.8.1(eslint@9.16.0(jiti@1.21.6))(typescript@5.6.3))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.31.0)(eslint@9.16.0(jiti@1.21.6)):
    eslint-import-resolver-typescript: private
  eslint-module-utils@2.12.0(@typescript-eslint/parser@8.8.1(eslint@9.16.0(jiti@1.21.6))(typescript@5.6.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.6.3(@typescript-eslint/parser@8.8.1(eslint@9.16.0(jiti@1.21.6))(typescript@5.6.3))(eslint-import-resolver-node@0.3.9)(eslint-plugin-import@2.31.0)(eslint@9.16.0(jiti@1.21.6)))(eslint@9.16.0(jiti@1.21.6)):
    eslint-module-utils: private
  eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.8.1(eslint@9.16.0(jiti@1.21.6))(typescript@5.6.3))(eslint-import-resolver-typescript@3.6.3)(eslint@9.16.0(jiti@1.21.6)):
    eslint-plugin-import: private
  eslint-plugin-jsx-a11y@6.10.0(eslint@9.16.0(jiti@1.21.6)):
    eslint-plugin-jsx-a11y: private
  eslint-plugin-react-hooks@5.1.0(eslint@9.16.0(jiti@1.21.6)):
    eslint-plugin-react-hooks: private
  eslint-plugin-react@7.37.1(eslint@9.16.0(jiti@1.21.6)):
    eslint-plugin-react: private
  eslint-scope@8.2.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: private
  espree@10.3.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-util-is-identifier-name@3.0.0:
    estree-util-is-identifier-name: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  eventemitter3@4.0.7:
    eventemitter3: private
  events@3.3.0:
    events: private
  expr-eval@2.0.2:
    expr-eval: private
  express@4.21.1:
    express: private
  extend@3.0.2:
    extend: private
  fast-copy@3.0.2:
    fast-copy: private
  fast-decode-uri-component@1.0.1:
    fast-decode-uri-component: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.2:
    fast-glob: private
  fast-json-patch@3.1.1:
    fast-json-patch: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-querystring@1.1.2:
    fast-querystring: private
  fast-redact@3.5.0:
    fast-redact: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-text-encoding@1.0.6:
    fast-text-encoding: private
  fast-xml-parser@4.4.1:
    fast-xml-parser: private
  fastq@1.17.1:
    fastq: private
  fault@1.0.4:
    fault: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  file-type@16.5.4:
    file-type: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flat@5.0.2:
    flat: private
  flatted@3.3.1:
    flatted: private
  follow-redirects@1.15.9(debug@4.3.7):
    follow-redirects: private
  for-each@0.3.3:
    for-each: private
  foreground-child@3.3.0:
    foreground-child: private
  form-data-encoder@1.7.2:
    form-data-encoder: private
  form-data@4.0.1:
    form-data: private
  format@0.2.2:
    format: private
  formdata-node@4.4.1:
    formdata-node: private
  forwarded@0.2.0:
    forwarded: private
  fresh@0.5.2:
    fresh: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.6:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gaxios@5.1.3:
    gaxios: private
  gcp-metadata@5.3.0:
    gcp-metadata: private
  get-intrinsic@1.2.4:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-symbol-description@1.0.2:
    get-symbol-description: private
  get-tsconfig@4.8.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  globals@14.0.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  google-auth-library@8.9.0:
    google-auth-library: private
  google-p12-pem@4.0.1:
    google-p12-pem: private
  gopd@1.0.1:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  graphql-query-complexity@0.12.0(graphql@16.9.0):
    graphql-query-complexity: private
  graphql-scalars@1.23.0(graphql@16.9.0):
    graphql-scalars: private
  graphql-yoga@5.7.0(graphql@16.9.0):
    graphql-yoga: private
  graphql@16.9.0:
    graphql: private
  groq-sdk@0.5.0:
    groq-sdk: private
  gtoken@6.1.2:
    gtoken: private
  has-bigints@1.0.2:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.0.3:
    has-proto: private
  has-symbols@1.0.3:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hast-util-from-parse5@8.0.3:
    hast-util-from-parse5: private
  hast-util-parse-selector@2.2.5:
    hast-util-parse-selector: private
  hast-util-raw@9.1.0:
    hast-util-raw: private
  hast-util-to-jsx-runtime@2.3.6:
    hast-util-to-jsx-runtime: private
  hast-util-to-parse5@8.0.0:
    hast-util-to-parse5: private
  hast-util-whitespace@2.0.1:
    hast-util-whitespace: private
  hastscript@6.0.0:
    hastscript: private
  help-me@5.0.0:
    help-me: private
  highlight.js@10.7.3:
    highlight.js: private
  highlightjs-vue@1.0.0:
    highlightjs-vue: private
  html-url-attributes@3.0.1:
    html-url-attributes: private
  html-void-elements@3.0.0:
    html-void-elements: private
  http-errors@2.0.0:
    http-errors: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  humanize-ms@1.2.1:
    humanize-ms: private
  ibm-cloud-sdk-core@5.1.2:
    ibm-cloud-sdk-core: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.0:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inherits@2.0.4:
    inherits: private
  inline-style-parser@0.1.1:
    inline-style-parser: private
  internal-slot@1.0.7:
    internal-slot: private
  invariant@2.2.4:
    invariant: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-alphabetical@1.0.4:
    is-alphabetical: private
  is-alphanumerical@1.0.4:
    is-alphanumerical: private
  is-arguments@1.1.1:
    is-arguments: private
  is-array-buffer@3.0.4:
    is-array-buffer: private
  is-async-function@2.0.0:
    is-async-function: private
  is-bigint@1.0.4:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.1.2:
    is-boolean-object: private
  is-buffer@2.0.5:
    is-buffer: private
  is-bun-module@1.2.1:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.15.1:
    is-core-module: private
  is-data-view@1.0.1:
    is-data-view: private
  is-date-object@1.0.5:
    is-date-object: private
  is-decimal@1.0.4:
    is-decimal: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.0.2:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.0.10:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hexadecimal@1.0.4:
    is-hexadecimal: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.0.7:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-regex@1.1.4:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.3:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.0.7:
    is-string: private
  is-symbol@1.0.4:
    is-symbol: private
  is-typed-array@1.1.13:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.0.2:
    is-weakref: private
  is-weakset@2.0.3:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isstream@0.1.2:
    isstream: private
  iterator.prototype@1.1.3:
    iterator.prototype: private
  jackspeak@3.4.3:
    jackspeak: private
  jiti@1.21.6:
    jiti: private
  jose@5.9.3:
    jose: private
  joycon@3.1.1:
    joycon: private
  js-tiktoken@1.0.15:
    js-tiktoken: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-bigint@1.0.0:
    json-bigint: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@1.0.2:
    json5: private
  jsonpointer@5.0.1:
    jsonpointer: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  jwa@1.4.1:
    jwa: private
  jws@4.0.0:
    jws: private
  katex@0.16.11:
    katex: private
  keyv@4.5.4:
    keyv: private
  kleur@4.1.5:
    kleur: private
  langchain@0.3.5(@langchain/aws@0.1.11(@langchain/core@0.3.39(openai@4.85.1(ws@8.18.0)(zod@3.23.8))))(@langchain/core@0.3.39(openai@4.85.1(ws@8.18.0)(zod@3.23.8)))(axios@1.7.9)(openai@4.85.1(ws@8.18.0)(zod@3.23.8))(ws@8.18.0):
    langchain: private
  langsmith@0.3.7(openai@4.85.1(ws@8.18.0)(zod@3.23.8)):
    langsmith: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  libphonenumber-js@1.11.11:
    libphonenumber-js: private
  lilconfig@2.1.0:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.get@4.4.2:
    lodash.get: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  lowlight@1.20.0:
    lowlight: private
  lru-cache@10.4.3:
    lru-cache: private
  markdown-table@3.0.3:
    markdown-table: private
  mdast-util-definitions@5.1.2:
    mdast-util-definitions: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-math@3.0.0:
    mdast-util-math: private
  mdast-util-mdx-expression@2.0.1:
    mdast-util-mdx-expression: private
  mdast-util-mdx-jsx@3.2.0:
    mdast-util-mdx-jsx: private
  mdast-util-mdxjs-esm@2.0.1:
    mdast-util-mdxjs-esm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@3.2.0:
    mdast-util-to-string: private
  media-typer@0.3.0:
    media-typer: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-extension-math@3.1.0:
    micromark-extension-math: private
  micromark-factory-destination@1.1.0:
    micromark-factory-destination: private
  micromark-factory-label@1.1.0:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@1.1.0:
    micromark-factory-title: private
  micromark-factory-whitespace@1.1.0:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@1.1.0:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@1.1.0:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@1.2.0:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@1.1.0:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@1.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@3.2.0:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mri@1.2.0:
    mri: private
  ms@2.1.3:
    ms: private
  mustache@4.2.0:
    mustache: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.7:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.3:
    negotiator: private
  node-domexception@1.0.0:
    node-domexception: private
  node-fetch@2.7.0:
    node-fetch: private
  node-forge@1.3.1:
    node-forge: private
  normalize-path@3.0.0:
    normalize-path: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.2:
    object-inspect: private
  object-is@1.1.6:
    object-is: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.5:
    object.assign: private
  object.entries@1.1.8:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.0:
    object.values: private
  on-exit-leak-free@2.1.2:
    on-exit-leak-free: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  openapi-types@12.1.3:
    openapi-types: private
  optionator@0.9.4:
    optionator: private
  p-finally@1.0.0:
    p-finally: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-queue@6.6.2:
    p-queue: private
  p-retry@4.6.2:
    p-retry: private
  p-timeout@3.2.0:
    p-timeout: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@2.0.0:
    parse-entities: private
  parse5@7.3.0:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  partial-json@0.1.7:
    partial-json: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@0.1.10:
    path-to-regexp: private
  peek-readable@4.1.0:
    peek-readable: private
  picocolors@1.1.0:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pino-abstract-transport@1.2.0:
    pino-abstract-transport: private
  pino-pretty@11.2.2:
    pino-pretty: private
  pino-std-serializers@7.0.0:
    pino-std-serializers: private
  pino@9.4.0:
    pino: private
  pirates@4.0.6:
    pirates: private
  playwright-core@1.50.1:
    playwright-core: private
  playwright@1.50.1:
    playwright: private
  possible-typed-array-names@1.0.0:
    possible-typed-array-names: private
  postcss-import@15.1.0(postcss@8.4.47):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.4.47):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.4.47):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.4.47):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prismjs@1.29.0:
    prismjs: private
  process-warning@4.0.0:
    process-warning: private
  process@0.11.10:
    process: private
  prop-types@15.8.1:
    prop-types: private
  property-information@6.5.0:
    property-information: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  psl@1.15.0:
    psl: private
  pump@3.0.2:
    pump: private
  punycode@2.3.1:
    punycode: private
  qs@6.13.0:
    qs: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-format-unescaped@4.0.4:
    quick-format-unescaped: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  react-is@18.3.1:
    react-is: private
  react-markdown@8.0.7(@types/react@19.0.1)(react@19.0.0):
    react-markdown: private
  react-remove-scroll-bar@2.3.6(@types/react@19.0.1)(react@19.0.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.6.0(@types/react@19.0.1)(react@19.0.0):
    react-remove-scroll: private
  react-style-singleton@2.2.1(@types/react@19.0.1)(react@19.0.0):
    react-style-singleton: private
  react-syntax-highlighter@15.6.1(react@19.0.0):
    react-syntax-highlighter: private
  read-cache@1.0.0:
    read-cache: private
  readable-stream@4.7.0:
    readable-stream: private
  readable-web-to-node-stream@3.0.3:
    readable-web-to-node-stream: private
  readdirp@3.6.0:
    readdirp: private
  real-require@0.2.0:
    real-require: private
  reflect-metadata@0.2.2:
    reflect-metadata: private
  reflect.getprototypeof@1.0.6:
    reflect.getprototypeof: private
  refractor@3.6.0:
    refractor: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regexp.prototype.flags@1.5.3:
    regexp.prototype.flags: private
  rehype-raw@7.0.0:
    rehype-raw: private
  remark-gfm@4.0.1:
    remark-gfm: private
  remark-math@6.0.0:
    remark-math: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.2:
    remark-rehype: private
  remark-stringify@11.0.0:
    remark-stringify: private
  requires-port@1.0.0:
    requires-port: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.8:
    resolve: private
  retry-axios@2.6.0(axios@1.7.9(debug@4.3.7)):
    retry-axios: private
  retry@0.13.1:
    retry: private
  reusify@1.0.4:
    reusify: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.1:
    rxjs: private
  sade@1.8.1:
    sade: private
  safe-array-concat@1.1.2:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-regex-test@1.0.3:
    safe-regex-test: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.25.0:
    scheduler: private
  secure-json-parse@2.7.0:
    secure-json-parse: private
  semver@6.3.1:
    semver: private
  send@0.19.0:
    send: private
  serve-static@1.16.2:
    serve-static: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel@1.0.6:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-wcswidth@1.0.1:
    simple-wcswidth: private
  sonic-boom@4.1.0:
    sonic-boom: private
  source-map-js@1.2.1:
    source-map-js: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  split2@4.2.0:
    split2: private
  statuses@2.0.1:
    statuses: private
  stop-iteration-iterator@1.0.0:
    stop-iteration-iterator: private
  streamsearch@1.1.0:
    streamsearch: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  string.prototype.includes@2.0.0:
    string.prototype.includes: private
  string.prototype.matchall@4.0.11:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.9:
    string.prototype.trim: private
  string.prototype.trimend@1.0.8:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strnum@1.1.2:
    strnum: private
  strtok3@6.3.0:
    strtok3: private
  style-to-js@1.1.16:
    style-to-js: private
  style-to-object@0.4.4:
    style-to-object: private
  styled-jsx@5.1.6(react@19.0.0):
    styled-jsx: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tabbable@6.2.0:
    tabbable: private
  tapable@2.2.1:
    tapable: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  thread-stream@3.1.0:
    thread-stream: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  token-types@4.2.1:
    token-types: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@0.0.3:
    tr46: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  ts-api-utils@1.3.0(typescript@5.6.3):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-graphql@2.0.0-rc.1(class-validator@0.14.1)(graphql-scalars@1.23.0(graphql@16.9.0))(graphql@16.9.0):
    type-graphql: private
  type-is@1.6.18:
    type-is: private
  typed-array-buffer@1.0.2:
    typed-array-buffer: private
  typed-array-byte-length@1.0.1:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.2:
    typed-array-byte-offset: private
  typed-array-length@1.0.6:
    typed-array-length: private
  unbox-primitive@1.0.2:
    unbox-primitive: private
  undici-types@6.20.0:
    undici-types: private
  unified@11.0.5:
    unified: private
  unist-util-generated@2.0.1:
    unist-util-generated: private
  unist-util-is@5.2.1:
    unist-util-is: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-remove-position@5.0.0:
    unist-util-remove-position: private
  unist-util-stringify-position@3.0.3:
    unist-util-stringify-position: private
  unist-util-visit-parents@5.1.3:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  universalify@0.2.0:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  untruncate-json@0.0.1:
    untruncate-json: private
  uri-js@4.4.1:
    uri-js: private
  url-parse@1.5.10:
    url-parse: private
  urlpattern-polyfill@10.0.0:
    urlpattern-polyfill: private
  urql@4.1.0(@urql/core@5.0.6(graphql@16.9.0))(react@19.0.0):
    urql: private
  use-callback-ref@1.3.2(@types/react@19.0.1)(react@19.0.0):
    use-callback-ref: private
  use-sidecar@1.1.2(@types/react@19.0.1)(react@19.0.0):
    use-sidecar: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@11.1.0:
    uuid: private
  uvu@0.5.6:
    uvu: private
  validator@13.12.0:
    validator: private
  value-or-promise@1.0.12:
    value-or-promise: private
  vary@1.1.2:
    vary: private
  vfile-location@5.0.3:
    vfile-location: private
  vfile-message@4.0.2:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  web-namespaces@2.0.1:
    web-namespaces: private
  web-streams-polyfill@4.0.0-beta.3:
    web-streams-polyfill: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-boxed-primitive@1.0.2:
    which-boxed-primitive: private
  which-builtin-type@1.1.4:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.15:
    which-typed-array: private
  which@2.0.2:
    which: private
  wonka@6.3.4:
    wonka: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.0:
    ws: private
  xtend@4.0.2:
    xtend: private
  yallist@4.0.0:
    yallist: private
  yaml@2.5.1:
    yaml: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zod-to-json-schema@3.23.5(zod@3.23.8):
    zod-to-json-schema: private
  zod@3.23.8:
    zod: private
  zwitch@2.0.4:
    zwitch: private
ignoredBuilds:
  - '@scarf/scarf'
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Fri, 04 Jul 2025 01:40:33 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/runtime@1.3.1'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@img/sharp-win32-x64@0.33.5'
  - '@next/swc-darwin-arm64@15.1.0'
  - '@next/swc-darwin-x64@15.1.0'
  - '@next/swc-linux-arm64-gnu@15.1.0'
  - '@next/swc-linux-arm64-musl@15.1.0'
  - '@next/swc-win32-arm64-msvc@15.1.0'
  - '@next/swc-win32-x64-msvc@15.1.0'
  - color-string@1.9.1
  - color@4.2.3
  - detect-libc@2.0.3
  - fsevents@2.3.2
  - fsevents@2.3.3
  - is-arrayish@0.3.2
  - sharp@0.33.5
  - simple-swizzle@0.2.2
storeDir: /root/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
