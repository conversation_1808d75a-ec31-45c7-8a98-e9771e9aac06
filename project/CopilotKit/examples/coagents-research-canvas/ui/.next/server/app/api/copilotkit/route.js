/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/copilotkit/route";
exports.ids = ["app/api/copilotkit/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist sync recursive":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/ sync ***!
  \*************************************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:fs/promises":
/*!***********************************!*\
  !*** external "node:fs/promises" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs/promises");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/web");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcopilotkit%2Froute&page=%2Fapi%2Fcopilotkit%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcopilotkit%2Froute.ts&appDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcopilotkit%2Froute&page=%2Fapi%2Fcopilotkit%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcopilotkit%2Froute.ts&appDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _merge_project_CopilotKit_examples_coagents_research_canvas_ui_src_app_api_copilotkit_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/copilotkit/route.ts */ \"(rsc)/./src/app/api/copilotkit/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/copilotkit/route\",\n        pathname: \"/api/copilotkit\",\n        filename: \"route\",\n        bundlePath: \"app/api/copilotkit/route\"\n    },\n    resolvedPagePath: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/api/copilotkit/route.ts\",\n    nextConfigOutput,\n    userland: _merge_project_CopilotKit_examples_coagents_research_canvas_ui_src_app_api_copilotkit_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4xLjBfQHBsYXl3cmlnaHQrdGVzdEAxLjUwLjFfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZjb3BpbG90a2l0JTJGcm91dGUmcGFnZT0lMkZhcGklMkZjb3BpbG90a2l0JTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGY29waWxvdGtpdCUyRnJvdXRlLnRzJmFwcERpcj0lMkZtZXJnZSUyRnByb2plY3QlMkZDb3BpbG90S2l0JTJGZXhhbXBsZXMlMkZjb2FnZW50cy1yZXNlYXJjaC1jYW52YXMlMkZ1aSUyRnNyYyUyRmFwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9JTJGbWVyZ2UlMkZwcm9qZWN0JTJGQ29waWxvdEtpdCUyRmV4YW1wbGVzJTJGY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzJTJGdWkmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9c3RhbmRhbG9uZSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUM4QztBQUMzSDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvc3JjL2FwcC9hcGkvY29waWxvdGtpdC9yb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJzdGFuZGFsb25lXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2NvcGlsb3RraXQvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9jb3BpbG90a2l0XCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9jb3BpbG90a2l0L3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvc3JjL2FwcC9hcGkvY29waWxvdGtpdC9yb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcopilotkit%2Froute&page=%2Fapi%2Fcopilotkit%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcopilotkit%2Froute.ts&appDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/copilotkit/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/copilotkit/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _copilotkit_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @copilotkit/runtime */ \"(rsc)/./node_modules/.pnpm/@copilotkit+runtime@1.9.2-next.7_@ag-ui+client@0.0.28_@ag-ui+core@0.0.28_@ag-ui+encoder_073fe4e377ba2e9f0105a8b8a414e9ef/node_modules/@copilotkit/runtime/dist/index.js\");\n/* harmony import */ var _copilotkit_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_copilotkit_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/.pnpm/openai@4.85.1_ws@8.18.0_zod@3.23.8/node_modules/openai/index.mjs\");\n\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\nconst llmAdapter = new _copilotkit_runtime__WEBPACK_IMPORTED_MODULE_1__.OpenAIAdapter({\n    openai\n});\nconst langsmithApiKey = process.env.LANGSMITH_API_KEY;\nconst POST = async (req)=>{\n    const searchParams = req.nextUrl.searchParams;\n    const deploymentUrl = searchParams.get(\"lgcDeploymentUrl\") || process.env.LGC_DEPLOYMENT_URL;\n    const isCrewAi = searchParams.get(\"coAgentsModel\") === \"crewai\";\n    let runtime = new _copilotkit_runtime__WEBPACK_IMPORTED_MODULE_1__.CopilotRuntime({\n        remoteEndpoints: [\n            (0,_copilotkit_runtime__WEBPACK_IMPORTED_MODULE_1__.copilotKitEndpoint)({\n                url: process.env.REMOTE_ACTION_URL || \"http://localhost:8000/copilotkit\"\n            })\n        ]\n    });\n    if (deploymentUrl && !isCrewAi) {\n        runtime = new _copilotkit_runtime__WEBPACK_IMPORTED_MODULE_1__.CopilotRuntime({\n            agents: {\n                'research_agent': new _copilotkit_runtime__WEBPACK_IMPORTED_MODULE_1__.LangGraphAgent({\n                    deploymentUrl,\n                    langsmithApiKey,\n                    graphId: 'research_agent'\n                }),\n                'research_agent_google_genai': new _copilotkit_runtime__WEBPACK_IMPORTED_MODULE_1__.LangGraphAgent({\n                    deploymentUrl,\n                    langsmithApiKey,\n                    graphId: 'research_agent_google_genai'\n                })\n            }\n        });\n    }\n    const { handleRequest } = (0,_copilotkit_runtime__WEBPACK_IMPORTED_MODULE_1__.copilotRuntimeNextJSAppRouterEndpoint)({\n        runtime,\n        serviceAdapter: llmAdapter,\n        endpoint: \"/api/copilotkit\"\n    });\n    return handleRequest(req);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/copilotkit/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0","vendor-chunks/@aws-sdk+core@3.826.0","vendor-chunks/graphql@16.9.0","vendor-chunks/zod@3.23.8","vendor-chunks/@copilotkit+shared@1.9.2-next.7","vendor-chunks/formdata-node@4.4.1","vendor-chunks/debug@4.3.7","vendor-chunks/uuid@10.0.0","vendor-chunks/untruncate-json@0.0.1","vendor-chunks/extend@3.0.2","vendor-chunks/ms@2.1.3","vendor-chunks/supports-color@7.2.0","vendor-chunks/has-flag@4.0.0","vendor-chunks/rxjs@7.8.1","vendor-chunks/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_","vendor-chunks/class-validator@0.14.1","vendor-chunks/openai@4.85.1_ws@8.18.0_zod@3.23.8","vendor-chunks/validator@13.12.0","vendor-chunks/type-graphql@2.0.0-rc.1_class-validator@0.14.1_graphql-scalars@1.23.0_graphql@16.9.0__graphql@16.9.0","vendor-chunks/@aws-sdk+client-kendra@3.830.0","vendor-chunks/graphql-scalars@1.23.0_graphql@16.9.0","vendor-chunks/zod-to-json-schema@3.23.5_zod@3.23.8","vendor-chunks/@smithy+types@4.3.1","vendor-chunks/@graphql-tools+utils@10.5.4_graphql@16.9.0","vendor-chunks/@smithy+core@3.5.3","vendor-chunks/@aws-sdk+client-bedrock-agent-runtime@3.830.0","vendor-chunks/semver@7.6.3","vendor-chunks/@smithy+util-endpoints@3.0.6","vendor-chunks/node-forge@1.3.1","vendor-chunks/libphonenumber-js@1.11.11","vendor-chunks/langsmith@0.3.7_openai@4.85.1_ws@8.18.0_zod@3.23.8_","vendor-chunks/graphql-yoga@5.7.0_graphql@16.9.0","vendor-chunks/google-auth-library@8.9.0","vendor-chunks/jose@5.9.3","vendor-chunks/pino-pretty@11.2.2","vendor-chunks/@aws-sdk+client-bedrock-runtime@3.830.0","vendor-chunks/readable-stream@4.7.0","vendor-chunks/@smithy+smithy-client@4.4.3","vendor-chunks/groq-sdk@0.5.0","vendor-chunks/form-data-encoder@1.7.2","vendor-chunks/@anthropic-ai+sdk@0.27.3","vendor-chunks/@segment+analytics-node@2.2.0","vendor-chunks/@whatwg-node+node-fetch@0.5.26","vendor-chunks/@smithy+config-resolver@4.1.4","vendor-chunks/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8","vendor-chunks/@graphql-tools+merge@9.0.7_graphql@16.9.0","vendor-chunks/class-transformer@0.5.1","vendor-chunks/@smithy+util-stream@4.2.2","vendor-chunks/@segment+analytics-core@1.8.0","vendor-chunks/@smithy+shared-ini-file-loader@4.0.4","vendor-chunks/@aws-sdk+util-endpoints@3.828.0","vendor-chunks/@envelop+core@5.0.2","vendor-chunks/@smithy+signature-v4@5.1.2","vendor-chunks/@smithy+node-http-handler@4.0.6","vendor-chunks/@smithy+middleware-endpoint@4.1.11","vendor-chunks/pino@9.4.0","vendor-chunks/@smithy+middleware-retry@4.1.12","vendor-chunks/@smithy+util-retry@4.0.5","vendor-chunks/@smithy+protocol-http@5.1.2","vendor-chunks/@smithy+eventstream-codec@4.0.4","vendor-chunks/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0","vendor-chunks/@whatwg-node+server@0.9.49","vendor-chunks/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0","vendor-chunks/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__","vendor-chunks/@graphql-tools+schema@10.0.6_graphql@16.9.0","vendor-chunks/@graphql-tools+executor@1.3.1_graphql@16.9.0","vendor-chunks/@cfworker+json-schema@4.1.1","vendor-chunks/fast-redact@3.5.0","vendor-chunks/pino-std-serializers@7.0.0","vendor-chunks/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8","vendor-chunks/@envelop+types@5.0.0","vendor-chunks/@smithy+property-provider@4.0.4","vendor-chunks/@aws-sdk+region-config-resolver@3.821.0","vendor-chunks/uuid@11.1.0","vendor-chunks/uuid@9.0.1","vendor-chunks/@smithy+node-config-provider@4.1.3","vendor-chunks/@aws-sdk+middleware-user-agent@3.828.0","vendor-chunks/@graphql-yoga+subscription@5.0.1","vendor-chunks/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0","vendor-chunks/whatwg-url@5.0.0","vendor-chunks/jws@4.0.0","vendor-chunks/@smithy+fetch-http-handler@5.0.4","vendor-chunks/@smithy+eventstream-serde-universal@4.0.4","vendor-chunks/@aws-sdk+util-user-agent-node@3.828.0","vendor-chunks/@aws-sdk+middleware-eventstream@3.821.0","vendor-chunks/@aws-crypto+util@5.2.0","vendor-chunks/fast-json-patch@3.1.1","vendor-chunks/@bufbuild+protobuf@2.2.5","vendor-chunks/thread-stream@3.1.0","vendor-chunks/gaxios@5.1.3","vendor-chunks/fast-querystring@1.1.2","vendor-chunks/busboy@1.6.0","vendor-chunks/agentkeepalive@4.5.0","vendor-chunks/@smithy+util-utf8@4.0.0","vendor-chunks/@smithy+util-utf8@2.3.0","vendor-chunks/@smithy+util-defaults-mode-node@4.0.19","vendor-chunks/@smithy+util-config-provider@4.0.0","vendor-chunks/@smithy+middleware-serde@4.0.8","vendor-chunks/@smithy+eventstream-serde-node@4.0.4","vendor-chunks/@aws-sdk+eventstream-handler-node@3.821.0","vendor-chunks/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3","vendor-chunks/retry@0.13.1","vendor-chunks/p-queue@6.6.2","vendor-chunks/json-bigint@1.0.0","vendor-chunks/https-proxy-agent@5.0.1","vendor-chunks/dset@3.1.4","vendor-chunks/color-convert@2.0.1","vendor-chunks/chalk@4.1.2","vendor-chunks/@whatwg-node+fetch@0.9.21","vendor-chunks/@smithy+util-uri-escape@4.0.0","vendor-chunks/@smithy+util-middleware@4.0.4","vendor-chunks/@smithy+util-base64@4.0.0","vendor-chunks/@aws-sdk+credential-provider-node@3.830.0","vendor-chunks/urlpattern-polyfill@10.0.0","vendor-chunks/langchain@0.3.5_@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zo_0557d33c1cc5d758f91cfdd5be38f06b","vendor-chunks/yallist@4.0.0","vendor-chunks/web-streams-polyfill@4.0.0-beta.3","vendor-chunks/value-or-promise@1.0.12","vendor-chunks/tr46@0.0.3","vendor-chunks/partial-json@0.1.7","vendor-chunks/gcp-metadata@5.3.0","vendor-chunks/ecdsa-sig-formatter@1.0.11","vendor-chunks/agent-base@6.0.2","vendor-chunks/@smithy+util-body-length-node@4.0.0","vendor-chunks/@smithy+service-error-classification@4.0.5","vendor-chunks/@smithy+middleware-stack@4.0.4","vendor-chunks/@smithy+eventstream-serde-config-resolver@4.1.2","vendor-chunks/@segment+analytics-generic-utils@1.2.0","vendor-chunks/@kamilkisiela+fast-url-parser@1.1.4","vendor-chunks/@aws-sdk+middleware-logger@3.821.0","vendor-chunks/@aws-sdk+credential-provider-env@3.826.0","vendor-chunks/@aws-crypto+crc32@5.2.0","vendor-chunks/tslib@2.8.1","vendor-chunks/node-fetch@2.7.0","vendor-chunks/@lukeed+uuid@2.0.1","vendor-chunks/@ag-ui+proto@0.0.28","vendor-chunks/@ag-ui+langgraph@0.0.4_openai@4.85.1_ws@8.18.0_zod@3.23.8__react@19.0.0","vendor-chunks/@ag-ui+core@0.0.28","vendor-chunks/@ag-ui+client@0.0.28","vendor-chunks/sonic-boom@4.1.0","vendor-chunks/reflect-metadata@0.2.2","vendor-chunks/lru-cache@10.4.3","vendor-chunks/js-tiktoken@1.0.15","vendor-chunks/fast-copy@3.0.2","vendor-chunks/cross-inspect@1.0.1","vendor-chunks/colorette@2.0.20","vendor-chunks/@whatwg-node+events@0.1.2","vendor-chunks/@repeaterjs+repeater@3.0.6","vendor-chunks/@graphql-yoga+logger@2.0.0","vendor-chunks/wrappy@1.0.2","vendor-chunks/webidl-conversions@3.0.1","vendor-chunks/web-streams-polyfill@3.3.3","vendor-chunks/string_decoder@1.3.0","vendor-chunks/streamsearch@1.1.0","vendor-chunks/split2@4.2.0","vendor-chunks/secure-json-parse@2.7.0","vendor-chunks/safe-stable-stringify@2.5.0","vendor-chunks/safe-buffer@5.2.1","vendor-chunks/quick-format-unescaped@4.0.4","vendor-chunks/pump@3.0.2","vendor-chunks/process@0.11.10","vendor-chunks/pino-abstract-transport@1.2.0","vendor-chunks/p-timeout@3.2.0","vendor-chunks/p-retry@4.6.2","vendor-chunks/p-finally@1.0.0","vendor-chunks/once@1.4.0","vendor-chunks/on-exit-leak-free@2.1.2","vendor-chunks/node-domexception@1.0.0","vendor-chunks/lru-cache@6.0.0","vendor-chunks/jwa@2.0.0","vendor-chunks/is-stream@2.0.1","vendor-chunks/humanize-ms@1.2.1","vendor-chunks/gtoken@6.1.2","vendor-chunks/google-p12-pem@4.0.1","vendor-chunks/fast-text-encoding@1.0.6","vendor-chunks/fast-safe-stringify@2.1.1","vendor-chunks/fast-decode-uri-component@1.0.1","vendor-chunks/eventemitter3@4.0.7","vendor-chunks/event-target-shim@5.0.1","vendor-chunks/end-of-stream@1.4.4","vendor-chunks/decamelize@1.2.0","vendor-chunks/dateformat@4.6.3","vendor-chunks/color-name@1.1.4","vendor-chunks/camelcase@6.3.0","vendor-chunks/buffer-equal-constant-time@1.0.1","vendor-chunks/bignumber.js@9.1.2","vendor-chunks/base64-js@1.5.1","vendor-chunks/atomic-sleep@1.0.0","vendor-chunks/arrify@2.0.1","vendor-chunks/ansi-styles@5.2.0","vendor-chunks/ansi-styles@4.3.0","vendor-chunks/abort-controller@3.0.0","vendor-chunks/@smithy+util-hex-encoding@4.0.0","vendor-chunks/@smithy+util-buffer-from@4.0.0","vendor-chunks/@smithy+util-buffer-from@2.2.0","vendor-chunks/@smithy+url-parser@4.0.4","vendor-chunks/@smithy+querystring-parser@4.0.4","vendor-chunks/@smithy+querystring-builder@4.0.4","vendor-chunks/@smithy+middleware-content-length@4.0.4","vendor-chunks/@smithy+is-array-buffer@4.0.0","vendor-chunks/@smithy+is-array-buffer@2.2.0","vendor-chunks/@smithy+hash-node@4.0.4","vendor-chunks/@copilotkit+runtime@1.9.2-next.7_@ag-ui+client@0.0.28_@ag-ui+core@0.0.28_@ag-ui+encoder_073fe4e377ba2e9f0105a8b8a414e9ef","vendor-chunks/@aws-sdk+middleware-recursion-detection@3.821.0","vendor-chunks/@aws-sdk+middleware-host-header@3.821.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcopilotkit%2Froute&page=%2Fapi%2Fcopilotkit%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcopilotkit%2Froute.ts&appDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();