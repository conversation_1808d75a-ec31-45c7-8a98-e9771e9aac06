/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?d8ab\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4xLjBfQHBsYXl3cmlnaHQrdGVzdEAxLjUwLjFfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRm1lcmdlJTJGcHJvamVjdCUyRkNvcGlsb3RLaXQlMkZleGFtcGxlcyUyRmNvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcyUyRnVpJTJGbm9kZV9tb2R1bGVzJTJGLnBucG0lMkZuZXh0JTQwMTUuMS4wXyU0MHBsYXl3cmlnaHQlMkJ0ZXN0JTQwMS41MC4xX3JlYWN0LWRvbSU0MDE5LjAuMF9yZWFjdCU0MDE5LjAuMF9fcmVhY3QlNDAxOS4wLjAlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZtZXJnZSUyRnByb2plY3QlMkZDb3BpbG90S2l0JTJGZXhhbXBsZXMlMkZjb2FnZW50cy1yZXNlYXJjaC1jYW52YXMlMkZ1aSUyRm5vZGVfbW9kdWxlcyUyRi5wbnBtJTJGbmV4dCU0MDE1LjEuMF8lNDBwbGF5d3JpZ2h0JTJCdGVzdCU0MDEuNTAuMV9yZWFjdC1kb20lNDAxOS4wLjBfcmVhY3QlNDAxOS4wLjBfX3JlYWN0JTQwMTkuMC4wJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGbWVyZ2UlMkZwcm9qZWN0JTJGQ29waWxvdEtpdCUyRmV4YW1wbGVzJTJGY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzJTJGdWklMkZub2RlX21vZHVsZXMlMkYucG5wbSUyRm5leHQlNDAxNS4xLjBfJTQwcGxheXdyaWdodCUyQnRlc3QlNDAxLjUwLjFfcmVhY3QtZG9tJTQwMTkuMC4wX3JlYWN0JTQwMTkuMC4wX19yZWFjdCU0MDE5LjAuMCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRm1lcmdlJTJGcHJvamVjdCUyRkNvcGlsb3RLaXQlMkZleGFtcGxlcyUyRmNvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcyUyRnVpJTJGbm9kZV9tb2R1bGVzJTJGLnBucG0lMkZuZXh0JTQwMTUuMS4wXyU0MHBsYXl3cmlnaHQlMkJ0ZXN0JTQwMS41MC4xX3JlYWN0LWRvbSU0MDE5LjAuMF9yZWFjdCU0MDE5LjAuMF9fcmVhY3QlNDAxOS4wLjAlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZodHRwLWFjY2Vzcy1mYWxsYmFjayUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRm1lcmdlJTJGcHJvamVjdCUyRkNvcGlsb3RLaXQlMkZleGFtcGxlcyUyRmNvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcyUyRnVpJTJGbm9kZV9tb2R1bGVzJTJGLnBucG0lMkZuZXh0JTQwMTUuMS4wXyU0MHBsYXl3cmlnaHQlMkJ0ZXN0JTQwMS41MC4xX3JlYWN0LWRvbSU0MDE5LjAuMF9yZWFjdCU0MDE5LjAuMF9fcmVhY3QlNDAxOS4wLjAlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRm1lcmdlJTJGcHJvamVjdCUyRkNvcGlsb3RLaXQlMkZleGFtcGxlcyUyRmNvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcyUyRnVpJTJGbm9kZV9tb2R1bGVzJTJGLnBucG0lMkZuZXh0JTQwMTUuMS4wXyU0MHBsYXl3cmlnaHQlMkJ0ZXN0JTQwMS41MC4xX3JlYWN0LWRvbSU0MDE5LjAuMF9yZWFjdCU0MDE5LjAuMF9fcmVhY3QlNDAxOS4wLjAlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRm1lcmdlJTJGcHJvamVjdCUyRkNvcGlsb3RLaXQlMkZleGFtcGxlcyUyRmNvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcyUyRnVpJTJGbm9kZV9tb2R1bGVzJTJGLnBucG0lMkZuZXh0JTQwMTUuMS4wXyU0MHBsYXl3cmlnaHQlMkJ0ZXN0JTQwMS41MC4xX3JlYWN0LWRvbSU0MDE5LjAuMF9yZWFjdCU0MDE5LjAuMF9fcmVhY3QlNDAxOS4wLjAlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmxpYiUyRm1ldGFkYXRhJTJGbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBhQUE4UDtBQUM5UDtBQUNBLGdiQUFpUTtBQUNqUTtBQUNBLGdiQUFpUTtBQUNqUTtBQUNBLDBkQUFzUjtBQUN0UjtBQUNBLDhhQUFnUTtBQUNoUTtBQUNBLDRjQUErUTtBQUMvUTtBQUNBLDRhQUErUCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuMS4wX0BwbGF5d3JpZ2h0K3Rlc3RAMS41MC4xX3JlYWN0LWRvbUAxOS4wLjBfcmVhY3RAMTkuMC4wX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuMS4wX0BwbGF5d3JpZ2h0K3Rlc3RAMS41MC4xX3JlYWN0LWRvbUAxOS4wLjBfcmVhY3RAMTkuMC4wX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuMS4wX0BwbGF5d3JpZ2h0K3Rlc3RAMS41MC4xX3JlYWN0LWRvbUAxOS4wLjBfcmVhY3RAMTkuMC4wX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuMS4wX0BwbGF5d3JpZ2h0K3Rlc3RAMS41MC4xX3JlYWN0LWRvbUAxOS4wLjBfcmVhY3RAMTkuMC4wX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9odHRwLWFjY2Vzcy1mYWxsYmFjay9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuMS4wX0BwbGF5d3JpZ2h0K3Rlc3RAMS41MC4xX3JlYWN0LWRvbUAxOS4wLjBfcmVhY3RAMTkuMC4wX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4xLjBfQHBsYXl3cmlnaHQrdGVzdEAxLjUwLjFfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjEuMF9AcGxheXdyaWdodCt0ZXN0QDEuNTAuMV9yZWFjdC1kb21AMTkuMC4wX3JlYWN0QDE5LjAuMF9fcmVhY3RAMTkuMC4wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvbGliL21ldGFkYXRhL21ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2F%40copilotkit%2Breact-ui%401.9.2-next.7_%40types%2Breact%4019.0.1_graphql%4016.9.0_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2F%40copilotkit%2Freact-ui%2Fdist%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2F%40copilotkit%2Breact-ui%401.9.2-next.7_%40types%2Breact%4019.0.1_graphql%4016.9.0_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2F%40copilotkit%2Freact-ui%2Fdist%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2F%40copilotkit%2Breact-ui%401.9.2-next.7_%40types%2Breact%4019.0.1_graphql%4016.9.0_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2F%40copilotkit%2Freact-ui%2Fdist%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2Fnext%4015.1.0_%40playwright%2Btest%401.50.1_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2Fnext%2Ffont%2Flocal%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FGeistMonoVF.woff%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22weight%5C%22%3A%5C%22100%20900%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fnode_modules%2F.pnpm%2F%40copilotkit%2Breact-ui%401.9.2-next.7_%40types%2Breact%4019.0.1_graphql%4016.9.0_react-dom%4019.0.0_react%4019.0.0__react%4019.0.0%2Fnode_modules%2F%40copilotkit%2Freact-ui%2Fdist%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4xLjBfQHBsYXl3cmlnaHQrdGVzdEAxLjUwLjFfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRm1lcmdlJTJGcHJvamVjdCUyRkNvcGlsb3RLaXQlMkZleGFtcGxlcyUyRmNvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcyUyRnVpJTJGc3JjJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFvSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvc3JjL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4xLjBfQHBsYXl3cmlnaHQrdGVzdEAxLjUwLjFfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRm1lcmdlJTJGcHJvamVjdCUyRkNvcGlsb3RLaXQlMkZleGFtcGxlcyUyRmNvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcyUyRnVpJTJGc3JjJTJGYXBwJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFvSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvc3JjL2FwcC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/Main.tsx":
/*!**************************!*\
  !*** ./src/app/Main.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Main)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ResearchCanvas__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ResearchCanvas */ \"(ssr)/./src/components/ResearchCanvas.tsx\");\n/* harmony import */ var _lib_model_selector_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/model-selector-provider */ \"(ssr)/./src/lib/model-selector-provider.tsx\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-ZHEEHGLS.mjs\");\n/* harmony import */ var _copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @copilotkit/react-ui */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-KQEMBE47.mjs\");\n/* harmony import */ var _copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @copilotkit/react-ui */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-Z4XPPVZT.mjs\");\n\n\n\n\n\n\nfunction Main() {\n    const { model, agent } = (0,_lib_model_selector_provider__WEBPACK_IMPORTED_MODULE_2__.useModelSelectorContext)();\n    const { state, setState } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__.useCoAgent)({\n        name: agent,\n        initialState: {\n            model,\n            research_question: \"\",\n            resources: [],\n            report: \"\",\n            logs: []\n        }\n    });\n    (0,_copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_4__.useCopilotChatSuggestions)({\n        instructions: \"Lifespan of penguins\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"flex h-[60px] bg-[#0E103D] text-white items-center px-10 text-2xl font-medium\",\n                children: \"Research Helper\"\n            }, void 0, false, {\n                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/Main.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 border\",\n                style: {\n                    height: \"calc(100vh - 60px)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ResearchCanvas__WEBPACK_IMPORTED_MODULE_1__.ResearchCanvas, {}, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/Main.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/Main.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-[500px] h-full flex-shrink-0\",\n                        style: {\n                            \"--copilot-kit-background-color\": \"#E0E9FD\",\n                            \"--copilot-kit-secondary-color\": \"#6766FC\",\n                            \"--copilot-kit-separator-color\": \"#b8b8b8\",\n                            \"--copilot-kit-primary-color\": \"#FFFFFF\",\n                            \"--copilot-kit-contrast-color\": \"#000000\",\n                            \"--copilot-kit-secondary-contrast-color\": \"#000\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_copilotkit_react_ui__WEBPACK_IMPORTED_MODULE_5__.CopilotChat, {\n                            className: \"h-full\",\n                            onSubmitMessage: async (message)=>{\n                                // clear the logs before starting the new research\n                                setState({\n                                    ...state,\n                                    logs: []\n                                });\n                                await new Promise((resolve)=>setTimeout(resolve, 30));\n                            },\n                            labels: {\n                                initial: \"Hi! How can I assist you with your research today?\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/Main.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/Main.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/Main.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/Main.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModelSelectorWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-RN3ZRHI7.mjs\");\n/* harmony import */ var _Main__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Main */ \"(ssr)/./src/app/Main.tsx\");\n/* harmony import */ var _lib_model_selector_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/model-selector-provider */ \"(ssr)/./src/lib/model-selector-provider.tsx\");\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ModelSelector */ \"(ssr)/./src/components/ModelSelector.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ModelSelectorWrapper() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_model_selector_provider__WEBPACK_IMPORTED_MODULE_2__.ModelSelectorProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Home, {}, void 0, false, {\n                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_3__.ModelSelector, {}, void 0, false, {\n                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\nfunction Home() {\n    const { agent, lgcDeploymentUrl } = (0,_lib_model_selector_provider__WEBPACK_IMPORTED_MODULE_2__.useModelSelectorContext)();\n    // This logic is implemented to demonstrate multi-agent frameworks in this demo project.\n    // There are cleaner ways to handle this in a production environment.\n    const runtimeUrl = lgcDeploymentUrl ? `/api/copilotkit?lgcDeploymentUrl=${lgcDeploymentUrl}` : `/api/copilotkit${agent.includes(\"crewai\") ? \"?coAgentsModel=crewai\" : \"\"}`;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__.CopilotKit, {\n        runtimeUrl: runtimeUrl,\n        showDevConsole: false,\n        agent: agent,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Main__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/page.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/page.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AddResourceDialog.tsx":
/*!**********************************************!*\
  !*** ./src/components/AddResourceDialog.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddResourceDialog: () => (/* binding */ AddResourceDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Plus_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,PlusCircle!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.451.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,PlusCircle!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.451.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n\n\n\n\n\n\nfunction AddResourceDialog({ isOpen, onOpenChange, newResource, setNewResource, addResource }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__.Dialog, {\n        open: isOpen,\n        onOpenChange: onOpenChange,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__.DialogTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    variant: \"link\",\n                    size: \"sm\",\n                    className: \"text-sm font-bold text-[#6766FC]\",\n                    children: [\n                        \"Add Resource \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-6 h-6 ml-2\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 24\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__.DialogContent, {\n                className: \"sm:max-w-[425px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__.DialogHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__.DialogTitle, {\n                            children: \"Add New Resource\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4 py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"new-url\",\n                                className: \"text-sm font-bold\",\n                                children: \"Resource URL\"\n                            }, void 0, false, {\n                                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                id: \"new-url\",\n                                placeholder: \"Resource URL\",\n                                value: newResource.url || \"\",\n                                onChange: (e)=>setNewResource({\n                                        ...newResource,\n                                        url: e.target.value\n                                    }),\n                                \"aria-label\": \"New resource URL\",\n                                className: \"bg-background\"\n                            }, void 0, false, {\n                                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"new-title\",\n                                className: \"text-sm font-bold\",\n                                children: \"Resource Title\"\n                            }, void 0, false, {\n                                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                id: \"new-title\",\n                                placeholder: \"Resource Title\",\n                                value: newResource.title || \"\",\n                                onChange: (e)=>setNewResource({\n                                        ...newResource,\n                                        title: e.target.value\n                                    }),\n                                \"aria-label\": \"New resource title\",\n                                className: \"bg-background\"\n                            }, void 0, false, {\n                                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"new-description\",\n                                className: \"text-sm font-bold\",\n                                children: \"Resource Description\"\n                            }, void 0, false, {\n                                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                                id: \"new-description\",\n                                placeholder: \"Resource Description\",\n                                value: newResource.description || \"\",\n                                onChange: (e)=>setNewResource({\n                                        ...newResource,\n                                        description: e.target.value\n                                    }),\n                                \"aria-label\": \"New resource description\",\n                                className: \"bg-background\"\n                            }, void 0, false, {\n                                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: addResource,\n                        className: \"w-full bg-[#6766FC] text-white\",\n                        disabled: !newResource.url || !newResource.title || !newResource.description,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            \" Add Resource\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/AddResourceDialog.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AddResourceDialog.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/EditResourceDialog.tsx":
/*!***********************************************!*\
  !*** ./src/components/EditResourceDialog.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditResourceDialog: () => (/* binding */ EditResourceDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n\n\n\n\n\nfunction EditResourceDialog({ isOpen, onOpenChange, editResource, setEditResource, updateResource }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__.Dialog, {\n        open: isOpen,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__.DialogContent, {\n            className: \"sm:max-w-[425px]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__.DialogHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_1__.DialogTitle, {\n                        children: \"Edit Resource\"\n                    }, void 0, false, {\n                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/EditResourceDialog.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/EditResourceDialog.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"edit-url\",\n                            className: \"text-sm font-bold\",\n                            children: \"Resource URL\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/EditResourceDialog.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                            id: \"edit-url\",\n                            placeholder: \"Resource URL\",\n                            value: editResource?.url || \"\",\n                            onChange: (e)=>setEditResource((prev)=>prev ? {\n                                        ...prev,\n                                        url: e.target.value\n                                    } : null),\n                            \"aria-label\": \"Edit resource URL\",\n                            className: \"bg-background\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/EditResourceDialog.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"edit-title\",\n                            className: \"text-sm font-bold\",\n                            children: \"Resource Title\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/EditResourceDialog.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                            id: \"edit-title\",\n                            placeholder: \"Resource Title\",\n                            value: editResource?.title || \"\",\n                            onChange: (e)=>setEditResource((prev)=>prev ? {\n                                        ...prev,\n                                        title: e.target.value\n                                    } : null),\n                            \"aria-label\": \"Edit resource title\",\n                            className: \"bg-background\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/EditResourceDialog.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"edit-description\",\n                            className: \"text-sm font-bold\",\n                            children: \"Resource Description\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/EditResourceDialog.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                            id: \"edit-description\",\n                            placeholder: \"Resource Description\",\n                            value: editResource?.description || \"\",\n                            onChange: (e)=>setEditResource((prev)=>prev ? {\n                                        ...prev,\n                                        description: e.target.value\n                                    } : null),\n                            \"aria-label\": \"Edit resource description\",\n                            className: \"bg-background\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/EditResourceDialog.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/EditResourceDialog.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    onClick: updateResource,\n                    className: \"w-full bg-[#6766FC] text-white\",\n                    disabled: !editResource?.url || !editResource?.title || !editResource?.description,\n                    children: \"Save Changes\"\n                }, void 0, false, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/EditResourceDialog.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/EditResourceDialog.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/EditResourceDialog.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/EditResourceDialog.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ModelSelector.tsx":
/*!******************************************!*\
  !*** ./src/components/ModelSelector.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModelSelector: () => (/* binding */ ModelSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/select */ \"(ssr)/./src/components/ui/select.tsx\");\n/* harmony import */ var _lib_model_selector_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/model-selector-provider */ \"(ssr)/./src/lib/model-selector-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ ModelSelector auto */ \n\n\n\nfunction ModelSelector() {\n    const { model, setModel } = (0,_lib_model_selector_provider__WEBPACK_IMPORTED_MODULE_3__.useModelSelectorContext)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-0 left-0 p-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.Select, {\n            value: model,\n            onValueChange: (v)=>setModel(v),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectTrigger, {\n                    className: \"w-[180px]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectValue, {\n                        placeholder: \"Theme\"\n                    }, void 0, false, {\n                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ModelSelector.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ModelSelector.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                            value: \"openai\",\n                            children: \"OpenAI\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ModelSelector.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                            value: \"anthropic\",\n                            children: \"Anthropic\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ModelSelector.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                            value: \"google_genai\",\n                            children: \"Google Generative AI\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ModelSelector.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_2__.SelectItem, {\n                            value: \"crewai\",\n                            children: \"CrewAI\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ModelSelector.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ModelSelector.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ModelSelector.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ModelSelector.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ModelSelector.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Progress.tsx":
/*!*************************************!*\
  !*** ./src/components/Progress.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Progress: () => (/* binding */ Progress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_LoaderCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,LoaderCircle!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.451.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_LoaderCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,LoaderCircle!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.451.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n\n\n\n\nfunction Progress({ logs }) {\n    if (logs.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-test-id\": \"progress-steps\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border border-slate-200 bg-slate-100/30 shadow-md rounded-lg overflow-hidden text-sm py-2\",\n            children: logs.map((log, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    \"data-test-id\": \"progress-step-item\",\n                    className: `flex ${log.done || index === logs.findIndex((log)=>!log.done) ? \"\" : \"opacity-50\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 bg-slate-700 flex items-center justify-center rounded-full mt-[10px] ml-[12px]\",\n                                    \"data-test-id\": log.done ? 'progress-step-item_done' : 'progress-step-item_loading',\n                                    children: log.done ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_LoaderCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-3 h-3 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Progress.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_LoaderCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-3 h-3 text-white animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Progress.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Progress.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 15\n                                }, this),\n                                index < logs.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"h-full w-[1px] bg-slate-200 ml-[20px]\")\n                                }, void 0, false, {\n                                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Progress.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Progress.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex justify-center py-2 pl-2 pr-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex items-center text-xs\",\n                                children: log.message.replace(/https?:\\/\\/[^\\s]+/g, (url)=>(0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.truncateUrl)(url) // Replace with truncated URL\n                                )\n                            }, void 0, false, {\n                                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Progress.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Progress.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, index, true, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Progress.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Progress.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Progress.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Progress.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ResearchCanvas.tsx":
/*!*******************************************!*\
  !*** ./src/components/ResearchCanvas.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResearchCanvas: () => (/* binding */ ResearchCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-ZHEEHGLS.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-T4ZKC4X4.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-JHIZ5HAI.mjs\");\n/* harmony import */ var _Progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Progress */ \"(ssr)/./src/components/Progress.tsx\");\n/* harmony import */ var _EditResourceDialog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./EditResourceDialog */ \"(ssr)/./src/components/EditResourceDialog.tsx\");\n/* harmony import */ var _AddResourceDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./AddResourceDialog */ \"(ssr)/./src/components/AddResourceDialog.tsx\");\n/* harmony import */ var _Resources__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Resources */ \"(ssr)/./src/components/Resources.tsx\");\n/* harmony import */ var _lib_model_selector_provider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/model-selector-provider */ \"(ssr)/./src/lib/model-selector-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ ResearchCanvas auto */ \n\n\n\n\n\n\n\n\n\nfunction ResearchCanvas() {\n    const { model, agent } = (0,_lib_model_selector_provider__WEBPACK_IMPORTED_MODULE_8__.useModelSelectorContext)();\n    const { state, setState } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_9__.useCoAgent)({\n        name: agent,\n        initialState: {\n            model\n        }\n    });\n    (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_10__.useCoAgentStateRender)({\n        name: agent,\n        render: {\n            \"ResearchCanvas.useCoAgentStateRender\": ({ state, nodeName, status })=>{\n                if (!state.logs || state.logs.length === 0) {\n                    return null;\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                    logs: state.logs\n                }, void 0, false, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 14\n                }, this);\n            }\n        }[\"ResearchCanvas.useCoAgentStateRender\"]\n    });\n    (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_11__.useCopilotAction)({\n        name: \"DeleteResources\",\n        description: \"Prompt the user for resource delete confirmation, and then perform resource deletion\",\n        available: \"remote\",\n        parameters: [\n            {\n                name: \"urls\",\n                type: \"string[]\"\n            }\n        ],\n        renderAndWait: {\n            \"ResearchCanvas.useCopilotAction\": ({ args, status, handler })=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\",\n                    \"data-test-id\": \"delete-resource-generative-ui-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold text-base mb-2\",\n                            children: \"Delete these resources?\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Resources__WEBPACK_IMPORTED_MODULE_7__.Resources, {\n                            resources: resources.filter({\n                                \"ResearchCanvas.useCopilotAction\": (resource)=>(args.urls || []).includes(resource.url)\n                            }[\"ResearchCanvas.useCopilotAction\"]),\n                            customWidth: 200\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        status === \"executing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex justify-start space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: {\n                                        \"ResearchCanvas.useCopilotAction\": ()=>handler(\"NO\")\n                                    }[\"ResearchCanvas.useCopilotAction\"],\n                                    className: \"px-4 py-2 text-[#6766FC] border border-[#6766FC] rounded text-sm font-bold\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    \"data-test-id\": \"button-delete\",\n                                    onClick: {\n                                        \"ResearchCanvas.useCopilotAction\": ()=>handler(\"YES\")\n                                    }[\"ResearchCanvas.useCopilotAction\"],\n                                    className: \"px-4 py-2 bg-[#6766FC] text-white rounded text-sm font-bold\",\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this);\n            }\n        }[\"ResearchCanvas.useCopilotAction\"]\n    });\n    const resources = state.resources || [];\n    const setResources = (resources)=>{\n        setState({\n            ...state,\n            resources\n        });\n    };\n    // const [resources, setResources] = useState<Resource[]>(dummyResources);\n    const [newResource, setNewResource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        url: \"\",\n        title: \"\",\n        description: \"\"\n    });\n    const [isAddResourceOpen, setIsAddResourceOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const addResource = ()=>{\n        if (newResource.url) {\n            setResources([\n                ...resources,\n                {\n                    ...newResource\n                }\n            ]);\n            setNewResource({\n                url: \"\",\n                title: \"\",\n                description: \"\"\n            });\n            setIsAddResourceOpen(false);\n        }\n    };\n    const removeResource = (url)=>{\n        setResources(resources.filter((resource)=>resource.url !== url));\n    };\n    const [editResource, setEditResource] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [originalUrl, setOriginalUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditResourceOpen, setIsEditResourceOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCardClick = (resource)=>{\n        setEditResource({\n            ...resource\n        }); // Ensure a new object is created\n        setOriginalUrl(resource.url); // Store the original URL\n        setIsEditResourceOpen(true);\n    };\n    const updateResource = ()=>{\n        if (editResource && originalUrl) {\n            setResources(resources.map((resource)=>resource.url === originalUrl ? {\n                    ...editResource\n                } : resource));\n            setEditResource(null);\n            setOriginalUrl(null);\n            setIsEditResourceOpen(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full overflow-y-auto p-10 bg-[#F5F8FF]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8 pb-10\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-medium mb-3 text-primary\",\n                            children: \"Research Question\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                            placeholder: \"Enter your research question\",\n                            value: state.research_question || \"\",\n                            onChange: (e)=>setState({\n                                    ...state,\n                                    research_question: e.target.value\n                                }),\n                            \"aria-label\": \"Research question\",\n                            className: \"bg-background px-6 py-8 border-0 shadow-none rounded-xl text-md font-extralight focus-visible:ring-0 placeholder:text-slate-400\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-medium text-primary\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditResourceDialog__WEBPACK_IMPORTED_MODULE_5__.EditResourceDialog, {\n                                    isOpen: isEditResourceOpen,\n                                    onOpenChange: setIsEditResourceOpen,\n                                    editResource: editResource,\n                                    setEditResource: setEditResource,\n                                    updateResource: updateResource\n                                }, void 0, false, {\n                                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddResourceDialog__WEBPACK_IMPORTED_MODULE_6__.AddResourceDialog, {\n                                    isOpen: isAddResourceOpen,\n                                    onOpenChange: setIsAddResourceOpen,\n                                    newResource: newResource,\n                                    setNewResource: setNewResource,\n                                    addResource: addResource\n                                }, void 0, false, {\n                                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        resources.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-slate-400\",\n                            children: \"Click the button above to add resources.\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        resources.length !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Resources__WEBPACK_IMPORTED_MODULE_7__.Resources, {\n                            resources: resources,\n                            handleCardClick: handleCardClick,\n                            removeResource: removeResource\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-medium mb-3 text-primary\",\n                            children: \"Research Draft\"\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                            \"data-test-id\": \"research-draft\",\n                            placeholder: \"Write your research draft here\",\n                            value: state.report || \"\",\n                            onChange: (e)=>setState({\n                                    ...state,\n                                    report: e.target.value\n                                }),\n                            rows: 10,\n                            \"aria-label\": \"Research draft\",\n                            className: \"bg-background px-6 py-8 border-0 shadow-none rounded-xl text-md font-extralight focus-visible:ring-0 placeholder:text-slate-400\",\n                            style: {\n                                minHeight: \"200px\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ResearchCanvas.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9SZXNlYXJjaENhbnZhcy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVpQztBQUNhO0FBQ007QUFLcEI7QUFDTTtBQUNvQjtBQUNGO0FBQ2hCO0FBRWdDO0FBRWpFLFNBQVNXO0lBQ2QsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLEtBQUssRUFBRSxHQUFHSCxxRkFBdUJBO0lBRWhELE1BQU0sRUFBRUksS0FBSyxFQUFFQyxRQUFRLEVBQUUsR0FBR1osa0VBQVVBLENBQWE7UUFDakRhLE1BQU1IO1FBQ05JLGNBQWM7WUFDWkw7UUFDRjtJQUNGO0lBRUFSLDhFQUFxQkEsQ0FBQztRQUNwQlksTUFBTUg7UUFDTkssTUFBTTtvREFBRSxDQUFDLEVBQUVKLEtBQUssRUFBRUssUUFBUSxFQUFFQyxNQUFNLEVBQUU7Z0JBQ2xDLElBQUksQ0FBQ04sTUFBTU8sSUFBSSxJQUFJUCxNQUFNTyxJQUFJLENBQUNDLE1BQU0sS0FBSyxHQUFHO29CQUMxQyxPQUFPO2dCQUNUO2dCQUNBLHFCQUFPLDhEQUFDaEIsK0NBQVFBO29CQUFDZSxNQUFNUCxNQUFNTyxJQUFJOzs7Ozs7WUFDbkM7O0lBQ0Y7SUFFQWhCLHlFQUFnQkEsQ0FBQztRQUNmVyxNQUFNO1FBQ05PLGFBQ0U7UUFDRkMsV0FBVztRQUNYQyxZQUFZO1lBQ1Y7Z0JBQ0VULE1BQU07Z0JBQ05VLE1BQU07WUFDUjtTQUNEO1FBQ0RDLGFBQWE7K0NBQUUsQ0FBQyxFQUFFQyxJQUFJLEVBQUVSLE1BQU0sRUFBRVMsT0FBTyxFQUFFO2dCQUN2QyxxQkFDRSw4REFBQ0M7b0JBQ0NDLFdBQVU7b0JBQ1ZDLGdCQUFhOztzQ0FFYiw4REFBQ0Y7NEJBQUlDLFdBQVU7c0NBQTJCOzs7Ozs7c0NBRzFDLDhEQUFDdEIsaURBQVNBOzRCQUNSd0IsV0FBV0EsVUFBVUMsTUFBTTttRUFBQyxDQUFDQyxXQUMzQixDQUFDUCxLQUFLUSxJQUFJLElBQUksRUFBRSxFQUFFQyxRQUFRLENBQUNGLFNBQVNHLEdBQUc7OzRCQUV6Q0MsYUFBYTs7Ozs7O3dCQUVkbkIsV0FBVyw2QkFDViw4REFBQ1U7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDUztvQ0FDQ0MsT0FBTzsyRUFBRSxJQUFNWixRQUFROztvQ0FDdkJFLFdBQVU7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQ1M7b0NBQ0NSLGdCQUFhO29DQUNiUyxPQUFPOzJFQUFFLElBQU1aLFFBQVE7O29DQUN2QkUsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBT1g7O0lBQ0Y7SUFFQSxNQUFNRSxZQUF3Qm5CLE1BQU1tQixTQUFTLElBQUksRUFBRTtJQUNuRCxNQUFNUyxlQUFlLENBQUNUO1FBQ3BCbEIsU0FBUztZQUFFLEdBQUdELEtBQUs7WUFBRW1CO1FBQVU7SUFDakM7SUFFQSwwRUFBMEU7SUFDMUUsTUFBTSxDQUFDVSxhQUFhQyxlQUFlLEdBQUc1QywrQ0FBUUEsQ0FBVztRQUN2RHNDLEtBQUs7UUFDTE8sT0FBTztRQUNQdEIsYUFBYTtJQUNmO0lBQ0EsTUFBTSxDQUFDdUIsbUJBQW1CQyxxQkFBcUIsR0FBRy9DLCtDQUFRQSxDQUFDO0lBRTNELE1BQU1nRCxjQUFjO1FBQ2xCLElBQUlMLFlBQVlMLEdBQUcsRUFBRTtZQUNuQkksYUFBYTttQkFBSVQ7Z0JBQVc7b0JBQUUsR0FBR1UsV0FBVztnQkFBQzthQUFFO1lBQy9DQyxlQUFlO2dCQUFFTixLQUFLO2dCQUFJTyxPQUFPO2dCQUFJdEIsYUFBYTtZQUFHO1lBQ3JEd0IscUJBQXFCO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNRSxpQkFBaUIsQ0FBQ1g7UUFDdEJJLGFBQ0VULFVBQVVDLE1BQU0sQ0FBQyxDQUFDQyxXQUF1QkEsU0FBU0csR0FBRyxLQUFLQTtJQUU5RDtJQUVBLE1BQU0sQ0FBQ1ksY0FBY0MsZ0JBQWdCLEdBQUduRCwrQ0FBUUEsQ0FBa0I7SUFDbEUsTUFBTSxDQUFDb0QsYUFBYUMsZUFBZSxHQUFHckQsK0NBQVFBLENBQWdCO0lBQzlELE1BQU0sQ0FBQ3NELG9CQUFvQkMsc0JBQXNCLEdBQUd2RCwrQ0FBUUEsQ0FBQztJQUU3RCxNQUFNd0Qsa0JBQWtCLENBQUNyQjtRQUN2QmdCLGdCQUFnQjtZQUFFLEdBQUdoQixRQUFRO1FBQUMsSUFBSSxpQ0FBaUM7UUFDbkVrQixlQUFlbEIsU0FBU0csR0FBRyxHQUFHLHlCQUF5QjtRQUN2RGlCLHNCQUFzQjtJQUN4QjtJQUVBLE1BQU1FLGlCQUFpQjtRQUNyQixJQUFJUCxnQkFBZ0JFLGFBQWE7WUFDL0JWLGFBQ0VULFVBQVV5QixHQUFHLENBQUMsQ0FBQ3ZCLFdBQ2JBLFNBQVNHLEdBQUcsS0FBS2MsY0FBYztvQkFBRSxHQUFHRixZQUFZO2dCQUFDLElBQUlmO1lBR3pEZ0IsZ0JBQWdCO1lBQ2hCRSxlQUFlO1lBQ2ZFLHNCQUFzQjtRQUN4QjtJQUNGO0lBRUEscUJBQ0UsOERBQUN6QjtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7O3NDQUNDLDhEQUFDNkI7NEJBQUc1QixXQUFVO3NDQUF3Qzs7Ozs7O3NDQUd0RCw4REFBQzlCLHVEQUFLQTs0QkFDSjJELGFBQVk7NEJBQ1pDLE9BQU8vQyxNQUFNZ0QsaUJBQWlCLElBQUk7NEJBQ2xDQyxVQUFVLENBQUNDLElBQ1RqRCxTQUFTO29DQUFFLEdBQUdELEtBQUs7b0NBQUVnRCxtQkFBbUJFLEVBQUVDLE1BQU0sQ0FBQ0osS0FBSztnQ0FBQzs0QkFFekRLLGNBQVc7NEJBQ1huQyxXQUFVOzs7Ozs7Ozs7Ozs7OEJBSWQsOERBQUNEOztzQ0FDQyw4REFBQ0E7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDNEI7b0NBQUc1QixXQUFVOzhDQUFtQzs7Ozs7OzhDQUNqRCw4REFBQ3hCLG1FQUFrQkE7b0NBQ2pCNEQsUUFBUWI7b0NBQ1JjLGNBQWNiO29DQUNkTCxjQUFjQTtvQ0FDZEMsaUJBQWlCQTtvQ0FDakJNLGdCQUFnQkE7Ozs7Ozs4Q0FFbEIsOERBQUNqRCxpRUFBaUJBO29DQUNoQjJELFFBQVFyQjtvQ0FDUnNCLGNBQWNyQjtvQ0FDZEosYUFBYUE7b0NBQ2JDLGdCQUFnQkE7b0NBQ2hCSSxhQUFhQTs7Ozs7Ozs7Ozs7O3dCQUdoQmYsVUFBVVgsTUFBTSxLQUFLLG1CQUNwQiw4REFBQ1E7NEJBQUlDLFdBQVU7c0NBQXlCOzs7Ozs7d0JBS3pDRSxVQUFVWCxNQUFNLEtBQUssbUJBQ3BCLDhEQUFDYixpREFBU0E7NEJBQ1J3QixXQUFXQTs0QkFDWHVCLGlCQUFpQkE7NEJBQ2pCUCxnQkFBZ0JBOzs7Ozs7Ozs7Ozs7OEJBS3RCLDhEQUFDbkI7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDNEI7NEJBQUc1QixXQUFVO3NDQUF3Qzs7Ozs7O3NDQUd0RCw4REFBQzdCLDZEQUFRQTs0QkFDUDhCLGdCQUFhOzRCQUNiNEIsYUFBWTs0QkFDWkMsT0FBTy9DLE1BQU11RCxNQUFNLElBQUk7NEJBQ3ZCTixVQUFVLENBQUNDLElBQU1qRCxTQUFTO29DQUFFLEdBQUdELEtBQUs7b0NBQUV1RCxRQUFRTCxFQUFFQyxNQUFNLENBQUNKLEtBQUs7Z0NBQUM7NEJBQzdEUyxNQUFNOzRCQUNOSixjQUFXOzRCQUNYbkMsV0FBVTs0QkFDVndDLE9BQU87Z0NBQUVDLFdBQVc7NEJBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXhDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9zcmMvY29tcG9uZW50cy9SZXNlYXJjaENhbnZhcy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIjtcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90ZXh0YXJlYVwiO1xuaW1wb3J0IHtcbiAgdXNlQ29BZ2VudCxcbiAgdXNlQ29BZ2VudFN0YXRlUmVuZGVyLFxuICB1c2VDb3BpbG90QWN0aW9uLFxufSBmcm9tIFwiQGNvcGlsb3RraXQvcmVhY3QtY29yZVwiO1xuaW1wb3J0IHsgUHJvZ3Jlc3MgfSBmcm9tIFwiLi9Qcm9ncmVzc1wiO1xuaW1wb3J0IHsgRWRpdFJlc291cmNlRGlhbG9nIH0gZnJvbSBcIi4vRWRpdFJlc291cmNlRGlhbG9nXCI7XG5pbXBvcnQgeyBBZGRSZXNvdXJjZURpYWxvZyB9IGZyb20gXCIuL0FkZFJlc291cmNlRGlhbG9nXCI7XG5pbXBvcnQgeyBSZXNvdXJjZXMgfSBmcm9tIFwiLi9SZXNvdXJjZXNcIjtcbmltcG9ydCB7IEFnZW50U3RhdGUsIFJlc291cmNlIH0gZnJvbSBcIkAvbGliL3R5cGVzXCI7XG5pbXBvcnQgeyB1c2VNb2RlbFNlbGVjdG9yQ29udGV4dCB9IGZyb20gXCJAL2xpYi9tb2RlbC1zZWxlY3Rvci1wcm92aWRlclwiO1xuXG5leHBvcnQgZnVuY3Rpb24gUmVzZWFyY2hDYW52YXMoKSB7XG4gIGNvbnN0IHsgbW9kZWwsIGFnZW50IH0gPSB1c2VNb2RlbFNlbGVjdG9yQ29udGV4dCgpO1xuXG4gIGNvbnN0IHsgc3RhdGUsIHNldFN0YXRlIH0gPSB1c2VDb0FnZW50PEFnZW50U3RhdGU+KHtcbiAgICBuYW1lOiBhZ2VudCxcbiAgICBpbml0aWFsU3RhdGU6IHtcbiAgICAgIG1vZGVsLFxuICAgIH0sXG4gIH0pO1xuXG4gIHVzZUNvQWdlbnRTdGF0ZVJlbmRlcih7XG4gICAgbmFtZTogYWdlbnQsXG4gICAgcmVuZGVyOiAoeyBzdGF0ZSwgbm9kZU5hbWUsIHN0YXR1cyB9KSA9PiB7XG4gICAgICBpZiAoIXN0YXRlLmxvZ3MgfHwgc3RhdGUubG9ncy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9XG4gICAgICByZXR1cm4gPFByb2dyZXNzIGxvZ3M9e3N0YXRlLmxvZ3N9IC8+O1xuICAgIH0sXG4gIH0pO1xuXG4gIHVzZUNvcGlsb3RBY3Rpb24oe1xuICAgIG5hbWU6IFwiRGVsZXRlUmVzb3VyY2VzXCIsXG4gICAgZGVzY3JpcHRpb246XG4gICAgICBcIlByb21wdCB0aGUgdXNlciBmb3IgcmVzb3VyY2UgZGVsZXRlIGNvbmZpcm1hdGlvbiwgYW5kIHRoZW4gcGVyZm9ybSByZXNvdXJjZSBkZWxldGlvblwiLFxuICAgIGF2YWlsYWJsZTogXCJyZW1vdGVcIixcbiAgICBwYXJhbWV0ZXJzOiBbXG4gICAgICB7XG4gICAgICAgIG5hbWU6IFwidXJsc1wiLFxuICAgICAgICB0eXBlOiBcInN0cmluZ1tdXCIsXG4gICAgICB9LFxuICAgIF0sXG4gICAgcmVuZGVyQW5kV2FpdDogKHsgYXJncywgc3RhdHVzLCBoYW5kbGVyIH0pID0+IHtcbiAgICAgIHJldHVybiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICBjbGFzc05hbWU9XCJcIlxuICAgICAgICAgIGRhdGEtdGVzdC1pZD1cImRlbGV0ZS1yZXNvdXJjZS1nZW5lcmF0aXZlLXVpLWNvbnRhaW5lclwiXG4gICAgICAgID5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LWJhc2UgbWItMlwiPlxuICAgICAgICAgICAgRGVsZXRlIHRoZXNlIHJlc291cmNlcz9cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8UmVzb3VyY2VzXG4gICAgICAgICAgICByZXNvdXJjZXM9e3Jlc291cmNlcy5maWx0ZXIoKHJlc291cmNlKSA9PlxuICAgICAgICAgICAgICAoYXJncy51cmxzIHx8IFtdKS5pbmNsdWRlcyhyZXNvdXJjZS51cmwpXG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgY3VzdG9tV2lkdGg9ezIwMH1cbiAgICAgICAgICAvPlxuICAgICAgICAgIHtzdGF0dXMgPT09IFwiZXhlY3V0aW5nXCIgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IGZsZXgganVzdGlmeS1zdGFydCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZXIoXCJOT1wiKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1bIzY3NjZGQ10gYm9yZGVyIGJvcmRlci1bIzY3NjZGQ10gcm91bmRlZCB0ZXh0LXNtIGZvbnQtYm9sZFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBkYXRhLXRlc3QtaWQ9XCJidXR0b24tZGVsZXRlXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVyKFwiWUVTXCIpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1bIzY3NjZGQ10gdGV4dC13aGl0ZSByb3VuZGVkIHRleHQtc20gZm9udC1ib2xkXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIERlbGV0ZVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKTtcbiAgICB9LFxuICB9KTtcblxuICBjb25zdCByZXNvdXJjZXM6IFJlc291cmNlW10gPSBzdGF0ZS5yZXNvdXJjZXMgfHwgW107XG4gIGNvbnN0IHNldFJlc291cmNlcyA9IChyZXNvdXJjZXM6IFJlc291cmNlW10pID0+IHtcbiAgICBzZXRTdGF0ZSh7IC4uLnN0YXRlLCByZXNvdXJjZXMgfSk7XG4gIH07XG5cbiAgLy8gY29uc3QgW3Jlc291cmNlcywgc2V0UmVzb3VyY2VzXSA9IHVzZVN0YXRlPFJlc291cmNlW10+KGR1bW15UmVzb3VyY2VzKTtcbiAgY29uc3QgW25ld1Jlc291cmNlLCBzZXROZXdSZXNvdXJjZV0gPSB1c2VTdGF0ZTxSZXNvdXJjZT4oe1xuICAgIHVybDogXCJcIixcbiAgICB0aXRsZTogXCJcIixcbiAgICBkZXNjcmlwdGlvbjogXCJcIixcbiAgfSk7XG4gIGNvbnN0IFtpc0FkZFJlc291cmNlT3Blbiwgc2V0SXNBZGRSZXNvdXJjZU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IGFkZFJlc291cmNlID0gKCkgPT4ge1xuICAgIGlmIChuZXdSZXNvdXJjZS51cmwpIHtcbiAgICAgIHNldFJlc291cmNlcyhbLi4ucmVzb3VyY2VzLCB7IC4uLm5ld1Jlc291cmNlIH1dKTtcbiAgICAgIHNldE5ld1Jlc291cmNlKHsgdXJsOiBcIlwiLCB0aXRsZTogXCJcIiwgZGVzY3JpcHRpb246IFwiXCIgfSk7XG4gICAgICBzZXRJc0FkZFJlc291cmNlT3BlbihmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHJlbW92ZVJlc291cmNlID0gKHVybDogc3RyaW5nKSA9PiB7XG4gICAgc2V0UmVzb3VyY2VzKFxuICAgICAgcmVzb3VyY2VzLmZpbHRlcigocmVzb3VyY2U6IFJlc291cmNlKSA9PiByZXNvdXJjZS51cmwgIT09IHVybClcbiAgICApO1xuICB9O1xuXG4gIGNvbnN0IFtlZGl0UmVzb3VyY2UsIHNldEVkaXRSZXNvdXJjZV0gPSB1c2VTdGF0ZTxSZXNvdXJjZSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbb3JpZ2luYWxVcmwsIHNldE9yaWdpbmFsVXJsXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNFZGl0UmVzb3VyY2VPcGVuLCBzZXRJc0VkaXRSZXNvdXJjZU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IGhhbmRsZUNhcmRDbGljayA9IChyZXNvdXJjZTogUmVzb3VyY2UpID0+IHtcbiAgICBzZXRFZGl0UmVzb3VyY2UoeyAuLi5yZXNvdXJjZSB9KTsgLy8gRW5zdXJlIGEgbmV3IG9iamVjdCBpcyBjcmVhdGVkXG4gICAgc2V0T3JpZ2luYWxVcmwocmVzb3VyY2UudXJsKTsgLy8gU3RvcmUgdGhlIG9yaWdpbmFsIFVSTFxuICAgIHNldElzRWRpdFJlc291cmNlT3Blbih0cnVlKTtcbiAgfTtcblxuICBjb25zdCB1cGRhdGVSZXNvdXJjZSA9ICgpID0+IHtcbiAgICBpZiAoZWRpdFJlc291cmNlICYmIG9yaWdpbmFsVXJsKSB7XG4gICAgICBzZXRSZXNvdXJjZXMoXG4gICAgICAgIHJlc291cmNlcy5tYXAoKHJlc291cmNlKSA9PlxuICAgICAgICAgIHJlc291cmNlLnVybCA9PT0gb3JpZ2luYWxVcmwgPyB7IC4uLmVkaXRSZXNvdXJjZSB9IDogcmVzb3VyY2VcbiAgICAgICAgKVxuICAgICAgKTtcbiAgICAgIHNldEVkaXRSZXNvdXJjZShudWxsKTtcbiAgICAgIHNldE9yaWdpbmFsVXJsKG51bGwpO1xuICAgICAgc2V0SXNFZGl0UmVzb3VyY2VPcGVuKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb3ZlcmZsb3cteS1hdXRvIHAtMTAgYmctWyNGNUY4RkZdXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOCBwYi0xMFwiPlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIG1iLTMgdGV4dC1wcmltYXJ5XCI+XG4gICAgICAgICAgICBSZXNlYXJjaCBRdWVzdGlvblxuICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgPElucHV0XG4gICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgcmVzZWFyY2ggcXVlc3Rpb25cIlxuICAgICAgICAgICAgdmFsdWU9e3N0YXRlLnJlc2VhcmNoX3F1ZXN0aW9uIHx8IFwiXCJ9XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XG4gICAgICAgICAgICAgIHNldFN0YXRlKHsgLi4uc3RhdGUsIHJlc2VhcmNoX3F1ZXN0aW9uOiBlLnRhcmdldC52YWx1ZSB9KVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYXJpYS1sYWJlbD1cIlJlc2VhcmNoIHF1ZXN0aW9uXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJhY2tncm91bmQgcHgtNiBweS04IGJvcmRlci0wIHNoYWRvdy1ub25lIHJvdW5kZWQteGwgdGV4dC1tZCBmb250LWV4dHJhbGlnaHQgZm9jdXMtdmlzaWJsZTpyaW5nLTAgcGxhY2Vob2xkZXI6dGV4dC1zbGF0ZS00MDBcIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1wcmltYXJ5XCI+UmVzb3VyY2VzPC9oMj5cbiAgICAgICAgICAgIDxFZGl0UmVzb3VyY2VEaWFsb2dcbiAgICAgICAgICAgICAgaXNPcGVuPXtpc0VkaXRSZXNvdXJjZU9wZW59XG4gICAgICAgICAgICAgIG9uT3BlbkNoYW5nZT17c2V0SXNFZGl0UmVzb3VyY2VPcGVufVxuICAgICAgICAgICAgICBlZGl0UmVzb3VyY2U9e2VkaXRSZXNvdXJjZX1cbiAgICAgICAgICAgICAgc2V0RWRpdFJlc291cmNlPXtzZXRFZGl0UmVzb3VyY2V9XG4gICAgICAgICAgICAgIHVwZGF0ZVJlc291cmNlPXt1cGRhdGVSZXNvdXJjZX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8QWRkUmVzb3VyY2VEaWFsb2dcbiAgICAgICAgICAgICAgaXNPcGVuPXtpc0FkZFJlc291cmNlT3Blbn1cbiAgICAgICAgICAgICAgb25PcGVuQ2hhbmdlPXtzZXRJc0FkZFJlc291cmNlT3Blbn1cbiAgICAgICAgICAgICAgbmV3UmVzb3VyY2U9e25ld1Jlc291cmNlfVxuICAgICAgICAgICAgICBzZXROZXdSZXNvdXJjZT17c2V0TmV3UmVzb3VyY2V9XG4gICAgICAgICAgICAgIGFkZFJlc291cmNlPXthZGRSZXNvdXJjZX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAge3Jlc291cmNlcy5sZW5ndGggPT09IDAgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtNDAwXCI+XG4gICAgICAgICAgICAgIENsaWNrIHRoZSBidXR0b24gYWJvdmUgdG8gYWRkIHJlc291cmNlcy5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7cmVzb3VyY2VzLmxlbmd0aCAhPT0gMCAmJiAoXG4gICAgICAgICAgICA8UmVzb3VyY2VzXG4gICAgICAgICAgICAgIHJlc291cmNlcz17cmVzb3VyY2VzfVxuICAgICAgICAgICAgICBoYW5kbGVDYXJkQ2xpY2s9e2hhbmRsZUNhcmRDbGlja31cbiAgICAgICAgICAgICAgcmVtb3ZlUmVzb3VyY2U9e3JlbW92ZVJlc291cmNlfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsXCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gbWItMyB0ZXh0LXByaW1hcnlcIj5cbiAgICAgICAgICAgIFJlc2VhcmNoIERyYWZ0XG4gICAgICAgICAgPC9oMj5cbiAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgIGRhdGEtdGVzdC1pZD1cInJlc2VhcmNoLWRyYWZ0XCJcbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiV3JpdGUgeW91ciByZXNlYXJjaCBkcmFmdCBoZXJlXCJcbiAgICAgICAgICAgIHZhbHVlPXtzdGF0ZS5yZXBvcnQgfHwgXCJcIn1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U3RhdGUoeyAuLi5zdGF0ZSwgcmVwb3J0OiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgIHJvd3M9ezEwfVxuICAgICAgICAgICAgYXJpYS1sYWJlbD1cIlJlc2VhcmNoIGRyYWZ0XCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJhY2tncm91bmQgcHgtNiBweS04IGJvcmRlci0wIHNoYWRvdy1ub25lIHJvdW5kZWQteGwgdGV4dC1tZCBmb250LWV4dHJhbGlnaHQgZm9jdXMtdmlzaWJsZTpyaW5nLTAgcGxhY2Vob2xkZXI6dGV4dC1zbGF0ZS00MDBcIlxuICAgICAgICAgICAgc3R5bGU9e3sgbWluSGVpZ2h0OiBcIjIwMHB4XCIgfX1cbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiSW5wdXQiLCJUZXh0YXJlYSIsInVzZUNvQWdlbnQiLCJ1c2VDb0FnZW50U3RhdGVSZW5kZXIiLCJ1c2VDb3BpbG90QWN0aW9uIiwiUHJvZ3Jlc3MiLCJFZGl0UmVzb3VyY2VEaWFsb2ciLCJBZGRSZXNvdXJjZURpYWxvZyIsIlJlc291cmNlcyIsInVzZU1vZGVsU2VsZWN0b3JDb250ZXh0IiwiUmVzZWFyY2hDYW52YXMiLCJtb2RlbCIsImFnZW50Iiwic3RhdGUiLCJzZXRTdGF0ZSIsIm5hbWUiLCJpbml0aWFsU3RhdGUiLCJyZW5kZXIiLCJub2RlTmFtZSIsInN0YXR1cyIsImxvZ3MiLCJsZW5ndGgiLCJkZXNjcmlwdGlvbiIsImF2YWlsYWJsZSIsInBhcmFtZXRlcnMiLCJ0eXBlIiwicmVuZGVyQW5kV2FpdCIsImFyZ3MiLCJoYW5kbGVyIiwiZGl2IiwiY2xhc3NOYW1lIiwiZGF0YS10ZXN0LWlkIiwicmVzb3VyY2VzIiwiZmlsdGVyIiwicmVzb3VyY2UiLCJ1cmxzIiwiaW5jbHVkZXMiLCJ1cmwiLCJjdXN0b21XaWR0aCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzZXRSZXNvdXJjZXMiLCJuZXdSZXNvdXJjZSIsInNldE5ld1Jlc291cmNlIiwidGl0bGUiLCJpc0FkZFJlc291cmNlT3BlbiIsInNldElzQWRkUmVzb3VyY2VPcGVuIiwiYWRkUmVzb3VyY2UiLCJyZW1vdmVSZXNvdXJjZSIsImVkaXRSZXNvdXJjZSIsInNldEVkaXRSZXNvdXJjZSIsIm9yaWdpbmFsVXJsIiwic2V0T3JpZ2luYWxVcmwiLCJpc0VkaXRSZXNvdXJjZU9wZW4iLCJzZXRJc0VkaXRSZXNvdXJjZU9wZW4iLCJoYW5kbGVDYXJkQ2xpY2siLCJ1cGRhdGVSZXNvdXJjZSIsIm1hcCIsImgyIiwicGxhY2Vob2xkZXIiLCJ2YWx1ZSIsInJlc2VhcmNoX3F1ZXN0aW9uIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwiYXJpYS1sYWJlbCIsImlzT3BlbiIsIm9uT3BlbkNoYW5nZSIsInJlcG9ydCIsInJvd3MiLCJzdHlsZSIsIm1pbkhlaWdodCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ResearchCanvas.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Resources.tsx":
/*!**************************************!*\
  !*** ./src/components/Resources.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Resources: () => (/* binding */ Resources)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Trash2!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.451.0_react@19.0.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nfunction Resources({ resources, handleCardClick, removeResource, customWidth }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-test-id\": \"resources\",\n        className: \"flex space-x-3 overflow-x-auto\",\n        children: resources.map((resource, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                \"data-test-id\": `resource`,\n                className: \"bg-background border-0 shadow-none rounded-xl text-md font-extralight focus-visible:ring-0 flex-none\" + (handleCardClick ? \" cursor-pointer\" : \"\"),\n                style: {\n                    width: customWidth + \"px\" || 0\n                },\n                onClick: ()=>handleCardClick?.(resource),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                    className: \"px-6 py-6 relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-3 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-grow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold text-lg\",\n                                        style: {\n                                            maxWidth: customWidth ? customWidth - 30 + \"px\" : \"230px\",\n                                            overflow: \"hidden\",\n                                            textOverflow: \"ellipsis\",\n                                            whiteSpace: \"nowrap\"\n                                        },\n                                        children: resource.title\n                                    }, void 0, false, {\n                                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Resources.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base mt-2\",\n                                        style: {\n                                            maxWidth: customWidth ? customWidth - 30 + \"px\" : \"250px\",\n                                            overflowWrap: \"break-word\"\n                                        },\n                                        children: resource.description?.length > 250 ? resource.description.slice(0, 250) + \"...\" : resource.description\n                                    }, void 0, false, {\n                                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Resources.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: resource.url,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"text-sm text-primary hover:underline mt-3 text-slate-400 inline-block\",\n                                        title: resource.url,\n                                        style: {\n                                            width: customWidth ? customWidth - 30 + \"px\" : \"250px\",\n                                            overflow: \"hidden\",\n                                            textOverflow: \"ellipsis\",\n                                            whiteSpace: \"nowrap\"\n                                        },\n                                        children: resource.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: `https://www.google.com/s2/favicons?domain=${resource.url}`,\n                                                    alt: \"favicon\",\n                                                    className: \"inline-block mr-2\",\n                                                    style: {\n                                                        width: \"16px\",\n                                                        height: \"16px\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Resources.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 23\n                                                }, this),\n                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.truncateUrl)(resource.url)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Resources.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Resources.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 15\n                            }, this),\n                            removeResource && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start absolute top-4 right-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    \"data-test-id\": \"remove-resource\",\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        removeResource?.(resource.url);\n                                    },\n                                    \"aria-label\": `Remove ${resource.url}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 text-gray-400 hover:text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Resources.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Resources.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Resources.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Resources.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Resources.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, this)\n            }, idx, false, {\n                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Resources.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/Resources.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Resources.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.0_@types+react@19.0.1_react@19.0.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.0/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4Qix1T0FDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FDRTtZQUNGQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTUssdUJBQVNsQiw2Q0FBZ0IsQ0FDN0IsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFZCxPQUFPLEVBQUVPLElBQUksRUFBRVEsVUFBVSxLQUFLLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4RCxNQUFNQyxPQUFPSCxVQUFVcEIsc0RBQUlBLEdBQUc7SUFDOUIscUJBQ0UsOERBQUN1QjtRQUNDSixXQUFXakIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU87UUFBVTtRQUN4REcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixPQUFPTyxXQUFXLEdBQUc7QUFFWSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvc3JjL2NvbXBvbmVudHMvdWkvYnV0dG9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgU2xvdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3Qtc2xvdFwiXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgYnV0dG9uVmFyaWFudHMgPSBjdmEoXG4gIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHdoaXRlc3BhY2Utbm93cmFwIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycyBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMSBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgXCJiZy1wcmltYXJ5IHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIHNoYWRvdyBob3ZlcjpiZy1wcmltYXJ5LzkwXCIsXG4gICAgICAgIGRlc3RydWN0aXZlOlxuICAgICAgICAgIFwiYmctZGVzdHJ1Y3RpdmUgdGV4dC1kZXN0cnVjdGl2ZS1mb3JlZ3JvdW5kIHNoYWRvdy1zbSBob3ZlcjpiZy1kZXN0cnVjdGl2ZS85MFwiLFxuICAgICAgICBvdXRsaW5lOlxuICAgICAgICAgIFwiYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHNoYWRvdy1zbSBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBzZWNvbmRhcnk6XG4gICAgICAgICAgXCJiZy1zZWNvbmRhcnkgdGV4dC1zZWNvbmRhcnktZm9yZWdyb3VuZCBzaGFkb3ctc20gaG92ZXI6Ymctc2Vjb25kYXJ5LzgwXCIsXG4gICAgICAgIGdob3N0OiBcImhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgIGxpbms6IFwidGV4dC1wcmltYXJ5IHVuZGVybGluZS1vZmZzZXQtNCBob3Zlcjp1bmRlcmxpbmVcIixcbiAgICAgIH0sXG4gICAgICBzaXplOiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiaC05IHB4LTQgcHktMlwiLFxuICAgICAgICBzbTogXCJoLTggcm91bmRlZC1tZCBweC0zIHRleHQteHNcIixcbiAgICAgICAgbGc6IFwiaC0xMCByb3VuZGVkLW1kIHB4LThcIixcbiAgICAgICAgaWNvbjogXCJoLTkgdy05XCIsXG4gICAgICB9LFxuICAgIH0sXG4gICAgZGVmYXVsdFZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcbiAgICAgIHNpemU6IFwiZGVmYXVsdFwiLFxuICAgIH0sXG4gIH1cbilcblxuZXhwb3J0IGludGVyZmFjZSBCdXR0b25Qcm9wc1xuICBleHRlbmRzIFJlYWN0LkJ1dHRvbkhUTUxBdHRyaWJ1dGVzPEhUTUxCdXR0b25FbGVtZW50PixcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGJ1dHRvblZhcmlhbnRzPiB7XG4gIGFzQ2hpbGQ/OiBib29sZWFuXG59XG5cbmNvbnN0IEJ1dHRvbiA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTEJ1dHRvbkVsZW1lbnQsIEJ1dHRvblByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB2YXJpYW50LCBzaXplLCBhc0NoaWxkID0gZmFsc2UsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IFwiYnV0dG9uXCJcbiAgICByZXR1cm4gKFxuICAgICAgPENvbXBcbiAgICAgICAgY2xhc3NOYW1lPXtjbihidXR0b25WYXJpYW50cyh7IHZhcmlhbnQsIHNpemUsIGNsYXNzTmFtZSB9KSl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuQnV0dG9uLmRpc3BsYXlOYW1lID0gXCJCdXR0b25cIlxuXG5leHBvcnQgeyBCdXR0b24sIGJ1dHRvblZhcmlhbnRzIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNsb3QiLCJjdmEiLCJjbiIsImJ1dHRvblZhcmlhbnRzIiwidmFyaWFudHMiLCJ2YXJpYW50IiwiZGVmYXVsdCIsImRlc3RydWN0aXZlIiwib3V0bGluZSIsInNlY29uZGFyeSIsImdob3N0IiwibGluayIsInNpemUiLCJzbSIsImxnIiwiaWNvbiIsImRlZmF1bHRWYXJpYW50cyIsIkJ1dHRvbiIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJhc0NoaWxkIiwicHJvcHMiLCJyZWYiLCJDb21wIiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border bg-card text-card-foreground shadow\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/card.tsx\",\n        lineNumber: 48,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/card.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/card.tsx\",\n        lineNumber: 68,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/dialog.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/dialog.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dialog@1.1.2_@types+react-dom@19.0.2_@types+react@19.0.1__@types+react@_50f3c29ce13c99f237f6ea1b58ffe723/node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-icons@1.3.2_react@19.0.0/node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogTrigger,DialogClose,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/dialog.tsx\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined));\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.Cross2Icon, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/dialog.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/dialog.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/dialog.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/dialog.tsx\",\n                lineNumber: 38,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/dialog.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/dialog.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined);\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/dialog.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined);\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/dialog.tsx\",\n        lineNumber: 88,\n        columnNumber: 3\n    }, undefined));\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/dialog.tsx\",\n        lineNumber: 103,\n        columnNumber: 3\n    }, undefined));\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBSWhDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCw4VkFDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvc3JjL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IHR5cGUgSW5wdXRQcm9wcyA9IFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD5cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC05IHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctdHJhbnNwYXJlbnQgcHgtMyBweS0xIHRleHQtc20gc2hhZG93LXNtIHRyYW5zaXRpb24tY29sb3JzIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBmaWxlOnRleHQtZm9yZWdyb3VuZCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTEgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/select.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/select.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-icons@1.3.2_react@19.0.0/node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* harmony import */ var _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-select */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-select@2.1.2_@types+react-dom@19.0.2_@types+react@19.0.1__@types+react@_0e7a98c8b564ae73533dcad10fe56373/node_modules/@radix-ui/react-select/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Select,SelectGroup,SelectValue,SelectTrigger,SelectContent,SelectLabel,SelectItem,SelectSeparator,SelectScrollUpButton,SelectScrollDownButton auto */ \n\n\n\n\nconst Select = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst SelectGroup = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst SelectValue = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Value;\nconst SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.CaretSortIcon, {\n                    className: \"h-4 w-4 opacity-50\"\n                }, void 0, false, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n                lineNumber: 33,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nSelectTrigger.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.ChevronUpIcon, {}, void 0, false, {\n            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n            lineNumber: 52,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n        lineNumber: 44,\n        columnNumber: 3\n    }, undefined));\nSelectScrollUpButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton.displayName;\nconst SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.ChevronDownIcon, {}, void 0, false, {\n            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n            lineNumber: 69,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n        lineNumber: 61,\n        columnNumber: 3\n    }, undefined));\nSelectScrollDownButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton.displayName;\nconst SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, position = \"popper\", ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", position === \"popper\" && \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\", className),\n            position: position,\n            ...props,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollUpButton, {}, void 0, false, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-1\", position === \"popper\" && \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollDownButton, {}, void 0, false, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n            lineNumber: 80,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n        lineNumber: 79,\n        columnNumber: 3\n    }, undefined));\nSelectContent.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n        lineNumber: 111,\n        columnNumber: 3\n    }, undefined));\nSelectLabel.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.CheckIcon, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemText, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n                lineNumber: 136,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined));\nSelectItem.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/select.tsx\",\n        lineNumber: 145,\n        columnNumber: 3\n    }, undefined));\nSelectSeparator.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/select.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/textarea.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/textarea.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/components/ui/textarea.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS90ZXh0YXJlYS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBSWhDLE1BQU1FLHlCQUFXRiw2Q0FBZ0IsQ0FDL0IsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4QixxQkFDRSw4REFBQ0M7UUFDQ0gsV0FBV0gsOENBQUVBLENBQ1gsZ1FBQ0FHO1FBRUZFLEtBQUtBO1FBQ0osR0FBR0QsS0FBSzs7Ozs7O0FBR2Y7QUFFRkgsU0FBU00sV0FBVyxHQUFHO0FBRUoiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL3NyYy9jb21wb25lbnRzL3VpL3RleHRhcmVhLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCB0eXBlIFRleHRhcmVhUHJvcHMgPSBSZWFjdC5UZXh0YXJlYUhUTUxBdHRyaWJ1dGVzPEhUTUxUZXh0QXJlYUVsZW1lbnQ+XG5cbmNvbnN0IFRleHRhcmVhID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MVGV4dEFyZWFFbGVtZW50LCBUZXh0YXJlYVByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPHRleHRhcmVhXG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IG1pbi1oLVs2MHB4XSB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLXRyYW5zcGFyZW50IHB4LTMgcHktMiB0ZXh0LXNtIHNoYWRvdy1zbSBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTEgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5UZXh0YXJlYS5kaXNwbGF5TmFtZSA9IFwiVGV4dGFyZWFcIlxuXG5leHBvcnQgeyBUZXh0YXJlYSB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIlRleHRhcmVhIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwidGV4dGFyZWEiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/model-selector-provider.tsx":
/*!*********************************************!*\
  !*** ./src/lib/model-selector-provider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModelSelectorProvider: () => (/* binding */ ModelSelectorProvider),\n/* harmony export */   useModelSelectorContext: () => (/* binding */ useModelSelectorContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ModelSelectorProvider,useModelSelectorContext auto */ \n\n\nconst ModelSelectorContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst ModelSelectorProvider = ({ children })=>{\n    const model = globalThis.window === undefined ? \"openai\" : new URL(window.location.href).searchParams.get(\"coAgentsModel\") ?? \"openai\";\n    const [hidden, setHidden] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const setModel = (model)=>{\n        const url = new URL(window.location.href);\n        url.searchParams.set(\"coAgentsModel\", model);\n        window.location.href = url.toString();\n    };\n    const lgcDeploymentUrl = globalThis.window === undefined ? null : new URL(window.location.href).searchParams.get(\"lgcDeploymentUrl\");\n    let agent = \"research_agent\";\n    if (model === \"google_genai\") {\n        agent = \"research_agent_google_genai\";\n    } else if (model === \"crewai\") {\n        agent = \"research_agent_crewai\";\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModelSelectorContext.Provider, {\n        value: {\n            model,\n            agent,\n            lgcDeploymentUrl,\n            hidden,\n            setModel,\n            setHidden\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/lib/model-selector-provider.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\nconst useModelSelectorContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ModelSelectorContext);\n    if (context === undefined) {\n        throw new Error(\"useModelSelectorContext must be used within a ModelSelectorProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/model-selector-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   truncateUrl: () => (/* binding */ truncateUrl)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/.pnpm/tailwind-merge@2.5.3/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nconst truncateUrl = (url, maxLength = 40)=>{\n    if (!url) return \"\";\n    if (url.length <= maxLength) return url;\n    return url.substring(0, maxLength - 3) + \"...\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNkM7QUFDSjtBQUVsQyxTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEI7QUFFTyxNQUFNQyxjQUFjLENBQUNDLEtBQWFDLFlBQW9CLEVBQUU7SUFDN0QsSUFBSSxDQUFDRCxLQUFLLE9BQU87SUFDakIsSUFBSUEsSUFBSUUsTUFBTSxJQUFJRCxXQUFXLE9BQU9EO0lBQ3BDLE9BQU9BLElBQUlHLFNBQVMsQ0FBQyxHQUFHRixZQUFZLEtBQUs7QUFDM0MsRUFBRSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvc3JjL2xpYi91dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiO1xuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiO1xuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKTtcbn1cblxuZXhwb3J0IGNvbnN0IHRydW5jYXRlVXJsID0gKHVybDogc3RyaW5nLCBtYXhMZW5ndGg6IG51bWJlciA9IDQwKSA9PiB7XG4gIGlmICghdXJsKSByZXR1cm4gXCJcIjtcbiAgaWYgKHVybC5sZW5ndGggPD0gbWF4TGVuZ3RoKSByZXR1cm4gdXJsO1xuICByZXR1cm4gdXJsLnN1YnN0cmluZygwLCBtYXhMZW5ndGggLSAzKSArIFwiLi4uXCI7XG59O1xuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiLCJ0cnVuY2F0ZVVybCIsInVybCIsIm1heExlbmd0aCIsImxlbmd0aCIsInN1YnN0cmluZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c7fe948cde0c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYzdmZTk0OGNkZTBjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistVF.woff\",\"variable\":\"--font-geist-sans\",\"weight\":\"100 900\"}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/font/local/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/GeistVF.woff\\\",\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"weight\\\":\\\"100 900\\\"}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/GeistMonoVF.woff\",\"variable\":\"--font-geist-mono\",\"weight\":\"100 900\"}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/font/local/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/GeistMonoVF.woff\\\",\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"weight\\\":\\\"100 900\\\"}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _copilotkit_react_ui_styles_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @copilotkit/react-ui/styles.css */ \"(rsc)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/index.css\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistVF_woff_variable_font_geist_sans_weight_100_900_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_fonts_GeistMonoVF_woff_variable_font_geist_mono_weight_100_900_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFLTUE7QUFLQUM7QUFSbUM7QUFDbEI7QUFhaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFDQ0MsV0FBVyxHQUFHViw0TUFBa0IsQ0FBQyxDQUFDLEVBQUVDLGdOQUFrQixDQUFDLFlBQVksQ0FBQztzQkFFbkVLOzs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9zcmMvYXBwL2xheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgbG9jYWxGb250IGZyb20gXCJuZXh0L2ZvbnQvbG9jYWxcIjtcbmltcG9ydCBcIkBjb3BpbG90a2l0L3JlYWN0LXVpL3N0eWxlcy5jc3NcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuY29uc3QgZ2Vpc3RTYW5zID0gbG9jYWxGb250KHtcbiAgc3JjOiBcIi4vZm9udHMvR2Vpc3RWRi53b2ZmXCIsXG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1zYW5zXCIsXG4gIHdlaWdodDogXCIxMDAgOTAwXCIsXG59KTtcbmNvbnN0IGdlaXN0TW9ubyA9IGxvY2FsRm9udCh7XG4gIHNyYzogXCIuL2ZvbnRzL0dlaXN0TW9ub1ZGLndvZmZcIixcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LW1vbm9cIixcbiAgd2VpZ2h0OiBcIjEwMCA5MDBcIixcbn0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJDcmVhdGUgTmV4dCBBcHBcIixcbiAgZGVzY3JpcHRpb246IFwiR2VuZXJhdGVkIGJ5IGNyZWF0ZSBuZXh0IGFwcFwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5XG4gICAgICAgIGNsYXNzTmFtZT17YCR7Z2Vpc3RTYW5zLnZhcmlhYmxlfSAke2dlaXN0TW9uby52YXJpYWJsZX0gYW50aWFsaWFzZWRgfVxuICAgICAgPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImdlaXN0U2FucyIsImdlaXN0TW9ubyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/merge/project/CopilotKit/examples/coagents-research-canvas/ui/src/app/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4xLjBfQHBsYXl3cmlnaHQrdGVzdEAxLjUwLjFfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL3NyYy9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0","vendor-chunks/graphql@16.9.0","vendor-chunks/zod@3.23.8","vendor-chunks/@copilotkit+shared@1.9.2-next.7","vendor-chunks/debug@4.3.7","vendor-chunks/uuid@10.0.0","vendor-chunks/untruncate-json@0.0.1","vendor-chunks/extend@3.0.2","vendor-chunks/ms@2.1.3","vendor-chunks/supports-color@7.2.0","vendor-chunks/has-flag@4.0.0","vendor-chunks/refractor@3.6.0","vendor-chunks/mdast-util-to-markdown@2.1.2","vendor-chunks/mdast-util-to-hast@12.3.0","vendor-chunks/mdast-util-to-hast@13.2.0","vendor-chunks/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0","vendor-chunks/micromark-core-commonmark@2.0.3","vendor-chunks/micromark-core-commonmark@1.1.0","vendor-chunks/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0","vendor-chunks/property-information@7.1.0","vendor-chunks/property-information@6.5.0","vendor-chunks/property-information@5.6.0","vendor-chunks/parse5@7.3.0","vendor-chunks/@babel+runtime@7.25.7","vendor-chunks/micromark@4.0.2","vendor-chunks/micromark@3.2.0","vendor-chunks/lucide-react@0.451.0_react@19.0.0","vendor-chunks/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0","vendor-chunks/react-remove-scroll@2.6.0_@types+react@19.0.1_react@19.0.0","vendor-chunks/react-syntax-highlighter@15.6.1_react@19.0.0","vendor-chunks/entities@6.0.0","vendor-chunks/prop-types@15.8.1","vendor-chunks/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0","vendor-chunks/micromark-util-symbol@2.0.1","vendor-chunks/micromark-util-symbol@1.1.0","vendor-chunks/micromark-extension-gfm-table@2.1.1","vendor-chunks/@ungap+structured-clone@1.3.0","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/react-style-singleton@2.2.1_@types+react@19.0.1_react@19.0.0","vendor-chunks/react-remove-scroll-bar@2.3.6_@types+react@19.0.1_react@19.0.0","vendor-chunks/micromark-extension-math@3.1.0","vendor-chunks/hastscript@9.0.1","vendor-chunks/use-callback-ref@1.3.2_@types+react@19.0.1_react@19.0.0","vendor-chunks/hastscript@6.0.0","vendor-chunks/vfile@6.0.3","vendor-chunks/vfile@5.3.7","vendor-chunks/uvu@0.5.6","vendor-chunks/unist-util-visit-parents@6.0.1","vendor-chunks/unist-util-visit-parents@5.1.3","vendor-chunks/unified@11.0.5","vendor-chunks/micromark-util-subtokenize@2.1.0","vendor-chunks/micromark-util-character@1.2.0","vendor-chunks/micromark-extension-gfm-task-list-item@2.1.0","vendor-chunks/micromark-extension-gfm-strikethrough@2.1.0","vendor-chunks/micromark-extension-gfm-footnote@2.1.0","vendor-chunks/micromark-extension-gfm-autolink-literal@2.1.0","vendor-chunks/@urql+core@5.0.6_graphql@16.9.0","vendor-chunks/@floating-ui+utils@0.2.8","vendor-chunks/use-sidecar@1.1.2_@types+react@19.0.1_react@19.0.0","vendor-chunks/style-to-object@0.4.4","vendor-chunks/style-to-js@1.1.16","vendor-chunks/react-is@18.3.1","vendor-chunks/react-is@16.13.1","vendor-chunks/parse-entities@2.0.0","vendor-chunks/character-reference-invalid@1.1.4","vendor-chunks/character-entities@1.2.4","vendor-chunks/character-entities-legacy@1.1.4","vendor-chunks/zwitch@2.0.4","vendor-chunks/wonka@6.3.4","vendor-chunks/web-namespaces@2.0.1","vendor-chunks/vfile-message@4.0.2","vendor-chunks/vfile-message@3.1.4","vendor-chunks/vfile-location@5.0.3","vendor-chunks/unist-util-visit@5.0.0","vendor-chunks/unist-util-visit@4.1.2","vendor-chunks/unist-util-stringify-position@4.0.0","vendor-chunks/unist-util-stringify-position@3.0.3","vendor-chunks/unist-util-position@5.0.0","vendor-chunks/unist-util-position@4.0.4","vendor-chunks/unist-util-is@6.0.0","vendor-chunks/unist-util-is@5.2.1","vendor-chunks/unist-util-generated@2.0.1","vendor-chunks/unified@10.1.2","vendor-chunks/tslib@2.7.0","vendor-chunks/trough@2.2.0","vendor-chunks/trim-lines@3.0.1","vendor-chunks/tailwind-merge@2.5.3","vendor-chunks/space-separated-tokens@2.0.2","vendor-chunks/remark-rehype@11.1.2","vendor-chunks/remark-rehype@10.1.0","vendor-chunks/remark-parse@11.0.0","vendor-chunks/remark-parse@10.0.2","vendor-chunks/remark-math@6.0.0","vendor-chunks/remark-gfm@4.0.1","vendor-chunks/rehype-raw@7.0.0","vendor-chunks/react-markdown@10.1.0_@types+react@19.0.1_react@19.0.0","vendor-chunks/micromark-util-subtokenize@1.1.0","vendor-chunks/micromark-util-sanitize-uri@2.0.1","vendor-chunks/micromark-util-sanitize-uri@1.2.0","vendor-chunks/micromark-util-resolve-all@2.0.1","vendor-chunks/micromark-util-resolve-all@1.1.0","vendor-chunks/micromark-util-normalize-identifier@2.0.1","vendor-chunks/micromark-util-normalize-identifier@1.1.0","vendor-chunks/micromark-util-html-tag-name@2.0.1","vendor-chunks/micromark-util-html-tag-name@1.2.0","vendor-chunks/micromark-util-encode@2.0.1","vendor-chunks/micromark-util-encode@1.1.0","vendor-chunks/micromark-util-decode-string@2.0.1","vendor-chunks/micromark-util-decode-string@1.1.0","vendor-chunks/micromark-util-decode-numeric-character-reference@2.0.2","vendor-chunks/micromark-util-decode-numeric-character-reference@1.1.0","vendor-chunks/micromark-util-combine-extensions@2.0.1","vendor-chunks/micromark-util-combine-extensions@1.1.0","vendor-chunks/micromark-util-classify-character@2.0.1","vendor-chunks/micromark-util-classify-character@1.1.0","vendor-chunks/micromark-util-chunked@2.0.1","vendor-chunks/micromark-util-chunked@1.1.0","vendor-chunks/micromark-util-character@2.1.1","vendor-chunks/micromark-factory-whitespace@2.0.1","vendor-chunks/micromark-factory-whitespace@1.1.0","vendor-chunks/micromark-factory-title@2.0.1","vendor-chunks/micromark-factory-title@1.1.0","vendor-chunks/micromark-factory-space@2.0.1","vendor-chunks/micromark-factory-space@1.1.0","vendor-chunks/micromark-factory-label@2.0.1","vendor-chunks/micromark-factory-label@1.1.0","vendor-chunks/micromark-factory-destination@2.0.1","vendor-chunks/micromark-factory-destination@1.1.0","vendor-chunks/micromark-extension-gfm@3.0.0","vendor-chunks/micromark-extension-gfm-tagfilter@2.0.0","vendor-chunks/mdast-util-to-string@4.0.0","vendor-chunks/mdast-util-to-string@3.2.0","vendor-chunks/mdast-util-phrasing@4.1.0","vendor-chunks/mdast-util-math@3.0.0","vendor-chunks/mdast-util-gfm@3.1.0","vendor-chunks/mdast-util-gfm-task-list-item@2.0.0","vendor-chunks/mdast-util-gfm-table@2.0.0","vendor-chunks/mdast-util-gfm-strikethrough@2.0.0","vendor-chunks/mdast-util-gfm-footnote@2.1.0","vendor-chunks/mdast-util-gfm-autolink-literal@2.0.1","vendor-chunks/mdast-util-from-markdown@2.0.2","vendor-chunks/mdast-util-from-markdown@1.3.1","vendor-chunks/mdast-util-find-and-replace@3.0.2","vendor-chunks/mdast-util-definitions@5.1.2","vendor-chunks/markdown-table@3.0.3","vendor-chunks/longest-streak@3.1.0","vendor-chunks/kleur@4.1.5","vendor-chunks/is-plain-obj@4.1.0","vendor-chunks/html-void-elements@3.0.0","vendor-chunks/html-url-attributes@3.0.1","vendor-chunks/hast-util-whitespace@3.0.0","vendor-chunks/hast-util-whitespace@2.0.1","vendor-chunks/hast-util-to-parse5@8.0.0","vendor-chunks/hast-util-to-jsx-runtime@2.3.6","vendor-chunks/hast-util-raw@9.1.0","vendor-chunks/hast-util-parse-selector@4.0.0","vendor-chunks/hast-util-from-parse5@8.0.3","vendor-chunks/estree-util-is-identifier-name@3.0.0","vendor-chunks/escape-string-regexp@5.0.0","vendor-chunks/diff@5.2.0","vendor-chunks/devlop@1.1.0","vendor-chunks/dequal@2.0.3","vendor-chunks/decode-named-character-reference@1.0.2","vendor-chunks/comma-separated-tokens@2.0.3","vendor-chunks/clsx@2.1.1","vendor-chunks/clsx@2.0.0","vendor-chunks/class-variance-authority@0.7.0","vendor-chunks/character-entities@2.0.2","vendor-chunks/ccount@2.0.1","vendor-chunks/bail@2.0.2","vendor-chunks/@radix-ui+react-visually-hidden@1.1.0_@types+react-dom@19.0.2_@types+react@19.0.1__@typ_509d81ea7e7dff83a84b9c8c00730592","vendor-chunks/@radix-ui+react-use-size@1.1.0_@types+react@19.0.1_react@19.0.0","vendor-chunks/@radix-ui+react-use-previous@1.1.0_@types+react@19.0.1_react@19.0.0","vendor-chunks/@radix-ui+react-use-layout-effect@1.1.0_@types+react@19.0.1_react@19.0.0","vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.0_@types+react@19.0.1_react@19.0.0","vendor-chunks/@radix-ui+react-use-controllable-state@1.1.0_@types+react@19.0.1_react@19.0.0","vendor-chunks/@radix-ui+react-use-callback-ref@1.1.0_@types+react@19.0.1_react@19.0.0","vendor-chunks/@radix-ui+react-slot@1.1.0_@types+react@19.0.1_react@19.0.0","vendor-chunks/@radix-ui+react-select@2.1.2_@types+react-dom@19.0.2_@types+react@19.0.1__@types+react@_0e7a98c8b564ae73533dcad10fe56373","vendor-chunks/@radix-ui+react-primitive@2.0.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+rea_c98e92916030b59f0b851119b2fb60f3","vendor-chunks/@radix-ui+react-presence@1.1.1_@types+react-dom@19.0.2_@types+react@19.0.1__@types+reac_694e2d743774bec0bd37458d4c1c8087","vendor-chunks/@radix-ui+react-portal@1.1.2_@types+react-dom@19.0.2_@types+react@19.0.1__@types+react@_ae565f7eca12aa2abd0ded79c280f26d","vendor-chunks/@radix-ui+react-popper@1.2.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+react@_0aa3ba0c619aac13136194a66f93d5be","vendor-chunks/@radix-ui+react-id@1.1.0_@types+react@19.0.1_react@19.0.0","vendor-chunks/@radix-ui+react-focus-scope@1.1.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+r_2b75ad9f89a157aafc3bc27cf01c05b6","vendor-chunks/@radix-ui+react-focus-guards@1.1.1_@types+react@19.0.1_react@19.0.0","vendor-chunks/@radix-ui+react-dismissable-layer@1.1.1_@types+react-dom@19.0.2_@types+react@19.0.1__@t_251748a55be6fbeeb714d5b52687e06c","vendor-chunks/@radix-ui+react-direction@1.1.0_@types+react@19.0.1_react@19.0.0","vendor-chunks/@radix-ui+react-dialog@1.1.2_@types+react-dom@19.0.2_@types+react@19.0.1__@types+react@_50f3c29ce13c99f237f6ea1b58ffe723","vendor-chunks/@radix-ui+react-context@1.1.1_@types+react@19.0.1_react@19.0.0","vendor-chunks/@radix-ui+react-context@1.1.0_@types+react@19.0.1_react@19.0.0","vendor-chunks/@radix-ui+react-compose-refs@1.1.0_@types+react@19.0.1_react@19.0.0","vendor-chunks/@radix-ui+react-collection@1.1.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+re_a4a23facf0844a30206f8161011fe7b3","vendor-chunks/@radix-ui+react-arrow@1.1.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+react@1_9e129052b105b393ae22c70411318c7c","vendor-chunks/@radix-ui+primitive@1.1.0","vendor-chunks/@radix-ui+number@1.1.0","vendor-chunks/@floating-ui+react-dom@2.1.2_react-dom@19.0.0_react@19.0.0__react@19.0.0","vendor-chunks/@floating-ui+dom@1.6.11","vendor-chunks/@floating-ui+core@1.6.8","vendor-chunks/@0no-co+graphql.web@1.0.8_graphql@16.9.0","vendor-chunks/xtend@4.0.2","vendor-chunks/style-to-object@1.0.8","vendor-chunks/space-separated-tokens@1.1.5","vendor-chunks/prismjs@1.27.0","vendor-chunks/object-assign@4.1.1","vendor-chunks/is-hexadecimal@1.0.4","vendor-chunks/is-decimal@1.0.4","vendor-chunks/is-buffer@2.0.5","vendor-chunks/is-alphanumerical@1.0.4","vendor-chunks/is-alphabetical@1.0.4","vendor-chunks/inline-style-parser@0.2.4","vendor-chunks/inline-style-parser@0.1.1","vendor-chunks/hast-util-parse-selector@2.2.5","vendor-chunks/get-nonce@1.0.1","vendor-chunks/comma-separated-tokens@1.0.8","vendor-chunks/aria-hidden@1.2.4","vendor-chunks/@radix-ui+react-icons@1.3.2_react@19.0.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmerge%2Fproject%2FCopilotKit%2Fexamples%2Fcoagents-research-canvas%2Fui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();