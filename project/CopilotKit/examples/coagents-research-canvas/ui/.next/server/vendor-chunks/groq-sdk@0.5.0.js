/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/groq-sdk@0.5.0";
exports.ids = ["vendor-chunks/groq-sdk@0.5.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/MultipartBody.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/MultipartBody.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MultipartBody = void 0;\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nclass MultipartBody {\n    constructor(body) {\n        this.body = body;\n    }\n    get [Symbol.toStringTag]() {\n        return 'MultipartBody';\n    }\n}\nexports.MultipartBody = MultipartBody;\n//# sourceMappingURL=MultipartBody.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL19zaGltcy9NdWx0aXBhcnRCb2R5LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL19zaGltcy9NdWx0aXBhcnRCb2R5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5NdWx0aXBhcnRCb2R5ID0gdm9pZCAwO1xuLyoqXG4gKiBEaXNjbGFpbWVyOiBtb2R1bGVzIGluIF9zaGltcyBhcmVuJ3QgaW50ZW5kZWQgdG8gYmUgaW1wb3J0ZWQgYnkgU0RLIHVzZXJzLlxuICovXG5jbGFzcyBNdWx0aXBhcnRCb2R5IHtcbiAgICBjb25zdHJ1Y3Rvcihib2R5KSB7XG4gICAgICAgIHRoaXMuYm9keSA9IGJvZHk7XG4gICAgfVxuICAgIGdldCBbU3ltYm9sLnRvU3RyaW5nVGFnXSgpIHtcbiAgICAgICAgcmV0dXJuICdNdWx0aXBhcnRCb2R5JztcbiAgICB9XG59XG5leHBvcnRzLk11bHRpcGFydEJvZHkgPSBNdWx0aXBhcnRCb2R5O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9TXVsdGlwYXJ0Qm9keS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/MultipartBody.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/auto/runtime-node.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/auto/runtime-node.js ***!
  \*********************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\n__exportStar(__webpack_require__(/*! ../node-runtime.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/node-runtime.js\"), exports);\n//# sourceMappingURL=runtime-node.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL19zaGltcy9hdXRvL3J1bnRpbWUtbm9kZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxvQ0FBb0M7QUFDbkQ7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RDtBQUNBO0FBQ0E7QUFDQSxhQUFhLG1CQUFPLENBQUMsa0hBQW9CO0FBQ3pDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL19zaGltcy9hdXRvL3J1bnRpbWUtbm9kZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIHZhciBkZXNjID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihtLCBrKTtcbiAgICBpZiAoIWRlc2MgfHwgKFwiZ2V0XCIgaW4gZGVzYyA/ICFtLl9fZXNNb2R1bGUgOiBkZXNjLndyaXRhYmxlIHx8IGRlc2MuY29uZmlndXJhYmxlKSkge1xuICAgICAgZGVzYyA9IHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfTtcbiAgICB9XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIGsyLCBkZXNjKTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19leHBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2V4cG9ydFN0YXIpIHx8IGZ1bmN0aW9uKG0sIGV4cG9ydHMpIHtcbiAgICBmb3IgKHZhciBwIGluIG0pIGlmIChwICE9PSBcImRlZmF1bHRcIiAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIHApKSBfX2NyZWF0ZUJpbmRpbmcoZXhwb3J0cywgbSwgcCk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLyoqXG4gKiBEaXNjbGFpbWVyOiBtb2R1bGVzIGluIF9zaGltcyBhcmVuJ3QgaW50ZW5kZWQgdG8gYmUgaW1wb3J0ZWQgYnkgU0RLIHVzZXJzLlxuICovXG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4uL25vZGUtcnVudGltZS5qc1wiKSwgZXhwb3J0cyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ydW50aW1lLW5vZGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/auto/runtime-node.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nconst shims = __webpack_require__(/*! ./registry */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/registry.js\");\nconst auto = __webpack_require__(/*! groq-sdk/_shims/auto/runtime */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/auto/runtime-node.js\");\nif (!shims.kind) shims.setShims(auto.getRuntime(), { auto: true });\nfor (const property of Object.keys(shims)) {\n  Object.defineProperty(exports, property, {\n    get() {\n      return shims[property];\n    },\n  });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL19zaGltcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLG1CQUFPLENBQUMsc0dBQVk7QUFDbEMsYUFBYSxtQkFBTyxDQUFDLGlJQUE4QjtBQUNuRCxxREFBcUQsWUFBWTtBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9ncm9xLXNka0AwLjUuMC9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvX3NoaW1zL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRGlzY2xhaW1lcjogbW9kdWxlcyBpbiBfc2hpbXMgYXJlbid0IGludGVuZGVkIHRvIGJlIGltcG9ydGVkIGJ5IFNESyB1c2Vycy5cbiAqL1xuY29uc3Qgc2hpbXMgPSByZXF1aXJlKCcuL3JlZ2lzdHJ5Jyk7XG5jb25zdCBhdXRvID0gcmVxdWlyZSgnZ3JvcS1zZGsvX3NoaW1zL2F1dG8vcnVudGltZScpO1xuaWYgKCFzaGltcy5raW5kKSBzaGltcy5zZXRTaGltcyhhdXRvLmdldFJ1bnRpbWUoKSwgeyBhdXRvOiB0cnVlIH0pO1xuZm9yIChjb25zdCBwcm9wZXJ0eSBvZiBPYmplY3Qua2V5cyhzaGltcykpIHtcbiAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIHByb3BlcnR5LCB7XG4gICAgZ2V0KCkge1xuICAgICAgcmV0dXJuIHNoaW1zW3Byb3BlcnR5XTtcbiAgICB9LFxuICB9KTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/node-runtime.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/node-runtime.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getRuntime = void 0;\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nconst nf = __importStar(__webpack_require__(/*! node-fetch */ \"(rsc)/./node_modules/.pnpm/node-fetch@2.7.0/node_modules/node-fetch/lib/index.mjs\"));\nconst fd = __importStar(__webpack_require__(/*! formdata-node */ \"(rsc)/./node_modules/.pnpm/formdata-node@4.4.1/node_modules/formdata-node/lib/cjs/index.js\"));\nconst agentkeepalive_1 = __importDefault(__webpack_require__(/*! agentkeepalive */ \"(rsc)/./node_modules/.pnpm/agentkeepalive@4.5.0/node_modules/agentkeepalive/index.js\"));\nconst abort_controller_1 = __webpack_require__(/*! abort-controller */ \"(rsc)/./node_modules/.pnpm/abort-controller@3.0.0/node_modules/abort-controller/dist/abort-controller.js\");\nconst node_fs_1 = __webpack_require__(/*! node:fs */ \"node:fs\");\nconst form_data_encoder_1 = __webpack_require__(/*! form-data-encoder */ \"(rsc)/./node_modules/.pnpm/form-data-encoder@1.7.2/node_modules/form-data-encoder/lib/cjs/index.js\");\nconst node_stream_1 = __webpack_require__(/*! node:stream */ \"node:stream\");\nconst MultipartBody_1 = __webpack_require__(/*! ./MultipartBody.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/MultipartBody.js\");\n// @ts-ignore (this package does not have proper export maps for this export)\nconst ponyfill_es2018_js_1 = __webpack_require__(/*! web-streams-polyfill/dist/ponyfill.es2018.js */ \"(rsc)/./node_modules/.pnpm/web-streams-polyfill@3.3.3/node_modules/web-streams-polyfill/dist/ponyfill.es2018.js\");\nlet fileFromPathWarned = false;\nasync function fileFromPath(path, ...args) {\n    // this import fails in environments that don't handle export maps correctly, like old versions of Jest\n    const { fileFromPath: _fileFromPath } = await Promise.resolve().then(() => __importStar(__webpack_require__(/*! formdata-node/file-from-path */ \"(rsc)/./node_modules/.pnpm/formdata-node@4.4.1/node_modules/formdata-node/lib/cjs/fileFromPath.js\")));\n    if (!fileFromPathWarned) {\n        console.warn(`fileFromPath is deprecated; use fs.createReadStream(${JSON.stringify(path)}) instead`);\n        fileFromPathWarned = true;\n    }\n    // @ts-ignore\n    return await _fileFromPath(path, ...args);\n}\nconst defaultHttpAgent = new agentkeepalive_1.default({ keepAlive: true, timeout: 5 * 60 * 1000 });\nconst defaultHttpsAgent = new agentkeepalive_1.default.HttpsAgent({ keepAlive: true, timeout: 5 * 60 * 1000 });\nasync function getMultipartRequestOptions(form, opts) {\n    const encoder = new form_data_encoder_1.FormDataEncoder(form);\n    const readable = node_stream_1.Readable.from(encoder);\n    const body = new MultipartBody_1.MultipartBody(readable);\n    const headers = {\n        ...opts.headers,\n        ...encoder.headers,\n        'Content-Length': encoder.contentLength,\n    };\n    return { ...opts, body: body, headers };\n}\nfunction getRuntime() {\n    // Polyfill global object if needed.\n    if (typeof AbortController === 'undefined') {\n        // @ts-expect-error (the types are subtly different, but compatible in practice)\n        globalThis.AbortController = abort_controller_1.AbortController;\n    }\n    return {\n        kind: 'node',\n        fetch: nf.default,\n        Request: nf.Request,\n        Response: nf.Response,\n        Headers: nf.Headers,\n        FormData: fd.FormData,\n        Blob: fd.Blob,\n        File: fd.File,\n        ReadableStream: ponyfill_es2018_js_1.ReadableStream,\n        getMultipartRequestOptions,\n        getDefaultAgent: (url) => (url.startsWith('https') ? defaultHttpsAgent : defaultHttpAgent),\n        fileFromPath,\n        isFsReadStream: (value) => value instanceof node_fs_1.ReadStream,\n    };\n}\nexports.getRuntime = getRuntime;\n//# sourceMappingURL=node-runtime.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/node-runtime.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/registry.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/registry.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.setShims = exports.isFsReadStream = exports.fileFromPath = exports.getDefaultAgent = exports.getMultipartRequestOptions = exports.ReadableStream = exports.File = exports.Blob = exports.FormData = exports.Headers = exports.Response = exports.Request = exports.fetch = exports.kind = exports.auto = void 0;\nexports.auto = false;\nexports.kind = undefined;\nexports.fetch = undefined;\nexports.Request = undefined;\nexports.Response = undefined;\nexports.Headers = undefined;\nexports.FormData = undefined;\nexports.Blob = undefined;\nexports.File = undefined;\nexports.ReadableStream = undefined;\nexports.getMultipartRequestOptions = undefined;\nexports.getDefaultAgent = undefined;\nexports.fileFromPath = undefined;\nexports.isFsReadStream = undefined;\nfunction setShims(shims, options = { auto: false }) {\n    if (exports.auto) {\n        throw new Error(`you must \\`import 'groq-sdk/shims/${shims.kind}'\\` before importing anything else from groq-sdk`);\n    }\n    if (exports.kind) {\n        throw new Error(`can't \\`import 'groq-sdk/shims/${shims.kind}'\\` after \\`import 'groq-sdk/shims/${exports.kind}'\\``);\n    }\n    exports.auto = options.auto;\n    exports.kind = shims.kind;\n    exports.fetch = shims.fetch;\n    exports.Request = shims.Request;\n    exports.Response = shims.Response;\n    exports.Headers = shims.Headers;\n    exports.FormData = shims.FormData;\n    exports.Blob = shims.Blob;\n    exports.File = shims.File;\n    exports.ReadableStream = shims.ReadableStream;\n    exports.getMultipartRequestOptions = shims.getMultipartRequestOptions;\n    exports.getDefaultAgent = shims.getDefaultAgent;\n    exports.fileFromPath = shims.fileFromPath;\n    exports.isFsReadStream = shims.isFsReadStream;\n}\nexports.setShims = setShims;\n//# sourceMappingURL=registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/registry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/core.js":
/*!*************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/core.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _AbstractPage_client;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isObj = exports.toBase64 = exports.getRequiredHeader = exports.isHeadersProtocol = exports.isRunningInBrowser = exports.debug = exports.hasOwn = exports.isEmptyObj = exports.maybeCoerceBoolean = exports.maybeCoerceFloat = exports.maybeCoerceInteger = exports.coerceBoolean = exports.coerceFloat = exports.coerceInteger = exports.readEnv = exports.ensurePresent = exports.castToError = exports.sleep = exports.safeJSON = exports.isRequestOptions = exports.createResponseHeaders = exports.PagePromise = exports.AbstractPage = exports.APIClient = exports.APIPromise = exports.createForm = exports.multipartFormRequestOptions = exports.maybeMultipartFormRequestOptions = void 0;\nconst version_1 = __webpack_require__(/*! ./version.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/version.js\");\nconst streaming_1 = __webpack_require__(/*! ./lib/streaming.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/lib/streaming.js\");\nconst error_1 = __webpack_require__(/*! ./error.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/error.js\");\nconst index_1 = __webpack_require__(/*! ./_shims/index.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/index.js\");\nconst uploads_1 = __webpack_require__(/*! ./uploads.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/uploads.js\");\nvar uploads_2 = __webpack_require__(/*! ./uploads.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/uploads.js\");\nObject.defineProperty(exports, \"maybeMultipartFormRequestOptions\", ({ enumerable: true, get: function () { return uploads_2.maybeMultipartFormRequestOptions; } }));\nObject.defineProperty(exports, \"multipartFormRequestOptions\", ({ enumerable: true, get: function () { return uploads_2.multipartFormRequestOptions; } }));\nObject.defineProperty(exports, \"createForm\", ({ enumerable: true, get: function () { return uploads_2.createForm; } }));\nasync function defaultParseResponse(props) {\n    const { response } = props;\n    if (props.options.stream) {\n        debug('response', response.status, response.url, response.headers, response.body);\n        // Note: there is an invariant here that isn't represented in the type system\n        // that if you set `stream: true` the response type must also be `Stream<T>`\n        if (props.options.__streamClass) {\n            return props.options.__streamClass.fromSSEResponse(response, props.controller);\n        }\n        return streaming_1.Stream.fromSSEResponse(response, props.controller);\n    }\n    // fetch refuses to read the body when the status code is 204.\n    if (response.status === 204) {\n        return null;\n    }\n    if (props.options.__binaryResponse) {\n        return response;\n    }\n    const contentType = response.headers.get('content-type');\n    const isJSON = contentType?.includes('application/json') || contentType?.includes('application/vnd.api+json');\n    if (isJSON) {\n        const json = await response.json();\n        debug('response', response.status, response.url, response.headers, json);\n        return json;\n    }\n    const text = await response.text();\n    debug('response', response.status, response.url, response.headers, text);\n    // TODO handle blob, arraybuffer, other content types, etc.\n    return text;\n}\n/**\n * A subclass of `Promise` providing additional helper methods\n * for interacting with the SDK.\n */\nclass APIPromise extends Promise {\n    constructor(responsePromise, parseResponse = defaultParseResponse) {\n        super((resolve) => {\n            // this is maybe a bit weird but this has to be a no-op to not implicitly\n            // parse the response body; instead .then, .catch, .finally are overridden\n            // to parse the response\n            resolve(null);\n        });\n        this.responsePromise = responsePromise;\n        this.parseResponse = parseResponse;\n    }\n    _thenUnwrap(transform) {\n        return new APIPromise(this.responsePromise, async (props) => transform(await this.parseResponse(props)));\n    }\n    /**\n     * Gets the raw `Response` instance instead of parsing the response\n     * data.\n     *\n     * If you want to parse the response body but still get the `Response`\n     * instance, you can use {@link withResponse()}.\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from 'groq-sdk'`:\n     * - `import 'groq-sdk/shims/node'` (if you're running on Node)\n     * - `import 'groq-sdk/shims/web'` (otherwise)\n     */\n    asResponse() {\n        return this.responsePromise.then((p) => p.response);\n    }\n    /**\n     * Gets the parsed response data and the raw `Response` instance.\n     *\n     * If you just want to get the raw `Response` instance without parsing it,\n     * you can use {@link asResponse()}.\n     *\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from 'groq-sdk'`:\n     * - `import 'groq-sdk/shims/node'` (if you're running on Node)\n     * - `import 'groq-sdk/shims/web'` (otherwise)\n     */\n    async withResponse() {\n        const [data, response] = await Promise.all([this.parse(), this.asResponse()]);\n        return { data, response };\n    }\n    parse() {\n        if (!this.parsedPromise) {\n            this.parsedPromise = this.responsePromise.then(this.parseResponse);\n        }\n        return this.parsedPromise;\n    }\n    then(onfulfilled, onrejected) {\n        return this.parse().then(onfulfilled, onrejected);\n    }\n    catch(onrejected) {\n        return this.parse().catch(onrejected);\n    }\n    finally(onfinally) {\n        return this.parse().finally(onfinally);\n    }\n}\nexports.APIPromise = APIPromise;\nclass APIClient {\n    constructor({ baseURL, maxRetries = 2, timeout = 60000, // 1 minute\n    httpAgent, fetch: overridenFetch, }) {\n        this.baseURL = baseURL;\n        this.maxRetries = validatePositiveInteger('maxRetries', maxRetries);\n        this.timeout = validatePositiveInteger('timeout', timeout);\n        this.httpAgent = httpAgent;\n        this.fetch = overridenFetch ?? index_1.fetch;\n    }\n    authHeaders(opts) {\n        return {};\n    }\n    /**\n     * Override this to add your own default headers, for example:\n     *\n     *  {\n     *    ...super.defaultHeaders(),\n     *    Authorization: 'Bearer 123',\n     *  }\n     */\n    defaultHeaders(opts) {\n        return {\n            Accept: 'application/json',\n            'Content-Type': 'application/json',\n            'User-Agent': this.getUserAgent(),\n            ...getPlatformHeaders(),\n            ...this.authHeaders(opts),\n        };\n    }\n    /**\n     * Override this to add your own headers validation:\n     */\n    validateHeaders(headers, customHeaders) { }\n    defaultIdempotencyKey() {\n        return `stainless-node-retry-${uuid4()}`;\n    }\n    get(path, opts) {\n        return this.methodRequest('get', path, opts);\n    }\n    post(path, opts) {\n        return this.methodRequest('post', path, opts);\n    }\n    patch(path, opts) {\n        return this.methodRequest('patch', path, opts);\n    }\n    put(path, opts) {\n        return this.methodRequest('put', path, opts);\n    }\n    delete(path, opts) {\n        return this.methodRequest('delete', path, opts);\n    }\n    methodRequest(method, path, opts) {\n        return this.request(Promise.resolve(opts).then(async (opts) => {\n            const body = opts && (0, uploads_1.isBlobLike)(opts?.body) ? new DataView(await opts.body.arrayBuffer())\n                : opts?.body instanceof DataView ? opts.body\n                    : opts?.body instanceof ArrayBuffer ? new DataView(opts.body)\n                        : opts && ArrayBuffer.isView(opts?.body) ? new DataView(opts.body.buffer)\n                            : opts?.body;\n            return { method, path, ...opts, body };\n        }));\n    }\n    getAPIList(path, Page, opts) {\n        return this.requestAPIList(Page, { method: 'get', path, ...opts });\n    }\n    calculateContentLength(body) {\n        if (typeof body === 'string') {\n            if (typeof Buffer !== 'undefined') {\n                return Buffer.byteLength(body, 'utf8').toString();\n            }\n            if (typeof TextEncoder !== 'undefined') {\n                const encoder = new TextEncoder();\n                const encoded = encoder.encode(body);\n                return encoded.length.toString();\n            }\n        }\n        else if (ArrayBuffer.isView(body)) {\n            return body.byteLength.toString();\n        }\n        return null;\n    }\n    buildRequest(options) {\n        const { method, path, query, headers: headers = {} } = options;\n        const body = ArrayBuffer.isView(options.body) || (options.__binaryRequest && typeof options.body === 'string') ?\n            options.body\n            : (0, uploads_1.isMultipartBody)(options.body) ? options.body.body\n                : options.body ? JSON.stringify(options.body, null, 2)\n                    : null;\n        const contentLength = this.calculateContentLength(body);\n        const url = this.buildURL(path, query);\n        if ('timeout' in options)\n            validatePositiveInteger('timeout', options.timeout);\n        const timeout = options.timeout ?? this.timeout;\n        const httpAgent = options.httpAgent ?? this.httpAgent ?? (0, index_1.getDefaultAgent)(url);\n        const minAgentTimeout = timeout + 1000;\n        if (typeof httpAgent?.options?.timeout === 'number' &&\n            minAgentTimeout > (httpAgent.options.timeout ?? 0)) {\n            // Allow any given request to bump our agent active socket timeout.\n            // This may seem strange, but leaking active sockets should be rare and not particularly problematic,\n            // and without mutating agent we would need to create more of them.\n            // This tradeoff optimizes for performance.\n            httpAgent.options.timeout = minAgentTimeout;\n        }\n        if (this.idempotencyHeader && method !== 'get') {\n            if (!options.idempotencyKey)\n                options.idempotencyKey = this.defaultIdempotencyKey();\n            headers[this.idempotencyHeader] = options.idempotencyKey;\n        }\n        const reqHeaders = this.buildHeaders({ options, headers, contentLength });\n        const req = {\n            method,\n            ...(body && { body: body }),\n            headers: reqHeaders,\n            ...(httpAgent && { agent: httpAgent }),\n            // @ts-ignore node-fetch uses a custom AbortSignal type that is\n            // not compatible with standard web types\n            signal: options.signal ?? null,\n        };\n        return { req, url, timeout };\n    }\n    buildHeaders({ options, headers, contentLength, }) {\n        const reqHeaders = {};\n        if (contentLength) {\n            reqHeaders['content-length'] = contentLength;\n        }\n        const defaultHeaders = this.defaultHeaders(options);\n        applyHeadersMut(reqHeaders, defaultHeaders);\n        applyHeadersMut(reqHeaders, headers);\n        // let builtin fetch set the Content-Type for multipart bodies\n        if ((0, uploads_1.isMultipartBody)(options.body) && index_1.kind !== 'node') {\n            delete reqHeaders['content-type'];\n        }\n        this.validateHeaders(reqHeaders, headers);\n        return reqHeaders;\n    }\n    /**\n     * Used as a callback for mutating the given `FinalRequestOptions` object.\n     */\n    async prepareOptions(options) { }\n    /**\n     * Used as a callback for mutating the given `RequestInit` object.\n     *\n     * This is useful for cases where you want to add certain headers based off of\n     * the request properties, e.g. `method` or `url`.\n     */\n    async prepareRequest(request, { url, options }) { }\n    parseHeaders(headers) {\n        return (!headers ? {}\n            : Symbol.iterator in headers ?\n                Object.fromEntries(Array.from(headers).map((header) => [...header]))\n                : { ...headers });\n    }\n    makeStatusError(status, error, message, headers) {\n        return error_1.APIError.generate(status, error, message, headers);\n    }\n    request(options, remainingRetries = null) {\n        return new APIPromise(this.makeRequest(options, remainingRetries));\n    }\n    async makeRequest(optionsInput, retriesRemaining) {\n        const options = await optionsInput;\n        if (retriesRemaining == null) {\n            retriesRemaining = options.maxRetries ?? this.maxRetries;\n        }\n        await this.prepareOptions(options);\n        const { req, url, timeout } = this.buildRequest(options);\n        await this.prepareRequest(req, { url, options });\n        debug('request', url, options, req.headers);\n        if (options.signal?.aborted) {\n            throw new error_1.APIUserAbortError();\n        }\n        const controller = new AbortController();\n        const response = await this.fetchWithTimeout(url, req, timeout, controller).catch(exports.castToError);\n        if (response instanceof Error) {\n            if (options.signal?.aborted) {\n                throw new error_1.APIUserAbortError();\n            }\n            if (retriesRemaining) {\n                return this.retryRequest(options, retriesRemaining);\n            }\n            if (response.name === 'AbortError') {\n                throw new error_1.APIConnectionTimeoutError();\n            }\n            throw new error_1.APIConnectionError({ cause: response });\n        }\n        const responseHeaders = (0, exports.createResponseHeaders)(response.headers);\n        if (!response.ok) {\n            if (retriesRemaining && this.shouldRetry(response)) {\n                const retryMessage = `retrying, ${retriesRemaining} attempts remaining`;\n                debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders);\n                return this.retryRequest(options, retriesRemaining, responseHeaders);\n            }\n            const errText = await response.text().catch((e) => (0, exports.castToError)(e).message);\n            const errJSON = (0, exports.safeJSON)(errText);\n            const errMessage = errJSON ? undefined : errText;\n            const retryMessage = retriesRemaining ? `(error; no more retries left)` : `(error; not retryable)`;\n            debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders, errMessage);\n            const err = this.makeStatusError(response.status, errJSON, errMessage, responseHeaders);\n            throw err;\n        }\n        return { response, options, controller };\n    }\n    requestAPIList(Page, options) {\n        const request = this.makeRequest(options, null);\n        return new PagePromise(this, request, Page);\n    }\n    buildURL(path, query) {\n        const url = isAbsoluteURL(path) ?\n            new URL(path)\n            : new URL(this.baseURL + (this.baseURL.endsWith('/') && path.startsWith('/') ? path.slice(1) : path));\n        const defaultQuery = this.defaultQuery();\n        if (!isEmptyObj(defaultQuery)) {\n            query = { ...defaultQuery, ...query };\n        }\n        if (typeof query === 'object' && query && !Array.isArray(query)) {\n            url.search = this.stringifyQuery(query);\n        }\n        return url.toString();\n    }\n    stringifyQuery(query) {\n        return Object.entries(query)\n            .filter(([_, value]) => typeof value !== 'undefined')\n            .map(([key, value]) => {\n            if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n                return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;\n            }\n            if (value === null) {\n                return `${encodeURIComponent(key)}=`;\n            }\n            throw new error_1.GroqError(`Cannot stringify type ${typeof value}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`);\n        })\n            .join('&');\n    }\n    async fetchWithTimeout(url, init, ms, controller) {\n        const { signal, ...options } = init || {};\n        if (signal)\n            signal.addEventListener('abort', () => controller.abort());\n        const timeout = setTimeout(() => controller.abort(), ms);\n        return (this.getRequestClient()\n            // use undefined this binding; fetch errors if bound to something else in browser/cloudflare\n            .fetch.call(undefined, url, { signal: controller.signal, ...options })\n            .finally(() => {\n            clearTimeout(timeout);\n        }));\n    }\n    getRequestClient() {\n        return { fetch: this.fetch };\n    }\n    shouldRetry(response) {\n        // Note this is not a standard header.\n        const shouldRetryHeader = response.headers.get('x-should-retry');\n        // If the server explicitly says whether or not to retry, obey.\n        if (shouldRetryHeader === 'true')\n            return true;\n        if (shouldRetryHeader === 'false')\n            return false;\n        // Retry on request timeouts.\n        if (response.status === 408)\n            return true;\n        // Retry on lock timeouts.\n        if (response.status === 409)\n            return true;\n        // Retry on rate limits.\n        if (response.status === 429)\n            return true;\n        // Retry internal errors.\n        if (response.status >= 500)\n            return true;\n        return false;\n    }\n    async retryRequest(options, retriesRemaining, responseHeaders) {\n        let timeoutMillis;\n        // Note the `retry-after-ms` header may not be standard, but is a good idea and we'd like proactive support for it.\n        const retryAfterMillisHeader = responseHeaders?.['retry-after-ms'];\n        if (retryAfterMillisHeader) {\n            const timeoutMs = parseFloat(retryAfterMillisHeader);\n            if (!Number.isNaN(timeoutMs)) {\n                timeoutMillis = timeoutMs;\n            }\n        }\n        // About the Retry-After header: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After\n        const retryAfterHeader = responseHeaders?.['retry-after'];\n        if (retryAfterHeader && !timeoutMillis) {\n            const timeoutSeconds = parseFloat(retryAfterHeader);\n            if (!Number.isNaN(timeoutSeconds)) {\n                timeoutMillis = timeoutSeconds * 1000;\n            }\n            else {\n                timeoutMillis = Date.parse(retryAfterHeader) - Date.now();\n            }\n        }\n        // If the API asks us to wait a certain amount of time (and it's a reasonable amount),\n        // just do what it says, but otherwise calculate a default\n        if (!(timeoutMillis && 0 <= timeoutMillis && timeoutMillis < 60 * 1000)) {\n            const maxRetries = options.maxRetries ?? this.maxRetries;\n            timeoutMillis = this.calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries);\n        }\n        await (0, exports.sleep)(timeoutMillis);\n        return this.makeRequest(options, retriesRemaining - 1);\n    }\n    calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries) {\n        const initialRetryDelay = 0.5;\n        const maxRetryDelay = 8.0;\n        const numRetries = maxRetries - retriesRemaining;\n        // Apply exponential backoff, but not more than the max.\n        const sleepSeconds = Math.min(initialRetryDelay * Math.pow(2, numRetries), maxRetryDelay);\n        // Apply some jitter, take up to at most 25 percent of the retry time.\n        const jitter = 1 - Math.random() * 0.25;\n        return sleepSeconds * jitter * 1000;\n    }\n    getUserAgent() {\n        return `${this.constructor.name}/JS ${version_1.VERSION}`;\n    }\n}\nexports.APIClient = APIClient;\nclass AbstractPage {\n    constructor(client, response, body, options) {\n        _AbstractPage_client.set(this, void 0);\n        __classPrivateFieldSet(this, _AbstractPage_client, client, \"f\");\n        this.options = options;\n        this.response = response;\n        this.body = body;\n    }\n    hasNextPage() {\n        const items = this.getPaginatedItems();\n        if (!items.length)\n            return false;\n        return this.nextPageInfo() != null;\n    }\n    async getNextPage() {\n        const nextInfo = this.nextPageInfo();\n        if (!nextInfo) {\n            throw new error_1.GroqError('No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.');\n        }\n        const nextOptions = { ...this.options };\n        if ('params' in nextInfo && typeof nextOptions.query === 'object') {\n            nextOptions.query = { ...nextOptions.query, ...nextInfo.params };\n        }\n        else if ('url' in nextInfo) {\n            const params = [...Object.entries(nextOptions.query || {}), ...nextInfo.url.searchParams.entries()];\n            for (const [key, value] of params) {\n                nextInfo.url.searchParams.set(key, value);\n            }\n            nextOptions.query = undefined;\n            nextOptions.path = nextInfo.url.toString();\n        }\n        return await __classPrivateFieldGet(this, _AbstractPage_client, \"f\").requestAPIList(this.constructor, nextOptions);\n    }\n    async *iterPages() {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        let page = this;\n        yield page;\n        while (page.hasNextPage()) {\n            page = await page.getNextPage();\n            yield page;\n        }\n    }\n    async *[(_AbstractPage_client = new WeakMap(), Symbol.asyncIterator)]() {\n        for await (const page of this.iterPages()) {\n            for (const item of page.getPaginatedItems()) {\n                yield item;\n            }\n        }\n    }\n}\nexports.AbstractPage = AbstractPage;\n/**\n * This subclass of Promise will resolve to an instantiated Page once the request completes.\n *\n * It also implements AsyncIterable to allow auto-paginating iteration on an unawaited list call, eg:\n *\n *    for await (const item of client.items.list()) {\n *      console.log(item)\n *    }\n */\nclass PagePromise extends APIPromise {\n    constructor(client, request, Page) {\n        super(request, async (props) => new Page(client, props.response, await defaultParseResponse(props), props.options));\n    }\n    /**\n     * Allow auto-paginating iteration on an unawaited list call, eg:\n     *\n     *    for await (const item of client.items.list()) {\n     *      console.log(item)\n     *    }\n     */\n    async *[Symbol.asyncIterator]() {\n        const page = await this;\n        for await (const item of page) {\n            yield item;\n        }\n    }\n}\nexports.PagePromise = PagePromise;\nconst createResponseHeaders = (headers) => {\n    return new Proxy(Object.fromEntries(\n    // @ts-ignore\n    headers.entries()), {\n        get(target, name) {\n            const key = name.toString();\n            return target[key.toLowerCase()] || target[key];\n        },\n    });\n};\nexports.createResponseHeaders = createResponseHeaders;\n// This is required so that we can determine if a given object matches the RequestOptions\n// type at runtime. While this requires duplication, it is enforced by the TypeScript\n// compiler such that any missing / extraneous keys will cause an error.\nconst requestOptionsKeys = {\n    method: true,\n    path: true,\n    query: true,\n    body: true,\n    headers: true,\n    maxRetries: true,\n    stream: true,\n    timeout: true,\n    httpAgent: true,\n    signal: true,\n    idempotencyKey: true,\n    __binaryRequest: true,\n    __binaryResponse: true,\n    __streamClass: true,\n};\nconst isRequestOptions = (obj) => {\n    return (typeof obj === 'object' &&\n        obj !== null &&\n        !isEmptyObj(obj) &&\n        Object.keys(obj).every((k) => hasOwn(requestOptionsKeys, k)));\n};\nexports.isRequestOptions = isRequestOptions;\nconst getPlatformProperties = () => {\n    if (typeof Deno !== 'undefined' && Deno.build != null) {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': version_1.VERSION,\n            'X-Stainless-OS': normalizePlatform(Deno.build.os),\n            'X-Stainless-Arch': normalizeArch(Deno.build.arch),\n            'X-Stainless-Runtime': 'deno',\n            'X-Stainless-Runtime-Version': typeof Deno.version === 'string' ? Deno.version : Deno.version?.deno ?? 'unknown',\n        };\n    }\n    if (typeof EdgeRuntime !== 'undefined') {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': version_1.VERSION,\n            'X-Stainless-OS': 'Unknown',\n            'X-Stainless-Arch': `other:${EdgeRuntime}`,\n            'X-Stainless-Runtime': 'edge',\n            'X-Stainless-Runtime-Version': process.version,\n        };\n    }\n    // Check if Node.js\n    if (Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]') {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': version_1.VERSION,\n            'X-Stainless-OS': normalizePlatform(process.platform),\n            'X-Stainless-Arch': normalizeArch(process.arch),\n            'X-Stainless-Runtime': 'node',\n            'X-Stainless-Runtime-Version': process.version,\n        };\n    }\n    const browserInfo = getBrowserInfo();\n    if (browserInfo) {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': version_1.VERSION,\n            'X-Stainless-OS': 'Unknown',\n            'X-Stainless-Arch': 'unknown',\n            'X-Stainless-Runtime': `browser:${browserInfo.browser}`,\n            'X-Stainless-Runtime-Version': browserInfo.version,\n        };\n    }\n    // TODO add support for Cloudflare workers, etc.\n    return {\n        'X-Stainless-Lang': 'js',\n        'X-Stainless-Package-Version': version_1.VERSION,\n        'X-Stainless-OS': 'Unknown',\n        'X-Stainless-Arch': 'unknown',\n        'X-Stainless-Runtime': 'unknown',\n        'X-Stainless-Runtime-Version': 'unknown',\n    };\n};\n// Note: modified from https://github.com/JS-DevTools/host-environment/blob/b1ab79ecde37db5d6e163c050e54fe7d287d7c92/src/isomorphic.browser.ts\nfunction getBrowserInfo() {\n    if (typeof navigator === 'undefined' || !navigator) {\n        return null;\n    }\n    // NOTE: The order matters here!\n    const browserPatterns = [\n        { key: 'edge', pattern: /Edge(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'ie', pattern: /MSIE(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'ie', pattern: /Trident(?:.*rv\\:(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'chrome', pattern: /Chrome(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'firefox', pattern: /Firefox(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'safari', pattern: /(?:Version\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?(?:\\W+Mobile\\S*)?\\W+Safari/ },\n    ];\n    // Find the FIRST matching browser\n    for (const { key, pattern } of browserPatterns) {\n        const match = pattern.exec(navigator.userAgent);\n        if (match) {\n            const major = match[1] || 0;\n            const minor = match[2] || 0;\n            const patch = match[3] || 0;\n            return { browser: key, version: `${major}.${minor}.${patch}` };\n        }\n    }\n    return null;\n}\nconst normalizeArch = (arch) => {\n    // Node docs:\n    // - https://nodejs.org/api/process.html#processarch\n    // Deno docs:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    if (arch === 'x32')\n        return 'x32';\n    if (arch === 'x86_64' || arch === 'x64')\n        return 'x64';\n    if (arch === 'arm')\n        return 'arm';\n    if (arch === 'aarch64' || arch === 'arm64')\n        return 'arm64';\n    if (arch)\n        return `other:${arch}`;\n    return 'unknown';\n};\nconst normalizePlatform = (platform) => {\n    // Node platforms:\n    // - https://nodejs.org/api/process.html#processplatform\n    // Deno platforms:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    // - https://github.com/denoland/deno/issues/14799\n    platform = platform.toLowerCase();\n    // NOTE: this iOS check is untested and may not work\n    // Node does not work natively on IOS, there is a fork at\n    // https://github.com/nodejs-mobile/nodejs-mobile\n    // however it is unknown at the time of writing how to detect if it is running\n    if (platform.includes('ios'))\n        return 'iOS';\n    if (platform === 'android')\n        return 'Android';\n    if (platform === 'darwin')\n        return 'MacOS';\n    if (platform === 'win32')\n        return 'Windows';\n    if (platform === 'freebsd')\n        return 'FreeBSD';\n    if (platform === 'openbsd')\n        return 'OpenBSD';\n    if (platform === 'linux')\n        return 'Linux';\n    if (platform)\n        return `Other:${platform}`;\n    return 'Unknown';\n};\nlet _platformHeaders;\nconst getPlatformHeaders = () => {\n    return (_platformHeaders ?? (_platformHeaders = getPlatformProperties()));\n};\nconst safeJSON = (text) => {\n    try {\n        return JSON.parse(text);\n    }\n    catch (err) {\n        return undefined;\n    }\n};\nexports.safeJSON = safeJSON;\n// https://stackoverflow.com/a/19709846\nconst startsWithSchemeRegexp = new RegExp('^(?:[a-z]+:)?//', 'i');\nconst isAbsoluteURL = (url) => {\n    return startsWithSchemeRegexp.test(url);\n};\nconst sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\nexports.sleep = sleep;\nconst validatePositiveInteger = (name, n) => {\n    if (typeof n !== 'number' || !Number.isInteger(n)) {\n        throw new error_1.GroqError(`${name} must be an integer`);\n    }\n    if (n < 0) {\n        throw new error_1.GroqError(`${name} must be a positive integer`);\n    }\n    return n;\n};\nconst castToError = (err) => {\n    if (err instanceof Error)\n        return err;\n    return new Error(err);\n};\nexports.castToError = castToError;\nconst ensurePresent = (value) => {\n    if (value == null)\n        throw new error_1.GroqError(`Expected a value to be given but received ${value} instead.`);\n    return value;\n};\nexports.ensurePresent = ensurePresent;\n/**\n * Read an environment variable.\n *\n * Trims beginning and trailing whitespace.\n *\n * Will return undefined if the environment variable doesn't exist or cannot be accessed.\n */\nconst readEnv = (env) => {\n    if (typeof process !== 'undefined') {\n        return process.env?.[env]?.trim() ?? undefined;\n    }\n    if (typeof Deno !== 'undefined') {\n        return Deno.env?.get?.(env)?.trim();\n    }\n    return undefined;\n};\nexports.readEnv = readEnv;\nconst coerceInteger = (value) => {\n    if (typeof value === 'number')\n        return Math.round(value);\n    if (typeof value === 'string')\n        return parseInt(value, 10);\n    throw new error_1.GroqError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nexports.coerceInteger = coerceInteger;\nconst coerceFloat = (value) => {\n    if (typeof value === 'number')\n        return value;\n    if (typeof value === 'string')\n        return parseFloat(value);\n    throw new error_1.GroqError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nexports.coerceFloat = coerceFloat;\nconst coerceBoolean = (value) => {\n    if (typeof value === 'boolean')\n        return value;\n    if (typeof value === 'string')\n        return value === 'true';\n    return Boolean(value);\n};\nexports.coerceBoolean = coerceBoolean;\nconst maybeCoerceInteger = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return (0, exports.coerceInteger)(value);\n};\nexports.maybeCoerceInteger = maybeCoerceInteger;\nconst maybeCoerceFloat = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return (0, exports.coerceFloat)(value);\n};\nexports.maybeCoerceFloat = maybeCoerceFloat;\nconst maybeCoerceBoolean = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return (0, exports.coerceBoolean)(value);\n};\nexports.maybeCoerceBoolean = maybeCoerceBoolean;\n// https://stackoverflow.com/a/34491287\nfunction isEmptyObj(obj) {\n    if (!obj)\n        return true;\n    for (const _k in obj)\n        return false;\n    return true;\n}\nexports.isEmptyObj = isEmptyObj;\n// https://eslint.org/docs/latest/rules/no-prototype-builtins\nfunction hasOwn(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\nexports.hasOwn = hasOwn;\n/**\n * Copies headers from \"newHeaders\" onto \"targetHeaders\",\n * using lower-case for all properties,\n * ignoring any keys with undefined values,\n * and deleting any keys with null values.\n */\nfunction applyHeadersMut(targetHeaders, newHeaders) {\n    for (const k in newHeaders) {\n        if (!hasOwn(newHeaders, k))\n            continue;\n        const lowerKey = k.toLowerCase();\n        if (!lowerKey)\n            continue;\n        const val = newHeaders[k];\n        if (val === null) {\n            delete targetHeaders[lowerKey];\n        }\n        else if (val !== undefined) {\n            targetHeaders[lowerKey] = val;\n        }\n    }\n}\nfunction debug(action, ...args) {\n    if (typeof process !== 'undefined' && process?.env?.['DEBUG'] === 'true') {\n        console.log(`Groq:DEBUG:${action}`, ...args);\n    }\n}\nexports.debug = debug;\n/**\n * https://stackoverflow.com/a/2117523\n */\nconst uuid4 = () => {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16) | 0;\n        const v = c === 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n};\nconst isRunningInBrowser = () => {\n    return (\n    // @ts-ignore\n    typeof window !== 'undefined' &&\n        // @ts-ignore\n        typeof window.document !== 'undefined' &&\n        // @ts-ignore\n        typeof navigator !== 'undefined');\n};\nexports.isRunningInBrowser = isRunningInBrowser;\nconst isHeadersProtocol = (headers) => {\n    return typeof headers?.get === 'function';\n};\nexports.isHeadersProtocol = isHeadersProtocol;\nconst getRequiredHeader = (headers, header) => {\n    const lowerCasedHeader = header.toLowerCase();\n    if ((0, exports.isHeadersProtocol)(headers)) {\n        // to deal with the case where the header looks like Stainless-Event-Id\n        const intercapsHeader = header[0]?.toUpperCase() +\n            header.substring(1).replace(/([^\\w])(\\w)/g, (_m, g1, g2) => g1 + g2.toUpperCase());\n        for (const key of [header, lowerCasedHeader, header.toUpperCase(), intercapsHeader]) {\n            const value = headers.get(key);\n            if (value) {\n                return value;\n            }\n        }\n    }\n    for (const [key, value] of Object.entries(headers)) {\n        if (key.toLowerCase() === lowerCasedHeader) {\n            if (Array.isArray(value)) {\n                if (value.length <= 1)\n                    return value[0];\n                console.warn(`Received ${value.length} entries for the ${header} header, using the first entry.`);\n                return value[0];\n            }\n            return value;\n        }\n    }\n    throw new Error(`Could not find ${header} header`);\n};\nexports.getRequiredHeader = getRequiredHeader;\n/**\n * Encodes a string to Base64 format.\n */\nconst toBase64 = (str) => {\n    if (!str)\n        return '';\n    if (typeof Buffer !== 'undefined') {\n        return Buffer.from(str).toString('base64');\n    }\n    if (typeof btoa !== 'undefined') {\n        return btoa(str);\n    }\n    throw new error_1.GroqError('Cannot generate b64 string; Expected `Buffer` or `btoa` to be defined');\n};\nexports.toBase64 = toBase64;\nfunction isObj(obj) {\n    return obj != null && typeof obj === 'object' && !Array.isArray(obj);\n}\nexports.isObj = isObj;\n//# sourceMappingURL=core.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/error.js":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/error.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InternalServerError = exports.RateLimitError = exports.UnprocessableEntityError = exports.ConflictError = exports.NotFoundError = exports.PermissionDeniedError = exports.AuthenticationError = exports.BadRequestError = exports.APIConnectionTimeoutError = exports.APIConnectionError = exports.APIUserAbortError = exports.APIError = exports.GroqError = void 0;\nconst core_1 = __webpack_require__(/*! ./core.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/core.js\");\nclass GroqError extends Error {\n}\nexports.GroqError = GroqError;\nclass APIError extends GroqError {\n    constructor(status, error, message, headers) {\n        super(`${APIError.makeMessage(status, error, message)}`);\n        this.status = status;\n        this.headers = headers;\n        this.error = error;\n    }\n    static makeMessage(status, error, message) {\n        const msg = error?.message ?\n            typeof error.message === 'string' ?\n                error.message\n                : JSON.stringify(error.message)\n            : error ? JSON.stringify(error)\n                : message;\n        if (status && msg) {\n            return `${status} ${msg}`;\n        }\n        if (status) {\n            return `${status} status code (no body)`;\n        }\n        if (msg) {\n            return msg;\n        }\n        return '(no status code or body)';\n    }\n    static generate(status, errorResponse, message, headers) {\n        if (!status) {\n            return new APIConnectionError({ cause: (0, core_1.castToError)(errorResponse) });\n        }\n        const error = errorResponse;\n        if (status === 400) {\n            return new BadRequestError(status, error, message, headers);\n        }\n        if (status === 401) {\n            return new AuthenticationError(status, error, message, headers);\n        }\n        if (status === 403) {\n            return new PermissionDeniedError(status, error, message, headers);\n        }\n        if (status === 404) {\n            return new NotFoundError(status, error, message, headers);\n        }\n        if (status === 409) {\n            return new ConflictError(status, error, message, headers);\n        }\n        if (status === 422) {\n            return new UnprocessableEntityError(status, error, message, headers);\n        }\n        if (status === 429) {\n            return new RateLimitError(status, error, message, headers);\n        }\n        if (status >= 500) {\n            return new InternalServerError(status, error, message, headers);\n        }\n        return new APIError(status, error, message, headers);\n    }\n}\nexports.APIError = APIError;\nclass APIUserAbortError extends APIError {\n    constructor({ message } = {}) {\n        super(undefined, undefined, message || 'Request was aborted.', undefined);\n        this.status = undefined;\n    }\n}\nexports.APIUserAbortError = APIUserAbortError;\nclass APIConnectionError extends APIError {\n    constructor({ message, cause }) {\n        super(undefined, undefined, message || 'Connection error.', undefined);\n        this.status = undefined;\n        // in some environments the 'cause' property is already declared\n        // @ts-ignore\n        if (cause)\n            this.cause = cause;\n    }\n}\nexports.APIConnectionError = APIConnectionError;\nclass APIConnectionTimeoutError extends APIConnectionError {\n    constructor({ message } = {}) {\n        super({ message: message ?? 'Request timed out.' });\n    }\n}\nexports.APIConnectionTimeoutError = APIConnectionTimeoutError;\nclass BadRequestError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 400;\n    }\n}\nexports.BadRequestError = BadRequestError;\nclass AuthenticationError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 401;\n    }\n}\nexports.AuthenticationError = AuthenticationError;\nclass PermissionDeniedError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 403;\n    }\n}\nexports.PermissionDeniedError = PermissionDeniedError;\nclass NotFoundError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 404;\n    }\n}\nexports.NotFoundError = NotFoundError;\nclass ConflictError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 409;\n    }\n}\nexports.ConflictError = ConflictError;\nclass UnprocessableEntityError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 422;\n    }\n}\nexports.UnprocessableEntityError = UnprocessableEntityError;\nclass RateLimitError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 429;\n    }\n}\nexports.RateLimitError = RateLimitError;\nclass InternalServerError extends APIError {\n}\nexports.InternalServerError = InternalServerError;\n//# sourceMappingURL=error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/index.js ***!
  \**************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.fileFromPath = exports.toFile = exports.UnprocessableEntityError = exports.PermissionDeniedError = exports.InternalServerError = exports.AuthenticationError = exports.BadRequestError = exports.RateLimitError = exports.ConflictError = exports.NotFoundError = exports.APIUserAbortError = exports.APIConnectionTimeoutError = exports.APIConnectionError = exports.APIError = exports.GroqError = exports.Groq = void 0;\nconst Core = __importStar(__webpack_require__(/*! ./core.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/core.js\"));\nconst Errors = __importStar(__webpack_require__(/*! ./error.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/error.js\"));\nconst Uploads = __importStar(__webpack_require__(/*! ./uploads.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/uploads.js\"));\nconst API = __importStar(__webpack_require__(/*! ./resources/index.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/index.js\"));\n/** API Client for interfacing with the Groq API. */\nclass Groq extends Core.APIClient {\n    /**\n     * API Client for interfacing with the Groq API.\n     *\n     * @param {string | undefined} [opts.apiKey=process.env['GROQ_API_KEY'] ?? undefined]\n     * @param {string} [opts.baseURL=process.env['GROQ_BASE_URL'] ?? https://api.groq.com] - Override the default base URL for the API.\n     * @param {number} [opts.timeout=1 minute] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.\n     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.\n     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.\n     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.\n     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.\n     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.\n     * @param {boolean} [opts.dangerouslyAllowBrowser=false] - By default, client-side use of this library is not allowed, as it risks exposing your secret API credentials to attackers.\n     */\n    constructor({ baseURL = Core.readEnv('GROQ_BASE_URL'), apiKey = Core.readEnv('GROQ_API_KEY'), ...opts } = {}) {\n        if (apiKey === undefined) {\n            throw new Errors.GroqError(\"The GROQ_API_KEY environment variable is missing or empty; either provide it, or instantiate the Groq client with an apiKey option, like new Groq({ apiKey: 'My API Key' }).\");\n        }\n        const options = {\n            apiKey,\n            ...opts,\n            baseURL: baseURL || `https://api.groq.com`,\n        };\n        if (!options.dangerouslyAllowBrowser && Core.isRunningInBrowser()) {\n            throw new Errors.GroqError(\"It looks like you're running in a browser-like environment.\\n\\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\\nIf you understand the risks and have appropriate mitigations in place,\\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\\n\\nnew Groq({ apiKey, dangerouslyAllowBrowser: true })\");\n        }\n        super({\n            baseURL: options.baseURL,\n            timeout: options.timeout ?? 60000 /* 1 minute */,\n            httpAgent: options.httpAgent,\n            maxRetries: options.maxRetries,\n            fetch: options.fetch,\n        });\n        this.completions = new API.Completions(this);\n        this.chat = new API.Chat(this);\n        this.embeddings = new API.Embeddings(this);\n        this.audio = new API.Audio(this);\n        this.models = new API.Models(this);\n        this._options = options;\n        this.apiKey = apiKey;\n    }\n    defaultQuery() {\n        return this._options.defaultQuery;\n    }\n    defaultHeaders(opts) {\n        return {\n            ...super.defaultHeaders(opts),\n            ...this._options.defaultHeaders,\n        };\n    }\n    authHeaders(opts) {\n        return { Authorization: `Bearer ${this.apiKey}` };\n    }\n}\nexports.Groq = Groq;\n_a = Groq;\nGroq.Groq = _a;\nGroq.GroqError = Errors.GroqError;\nGroq.APIError = Errors.APIError;\nGroq.APIConnectionError = Errors.APIConnectionError;\nGroq.APIConnectionTimeoutError = Errors.APIConnectionTimeoutError;\nGroq.APIUserAbortError = Errors.APIUserAbortError;\nGroq.NotFoundError = Errors.NotFoundError;\nGroq.ConflictError = Errors.ConflictError;\nGroq.RateLimitError = Errors.RateLimitError;\nGroq.BadRequestError = Errors.BadRequestError;\nGroq.AuthenticationError = Errors.AuthenticationError;\nGroq.InternalServerError = Errors.InternalServerError;\nGroq.PermissionDeniedError = Errors.PermissionDeniedError;\nGroq.UnprocessableEntityError = Errors.UnprocessableEntityError;\nGroq.toFile = Uploads.toFile;\nGroq.fileFromPath = Uploads.fileFromPath;\nexports.GroqError = Errors.GroqError, exports.APIError = Errors.APIError, exports.APIConnectionError = Errors.APIConnectionError, exports.APIConnectionTimeoutError = Errors.APIConnectionTimeoutError, exports.APIUserAbortError = Errors.APIUserAbortError, exports.NotFoundError = Errors.NotFoundError, exports.ConflictError = Errors.ConflictError, exports.RateLimitError = Errors.RateLimitError, exports.BadRequestError = Errors.BadRequestError, exports.AuthenticationError = Errors.AuthenticationError, exports.InternalServerError = Errors.InternalServerError, exports.PermissionDeniedError = Errors.PermissionDeniedError, exports.UnprocessableEntityError = Errors.UnprocessableEntityError;\nexports.toFile = Uploads.toFile;\nexports.fileFromPath = Uploads.fileFromPath;\n(function (Groq) {\n    Groq.Completions = API.Completions;\n    Groq.Chat = API.Chat;\n    Groq.Embeddings = API.Embeddings;\n    Groq.Audio = API.Audio;\n    Groq.Models = API.Models;\n})(Groq = exports.Groq || (exports.Groq = {}));\nexports = module.exports = Groq;\nexports[\"default\"] = Groq;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/lib/streaming.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/lib/streaming.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.readableStreamAsyncIterable = exports.Stream = void 0;\nconst index_1 = __webpack_require__(/*! ../_shims/index.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/index.js\");\nconst error_1 = __webpack_require__(/*! ../error.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/error.js\");\nconst error_2 = __webpack_require__(/*! ../error.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/error.js\");\nclass Stream {\n    constructor(iterator, controller) {\n        this.iterator = iterator;\n        this.controller = controller;\n    }\n    static fromSSEResponse(response, controller) {\n        let consumed = false;\n        const decoder = new SSEDecoder();\n        async function* iterMessages() {\n            if (!response.body) {\n                controller.abort();\n                throw new error_1.GroqError(`Attempted to iterate over a response with no body`);\n            }\n            const lineDecoder = new LineDecoder();\n            const iter = readableStreamAsyncIterable(response.body);\n            for await (const chunk of iter) {\n                for (const line of lineDecoder.decode(chunk)) {\n                    const sse = decoder.decode(line);\n                    if (sse)\n                        yield sse;\n                }\n            }\n            for (const line of lineDecoder.flush()) {\n                const sse = decoder.decode(line);\n                if (sse)\n                    yield sse;\n            }\n        }\n        async function* iterator() {\n            if (consumed) {\n                throw new Error('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n            }\n            consumed = true;\n            let done = false;\n            try {\n                for await (const sse of iterMessages()) {\n                    if (done)\n                        continue;\n                    if (sse.data.startsWith('[DONE]')) {\n                        done = true;\n                        continue;\n                    }\n                    if (sse.event === null) {\n                        let data;\n                        try {\n                            data = JSON.parse(sse.data);\n                        }\n                        catch (e) {\n                            console.error(`Could not parse message into JSON:`, sse.data);\n                            console.error(`From chunk:`, sse.raw);\n                            throw e;\n                        }\n                        if (data && data.error) {\n                            throw new error_2.APIError(undefined, data.error, undefined, undefined);\n                        }\n                        yield data;\n                    }\n                }\n                done = true;\n            }\n            catch (e) {\n                // If the user calls `stream.controller.abort()`, we should exit without throwing.\n                if (e instanceof Error && e.name === 'AbortError')\n                    return;\n                throw e;\n            }\n            finally {\n                // If the user `break`s, abort the ongoing request.\n                if (!done)\n                    controller.abort();\n            }\n        }\n        return new Stream(iterator, controller);\n    }\n    /**\n     * Generates a Stream from a newline-separated ReadableStream\n     * where each item is a JSON value.\n     */\n    static fromReadableStream(readableStream, controller) {\n        let consumed = false;\n        async function* iterLines() {\n            const lineDecoder = new LineDecoder();\n            const iter = readableStreamAsyncIterable(readableStream);\n            for await (const chunk of iter) {\n                for (const line of lineDecoder.decode(chunk)) {\n                    yield line;\n                }\n            }\n            for (const line of lineDecoder.flush()) {\n                yield line;\n            }\n        }\n        async function* iterator() {\n            if (consumed) {\n                throw new Error('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n            }\n            consumed = true;\n            let done = false;\n            try {\n                for await (const line of iterLines()) {\n                    if (done)\n                        continue;\n                    if (line)\n                        yield JSON.parse(line);\n                }\n                done = true;\n            }\n            catch (e) {\n                // If the user calls `stream.controller.abort()`, we should exit without throwing.\n                if (e instanceof Error && e.name === 'AbortError')\n                    return;\n                throw e;\n            }\n            finally {\n                // If the user `break`s, abort the ongoing request.\n                if (!done)\n                    controller.abort();\n            }\n        }\n        return new Stream(iterator, controller);\n    }\n    [Symbol.asyncIterator]() {\n        return this.iterator();\n    }\n    /**\n     * Splits the stream into two streams which can be\n     * independently read from at different speeds.\n     */\n    tee() {\n        const left = [];\n        const right = [];\n        const iterator = this.iterator();\n        const teeIterator = (queue) => {\n            return {\n                next: () => {\n                    if (queue.length === 0) {\n                        const result = iterator.next();\n                        left.push(result);\n                        right.push(result);\n                    }\n                    return queue.shift();\n                },\n            };\n        };\n        return [\n            new Stream(() => teeIterator(left), this.controller),\n            new Stream(() => teeIterator(right), this.controller),\n        ];\n    }\n    /**\n     * Converts this stream to a newline-separated ReadableStream of\n     * JSON stringified values in the stream\n     * which can be turned back into a Stream with `Stream.fromReadableStream()`.\n     */\n    toReadableStream() {\n        const self = this;\n        let iter;\n        const encoder = new TextEncoder();\n        return new index_1.ReadableStream({\n            async start() {\n                iter = self[Symbol.asyncIterator]();\n            },\n            async pull(ctrl) {\n                try {\n                    const { value, done } = await iter.next();\n                    if (done)\n                        return ctrl.close();\n                    const bytes = encoder.encode(JSON.stringify(value) + '\\n');\n                    ctrl.enqueue(bytes);\n                }\n                catch (err) {\n                    ctrl.error(err);\n                }\n            },\n            async cancel() {\n                await iter.return?.();\n            },\n        });\n    }\n}\nexports.Stream = Stream;\nclass SSEDecoder {\n    constructor() {\n        this.event = null;\n        this.data = [];\n        this.chunks = [];\n    }\n    decode(line) {\n        if (line.endsWith('\\r')) {\n            line = line.substring(0, line.length - 1);\n        }\n        if (!line) {\n            // empty line and we didn't previously encounter any messages\n            if (!this.event && !this.data.length)\n                return null;\n            const sse = {\n                event: this.event,\n                data: this.data.join('\\n'),\n                raw: this.chunks,\n            };\n            this.event = null;\n            this.data = [];\n            this.chunks = [];\n            return sse;\n        }\n        this.chunks.push(line);\n        if (line.startsWith(':')) {\n            return null;\n        }\n        let [fieldname, _, value] = partition(line, ':');\n        if (value.startsWith(' ')) {\n            value = value.substring(1);\n        }\n        if (fieldname === 'event') {\n            this.event = value;\n        }\n        else if (fieldname === 'data') {\n            this.data.push(value);\n        }\n        return null;\n    }\n}\n/**\n * A re-implementation of httpx's `LineDecoder` in Python that handles incrementally\n * reading lines from text.\n *\n * https://github.com/encode/httpx/blob/920333ea98118e9cf617f246905d7b202510941c/httpx/_decoders.py#L258\n */\nclass LineDecoder {\n    constructor() {\n        this.buffer = [];\n        this.trailingCR = false;\n    }\n    decode(chunk) {\n        let text = this.decodeText(chunk);\n        if (this.trailingCR) {\n            text = '\\r' + text;\n            this.trailingCR = false;\n        }\n        if (text.endsWith('\\r')) {\n            this.trailingCR = true;\n            text = text.slice(0, -1);\n        }\n        if (!text) {\n            return [];\n        }\n        const trailingNewline = LineDecoder.NEWLINE_CHARS.has(text[text.length - 1] || '');\n        let lines = text.split(LineDecoder.NEWLINE_REGEXP);\n        if (lines.length === 1 && !trailingNewline) {\n            this.buffer.push(lines[0]);\n            return [];\n        }\n        if (this.buffer.length > 0) {\n            lines = [this.buffer.join('') + lines[0], ...lines.slice(1)];\n            this.buffer = [];\n        }\n        if (!trailingNewline) {\n            this.buffer = [lines.pop() || ''];\n        }\n        return lines;\n    }\n    decodeText(bytes) {\n        if (bytes == null)\n            return '';\n        if (typeof bytes === 'string')\n            return bytes;\n        // Node:\n        if (typeof Buffer !== 'undefined') {\n            if (bytes instanceof Buffer) {\n                return bytes.toString();\n            }\n            if (bytes instanceof Uint8Array) {\n                return Buffer.from(bytes).toString();\n            }\n            throw new error_1.GroqError(`Unexpected: received non-Uint8Array (${bytes.constructor.name}) stream chunk in an environment with a global \"Buffer\" defined, which this library assumes to be Node. Please report this error.`);\n        }\n        // Browser\n        if (typeof TextDecoder !== 'undefined') {\n            if (bytes instanceof Uint8Array || bytes instanceof ArrayBuffer) {\n                this.textDecoder ?? (this.textDecoder = new TextDecoder('utf8'));\n                return this.textDecoder.decode(bytes);\n            }\n            throw new error_1.GroqError(`Unexpected: received non-Uint8Array/ArrayBuffer (${bytes.constructor.name}) in a web platform. Please report this error.`);\n        }\n        throw new error_1.GroqError(`Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.`);\n    }\n    flush() {\n        if (!this.buffer.length && !this.trailingCR) {\n            return [];\n        }\n        const lines = [this.buffer.join('')];\n        this.buffer = [];\n        this.trailingCR = false;\n        return lines;\n    }\n}\n// prettier-ignore\nLineDecoder.NEWLINE_CHARS = new Set(['\\n', '\\r', '\\x0b', '\\x0c', '\\x1c', '\\x1d', '\\x1e', '\\x85', '\\u2028', '\\u2029']);\nLineDecoder.NEWLINE_REGEXP = /\\r\\n|[\\n\\r\\x0b\\x0c\\x1c\\x1d\\x1e\\x85\\u2028\\u2029]/g;\nfunction partition(str, delimiter) {\n    const index = str.indexOf(delimiter);\n    if (index !== -1) {\n        return [str.substring(0, index), delimiter, str.substring(index + delimiter.length)];\n    }\n    return [str, '', ''];\n}\n/**\n * Most browsers don't yet have async iterable support for ReadableStream,\n * and Node has a very different way of reading bytes from its \"ReadableStream\".\n *\n * This polyfill was pulled from https://github.com/MattiasBuelens/web-streams-polyfill/pull/122#issuecomment-1627354490\n */\nfunction readableStreamAsyncIterable(stream) {\n    if (stream[Symbol.asyncIterator])\n        return stream;\n    const reader = stream.getReader();\n    return {\n        async next() {\n            try {\n                const result = await reader.read();\n                if (result?.done)\n                    reader.releaseLock(); // release lock when stream becomes closed\n                return result;\n            }\n            catch (e) {\n                reader.releaseLock(); // release lock when stream becomes errored\n                throw e;\n            }\n        },\n        async return() {\n            const cancelPromise = reader.cancel();\n            reader.releaseLock();\n            await cancelPromise;\n            return { done: true, value: undefined };\n        },\n        [Symbol.asyncIterator]() {\n            return this;\n        },\n    };\n}\nexports.readableStreamAsyncIterable = readableStreamAsyncIterable;\n//# sourceMappingURL=streaming.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/lib/streaming.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resource.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resource.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.APIResource = void 0;\nclass APIResource {\n    constructor(client) {\n        this._client = client;\n    }\n}\nexports.APIResource = APIResource;\n//# sourceMappingURL=resource.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9ncm9xLXNka0AwLjUuMC9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBGaWxlIGdlbmVyYXRlZCBmcm9tIG91ciBPcGVuQVBJIHNwZWMgYnkgU3RhaW5sZXNzLiBTZWUgQ09OVFJJQlVUSU5HLm1kIGZvciBkZXRhaWxzLlxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5BUElSZXNvdXJjZSA9IHZvaWQgMDtcbmNsYXNzIEFQSVJlc291cmNlIHtcbiAgICBjb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgICAgICAgdGhpcy5fY2xpZW50ID0gY2xpZW50O1xuICAgIH1cbn1cbmV4cG9ydHMuQVBJUmVzb3VyY2UgPSBBUElSZXNvdXJjZTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlc291cmNlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resource.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/audio/audio.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/audio/audio.js ***!
  \******************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Audio = void 0;\nconst resource_1 = __webpack_require__(/*! ../../resource.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resource.js\");\nconst TranscriptionsAPI = __importStar(__webpack_require__(/*! ./transcriptions.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/audio/transcriptions.js\"));\nconst TranslationsAPI = __importStar(__webpack_require__(/*! ./translations.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/audio/translations.js\"));\nclass Audio extends resource_1.APIResource {\n    constructor() {\n        super(...arguments);\n        this.transcriptions = new TranscriptionsAPI.Transcriptions(this._client);\n        this.translations = new TranslationsAPI.Translations(this._client);\n    }\n}\nexports.Audio = Audio;\n(function (Audio) {\n    Audio.Transcriptions = TranscriptionsAPI.Transcriptions;\n    Audio.Translations = TranslationsAPI.Translations;\n})(Audio = exports.Audio || (exports.Audio = {}));\n//# sourceMappingURL=audio.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/audio/audio.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/audio/transcriptions.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/audio/transcriptions.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Transcriptions = void 0;\nconst resource_1 = __webpack_require__(/*! ../../resource.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resource.js\");\nconst core_1 = __webpack_require__(/*! ../../core.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/core.js\");\nclass Transcriptions extends resource_1.APIResource {\n    /**\n     * Transcribes audio into the input language.\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/audio/transcriptions', (0, core_1.multipartFormRequestOptions)({ body, ...options }));\n    }\n}\nexports.Transcriptions = Transcriptions;\n(function (Transcriptions) {\n})(Transcriptions = exports.Transcriptions || (exports.Transcriptions = {}));\n//# sourceMappingURL=transcriptions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlcy9hdWRpby90cmFuc2NyaXB0aW9ucy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHNCQUFzQjtBQUN0QixtQkFBbUIsbUJBQU8sQ0FBQyxzR0FBbUI7QUFDOUMsZUFBZSxtQkFBTyxDQUFDLDhGQUFlO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4R0FBOEcsa0JBQWtCO0FBQ2hJO0FBQ0E7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQSxDQUFDLDhDQUE4QyxzQkFBc0IsS0FBSztBQUMxRSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL2dyb3Etc2RrQDAuNS4wL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvYXVkaW8vdHJhbnNjcmlwdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBGaWxlIGdlbmVyYXRlZCBmcm9tIG91ciBPcGVuQVBJIHNwZWMgYnkgU3RhaW5sZXNzLiBTZWUgQ09OVFJJQlVUSU5HLm1kIGZvciBkZXRhaWxzLlxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5UcmFuc2NyaXB0aW9ucyA9IHZvaWQgMDtcbmNvbnN0IHJlc291cmNlXzEgPSByZXF1aXJlKFwiLi4vLi4vcmVzb3VyY2UuanNcIik7XG5jb25zdCBjb3JlXzEgPSByZXF1aXJlKFwiLi4vLi4vY29yZS5qc1wiKTtcbmNsYXNzIFRyYW5zY3JpcHRpb25zIGV4dGVuZHMgcmVzb3VyY2VfMS5BUElSZXNvdXJjZSB7XG4gICAgLyoqXG4gICAgICogVHJhbnNjcmliZXMgYXVkaW8gaW50byB0aGUgaW5wdXQgbGFuZ3VhZ2UuXG4gICAgICovXG4gICAgY3JlYXRlKGJvZHksIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5wb3N0KCcvb3BlbmFpL3YxL2F1ZGlvL3RyYW5zY3JpcHRpb25zJywgKDAsIGNvcmVfMS5tdWx0aXBhcnRGb3JtUmVxdWVzdE9wdGlvbnMpKHsgYm9keSwgLi4ub3B0aW9ucyB9KSk7XG4gICAgfVxufVxuZXhwb3J0cy5UcmFuc2NyaXB0aW9ucyA9IFRyYW5zY3JpcHRpb25zO1xuKGZ1bmN0aW9uIChUcmFuc2NyaXB0aW9ucykge1xufSkoVHJhbnNjcmlwdGlvbnMgPSBleHBvcnRzLlRyYW5zY3JpcHRpb25zIHx8IChleHBvcnRzLlRyYW5zY3JpcHRpb25zID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRyYW5zY3JpcHRpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/audio/transcriptions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/audio/translations.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/audio/translations.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Translations = void 0;\nconst resource_1 = __webpack_require__(/*! ../../resource.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resource.js\");\nconst core_1 = __webpack_require__(/*! ../../core.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/core.js\");\nclass Translations extends resource_1.APIResource {\n    /**\n     * Translates audio into English.\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/audio/translations', (0, core_1.multipartFormRequestOptions)({ body, ...options }));\n    }\n}\nexports.Translations = Translations;\n(function (Translations) {\n})(Translations = exports.Translations || (exports.Translations = {}));\n//# sourceMappingURL=translations.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlcy9hdWRpby90cmFuc2xhdGlvbnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxvQkFBb0I7QUFDcEIsbUJBQW1CLG1CQUFPLENBQUMsc0dBQW1CO0FBQzlDLGVBQWUsbUJBQU8sQ0FBQyw4RkFBZTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEdBQTRHLGtCQUFrQjtBQUM5SDtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0EsQ0FBQywwQ0FBMEMsb0JBQW9CLEtBQUs7QUFDcEUiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9ncm9xLXNka0AwLjUuMC9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2F1ZGlvL3RyYW5zbGF0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlRyYW5zbGF0aW9ucyA9IHZvaWQgMDtcbmNvbnN0IHJlc291cmNlXzEgPSByZXF1aXJlKFwiLi4vLi4vcmVzb3VyY2UuanNcIik7XG5jb25zdCBjb3JlXzEgPSByZXF1aXJlKFwiLi4vLi4vY29yZS5qc1wiKTtcbmNsYXNzIFRyYW5zbGF0aW9ucyBleHRlbmRzIHJlc291cmNlXzEuQVBJUmVzb3VyY2Uge1xuICAgIC8qKlxuICAgICAqIFRyYW5zbGF0ZXMgYXVkaW8gaW50byBFbmdsaXNoLlxuICAgICAqL1xuICAgIGNyZWF0ZShib2R5LCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQucG9zdCgnL29wZW5haS92MS9hdWRpby90cmFuc2xhdGlvbnMnLCAoMCwgY29yZV8xLm11bHRpcGFydEZvcm1SZXF1ZXN0T3B0aW9ucykoeyBib2R5LCAuLi5vcHRpb25zIH0pKTtcbiAgICB9XG59XG5leHBvcnRzLlRyYW5zbGF0aW9ucyA9IFRyYW5zbGF0aW9ucztcbihmdW5jdGlvbiAoVHJhbnNsYXRpb25zKSB7XG59KShUcmFuc2xhdGlvbnMgPSBleHBvcnRzLlRyYW5zbGF0aW9ucyB8fCAoZXhwb3J0cy5UcmFuc2xhdGlvbnMgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHJhbnNsYXRpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/audio/translations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/chat/chat.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/chat/chat.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Chat = void 0;\nconst resource_1 = __webpack_require__(/*! ../../resource.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resource.js\");\nconst CompletionsAPI = __importStar(__webpack_require__(/*! ./completions.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/chat/completions.js\"));\nclass Chat extends resource_1.APIResource {\n    constructor() {\n        super(...arguments);\n        this.completions = new CompletionsAPI.Completions(this._client);\n    }\n}\nexports.Chat = Chat;\n(function (Chat) {\n    Chat.Completions = CompletionsAPI.Completions;\n})(Chat = exports.Chat || (exports.Chat = {}));\n//# sourceMappingURL=chat.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/chat/chat.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/chat/completions.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/chat/completions.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Completions = void 0;\nconst resource_1 = __webpack_require__(/*! ../../resource.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resource.js\");\nclass Completions extends resource_1.APIResource {\n    create(body, options) {\n        return this._client.post('/openai/v1/chat/completions', {\n            body,\n            ...options,\n            stream: body.stream ?? false,\n        });\n    }\n}\nexports.Completions = Completions;\n(function (Completions) {\n})(Completions = exports.Completions || (exports.Completions = {}));\n//# sourceMappingURL=completions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlcy9jaGF0L2NvbXBsZXRpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsbUJBQW1CO0FBQ25CLG1CQUFtQixtQkFBTyxDQUFDLHNHQUFtQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBLENBQUMsd0NBQXdDLG1CQUFtQixLQUFLO0FBQ2pFIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlcy9jaGF0L2NvbXBsZXRpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuQ29tcGxldGlvbnMgPSB2b2lkIDA7XG5jb25zdCByZXNvdXJjZV8xID0gcmVxdWlyZShcIi4uLy4uL3Jlc291cmNlLmpzXCIpO1xuY2xhc3MgQ29tcGxldGlvbnMgZXh0ZW5kcyByZXNvdXJjZV8xLkFQSVJlc291cmNlIHtcbiAgICBjcmVhdGUoYm9keSwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LnBvc3QoJy9vcGVuYWkvdjEvY2hhdC9jb21wbGV0aW9ucycsIHtcbiAgICAgICAgICAgIGJvZHksXG4gICAgICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAgICAgc3RyZWFtOiBib2R5LnN0cmVhbSA/PyBmYWxzZSxcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0cy5Db21wbGV0aW9ucyA9IENvbXBsZXRpb25zO1xuKGZ1bmN0aW9uIChDb21wbGV0aW9ucykge1xufSkoQ29tcGxldGlvbnMgPSBleHBvcnRzLkNvbXBsZXRpb25zIHx8IChleHBvcnRzLkNvbXBsZXRpb25zID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbXBsZXRpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/chat/completions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/completions.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/completions.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Completions = void 0;\nconst resource_1 = __webpack_require__(/*! ../resource.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resource.js\");\nclass Completions extends resource_1.APIResource {\n}\nexports.Completions = Completions;\n(function (Completions) {\n})(Completions = exports.Completions || (exports.Completions = {}));\n//# sourceMappingURL=completions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlcy9jb21wbGV0aW9ucy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG1CQUFtQjtBQUNuQixtQkFBbUIsbUJBQU8sQ0FBQyxtR0FBZ0I7QUFDM0M7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBLENBQUMsd0NBQXdDLG1CQUFtQixLQUFLO0FBQ2pFIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlcy9jb21wbGV0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkNvbXBsZXRpb25zID0gdm9pZCAwO1xuY29uc3QgcmVzb3VyY2VfMSA9IHJlcXVpcmUoXCIuLi9yZXNvdXJjZS5qc1wiKTtcbmNsYXNzIENvbXBsZXRpb25zIGV4dGVuZHMgcmVzb3VyY2VfMS5BUElSZXNvdXJjZSB7XG59XG5leHBvcnRzLkNvbXBsZXRpb25zID0gQ29tcGxldGlvbnM7XG4oZnVuY3Rpb24gKENvbXBsZXRpb25zKSB7XG59KShDb21wbGV0aW9ucyA9IGV4cG9ydHMuQ29tcGxldGlvbnMgfHwgKGV4cG9ydHMuQ29tcGxldGlvbnMgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29tcGxldGlvbnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/completions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/embeddings.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/embeddings.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Embeddings = void 0;\nconst resource_1 = __webpack_require__(/*! ../resource.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resource.js\");\nclass Embeddings extends resource_1.APIResource {\n    /**\n     * Creates an embedding vector representing the input text.\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/embeddings', { body, ...options });\n    }\n}\nexports.Embeddings = Embeddings;\n(function (Embeddings) {\n})(Embeddings = exports.Embeddings || (exports.Embeddings = {}));\n//# sourceMappingURL=embeddings.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlcy9lbWJlZGRpbmdzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0JBQWtCO0FBQ2xCLG1CQUFtQixtQkFBTyxDQUFDLG1HQUFnQjtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNERBQTRELGtCQUFrQjtBQUM5RTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0EsQ0FBQyxzQ0FBc0Msa0JBQWtCLEtBQUs7QUFDOUQiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9ncm9xLXNka0AwLjUuMC9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2VtYmVkZGluZ3MuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBGaWxlIGdlbmVyYXRlZCBmcm9tIG91ciBPcGVuQVBJIHNwZWMgYnkgU3RhaW5sZXNzLiBTZWUgQ09OVFJJQlVUSU5HLm1kIGZvciBkZXRhaWxzLlxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5FbWJlZGRpbmdzID0gdm9pZCAwO1xuY29uc3QgcmVzb3VyY2VfMSA9IHJlcXVpcmUoXCIuLi9yZXNvdXJjZS5qc1wiKTtcbmNsYXNzIEVtYmVkZGluZ3MgZXh0ZW5kcyByZXNvdXJjZV8xLkFQSVJlc291cmNlIHtcbiAgICAvKipcbiAgICAgKiBDcmVhdGVzIGFuIGVtYmVkZGluZyB2ZWN0b3IgcmVwcmVzZW50aW5nIHRoZSBpbnB1dCB0ZXh0LlxuICAgICAqL1xuICAgIGNyZWF0ZShib2R5LCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQucG9zdCgnL29wZW5haS92MS9lbWJlZGRpbmdzJywgeyBib2R5LCAuLi5vcHRpb25zIH0pO1xuICAgIH1cbn1cbmV4cG9ydHMuRW1iZWRkaW5ncyA9IEVtYmVkZGluZ3M7XG4oZnVuY3Rpb24gKEVtYmVkZGluZ3MpIHtcbn0pKEVtYmVkZGluZ3MgPSBleHBvcnRzLkVtYmVkZGluZ3MgfHwgKGV4cG9ydHMuRW1iZWRkaW5ncyA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1lbWJlZGRpbmdzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/embeddings.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/index.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/index.js ***!
  \************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Models = exports.Embeddings = exports.Completions = exports.Chat = exports.Audio = void 0;\n__exportStar(__webpack_require__(/*! ./shared.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/shared.js\"), exports);\nvar audio_1 = __webpack_require__(/*! ./audio/audio.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/audio/audio.js\");\nObject.defineProperty(exports, \"Audio\", ({ enumerable: true, get: function () { return audio_1.Audio; } }));\nvar chat_1 = __webpack_require__(/*! ./chat/chat.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/chat/chat.js\");\nObject.defineProperty(exports, \"Chat\", ({ enumerable: true, get: function () { return chat_1.Chat; } }));\nvar completions_1 = __webpack_require__(/*! ./completions.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/completions.js\");\nObject.defineProperty(exports, \"Completions\", ({ enumerable: true, get: function () { return completions_1.Completions; } }));\nvar embeddings_1 = __webpack_require__(/*! ./embeddings.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/embeddings.js\");\nObject.defineProperty(exports, \"Embeddings\", ({ enumerable: true, get: function () { return embeddings_1.Embeddings; } }));\nvar models_1 = __webpack_require__(/*! ./models.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/models.js\");\nObject.defineProperty(exports, \"Models\", ({ enumerable: true, get: function () { return models_1.Models; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/models.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/models.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Models = void 0;\nconst resource_1 = __webpack_require__(/*! ../resource.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resource.js\");\nclass Models extends resource_1.APIResource {\n    /**\n     * Get a specific model\n     */\n    retrieve(model, options) {\n        return this._client.get(`/openai/v1/models/${model}`, options);\n    }\n    /**\n     * get all available models\n     */\n    list(options) {\n        return this._client.get('/openai/v1/models', options);\n    }\n    /**\n     * Delete a model\n     */\n    delete(model, options) {\n        return this._client.delete(`/openai/v1/models/${model}`, options);\n    }\n}\nexports.Models = Models;\n(function (Models) {\n})(Models = exports.Models || (exports.Models = {}));\n//# sourceMappingURL=models.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlcy9tb2RlbHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxjQUFjO0FBQ2QsbUJBQW1CLG1CQUFPLENBQUMsbUdBQWdCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQsTUFBTTtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdELE1BQU07QUFDOUQ7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBLENBQUMsOEJBQThCLGNBQWMsS0FBSztBQUNsRCIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL2dyb3Etc2RrQDAuNS4wL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvbW9kZWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuTW9kZWxzID0gdm9pZCAwO1xuY29uc3QgcmVzb3VyY2VfMSA9IHJlcXVpcmUoXCIuLi9yZXNvdXJjZS5qc1wiKTtcbmNsYXNzIE1vZGVscyBleHRlbmRzIHJlc291cmNlXzEuQVBJUmVzb3VyY2Uge1xuICAgIC8qKlxuICAgICAqIEdldCBhIHNwZWNpZmljIG1vZGVsXG4gICAgICovXG4gICAgcmV0cmlldmUobW9kZWwsIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5nZXQoYC9vcGVuYWkvdjEvbW9kZWxzLyR7bW9kZWx9YCwgb3B0aW9ucyk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIGdldCBhbGwgYXZhaWxhYmxlIG1vZGVsc1xuICAgICAqL1xuICAgIGxpc3Qob3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LmdldCgnL29wZW5haS92MS9tb2RlbHMnLCBvcHRpb25zKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGEgbW9kZWxcbiAgICAgKi9cbiAgICBkZWxldGUobW9kZWwsIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5kZWxldGUoYC9vcGVuYWkvdjEvbW9kZWxzLyR7bW9kZWx9YCwgb3B0aW9ucyk7XG4gICAgfVxufVxuZXhwb3J0cy5Nb2RlbHMgPSBNb2RlbHM7XG4oZnVuY3Rpb24gKE1vZGVscykge1xufSkoTW9kZWxzID0gZXhwb3J0cy5Nb2RlbHMgfHwgKGV4cG9ydHMuTW9kZWxzID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1vZGVscy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/models.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/shared.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/shared.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=shared.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlcy9zaGFyZWQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL2dyb3Etc2RrQDAuNS4wL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvc2hhcmVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNoYXJlZC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/resources/shared.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/uploads.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/uploads.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createForm = exports.multipartFormRequestOptions = exports.maybeMultipartFormRequestOptions = exports.isMultipartBody = exports.toFile = exports.isUploadable = exports.isBlobLike = exports.isFileLike = exports.isResponseLike = exports.fileFromPath = void 0;\nconst index_1 = __webpack_require__(/*! ./_shims/index.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/index.js\");\nvar index_2 = __webpack_require__(/*! ./_shims/index.js */ \"(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/_shims/index.js\");\nObject.defineProperty(exports, \"fileFromPath\", ({ enumerable: true, get: function () { return index_2.fileFromPath; } }));\nconst isResponseLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.url === 'string' &&\n    typeof value.blob === 'function';\nexports.isResponseLike = isResponseLike;\nconst isFileLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.name === 'string' &&\n    typeof value.lastModified === 'number' &&\n    (0, exports.isBlobLike)(value);\nexports.isFileLike = isFileLike;\n/**\n * The BlobLike type omits arrayBuffer() because @types/node-fetch@^2.6.4 lacks it; but this check\n * adds the arrayBuffer() method type because it is available and used at runtime\n */\nconst isBlobLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.size === 'number' &&\n    typeof value.type === 'string' &&\n    typeof value.text === 'function' &&\n    typeof value.slice === 'function' &&\n    typeof value.arrayBuffer === 'function';\nexports.isBlobLike = isBlobLike;\nconst isUploadable = (value) => {\n    return (0, exports.isFileLike)(value) || (0, exports.isResponseLike)(value) || (0, index_1.isFsReadStream)(value);\n};\nexports.isUploadable = isUploadable;\n/**\n * Helper for creating a {@link File} to pass to an SDK upload method from a variety of different data formats\n * @param value the raw content of the file.  Can be an {@link Uploadable}, {@link BlobLikePart}, or {@link AsyncIterable} of {@link BlobLikePart}s\n * @param {string=} name the name of the file. If omitted, toFile will try to determine a file name from bits if possible\n * @param {Object=} options additional properties\n * @param {string=} options.type the MIME type of the content\n * @param {number=} options.lastModified the last modified timestamp\n * @returns a {@link File} with the given properties\n */\nasync function toFile(value, name, options) {\n    // If it's a promise, resolve it.\n    value = await value;\n    // Use the file's options if there isn't one provided\n    options ?? (options = (0, exports.isFileLike)(value) ? { lastModified: value.lastModified, type: value.type } : {});\n    if ((0, exports.isResponseLike)(value)) {\n        const blob = await value.blob();\n        name || (name = new URL(value.url).pathname.split(/[\\\\/]/).pop() ?? 'unknown_file');\n        return new index_1.File([blob], name, options);\n    }\n    const bits = await getBytes(value);\n    name || (name = getName(value) ?? 'unknown_file');\n    if (!options.type) {\n        const type = bits[0]?.type;\n        if (typeof type === 'string') {\n            options = { ...options, type };\n        }\n    }\n    return new index_1.File(bits, name, options);\n}\nexports.toFile = toFile;\nasync function getBytes(value) {\n    let parts = [];\n    if (typeof value === 'string' ||\n        ArrayBuffer.isView(value) || // includes Uint8Array, Buffer, etc.\n        value instanceof ArrayBuffer) {\n        parts.push(value);\n    }\n    else if ((0, exports.isBlobLike)(value)) {\n        parts.push(await value.arrayBuffer());\n    }\n    else if (isAsyncIterableIterator(value) // includes Readable, ReadableStream, etc.\n    ) {\n        for await (const chunk of value) {\n            parts.push(chunk); // TODO, consider validating?\n        }\n    }\n    else {\n        throw new Error(`Unexpected data type: ${typeof value}; constructor: ${value?.constructor\n            ?.name}; props: ${propsForError(value)}`);\n    }\n    return parts;\n}\nfunction propsForError(value) {\n    const props = Object.getOwnPropertyNames(value);\n    return `[${props.map((p) => `\"${p}\"`).join(', ')}]`;\n}\nfunction getName(value) {\n    return (getStringFromMaybeBuffer(value.name) ||\n        getStringFromMaybeBuffer(value.filename) ||\n        // For fs.ReadStream\n        getStringFromMaybeBuffer(value.path)?.split(/[\\\\/]/).pop());\n}\nconst getStringFromMaybeBuffer = (x) => {\n    if (typeof x === 'string')\n        return x;\n    if (typeof Buffer !== 'undefined' && x instanceof Buffer)\n        return String(x);\n    return undefined;\n};\nconst isAsyncIterableIterator = (value) => value != null && typeof value === 'object' && typeof value[Symbol.asyncIterator] === 'function';\nconst isMultipartBody = (body) => body && typeof body === 'object' && body.body && body[Symbol.toStringTag] === 'MultipartBody';\nexports.isMultipartBody = isMultipartBody;\n/**\n * Returns a multipart/form-data request if any part of the given request body contains a File / Blob value.\n * Otherwise returns the request as is.\n */\nconst maybeMultipartFormRequestOptions = async (opts) => {\n    if (!hasUploadableValue(opts.body))\n        return opts;\n    const form = await (0, exports.createForm)(opts.body);\n    return (0, index_1.getMultipartRequestOptions)(form, opts);\n};\nexports.maybeMultipartFormRequestOptions = maybeMultipartFormRequestOptions;\nconst multipartFormRequestOptions = async (opts) => {\n    const form = await (0, exports.createForm)(opts.body);\n    return (0, index_1.getMultipartRequestOptions)(form, opts);\n};\nexports.multipartFormRequestOptions = multipartFormRequestOptions;\nconst createForm = async (body) => {\n    const form = new index_1.FormData();\n    await Promise.all(Object.entries(body || {}).map(([key, value]) => addFormValue(form, key, value)));\n    return form;\n};\nexports.createForm = createForm;\nconst hasUploadableValue = (value) => {\n    if ((0, exports.isUploadable)(value))\n        return true;\n    if (Array.isArray(value))\n        return value.some(hasUploadableValue);\n    if (value && typeof value === 'object') {\n        for (const k in value) {\n            if (hasUploadableValue(value[k]))\n                return true;\n        }\n    }\n    return false;\n};\nconst addFormValue = async (form, key, value) => {\n    if (value === undefined)\n        return;\n    if (value == null) {\n        throw new TypeError(`Received null for \"${key}\"; to pass null in FormData, you must use the string 'null'`);\n    }\n    // TODO: make nested formats configurable\n    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n        form.append(key, String(value));\n    }\n    else if ((0, exports.isUploadable)(value)) {\n        const file = await toFile(value);\n        form.append(key, file);\n    }\n    else if (Array.isArray(value)) {\n        await Promise.all(value.map((entry) => addFormValue(form, key + '[]', entry)));\n    }\n    else if (typeof value === 'object') {\n        await Promise.all(Object.entries(value).map(([name, prop]) => addFormValue(form, `${key}[${name}]`, prop)));\n    }\n    else {\n        throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${value} instead`);\n    }\n};\n//# sourceMappingURL=uploads.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3VwbG9hZHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0JBQWtCLEdBQUcsbUNBQW1DLEdBQUcsd0NBQXdDLEdBQUcsdUJBQXVCLEdBQUcsY0FBYyxHQUFHLG9CQUFvQixHQUFHLGtCQUFrQixHQUFHLGtCQUFrQixHQUFHLHNCQUFzQixHQUFHLG9CQUFvQjtBQUMvUCxnQkFBZ0IsbUJBQU8sQ0FBQywwR0FBbUI7QUFDM0MsY0FBYyxtQkFBTyxDQUFDLDBHQUFtQjtBQUN6QyxnREFBK0MsRUFBRSxxQ0FBcUMsZ0NBQWdDLEVBQUM7QUFDdkg7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBLG9GQUFvRjtBQUNwRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0EsMEJBQTBCLFlBQVk7QUFDdEMseURBQXlELGlCQUFpQixHQUFHLG1CQUFtQixNQUFNLHFCQUFxQixJQUFJLG1CQUFtQjtBQUNsSixXQUFXLFNBQVM7QUFDcEIsV0FBVyxTQUFTO0FBQ3BCLFdBQVcsU0FBUztBQUNwQixXQUFXLFNBQVM7QUFDcEIsZUFBZSxZQUFZO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2REFBNkQscURBQXFELElBQUk7QUFDdEg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0EsaURBQWlELGVBQWUsZUFBZTtBQUMvRSxxQkFBcUIsU0FBUyxxQkFBcUI7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUscUJBQXFCLEVBQUUsZUFBZTtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QztBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQztBQUNuQztBQUNBO0FBQ0EsK0NBQStDO0FBQy9DO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCxJQUFJLEdBQUc7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0RkFBNEYsSUFBSSxHQUFHLEtBQUs7QUFDeEc7QUFDQTtBQUNBLG9JQUFvSSxPQUFPO0FBQzNJO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL2dyb3Etc2RrQDAuNS4wL25vZGVfbW9kdWxlcy9ncm9xLXNkay91cGxvYWRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5jcmVhdGVGb3JtID0gZXhwb3J0cy5tdWx0aXBhcnRGb3JtUmVxdWVzdE9wdGlvbnMgPSBleHBvcnRzLm1heWJlTXVsdGlwYXJ0Rm9ybVJlcXVlc3RPcHRpb25zID0gZXhwb3J0cy5pc011bHRpcGFydEJvZHkgPSBleHBvcnRzLnRvRmlsZSA9IGV4cG9ydHMuaXNVcGxvYWRhYmxlID0gZXhwb3J0cy5pc0Jsb2JMaWtlID0gZXhwb3J0cy5pc0ZpbGVMaWtlID0gZXhwb3J0cy5pc1Jlc3BvbnNlTGlrZSA9IGV4cG9ydHMuZmlsZUZyb21QYXRoID0gdm9pZCAwO1xuY29uc3QgaW5kZXhfMSA9IHJlcXVpcmUoXCIuL19zaGltcy9pbmRleC5qc1wiKTtcbnZhciBpbmRleF8yID0gcmVxdWlyZShcIi4vX3NoaW1zL2luZGV4LmpzXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiZmlsZUZyb21QYXRoXCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiBpbmRleF8yLmZpbGVGcm9tUGF0aDsgfSB9KTtcbmNvbnN0IGlzUmVzcG9uc2VMaWtlID0gKHZhbHVlKSA9PiB2YWx1ZSAhPSBudWxsICYmXG4gICAgdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJlxuICAgIHR5cGVvZiB2YWx1ZS51cmwgPT09ICdzdHJpbmcnICYmXG4gICAgdHlwZW9mIHZhbHVlLmJsb2IgPT09ICdmdW5jdGlvbic7XG5leHBvcnRzLmlzUmVzcG9uc2VMaWtlID0gaXNSZXNwb25zZUxpa2U7XG5jb25zdCBpc0ZpbGVMaWtlID0gKHZhbHVlKSA9PiB2YWx1ZSAhPSBudWxsICYmXG4gICAgdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJlxuICAgIHR5cGVvZiB2YWx1ZS5uYW1lID09PSAnc3RyaW5nJyAmJlxuICAgIHR5cGVvZiB2YWx1ZS5sYXN0TW9kaWZpZWQgPT09ICdudW1iZXInICYmXG4gICAgKDAsIGV4cG9ydHMuaXNCbG9iTGlrZSkodmFsdWUpO1xuZXhwb3J0cy5pc0ZpbGVMaWtlID0gaXNGaWxlTGlrZTtcbi8qKlxuICogVGhlIEJsb2JMaWtlIHR5cGUgb21pdHMgYXJyYXlCdWZmZXIoKSBiZWNhdXNlIEB0eXBlcy9ub2RlLWZldGNoQF4yLjYuNCBsYWNrcyBpdDsgYnV0IHRoaXMgY2hlY2tcbiAqIGFkZHMgdGhlIGFycmF5QnVmZmVyKCkgbWV0aG9kIHR5cGUgYmVjYXVzZSBpdCBpcyBhdmFpbGFibGUgYW5kIHVzZWQgYXQgcnVudGltZVxuICovXG5jb25zdCBpc0Jsb2JMaWtlID0gKHZhbHVlKSA9PiB2YWx1ZSAhPSBudWxsICYmXG4gICAgdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJlxuICAgIHR5cGVvZiB2YWx1ZS5zaXplID09PSAnbnVtYmVyJyAmJlxuICAgIHR5cGVvZiB2YWx1ZS50eXBlID09PSAnc3RyaW5nJyAmJlxuICAgIHR5cGVvZiB2YWx1ZS50ZXh0ID09PSAnZnVuY3Rpb24nICYmXG4gICAgdHlwZW9mIHZhbHVlLnNsaWNlID09PSAnZnVuY3Rpb24nICYmXG4gICAgdHlwZW9mIHZhbHVlLmFycmF5QnVmZmVyID09PSAnZnVuY3Rpb24nO1xuZXhwb3J0cy5pc0Jsb2JMaWtlID0gaXNCbG9iTGlrZTtcbmNvbnN0IGlzVXBsb2FkYWJsZSA9ICh2YWx1ZSkgPT4ge1xuICAgIHJldHVybiAoMCwgZXhwb3J0cy5pc0ZpbGVMaWtlKSh2YWx1ZSkgfHwgKDAsIGV4cG9ydHMuaXNSZXNwb25zZUxpa2UpKHZhbHVlKSB8fCAoMCwgaW5kZXhfMS5pc0ZzUmVhZFN0cmVhbSkodmFsdWUpO1xufTtcbmV4cG9ydHMuaXNVcGxvYWRhYmxlID0gaXNVcGxvYWRhYmxlO1xuLyoqXG4gKiBIZWxwZXIgZm9yIGNyZWF0aW5nIGEge0BsaW5rIEZpbGV9IHRvIHBhc3MgdG8gYW4gU0RLIHVwbG9hZCBtZXRob2QgZnJvbSBhIHZhcmlldHkgb2YgZGlmZmVyZW50IGRhdGEgZm9ybWF0c1xuICogQHBhcmFtIHZhbHVlIHRoZSByYXcgY29udGVudCBvZiB0aGUgZmlsZS4gIENhbiBiZSBhbiB7QGxpbmsgVXBsb2FkYWJsZX0sIHtAbGluayBCbG9iTGlrZVBhcnR9LCBvciB7QGxpbmsgQXN5bmNJdGVyYWJsZX0gb2Yge0BsaW5rIEJsb2JMaWtlUGFydH1zXG4gKiBAcGFyYW0ge3N0cmluZz19IG5hbWUgdGhlIG5hbWUgb2YgdGhlIGZpbGUuIElmIG9taXR0ZWQsIHRvRmlsZSB3aWxsIHRyeSB0byBkZXRlcm1pbmUgYSBmaWxlIG5hbWUgZnJvbSBiaXRzIGlmIHBvc3NpYmxlXG4gKiBAcGFyYW0ge09iamVjdD19IG9wdGlvbnMgYWRkaXRpb25hbCBwcm9wZXJ0aWVzXG4gKiBAcGFyYW0ge3N0cmluZz19IG9wdGlvbnMudHlwZSB0aGUgTUlNRSB0eXBlIG9mIHRoZSBjb250ZW50XG4gKiBAcGFyYW0ge251bWJlcj19IG9wdGlvbnMubGFzdE1vZGlmaWVkIHRoZSBsYXN0IG1vZGlmaWVkIHRpbWVzdGFtcFxuICogQHJldHVybnMgYSB7QGxpbmsgRmlsZX0gd2l0aCB0aGUgZ2l2ZW4gcHJvcGVydGllc1xuICovXG5hc3luYyBmdW5jdGlvbiB0b0ZpbGUodmFsdWUsIG5hbWUsIG9wdGlvbnMpIHtcbiAgICAvLyBJZiBpdCdzIGEgcHJvbWlzZSwgcmVzb2x2ZSBpdC5cbiAgICB2YWx1ZSA9IGF3YWl0IHZhbHVlO1xuICAgIC8vIFVzZSB0aGUgZmlsZSdzIG9wdGlvbnMgaWYgdGhlcmUgaXNuJ3Qgb25lIHByb3ZpZGVkXG4gICAgb3B0aW9ucyA/PyAob3B0aW9ucyA9ICgwLCBleHBvcnRzLmlzRmlsZUxpa2UpKHZhbHVlKSA/IHsgbGFzdE1vZGlmaWVkOiB2YWx1ZS5sYXN0TW9kaWZpZWQsIHR5cGU6IHZhbHVlLnR5cGUgfSA6IHt9KTtcbiAgICBpZiAoKDAsIGV4cG9ydHMuaXNSZXNwb25zZUxpa2UpKHZhbHVlKSkge1xuICAgICAgICBjb25zdCBibG9iID0gYXdhaXQgdmFsdWUuYmxvYigpO1xuICAgICAgICBuYW1lIHx8IChuYW1lID0gbmV3IFVSTCh2YWx1ZS51cmwpLnBhdGhuYW1lLnNwbGl0KC9bXFxcXC9dLykucG9wKCkgPz8gJ3Vua25vd25fZmlsZScpO1xuICAgICAgICByZXR1cm4gbmV3IGluZGV4XzEuRmlsZShbYmxvYl0sIG5hbWUsIG9wdGlvbnMpO1xuICAgIH1cbiAgICBjb25zdCBiaXRzID0gYXdhaXQgZ2V0Qnl0ZXModmFsdWUpO1xuICAgIG5hbWUgfHwgKG5hbWUgPSBnZXROYW1lKHZhbHVlKSA/PyAndW5rbm93bl9maWxlJyk7XG4gICAgaWYgKCFvcHRpb25zLnR5cGUpIHtcbiAgICAgICAgY29uc3QgdHlwZSA9IGJpdHNbMF0/LnR5cGU7XG4gICAgICAgIGlmICh0eXBlb2YgdHlwZSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgIG9wdGlvbnMgPSB7IC4uLm9wdGlvbnMsIHR5cGUgfTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gbmV3IGluZGV4XzEuRmlsZShiaXRzLCBuYW1lLCBvcHRpb25zKTtcbn1cbmV4cG9ydHMudG9GaWxlID0gdG9GaWxlO1xuYXN5bmMgZnVuY3Rpb24gZ2V0Qnl0ZXModmFsdWUpIHtcbiAgICBsZXQgcGFydHMgPSBbXTtcbiAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyB8fFxuICAgICAgICBBcnJheUJ1ZmZlci5pc1ZpZXcodmFsdWUpIHx8IC8vIGluY2x1ZGVzIFVpbnQ4QXJyYXksIEJ1ZmZlciwgZXRjLlxuICAgICAgICB2YWx1ZSBpbnN0YW5jZW9mIEFycmF5QnVmZmVyKSB7XG4gICAgICAgIHBhcnRzLnB1c2godmFsdWUpO1xuICAgIH1cbiAgICBlbHNlIGlmICgoMCwgZXhwb3J0cy5pc0Jsb2JMaWtlKSh2YWx1ZSkpIHtcbiAgICAgICAgcGFydHMucHVzaChhd2FpdCB2YWx1ZS5hcnJheUJ1ZmZlcigpKTtcbiAgICB9XG4gICAgZWxzZSBpZiAoaXNBc3luY0l0ZXJhYmxlSXRlcmF0b3IodmFsdWUpIC8vIGluY2x1ZGVzIFJlYWRhYmxlLCBSZWFkYWJsZVN0cmVhbSwgZXRjLlxuICAgICkge1xuICAgICAgICBmb3IgYXdhaXQgKGNvbnN0IGNodW5rIG9mIHZhbHVlKSB7XG4gICAgICAgICAgICBwYXJ0cy5wdXNoKGNodW5rKTsgLy8gVE9ETywgY29uc2lkZXIgdmFsaWRhdGluZz9cbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBVbmV4cGVjdGVkIGRhdGEgdHlwZTogJHt0eXBlb2YgdmFsdWV9OyBjb25zdHJ1Y3RvcjogJHt2YWx1ZT8uY29uc3RydWN0b3JcbiAgICAgICAgICAgID8ubmFtZX07IHByb3BzOiAke3Byb3BzRm9yRXJyb3IodmFsdWUpfWApO1xuICAgIH1cbiAgICByZXR1cm4gcGFydHM7XG59XG5mdW5jdGlvbiBwcm9wc0ZvckVycm9yKHZhbHVlKSB7XG4gICAgY29uc3QgcHJvcHMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlOYW1lcyh2YWx1ZSk7XG4gICAgcmV0dXJuIGBbJHtwcm9wcy5tYXAoKHApID0+IGBcIiR7cH1cImApLmpvaW4oJywgJyl9XWA7XG59XG5mdW5jdGlvbiBnZXROYW1lKHZhbHVlKSB7XG4gICAgcmV0dXJuIChnZXRTdHJpbmdGcm9tTWF5YmVCdWZmZXIodmFsdWUubmFtZSkgfHxcbiAgICAgICAgZ2V0U3RyaW5nRnJvbU1heWJlQnVmZmVyKHZhbHVlLmZpbGVuYW1lKSB8fFxuICAgICAgICAvLyBGb3IgZnMuUmVhZFN0cmVhbVxuICAgICAgICBnZXRTdHJpbmdGcm9tTWF5YmVCdWZmZXIodmFsdWUucGF0aCk/LnNwbGl0KC9bXFxcXC9dLykucG9wKCkpO1xufVxuY29uc3QgZ2V0U3RyaW5nRnJvbU1heWJlQnVmZmVyID0gKHgpID0+IHtcbiAgICBpZiAodHlwZW9mIHggPT09ICdzdHJpbmcnKVxuICAgICAgICByZXR1cm4geDtcbiAgICBpZiAodHlwZW9mIEJ1ZmZlciAhPT0gJ3VuZGVmaW5lZCcgJiYgeCBpbnN0YW5jZW9mIEJ1ZmZlcilcbiAgICAgICAgcmV0dXJuIFN0cmluZyh4KTtcbiAgICByZXR1cm4gdW5kZWZpbmVkO1xufTtcbmNvbnN0IGlzQXN5bmNJdGVyYWJsZUl0ZXJhdG9yID0gKHZhbHVlKSA9PiB2YWx1ZSAhPSBudWxsICYmIHR5cGVvZiB2YWx1ZSA9PT0gJ29iamVjdCcgJiYgdHlwZW9mIHZhbHVlW1N5bWJvbC5hc3luY0l0ZXJhdG9yXSA9PT0gJ2Z1bmN0aW9uJztcbmNvbnN0IGlzTXVsdGlwYXJ0Qm9keSA9IChib2R5KSA9PiBib2R5ICYmIHR5cGVvZiBib2R5ID09PSAnb2JqZWN0JyAmJiBib2R5LmJvZHkgJiYgYm9keVtTeW1ib2wudG9TdHJpbmdUYWddID09PSAnTXVsdGlwYXJ0Qm9keSc7XG5leHBvcnRzLmlzTXVsdGlwYXJ0Qm9keSA9IGlzTXVsdGlwYXJ0Qm9keTtcbi8qKlxuICogUmV0dXJucyBhIG11bHRpcGFydC9mb3JtLWRhdGEgcmVxdWVzdCBpZiBhbnkgcGFydCBvZiB0aGUgZ2l2ZW4gcmVxdWVzdCBib2R5IGNvbnRhaW5zIGEgRmlsZSAvIEJsb2IgdmFsdWUuXG4gKiBPdGhlcndpc2UgcmV0dXJucyB0aGUgcmVxdWVzdCBhcyBpcy5cbiAqL1xuY29uc3QgbWF5YmVNdWx0aXBhcnRGb3JtUmVxdWVzdE9wdGlvbnMgPSBhc3luYyAob3B0cykgPT4ge1xuICAgIGlmICghaGFzVXBsb2FkYWJsZVZhbHVlKG9wdHMuYm9keSkpXG4gICAgICAgIHJldHVybiBvcHRzO1xuICAgIGNvbnN0IGZvcm0gPSBhd2FpdCAoMCwgZXhwb3J0cy5jcmVhdGVGb3JtKShvcHRzLmJvZHkpO1xuICAgIHJldHVybiAoMCwgaW5kZXhfMS5nZXRNdWx0aXBhcnRSZXF1ZXN0T3B0aW9ucykoZm9ybSwgb3B0cyk7XG59O1xuZXhwb3J0cy5tYXliZU11bHRpcGFydEZvcm1SZXF1ZXN0T3B0aW9ucyA9IG1heWJlTXVsdGlwYXJ0Rm9ybVJlcXVlc3RPcHRpb25zO1xuY29uc3QgbXVsdGlwYXJ0Rm9ybVJlcXVlc3RPcHRpb25zID0gYXN5bmMgKG9wdHMpID0+IHtcbiAgICBjb25zdCBmb3JtID0gYXdhaXQgKDAsIGV4cG9ydHMuY3JlYXRlRm9ybSkob3B0cy5ib2R5KTtcbiAgICByZXR1cm4gKDAsIGluZGV4XzEuZ2V0TXVsdGlwYXJ0UmVxdWVzdE9wdGlvbnMpKGZvcm0sIG9wdHMpO1xufTtcbmV4cG9ydHMubXVsdGlwYXJ0Rm9ybVJlcXVlc3RPcHRpb25zID0gbXVsdGlwYXJ0Rm9ybVJlcXVlc3RPcHRpb25zO1xuY29uc3QgY3JlYXRlRm9ybSA9IGFzeW5jIChib2R5KSA9PiB7XG4gICAgY29uc3QgZm9ybSA9IG5ldyBpbmRleF8xLkZvcm1EYXRhKCk7XG4gICAgYXdhaXQgUHJvbWlzZS5hbGwoT2JqZWN0LmVudHJpZXMoYm9keSB8fCB7fSkubWFwKChba2V5LCB2YWx1ZV0pID0+IGFkZEZvcm1WYWx1ZShmb3JtLCBrZXksIHZhbHVlKSkpO1xuICAgIHJldHVybiBmb3JtO1xufTtcbmV4cG9ydHMuY3JlYXRlRm9ybSA9IGNyZWF0ZUZvcm07XG5jb25zdCBoYXNVcGxvYWRhYmxlVmFsdWUgPSAodmFsdWUpID0+IHtcbiAgICBpZiAoKDAsIGV4cG9ydHMuaXNVcGxvYWRhYmxlKSh2YWx1ZSkpXG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSlcbiAgICAgICAgcmV0dXJuIHZhbHVlLnNvbWUoaGFzVXBsb2FkYWJsZVZhbHVlKTtcbiAgICBpZiAodmFsdWUgJiYgdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0Jykge1xuICAgICAgICBmb3IgKGNvbnN0IGsgaW4gdmFsdWUpIHtcbiAgICAgICAgICAgIGlmIChoYXNVcGxvYWRhYmxlVmFsdWUodmFsdWVba10pKVxuICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbn07XG5jb25zdCBhZGRGb3JtVmFsdWUgPSBhc3luYyAoZm9ybSwga2V5LCB2YWx1ZSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkKVxuICAgICAgICByZXR1cm47XG4gICAgaWYgKHZhbHVlID09IG51bGwpIHtcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihgUmVjZWl2ZWQgbnVsbCBmb3IgXCIke2tleX1cIjsgdG8gcGFzcyBudWxsIGluIEZvcm1EYXRhLCB5b3UgbXVzdCB1c2UgdGhlIHN0cmluZyAnbnVsbCdgKTtcbiAgICB9XG4gICAgLy8gVE9ETzogbWFrZSBuZXN0ZWQgZm9ybWF0cyBjb25maWd1cmFibGVcbiAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyB8fCB0eXBlb2YgdmFsdWUgPT09ICdudW1iZXInIHx8IHR5cGVvZiB2YWx1ZSA9PT0gJ2Jvb2xlYW4nKSB7XG4gICAgICAgIGZvcm0uYXBwZW5kKGtleSwgU3RyaW5nKHZhbHVlKSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKCgwLCBleHBvcnRzLmlzVXBsb2FkYWJsZSkodmFsdWUpKSB7XG4gICAgICAgIGNvbnN0IGZpbGUgPSBhd2FpdCB0b0ZpbGUodmFsdWUpO1xuICAgICAgICBmb3JtLmFwcGVuZChrZXksIGZpbGUpO1xuICAgIH1cbiAgICBlbHNlIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSkge1xuICAgICAgICBhd2FpdCBQcm9taXNlLmFsbCh2YWx1ZS5tYXAoKGVudHJ5KSA9PiBhZGRGb3JtVmFsdWUoZm9ybSwga2V5ICsgJ1tdJywgZW50cnkpKSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoT2JqZWN0LmVudHJpZXModmFsdWUpLm1hcCgoW25hbWUsIHByb3BdKSA9PiBhZGRGb3JtVmFsdWUoZm9ybSwgYCR7a2V5fVske25hbWV9XWAsIHByb3ApKSk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKGBJbnZhbGlkIHZhbHVlIGdpdmVuIHRvIGZvcm0sIGV4cGVjdGVkIGEgc3RyaW5nLCBudW1iZXIsIGJvb2xlYW4sIG9iamVjdCwgQXJyYXksIEZpbGUgb3IgQmxvYiBidXQgZ290ICR7dmFsdWV9IGluc3RlYWRgKTtcbiAgICB9XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXBsb2Fkcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/uploads.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/version.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/version.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.VERSION = void 0;\nexports.VERSION = '0.5.0'; // x-release-please-version\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JvcS1zZGtAMC41LjAvbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZUFBZTtBQUNmLGVBQWUsWUFBWTtBQUMzQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL2dyb3Etc2RrQDAuNS4wL25vZGVfbW9kdWxlcy9ncm9xLXNkay92ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5WRVJTSU9OID0gdm9pZCAwO1xuZXhwb3J0cy5WRVJTSU9OID0gJzAuNS4wJzsgLy8geC1yZWxlYXNlLXBsZWFzZS12ZXJzaW9uXG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/groq-sdk@0.5.0/node_modules/groq-sdk/version.js\n");

/***/ })

};
;