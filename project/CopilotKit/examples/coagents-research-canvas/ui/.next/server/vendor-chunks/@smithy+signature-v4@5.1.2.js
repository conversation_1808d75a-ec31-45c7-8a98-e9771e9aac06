"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+signature-v4@5.1.2";
exports.ids = ["vendor-chunks/@smithy+signature-v4@5.1.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/HeaderFormatter.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/HeaderFormatter.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeaderFormatter: () => (/* binding */ HeaderFormatter),\n/* harmony export */   Int64: () => (/* binding */ Int64)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-hex-encoding */ \"(rsc)/./node_modules/.pnpm/@smithy+util-hex-encoding@4.0.0/node_modules/@smithy/util-hex-encoding/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n\n\nclass HeaderFormatter {\n    format(headers) {\n        const chunks = [];\n        for (const headerName of Object.keys(headers)) {\n            const bytes = (0,_smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__.fromUtf8)(headerName);\n            chunks.push(Uint8Array.from([bytes.byteLength]), bytes, this.formatHeaderValue(headers[headerName]));\n        }\n        const out = new Uint8Array(chunks.reduce((carry, bytes) => carry + bytes.byteLength, 0));\n        let position = 0;\n        for (const chunk of chunks) {\n            out.set(chunk, position);\n            position += chunk.byteLength;\n        }\n        return out;\n    }\n    formatHeaderValue(header) {\n        switch (header.type) {\n            case \"boolean\":\n                return Uint8Array.from([header.value ? 0 : 1]);\n            case \"byte\":\n                return Uint8Array.from([2, header.value]);\n            case \"short\":\n                const shortView = new DataView(new ArrayBuffer(3));\n                shortView.setUint8(0, 3);\n                shortView.setInt16(1, header.value, false);\n                return new Uint8Array(shortView.buffer);\n            case \"integer\":\n                const intView = new DataView(new ArrayBuffer(5));\n                intView.setUint8(0, 4);\n                intView.setInt32(1, header.value, false);\n                return new Uint8Array(intView.buffer);\n            case \"long\":\n                const longBytes = new Uint8Array(9);\n                longBytes[0] = 5;\n                longBytes.set(header.value.bytes, 1);\n                return longBytes;\n            case \"binary\":\n                const binView = new DataView(new ArrayBuffer(3 + header.value.byteLength));\n                binView.setUint8(0, 6);\n                binView.setUint16(1, header.value.byteLength, false);\n                const binBytes = new Uint8Array(binView.buffer);\n                binBytes.set(header.value, 3);\n                return binBytes;\n            case \"string\":\n                const utf8Bytes = (0,_smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__.fromUtf8)(header.value);\n                const strView = new DataView(new ArrayBuffer(3 + utf8Bytes.byteLength));\n                strView.setUint8(0, 7);\n                strView.setUint16(1, utf8Bytes.byteLength, false);\n                const strBytes = new Uint8Array(strView.buffer);\n                strBytes.set(utf8Bytes, 3);\n                return strBytes;\n            case \"timestamp\":\n                const tsBytes = new Uint8Array(9);\n                tsBytes[0] = 8;\n                tsBytes.set(Int64.fromNumber(header.value.valueOf()).bytes, 1);\n                return tsBytes;\n            case \"uuid\":\n                if (!UUID_PATTERN.test(header.value)) {\n                    throw new Error(`Invalid UUID received: ${header.value}`);\n                }\n                const uuidBytes = new Uint8Array(17);\n                uuidBytes[0] = 9;\n                uuidBytes.set((0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.fromHex)(header.value.replace(/\\-/g, \"\")), 1);\n                return uuidBytes;\n        }\n    }\n}\nvar HEADER_VALUE_TYPE;\n(function (HEADER_VALUE_TYPE) {\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"boolTrue\"] = 0] = \"boolTrue\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"boolFalse\"] = 1] = \"boolFalse\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"byte\"] = 2] = \"byte\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"short\"] = 3] = \"short\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"integer\"] = 4] = \"integer\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"long\"] = 5] = \"long\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"byteArray\"] = 6] = \"byteArray\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"string\"] = 7] = \"string\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"timestamp\"] = 8] = \"timestamp\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"uuid\"] = 9] = \"uuid\";\n})(HEADER_VALUE_TYPE || (HEADER_VALUE_TYPE = {}));\nconst UUID_PATTERN = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/;\nclass Int64 {\n    constructor(bytes) {\n        this.bytes = bytes;\n        if (bytes.byteLength !== 8) {\n            throw new Error(\"Int64 buffers must be exactly 8 bytes\");\n        }\n    }\n    static fromNumber(number) {\n        if (number > 9223372036854776000 || number < -9223372036854776000) {\n            throw new Error(`${number} is too large (or, if negative, too small) to represent as an Int64`);\n        }\n        const bytes = new Uint8Array(8);\n        for (let i = 7, remaining = Math.abs(Math.round(number)); i > -1 && remaining > 0; i--, remaining /= 256) {\n            bytes[i] = remaining;\n        }\n        if (number < 0) {\n            negate(bytes);\n        }\n        return new Int64(bytes);\n    }\n    valueOf() {\n        const bytes = this.bytes.slice(0);\n        const negative = bytes[0] & 0b10000000;\n        if (negative) {\n            negate(bytes);\n        }\n        return parseInt((0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(bytes), 16) * (negative ? -1 : 1);\n    }\n    toString() {\n        return String(this.valueOf());\n    }\n}\nfunction negate(bytes) {\n    for (let i = 0; i < 8; i++) {\n        bytes[i] ^= 0xff;\n    }\n    for (let i = 7; i > -1; i--) {\n        bytes[i]++;\n        if (bytes[i] !== 0)\n            break;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/HeaderFormatter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/SignatureV4.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/SignatureV4.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignatureV4: () => (/* binding */ SignatureV4)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-hex-encoding */ \"(rsc)/./node_modules/.pnpm/@smithy+util-hex-encoding@4.0.0/node_modules/@smithy/util-hex-encoding/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/constants.js\");\n/* harmony import */ var _credentialDerivation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./credentialDerivation */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/credentialDerivation.js\");\n/* harmony import */ var _getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getCanonicalHeaders */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getCanonicalHeaders.js\");\n/* harmony import */ var _getPayloadHash__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./getPayloadHash */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getPayloadHash.js\");\n/* harmony import */ var _HeaderFormatter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./HeaderFormatter */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/HeaderFormatter.js\");\n/* harmony import */ var _headerUtil__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./headerUtil */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/headerUtil.js\");\n/* harmony import */ var _moveHeadersToQuery__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./moveHeadersToQuery */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/moveHeadersToQuery.js\");\n/* harmony import */ var _prepareRequest__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./prepareRequest */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/prepareRequest.js\");\n/* harmony import */ var _SignatureV4Base__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./SignatureV4Base */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/SignatureV4Base.js\");\n\n\n\n\n\n\n\n\n\n\n\nclass SignatureV4 extends _SignatureV4Base__WEBPACK_IMPORTED_MODULE_10__.SignatureV4Base {\n    constructor({ applyChecksum, credentials, region, service, sha256, uriEscapePath = true, }) {\n        super({\n            applyChecksum,\n            credentials,\n            region,\n            service,\n            sha256,\n            uriEscapePath,\n        });\n        this.headerFormatter = new _HeaderFormatter__WEBPACK_IMPORTED_MODULE_6__.HeaderFormatter();\n    }\n    async presign(originalRequest, options = {}) {\n        const { signingDate = new Date(), expiresIn = 3600, unsignableHeaders, unhoistableHeaders, signableHeaders, hoistableHeaders, signingRegion, signingService, } = options;\n        const credentials = await this.credentialProvider();\n        this.validateResolvedCredentials(credentials);\n        const region = signingRegion ?? (await this.regionProvider());\n        const { longDate, shortDate } = this.formatDate(signingDate);\n        if (expiresIn > _constants__WEBPACK_IMPORTED_MODULE_2__.MAX_PRESIGNED_TTL) {\n            return Promise.reject(\"Signature version 4 presigned URLs\" + \" must have an expiration date less than one week in\" + \" the future\");\n        }\n        const scope = (0,_credentialDerivation__WEBPACK_IMPORTED_MODULE_3__.createScope)(shortDate, region, signingService ?? this.service);\n        const request = (0,_moveHeadersToQuery__WEBPACK_IMPORTED_MODULE_8__.moveHeadersToQuery)((0,_prepareRequest__WEBPACK_IMPORTED_MODULE_9__.prepareRequest)(originalRequest), { unhoistableHeaders, hoistableHeaders });\n        if (credentials.sessionToken) {\n            request.query[_constants__WEBPACK_IMPORTED_MODULE_2__.TOKEN_QUERY_PARAM] = credentials.sessionToken;\n        }\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_2__.ALGORITHM_QUERY_PARAM] = _constants__WEBPACK_IMPORTED_MODULE_2__.ALGORITHM_IDENTIFIER;\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_2__.CREDENTIAL_QUERY_PARAM] = `${credentials.accessKeyId}/${scope}`;\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_2__.AMZ_DATE_QUERY_PARAM] = longDate;\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_2__.EXPIRES_QUERY_PARAM] = expiresIn.toString(10);\n        const canonicalHeaders = (0,_getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_4__.getCanonicalHeaders)(request, unsignableHeaders, signableHeaders);\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_2__.SIGNED_HEADERS_QUERY_PARAM] = this.getCanonicalHeaderList(canonicalHeaders);\n        request.query[_constants__WEBPACK_IMPORTED_MODULE_2__.SIGNATURE_QUERY_PARAM] = await this.getSignature(longDate, scope, this.getSigningKey(credentials, region, shortDate, signingService), this.createCanonicalRequest(request, canonicalHeaders, await (0,_getPayloadHash__WEBPACK_IMPORTED_MODULE_5__.getPayloadHash)(originalRequest, this.sha256)));\n        return request;\n    }\n    async sign(toSign, options) {\n        if (typeof toSign === \"string\") {\n            return this.signString(toSign, options);\n        }\n        else if (toSign.headers && toSign.payload) {\n            return this.signEvent(toSign, options);\n        }\n        else if (toSign.message) {\n            return this.signMessage(toSign, options);\n        }\n        else {\n            return this.signRequest(toSign, options);\n        }\n    }\n    async signEvent({ headers, payload }, { signingDate = new Date(), priorSignature, signingRegion, signingService }) {\n        const region = signingRegion ?? (await this.regionProvider());\n        const { shortDate, longDate } = this.formatDate(signingDate);\n        const scope = (0,_credentialDerivation__WEBPACK_IMPORTED_MODULE_3__.createScope)(shortDate, region, signingService ?? this.service);\n        const hashedPayload = await (0,_getPayloadHash__WEBPACK_IMPORTED_MODULE_5__.getPayloadHash)({ headers: {}, body: payload }, this.sha256);\n        const hash = new this.sha256();\n        hash.update(headers);\n        const hashedHeaders = (0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(await hash.digest());\n        const stringToSign = [\n            _constants__WEBPACK_IMPORTED_MODULE_2__.EVENT_ALGORITHM_IDENTIFIER,\n            longDate,\n            scope,\n            priorSignature,\n            hashedHeaders,\n            hashedPayload,\n        ].join(\"\\n\");\n        return this.signString(stringToSign, { signingDate, signingRegion: region, signingService });\n    }\n    async signMessage(signableMessage, { signingDate = new Date(), signingRegion, signingService }) {\n        const promise = this.signEvent({\n            headers: this.headerFormatter.format(signableMessage.message.headers),\n            payload: signableMessage.message.body,\n        }, {\n            signingDate,\n            signingRegion,\n            signingService,\n            priorSignature: signableMessage.priorSignature,\n        });\n        return promise.then((signature) => {\n            return { message: signableMessage.message, signature };\n        });\n    }\n    async signString(stringToSign, { signingDate = new Date(), signingRegion, signingService } = {}) {\n        const credentials = await this.credentialProvider();\n        this.validateResolvedCredentials(credentials);\n        const region = signingRegion ?? (await this.regionProvider());\n        const { shortDate } = this.formatDate(signingDate);\n        const hash = new this.sha256(await this.getSigningKey(credentials, region, shortDate, signingService));\n        hash.update((0,_smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__.toUint8Array)(stringToSign));\n        return (0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(await hash.digest());\n    }\n    async signRequest(requestToSign, { signingDate = new Date(), signableHeaders, unsignableHeaders, signingRegion, signingService, } = {}) {\n        const credentials = await this.credentialProvider();\n        this.validateResolvedCredentials(credentials);\n        const region = signingRegion ?? (await this.regionProvider());\n        const request = (0,_prepareRequest__WEBPACK_IMPORTED_MODULE_9__.prepareRequest)(requestToSign);\n        const { longDate, shortDate } = this.formatDate(signingDate);\n        const scope = (0,_credentialDerivation__WEBPACK_IMPORTED_MODULE_3__.createScope)(shortDate, region, signingService ?? this.service);\n        request.headers[_constants__WEBPACK_IMPORTED_MODULE_2__.AMZ_DATE_HEADER] = longDate;\n        if (credentials.sessionToken) {\n            request.headers[_constants__WEBPACK_IMPORTED_MODULE_2__.TOKEN_HEADER] = credentials.sessionToken;\n        }\n        const payloadHash = await (0,_getPayloadHash__WEBPACK_IMPORTED_MODULE_5__.getPayloadHash)(request, this.sha256);\n        if (!(0,_headerUtil__WEBPACK_IMPORTED_MODULE_7__.hasHeader)(_constants__WEBPACK_IMPORTED_MODULE_2__.SHA256_HEADER, request.headers) && this.applyChecksum) {\n            request.headers[_constants__WEBPACK_IMPORTED_MODULE_2__.SHA256_HEADER] = payloadHash;\n        }\n        const canonicalHeaders = (0,_getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_4__.getCanonicalHeaders)(request, unsignableHeaders, signableHeaders);\n        const signature = await this.getSignature(longDate, scope, this.getSigningKey(credentials, region, shortDate, signingService), this.createCanonicalRequest(request, canonicalHeaders, payloadHash));\n        request.headers[_constants__WEBPACK_IMPORTED_MODULE_2__.AUTH_HEADER] =\n            `${_constants__WEBPACK_IMPORTED_MODULE_2__.ALGORITHM_IDENTIFIER} ` +\n                `Credential=${credentials.accessKeyId}/${scope}, ` +\n                `SignedHeaders=${this.getCanonicalHeaderList(canonicalHeaders)}, ` +\n                `Signature=${signature}`;\n        return request;\n    }\n    async getSignature(longDate, credentialScope, keyPromise, canonicalRequest) {\n        const stringToSign = await this.createStringToSign(longDate, credentialScope, canonicalRequest, _constants__WEBPACK_IMPORTED_MODULE_2__.ALGORITHM_IDENTIFIER);\n        const hash = new this.sha256(await keyPromise);\n        hash.update((0,_smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__.toUint8Array)(stringToSign));\n        return (0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(await hash.digest());\n    }\n    getSigningKey(credentials, region, shortDate, service) {\n        return (0,_credentialDerivation__WEBPACK_IMPORTED_MODULE_3__.getSigningKey)(this.sha256, credentials, shortDate, region, service || this.service);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/SignatureV4.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/SignatureV4Base.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/SignatureV4Base.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignatureV4Base: () => (/* binding */ SignatureV4Base)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-hex-encoding */ \"(rsc)/./node_modules/.pnpm/@smithy+util-hex-encoding@4.0.0/node_modules/@smithy/util-hex-encoding/dist-es/index.js\");\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n/* harmony import */ var _smithy_util_uri_escape__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/util-uri-escape */ \"(rsc)/./node_modules/.pnpm/@smithy+util-uri-escape@4.0.0/node_modules/@smithy/util-uri-escape/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n/* harmony import */ var _getCanonicalQuery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getCanonicalQuery */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getCanonicalQuery.js\");\n/* harmony import */ var _utilDate__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utilDate */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/utilDate.js\");\n\n\n\n\n\n\nclass SignatureV4Base {\n    constructor({ applyChecksum, credentials, region, service, sha256, uriEscapePath = true, }) {\n        this.service = service;\n        this.sha256 = sha256;\n        this.uriEscapePath = uriEscapePath;\n        this.applyChecksum = typeof applyChecksum === \"boolean\" ? applyChecksum : true;\n        this.regionProvider = (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_1__.normalizeProvider)(region);\n        this.credentialProvider = (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_1__.normalizeProvider)(credentials);\n    }\n    createCanonicalRequest(request, canonicalHeaders, payloadHash) {\n        const sortedHeaders = Object.keys(canonicalHeaders).sort();\n        return `${request.method}\n${this.getCanonicalPath(request)}\n${(0,_getCanonicalQuery__WEBPACK_IMPORTED_MODULE_4__.getCanonicalQuery)(request)}\n${sortedHeaders.map((name) => `${name}:${canonicalHeaders[name]}`).join(\"\\n\")}\n\n${sortedHeaders.join(\";\")}\n${payloadHash}`;\n    }\n    async createStringToSign(longDate, credentialScope, canonicalRequest, algorithmIdentifier) {\n        const hash = new this.sha256();\n        hash.update((0,_smithy_util_utf8__WEBPACK_IMPORTED_MODULE_3__.toUint8Array)(canonicalRequest));\n        const hashedRequest = await hash.digest();\n        return `${algorithmIdentifier}\n${longDate}\n${credentialScope}\n${(0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(hashedRequest)}`;\n    }\n    getCanonicalPath({ path }) {\n        if (this.uriEscapePath) {\n            const normalizedPathSegments = [];\n            for (const pathSegment of path.split(\"/\")) {\n                if (pathSegment?.length === 0)\n                    continue;\n                if (pathSegment === \".\")\n                    continue;\n                if (pathSegment === \"..\") {\n                    normalizedPathSegments.pop();\n                }\n                else {\n                    normalizedPathSegments.push(pathSegment);\n                }\n            }\n            const normalizedPath = `${path?.startsWith(\"/\") ? \"/\" : \"\"}${normalizedPathSegments.join(\"/\")}${normalizedPathSegments.length > 0 && path?.endsWith(\"/\") ? \"/\" : \"\"}`;\n            const doubleEncoded = (0,_smithy_util_uri_escape__WEBPACK_IMPORTED_MODULE_2__.escapeUri)(normalizedPath);\n            return doubleEncoded.replace(/%2F/g, \"/\");\n        }\n        return path;\n    }\n    validateResolvedCredentials(credentials) {\n        if (typeof credentials !== \"object\" ||\n            typeof credentials.accessKeyId !== \"string\" ||\n            typeof credentials.secretAccessKey !== \"string\") {\n            throw new Error(\"Resolved credential object is not valid\");\n        }\n    }\n    formatDate(now) {\n        const longDate = (0,_utilDate__WEBPACK_IMPORTED_MODULE_5__.iso8601)(now).replace(/[\\-:]/g, \"\");\n        return {\n            longDate,\n            shortDate: longDate.slice(0, 8),\n        };\n    }\n    getCanonicalHeaderList(headers) {\n        return Object.keys(headers).sort().join(\";\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/SignatureV4Base.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/constants.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/constants.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALGORITHM_IDENTIFIER: () => (/* binding */ ALGORITHM_IDENTIFIER),\n/* harmony export */   ALGORITHM_IDENTIFIER_V4A: () => (/* binding */ ALGORITHM_IDENTIFIER_V4A),\n/* harmony export */   ALGORITHM_QUERY_PARAM: () => (/* binding */ ALGORITHM_QUERY_PARAM),\n/* harmony export */   ALWAYS_UNSIGNABLE_HEADERS: () => (/* binding */ ALWAYS_UNSIGNABLE_HEADERS),\n/* harmony export */   AMZ_DATE_HEADER: () => (/* binding */ AMZ_DATE_HEADER),\n/* harmony export */   AMZ_DATE_QUERY_PARAM: () => (/* binding */ AMZ_DATE_QUERY_PARAM),\n/* harmony export */   AUTH_HEADER: () => (/* binding */ AUTH_HEADER),\n/* harmony export */   CREDENTIAL_QUERY_PARAM: () => (/* binding */ CREDENTIAL_QUERY_PARAM),\n/* harmony export */   DATE_HEADER: () => (/* binding */ DATE_HEADER),\n/* harmony export */   EVENT_ALGORITHM_IDENTIFIER: () => (/* binding */ EVENT_ALGORITHM_IDENTIFIER),\n/* harmony export */   EXPIRES_QUERY_PARAM: () => (/* binding */ EXPIRES_QUERY_PARAM),\n/* harmony export */   GENERATED_HEADERS: () => (/* binding */ GENERATED_HEADERS),\n/* harmony export */   HOST_HEADER: () => (/* binding */ HOST_HEADER),\n/* harmony export */   KEY_TYPE_IDENTIFIER: () => (/* binding */ KEY_TYPE_IDENTIFIER),\n/* harmony export */   MAX_CACHE_SIZE: () => (/* binding */ MAX_CACHE_SIZE),\n/* harmony export */   MAX_PRESIGNED_TTL: () => (/* binding */ MAX_PRESIGNED_TTL),\n/* harmony export */   PROXY_HEADER_PATTERN: () => (/* binding */ PROXY_HEADER_PATTERN),\n/* harmony export */   REGION_SET_PARAM: () => (/* binding */ REGION_SET_PARAM),\n/* harmony export */   SEC_HEADER_PATTERN: () => (/* binding */ SEC_HEADER_PATTERN),\n/* harmony export */   SHA256_HEADER: () => (/* binding */ SHA256_HEADER),\n/* harmony export */   SIGNATURE_HEADER: () => (/* binding */ SIGNATURE_HEADER),\n/* harmony export */   SIGNATURE_QUERY_PARAM: () => (/* binding */ SIGNATURE_QUERY_PARAM),\n/* harmony export */   SIGNED_HEADERS_QUERY_PARAM: () => (/* binding */ SIGNED_HEADERS_QUERY_PARAM),\n/* harmony export */   TOKEN_HEADER: () => (/* binding */ TOKEN_HEADER),\n/* harmony export */   TOKEN_QUERY_PARAM: () => (/* binding */ TOKEN_QUERY_PARAM),\n/* harmony export */   UNSIGNABLE_PATTERNS: () => (/* binding */ UNSIGNABLE_PATTERNS),\n/* harmony export */   UNSIGNED_PAYLOAD: () => (/* binding */ UNSIGNED_PAYLOAD)\n/* harmony export */ });\nconst ALGORITHM_QUERY_PARAM = \"X-Amz-Algorithm\";\nconst CREDENTIAL_QUERY_PARAM = \"X-Amz-Credential\";\nconst AMZ_DATE_QUERY_PARAM = \"X-Amz-Date\";\nconst SIGNED_HEADERS_QUERY_PARAM = \"X-Amz-SignedHeaders\";\nconst EXPIRES_QUERY_PARAM = \"X-Amz-Expires\";\nconst SIGNATURE_QUERY_PARAM = \"X-Amz-Signature\";\nconst TOKEN_QUERY_PARAM = \"X-Amz-Security-Token\";\nconst REGION_SET_PARAM = \"X-Amz-Region-Set\";\nconst AUTH_HEADER = \"authorization\";\nconst AMZ_DATE_HEADER = AMZ_DATE_QUERY_PARAM.toLowerCase();\nconst DATE_HEADER = \"date\";\nconst GENERATED_HEADERS = [AUTH_HEADER, AMZ_DATE_HEADER, DATE_HEADER];\nconst SIGNATURE_HEADER = SIGNATURE_QUERY_PARAM.toLowerCase();\nconst SHA256_HEADER = \"x-amz-content-sha256\";\nconst TOKEN_HEADER = TOKEN_QUERY_PARAM.toLowerCase();\nconst HOST_HEADER = \"host\";\nconst ALWAYS_UNSIGNABLE_HEADERS = {\n    authorization: true,\n    \"cache-control\": true,\n    connection: true,\n    expect: true,\n    from: true,\n    \"keep-alive\": true,\n    \"max-forwards\": true,\n    pragma: true,\n    referer: true,\n    te: true,\n    trailer: true,\n    \"transfer-encoding\": true,\n    upgrade: true,\n    \"user-agent\": true,\n    \"x-amzn-trace-id\": true,\n};\nconst PROXY_HEADER_PATTERN = /^proxy-/;\nconst SEC_HEADER_PATTERN = /^sec-/;\nconst UNSIGNABLE_PATTERNS = [/^proxy-/i, /^sec-/i];\nconst ALGORITHM_IDENTIFIER = \"AWS4-HMAC-SHA256\";\nconst ALGORITHM_IDENTIFIER_V4A = \"AWS4-ECDSA-P256-SHA256\";\nconst EVENT_ALGORITHM_IDENTIFIER = \"AWS4-HMAC-SHA256-PAYLOAD\";\nconst UNSIGNED_PAYLOAD = \"UNSIGNED-PAYLOAD\";\nconst MAX_CACHE_SIZE = 50;\nconst KEY_TYPE_IDENTIFIER = \"aws4_request\";\nconst MAX_PRESIGNED_TTL = 60 * 60 * 24 * 7;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/credentialDerivation.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/credentialDerivation.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearCredentialCache: () => (/* binding */ clearCredentialCache),\n/* harmony export */   createScope: () => (/* binding */ createScope),\n/* harmony export */   getSigningKey: () => (/* binding */ getSigningKey)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-hex-encoding */ \"(rsc)/./node_modules/.pnpm/@smithy+util-hex-encoding@4.0.0/node_modules/@smithy/util-hex-encoding/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/constants.js\");\n\n\n\nconst signingKeyCache = {};\nconst cacheQueue = [];\nconst createScope = (shortDate, region, service) => `${shortDate}/${region}/${service}/${_constants__WEBPACK_IMPORTED_MODULE_2__.KEY_TYPE_IDENTIFIER}`;\nconst getSigningKey = async (sha256Constructor, credentials, shortDate, region, service) => {\n    const credsHash = await hmac(sha256Constructor, credentials.secretAccessKey, credentials.accessKeyId);\n    const cacheKey = `${shortDate}:${region}:${service}:${(0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(credsHash)}:${credentials.sessionToken}`;\n    if (cacheKey in signingKeyCache) {\n        return signingKeyCache[cacheKey];\n    }\n    cacheQueue.push(cacheKey);\n    while (cacheQueue.length > _constants__WEBPACK_IMPORTED_MODULE_2__.MAX_CACHE_SIZE) {\n        delete signingKeyCache[cacheQueue.shift()];\n    }\n    let key = `AWS4${credentials.secretAccessKey}`;\n    for (const signable of [shortDate, region, service, _constants__WEBPACK_IMPORTED_MODULE_2__.KEY_TYPE_IDENTIFIER]) {\n        key = await hmac(sha256Constructor, key, signable);\n    }\n    return (signingKeyCache[cacheKey] = key);\n};\nconst clearCredentialCache = () => {\n    cacheQueue.length = 0;\n    Object.keys(signingKeyCache).forEach((cacheKey) => {\n        delete signingKeyCache[cacheKey];\n    });\n};\nconst hmac = (ctor, secret, data) => {\n    const hash = new ctor(secret);\n    hash.update((0,_smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__.toUint8Array)(data));\n    return hash.digest();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/credentialDerivation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getCanonicalHeaders.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getCanonicalHeaders.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCanonicalHeaders: () => (/* binding */ getCanonicalHeaders)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/constants.js\");\n\nconst getCanonicalHeaders = ({ headers }, unsignableHeaders, signableHeaders) => {\n    const canonical = {};\n    for (const headerName of Object.keys(headers).sort()) {\n        if (headers[headerName] == undefined) {\n            continue;\n        }\n        const canonicalHeaderName = headerName.toLowerCase();\n        if (canonicalHeaderName in _constants__WEBPACK_IMPORTED_MODULE_0__.ALWAYS_UNSIGNABLE_HEADERS ||\n            unsignableHeaders?.has(canonicalHeaderName) ||\n            _constants__WEBPACK_IMPORTED_MODULE_0__.PROXY_HEADER_PATTERN.test(canonicalHeaderName) ||\n            _constants__WEBPACK_IMPORTED_MODULE_0__.SEC_HEADER_PATTERN.test(canonicalHeaderName)) {\n            if (!signableHeaders || (signableHeaders && !signableHeaders.has(canonicalHeaderName))) {\n                continue;\n            }\n        }\n        canonical[canonicalHeaderName] = headers[headerName].trim().replace(/\\s+/g, \" \");\n    }\n    return canonical;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getCanonicalHeaders.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getCanonicalQuery.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getCanonicalQuery.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCanonicalQuery: () => (/* binding */ getCanonicalQuery)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-uri-escape */ \"(rsc)/./node_modules/.pnpm/@smithy+util-uri-escape@4.0.0/node_modules/@smithy/util-uri-escape/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/constants.js\");\n\n\nconst getCanonicalQuery = ({ query = {} }) => {\n    const keys = [];\n    const serialized = {};\n    for (const key of Object.keys(query)) {\n        if (key.toLowerCase() === _constants__WEBPACK_IMPORTED_MODULE_1__.SIGNATURE_HEADER) {\n            continue;\n        }\n        const encodedKey = (0,_smithy_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__.escapeUri)(key);\n        keys.push(encodedKey);\n        const value = query[key];\n        if (typeof value === \"string\") {\n            serialized[encodedKey] = `${encodedKey}=${(0,_smithy_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__.escapeUri)(value)}`;\n        }\n        else if (Array.isArray(value)) {\n            serialized[encodedKey] = value\n                .slice(0)\n                .reduce((encoded, value) => encoded.concat([`${encodedKey}=${(0,_smithy_util_uri_escape__WEBPACK_IMPORTED_MODULE_0__.escapeUri)(value)}`]), [])\n                .sort()\n                .join(\"&\");\n        }\n    }\n    return keys\n        .sort()\n        .map((key) => serialized[key])\n        .filter((serialized) => serialized)\n        .join(\"&\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getCanonicalQuery.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getPayloadHash.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getPayloadHash.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPayloadHash: () => (/* binding */ getPayloadHash)\n/* harmony export */ });\n/* harmony import */ var _smithy_is_array_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/is-array-buffer */ \"(rsc)/./node_modules/.pnpm/@smithy+is-array-buffer@4.0.0/node_modules/@smithy/is-array-buffer/dist-es/index.js\");\n/* harmony import */ var _smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-hex-encoding */ \"(rsc)/./node_modules/.pnpm/@smithy+util-hex-encoding@4.0.0/node_modules/@smithy/util-hex-encoding/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/constants.js\");\n\n\n\n\nconst getPayloadHash = async ({ headers, body }, hashConstructor) => {\n    for (const headerName of Object.keys(headers)) {\n        if (headerName.toLowerCase() === _constants__WEBPACK_IMPORTED_MODULE_3__.SHA256_HEADER) {\n            return headers[headerName];\n        }\n    }\n    if (body == undefined) {\n        return \"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\";\n    }\n    else if (typeof body === \"string\" || ArrayBuffer.isView(body) || (0,_smithy_is_array_buffer__WEBPACK_IMPORTED_MODULE_0__.isArrayBuffer)(body)) {\n        const hashCtor = new hashConstructor();\n        hashCtor.update((0,_smithy_util_utf8__WEBPACK_IMPORTED_MODULE_2__.toUint8Array)(body));\n        return (0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_1__.toHex)(await hashCtor.digest());\n    }\n    return _constants__WEBPACK_IMPORTED_MODULE_3__.UNSIGNED_PAYLOAD;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzaWduYXR1cmUtdjRANS4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvZ2V0UGF5bG9hZEhhc2guanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBd0Q7QUFDTjtBQUNEO0FBQ2E7QUFDdkQsZ0NBQWdDLGVBQWU7QUFDdEQ7QUFDQSx5Q0FBeUMscURBQWE7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUVBQXFFLHNFQUFhO0FBQ2xGO0FBQ0Esd0JBQXdCLCtEQUFZO0FBQ3BDLGVBQWUsZ0VBQUs7QUFDcEI7QUFDQSxXQUFXLHdEQUFnQjtBQUMzQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrc2lnbmF0dXJlLXY0QDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NpZ25hdHVyZS12NC9kaXN0LWVzL2dldFBheWxvYWRIYXNoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzQXJyYXlCdWZmZXIgfSBmcm9tIFwiQHNtaXRoeS9pcy1hcnJheS1idWZmZXJcIjtcbmltcG9ydCB7IHRvSGV4IH0gZnJvbSBcIkBzbWl0aHkvdXRpbC1oZXgtZW5jb2RpbmdcIjtcbmltcG9ydCB7IHRvVWludDhBcnJheSB9IGZyb20gXCJAc21pdGh5L3V0aWwtdXRmOFwiO1xuaW1wb3J0IHsgU0hBMjU2X0hFQURFUiwgVU5TSUdORURfUEFZTE9BRCB9IGZyb20gXCIuL2NvbnN0YW50c1wiO1xuZXhwb3J0IGNvbnN0IGdldFBheWxvYWRIYXNoID0gYXN5bmMgKHsgaGVhZGVycywgYm9keSB9LCBoYXNoQ29uc3RydWN0b3IpID0+IHtcbiAgICBmb3IgKGNvbnN0IGhlYWRlck5hbWUgb2YgT2JqZWN0LmtleXMoaGVhZGVycykpIHtcbiAgICAgICAgaWYgKGhlYWRlck5hbWUudG9Mb3dlckNhc2UoKSA9PT0gU0hBMjU2X0hFQURFUikge1xuICAgICAgICAgICAgcmV0dXJuIGhlYWRlcnNbaGVhZGVyTmFtZV07XG4gICAgICAgIH1cbiAgICB9XG4gICAgaWYgKGJvZHkgPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJldHVybiBcImUzYjBjNDQyOThmYzFjMTQ5YWZiZjRjODk5NmZiOTI0MjdhZTQxZTQ2NDliOTM0Y2E0OTU5OTFiNzg1MmI4NTVcIjtcbiAgICB9XG4gICAgZWxzZSBpZiAodHlwZW9mIGJvZHkgPT09IFwic3RyaW5nXCIgfHwgQXJyYXlCdWZmZXIuaXNWaWV3KGJvZHkpIHx8IGlzQXJyYXlCdWZmZXIoYm9keSkpIHtcbiAgICAgICAgY29uc3QgaGFzaEN0b3IgPSBuZXcgaGFzaENvbnN0cnVjdG9yKCk7XG4gICAgICAgIGhhc2hDdG9yLnVwZGF0ZSh0b1VpbnQ4QXJyYXkoYm9keSkpO1xuICAgICAgICByZXR1cm4gdG9IZXgoYXdhaXQgaGFzaEN0b3IuZGlnZXN0KCkpO1xuICAgIH1cbiAgICByZXR1cm4gVU5TSUdORURfUEFZTE9BRDtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getPayloadHash.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/headerUtil.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/headerUtil.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteHeader: () => (/* binding */ deleteHeader),\n/* harmony export */   getHeaderValue: () => (/* binding */ getHeaderValue),\n/* harmony export */   hasHeader: () => (/* binding */ hasHeader)\n/* harmony export */ });\nconst hasHeader = (soughtHeader, headers) => {\n    soughtHeader = soughtHeader.toLowerCase();\n    for (const headerName of Object.keys(headers)) {\n        if (soughtHeader === headerName.toLowerCase()) {\n            return true;\n        }\n    }\n    return false;\n};\nconst getHeaderValue = (soughtHeader, headers) => {\n    soughtHeader = soughtHeader.toLowerCase();\n    for (const headerName of Object.keys(headers)) {\n        if (soughtHeader === headerName.toLowerCase()) {\n            return headers[headerName];\n        }\n    }\n    return undefined;\n};\nconst deleteHeader = (soughtHeader, headers) => {\n    soughtHeader = soughtHeader.toLowerCase();\n    for (const headerName of Object.keys(headers)) {\n        if (soughtHeader === headerName.toLowerCase()) {\n            delete headers[headerName];\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzaWduYXR1cmUtdjRANS4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvaGVhZGVyVXRpbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzaWduYXR1cmUtdjRANS4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvaGVhZGVyVXRpbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgaGFzSGVhZGVyID0gKHNvdWdodEhlYWRlciwgaGVhZGVycykgPT4ge1xuICAgIHNvdWdodEhlYWRlciA9IHNvdWdodEhlYWRlci50b0xvd2VyQ2FzZSgpO1xuICAgIGZvciAoY29uc3QgaGVhZGVyTmFtZSBvZiBPYmplY3Qua2V5cyhoZWFkZXJzKSkge1xuICAgICAgICBpZiAoc291Z2h0SGVhZGVyID09PSBoZWFkZXJOYW1lLnRvTG93ZXJDYXNlKCkpIHtcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbn07XG5leHBvcnQgY29uc3QgZ2V0SGVhZGVyVmFsdWUgPSAoc291Z2h0SGVhZGVyLCBoZWFkZXJzKSA9PiB7XG4gICAgc291Z2h0SGVhZGVyID0gc291Z2h0SGVhZGVyLnRvTG93ZXJDYXNlKCk7XG4gICAgZm9yIChjb25zdCBoZWFkZXJOYW1lIG9mIE9iamVjdC5rZXlzKGhlYWRlcnMpKSB7XG4gICAgICAgIGlmIChzb3VnaHRIZWFkZXIgPT09IGhlYWRlck5hbWUudG9Mb3dlckNhc2UoKSkge1xuICAgICAgICAgICAgcmV0dXJuIGhlYWRlcnNbaGVhZGVyTmFtZV07XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHVuZGVmaW5lZDtcbn07XG5leHBvcnQgY29uc3QgZGVsZXRlSGVhZGVyID0gKHNvdWdodEhlYWRlciwgaGVhZGVycykgPT4ge1xuICAgIHNvdWdodEhlYWRlciA9IHNvdWdodEhlYWRlci50b0xvd2VyQ2FzZSgpO1xuICAgIGZvciAoY29uc3QgaGVhZGVyTmFtZSBvZiBPYmplY3Qua2V5cyhoZWFkZXJzKSkge1xuICAgICAgICBpZiAoc291Z2h0SGVhZGVyID09PSBoZWFkZXJOYW1lLnRvTG93ZXJDYXNlKCkpIHtcbiAgICAgICAgICAgIGRlbGV0ZSBoZWFkZXJzW2hlYWRlck5hbWVdO1xuICAgICAgICB9XG4gICAgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/headerUtil.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/index.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/index.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALGORITHM_IDENTIFIER: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.ALGORITHM_IDENTIFIER),\n/* harmony export */   ALGORITHM_IDENTIFIER_V4A: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.ALGORITHM_IDENTIFIER_V4A),\n/* harmony export */   ALGORITHM_QUERY_PARAM: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.ALGORITHM_QUERY_PARAM),\n/* harmony export */   ALWAYS_UNSIGNABLE_HEADERS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.ALWAYS_UNSIGNABLE_HEADERS),\n/* harmony export */   AMZ_DATE_HEADER: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.AMZ_DATE_HEADER),\n/* harmony export */   AMZ_DATE_QUERY_PARAM: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.AMZ_DATE_QUERY_PARAM),\n/* harmony export */   AUTH_HEADER: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.AUTH_HEADER),\n/* harmony export */   CREDENTIAL_QUERY_PARAM: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.CREDENTIAL_QUERY_PARAM),\n/* harmony export */   DATE_HEADER: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.DATE_HEADER),\n/* harmony export */   EVENT_ALGORITHM_IDENTIFIER: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.EVENT_ALGORITHM_IDENTIFIER),\n/* harmony export */   EXPIRES_QUERY_PARAM: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.EXPIRES_QUERY_PARAM),\n/* harmony export */   GENERATED_HEADERS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.GENERATED_HEADERS),\n/* harmony export */   HOST_HEADER: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.HOST_HEADER),\n/* harmony export */   KEY_TYPE_IDENTIFIER: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.KEY_TYPE_IDENTIFIER),\n/* harmony export */   MAX_CACHE_SIZE: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.MAX_CACHE_SIZE),\n/* harmony export */   MAX_PRESIGNED_TTL: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.MAX_PRESIGNED_TTL),\n/* harmony export */   PROXY_HEADER_PATTERN: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.PROXY_HEADER_PATTERN),\n/* harmony export */   REGION_SET_PARAM: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.REGION_SET_PARAM),\n/* harmony export */   SEC_HEADER_PATTERN: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.SEC_HEADER_PATTERN),\n/* harmony export */   SHA256_HEADER: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.SHA256_HEADER),\n/* harmony export */   SIGNATURE_HEADER: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.SIGNATURE_HEADER),\n/* harmony export */   SIGNATURE_QUERY_PARAM: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.SIGNATURE_QUERY_PARAM),\n/* harmony export */   SIGNED_HEADERS_QUERY_PARAM: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.SIGNED_HEADERS_QUERY_PARAM),\n/* harmony export */   SignatureV4: () => (/* reexport safe */ _SignatureV4__WEBPACK_IMPORTED_MODULE_0__.SignatureV4),\n/* harmony export */   SignatureV4Base: () => (/* reexport safe */ _SignatureV4Base__WEBPACK_IMPORTED_MODULE_8__.SignatureV4Base),\n/* harmony export */   TOKEN_HEADER: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.TOKEN_HEADER),\n/* harmony export */   TOKEN_QUERY_PARAM: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.TOKEN_QUERY_PARAM),\n/* harmony export */   UNSIGNABLE_PATTERNS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.UNSIGNABLE_PATTERNS),\n/* harmony export */   UNSIGNED_PAYLOAD: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.UNSIGNED_PAYLOAD),\n/* harmony export */   clearCredentialCache: () => (/* reexport safe */ _credentialDerivation__WEBPACK_IMPORTED_MODULE_7__.clearCredentialCache),\n/* harmony export */   createScope: () => (/* reexport safe */ _credentialDerivation__WEBPACK_IMPORTED_MODULE_7__.createScope),\n/* harmony export */   getCanonicalHeaders: () => (/* reexport safe */ _getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_2__.getCanonicalHeaders),\n/* harmony export */   getCanonicalQuery: () => (/* reexport safe */ _getCanonicalQuery__WEBPACK_IMPORTED_MODULE_3__.getCanonicalQuery),\n/* harmony export */   getPayloadHash: () => (/* reexport safe */ _getPayloadHash__WEBPACK_IMPORTED_MODULE_4__.getPayloadHash),\n/* harmony export */   getSigningKey: () => (/* reexport safe */ _credentialDerivation__WEBPACK_IMPORTED_MODULE_7__.getSigningKey),\n/* harmony export */   hasHeader: () => (/* reexport safe */ _headerUtil__WEBPACK_IMPORTED_MODULE_9__.hasHeader),\n/* harmony export */   moveHeadersToQuery: () => (/* reexport safe */ _moveHeadersToQuery__WEBPACK_IMPORTED_MODULE_5__.moveHeadersToQuery),\n/* harmony export */   prepareRequest: () => (/* reexport safe */ _prepareRequest__WEBPACK_IMPORTED_MODULE_6__.prepareRequest),\n/* harmony export */   signatureV4aContainer: () => (/* reexport safe */ _signature_v4a_container__WEBPACK_IMPORTED_MODULE_10__.signatureV4aContainer)\n/* harmony export */ });\n/* harmony import */ var _SignatureV4__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SignatureV4 */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/SignatureV4.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/constants.js\");\n/* harmony import */ var _getCanonicalHeaders__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getCanonicalHeaders */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getCanonicalHeaders.js\");\n/* harmony import */ var _getCanonicalQuery__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getCanonicalQuery */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getCanonicalQuery.js\");\n/* harmony import */ var _getPayloadHash__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getPayloadHash */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/getPayloadHash.js\");\n/* harmony import */ var _moveHeadersToQuery__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./moveHeadersToQuery */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/moveHeadersToQuery.js\");\n/* harmony import */ var _prepareRequest__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./prepareRequest */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/prepareRequest.js\");\n/* harmony import */ var _credentialDerivation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./credentialDerivation */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/credentialDerivation.js\");\n/* harmony import */ var _SignatureV4Base__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./SignatureV4Base */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/SignatureV4Base.js\");\n/* harmony import */ var _headerUtil__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./headerUtil */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/headerUtil.js\");\n/* harmony import */ var _signature_v4a_container__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./signature-v4a-container */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/signature-v4a-container.js\");\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzaWduYXR1cmUtdjRANS4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDRjtBQUNnQztBQUNKO0FBQ047QUFDUTtBQUNSO0FBQ1g7QUFDYTtBQUNYO0FBQ0MiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3NpZ25hdHVyZS12NEA1LjEuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9zaWduYXR1cmUtdjQvZGlzdC1lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9TaWduYXR1cmVWNFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vY29uc3RhbnRzXCI7XG5leHBvcnQgeyBnZXRDYW5vbmljYWxIZWFkZXJzIH0gZnJvbSBcIi4vZ2V0Q2Fub25pY2FsSGVhZGVyc1wiO1xuZXhwb3J0IHsgZ2V0Q2Fub25pY2FsUXVlcnkgfSBmcm9tIFwiLi9nZXRDYW5vbmljYWxRdWVyeVwiO1xuZXhwb3J0IHsgZ2V0UGF5bG9hZEhhc2ggfSBmcm9tIFwiLi9nZXRQYXlsb2FkSGFzaFwiO1xuZXhwb3J0IHsgbW92ZUhlYWRlcnNUb1F1ZXJ5IH0gZnJvbSBcIi4vbW92ZUhlYWRlcnNUb1F1ZXJ5XCI7XG5leHBvcnQgeyBwcmVwYXJlUmVxdWVzdCB9IGZyb20gXCIuL3ByZXBhcmVSZXF1ZXN0XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jcmVkZW50aWFsRGVyaXZhdGlvblwiO1xuZXhwb3J0IHsgU2lnbmF0dXJlVjRCYXNlIH0gZnJvbSBcIi4vU2lnbmF0dXJlVjRCYXNlXCI7XG5leHBvcnQgeyBoYXNIZWFkZXIgfSBmcm9tIFwiLi9oZWFkZXJVdGlsXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zaWduYXR1cmUtdjRhLWNvbnRhaW5lclwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/moveHeadersToQuery.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/moveHeadersToQuery.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   moveHeadersToQuery: () => (/* binding */ moveHeadersToQuery)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n\nconst moveHeadersToQuery = (request, options = {}) => {\n    const { headers, query = {} } = _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.clone(request);\n    for (const name of Object.keys(headers)) {\n        const lname = name.toLowerCase();\n        if ((lname.slice(0, 6) === \"x-amz-\" && !options.unhoistableHeaders?.has(lname)) ||\n            options.hoistableHeaders?.has(lname)) {\n            query[name] = headers[name];\n            delete headers[name];\n        }\n    }\n    return {\n        ...request,\n        headers,\n        query,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzaWduYXR1cmUtdjRANS4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvbW92ZUhlYWRlcnNUb1F1ZXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBQzdDLGlEQUFpRDtBQUN4RCxZQUFZLHNCQUFzQixFQUFFLDhEQUFXO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3NpZ25hdHVyZS12NEA1LjEuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9zaWduYXR1cmUtdjQvZGlzdC1lcy9tb3ZlSGVhZGVyc1RvUXVlcnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSHR0cFJlcXVlc3QgfSBmcm9tIFwiQHNtaXRoeS9wcm90b2NvbC1odHRwXCI7XG5leHBvcnQgY29uc3QgbW92ZUhlYWRlcnNUb1F1ZXJ5ID0gKHJlcXVlc3QsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgIGNvbnN0IHsgaGVhZGVycywgcXVlcnkgPSB7fSB9ID0gSHR0cFJlcXVlc3QuY2xvbmUocmVxdWVzdCk7XG4gICAgZm9yIChjb25zdCBuYW1lIG9mIE9iamVjdC5rZXlzKGhlYWRlcnMpKSB7XG4gICAgICAgIGNvbnN0IGxuYW1lID0gbmFtZS50b0xvd2VyQ2FzZSgpO1xuICAgICAgICBpZiAoKGxuYW1lLnNsaWNlKDAsIDYpID09PSBcIngtYW16LVwiICYmICFvcHRpb25zLnVuaG9pc3RhYmxlSGVhZGVycz8uaGFzKGxuYW1lKSkgfHxcbiAgICAgICAgICAgIG9wdGlvbnMuaG9pc3RhYmxlSGVhZGVycz8uaGFzKGxuYW1lKSkge1xuICAgICAgICAgICAgcXVlcnlbbmFtZV0gPSBoZWFkZXJzW25hbWVdO1xuICAgICAgICAgICAgZGVsZXRlIGhlYWRlcnNbbmFtZV07XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgICAgLi4ucmVxdWVzdCxcbiAgICAgICAgaGVhZGVycyxcbiAgICAgICAgcXVlcnksXG4gICAgfTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/moveHeadersToQuery.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/prepareRequest.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/prepareRequest.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prepareRequest: () => (/* binding */ prepareRequest)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/constants.js\");\n\n\nconst prepareRequest = (request) => {\n    request = _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.clone(request);\n    for (const headerName of Object.keys(request.headers)) {\n        if (_constants__WEBPACK_IMPORTED_MODULE_1__.GENERATED_HEADERS.indexOf(headerName.toLowerCase()) > -1) {\n            delete request.headers[headerName];\n        }\n    }\n    return request;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzaWduYXR1cmUtdjRANS4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvcHJlcGFyZVJlcXVlc3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9EO0FBQ0o7QUFDekM7QUFDUCxjQUFjLDhEQUFXO0FBQ3pCO0FBQ0EsWUFBWSx5REFBaUI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrc2lnbmF0dXJlLXY0QDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NpZ25hdHVyZS12NC9kaXN0LWVzL3ByZXBhcmVSZXF1ZXN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEh0dHBSZXF1ZXN0IH0gZnJvbSBcIkBzbWl0aHkvcHJvdG9jb2wtaHR0cFwiO1xuaW1wb3J0IHsgR0VORVJBVEVEX0hFQURFUlMgfSBmcm9tIFwiLi9jb25zdGFudHNcIjtcbmV4cG9ydCBjb25zdCBwcmVwYXJlUmVxdWVzdCA9IChyZXF1ZXN0KSA9PiB7XG4gICAgcmVxdWVzdCA9IEh0dHBSZXF1ZXN0LmNsb25lKHJlcXVlc3QpO1xuICAgIGZvciAoY29uc3QgaGVhZGVyTmFtZSBvZiBPYmplY3Qua2V5cyhyZXF1ZXN0LmhlYWRlcnMpKSB7XG4gICAgICAgIGlmIChHRU5FUkFURURfSEVBREVSUy5pbmRleE9mKGhlYWRlck5hbWUudG9Mb3dlckNhc2UoKSkgPiAtMSkge1xuICAgICAgICAgICAgZGVsZXRlIHJlcXVlc3QuaGVhZGVyc1toZWFkZXJOYW1lXTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcmVxdWVzdDtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/prepareRequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/signature-v4a-container.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/signature-v4a-container.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signatureV4aContainer: () => (/* binding */ signatureV4aContainer)\n/* harmony export */ });\nconst signatureV4aContainer = {\n    SignatureV4a: null,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzaWduYXR1cmUtdjRANS4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvc2lnbmF0dXJlLXY0YS1jb250YWluZXIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrc2lnbmF0dXJlLXY0QDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NpZ25hdHVyZS12NC9kaXN0LWVzL3NpZ25hdHVyZS12NGEtY29udGFpbmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBzaWduYXR1cmVWNGFDb250YWluZXIgPSB7XG4gICAgU2lnbmF0dXJlVjRhOiBudWxsLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/signature-v4a-container.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/utilDate.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/utilDate.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iso8601: () => (/* binding */ iso8601),\n/* harmony export */   toDate: () => (/* binding */ toDate)\n/* harmony export */ });\nconst iso8601 = (time) => toDate(time)\n    .toISOString()\n    .replace(/\\.\\d{3}Z$/, \"Z\");\nconst toDate = (time) => {\n    if (typeof time === \"number\") {\n        return new Date(time * 1000);\n    }\n    if (typeof time === \"string\") {\n        if (Number(time)) {\n            return new Date(Number(time) * 1000);\n        }\n        return new Date(time);\n    }\n    return time;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzaWduYXR1cmUtdjRANS4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvc2lnbmF0dXJlLXY0L2Rpc3QtZXMvdXRpbERhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNQO0FBQ0EsbUJBQW1CLEVBQUU7QUFDZDtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3NpZ25hdHVyZS12NEA1LjEuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9zaWduYXR1cmUtdjQvZGlzdC1lcy91dGlsRGF0ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgaXNvODYwMSA9ICh0aW1lKSA9PiB0b0RhdGUodGltZSlcbiAgICAudG9JU09TdHJpbmcoKVxuICAgIC5yZXBsYWNlKC9cXC5cXGR7M31aJC8sIFwiWlwiKTtcbmV4cG9ydCBjb25zdCB0b0RhdGUgPSAodGltZSkgPT4ge1xuICAgIGlmICh0eXBlb2YgdGltZSA9PT0gXCJudW1iZXJcIikge1xuICAgICAgICByZXR1cm4gbmV3IERhdGUodGltZSAqIDEwMDApO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIHRpbWUgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgaWYgKE51bWJlcih0aW1lKSkge1xuICAgICAgICAgICAgcmV0dXJuIG5ldyBEYXRlKE51bWJlcih0aW1lKSAqIDEwMDApO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBuZXcgRGF0ZSh0aW1lKTtcbiAgICB9XG4gICAgcmV0dXJuIHRpbWU7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/utilDate.js\n");

/***/ })

};
;