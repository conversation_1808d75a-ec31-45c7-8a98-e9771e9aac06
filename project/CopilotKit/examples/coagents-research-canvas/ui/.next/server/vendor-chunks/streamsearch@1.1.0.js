"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/streamsearch@1.1.0";
exports.ids = ["vendor-chunks/streamsearch@1.1.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/streamsearch@1.1.0/node_modules/streamsearch/lib/sbmh.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/streamsearch@1.1.0/node_modules/streamsearch/lib/sbmh.js ***!
  \*************************************************************************************/
/***/ ((module) => {

eval("\n/*\n  Based heavily on the Streaming Boyer-Moore-Horspool C++ implementation\n  by Hongli Lai at: https://github.com/FooBarWidget/boyer-moore-horspool\n*/\nfunction memcmp(buf1, pos1, buf2, pos2, num) {\n  for (let i = 0; i < num; ++i) {\n    if (buf1[pos1 + i] !== buf2[pos2 + i])\n      return false;\n  }\n  return true;\n}\n\nclass SBMH {\n  constructor(needle, cb) {\n    if (typeof cb !== 'function')\n      throw new Error('Missing match callback');\n\n    if (typeof needle === 'string')\n      needle = Buffer.from(needle);\n    else if (!Buffer.isBuffer(needle))\n      throw new Error(`Expected Buffer for needle, got ${typeof needle}`);\n\n    const needleLen = needle.length;\n\n    this.maxMatches = Infinity;\n    this.matches = 0;\n\n    this._cb = cb;\n    this._lookbehindSize = 0;\n    this._needle = needle;\n    this._bufPos = 0;\n\n    this._lookbehind = Buffer.allocUnsafe(needleLen);\n\n    // Initialize occurrence table.\n    this._occ = [\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen\n    ];\n\n    // Populate occurrence table with analysis of the needle, ignoring the last\n    // letter.\n    if (needleLen > 1) {\n      for (let i = 0; i < needleLen - 1; ++i)\n        this._occ[needle[i]] = needleLen - 1 - i;\n    }\n  }\n\n  reset() {\n    this.matches = 0;\n    this._lookbehindSize = 0;\n    this._bufPos = 0;\n  }\n\n  push(chunk, pos) {\n    let result;\n    if (!Buffer.isBuffer(chunk))\n      chunk = Buffer.from(chunk, 'latin1');\n    const chunkLen = chunk.length;\n    this._bufPos = pos || 0;\n    while (result !== chunkLen && this.matches < this.maxMatches)\n      result = feed(this, chunk);\n    return result;\n  }\n\n  destroy() {\n    const lbSize = this._lookbehindSize;\n    if (lbSize)\n      this._cb(false, this._lookbehind, 0, lbSize, false);\n    this.reset();\n  }\n}\n\nfunction feed(self, data) {\n  const len = data.length;\n  const needle = self._needle;\n  const needleLen = needle.length;\n\n  // Positive: points to a position in `data`\n  //           pos == 3 points to data[3]\n  // Negative: points to a position in the lookbehind buffer\n  //           pos == -2 points to lookbehind[lookbehindSize - 2]\n  let pos = -self._lookbehindSize;\n  const lastNeedleCharPos = needleLen - 1;\n  const lastNeedleChar = needle[lastNeedleCharPos];\n  const end = len - needleLen;\n  const occ = self._occ;\n  const lookbehind = self._lookbehind;\n\n  if (pos < 0) {\n    // Lookbehind buffer is not empty. Perform Boyer-Moore-Horspool\n    // search with character lookup code that considers both the\n    // lookbehind buffer and the current round's haystack data.\n    //\n    // Loop until\n    //   there is a match.\n    // or until\n    //   we've moved past the position that requires the\n    //   lookbehind buffer. In this case we switch to the\n    //   optimized loop.\n    // or until\n    //   the character to look at lies outside the haystack.\n    while (pos < 0 && pos <= end) {\n      const nextPos = pos + lastNeedleCharPos;\n      const ch = (nextPos < 0\n                  ? lookbehind[self._lookbehindSize + nextPos]\n                  : data[nextPos]);\n\n      if (ch === lastNeedleChar\n          && matchNeedle(self, data, pos, lastNeedleCharPos)) {\n        self._lookbehindSize = 0;\n        ++self.matches;\n        if (pos > -self._lookbehindSize)\n          self._cb(true, lookbehind, 0, self._lookbehindSize + pos, false);\n        else\n          self._cb(true, undefined, 0, 0, true);\n\n        return (self._bufPos = pos + needleLen);\n      }\n\n      pos += occ[ch];\n    }\n\n    // No match.\n\n    // There's too few data for Boyer-Moore-Horspool to run,\n    // so let's use a different algorithm to skip as much as\n    // we can.\n    // Forward pos until\n    //   the trailing part of lookbehind + data\n    //   looks like the beginning of the needle\n    // or until\n    //   pos == 0\n    while (pos < 0 && !matchNeedle(self, data, pos, len - pos))\n      ++pos;\n\n    if (pos < 0) {\n      // Cut off part of the lookbehind buffer that has\n      // been processed and append the entire haystack\n      // into it.\n      const bytesToCutOff = self._lookbehindSize + pos;\n\n      if (bytesToCutOff > 0) {\n        // The cut off data is guaranteed not to contain the needle.\n        self._cb(false, lookbehind, 0, bytesToCutOff, false);\n      }\n\n      self._lookbehindSize -= bytesToCutOff;\n      lookbehind.copy(lookbehind, 0, bytesToCutOff, self._lookbehindSize);\n      lookbehind.set(data, self._lookbehindSize);\n      self._lookbehindSize += len;\n\n      self._bufPos = len;\n      return len;\n    }\n\n    // Discard lookbehind buffer.\n    self._cb(false, lookbehind, 0, self._lookbehindSize, false);\n    self._lookbehindSize = 0;\n  }\n\n  pos += self._bufPos;\n\n  const firstNeedleChar = needle[0];\n\n  // Lookbehind buffer is now empty. Perform Boyer-Moore-Horspool\n  // search with optimized character lookup code that only considers\n  // the current round's haystack data.\n  while (pos <= end) {\n    const ch = data[pos + lastNeedleCharPos];\n\n    if (ch === lastNeedleChar\n        && data[pos] === firstNeedleChar\n        && memcmp(needle, 0, data, pos, lastNeedleCharPos)) {\n      ++self.matches;\n      if (pos > 0)\n        self._cb(true, data, self._bufPos, pos, true);\n      else\n        self._cb(true, undefined, 0, 0, true);\n\n      return (self._bufPos = pos + needleLen);\n    }\n\n    pos += occ[ch];\n  }\n\n  // There was no match. If there's trailing haystack data that we cannot\n  // match yet using the Boyer-Moore-Horspool algorithm (because the trailing\n  // data is less than the needle size) then match using a modified\n  // algorithm that starts matching from the beginning instead of the end.\n  // Whatever trailing data is left after running this algorithm is added to\n  // the lookbehind buffer.\n  while (pos < len) {\n    if (data[pos] !== firstNeedleChar\n        || !memcmp(data, pos, needle, 0, len - pos)) {\n      ++pos;\n      continue;\n    }\n    data.copy(lookbehind, 0, pos, len);\n    self._lookbehindSize = len - pos;\n    break;\n  }\n\n  // Everything until `pos` is guaranteed not to contain needle data.\n  if (pos > 0)\n    self._cb(false, data, self._bufPos, pos < len ? pos : len, true);\n\n  self._bufPos = len;\n  return len;\n}\n\nfunction matchNeedle(self, data, pos, len) {\n  const lb = self._lookbehind;\n  const lbSize = self._lookbehindSize;\n  const needle = self._needle;\n\n  for (let i = 0; i < len; ++i, ++pos) {\n    const ch = (pos < 0 ? lb[lbSize + pos] : data[pos]);\n    if (ch !== needle[i])\n      return false;\n  }\n  return true;\n}\n\nmodule.exports = SBMH;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/streamsearch@1.1.0/node_modules/streamsearch/lib/sbmh.js\n");

/***/ })

};
;