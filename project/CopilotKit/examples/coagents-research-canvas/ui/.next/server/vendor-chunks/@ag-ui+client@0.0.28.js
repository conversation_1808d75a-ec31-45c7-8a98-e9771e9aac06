"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ag-ui+client@0.0.28";
exports.ids = ["vendor-chunks/@ag-ui+client@0.0.28"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@ag-ui+client@0.0.28/node_modules/@ag-ui/client/dist/index.mjs":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ag-ui+client@0.0.28/node_modules/@ag-ui/client/dist/index.mjs ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AGUIError: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError),\n/* harmony export */   AbstractAgent: () => (/* binding */ b),\n/* harmony export */   AssistantMessageSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AssistantMessageSchema),\n/* harmony export */   BaseMessageSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.BaseMessageSchema),\n/* harmony export */   ContextSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.ContextSchema),\n/* harmony export */   CustomEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.CustomEventSchema),\n/* harmony export */   DeveloperMessageSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.DeveloperMessageSchema),\n/* harmony export */   EventSchemas: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventSchemas),\n/* harmony export */   EventType: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType),\n/* harmony export */   FunctionCallSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.FunctionCallSchema),\n/* harmony export */   HttpAgent: () => (/* binding */ X),\n/* harmony export */   MessageSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.MessageSchema),\n/* harmony export */   MessagesSnapshotEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.MessagesSnapshotEventSchema),\n/* harmony export */   RawEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.RawEventSchema),\n/* harmony export */   RoleSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.RoleSchema),\n/* harmony export */   RunAgentInputSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.RunAgentInputSchema),\n/* harmony export */   RunErrorEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.RunErrorEventSchema),\n/* harmony export */   RunErrorSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.RunErrorSchema),\n/* harmony export */   RunFinishedEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.RunFinishedEventSchema),\n/* harmony export */   RunFinishedSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.RunFinishedSchema),\n/* harmony export */   RunStartedEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.RunStartedEventSchema),\n/* harmony export */   RunStartedSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.RunStartedSchema),\n/* harmony export */   StateDeltaEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.StateDeltaEventSchema),\n/* harmony export */   StateSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.StateSchema),\n/* harmony export */   StateSnapshotEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.StateSnapshotEventSchema),\n/* harmony export */   StepFinishedEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.StepFinishedEventSchema),\n/* harmony export */   StepFinishedSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.StepFinishedSchema),\n/* harmony export */   StepStartedEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.StepStartedEventSchema),\n/* harmony export */   StepStartedSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.StepStartedSchema),\n/* harmony export */   SystemMessageSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.SystemMessageSchema),\n/* harmony export */   TextMessageChunkEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.TextMessageChunkEventSchema),\n/* harmony export */   TextMessageContentEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.TextMessageContentEventSchema),\n/* harmony export */   TextMessageEndEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.TextMessageEndEventSchema),\n/* harmony export */   TextMessageStartEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.TextMessageStartEventSchema),\n/* harmony export */   ToolCallArgsEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.ToolCallArgsEventSchema),\n/* harmony export */   ToolCallChunkEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.ToolCallChunkEventSchema),\n/* harmony export */   ToolCallEndEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.ToolCallEndEventSchema),\n/* harmony export */   ToolCallSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.ToolCallSchema),\n/* harmony export */   ToolCallStartEventSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.ToolCallStartEventSchema),\n/* harmony export */   ToolMessageSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.ToolMessageSchema),\n/* harmony export */   ToolSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.ToolSchema),\n/* harmony export */   UserMessageSchema: () => (/* reexport safe */ _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.UserMessageSchema),\n/* harmony export */   convertToLegacyEvents: () => (/* binding */ U),\n/* harmony export */   defaultApplyEvents: () => (/* binding */ w),\n/* harmony export */   parseProtoStream: () => (/* binding */ P),\n/* harmony export */   parseSSEStream: () => (/* binding */ H),\n/* harmony export */   runHttpRequest: () => (/* binding */ D),\n/* harmony export */   transformHttpEventStream: () => (/* binding */ G),\n/* harmony export */   verifyEvents: () => (/* binding */ N)\n/* harmony export */ });\n/* harmony import */ var _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ag-ui/core */ \"(rsc)/./node_modules/.pnpm/@ag-ui+core@0.0.28/node_modules/@ag-ui/core/dist/index.mjs\");\n/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs/operators */ \"(rsc)/./node_modules/.pnpm/rxjs@7.8.1/node_modules/rxjs/dist/cjs/operators/index.js\");\n/* harmony import */ var fast_json_patch__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fast-json-patch */ \"(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/index.mjs\");\n/* harmony import */ var untruncate_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! untruncate-json */ \"(rsc)/./node_modules/.pnpm/untruncate-json@0.0.1/node_modules/untruncate-json/dist/esm/index.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/.pnpm/rxjs@7.8.1/node_modules/rxjs/dist/esm5/internal/observable/throwError.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/.pnpm/rxjs@7.8.1/node_modules/rxjs/dist/esm5/internal/observable/of.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/.pnpm/rxjs@7.8.1/node_modules/rxjs/dist/esm5/internal/Subject.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/.pnpm/rxjs@7.8.1/node_modules/rxjs/dist/esm5/internal/ReplaySubject.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/.pnpm/rxjs@7.8.1/node_modules/rxjs/dist/esm5/internal/observable/defer.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/.pnpm/rxjs@7.8.1/node_modules/rxjs/dist/esm5/internal/observable/from.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/.pnpm/rxjs@7.8.1/node_modules/rxjs/dist/esm5/internal/Observable.js\");\n/* harmony import */ var _ag_ui_proto__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @ag-ui/proto */ \"(rsc)/./node_modules/.pnpm/@ag-ui+proto@0.0.28/node_modules/@ag-ui/proto/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/.pnpm/zod@3.23.8/node_modules/zod/lib/index.mjs\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/.pnpm/rxjs@7.8.1/node_modules/rxjs/dist/esm5/internal/util/pipe.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/.pnpm/rxjs@7.8.1/node_modules/rxjs/dist/esm5/internal/lastValueFrom.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/.pnpm/rxjs@7.8.1/node_modules/rxjs/dist/esm5/internal/operators/mergeMap.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/.pnpm/rxjs@7.8.1/node_modules/rxjs/dist/esm5/internal/operators/finalize.js\");\nvar B=Object.defineProperty,$=Object.defineProperties;var K=Object.getOwnPropertyDescriptors;var j=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,q=Object.prototype.propertyIsEnumerable;var k=(u,n,t)=>n in u?B(u,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):u[n]=t,L=(u,n)=>{for(var t in n||(n={}))V.call(n,t)&&k(u,t,n[t]);if(j)for(var t of j(n))q.call(n,t)&&k(u,t,n[t]);return u},M=(u,n)=>$(u,K(n));var _=u=>{if(typeof structuredClone==\"function\")return structuredClone(u);try{return JSON.parse(JSON.stringify(u))}catch(n){return L({},u)}};var w=(...u)=>{let[n,t]=u,e=_(n.messages),s=_(n.state),i,l=r=>[_(r)],o=()=>[];return t.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.mergeMap)(r=>{var p;switch(r.type){case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_START:{let{messageId:d,role:c}=r,g={id:d,role:c,content:\"\"};return e.push(g),l({messages:e})}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_CONTENT:{let{delta:d}=r,c=e[e.length-1];return c.content=c.content+d,l({messages:e})}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_END:return o();case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_START:{let{toolCallId:d,toolCallName:c,parentMessageId:g}=r,E;return g&&e.length>0&&e[e.length-1].id===g?E=e[e.length-1]:(E={id:g||d,role:\"assistant\",toolCalls:[]},e.push(E)),(p=E.toolCalls)!=null||(E.toolCalls=[]),E.toolCalls.push({id:d,type:\"function\",function:{name:c,arguments:\"\"}}),l({messages:e})}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_ARGS:{let{delta:d}=r,c=e[e.length-1],g=c.toolCalls[c.toolCalls.length-1];if(g.function.arguments+=d,i){let E=i.find(h=>h.tool===g.function.name);if(E)try{let h=JSON.parse((0,untruncate_json__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(g.function.arguments));return E.tool_argument&&E.tool_argument in h?(s=M(L({},s),{[E.state_key]:h[E.tool_argument]}),l({messages:e,state:s})):(s=M(L({},s),{[E.state_key]:h}),l({messages:e,state:s}))}catch(h){}}return l({messages:e})}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_END:return o();case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STATE_SNAPSHOT:{let{snapshot:d}=r;return s=d,l({state:s})}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STATE_DELTA:{let{delta:d}=r;try{return s=(0,fast_json_patch__WEBPACK_IMPORTED_MODULE_1__.applyPatch)(s,d,!0,!1).newDocument,l({state:s})}catch(c){let g=c instanceof Error?c.message:String(c);return console.warn(`Failed to apply state patch:\nCurrent state: ${JSON.stringify(s,null,2)}\nPatch operations: ${JSON.stringify(d,null,2)}\nError: ${g}`),o()}}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.MESSAGES_SNAPSHOT:{let{messages:d}=r;return e=d,l({messages:e})}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RAW:return o();case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.CUSTOM:{let d=r;return d.name===\"PredictState\"&&(i=d.value),o()}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_STARTED:return o();case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_FINISHED:return o();case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_ERROR:return o();case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STEP_STARTED:return o();case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STEP_FINISHED:return i=void 0,o();case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_CHUNK:throw new Error(\"TEXT_MESSAGE_CHUNK must be tranformed before being applied\");case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_CHUNK:throw new Error(\"TOOL_CALL_CHUNK must be tranformed before being applied\")}let f=r.type;return o()}))};var N=u=>n=>{let t,e,s=!1,i=!1,l=!1,o=new Map;return n.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.mergeMap)(r=>{let f=r.type;if(u&&console.debug(\"[VERIFY]:\",JSON.stringify(r)),i)return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(`Cannot send event type '${f}': The run has already errored with 'RUN_ERROR'. No further events can be sent.`));if(s&&f!==_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_ERROR)return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(`Cannot send event type '${f}': The run has already finished with 'RUN_FINISHED'. Start a new run with 'RUN_STARTED'.`));if(t!==void 0&&![_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_CONTENT,_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_END,_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RAW].includes(f))return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(`Cannot send event type '${f}' after 'TEXT_MESSAGE_START': Send 'TEXT_MESSAGE_END' first.`));if(e!==void 0&&![_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_ARGS,_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_END,_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RAW].includes(f))return f===_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_START?(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(\"Cannot send 'TOOL_CALL_START' event: A tool call is already in progress. Complete it with 'TOOL_CALL_END' first.\")):(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(`Cannot send event type '${f}' after 'TOOL_CALL_START': Send 'TOOL_CALL_END' first.`));if(l){if(f===_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_STARTED)return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(\"Cannot send multiple 'RUN_STARTED' events: A 'RUN_STARTED' event was already sent. Each run must have exactly one 'RUN_STARTED' event at the beginning.\"))}else if(l=!0,f!==_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_STARTED&&f!==_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_ERROR)return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(\"First event must be 'RUN_STARTED'\"));switch(f){case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_START:return t!==void 0?(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(\"Cannot send 'TEXT_MESSAGE_START' event: A text message is already in progress. Complete it with 'TEXT_MESSAGE_END' first.\")):(t=r.messageId,(0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(r));case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_CONTENT:return t===void 0?(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(\"Cannot send 'TEXT_MESSAGE_CONTENT' event: No active text message found. Start a text message with 'TEXT_MESSAGE_START' first.\")):r.messageId!==t?(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(`Cannot send 'TEXT_MESSAGE_CONTENT' event: Message ID mismatch. The ID '${r.messageId}' doesn't match the active message ID '${t}'.`)):(0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(r);case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_END:return t===void 0?(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(\"Cannot send 'TEXT_MESSAGE_END' event: No active text message found. A 'TEXT_MESSAGE_START' event must be sent first.\")):r.messageId!==t?(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(`Cannot send 'TEXT_MESSAGE_END' event: Message ID mismatch. The ID '${r.messageId}' doesn't match the active message ID '${t}'.`)):(t=void 0,(0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(r));case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_START:return e!==void 0?(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(\"Cannot send 'TOOL_CALL_START' event: A tool call is already in progress. Complete it with 'TOOL_CALL_END' first.\")):(e=r.toolCallId,(0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(r));case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_ARGS:return e===void 0?(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(\"Cannot send 'TOOL_CALL_ARGS' event: No active tool call found. Start a tool call with 'TOOL_CALL_START' first.\")):r.toolCallId!==e?(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(`Cannot send 'TOOL_CALL_ARGS' event: Tool call ID mismatch. The ID '${r.toolCallId}' doesn't match the active tool call ID '${e}'.`)):(0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(r);case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_END:return e===void 0?(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(\"Cannot send 'TOOL_CALL_END' event: No active tool call found. A 'TOOL_CALL_START' event must be sent first.\")):r.toolCallId!==e?(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(`Cannot send 'TOOL_CALL_END' event: Tool call ID mismatch. The ID '${r.toolCallId}' doesn't match the active tool call ID '${e}'.`)):(e=void 0,(0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(r));case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STEP_STARTED:{let p=r.name;return o.has(p)?(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(`Step \"${p}\" is already active for 'STEP_STARTED'`)):(o.set(p,!0),(0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(r))}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STEP_FINISHED:{let p=r.name;return o.has(p)?(o.delete(p),(0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(r)):(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(`Cannot send 'STEP_FINISHED' for step \"${p}\" that was not started`))}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_STARTED:return (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(r);case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_FINISHED:{if(o.size>0){let p=Array.from(o.keys()).join(\", \");return (0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.AGUIError(`Cannot send 'RUN_FINISHED' while steps are still active: ${p}`))}return s=!0,(0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(r)}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_ERROR:return i=!0,(0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(r);case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.CUSTOM:return (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(r);default:return (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(r)}}))};var D=(u,n)=>(0,rxjs__WEBPACK_IMPORTED_MODULE_6__.defer)(()=>(0,rxjs__WEBPACK_IMPORTED_MODULE_7__.from)(fetch(u,n))).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.switchMap)(t=>{var i;let e={type:\"headers\",status:t.status,headers:t.headers},s=(i=t.body)==null?void 0:i.getReader();return s?new rxjs__WEBPACK_IMPORTED_MODULE_8__.Observable(l=>(l.next(e),(async()=>{try{for(;;){let{done:o,value:r}=await s.read();if(o)break;let f={type:\"data\",data:r};l.next(f)}l.complete()}catch(o){l.error(o)}})(),()=>{s.cancel()})):(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>new Error(\"Failed to getReader() from response\"))}));var H=u=>{let n=new rxjs__WEBPACK_IMPORTED_MODULE_9__.Subject,t=new TextDecoder(\"utf-8\",{fatal:!1}),e=\"\";u.subscribe({next:i=>{if(i.type!==\"headers\"&&i.type===\"data\"&&i.data){let l=t.decode(i.data,{stream:!0});e+=l;let o=e.split(/\\n\\n/);e=o.pop()||\"\";for(let r of o)s(r)}},error:i=>n.error(i),complete:()=>{e&&(e+=t.decode(),s(e)),n.complete()}});function s(i){let l=i.split(`\n`),o=[];for(let r of l)r.startsWith(\"data: \")&&o.push(r.slice(6));if(o.length>0)try{let r=o.join(`\n`),f=JSON.parse(r);n.next(f)}catch(r){n.error(r)}}return n.asObservable()};var P=u=>{let n=new rxjs__WEBPACK_IMPORTED_MODULE_9__.Subject,t=new Uint8Array(0);u.subscribe({next:s=>{if(s.type!==\"headers\"&&s.type===\"data\"&&s.data){let i=new Uint8Array(t.length+s.data.length);i.set(t,0),i.set(s.data,t.length),t=i,e()}},error:s=>n.error(s),complete:()=>{if(t.length>0)try{e()}catch(s){console.warn(\"Incomplete or invalid protocol buffer data at stream end\")}n.complete()}});function e(){for(;t.length>=4;){let l=4+new DataView(t.buffer,t.byteOffset,4).getUint32(0,!1);if(t.length<l)break;try{let o=t.slice(4,l),r=_ag_ui_proto__WEBPACK_IMPORTED_MODULE_10__.decode(o);n.next(r),t=t.slice(l)}catch(o){let r=o instanceof Error?o.message:String(o);n.error(new Error(`Failed to decode protocol buffer message: ${r}`));return}}}return n.asObservable()};var G=u=>{let n=new rxjs__WEBPACK_IMPORTED_MODULE_9__.Subject,t=new rxjs__WEBPACK_IMPORTED_MODULE_11__.ReplaySubject,e=!1;return u.subscribe({next:s=>{t.next(s),s.type===\"headers\"&&!e?(e=!0,s.headers.get(\"content-type\")===_ag_ui_proto__WEBPACK_IMPORTED_MODULE_10__.AGUI_MEDIA_TYPE?P(t).subscribe({next:l=>n.next(l),error:l=>n.error(l),complete:()=>n.complete()}):H(t).subscribe({next:l=>{try{let o=_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventSchemas.parse(l);n.next(o)}catch(o){n.error(o)}},error:l=>n.error(l),complete:()=>n.complete()})):e||n.error(new Error(\"No headers event received before data events\"))},error:s=>{t.error(s),n.error(s)},complete:()=>{t.complete()}}),n.asObservable()};var T=zod__WEBPACK_IMPORTED_MODULE_12__.z.enum([\"TextMessageStart\",\"TextMessageContent\",\"TextMessageEnd\",\"ActionExecutionStart\",\"ActionExecutionArgs\",\"ActionExecutionEnd\",\"ActionExecutionResult\",\"AgentStateMessage\",\"MetaEvent\",\"RunStarted\",\"RunFinished\",\"RunError\",\"NodeStarted\",\"NodeFinished\"]),ue=zod__WEBPACK_IMPORTED_MODULE_12__.z.enum([\"LangGraphInterruptEvent\",\"PredictState\",\"Exit\"]),ge=zod__WEBPACK_IMPORTED_MODULE_12__.z.object({type:zod__WEBPACK_IMPORTED_MODULE_12__.z.literal(T.enum.TextMessageStart),messageId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),parentMessageId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string().optional()}),Ee=zod__WEBPACK_IMPORTED_MODULE_12__.z.object({type:zod__WEBPACK_IMPORTED_MODULE_12__.z.literal(T.enum.TextMessageContent),messageId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),content:zod__WEBPACK_IMPORTED_MODULE_12__.z.string()}),pe=zod__WEBPACK_IMPORTED_MODULE_12__.z.object({type:zod__WEBPACK_IMPORTED_MODULE_12__.z.literal(T.enum.TextMessageEnd),messageId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string()}),de=zod__WEBPACK_IMPORTED_MODULE_12__.z.object({type:zod__WEBPACK_IMPORTED_MODULE_12__.z.literal(T.enum.ActionExecutionStart),actionExecutionId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),actionName:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),parentMessageId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string().optional()}),fe=zod__WEBPACK_IMPORTED_MODULE_12__.z.object({type:zod__WEBPACK_IMPORTED_MODULE_12__.z.literal(T.enum.ActionExecutionArgs),actionExecutionId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),args:zod__WEBPACK_IMPORTED_MODULE_12__.z.string()}),Te=zod__WEBPACK_IMPORTED_MODULE_12__.z.object({type:zod__WEBPACK_IMPORTED_MODULE_12__.z.literal(T.enum.ActionExecutionEnd),actionExecutionId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string()}),me=zod__WEBPACK_IMPORTED_MODULE_12__.z.object({type:zod__WEBPACK_IMPORTED_MODULE_12__.z.literal(T.enum.ActionExecutionResult),actionName:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),actionExecutionId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),result:zod__WEBPACK_IMPORTED_MODULE_12__.z.string()}),Se=zod__WEBPACK_IMPORTED_MODULE_12__.z.object({type:zod__WEBPACK_IMPORTED_MODULE_12__.z.literal(T.enum.AgentStateMessage),threadId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),agentName:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),nodeName:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),runId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),active:zod__WEBPACK_IMPORTED_MODULE_12__.z.boolean(),role:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),state:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),running:zod__WEBPACK_IMPORTED_MODULE_12__.z.boolean()}),Ae=zod__WEBPACK_IMPORTED_MODULE_12__.z.object({type:zod__WEBPACK_IMPORTED_MODULE_12__.z.literal(T.enum.MetaEvent),name:ue,value:zod__WEBPACK_IMPORTED_MODULE_12__.z.any()}),Ht=zod__WEBPACK_IMPORTED_MODULE_12__.z.discriminatedUnion(\"type\",[ge,Ee,pe,de,fe,Te,me,Se,Ae]),Pt=zod__WEBPACK_IMPORTED_MODULE_12__.z.object({id:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),role:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),content:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),parentMessageId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string().optional()}),Gt=zod__WEBPACK_IMPORTED_MODULE_12__.z.object({id:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),name:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),arguments:zod__WEBPACK_IMPORTED_MODULE_12__.z.any(),parentMessageId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string().optional()}),Ut=zod__WEBPACK_IMPORTED_MODULE_12__.z.object({id:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),result:zod__WEBPACK_IMPORTED_MODULE_12__.z.any(),actionExecutionId:zod__WEBPACK_IMPORTED_MODULE_12__.z.string(),actionName:zod__WEBPACK_IMPORTED_MODULE_12__.z.string()});var U=(u,n,t)=>e=>{let s={},i=!0,l=!0,o=\"\",r=null,f=null,p=[],d=c=>{typeof c==\"object\"&&c!==null&&(\"messages\"in c&&delete c.messages,s=c)};return e.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.mergeMap)(c=>{switch(c.type){case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_START:{let g=c;return[{type:T.enum.TextMessageStart,messageId:g.messageId}]}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_CONTENT:{let g=c;return[{type:T.enum.TextMessageContent,messageId:g.messageId,content:g.delta}]}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_END:{let g=c;return[{type:T.enum.TextMessageEnd,messageId:g.messageId}]}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_START:{let g=c;return p.push({id:g.toolCallId,type:\"function\",function:{name:g.toolCallName,arguments:\"\"}}),l=!0,[{type:T.enum.ActionExecutionStart,actionExecutionId:g.toolCallId,actionName:g.toolCallName,parentMessageId:g.parentMessageId}]}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_ARGS:{let g=c,E=p[p.length-1];E.function.arguments+=g.delta;let h=!1;if(f){let R=f.find(O=>O.tool==E.function.name);if(R)try{let O=JSON.parse((0,untruncate_json__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(E.function.arguments));R.tool_argument&&R.tool_argument in O?(d(M(L({},s),{[R.state_key]:O[R.tool_argument]})),h=!0):R.tool_argument||(d(M(L({},s),{[R.state_key]:O})),h=!0)}catch(O){}}return[{type:T.enum.ActionExecutionArgs,actionExecutionId:g.toolCallId,args:g.delta},...h?[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:\"assistant\",state:JSON.stringify(s),active:l}]:[]]}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_END:{let g=c;return[{type:T.enum.ActionExecutionEnd,actionExecutionId:g.toolCallId}]}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RAW:return[];case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.CUSTOM:{let g=c;switch(g.name){case\"Exit\":i=!1;break;case\"PredictState\":f=g.value;break}return[{type:T.enum.MetaEvent,name:g.name,value:g.value}]}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STATE_SNAPSHOT:return d(c.snapshot),[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:\"assistant\",state:JSON.stringify(s),active:l}];case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STATE_DELTA:{let E=(0,fast_json_patch__WEBPACK_IMPORTED_MODULE_1__.applyPatch)(s,c.delta,!0,!1);return E?(d(E.newDocument),[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:\"assistant\",state:JSON.stringify(s),active:l}]):[]}case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.MESSAGES_SNAPSHOT:return r=c.messages,[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:\"assistant\",state:JSON.stringify(L(L({},s),r?{messages:r}:{})),active:!0}];case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_STARTED:return[];case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_FINISHED:return r&&(s.messages=r),[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:\"assistant\",state:JSON.stringify(L(L({},s),r?{messages:_e(r)}:{})),active:!1}];case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_ERROR:return console.error(\"Run error\",c),[];case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STEP_STARTED:return o=c.stepName,p=[],f=null,[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:\"assistant\",state:JSON.stringify(s),active:!0}];case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STEP_FINISHED:return p=[],f=null,[{type:T.enum.AgentStateMessage,threadId:u,agentName:t,nodeName:o,runId:n,running:i,role:\"assistant\",state:JSON.stringify(s),active:!1}];default:return[]}}))};function _e(u){var t;let n=[];for(let e of u)if(e.role===\"assistant\"||e.role===\"user\"||e.role===\"system\"){if(e.content){let s={id:e.id,role:e.role,content:e.content};n.push(s)}if(e.role===\"assistant\"&&e.toolCalls&&e.toolCalls.length>0)for(let s of e.toolCalls){let i={id:s.id,name:s.function.name,arguments:JSON.parse(s.function.arguments),parentMessageId:e.id};n.push(i)}}else if(e.role===\"tool\"){let s=\"unknown\";for(let l of u)if(l.role===\"assistant\"&&((t=l.toolCalls)!=null&&t.length)){for(let o of l.toolCalls)if(o.id===e.toolCallId){s=o.function.name;break}}let i={id:e.id,result:e.content,actionExecutionId:e.toolCallId,actionName:s};n.push(i)}return n}var F=u=>n=>{let t,e,s,i=()=>{if(!t||s!==\"text\")throw new Error(\"No text message to close\");let r={type:_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_END,messageId:t.messageId};return s=void 0,t=void 0,u&&console.debug(\"[TRANSFORM]: TEXT_MESSAGE_END\",JSON.stringify(r)),r},l=()=>{if(!e||s!==\"tool\")throw new Error(\"No tool call to close\");let r={type:_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_END,toolCallId:e.toolCallId};return s=void 0,e=void 0,u&&console.debug(\"[TRANSFORM]: TOOL_CALL_END\",JSON.stringify(r)),r},o=()=>s===\"text\"?[i()]:s===\"tool\"?[l()]:[];return n.pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_13__.mergeMap)(r=>{switch(r.type){case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_START:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_CONTENT:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_END:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_START:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_ARGS:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_END:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STATE_SNAPSHOT:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STATE_DELTA:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.MESSAGES_SNAPSHOT:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.CUSTOM:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_STARTED:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_FINISHED:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RUN_ERROR:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STEP_STARTED:case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STEP_FINISHED:return[...o(),r];case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.RAW:return[r];case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_CHUNK:let p=r,d=[];if((s!==\"text\"||p.messageId!==void 0&&p.messageId!==(t==null?void 0:t.messageId))&&d.push(...o()),s!==\"text\"){if(p.messageId===void 0)throw new Error(\"First TEXT_MESSAGE_CHUNK must have a messageId\");t={messageId:p.messageId},s=\"text\";let E={type:_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_START,messageId:p.messageId,role:\"assistant\"};d.push(E),u&&console.debug(\"[TRANSFORM]: TEXT_MESSAGE_START\",JSON.stringify(E))}if(p.delta!==void 0){let E={type:_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TEXT_MESSAGE_CONTENT,messageId:t.messageId,delta:p.delta};d.push(E),u&&console.debug(\"[TRANSFORM]: TEXT_MESSAGE_CONTENT\",JSON.stringify(E))}return d;case _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_CHUNK:let c=r,g=[];if((s!==\"tool\"||c.toolCallId!==void 0&&c.toolCallId!==(e==null?void 0:e.toolCallId))&&g.push(...o()),s!==\"tool\"){if(c.toolCallId===void 0)throw new Error(\"First TOOL_CALL_CHUNK must have a toolCallId\");if(c.toolCallName===void 0)throw new Error(\"First TOOL_CALL_CHUNK must have a toolCallName\");e={toolCallId:c.toolCallId,toolCallName:c.toolCallName,parentMessageId:c.parentMessageId},s=\"tool\";let E={type:_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_START,toolCallId:c.toolCallId,toolCallName:c.toolCallName,parentMessageId:c.parentMessageId};g.push(E),u&&console.debug(\"[TRANSFORM]: TOOL_CALL_START\",JSON.stringify(E))}if(c.delta!==void 0){let E={type:_ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.TOOL_CALL_ARGS,toolCallId:e.toolCallId,delta:c.delta};g.push(E),u&&console.debug(\"[TRANSFORM]: TOOL_CALL_ARGS\",JSON.stringify(E))}return g}let f=r.type}),(0,rxjs__WEBPACK_IMPORTED_MODULE_14__.finalize)(()=>o()))};var b=class{constructor({agentId:n,description:t,threadId:e,initialMessages:s,initialState:i,debug:l}={}){this.debug=!1;this.agentId=n,this.description=t!=null?t:\"\",this.threadId=e!=null?e:(0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(),this.messages=_(s!=null?s:[]),this.state=_(i!=null?i:{}),this.debug=l!=null?l:!1}async runAgent(n){var s;this.agentId=(s=this.agentId)!=null?s:(0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])();let t=this.prepareRunAgentInput(n),e=(0,rxjs__WEBPACK_IMPORTED_MODULE_16__.pipe)(()=>this.run(t),F(this.debug),N(this.debug),i=>this.apply(t,i),i=>this.processApplyEvents(t,i),(0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.catchError)(i=>(this.onError(i),(0,rxjs__WEBPACK_IMPORTED_MODULE_4__.throwError)(()=>i))),(0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.finalize)(()=>{this.onFinalize()}));return (0,rxjs__WEBPACK_IMPORTED_MODULE_17__.lastValueFrom)(e((0,rxjs__WEBPACK_IMPORTED_MODULE_5__.of)(null))).then(()=>{})}abortRun(){}apply(...n){return w(...n)}processApplyEvents(n,t){return t.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.tap)(e=>{e.messages&&(this.messages=e.messages),e.state&&(this.state=e.state)}))}prepareRunAgentInput(n){var t,e,s;return{threadId:this.threadId,runId:(n==null?void 0:n.runId)||(0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(),tools:_((t=n==null?void 0:n.tools)!=null?t:[]),context:_((e=n==null?void 0:n.context)!=null?e:[]),forwardedProps:_((s=n==null?void 0:n.forwardedProps)!=null?s:{}),state:_(this.state),messages:_(this.messages)}}onError(n){console.error(\"Agent execution failed:\",n)}onFinalize(){}clone(){let n=Object.create(Object.getPrototypeOf(this));for(let t of Object.getOwnPropertyNames(this)){let e=this[t];typeof e!=\"function\"&&(n[t]=_(e))}return n}legacy_to_be_removed_runAgentBridged(n){var e;this.agentId=(e=this.agentId)!=null?e:(0,uuid__WEBPACK_IMPORTED_MODULE_15__[\"default\"])();let t=this.prepareRunAgentInput(n);return this.run(t).pipe(F(this.debug),N(this.debug),U(this.threadId,t.runId,this.agentId),s=>s.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.map)(i=>(this.debug&&console.debug(\"[LEGACY]:\",JSON.stringify(i)),i))))}};var X=class extends b{constructor(t){var e;super(t);this.abortController=new AbortController;this.url=t.url,this.headers=_((e=t.headers)!=null?e:{})}requestInit(t){return{method:\"POST\",headers:M(L({},this.headers),{\"Content-Type\":\"application/json\",Accept:\"text/event-stream\"}),body:JSON.stringify(t),signal:this.abortController.signal}}runAgent(t){var e;return this.abortController=(e=t==null?void 0:t.abortController)!=null?e:new AbortController,super.runAgent(t)}abortRun(){this.abortController.abort(),super.abortRun()}run(t){let e=D(this.url,this.requestInit(t));return G(e)}};\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@ag-ui+client@0.0.28/node_modules/@ag-ui/client/dist/index.mjs\n");

/***/ })

};
;