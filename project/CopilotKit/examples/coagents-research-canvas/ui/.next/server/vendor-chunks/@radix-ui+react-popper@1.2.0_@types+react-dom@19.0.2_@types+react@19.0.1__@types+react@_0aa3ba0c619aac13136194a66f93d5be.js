"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-popper@1.2.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+react@_0aa3ba0c619aac13136194a66f93d5be";
exports.ids = ["vendor-chunks/@radix-ui+react-popper@1.2.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+react@_0aa3ba0c619aac13136194a66f93d5be"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+react@_0aa3ba0c619aac13136194a66f93d5be/node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-popper@1.2.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+react@_0aa3ba0c619aac13136194a66f93d5be/node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+react-dom@2.1.2_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+dom@1.6.11/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-arrow@1.1.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+react@1_9e129052b105b393ae22c70411318c7c/node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.0_@types+react@19.0.1_react@19.0.0/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.0_@types+react@19.0.1_react@19.0.0/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.0.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+rea_c98e92916030b59f0b851119b2fb60f3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.0_@types+react@19.0.1_react@19.0.0/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.0_@types+react@19.0.1_react@19.0.0/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1.1.0_@types+react@19.0.1_react@19.0.0/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // packages/react/popper/src/Popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"PopperAnchor.useEffect\": ()=>{\n            context.onAnchorChange(virtualRef?.current || ref.current);\n        }\n    }[\"PopperAnchor.useEffect\"]);\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"PopperContent.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"PopperContent.useComposedRefs[composedRefs]\"]);\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: {\n            \"PopperContent.useFloating\": (...args)=>{\n                const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                    animationFrame: updatePositionStrategy === \"always\"\n                });\n                return cleanup;\n            }\n        }[\"PopperContent.useFloating\"],\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: {\n                    \"PopperContent.useFloating\": ({ elements, rects, availableWidth, availableHeight })=>{\n                        const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                        const contentStyle = elements.floating.style;\n                        contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                        contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                        contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                        contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                    }\n                }[\"PopperContent.useFloating\"]\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"PopperContent.useLayoutEffect\": ()=>{\n            if (isPositioned) {\n                handlePlaced?.();\n            }\n        }\n    }[\"PopperContent.useLayoutEffect\"], [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"PopperContent.useLayoutEffect\": ()=>{\n            if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n        }\n    }[\"PopperContent.useLayoutEffect\"], [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+react@_0aa3ba0c619aac13136194a66f93d5be/node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ })

};
;