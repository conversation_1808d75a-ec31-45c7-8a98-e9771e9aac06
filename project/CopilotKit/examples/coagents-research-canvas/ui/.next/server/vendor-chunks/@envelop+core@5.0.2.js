"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@envelop+core@5.0.2";
exports.ids = ["vendor-chunks/@envelop+core@5.0.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/create.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/create.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.envelop = void 0;\nconst orchestrator_js_1 = __webpack_require__(/*! ./orchestrator.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/orchestrator.js\");\nfunction notEmpty(value) {\n    return value != null;\n}\nfunction envelop(options) {\n    const plugins = options.plugins.filter(notEmpty);\n    const orchestrator = (0, orchestrator_js_1.createEnvelopOrchestrator)({\n        plugins,\n    });\n    const getEnveloped = (initialContext = {}) => {\n        const typedOrchestrator = orchestrator;\n        typedOrchestrator.init(initialContext);\n        return {\n            parse: typedOrchestrator.parse(initialContext),\n            validate: typedOrchestrator.validate(initialContext),\n            contextFactory: typedOrchestrator.contextFactory(initialContext),\n            execute: typedOrchestrator.execute,\n            subscribe: typedOrchestrator.subscribe,\n            schema: typedOrchestrator.getCurrentSchema(),\n        };\n    };\n    getEnveloped._plugins = plugins;\n    return getEnveloped;\n}\nexports.envelop = envelop;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/create.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/document-string-map.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/document-string-map.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getDocumentString = exports.documentStringMap = void 0;\nexports.documentStringMap = new WeakMap();\nfunction getDocumentString(document, print) {\n    let documentSource = exports.documentStringMap.get(document);\n    if (!documentSource && print) {\n        documentSource = print(document);\n        exports.documentStringMap.set(document, documentSource);\n    }\n    return documentSource;\n}\nexports.getDocumentString = getDocumentString;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGVudmVsb3ArY29yZUA1LjAuMi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvZG9jdW1lbnQtc3RyaW5nLW1hcC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx5QkFBeUIsR0FBRyx5QkFBeUI7QUFDckQseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AZW52ZWxvcCtjb3JlQDUuMC4yL25vZGVfbW9kdWxlcy9AZW52ZWxvcC9jb3JlL2Nqcy9kb2N1bWVudC1zdHJpbmctbWFwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5nZXREb2N1bWVudFN0cmluZyA9IGV4cG9ydHMuZG9jdW1lbnRTdHJpbmdNYXAgPSB2b2lkIDA7XG5leHBvcnRzLmRvY3VtZW50U3RyaW5nTWFwID0gbmV3IFdlYWtNYXAoKTtcbmZ1bmN0aW9uIGdldERvY3VtZW50U3RyaW5nKGRvY3VtZW50LCBwcmludCkge1xuICAgIGxldCBkb2N1bWVudFNvdXJjZSA9IGV4cG9ydHMuZG9jdW1lbnRTdHJpbmdNYXAuZ2V0KGRvY3VtZW50KTtcbiAgICBpZiAoIWRvY3VtZW50U291cmNlICYmIHByaW50KSB7XG4gICAgICAgIGRvY3VtZW50U291cmNlID0gcHJpbnQoZG9jdW1lbnQpO1xuICAgICAgICBleHBvcnRzLmRvY3VtZW50U3RyaW5nTWFwLnNldChkb2N1bWVudCwgZG9jdW1lbnRTb3VyY2UpO1xuICAgIH1cbiAgICByZXR1cm4gZG9jdW1lbnRTb3VyY2U7XG59XG5leHBvcnRzLmdldERvY3VtZW50U3RyaW5nID0gZ2V0RG9jdW1lbnRTdHJpbmc7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/document-string-map.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/index.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/index.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getDocumentString = void 0;\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n// eslint-disable-next-line import/export\ntslib_1.__exportStar(__webpack_require__(/*! @envelop/types */ \"(rsc)/./node_modules/.pnpm/@envelop+types@5.0.0/node_modules/@envelop/types/cjs/index.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./create.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/create.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/utils.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-envelop.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-envelop.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-logger.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-logger.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-schema.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-schema.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-error-handler.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-error-handler.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-extend-context.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-extend-context.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-payload-formatter.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-payload-formatter.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-masked-errors.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-masked-errors.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-engine.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-engine.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/use-validation-rule.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-validation-rule.js\"), exports);\nvar document_string_map_js_1 = __webpack_require__(/*! ./document-string-map.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/document-string-map.js\");\nObject.defineProperty(exports, \"getDocumentString\", ({ enumerable: true, get: function () { return document_string_map_js_1.getDocumentString; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/orchestrator.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/orchestrator.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createEnvelopOrchestrator = void 0;\nconst document_string_map_js_1 = __webpack_require__(/*! ./document-string-map.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/document-string-map.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/utils.js\");\nfunction throwEngineFunctionError(name) {\n    throw Error(`No \\`${name}\\` function found! Register it using \"useEngine\" plugin.`);\n}\nfunction createEnvelopOrchestrator({ plugins, }) {\n    let schema = null;\n    let initDone = false;\n    const parse = () => throwEngineFunctionError('parse');\n    const validate = () => throwEngineFunctionError('validate');\n    const execute = () => throwEngineFunctionError('execute');\n    const subscribe = () => throwEngineFunctionError('subscribe');\n    // Define the initial method for replacing the GraphQL schema, this is needed in order\n    // to allow setting the schema from the onPluginInit callback. We also need to make sure\n    // here not to call the same plugin that initiated the schema switch.\n    const replaceSchema = (newSchema, ignorePluginIndex = -1) => {\n        schema = newSchema;\n        if (initDone) {\n            for (const [i, plugin] of plugins.entries()) {\n                if (i !== ignorePluginIndex) {\n                    plugin.onSchemaChange &&\n                        plugin.onSchemaChange({\n                            schema,\n                            replaceSchema: schemaToSet => {\n                                replaceSchema(schemaToSet, i);\n                            },\n                        });\n                }\n            }\n        }\n    };\n    const contextErrorHandlers = [];\n    // Iterate all plugins and trigger onPluginInit\n    for (let i = 0; i < plugins.length; i++) {\n        const plugin = plugins[i];\n        const pluginsToAdd = [];\n        plugin.onPluginInit &&\n            plugin.onPluginInit({\n                plugins,\n                addPlugin: newPlugin => {\n                    pluginsToAdd.push(newPlugin);\n                },\n                setSchema: modifiedSchema => replaceSchema(modifiedSchema, i),\n                registerContextErrorHandler: handler => contextErrorHandlers.push(handler),\n            });\n        pluginsToAdd.length && plugins.splice(i + 1, 0, ...pluginsToAdd);\n    }\n    // A set of before callbacks defined here in order to allow it to be used later\n    const beforeCallbacks = {\n        init: [],\n        parse: [],\n        validate: [],\n        subscribe: [],\n        execute: [],\n        context: [],\n    };\n    for (const { onContextBuilding, onExecute, onParse, onSubscribe, onValidate, onEnveloped, } of plugins) {\n        onEnveloped && beforeCallbacks.init.push(onEnveloped);\n        onContextBuilding && beforeCallbacks.context.push(onContextBuilding);\n        onExecute && beforeCallbacks.execute.push(onExecute);\n        onParse && beforeCallbacks.parse.push(onParse);\n        onSubscribe && beforeCallbacks.subscribe.push(onSubscribe);\n        onValidate && beforeCallbacks.validate.push(onValidate);\n    }\n    const init = initialContext => {\n        for (const [i, onEnveloped] of beforeCallbacks.init.entries()) {\n            onEnveloped({\n                context: initialContext,\n                extendContext: extension => {\n                    if (!initialContext) {\n                        return;\n                    }\n                    Object.assign(initialContext, extension);\n                },\n                setSchema: modifiedSchema => replaceSchema(modifiedSchema, i),\n            });\n        }\n    };\n    const customParse = beforeCallbacks.parse.length\n        ? initialContext => (source, parseOptions) => {\n            let result = null;\n            let parseFn = parse;\n            const context = initialContext;\n            const afterCalls = [];\n            for (const onParse of beforeCallbacks.parse) {\n                const afterFn = onParse({\n                    context,\n                    extendContext: extension => {\n                        Object.assign(context, extension);\n                    },\n                    params: { source, options: parseOptions },\n                    parseFn,\n                    setParseFn: newFn => {\n                        parseFn = newFn;\n                    },\n                    setParsedDocument: newDoc => {\n                        result = newDoc;\n                    },\n                });\n                afterFn && afterCalls.push(afterFn);\n            }\n            if (result === null) {\n                try {\n                    result = parseFn(source, parseOptions);\n                }\n                catch (e) {\n                    result = e;\n                }\n            }\n            for (const afterCb of afterCalls) {\n                afterCb({\n                    context,\n                    extendContext: extension => {\n                        Object.assign(context, extension);\n                    },\n                    replaceParseResult: newResult => {\n                        result = newResult;\n                    },\n                    result,\n                });\n            }\n            if (result === null) {\n                throw new Error(`Failed to parse document.`);\n            }\n            if (result instanceof Error) {\n                throw result;\n            }\n            document_string_map_js_1.documentStringMap.set(result, source.toString());\n            return result;\n        }\n        : () => parse;\n    const customValidate = beforeCallbacks.validate\n        .length\n        ? initialContext => (schema, documentAST, rules, typeInfo, validationOptions) => {\n            let actualRules = rules ? [...rules] : undefined;\n            let validateFn = validate;\n            let result = null;\n            const context = initialContext;\n            const afterCalls = [];\n            for (const onValidate of beforeCallbacks.validate) {\n                const afterFn = onValidate({\n                    context,\n                    extendContext: extension => {\n                        Object.assign(context, extension);\n                    },\n                    params: {\n                        schema,\n                        documentAST,\n                        rules: actualRules,\n                        typeInfo,\n                        options: validationOptions,\n                    },\n                    validateFn,\n                    addValidationRule: rule => {\n                        if (!actualRules) {\n                            actualRules = [];\n                        }\n                        actualRules.push(rule);\n                    },\n                    setValidationFn: newFn => {\n                        validateFn = newFn;\n                    },\n                    setResult: newResults => {\n                        result = newResults;\n                    },\n                });\n                afterFn && afterCalls.push(afterFn);\n            }\n            if (!result) {\n                result = validateFn(schema, documentAST, actualRules, typeInfo, validationOptions);\n            }\n            if (!result) {\n                return;\n            }\n            const valid = result.length === 0;\n            for (const afterCb of afterCalls) {\n                afterCb({\n                    valid,\n                    result,\n                    context,\n                    extendContext: extension => {\n                        Object.assign(context, extension);\n                    },\n                    setResult: newResult => {\n                        result = newResult;\n                    },\n                });\n            }\n            return result;\n        }\n        : () => validate;\n    const customContextFactory = beforeCallbacks.context.length\n        ? initialContext => async (orchestratorCtx) => {\n            const afterCalls = [];\n            // In order to have access to the \"last working\" context object we keep this outside of the try block:\n            const context = initialContext;\n            if (orchestratorCtx) {\n                Object.assign(context, orchestratorCtx);\n            }\n            try {\n                let isBreakingContextBuilding = false;\n                for (const onContext of beforeCallbacks.context) {\n                    const afterHookResult = await onContext({\n                        context,\n                        extendContext: extension => {\n                            Object.assign(context, extension);\n                        },\n                        breakContextBuilding: () => {\n                            isBreakingContextBuilding = true;\n                        },\n                    });\n                    if (typeof afterHookResult === 'function') {\n                        afterCalls.push(afterHookResult);\n                    }\n                    if (isBreakingContextBuilding === true) {\n                        break;\n                    }\n                }\n                for (const afterCb of afterCalls) {\n                    afterCb({\n                        context,\n                        extendContext: extension => {\n                            Object.assign(context, extension);\n                        },\n                    });\n                }\n                return context;\n            }\n            catch (err) {\n                let error = err;\n                for (const errorCb of contextErrorHandlers) {\n                    errorCb({\n                        context,\n                        error,\n                        setError: err => {\n                            error = err;\n                        },\n                    });\n                }\n                throw error;\n            }\n        }\n        : initialContext => orchestratorCtx => {\n            if (orchestratorCtx) {\n                Object.assign(initialContext, orchestratorCtx);\n            }\n            return initialContext;\n        };\n    const useCustomSubscribe = beforeCallbacks.subscribe.length;\n    const customSubscribe = useCustomSubscribe\n        ? (0, utils_js_1.makeSubscribe)(async (args) => {\n            let subscribeFn = subscribe;\n            const afterCalls = [];\n            const subscribeErrorHandlers = [];\n            const context = args.contextValue || {};\n            let result;\n            for (const onSubscribe of beforeCallbacks.subscribe) {\n                const after = await onSubscribe({\n                    subscribeFn,\n                    setSubscribeFn: newSubscribeFn => {\n                        subscribeFn = newSubscribeFn;\n                    },\n                    extendContext: extension => {\n                        Object.assign(context, extension);\n                    },\n                    args: args,\n                    setResultAndStopExecution: stopResult => {\n                        result = stopResult;\n                    },\n                });\n                if (after) {\n                    if (after.onSubscribeResult) {\n                        afterCalls.push(after.onSubscribeResult);\n                    }\n                    if (after.onSubscribeError) {\n                        subscribeErrorHandlers.push(after.onSubscribeError);\n                    }\n                }\n                if (result !== undefined) {\n                    break;\n                }\n            }\n            if (result === undefined) {\n                result = await subscribeFn({\n                    ...args,\n                    contextValue: context,\n                    // Casted for GraphQL.js 15 compatibility\n                    // Can be removed once we drop support for GraphQL.js 15\n                });\n            }\n            if (!result) {\n                return;\n            }\n            const onNextHandler = [];\n            const onEndHandler = [];\n            for (const afterCb of afterCalls) {\n                const hookResult = afterCb({\n                    args: args,\n                    result,\n                    setResult: newResult => {\n                        result = newResult;\n                    },\n                });\n                if (hookResult) {\n                    if (hookResult.onNext) {\n                        onNextHandler.push(hookResult.onNext);\n                    }\n                    if (hookResult.onEnd) {\n                        onEndHandler.push(hookResult.onEnd);\n                    }\n                }\n            }\n            if (onNextHandler.length && (0, utils_js_1.isAsyncIterable)(result)) {\n                result = (0, utils_js_1.mapAsyncIterator)(result, async (result) => {\n                    for (const onNext of onNextHandler) {\n                        await onNext({\n                            args: args,\n                            result,\n                            setResult: newResult => (result = newResult),\n                        });\n                    }\n                    return result;\n                });\n            }\n            if (onEndHandler.length && (0, utils_js_1.isAsyncIterable)(result)) {\n                result = (0, utils_js_1.finalAsyncIterator)(result, () => {\n                    for (const onEnd of onEndHandler) {\n                        onEnd();\n                    }\n                });\n            }\n            if (subscribeErrorHandlers.length && (0, utils_js_1.isAsyncIterable)(result)) {\n                result = (0, utils_js_1.errorAsyncIterator)(result, err => {\n                    let error = err;\n                    for (const handler of subscribeErrorHandlers) {\n                        handler({\n                            error,\n                            setError: err => {\n                                error = err;\n                            },\n                        });\n                    }\n                    throw error;\n                });\n            }\n            return result;\n        })\n        : (0, utils_js_1.makeSubscribe)(subscribe);\n    const useCustomExecute = beforeCallbacks.execute.length;\n    const customExecute = useCustomExecute\n        ? (0, utils_js_1.makeExecute)(async (args) => {\n            let executeFn = execute;\n            let result;\n            const afterCalls = [];\n            const context = args.contextValue || {};\n            for (const onExecute of beforeCallbacks.execute) {\n                const after = await onExecute({\n                    executeFn,\n                    setExecuteFn: newExecuteFn => {\n                        executeFn = newExecuteFn;\n                    },\n                    setResultAndStopExecution: stopResult => {\n                        result = stopResult;\n                    },\n                    extendContext: extension => {\n                        if (typeof extension === 'object') {\n                            Object.assign(context, extension);\n                        }\n                        else {\n                            throw new Error(`Invalid context extension provided! Expected \"object\", got: \"${JSON.stringify(extension)}\" (${typeof extension})`);\n                        }\n                    },\n                    args: args,\n                });\n                if (after?.onExecuteDone) {\n                    afterCalls.push(after.onExecuteDone);\n                }\n                if (result !== undefined) {\n                    break;\n                }\n            }\n            if (result === undefined) {\n                result = (await executeFn({\n                    ...args,\n                    contextValue: context,\n                }));\n            }\n            const onNextHandler = [];\n            const onEndHandler = [];\n            for (const afterCb of afterCalls) {\n                const hookResult = await afterCb({\n                    args: args,\n                    result,\n                    setResult: newResult => {\n                        result = newResult;\n                    },\n                });\n                if (hookResult) {\n                    if (hookResult.onNext) {\n                        onNextHandler.push(hookResult.onNext);\n                    }\n                    if (hookResult.onEnd) {\n                        onEndHandler.push(hookResult.onEnd);\n                    }\n                }\n            }\n            if (onNextHandler.length && (0, utils_js_1.isAsyncIterable)(result)) {\n                result = (0, utils_js_1.mapAsyncIterator)(result, async (result) => {\n                    for (const onNext of onNextHandler) {\n                        await onNext({\n                            args: args,\n                            result,\n                            setResult: newResult => {\n                                result = newResult;\n                            },\n                        });\n                    }\n                    return result;\n                });\n            }\n            if (onEndHandler.length && (0, utils_js_1.isAsyncIterable)(result)) {\n                result = (0, utils_js_1.finalAsyncIterator)(result, () => {\n                    for (const onEnd of onEndHandler) {\n                        onEnd();\n                    }\n                });\n            }\n            return result;\n        })\n        : (0, utils_js_1.makeExecute)(execute);\n    initDone = true;\n    // This is done in order to trigger the first schema available, to allow plugins that needs the schema\n    // eagerly to have it.\n    if (schema) {\n        for (const [i, plugin] of plugins.entries()) {\n            plugin.onSchemaChange &&\n                plugin.onSchemaChange({\n                    schema,\n                    replaceSchema: modifiedSchema => replaceSchema(modifiedSchema, i),\n                });\n        }\n    }\n    return {\n        getCurrentSchema() {\n            return schema;\n        },\n        init,\n        parse: customParse,\n        validate: customValidate,\n        execute: customExecute,\n        subscribe: customSubscribe,\n        contextFactory: customContextFactory,\n    };\n}\nexports.createEnvelopOrchestrator = createEnvelopOrchestrator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/orchestrator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-engine.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-engine.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useEngine = void 0;\nconst useEngine = (engine) => {\n    return {\n        onExecute: ({ setExecuteFn }) => {\n            if (engine.execute) {\n                setExecuteFn(engine.execute);\n            }\n        },\n        onParse: ({ setParseFn }) => {\n            if (engine.parse) {\n                setParseFn(engine.parse);\n            }\n        },\n        onValidate: ({ setValidationFn, addValidationRule }) => {\n            if (engine.validate) {\n                setValidationFn(engine.validate);\n            }\n            engine.specifiedRules?.map(addValidationRule);\n        },\n        onSubscribe: ({ setSubscribeFn }) => {\n            if (engine.subscribe) {\n                setSubscribeFn(engine.subscribe);\n            }\n        },\n    };\n};\nexports.useEngine = useEngine;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-engine.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-envelop.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-envelop.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useEnvelop = void 0;\nconst useEnvelop = (envelop) => {\n    let initialized = false;\n    return {\n        onPluginInit({ addPlugin }) {\n            if (initialized) {\n                return;\n            }\n            for (const plugin of envelop._plugins) {\n                addPlugin(plugin);\n            }\n            // Avoid double execution if envelop is extended multiple times\n            initialized = true;\n        },\n    };\n};\nexports.useEnvelop = useEnvelop;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGVudmVsb3ArY29yZUA1LjAuMi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2UtZW52ZWxvcC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLFdBQVc7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0Esa0JBQWtCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGVudmVsb3ArY29yZUA1LjAuMi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2UtZW52ZWxvcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMudXNlRW52ZWxvcCA9IHZvaWQgMDtcbmNvbnN0IHVzZUVudmVsb3AgPSAoZW52ZWxvcCkgPT4ge1xuICAgIGxldCBpbml0aWFsaXplZCA9IGZhbHNlO1xuICAgIHJldHVybiB7XG4gICAgICAgIG9uUGx1Z2luSW5pdCh7IGFkZFBsdWdpbiB9KSB7XG4gICAgICAgICAgICBpZiAoaW5pdGlhbGl6ZWQpIHtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmb3IgKGNvbnN0IHBsdWdpbiBvZiBlbnZlbG9wLl9wbHVnaW5zKSB7XG4gICAgICAgICAgICAgICAgYWRkUGx1Z2luKHBsdWdpbik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBBdm9pZCBkb3VibGUgZXhlY3V0aW9uIGlmIGVudmVsb3AgaXMgZXh0ZW5kZWQgbXVsdGlwbGUgdGltZXNcbiAgICAgICAgICAgIGluaXRpYWxpemVkID0gdHJ1ZTtcbiAgICAgICAgfSxcbiAgICB9O1xufTtcbmV4cG9ydHMudXNlRW52ZWxvcCA9IHVzZUVudmVsb3A7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-envelop.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-error-handler.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-error-handler.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useErrorHandler = void 0;\nconst utils_js_1 = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/utils.js\");\nconst use_masked_errors_js_1 = __webpack_require__(/*! ./use-masked-errors.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-masked-errors.js\");\nconst makeHandleResult = (errorHandler) => ({ result, args }) => {\n    if (result.errors?.length) {\n        errorHandler({ errors: result.errors, context: args, phase: 'execution' });\n    }\n};\nconst useErrorHandler = (errorHandler) => {\n    const handleResult = makeHandleResult(errorHandler);\n    return {\n        onParse() {\n            return function onParseEnd({ result, context }) {\n                if (result instanceof Error) {\n                    errorHandler({ errors: [result], context, phase: 'parse' });\n                }\n            };\n        },\n        onValidate() {\n            return function onValidateEnd({ valid, result, context }) {\n                if (valid === false && result.length > 0) {\n                    errorHandler({ errors: result, context, phase: 'validate' });\n                }\n            };\n        },\n        onPluginInit(context) {\n            context.registerContextErrorHandler(({ error }) => {\n                if ((0, use_masked_errors_js_1.isGraphQLError)(error)) {\n                    errorHandler({ errors: [error], context, phase: 'context' });\n                }\n                else {\n                    // @ts-expect-error its not an error at this point so we just create a new one - can we handle this better?\n                    errorHandler({ errors: [new Error(error)], context, phase: 'context' });\n                }\n            });\n        },\n        onExecute() {\n            return {\n                onExecuteDone(payload) {\n                    return (0, utils_js_1.handleStreamOrSingleExecutionResult)(payload, handleResult);\n                },\n            };\n        },\n        onSubscribe() {\n            return {\n                onSubscribeResult(payload) {\n                    return (0, utils_js_1.handleStreamOrSingleExecutionResult)(payload, handleResult);\n                },\n            };\n        },\n    };\n};\nexports.useErrorHandler = useErrorHandler;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-error-handler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-extend-context.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-extend-context.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useExtendContext = void 0;\nconst useExtendContext = (contextFactory) => ({\n    async onContextBuilding({ context, extendContext }) {\n        extendContext((await contextFactory(context)));\n    },\n});\nexports.useExtendContext = useExtendContext;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGVudmVsb3ArY29yZUA1LjAuMi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2UtZXh0ZW5kLWNvbnRleHQuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsd0JBQXdCO0FBQ3hCO0FBQ0EsOEJBQThCLHdCQUF3QjtBQUN0RDtBQUNBLEtBQUs7QUFDTCxDQUFDO0FBQ0Qsd0JBQXdCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGVudmVsb3ArY29yZUA1LjAuMi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2UtZXh0ZW5kLWNvbnRleHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnVzZUV4dGVuZENvbnRleHQgPSB2b2lkIDA7XG5jb25zdCB1c2VFeHRlbmRDb250ZXh0ID0gKGNvbnRleHRGYWN0b3J5KSA9PiAoe1xuICAgIGFzeW5jIG9uQ29udGV4dEJ1aWxkaW5nKHsgY29udGV4dCwgZXh0ZW5kQ29udGV4dCB9KSB7XG4gICAgICAgIGV4dGVuZENvbnRleHQoKGF3YWl0IGNvbnRleHRGYWN0b3J5KGNvbnRleHQpKSk7XG4gICAgfSxcbn0pO1xuZXhwb3J0cy51c2VFeHRlbmRDb250ZXh0ID0gdXNlRXh0ZW5kQ29udGV4dDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-extend-context.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-logger.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-logger.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useLogger = void 0;\nconst utils_js_1 = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/utils.js\");\nconst DEFAULT_OPTIONS = {\n    logFn: console.log,\n};\nconst useLogger = (rawOptions = DEFAULT_OPTIONS) => {\n    const options = {\n        DEFAULT_OPTIONS,\n        ...rawOptions,\n    };\n    return {\n        onParse({ extendContext, params }) {\n            if (options.skipIntrospection && (0, utils_js_1.isIntrospectionOperationString)(params.source)) {\n                extendContext({\n                    [utils_js_1.envelopIsIntrospectionSymbol]: true,\n                });\n            }\n        },\n        onExecute({ args }) {\n            if (args.contextValue[utils_js_1.envelopIsIntrospectionSymbol]) {\n                return;\n            }\n            options.logFn('execute-start', { args });\n            return {\n                onExecuteDone: ({ result }) => {\n                    options.logFn('execute-end', { args, result });\n                },\n            };\n        },\n        onSubscribe({ args }) {\n            if (args.contextValue[utils_js_1.envelopIsIntrospectionSymbol]) {\n                return;\n            }\n            options.logFn('subscribe-start', { args });\n            return {\n                onSubscribeResult: ({ result }) => {\n                    options.logFn('subscribe-end', { args, result });\n                },\n            };\n        },\n    };\n};\nexports.useLogger = useLogger;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-logger.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-masked-errors.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-masked-errors.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useMaskedErrors = exports.defaultMaskError = exports.createDefaultMaskError = exports.isOriginalGraphQLError = exports.isGraphQLError = exports.DEFAULT_ERROR_MESSAGE = void 0;\nconst utils_js_1 = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/utils.js\");\nexports.DEFAULT_ERROR_MESSAGE = 'Unexpected error.';\nfunction isGraphQLError(error) {\n    return error instanceof Error && error.name === 'GraphQLError';\n}\nexports.isGraphQLError = isGraphQLError;\nfunction isOriginalGraphQLError(error) {\n    if (isGraphQLError(error)) {\n        if (error.originalError != null) {\n            return isOriginalGraphQLError(error.originalError);\n        }\n        return true;\n    }\n    return false;\n}\nexports.isOriginalGraphQLError = isOriginalGraphQLError;\nfunction createSerializableGraphQLError(message, originalError, isDev) {\n    const error = new Error(message);\n    error.name = 'GraphQLError';\n    if (isDev) {\n        const extensions = originalError instanceof Error\n            ? { message: originalError.message, stack: originalError.stack }\n            : { message: String(originalError) };\n        Object.defineProperty(error, 'extensions', {\n            get() {\n                return extensions;\n            },\n        });\n    }\n    Object.defineProperty(error, 'toJSON', {\n        value() {\n            return {\n                message: error.message,\n                extensions: error.extensions,\n            };\n        },\n    });\n    return error;\n}\nconst createDefaultMaskError = (isDev) => (error, message) => {\n    if (isOriginalGraphQLError(error)) {\n        return error;\n    }\n    return createSerializableGraphQLError(message, error, isDev);\n};\nexports.createDefaultMaskError = createDefaultMaskError;\nconst isDev = globalThis.process?.env?.NODE_ENV === 'development';\nexports.defaultMaskError = (0, exports.createDefaultMaskError)(isDev);\nconst makeHandleResult = (maskError, message) => ({ result, setResult, }) => {\n    if (result.errors != null) {\n        setResult({ ...result, errors: result.errors.map(error => maskError(error, message)) });\n    }\n};\nfunction useMaskedErrors(opts) {\n    const maskError = opts?.maskError ?? exports.defaultMaskError;\n    const message = opts?.errorMessage || exports.DEFAULT_ERROR_MESSAGE;\n    const handleResult = makeHandleResult(maskError, message);\n    return {\n        onPluginInit(context) {\n            context.registerContextErrorHandler(({ error, setError }) => {\n                setError(maskError(error, message));\n            });\n        },\n        onExecute() {\n            return {\n                onExecuteDone(payload) {\n                    return (0, utils_js_1.handleStreamOrSingleExecutionResult)(payload, handleResult);\n                },\n            };\n        },\n        onSubscribe() {\n            return {\n                onSubscribeResult(payload) {\n                    return (0, utils_js_1.handleStreamOrSingleExecutionResult)(payload, handleResult);\n                },\n                onSubscribeError({ error, setError }) {\n                    setError(maskError(error, message));\n                },\n            };\n        },\n    };\n}\nexports.useMaskedErrors = useMaskedErrors;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-masked-errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-payload-formatter.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-payload-formatter.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.usePayloadFormatter = void 0;\nconst utils_js_1 = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/utils.js\");\nconst makeHandleResult = (formatter) => ({ args, result, setResult, }) => {\n    const modified = formatter(result, args);\n    if (modified !== false) {\n        setResult(modified);\n    }\n};\nconst usePayloadFormatter = (formatter) => ({\n    onExecute() {\n        const handleResult = makeHandleResult(formatter);\n        return {\n            onExecuteDone(payload) {\n                return (0, utils_js_1.handleStreamOrSingleExecutionResult)(payload, handleResult);\n            },\n        };\n    },\n});\nexports.usePayloadFormatter = usePayloadFormatter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGVudmVsb3ArY29yZUA1LjAuMi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2UtcGF5bG9hZC1mb3JtYXR0ZXIuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsMkJBQTJCO0FBQzNCLG1CQUFtQixtQkFBTyxDQUFDLDJHQUFhO0FBQ3hDLDJDQUEyQywwQkFBMEI7QUFDckU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLEtBQUs7QUFDTCxDQUFDO0FBQ0QsMkJBQTJCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGVudmVsb3ArY29yZUA1LjAuMi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2UtcGF5bG9hZC1mb3JtYXR0ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnVzZVBheWxvYWRGb3JtYXR0ZXIgPSB2b2lkIDA7XG5jb25zdCB1dGlsc19qc18xID0gcmVxdWlyZShcIi4uL3V0aWxzLmpzXCIpO1xuY29uc3QgbWFrZUhhbmRsZVJlc3VsdCA9IChmb3JtYXR0ZXIpID0+ICh7IGFyZ3MsIHJlc3VsdCwgc2V0UmVzdWx0LCB9KSA9PiB7XG4gICAgY29uc3QgbW9kaWZpZWQgPSBmb3JtYXR0ZXIocmVzdWx0LCBhcmdzKTtcbiAgICBpZiAobW9kaWZpZWQgIT09IGZhbHNlKSB7XG4gICAgICAgIHNldFJlc3VsdChtb2RpZmllZCk7XG4gICAgfVxufTtcbmNvbnN0IHVzZVBheWxvYWRGb3JtYXR0ZXIgPSAoZm9ybWF0dGVyKSA9PiAoe1xuICAgIG9uRXhlY3V0ZSgpIHtcbiAgICAgICAgY29uc3QgaGFuZGxlUmVzdWx0ID0gbWFrZUhhbmRsZVJlc3VsdChmb3JtYXR0ZXIpO1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgb25FeGVjdXRlRG9uZShwYXlsb2FkKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuICgwLCB1dGlsc19qc18xLmhhbmRsZVN0cmVhbU9yU2luZ2xlRXhlY3V0aW9uUmVzdWx0KShwYXlsb2FkLCBoYW5kbGVSZXN1bHQpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfTtcbiAgICB9LFxufSk7XG5leHBvcnRzLnVzZVBheWxvYWRGb3JtYXR0ZXIgPSB1c2VQYXlsb2FkRm9ybWF0dGVyO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-payload-formatter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-schema.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-schema.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useSchemaByContext = exports.useSchema = void 0;\nconst useSchema = (schema) => {\n    return {\n        onPluginInit({ setSchema }) {\n            setSchema(schema);\n        },\n    };\n};\nexports.useSchema = useSchema;\nconst useSchemaByContext = (schemaLoader) => {\n    return {\n        onEnveloped({ setSchema, context }) {\n            setSchema(schemaLoader(context));\n        },\n    };\n};\nexports.useSchemaByContext = useSchemaByContext;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGVudmVsb3ArY29yZUA1LjAuMi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2Utc2NoZW1hLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDBCQUEwQixHQUFHLGlCQUFpQjtBQUM5QztBQUNBO0FBQ0EsdUJBQXVCLFdBQVc7QUFDbEM7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0Esc0JBQXNCLG9CQUFvQjtBQUMxQztBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsMEJBQTBCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGVudmVsb3ArY29yZUA1LjAuMi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2Utc2NoZW1hLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy51c2VTY2hlbWFCeUNvbnRleHQgPSBleHBvcnRzLnVzZVNjaGVtYSA9IHZvaWQgMDtcbmNvbnN0IHVzZVNjaGVtYSA9IChzY2hlbWEpID0+IHtcbiAgICByZXR1cm4ge1xuICAgICAgICBvblBsdWdpbkluaXQoeyBzZXRTY2hlbWEgfSkge1xuICAgICAgICAgICAgc2V0U2NoZW1hKHNjaGVtYSk7XG4gICAgICAgIH0sXG4gICAgfTtcbn07XG5leHBvcnRzLnVzZVNjaGVtYSA9IHVzZVNjaGVtYTtcbmNvbnN0IHVzZVNjaGVtYUJ5Q29udGV4dCA9IChzY2hlbWFMb2FkZXIpID0+IHtcbiAgICByZXR1cm4ge1xuICAgICAgICBvbkVudmVsb3BlZCh7IHNldFNjaGVtYSwgY29udGV4dCB9KSB7XG4gICAgICAgICAgICBzZXRTY2hlbWEoc2NoZW1hTG9hZGVyKGNvbnRleHQpKTtcbiAgICAgICAgfSxcbiAgICB9O1xufTtcbmV4cG9ydHMudXNlU2NoZW1hQnlDb250ZXh0ID0gdXNlU2NoZW1hQnlDb250ZXh0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-schema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-validation-rule.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-validation-rule.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useValidationRule = void 0;\nconst useValidationRule = (rule) => {\n    return {\n        onValidate({ addValidationRule }) {\n            addValidationRule(rule);\n        },\n    };\n};\nexports.useValidationRule = useValidationRule;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGVudmVsb3ArY29yZUA1LjAuMi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2UtdmFsaWRhdGlvbi1ydWxlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0EscUJBQXFCLG1CQUFtQjtBQUN4QztBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EseUJBQXlCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGVudmVsb3ArY29yZUA1LjAuMi9ub2RlX21vZHVsZXMvQGVudmVsb3AvY29yZS9janMvcGx1Z2lucy91c2UtdmFsaWRhdGlvbi1ydWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy51c2VWYWxpZGF0aW9uUnVsZSA9IHZvaWQgMDtcbmNvbnN0IHVzZVZhbGlkYXRpb25SdWxlID0gKHJ1bGUpID0+IHtcbiAgICByZXR1cm4ge1xuICAgICAgICBvblZhbGlkYXRlKHsgYWRkVmFsaWRhdGlvblJ1bGUgfSkge1xuICAgICAgICAgICAgYWRkVmFsaWRhdGlvblJ1bGUocnVsZSk7XG4gICAgICAgIH0sXG4gICAgfTtcbn07XG5leHBvcnRzLnVzZVZhbGlkYXRpb25SdWxlID0gdXNlVmFsaWRhdGlvblJ1bGU7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/plugins/use-validation-rule.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/utils.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/utils.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.mapMaybePromise = exports.isPromise = exports.errorAsyncIterator = exports.finalAsyncIterator = exports.handleStreamOrSingleExecutionResult = exports.isAsyncIterable = exports.makeExecute = exports.mapAsyncIterator = exports.makeSubscribe = exports.isIntrospectionOperationString = exports.envelopIsIntrospectionSymbol = void 0;\nexports.envelopIsIntrospectionSymbol = Symbol('ENVELOP_IS_INTROSPECTION');\nfunction isIntrospectionOperationString(operation) {\n    return (typeof operation === 'string' ? operation : operation.body).indexOf('__schema') !== -1;\n}\nexports.isIntrospectionOperationString = isIntrospectionOperationString;\nfunction getSubscribeArgs(args) {\n    return args.length === 1\n        ? args[0]\n        : {\n            schema: args[0],\n            document: args[1],\n            rootValue: args[2],\n            contextValue: args[3],\n            variableValues: args[4],\n            operationName: args[5],\n            fieldResolver: args[6],\n            subscribeFieldResolver: args[7],\n        };\n}\n/**\n * Utility function for making a subscribe function that handles polymorphic arguments.\n */\nconst makeSubscribe = (subscribeFn) => ((...polyArgs) => subscribeFn(getSubscribeArgs(polyArgs)));\nexports.makeSubscribe = makeSubscribe;\nfunction mapAsyncIterator(source, mapper) {\n    const iterator = source[Symbol.asyncIterator]();\n    async function mapResult(result) {\n        if (result.done) {\n            return result;\n        }\n        try {\n            return { value: await mapper(result.value), done: false };\n        }\n        catch (error) {\n            try {\n                await iterator.return?.();\n            }\n            catch (_error) {\n                /* ignore error */\n            }\n            throw error;\n        }\n    }\n    const stream = {\n        [Symbol.asyncIterator]() {\n            return stream;\n        },\n        async next() {\n            return await mapResult(await iterator.next());\n        },\n        async return() {\n            const promise = iterator.return?.();\n            return promise ? await mapResult(await promise) : { value: undefined, done: true };\n        },\n        async throw(error) {\n            const promise = iterator.throw?.();\n            if (promise) {\n                return await mapResult(await promise);\n            }\n            // if the source has no throw method we just re-throw error\n            // usually throw is not called anyways\n            throw error;\n        },\n    };\n    return stream;\n}\nexports.mapAsyncIterator = mapAsyncIterator;\nfunction getExecuteArgs(args) {\n    return args.length === 1\n        ? args[0]\n        : {\n            schema: args[0],\n            document: args[1],\n            rootValue: args[2],\n            contextValue: args[3],\n            variableValues: args[4],\n            operationName: args[5],\n            fieldResolver: args[6],\n            typeResolver: args[7],\n        };\n}\n/**\n * Utility function for making a execute function that handles polymorphic arguments.\n */\nconst makeExecute = (executeFn) => ((...polyArgs) => executeFn(getExecuteArgs(polyArgs)));\nexports.makeExecute = makeExecute;\n/**\n * Returns true if the provided object implements the AsyncIterator protocol via\n * implementing a `Symbol.asyncIterator` method.\n *\n * Source: https://github.com/graphql/graphql-js/blob/main/src/jsutils/isAsyncIterable.ts\n */\nfunction isAsyncIterable(maybeAsyncIterable) {\n    return (typeof maybeAsyncIterable === 'object' &&\n        maybeAsyncIterable != null &&\n        typeof maybeAsyncIterable[Symbol.asyncIterator] === 'function');\n}\nexports.isAsyncIterable = isAsyncIterable;\n/**\n * A utility function for handling `onExecuteDone` hook result, for simplifying the handling of AsyncIterable returned from `execute`.\n *\n * @param payload The payload send to `onExecuteDone` hook function\n * @param fn The handler to be executed on each result\n * @returns a subscription for streamed results, or undefined in case of an non-async\n */\nfunction handleStreamOrSingleExecutionResult(payload, fn) {\n    if (isAsyncIterable(payload.result)) {\n        return { onNext: fn };\n    }\n    fn({\n        args: payload.args,\n        result: payload.result,\n        setResult: payload.setResult,\n    });\n    return undefined;\n}\nexports.handleStreamOrSingleExecutionResult = handleStreamOrSingleExecutionResult;\nfunction finalAsyncIterator(source, onFinal) {\n    const iterator = source[Symbol.asyncIterator]();\n    let isDone = false;\n    const stream = {\n        [Symbol.asyncIterator]() {\n            return stream;\n        },\n        async next() {\n            const result = await iterator.next();\n            if (result.done && isDone === false) {\n                isDone = true;\n                onFinal();\n            }\n            return result;\n        },\n        async return() {\n            const promise = iterator.return?.();\n            if (isDone === false) {\n                isDone = true;\n                onFinal();\n            }\n            return promise ? await promise : { done: true, value: undefined };\n        },\n        async throw(error) {\n            const promise = iterator.throw?.();\n            if (promise) {\n                return await promise;\n            }\n            // if the source has no throw method we just re-throw error\n            // usually throw is not called anyways\n            throw error;\n        },\n    };\n    return stream;\n}\nexports.finalAsyncIterator = finalAsyncIterator;\nfunction errorAsyncIterator(source, onError) {\n    const iterator = source[Symbol.asyncIterator]();\n    const stream = {\n        [Symbol.asyncIterator]() {\n            return stream;\n        },\n        async next() {\n            try {\n                return await iterator.next();\n            }\n            catch (error) {\n                onError(error);\n                return { done: true, value: undefined };\n            }\n        },\n        async return() {\n            const promise = iterator.return?.();\n            return promise ? await promise : { done: true, value: undefined };\n        },\n        async throw(error) {\n            const promise = iterator.throw?.();\n            if (promise) {\n                return await promise;\n            }\n            // if the source has no throw method we just re-throw error\n            // usually throw is not called anyways\n            throw error;\n        },\n    };\n    return stream;\n}\nexports.errorAsyncIterator = errorAsyncIterator;\nfunction isPromise(value) {\n    return value?.then !== undefined;\n}\nexports.isPromise = isPromise;\nfunction mapMaybePromise(value, mapper, errorMapper) {\n    if (isPromise(value)) {\n        if (errorMapper) {\n            try {\n                return value.then(mapper, errorMapper);\n            }\n            catch (e) {\n                return errorMapper(e);\n            }\n        }\n        return value.then(mapper);\n    }\n    if (errorMapper) {\n        try {\n            return mapper(value);\n        }\n        catch (e) {\n            return errorMapper(e);\n        }\n    }\n    return mapper(value);\n}\nexports.mapMaybePromise = mapMaybePromise;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@envelop+core@5.0.2/node_modules/@envelop/core/cjs/utils.js\n");

/***/ })

};
;