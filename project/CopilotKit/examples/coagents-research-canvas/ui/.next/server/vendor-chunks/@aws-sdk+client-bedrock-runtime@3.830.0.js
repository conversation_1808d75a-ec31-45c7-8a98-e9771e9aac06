"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+client-bedrock-runtime@3.830.0";
exports.ids = ["vendor-chunks/@aws-sdk+client-bedrock-runtime@3.830.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/BedrockRuntime.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/BedrockRuntime.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BedrockRuntime: () => (/* binding */ BedrockRuntime)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _BedrockRuntimeClient__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./BedrockRuntimeClient */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/BedrockRuntimeClient.js\");\n/* harmony import */ var _commands_ApplyGuardrailCommand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./commands/ApplyGuardrailCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ApplyGuardrailCommand.js\");\n/* harmony import */ var _commands_ConverseCommand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./commands/ConverseCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ConverseCommand.js\");\n/* harmony import */ var _commands_ConverseStreamCommand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./commands/ConverseStreamCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ConverseStreamCommand.js\");\n/* harmony import */ var _commands_GetAsyncInvokeCommand__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./commands/GetAsyncInvokeCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/GetAsyncInvokeCommand.js\");\n/* harmony import */ var _commands_InvokeModelCommand__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./commands/InvokeModelCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelCommand.js\");\n/* harmony import */ var _commands_InvokeModelWithBidirectionalStreamCommand__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./commands/InvokeModelWithBidirectionalStreamCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelWithBidirectionalStreamCommand.js\");\n/* harmony import */ var _commands_InvokeModelWithResponseStreamCommand__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./commands/InvokeModelWithResponseStreamCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelWithResponseStreamCommand.js\");\n/* harmony import */ var _commands_ListAsyncInvokesCommand__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./commands/ListAsyncInvokesCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ListAsyncInvokesCommand.js\");\n/* harmony import */ var _commands_StartAsyncInvokeCommand__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./commands/StartAsyncInvokeCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/StartAsyncInvokeCommand.js\");\n\n\n\n\n\n\n\n\n\n\n\nconst commands = {\n    ApplyGuardrailCommand: _commands_ApplyGuardrailCommand__WEBPACK_IMPORTED_MODULE_1__.ApplyGuardrailCommand,\n    ConverseCommand: _commands_ConverseCommand__WEBPACK_IMPORTED_MODULE_2__.ConverseCommand,\n    ConverseStreamCommand: _commands_ConverseStreamCommand__WEBPACK_IMPORTED_MODULE_3__.ConverseStreamCommand,\n    GetAsyncInvokeCommand: _commands_GetAsyncInvokeCommand__WEBPACK_IMPORTED_MODULE_4__.GetAsyncInvokeCommand,\n    InvokeModelCommand: _commands_InvokeModelCommand__WEBPACK_IMPORTED_MODULE_5__.InvokeModelCommand,\n    InvokeModelWithBidirectionalStreamCommand: _commands_InvokeModelWithBidirectionalStreamCommand__WEBPACK_IMPORTED_MODULE_6__.InvokeModelWithBidirectionalStreamCommand,\n    InvokeModelWithResponseStreamCommand: _commands_InvokeModelWithResponseStreamCommand__WEBPACK_IMPORTED_MODULE_7__.InvokeModelWithResponseStreamCommand,\n    ListAsyncInvokesCommand: _commands_ListAsyncInvokesCommand__WEBPACK_IMPORTED_MODULE_8__.ListAsyncInvokesCommand,\n    StartAsyncInvokeCommand: _commands_StartAsyncInvokeCommand__WEBPACK_IMPORTED_MODULE_9__.StartAsyncInvokeCommand,\n};\nclass BedrockRuntime extends _BedrockRuntimeClient__WEBPACK_IMPORTED_MODULE_10__.BedrockRuntimeClient {\n}\n(0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.createAggregatedClient)(commands, BedrockRuntime);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/BedrockRuntime.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/BedrockRuntimeClient.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/BedrockRuntimeClient.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BedrockRuntimeClient: () => (/* binding */ BedrockRuntimeClient),\n/* harmony export */   __Client: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_11__.Client)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_middleware_eventstream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/middleware-eventstream */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_middleware_host_header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/middleware-host-header */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-host-header@3.821.0/node_modules/@aws-sdk/middleware-host-header/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_middleware_logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/middleware-logger */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-logger@3.821.0/node_modules/@aws-sdk/middleware-logger/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_middleware_recursion_detection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @aws-sdk/middleware-recursion-detection */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-recursion-detection@3.821.0/node_modules/@aws-sdk/middleware-recursion-detection/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @aws-sdk/middleware-user-agent */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/index.js\");\n/* harmony import */ var _smithy_config_resolver__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @smithy/config-resolver */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_eventstream_serde_config_resolver__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @smithy/eventstream-serde-config-resolver */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-config-resolver@4.1.2/node_modules/@smithy/eventstream-serde-config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_content_length__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @smithy/middleware-content-length */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-content-length@4.0.4/node_modules/@smithy/middleware-content-length/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @smithy/middleware-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./auth/httpAuthSchemeProvider */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/auth/httpAuthSchemeProvider.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/EndpointParameters.js\");\n/* harmony import */ var _runtimeConfig__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./runtimeConfig */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/runtimeConfig.js\");\n/* harmony import */ var _runtimeExtensions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./runtimeExtensions */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/runtimeExtensions.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nclass BedrockRuntimeClient extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_11__.Client {\n    config;\n    constructor(...[configuration]) {\n        const _config_0 = (0,_runtimeConfig__WEBPACK_IMPORTED_MODULE_12__.getRuntimeConfig)(configuration || {});\n        super(_config_0);\n        this.initConfig = _config_0;\n        const _config_1 = (0,_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_13__.resolveClientEndpointParameters)(_config_0);\n        const _config_2 = (0,_aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_4__.resolveUserAgentConfig)(_config_1);\n        const _config_3 = (0,_smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_10__.resolveRetryConfig)(_config_2);\n        const _config_4 = (0,_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_5__.resolveRegionConfig)(_config_3);\n        const _config_5 = (0,_aws_sdk_middleware_host_header__WEBPACK_IMPORTED_MODULE_1__.resolveHostHeaderConfig)(_config_4);\n        const _config_6 = (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_9__.resolveEndpointConfig)(_config_5);\n        const _config_7 = (0,_smithy_eventstream_serde_config_resolver__WEBPACK_IMPORTED_MODULE_7__.resolveEventStreamSerdeConfig)(_config_6);\n        const _config_8 = (0,_auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_14__.resolveHttpAuthSchemeConfig)(_config_7);\n        const _config_9 = (0,_aws_sdk_middleware_eventstream__WEBPACK_IMPORTED_MODULE_0__.resolveEventStreamConfig)(_config_8);\n        const _config_10 = (0,_runtimeExtensions__WEBPACK_IMPORTED_MODULE_15__.resolveRuntimeExtensions)(_config_9, configuration?.extensions || []);\n        this.config = _config_10;\n        this.middlewareStack.use((0,_aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_4__.getUserAgentPlugin)(this.config));\n        this.middlewareStack.use((0,_smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_10__.getRetryPlugin)(this.config));\n        this.middlewareStack.use((0,_smithy_middleware_content_length__WEBPACK_IMPORTED_MODULE_8__.getContentLengthPlugin)(this.config));\n        this.middlewareStack.use((0,_aws_sdk_middleware_host_header__WEBPACK_IMPORTED_MODULE_1__.getHostHeaderPlugin)(this.config));\n        this.middlewareStack.use((0,_aws_sdk_middleware_logger__WEBPACK_IMPORTED_MODULE_2__.getLoggerPlugin)(this.config));\n        this.middlewareStack.use((0,_aws_sdk_middleware_recursion_detection__WEBPACK_IMPORTED_MODULE_3__.getRecursionDetectionPlugin)(this.config));\n        this.middlewareStack.use((0,_smithy_core__WEBPACK_IMPORTED_MODULE_6__.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config, {\n            httpAuthSchemeParametersProvider: _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_14__.defaultBedrockRuntimeHttpAuthSchemeParametersProvider,\n            identityProviderConfigProvider: async (config) => new _smithy_core__WEBPACK_IMPORTED_MODULE_6__.DefaultIdentityProviderConfig({\n                \"aws.auth#sigv4\": config.credentials,\n            }),\n        }));\n        this.middlewareStack.use((0,_smithy_core__WEBPACK_IMPORTED_MODULE_6__.getHttpSigningPlugin)(this.config));\n    }\n    destroy() {\n        super.destroy();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/BedrockRuntimeClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/auth/httpAuthExtensionConfiguration.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/auth/httpAuthExtensionConfiguration.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHttpAuthExtensionConfiguration: () => (/* binding */ getHttpAuthExtensionConfiguration),\n/* harmony export */   resolveHttpAuthRuntimeConfig: () => (/* binding */ resolveHttpAuthRuntimeConfig)\n/* harmony export */ });\nconst getHttpAuthExtensionConfiguration = (runtimeConfig) => {\n    const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;\n    let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;\n    let _credentials = runtimeConfig.credentials;\n    return {\n        setHttpAuthScheme(httpAuthScheme) {\n            const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);\n            if (index === -1) {\n                _httpAuthSchemes.push(httpAuthScheme);\n            }\n            else {\n                _httpAuthSchemes.splice(index, 1, httpAuthScheme);\n            }\n        },\n        httpAuthSchemes() {\n            return _httpAuthSchemes;\n        },\n        setHttpAuthSchemeProvider(httpAuthSchemeProvider) {\n            _httpAuthSchemeProvider = httpAuthSchemeProvider;\n        },\n        httpAuthSchemeProvider() {\n            return _httpAuthSchemeProvider;\n        },\n        setCredentials(credentials) {\n            _credentials = credentials;\n        },\n        credentials() {\n            return _credentials;\n        },\n    };\n};\nconst resolveHttpAuthRuntimeConfig = (config) => {\n    return {\n        httpAuthSchemes: config.httpAuthSchemes(),\n        httpAuthSchemeProvider: config.httpAuthSchemeProvider(),\n        credentials: config.credentials(),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/auth/httpAuthExtensionConfiguration.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/auth/httpAuthSchemeProvider.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/auth/httpAuthSchemeProvider.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultBedrockRuntimeHttpAuthSchemeParametersProvider: () => (/* binding */ defaultBedrockRuntimeHttpAuthSchemeParametersProvider),\n/* harmony export */   defaultBedrockRuntimeHttpAuthSchemeProvider: () => (/* binding */ defaultBedrockRuntimeHttpAuthSchemeProvider),\n/* harmony export */   resolveHttpAuthSchemeConfig: () => (/* binding */ resolveHttpAuthSchemeConfig)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/resolveAwsSdkSigV4Config.js\");\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n\n\nconst defaultBedrockRuntimeHttpAuthSchemeParametersProvider = async (config, context, input) => {\n    return {\n        operation: (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.getSmithyContext)(context).operation,\n        region: (await (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(config.region)()) ||\n            (() => {\n                throw new Error(\"expected `region` to be configured for `aws.auth#sigv4`\");\n            })(),\n    };\n};\nfunction createAwsAuthSigv4HttpAuthOption(authParameters) {\n    return {\n        schemeId: \"aws.auth#sigv4\",\n        signingProperties: {\n            name: \"bedrock\",\n            region: authParameters.region,\n        },\n        propertiesExtractor: (config, context) => ({\n            signingProperties: {\n                config,\n                context,\n            },\n        }),\n    };\n}\nconst defaultBedrockRuntimeHttpAuthSchemeProvider = (authParameters) => {\n    const options = [];\n    switch (authParameters.operation) {\n        default: {\n            options.push(createAwsAuthSigv4HttpAuthOption(authParameters));\n        }\n    }\n    return options;\n};\nconst resolveHttpAuthSchemeConfig = (config) => {\n    const config_0 = (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_1__.resolveAwsSdkSigV4Config)(config);\n    return Object.assign(config_0, {\n        authSchemePreference: (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(config.authSchemePreference ?? []),\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/auth/httpAuthSchemeProvider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ApplyGuardrailCommand.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ApplyGuardrailCommand.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command),\n/* harmony export */   ApplyGuardrailCommand: () => (/* binding */ ApplyGuardrailCommand)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/EndpointParameters.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js\");\n/* harmony import */ var _protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../protocols/Aws_restJson1 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/protocols/Aws_restJson1.js\");\n\n\n\n\n\n\n\nclass ApplyGuardrailCommand extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command\n    .classBuilder()\n    .ep(_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__.commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        (0,_smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin)(config, this.serialize, this.deserialize),\n        (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AmazonBedrockFrontendService\", \"ApplyGuardrail\", {})\n    .n(\"BedrockRuntimeClient\", \"ApplyGuardrailCommand\")\n    .f(_models_models_0__WEBPACK_IMPORTED_MODULE_4__.ApplyGuardrailRequestFilterSensitiveLog, void 0)\n    .ser(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.se_ApplyGuardrailCommand)\n    .de(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.de_ApplyGuardrailCommand)\n    .build() {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ApplyGuardrailCommand.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ConverseCommand.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ConverseCommand.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command),\n/* harmony export */   ConverseCommand: () => (/* binding */ ConverseCommand)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/EndpointParameters.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js\");\n/* harmony import */ var _protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../protocols/Aws_restJson1 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/protocols/Aws_restJson1.js\");\n\n\n\n\n\n\n\nclass ConverseCommand extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command\n    .classBuilder()\n    .ep(_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__.commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        (0,_smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin)(config, this.serialize, this.deserialize),\n        (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AmazonBedrockFrontendService\", \"Converse\", {})\n    .n(\"BedrockRuntimeClient\", \"ConverseCommand\")\n    .f(_models_models_0__WEBPACK_IMPORTED_MODULE_4__.ConverseRequestFilterSensitiveLog, _models_models_0__WEBPACK_IMPORTED_MODULE_4__.ConverseResponseFilterSensitiveLog)\n    .ser(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.se_ConverseCommand)\n    .de(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.de_ConverseCommand)\n    .build() {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ConverseCommand.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ConverseStreamCommand.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ConverseStreamCommand.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command),\n/* harmony export */   ConverseStreamCommand: () => (/* binding */ ConverseStreamCommand)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/EndpointParameters.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js\");\n/* harmony import */ var _protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../protocols/Aws_restJson1 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/protocols/Aws_restJson1.js\");\n\n\n\n\n\n\n\nclass ConverseStreamCommand extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command\n    .classBuilder()\n    .ep(_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__.commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        (0,_smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin)(config, this.serialize, this.deserialize),\n        (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AmazonBedrockFrontendService\", \"ConverseStream\", {\n    eventStream: {\n        output: true,\n    },\n})\n    .n(\"BedrockRuntimeClient\", \"ConverseStreamCommand\")\n    .f(_models_models_0__WEBPACK_IMPORTED_MODULE_4__.ConverseStreamRequestFilterSensitiveLog, _models_models_0__WEBPACK_IMPORTED_MODULE_4__.ConverseStreamResponseFilterSensitiveLog)\n    .ser(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.se_ConverseStreamCommand)\n    .de(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.de_ConverseStreamCommand)\n    .build() {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ConverseStreamCommand.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/GetAsyncInvokeCommand.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/GetAsyncInvokeCommand.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command),\n/* harmony export */   GetAsyncInvokeCommand: () => (/* binding */ GetAsyncInvokeCommand)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/EndpointParameters.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js\");\n/* harmony import */ var _protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../protocols/Aws_restJson1 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/protocols/Aws_restJson1.js\");\n\n\n\n\n\n\n\nclass GetAsyncInvokeCommand extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command\n    .classBuilder()\n    .ep(_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__.commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        (0,_smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin)(config, this.serialize, this.deserialize),\n        (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AmazonBedrockFrontendService\", \"GetAsyncInvoke\", {})\n    .n(\"BedrockRuntimeClient\", \"GetAsyncInvokeCommand\")\n    .f(void 0, _models_models_0__WEBPACK_IMPORTED_MODULE_4__.GetAsyncInvokeResponseFilterSensitiveLog)\n    .ser(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.se_GetAsyncInvokeCommand)\n    .de(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.de_GetAsyncInvokeCommand)\n    .build() {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/GetAsyncInvokeCommand.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelCommand.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelCommand.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command),\n/* harmony export */   InvokeModelCommand: () => (/* binding */ InvokeModelCommand)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/EndpointParameters.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js\");\n/* harmony import */ var _protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../protocols/Aws_restJson1 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/protocols/Aws_restJson1.js\");\n\n\n\n\n\n\n\nclass InvokeModelCommand extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command\n    .classBuilder()\n    .ep(_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__.commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        (0,_smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin)(config, this.serialize, this.deserialize),\n        (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AmazonBedrockFrontendService\", \"InvokeModel\", {})\n    .n(\"BedrockRuntimeClient\", \"InvokeModelCommand\")\n    .f(_models_models_0__WEBPACK_IMPORTED_MODULE_4__.InvokeModelRequestFilterSensitiveLog, _models_models_0__WEBPACK_IMPORTED_MODULE_4__.InvokeModelResponseFilterSensitiveLog)\n    .ser(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.se_InvokeModelCommand)\n    .de(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.de_InvokeModelCommand)\n    .build() {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelCommand.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelWithBidirectionalStreamCommand.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelWithBidirectionalStreamCommand.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_3__.Command),\n/* harmony export */   InvokeModelWithBidirectionalStreamCommand: () => (/* binding */ InvokeModelWithBidirectionalStreamCommand)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_middleware_eventstream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/middleware-eventstream */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/EndpointParameters.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js\");\n/* harmony import */ var _protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../protocols/Aws_restJson1 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/protocols/Aws_restJson1.js\");\n\n\n\n\n\n\n\n\nclass InvokeModelWithBidirectionalStreamCommand extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_3__.Command\n    .classBuilder()\n    .ep(_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_4__.commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        (0,_smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_2__.getSerdePlugin)(config, this.serialize, this.deserialize),\n        (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_1__.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),\n        (0,_aws_sdk_middleware_eventstream__WEBPACK_IMPORTED_MODULE_0__.getEventStreamPlugin)(config),\n    ];\n})\n    .s(\"AmazonBedrockFrontendService\", \"InvokeModelWithBidirectionalStream\", {\n    eventStream: {\n        input: true,\n        output: true,\n    },\n})\n    .n(\"BedrockRuntimeClient\", \"InvokeModelWithBidirectionalStreamCommand\")\n    .f(_models_models_0__WEBPACK_IMPORTED_MODULE_5__.InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog, _models_models_0__WEBPACK_IMPORTED_MODULE_5__.InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog)\n    .ser(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_6__.se_InvokeModelWithBidirectionalStreamCommand)\n    .de(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_6__.de_InvokeModelWithBidirectionalStreamCommand)\n    .build() {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelWithBidirectionalStreamCommand.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelWithResponseStreamCommand.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelWithResponseStreamCommand.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command),\n/* harmony export */   InvokeModelWithResponseStreamCommand: () => (/* binding */ InvokeModelWithResponseStreamCommand)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/EndpointParameters.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js\");\n/* harmony import */ var _protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../protocols/Aws_restJson1 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/protocols/Aws_restJson1.js\");\n\n\n\n\n\n\n\nclass InvokeModelWithResponseStreamCommand extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command\n    .classBuilder()\n    .ep(_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__.commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        (0,_smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin)(config, this.serialize, this.deserialize),\n        (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AmazonBedrockFrontendService\", \"InvokeModelWithResponseStream\", {\n    eventStream: {\n        output: true,\n    },\n})\n    .n(\"BedrockRuntimeClient\", \"InvokeModelWithResponseStreamCommand\")\n    .f(_models_models_0__WEBPACK_IMPORTED_MODULE_4__.InvokeModelWithResponseStreamRequestFilterSensitiveLog, _models_models_0__WEBPACK_IMPORTED_MODULE_4__.InvokeModelWithResponseStreamResponseFilterSensitiveLog)\n    .ser(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.se_InvokeModelWithResponseStreamCommand)\n    .de(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.de_InvokeModelWithResponseStreamCommand)\n    .build() {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelWithResponseStreamCommand.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ListAsyncInvokesCommand.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ListAsyncInvokesCommand.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command),\n/* harmony export */   ListAsyncInvokesCommand: () => (/* binding */ ListAsyncInvokesCommand)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/EndpointParameters.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js\");\n/* harmony import */ var _protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../protocols/Aws_restJson1 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/protocols/Aws_restJson1.js\");\n\n\n\n\n\n\n\nclass ListAsyncInvokesCommand extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command\n    .classBuilder()\n    .ep(_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__.commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        (0,_smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin)(config, this.serialize, this.deserialize),\n        (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AmazonBedrockFrontendService\", \"ListAsyncInvokes\", {})\n    .n(\"BedrockRuntimeClient\", \"ListAsyncInvokesCommand\")\n    .f(void 0, _models_models_0__WEBPACK_IMPORTED_MODULE_4__.ListAsyncInvokesResponseFilterSensitiveLog)\n    .ser(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.se_ListAsyncInvokesCommand)\n    .de(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.de_ListAsyncInvokesCommand)\n    .build() {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ListAsyncInvokesCommand.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/StartAsyncInvokeCommand.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/StartAsyncInvokeCommand.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command),\n/* harmony export */   StartAsyncInvokeCommand: () => (/* binding */ StartAsyncInvokeCommand)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/EndpointParameters.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js\");\n/* harmony import */ var _protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../protocols/Aws_restJson1 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/protocols/Aws_restJson1.js\");\n\n\n\n\n\n\n\nclass StartAsyncInvokeCommand extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command\n    .classBuilder()\n    .ep(_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__.commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        (0,_smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin)(config, this.serialize, this.deserialize),\n        (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AmazonBedrockFrontendService\", \"StartAsyncInvoke\", {})\n    .n(\"BedrockRuntimeClient\", \"StartAsyncInvokeCommand\")\n    .f(_models_models_0__WEBPACK_IMPORTED_MODULE_4__.StartAsyncInvokeRequestFilterSensitiveLog, void 0)\n    .ser(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.se_StartAsyncInvokeCommand)\n    .de(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.de_StartAsyncInvokeCommand)\n    .build() {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/StartAsyncInvokeCommand.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/index.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/index.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _ApplyGuardrailCommand__WEBPACK_IMPORTED_MODULE_0__.$Command),\n/* harmony export */   ApplyGuardrailCommand: () => (/* reexport safe */ _ApplyGuardrailCommand__WEBPACK_IMPORTED_MODULE_0__.ApplyGuardrailCommand),\n/* harmony export */   ConverseCommand: () => (/* reexport safe */ _ConverseCommand__WEBPACK_IMPORTED_MODULE_1__.ConverseCommand),\n/* harmony export */   ConverseStreamCommand: () => (/* reexport safe */ _ConverseStreamCommand__WEBPACK_IMPORTED_MODULE_2__.ConverseStreamCommand),\n/* harmony export */   GetAsyncInvokeCommand: () => (/* reexport safe */ _GetAsyncInvokeCommand__WEBPACK_IMPORTED_MODULE_3__.GetAsyncInvokeCommand),\n/* harmony export */   InvokeModelCommand: () => (/* reexport safe */ _InvokeModelCommand__WEBPACK_IMPORTED_MODULE_4__.InvokeModelCommand),\n/* harmony export */   InvokeModelWithBidirectionalStreamCommand: () => (/* reexport safe */ _InvokeModelWithBidirectionalStreamCommand__WEBPACK_IMPORTED_MODULE_5__.InvokeModelWithBidirectionalStreamCommand),\n/* harmony export */   InvokeModelWithResponseStreamCommand: () => (/* reexport safe */ _InvokeModelWithResponseStreamCommand__WEBPACK_IMPORTED_MODULE_6__.InvokeModelWithResponseStreamCommand),\n/* harmony export */   ListAsyncInvokesCommand: () => (/* reexport safe */ _ListAsyncInvokesCommand__WEBPACK_IMPORTED_MODULE_7__.ListAsyncInvokesCommand),\n/* harmony export */   StartAsyncInvokeCommand: () => (/* reexport safe */ _StartAsyncInvokeCommand__WEBPACK_IMPORTED_MODULE_8__.StartAsyncInvokeCommand)\n/* harmony export */ });\n/* harmony import */ var _ApplyGuardrailCommand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ApplyGuardrailCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ApplyGuardrailCommand.js\");\n/* harmony import */ var _ConverseCommand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ConverseCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ConverseCommand.js\");\n/* harmony import */ var _ConverseStreamCommand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ConverseStreamCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ConverseStreamCommand.js\");\n/* harmony import */ var _GetAsyncInvokeCommand__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./GetAsyncInvokeCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/GetAsyncInvokeCommand.js\");\n/* harmony import */ var _InvokeModelCommand__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./InvokeModelCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelCommand.js\");\n/* harmony import */ var _InvokeModelWithBidirectionalStreamCommand__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./InvokeModelWithBidirectionalStreamCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelWithBidirectionalStreamCommand.js\");\n/* harmony import */ var _InvokeModelWithResponseStreamCommand__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./InvokeModelWithResponseStreamCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/InvokeModelWithResponseStreamCommand.js\");\n/* harmony import */ var _ListAsyncInvokesCommand__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ListAsyncInvokesCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ListAsyncInvokesCommand.js\");\n/* harmony import */ var _StartAsyncInvokeCommand__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StartAsyncInvokeCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/StartAsyncInvokeCommand.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvY29tbWFuZHMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF3QztBQUNOO0FBQ007QUFDQTtBQUNIO0FBQ3VCO0FBQ0w7QUFDYjtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvY29tbWFuZHMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vQXBwbHlHdWFyZHJhaWxDb21tYW5kXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9Db252ZXJzZUNvbW1hbmRcIjtcbmV4cG9ydCAqIGZyb20gXCIuL0NvbnZlcnNlU3RyZWFtQ29tbWFuZFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vR2V0QXN5bmNJbnZva2VDb21tYW5kXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9JbnZva2VNb2RlbENvbW1hbmRcIjtcbmV4cG9ydCAqIGZyb20gXCIuL0ludm9rZU1vZGVsV2l0aEJpZGlyZWN0aW9uYWxTdHJlYW1Db21tYW5kXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9JbnZva2VNb2RlbFdpdGhSZXNwb25zZVN0cmVhbUNvbW1hbmRcIjtcbmV4cG9ydCAqIGZyb20gXCIuL0xpc3RBc3luY0ludm9rZXNDb21tYW5kXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9TdGFydEFzeW5jSW52b2tlQ29tbWFuZFwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/EndpointParameters.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/EndpointParameters.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   commonParams: () => (/* binding */ commonParams),\n/* harmony export */   resolveClientEndpointParameters: () => (/* binding */ resolveClientEndpointParameters)\n/* harmony export */ });\nconst resolveClientEndpointParameters = (options) => {\n    return Object.assign(options, {\n        useDualstackEndpoint: options.useDualstackEndpoint ?? false,\n        useFipsEndpoint: options.useFipsEndpoint ?? false,\n        defaultSigningName: \"bedrock\",\n    });\n};\nconst commonParams = {\n    UseFIPS: { type: \"builtInParams\", name: \"useFipsEndpoint\" },\n    Endpoint: { type: \"builtInParams\", name: \"endpoint\" },\n    Region: { type: \"builtInParams\", name: \"region\" },\n    UseDualStack: { type: \"builtInParams\", name: \"useDualstackEndpoint\" },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvZW5kcG9pbnQvRW5kcG9pbnRQYXJhbWV0ZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNPO0FBQ1AsZUFBZSxnREFBZ0Q7QUFDL0QsZ0JBQWdCLHlDQUF5QztBQUN6RCxjQUFjLHVDQUF1QztBQUNyRCxvQkFBb0IscURBQXFEO0FBQ3pFIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvZW5kcG9pbnQvRW5kcG9pbnRQYXJhbWV0ZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCByZXNvbHZlQ2xpZW50RW5kcG9pbnRQYXJhbWV0ZXJzID0gKG9wdGlvbnMpID0+IHtcbiAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihvcHRpb25zLCB7XG4gICAgICAgIHVzZUR1YWxzdGFja0VuZHBvaW50OiBvcHRpb25zLnVzZUR1YWxzdGFja0VuZHBvaW50ID8/IGZhbHNlLFxuICAgICAgICB1c2VGaXBzRW5kcG9pbnQ6IG9wdGlvbnMudXNlRmlwc0VuZHBvaW50ID8/IGZhbHNlLFxuICAgICAgICBkZWZhdWx0U2lnbmluZ05hbWU6IFwiYmVkcm9ja1wiLFxuICAgIH0pO1xufTtcbmV4cG9ydCBjb25zdCBjb21tb25QYXJhbXMgPSB7XG4gICAgVXNlRklQUzogeyB0eXBlOiBcImJ1aWx0SW5QYXJhbXNcIiwgbmFtZTogXCJ1c2VGaXBzRW5kcG9pbnRcIiB9LFxuICAgIEVuZHBvaW50OiB7IHR5cGU6IFwiYnVpbHRJblBhcmFtc1wiLCBuYW1lOiBcImVuZHBvaW50XCIgfSxcbiAgICBSZWdpb246IHsgdHlwZTogXCJidWlsdEluUGFyYW1zXCIsIG5hbWU6IFwicmVnaW9uXCIgfSxcbiAgICBVc2VEdWFsU3RhY2s6IHsgdHlwZTogXCJidWlsdEluUGFyYW1zXCIsIG5hbWU6IFwidXNlRHVhbHN0YWNrRW5kcG9pbnRcIiB9LFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/EndpointParameters.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/endpointResolver.js":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/endpointResolver.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultEndpointResolver: () => (/* binding */ defaultEndpointResolver)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/index.js\");\n/* harmony import */ var _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/index.js\");\n/* harmony import */ var _ruleset__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ruleset */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/ruleset.js\");\n\n\n\nconst cache = new _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__.EndpointCache({\n    size: 50,\n    params: [\"Endpoint\", \"Region\", \"UseDualStack\", \"UseFIPS\"],\n});\nconst defaultEndpointResolver = (endpointParams, context = {}) => {\n    return cache.get(endpointParams, () => (0,_smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__.resolveEndpoint)(_ruleset__WEBPACK_IMPORTED_MODULE_2__.ruleSet, {\n        endpointParams: endpointParams,\n        logger: context.logger,\n    }));\n};\n_smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__.customEndpointFunctions.aws = _aws_sdk_util_endpoints__WEBPACK_IMPORTED_MODULE_0__.awsEndpointFunctions;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvZW5kcG9pbnQvZW5kcG9pbnRSZXNvbHZlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStEO0FBQ2tDO0FBQzdEO0FBQ3BDLGtCQUFrQixpRUFBYTtBQUMvQjtBQUNBO0FBQ0EsQ0FBQztBQUNNLDZEQUE2RDtBQUNwRSwyQ0FBMkMsdUVBQWUsQ0FBQyw2Q0FBTztBQUNsRTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsMkVBQXVCLE9BQU8seUVBQW9CIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvZW5kcG9pbnQvZW5kcG9pbnRSZXNvbHZlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBhd3NFbmRwb2ludEZ1bmN0aW9ucyB9IGZyb20gXCJAYXdzLXNkay91dGlsLWVuZHBvaW50c1wiO1xuaW1wb3J0IHsgY3VzdG9tRW5kcG9pbnRGdW5jdGlvbnMsIEVuZHBvaW50Q2FjaGUsIHJlc29sdmVFbmRwb2ludCB9IGZyb20gXCJAc21pdGh5L3V0aWwtZW5kcG9pbnRzXCI7XG5pbXBvcnQgeyBydWxlU2V0IH0gZnJvbSBcIi4vcnVsZXNldFwiO1xuY29uc3QgY2FjaGUgPSBuZXcgRW5kcG9pbnRDYWNoZSh7XG4gICAgc2l6ZTogNTAsXG4gICAgcGFyYW1zOiBbXCJFbmRwb2ludFwiLCBcIlJlZ2lvblwiLCBcIlVzZUR1YWxTdGFja1wiLCBcIlVzZUZJUFNcIl0sXG59KTtcbmV4cG9ydCBjb25zdCBkZWZhdWx0RW5kcG9pbnRSZXNvbHZlciA9IChlbmRwb2ludFBhcmFtcywgY29udGV4dCA9IHt9KSA9PiB7XG4gICAgcmV0dXJuIGNhY2hlLmdldChlbmRwb2ludFBhcmFtcywgKCkgPT4gcmVzb2x2ZUVuZHBvaW50KHJ1bGVTZXQsIHtcbiAgICAgICAgZW5kcG9pbnRQYXJhbXM6IGVuZHBvaW50UGFyYW1zLFxuICAgICAgICBsb2dnZXI6IGNvbnRleHQubG9nZ2VyLFxuICAgIH0pKTtcbn07XG5jdXN0b21FbmRwb2ludEZ1bmN0aW9ucy5hd3MgPSBhd3NFbmRwb2ludEZ1bmN0aW9ucztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/endpointResolver.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/ruleset.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/ruleset.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ruleSet: () => (/* binding */ ruleSet)\n/* harmony export */ });\nconst s = \"required\", t = \"fn\", u = \"argv\", v = \"ref\";\nconst a = true, b = \"isSet\", c = \"booleanEquals\", d = \"error\", e = \"endpoint\", f = \"tree\", g = \"PartitionResult\", h = { [s]: false, \"type\": \"String\" }, i = { [s]: true, \"default\": false, \"type\": \"Boolean\" }, j = { [v]: \"Endpoint\" }, k = { [t]: c, [u]: [{ [v]: \"UseFIPS\" }, true] }, l = { [t]: c, [u]: [{ [v]: \"UseDualStack\" }, true] }, m = {}, n = { [t]: \"getAttr\", [u]: [{ [v]: g }, \"supportsFIPS\"] }, o = { [t]: c, [u]: [true, { [t]: \"getAttr\", [u]: [{ [v]: g }, \"supportsDualStack\"] }] }, p = [k], q = [l], r = [{ [v]: \"Region\" }];\nconst _data = { version: \"1.0\", parameters: { Region: h, UseDualStack: i, UseFIPS: i, Endpoint: h }, rules: [{ conditions: [{ [t]: b, [u]: [j] }], rules: [{ conditions: p, error: \"Invalid Configuration: FIPS and custom endpoint are not supported\", type: d }, { rules: [{ conditions: q, error: \"Invalid Configuration: Dualstack and custom endpoint are not supported\", type: d }, { endpoint: { url: j, properties: m, headers: m }, type: e }], type: f }], type: f }, { rules: [{ conditions: [{ [t]: b, [u]: r }], rules: [{ conditions: [{ [t]: \"aws.partition\", [u]: r, assign: g }], rules: [{ conditions: [k, l], rules: [{ conditions: [{ [t]: c, [u]: [a, n] }, o], rules: [{ rules: [{ endpoint: { url: \"https://bedrock-runtime-fips.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: m, headers: m }, type: e }], type: f }], type: f }, { error: \"FIPS and DualStack are enabled, but this partition does not support one or both\", type: d }], type: f }, { conditions: p, rules: [{ conditions: [{ [t]: c, [u]: [n, a] }], rules: [{ rules: [{ endpoint: { url: \"https://bedrock-runtime-fips.{Region}.{PartitionResult#dnsSuffix}\", properties: m, headers: m }, type: e }], type: f }], type: f }, { error: \"FIPS is enabled but this partition does not support FIPS\", type: d }], type: f }, { conditions: q, rules: [{ conditions: [o], rules: [{ rules: [{ endpoint: { url: \"https://bedrock-runtime.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: m, headers: m }, type: e }], type: f }], type: f }, { error: \"DualStack is enabled but this partition does not support DualStack\", type: d }], type: f }, { rules: [{ endpoint: { url: \"https://bedrock-runtime.{Region}.{PartitionResult#dnsSuffix}\", properties: m, headers: m }, type: e }], type: f }], type: f }], type: f }, { error: \"Invalid Configuration: Missing Region\", type: d }], type: f }] };\nconst ruleSet = _data;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/ruleset.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/index.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/index.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.$Command),\n/* harmony export */   AccessDeniedException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.AccessDeniedException),\n/* harmony export */   ApplyGuardrailCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.ApplyGuardrailCommand),\n/* harmony export */   ApplyGuardrailRequestFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ApplyGuardrailRequestFilterSensitiveLog),\n/* harmony export */   AsyncInvokeOutputDataConfig: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.AsyncInvokeOutputDataConfig),\n/* harmony export */   AsyncInvokeStatus: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.AsyncInvokeStatus),\n/* harmony export */   AsyncInvokeSummaryFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.AsyncInvokeSummaryFilterSensitiveLog),\n/* harmony export */   BedrockRuntime: () => (/* reexport safe */ _BedrockRuntime__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntime),\n/* harmony export */   BedrockRuntimeClient: () => (/* reexport safe */ _BedrockRuntimeClient__WEBPACK_IMPORTED_MODULE_0__.BedrockRuntimeClient),\n/* harmony export */   BedrockRuntimeServiceException: () => (/* reexport safe */ _models_BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_5__.BedrockRuntimeServiceException),\n/* harmony export */   BidirectionalInputPayloadPartFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.BidirectionalInputPayloadPartFilterSensitiveLog),\n/* harmony export */   BidirectionalOutputPayloadPartFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.BidirectionalOutputPayloadPartFilterSensitiveLog),\n/* harmony export */   CachePointType: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.CachePointType),\n/* harmony export */   ConflictException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ConflictException),\n/* harmony export */   ContentBlock: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ContentBlock),\n/* harmony export */   ContentBlockDelta: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ContentBlockDelta),\n/* harmony export */   ContentBlockDeltaEventFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ContentBlockDeltaEventFilterSensitiveLog),\n/* harmony export */   ContentBlockDeltaFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ContentBlockDeltaFilterSensitiveLog),\n/* harmony export */   ContentBlockFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ContentBlockFilterSensitiveLog),\n/* harmony export */   ContentBlockStart: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ContentBlockStart),\n/* harmony export */   ConversationRole: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ConversationRole),\n/* harmony export */   ConverseCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.ConverseCommand),\n/* harmony export */   ConverseOutput: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ConverseOutput),\n/* harmony export */   ConverseOutputFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ConverseOutputFilterSensitiveLog),\n/* harmony export */   ConverseRequestFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ConverseRequestFilterSensitiveLog),\n/* harmony export */   ConverseResponseFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ConverseResponseFilterSensitiveLog),\n/* harmony export */   ConverseStreamCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.ConverseStreamCommand),\n/* harmony export */   ConverseStreamOutput: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ConverseStreamOutput),\n/* harmony export */   ConverseStreamOutputFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ConverseStreamOutputFilterSensitiveLog),\n/* harmony export */   ConverseStreamRequestFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ConverseStreamRequestFilterSensitiveLog),\n/* harmony export */   ConverseStreamResponseFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ConverseStreamResponseFilterSensitiveLog),\n/* harmony export */   DocumentFormat: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.DocumentFormat),\n/* harmony export */   DocumentSource: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.DocumentSource),\n/* harmony export */   GetAsyncInvokeCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.GetAsyncInvokeCommand),\n/* harmony export */   GetAsyncInvokeResponseFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GetAsyncInvokeResponseFilterSensitiveLog),\n/* harmony export */   GuardrailAction: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailAction),\n/* harmony export */   GuardrailContentBlock: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailContentBlock),\n/* harmony export */   GuardrailContentBlockFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailContentBlockFilterSensitiveLog),\n/* harmony export */   GuardrailContentFilterConfidence: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailContentFilterConfidence),\n/* harmony export */   GuardrailContentFilterStrength: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailContentFilterStrength),\n/* harmony export */   GuardrailContentFilterType: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailContentFilterType),\n/* harmony export */   GuardrailContentPolicyAction: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailContentPolicyAction),\n/* harmony export */   GuardrailContentQualifier: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailContentQualifier),\n/* harmony export */   GuardrailContentSource: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailContentSource),\n/* harmony export */   GuardrailContextualGroundingFilterType: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailContextualGroundingFilterType),\n/* harmony export */   GuardrailContextualGroundingPolicyAction: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailContextualGroundingPolicyAction),\n/* harmony export */   GuardrailConverseContentBlock: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailConverseContentBlock),\n/* harmony export */   GuardrailConverseContentBlockFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailConverseContentBlockFilterSensitiveLog),\n/* harmony export */   GuardrailConverseContentQualifier: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailConverseContentQualifier),\n/* harmony export */   GuardrailConverseImageBlockFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailConverseImageBlockFilterSensitiveLog),\n/* harmony export */   GuardrailConverseImageFormat: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailConverseImageFormat),\n/* harmony export */   GuardrailConverseImageSource: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailConverseImageSource),\n/* harmony export */   GuardrailConverseImageSourceFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailConverseImageSourceFilterSensitiveLog),\n/* harmony export */   GuardrailImageBlockFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailImageBlockFilterSensitiveLog),\n/* harmony export */   GuardrailImageFormat: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailImageFormat),\n/* harmony export */   GuardrailImageSource: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailImageSource),\n/* harmony export */   GuardrailImageSourceFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailImageSourceFilterSensitiveLog),\n/* harmony export */   GuardrailManagedWordType: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailManagedWordType),\n/* harmony export */   GuardrailOutputScope: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailOutputScope),\n/* harmony export */   GuardrailPiiEntityType: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailPiiEntityType),\n/* harmony export */   GuardrailSensitiveInformationPolicyAction: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailSensitiveInformationPolicyAction),\n/* harmony export */   GuardrailStreamProcessingMode: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailStreamProcessingMode),\n/* harmony export */   GuardrailTopicPolicyAction: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailTopicPolicyAction),\n/* harmony export */   GuardrailTopicType: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailTopicType),\n/* harmony export */   GuardrailTrace: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailTrace),\n/* harmony export */   GuardrailWordPolicyAction: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.GuardrailWordPolicyAction),\n/* harmony export */   ImageFormat: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ImageFormat),\n/* harmony export */   ImageSource: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ImageSource),\n/* harmony export */   InternalServerException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.InternalServerException),\n/* harmony export */   InvokeModelCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.InvokeModelCommand),\n/* harmony export */   InvokeModelRequestFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.InvokeModelRequestFilterSensitiveLog),\n/* harmony export */   InvokeModelResponseFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.InvokeModelResponseFilterSensitiveLog),\n/* harmony export */   InvokeModelWithBidirectionalStreamCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.InvokeModelWithBidirectionalStreamCommand),\n/* harmony export */   InvokeModelWithBidirectionalStreamInput: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.InvokeModelWithBidirectionalStreamInput),\n/* harmony export */   InvokeModelWithBidirectionalStreamInputFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.InvokeModelWithBidirectionalStreamInputFilterSensitiveLog),\n/* harmony export */   InvokeModelWithBidirectionalStreamOutput: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.InvokeModelWithBidirectionalStreamOutput),\n/* harmony export */   InvokeModelWithBidirectionalStreamOutputFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.InvokeModelWithBidirectionalStreamOutputFilterSensitiveLog),\n/* harmony export */   InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog),\n/* harmony export */   InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog),\n/* harmony export */   InvokeModelWithResponseStreamCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.InvokeModelWithResponseStreamCommand),\n/* harmony export */   InvokeModelWithResponseStreamRequestFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.InvokeModelWithResponseStreamRequestFilterSensitiveLog),\n/* harmony export */   InvokeModelWithResponseStreamResponseFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.InvokeModelWithResponseStreamResponseFilterSensitiveLog),\n/* harmony export */   ListAsyncInvokesCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.ListAsyncInvokesCommand),\n/* harmony export */   ListAsyncInvokesResponseFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ListAsyncInvokesResponseFilterSensitiveLog),\n/* harmony export */   MessageFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.MessageFilterSensitiveLog),\n/* harmony export */   ModelErrorException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ModelErrorException),\n/* harmony export */   ModelNotReadyException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ModelNotReadyException),\n/* harmony export */   ModelStreamErrorException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ModelStreamErrorException),\n/* harmony export */   ModelTimeoutException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ModelTimeoutException),\n/* harmony export */   PayloadPartFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.PayloadPartFilterSensitiveLog),\n/* harmony export */   PerformanceConfigLatency: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.PerformanceConfigLatency),\n/* harmony export */   PromptVariableValues: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.PromptVariableValues),\n/* harmony export */   ReasoningContentBlock: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ReasoningContentBlock),\n/* harmony export */   ReasoningContentBlockDelta: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ReasoningContentBlockDelta),\n/* harmony export */   ReasoningContentBlockDeltaFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ReasoningContentBlockDeltaFilterSensitiveLog),\n/* harmony export */   ReasoningContentBlockFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ReasoningContentBlockFilterSensitiveLog),\n/* harmony export */   ReasoningTextBlockFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ReasoningTextBlockFilterSensitiveLog),\n/* harmony export */   ResourceNotFoundException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ResourceNotFoundException),\n/* harmony export */   ResponseStream: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ResponseStream),\n/* harmony export */   ResponseStreamFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ResponseStreamFilterSensitiveLog),\n/* harmony export */   ServiceQuotaExceededException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ServiceQuotaExceededException),\n/* harmony export */   ServiceUnavailableException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ServiceUnavailableException),\n/* harmony export */   SortAsyncInvocationBy: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.SortAsyncInvocationBy),\n/* harmony export */   SortOrder: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.SortOrder),\n/* harmony export */   StartAsyncInvokeCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.StartAsyncInvokeCommand),\n/* harmony export */   StartAsyncInvokeRequestFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.StartAsyncInvokeRequestFilterSensitiveLog),\n/* harmony export */   StopReason: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.StopReason),\n/* harmony export */   SystemContentBlock: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.SystemContentBlock),\n/* harmony export */   SystemContentBlockFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.SystemContentBlockFilterSensitiveLog),\n/* harmony export */   ThrottlingException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ThrottlingException),\n/* harmony export */   Tool: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.Tool),\n/* harmony export */   ToolChoice: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ToolChoice),\n/* harmony export */   ToolInputSchema: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ToolInputSchema),\n/* harmony export */   ToolResultContentBlock: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ToolResultContentBlock),\n/* harmony export */   ToolResultStatus: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ToolResultStatus),\n/* harmony export */   Trace: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.Trace),\n/* harmony export */   ValidationException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.ValidationException),\n/* harmony export */   VideoFormat: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.VideoFormat),\n/* harmony export */   VideoSource: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_4__.VideoSource),\n/* harmony export */   __Client: () => (/* reexport safe */ _BedrockRuntimeClient__WEBPACK_IMPORTED_MODULE_0__.__Client),\n/* harmony export */   paginateListAsyncInvokes: () => (/* reexport safe */ _pagination__WEBPACK_IMPORTED_MODULE_3__.paginateListAsyncInvokes)\n/* harmony export */ });\n/* harmony import */ var _BedrockRuntimeClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./BedrockRuntimeClient */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/BedrockRuntimeClient.js\");\n/* harmony import */ var _BedrockRuntime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BedrockRuntime */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/BedrockRuntime.js\");\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./commands */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/index.js\");\n/* harmony import */ var _pagination__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pagination */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/pagination/index.js\");\n/* harmony import */ var _models__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./models */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/index.js\");\n/* harmony import */ var _models_BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./models/BedrockRuntimeServiceException */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/BedrockRuntimeServiceException.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF1QztBQUNOO0FBQ047QUFDRTtBQUNKO0FBQ2dFIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vQmVkcm9ja1J1bnRpbWVDbGllbnRcIjtcbmV4cG9ydCAqIGZyb20gXCIuL0JlZHJvY2tSdW50aW1lXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jb21tYW5kc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vcGFnaW5hdGlvblwiO1xuZXhwb3J0ICogZnJvbSBcIi4vbW9kZWxzXCI7XG5leHBvcnQgeyBCZWRyb2NrUnVudGltZVNlcnZpY2VFeGNlcHRpb24gfSBmcm9tIFwiLi9tb2RlbHMvQmVkcm9ja1J1bnRpbWVTZXJ2aWNlRXhjZXB0aW9uXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/BedrockRuntimeServiceException.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/BedrockRuntimeServiceException.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BedrockRuntimeServiceException: () => (/* binding */ BedrockRuntimeServiceException),\n/* harmony export */   __ServiceException: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.ServiceException)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n\n\nclass BedrockRuntimeServiceException extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.ServiceException {\n    constructor(options) {\n        super(options);\n        Object.setPrototypeOf(this, BedrockRuntimeServiceException.prototype);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvbW9kZWxzL0JlZHJvY2tSdW50aW1lU2VydmljZUV4Y2VwdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0Y7QUFDbEQ7QUFDdkIsNkNBQTZDLG1FQUFrQjtBQUN0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvbW9kZWxzL0JlZHJvY2tSdW50aW1lU2VydmljZUV4Y2VwdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTZXJ2aWNlRXhjZXB0aW9uIGFzIF9fU2VydmljZUV4Y2VwdGlvbiwgfSBmcm9tIFwiQHNtaXRoeS9zbWl0aHktY2xpZW50XCI7XG5leHBvcnQgeyBfX1NlcnZpY2VFeGNlcHRpb24gfTtcbmV4cG9ydCBjbGFzcyBCZWRyb2NrUnVudGltZVNlcnZpY2VFeGNlcHRpb24gZXh0ZW5kcyBfX1NlcnZpY2VFeGNlcHRpb24ge1xuICAgIGNvbnN0cnVjdG9yKG9wdGlvbnMpIHtcbiAgICAgICAgc3VwZXIob3B0aW9ucyk7XG4gICAgICAgIE9iamVjdC5zZXRQcm90b3R5cGVPZih0aGlzLCBCZWRyb2NrUnVudGltZVNlcnZpY2VFeGNlcHRpb24ucHJvdG90eXBlKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/BedrockRuntimeServiceException.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/index.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/index.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessDeniedException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.AccessDeniedException),\n/* harmony export */   ApplyGuardrailRequestFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ApplyGuardrailRequestFilterSensitiveLog),\n/* harmony export */   AsyncInvokeOutputDataConfig: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.AsyncInvokeOutputDataConfig),\n/* harmony export */   AsyncInvokeStatus: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.AsyncInvokeStatus),\n/* harmony export */   AsyncInvokeSummaryFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.AsyncInvokeSummaryFilterSensitiveLog),\n/* harmony export */   BidirectionalInputPayloadPartFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.BidirectionalInputPayloadPartFilterSensitiveLog),\n/* harmony export */   BidirectionalOutputPayloadPartFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.BidirectionalOutputPayloadPartFilterSensitiveLog),\n/* harmony export */   CachePointType: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.CachePointType),\n/* harmony export */   ConflictException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ConflictException),\n/* harmony export */   ContentBlock: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ContentBlock),\n/* harmony export */   ContentBlockDelta: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ContentBlockDelta),\n/* harmony export */   ContentBlockDeltaEventFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ContentBlockDeltaEventFilterSensitiveLog),\n/* harmony export */   ContentBlockDeltaFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ContentBlockDeltaFilterSensitiveLog),\n/* harmony export */   ContentBlockFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ContentBlockFilterSensitiveLog),\n/* harmony export */   ContentBlockStart: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ContentBlockStart),\n/* harmony export */   ConversationRole: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ConversationRole),\n/* harmony export */   ConverseOutput: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ConverseOutput),\n/* harmony export */   ConverseOutputFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ConverseOutputFilterSensitiveLog),\n/* harmony export */   ConverseRequestFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ConverseRequestFilterSensitiveLog),\n/* harmony export */   ConverseResponseFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ConverseResponseFilterSensitiveLog),\n/* harmony export */   ConverseStreamOutput: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ConverseStreamOutput),\n/* harmony export */   ConverseStreamOutputFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ConverseStreamOutputFilterSensitiveLog),\n/* harmony export */   ConverseStreamRequestFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ConverseStreamRequestFilterSensitiveLog),\n/* harmony export */   ConverseStreamResponseFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ConverseStreamResponseFilterSensitiveLog),\n/* harmony export */   DocumentFormat: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.DocumentFormat),\n/* harmony export */   DocumentSource: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.DocumentSource),\n/* harmony export */   GetAsyncInvokeResponseFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GetAsyncInvokeResponseFilterSensitiveLog),\n/* harmony export */   GuardrailAction: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailAction),\n/* harmony export */   GuardrailContentBlock: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailContentBlock),\n/* harmony export */   GuardrailContentBlockFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailContentBlockFilterSensitiveLog),\n/* harmony export */   GuardrailContentFilterConfidence: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailContentFilterConfidence),\n/* harmony export */   GuardrailContentFilterStrength: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailContentFilterStrength),\n/* harmony export */   GuardrailContentFilterType: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailContentFilterType),\n/* harmony export */   GuardrailContentPolicyAction: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailContentPolicyAction),\n/* harmony export */   GuardrailContentQualifier: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailContentQualifier),\n/* harmony export */   GuardrailContentSource: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailContentSource),\n/* harmony export */   GuardrailContextualGroundingFilterType: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailContextualGroundingFilterType),\n/* harmony export */   GuardrailContextualGroundingPolicyAction: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailContextualGroundingPolicyAction),\n/* harmony export */   GuardrailConverseContentBlock: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailConverseContentBlock),\n/* harmony export */   GuardrailConverseContentBlockFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailConverseContentBlockFilterSensitiveLog),\n/* harmony export */   GuardrailConverseContentQualifier: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailConverseContentQualifier),\n/* harmony export */   GuardrailConverseImageBlockFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailConverseImageBlockFilterSensitiveLog),\n/* harmony export */   GuardrailConverseImageFormat: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailConverseImageFormat),\n/* harmony export */   GuardrailConverseImageSource: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailConverseImageSource),\n/* harmony export */   GuardrailConverseImageSourceFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailConverseImageSourceFilterSensitiveLog),\n/* harmony export */   GuardrailImageBlockFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailImageBlockFilterSensitiveLog),\n/* harmony export */   GuardrailImageFormat: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailImageFormat),\n/* harmony export */   GuardrailImageSource: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailImageSource),\n/* harmony export */   GuardrailImageSourceFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailImageSourceFilterSensitiveLog),\n/* harmony export */   GuardrailManagedWordType: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailManagedWordType),\n/* harmony export */   GuardrailOutputScope: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailOutputScope),\n/* harmony export */   GuardrailPiiEntityType: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailPiiEntityType),\n/* harmony export */   GuardrailSensitiveInformationPolicyAction: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailSensitiveInformationPolicyAction),\n/* harmony export */   GuardrailStreamProcessingMode: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailStreamProcessingMode),\n/* harmony export */   GuardrailTopicPolicyAction: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailTopicPolicyAction),\n/* harmony export */   GuardrailTopicType: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailTopicType),\n/* harmony export */   GuardrailTrace: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailTrace),\n/* harmony export */   GuardrailWordPolicyAction: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.GuardrailWordPolicyAction),\n/* harmony export */   ImageFormat: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ImageFormat),\n/* harmony export */   ImageSource: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ImageSource),\n/* harmony export */   InternalServerException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InternalServerException),\n/* harmony export */   InvokeModelRequestFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvokeModelRequestFilterSensitiveLog),\n/* harmony export */   InvokeModelResponseFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvokeModelResponseFilterSensitiveLog),\n/* harmony export */   InvokeModelWithBidirectionalStreamInput: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvokeModelWithBidirectionalStreamInput),\n/* harmony export */   InvokeModelWithBidirectionalStreamInputFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvokeModelWithBidirectionalStreamInputFilterSensitiveLog),\n/* harmony export */   InvokeModelWithBidirectionalStreamOutput: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvokeModelWithBidirectionalStreamOutput),\n/* harmony export */   InvokeModelWithBidirectionalStreamOutputFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvokeModelWithBidirectionalStreamOutputFilterSensitiveLog),\n/* harmony export */   InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog),\n/* harmony export */   InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog),\n/* harmony export */   InvokeModelWithResponseStreamRequestFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvokeModelWithResponseStreamRequestFilterSensitiveLog),\n/* harmony export */   InvokeModelWithResponseStreamResponseFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvokeModelWithResponseStreamResponseFilterSensitiveLog),\n/* harmony export */   ListAsyncInvokesResponseFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ListAsyncInvokesResponseFilterSensitiveLog),\n/* harmony export */   MessageFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.MessageFilterSensitiveLog),\n/* harmony export */   ModelErrorException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ModelErrorException),\n/* harmony export */   ModelNotReadyException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ModelNotReadyException),\n/* harmony export */   ModelStreamErrorException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ModelStreamErrorException),\n/* harmony export */   ModelTimeoutException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ModelTimeoutException),\n/* harmony export */   PayloadPartFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.PayloadPartFilterSensitiveLog),\n/* harmony export */   PerformanceConfigLatency: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.PerformanceConfigLatency),\n/* harmony export */   PromptVariableValues: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.PromptVariableValues),\n/* harmony export */   ReasoningContentBlock: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ReasoningContentBlock),\n/* harmony export */   ReasoningContentBlockDelta: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ReasoningContentBlockDelta),\n/* harmony export */   ReasoningContentBlockDeltaFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ReasoningContentBlockDeltaFilterSensitiveLog),\n/* harmony export */   ReasoningContentBlockFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ReasoningContentBlockFilterSensitiveLog),\n/* harmony export */   ReasoningTextBlockFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ReasoningTextBlockFilterSensitiveLog),\n/* harmony export */   ResourceNotFoundException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ResourceNotFoundException),\n/* harmony export */   ResponseStream: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ResponseStream),\n/* harmony export */   ResponseStreamFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ResponseStreamFilterSensitiveLog),\n/* harmony export */   ServiceQuotaExceededException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ServiceQuotaExceededException),\n/* harmony export */   ServiceUnavailableException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ServiceUnavailableException),\n/* harmony export */   SortAsyncInvocationBy: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.SortAsyncInvocationBy),\n/* harmony export */   SortOrder: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.SortOrder),\n/* harmony export */   StartAsyncInvokeRequestFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.StartAsyncInvokeRequestFilterSensitiveLog),\n/* harmony export */   StopReason: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.StopReason),\n/* harmony export */   SystemContentBlock: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.SystemContentBlock),\n/* harmony export */   SystemContentBlockFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.SystemContentBlockFilterSensitiveLog),\n/* harmony export */   ThrottlingException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ThrottlingException),\n/* harmony export */   Tool: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.Tool),\n/* harmony export */   ToolChoice: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ToolChoice),\n/* harmony export */   ToolInputSchema: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ToolInputSchema),\n/* harmony export */   ToolResultContentBlock: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ToolResultContentBlock),\n/* harmony export */   ToolResultStatus: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ToolResultStatus),\n/* harmony export */   Trace: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.Trace),\n/* harmony export */   ValidationException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ValidationException),\n/* harmony export */   VideoFormat: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.VideoFormat),\n/* harmony export */   VideoSource: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.VideoSource)\n/* harmony export */ });\n/* harmony import */ var _models_0__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvbW9kZWxzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTJCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvbW9kZWxzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL21vZGVsc18wXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessDeniedException: () => (/* binding */ AccessDeniedException),\n/* harmony export */   ApplyGuardrailRequestFilterSensitiveLog: () => (/* binding */ ApplyGuardrailRequestFilterSensitiveLog),\n/* harmony export */   AsyncInvokeOutputDataConfig: () => (/* binding */ AsyncInvokeOutputDataConfig),\n/* harmony export */   AsyncInvokeStatus: () => (/* binding */ AsyncInvokeStatus),\n/* harmony export */   AsyncInvokeSummaryFilterSensitiveLog: () => (/* binding */ AsyncInvokeSummaryFilterSensitiveLog),\n/* harmony export */   BidirectionalInputPayloadPartFilterSensitiveLog: () => (/* binding */ BidirectionalInputPayloadPartFilterSensitiveLog),\n/* harmony export */   BidirectionalOutputPayloadPartFilterSensitiveLog: () => (/* binding */ BidirectionalOutputPayloadPartFilterSensitiveLog),\n/* harmony export */   CachePointType: () => (/* binding */ CachePointType),\n/* harmony export */   ConflictException: () => (/* binding */ ConflictException),\n/* harmony export */   ContentBlock: () => (/* binding */ ContentBlock),\n/* harmony export */   ContentBlockDelta: () => (/* binding */ ContentBlockDelta),\n/* harmony export */   ContentBlockDeltaEventFilterSensitiveLog: () => (/* binding */ ContentBlockDeltaEventFilterSensitiveLog),\n/* harmony export */   ContentBlockDeltaFilterSensitiveLog: () => (/* binding */ ContentBlockDeltaFilterSensitiveLog),\n/* harmony export */   ContentBlockFilterSensitiveLog: () => (/* binding */ ContentBlockFilterSensitiveLog),\n/* harmony export */   ContentBlockStart: () => (/* binding */ ContentBlockStart),\n/* harmony export */   ConversationRole: () => (/* binding */ ConversationRole),\n/* harmony export */   ConverseOutput: () => (/* binding */ ConverseOutput),\n/* harmony export */   ConverseOutputFilterSensitiveLog: () => (/* binding */ ConverseOutputFilterSensitiveLog),\n/* harmony export */   ConverseRequestFilterSensitiveLog: () => (/* binding */ ConverseRequestFilterSensitiveLog),\n/* harmony export */   ConverseResponseFilterSensitiveLog: () => (/* binding */ ConverseResponseFilterSensitiveLog),\n/* harmony export */   ConverseStreamOutput: () => (/* binding */ ConverseStreamOutput),\n/* harmony export */   ConverseStreamOutputFilterSensitiveLog: () => (/* binding */ ConverseStreamOutputFilterSensitiveLog),\n/* harmony export */   ConverseStreamRequestFilterSensitiveLog: () => (/* binding */ ConverseStreamRequestFilterSensitiveLog),\n/* harmony export */   ConverseStreamResponseFilterSensitiveLog: () => (/* binding */ ConverseStreamResponseFilterSensitiveLog),\n/* harmony export */   DocumentFormat: () => (/* binding */ DocumentFormat),\n/* harmony export */   DocumentSource: () => (/* binding */ DocumentSource),\n/* harmony export */   GetAsyncInvokeResponseFilterSensitiveLog: () => (/* binding */ GetAsyncInvokeResponseFilterSensitiveLog),\n/* harmony export */   GuardrailAction: () => (/* binding */ GuardrailAction),\n/* harmony export */   GuardrailContentBlock: () => (/* binding */ GuardrailContentBlock),\n/* harmony export */   GuardrailContentBlockFilterSensitiveLog: () => (/* binding */ GuardrailContentBlockFilterSensitiveLog),\n/* harmony export */   GuardrailContentFilterConfidence: () => (/* binding */ GuardrailContentFilterConfidence),\n/* harmony export */   GuardrailContentFilterStrength: () => (/* binding */ GuardrailContentFilterStrength),\n/* harmony export */   GuardrailContentFilterType: () => (/* binding */ GuardrailContentFilterType),\n/* harmony export */   GuardrailContentPolicyAction: () => (/* binding */ GuardrailContentPolicyAction),\n/* harmony export */   GuardrailContentQualifier: () => (/* binding */ GuardrailContentQualifier),\n/* harmony export */   GuardrailContentSource: () => (/* binding */ GuardrailContentSource),\n/* harmony export */   GuardrailContextualGroundingFilterType: () => (/* binding */ GuardrailContextualGroundingFilterType),\n/* harmony export */   GuardrailContextualGroundingPolicyAction: () => (/* binding */ GuardrailContextualGroundingPolicyAction),\n/* harmony export */   GuardrailConverseContentBlock: () => (/* binding */ GuardrailConverseContentBlock),\n/* harmony export */   GuardrailConverseContentBlockFilterSensitiveLog: () => (/* binding */ GuardrailConverseContentBlockFilterSensitiveLog),\n/* harmony export */   GuardrailConverseContentQualifier: () => (/* binding */ GuardrailConverseContentQualifier),\n/* harmony export */   GuardrailConverseImageBlockFilterSensitiveLog: () => (/* binding */ GuardrailConverseImageBlockFilterSensitiveLog),\n/* harmony export */   GuardrailConverseImageFormat: () => (/* binding */ GuardrailConverseImageFormat),\n/* harmony export */   GuardrailConverseImageSource: () => (/* binding */ GuardrailConverseImageSource),\n/* harmony export */   GuardrailConverseImageSourceFilterSensitiveLog: () => (/* binding */ GuardrailConverseImageSourceFilterSensitiveLog),\n/* harmony export */   GuardrailImageBlockFilterSensitiveLog: () => (/* binding */ GuardrailImageBlockFilterSensitiveLog),\n/* harmony export */   GuardrailImageFormat: () => (/* binding */ GuardrailImageFormat),\n/* harmony export */   GuardrailImageSource: () => (/* binding */ GuardrailImageSource),\n/* harmony export */   GuardrailImageSourceFilterSensitiveLog: () => (/* binding */ GuardrailImageSourceFilterSensitiveLog),\n/* harmony export */   GuardrailManagedWordType: () => (/* binding */ GuardrailManagedWordType),\n/* harmony export */   GuardrailOutputScope: () => (/* binding */ GuardrailOutputScope),\n/* harmony export */   GuardrailPiiEntityType: () => (/* binding */ GuardrailPiiEntityType),\n/* harmony export */   GuardrailSensitiveInformationPolicyAction: () => (/* binding */ GuardrailSensitiveInformationPolicyAction),\n/* harmony export */   GuardrailStreamProcessingMode: () => (/* binding */ GuardrailStreamProcessingMode),\n/* harmony export */   GuardrailTopicPolicyAction: () => (/* binding */ GuardrailTopicPolicyAction),\n/* harmony export */   GuardrailTopicType: () => (/* binding */ GuardrailTopicType),\n/* harmony export */   GuardrailTrace: () => (/* binding */ GuardrailTrace),\n/* harmony export */   GuardrailWordPolicyAction: () => (/* binding */ GuardrailWordPolicyAction),\n/* harmony export */   ImageFormat: () => (/* binding */ ImageFormat),\n/* harmony export */   ImageSource: () => (/* binding */ ImageSource),\n/* harmony export */   InternalServerException: () => (/* binding */ InternalServerException),\n/* harmony export */   InvokeModelRequestFilterSensitiveLog: () => (/* binding */ InvokeModelRequestFilterSensitiveLog),\n/* harmony export */   InvokeModelResponseFilterSensitiveLog: () => (/* binding */ InvokeModelResponseFilterSensitiveLog),\n/* harmony export */   InvokeModelWithBidirectionalStreamInput: () => (/* binding */ InvokeModelWithBidirectionalStreamInput),\n/* harmony export */   InvokeModelWithBidirectionalStreamInputFilterSensitiveLog: () => (/* binding */ InvokeModelWithBidirectionalStreamInputFilterSensitiveLog),\n/* harmony export */   InvokeModelWithBidirectionalStreamOutput: () => (/* binding */ InvokeModelWithBidirectionalStreamOutput),\n/* harmony export */   InvokeModelWithBidirectionalStreamOutputFilterSensitiveLog: () => (/* binding */ InvokeModelWithBidirectionalStreamOutputFilterSensitiveLog),\n/* harmony export */   InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog: () => (/* binding */ InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog),\n/* harmony export */   InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog: () => (/* binding */ InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog),\n/* harmony export */   InvokeModelWithResponseStreamRequestFilterSensitiveLog: () => (/* binding */ InvokeModelWithResponseStreamRequestFilterSensitiveLog),\n/* harmony export */   InvokeModelWithResponseStreamResponseFilterSensitiveLog: () => (/* binding */ InvokeModelWithResponseStreamResponseFilterSensitiveLog),\n/* harmony export */   ListAsyncInvokesResponseFilterSensitiveLog: () => (/* binding */ ListAsyncInvokesResponseFilterSensitiveLog),\n/* harmony export */   MessageFilterSensitiveLog: () => (/* binding */ MessageFilterSensitiveLog),\n/* harmony export */   ModelErrorException: () => (/* binding */ ModelErrorException),\n/* harmony export */   ModelNotReadyException: () => (/* binding */ ModelNotReadyException),\n/* harmony export */   ModelStreamErrorException: () => (/* binding */ ModelStreamErrorException),\n/* harmony export */   ModelTimeoutException: () => (/* binding */ ModelTimeoutException),\n/* harmony export */   PayloadPartFilterSensitiveLog: () => (/* binding */ PayloadPartFilterSensitiveLog),\n/* harmony export */   PerformanceConfigLatency: () => (/* binding */ PerformanceConfigLatency),\n/* harmony export */   PromptVariableValues: () => (/* binding */ PromptVariableValues),\n/* harmony export */   ReasoningContentBlock: () => (/* binding */ ReasoningContentBlock),\n/* harmony export */   ReasoningContentBlockDelta: () => (/* binding */ ReasoningContentBlockDelta),\n/* harmony export */   ReasoningContentBlockDeltaFilterSensitiveLog: () => (/* binding */ ReasoningContentBlockDeltaFilterSensitiveLog),\n/* harmony export */   ReasoningContentBlockFilterSensitiveLog: () => (/* binding */ ReasoningContentBlockFilterSensitiveLog),\n/* harmony export */   ReasoningTextBlockFilterSensitiveLog: () => (/* binding */ ReasoningTextBlockFilterSensitiveLog),\n/* harmony export */   ResourceNotFoundException: () => (/* binding */ ResourceNotFoundException),\n/* harmony export */   ResponseStream: () => (/* binding */ ResponseStream),\n/* harmony export */   ResponseStreamFilterSensitiveLog: () => (/* binding */ ResponseStreamFilterSensitiveLog),\n/* harmony export */   ServiceQuotaExceededException: () => (/* binding */ ServiceQuotaExceededException),\n/* harmony export */   ServiceUnavailableException: () => (/* binding */ ServiceUnavailableException),\n/* harmony export */   SortAsyncInvocationBy: () => (/* binding */ SortAsyncInvocationBy),\n/* harmony export */   SortOrder: () => (/* binding */ SortOrder),\n/* harmony export */   StartAsyncInvokeRequestFilterSensitiveLog: () => (/* binding */ StartAsyncInvokeRequestFilterSensitiveLog),\n/* harmony export */   StopReason: () => (/* binding */ StopReason),\n/* harmony export */   SystemContentBlock: () => (/* binding */ SystemContentBlock),\n/* harmony export */   SystemContentBlockFilterSensitiveLog: () => (/* binding */ SystemContentBlockFilterSensitiveLog),\n/* harmony export */   ThrottlingException: () => (/* binding */ ThrottlingException),\n/* harmony export */   Tool: () => (/* binding */ Tool),\n/* harmony export */   ToolChoice: () => (/* binding */ ToolChoice),\n/* harmony export */   ToolInputSchema: () => (/* binding */ ToolInputSchema),\n/* harmony export */   ToolResultContentBlock: () => (/* binding */ ToolResultContentBlock),\n/* harmony export */   ToolResultStatus: () => (/* binding */ ToolResultStatus),\n/* harmony export */   Trace: () => (/* binding */ Trace),\n/* harmony export */   ValidationException: () => (/* binding */ ValidationException),\n/* harmony export */   VideoFormat: () => (/* binding */ VideoFormat),\n/* harmony export */   VideoSource: () => (/* binding */ VideoSource)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BedrockRuntimeServiceException */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/BedrockRuntimeServiceException.js\");\n\n\nclass AccessDeniedException extends _BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntimeServiceException {\n    name = \"AccessDeniedException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"AccessDeniedException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, AccessDeniedException.prototype);\n    }\n}\nvar AsyncInvokeOutputDataConfig;\n(function (AsyncInvokeOutputDataConfig) {\n    AsyncInvokeOutputDataConfig.visit = (value, visitor) => {\n        if (value.s3OutputDataConfig !== undefined)\n            return visitor.s3OutputDataConfig(value.s3OutputDataConfig);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(AsyncInvokeOutputDataConfig || (AsyncInvokeOutputDataConfig = {}));\nconst AsyncInvokeStatus = {\n    COMPLETED: \"Completed\",\n    FAILED: \"Failed\",\n    IN_PROGRESS: \"InProgress\",\n};\nclass InternalServerException extends _BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntimeServiceException {\n    name = \"InternalServerException\";\n    $fault = \"server\";\n    constructor(opts) {\n        super({\n            name: \"InternalServerException\",\n            $fault: \"server\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, InternalServerException.prototype);\n    }\n}\nclass ThrottlingException extends _BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntimeServiceException {\n    name = \"ThrottlingException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"ThrottlingException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ThrottlingException.prototype);\n    }\n}\nclass ValidationException extends _BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntimeServiceException {\n    name = \"ValidationException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"ValidationException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ValidationException.prototype);\n    }\n}\nconst SortAsyncInvocationBy = {\n    SUBMISSION_TIME: \"SubmissionTime\",\n};\nconst SortOrder = {\n    ASCENDING: \"Ascending\",\n    DESCENDING: \"Descending\",\n};\nclass ConflictException extends _BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntimeServiceException {\n    name = \"ConflictException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"ConflictException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ConflictException.prototype);\n    }\n}\nclass ResourceNotFoundException extends _BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntimeServiceException {\n    name = \"ResourceNotFoundException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"ResourceNotFoundException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ResourceNotFoundException.prototype);\n    }\n}\nclass ServiceQuotaExceededException extends _BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntimeServiceException {\n    name = \"ServiceQuotaExceededException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"ServiceQuotaExceededException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ServiceQuotaExceededException.prototype);\n    }\n}\nclass ServiceUnavailableException extends _BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntimeServiceException {\n    name = \"ServiceUnavailableException\";\n    $fault = \"server\";\n    constructor(opts) {\n        super({\n            name: \"ServiceUnavailableException\",\n            $fault: \"server\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ServiceUnavailableException.prototype);\n    }\n}\nconst GuardrailImageFormat = {\n    JPEG: \"jpeg\",\n    PNG: \"png\",\n};\nvar GuardrailImageSource;\n(function (GuardrailImageSource) {\n    GuardrailImageSource.visit = (value, visitor) => {\n        if (value.bytes !== undefined)\n            return visitor.bytes(value.bytes);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(GuardrailImageSource || (GuardrailImageSource = {}));\nconst GuardrailContentQualifier = {\n    GROUNDING_SOURCE: \"grounding_source\",\n    GUARD_CONTENT: \"guard_content\",\n    QUERY: \"query\",\n};\nvar GuardrailContentBlock;\n(function (GuardrailContentBlock) {\n    GuardrailContentBlock.visit = (value, visitor) => {\n        if (value.text !== undefined)\n            return visitor.text(value.text);\n        if (value.image !== undefined)\n            return visitor.image(value.image);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(GuardrailContentBlock || (GuardrailContentBlock = {}));\nconst GuardrailOutputScope = {\n    FULL: \"FULL\",\n    INTERVENTIONS: \"INTERVENTIONS\",\n};\nconst GuardrailContentSource = {\n    INPUT: \"INPUT\",\n    OUTPUT: \"OUTPUT\",\n};\nconst GuardrailAction = {\n    GUARDRAIL_INTERVENED: \"GUARDRAIL_INTERVENED\",\n    NONE: \"NONE\",\n};\nconst GuardrailContentPolicyAction = {\n    BLOCKED: \"BLOCKED\",\n    NONE: \"NONE\",\n};\nconst GuardrailContentFilterConfidence = {\n    HIGH: \"HIGH\",\n    LOW: \"LOW\",\n    MEDIUM: \"MEDIUM\",\n    NONE: \"NONE\",\n};\nconst GuardrailContentFilterStrength = {\n    HIGH: \"HIGH\",\n    LOW: \"LOW\",\n    MEDIUM: \"MEDIUM\",\n    NONE: \"NONE\",\n};\nconst GuardrailContentFilterType = {\n    HATE: \"HATE\",\n    INSULTS: \"INSULTS\",\n    MISCONDUCT: \"MISCONDUCT\",\n    PROMPT_ATTACK: \"PROMPT_ATTACK\",\n    SEXUAL: \"SEXUAL\",\n    VIOLENCE: \"VIOLENCE\",\n};\nconst GuardrailContextualGroundingPolicyAction = {\n    BLOCKED: \"BLOCKED\",\n    NONE: \"NONE\",\n};\nconst GuardrailContextualGroundingFilterType = {\n    GROUNDING: \"GROUNDING\",\n    RELEVANCE: \"RELEVANCE\",\n};\nconst GuardrailSensitiveInformationPolicyAction = {\n    ANONYMIZED: \"ANONYMIZED\",\n    BLOCKED: \"BLOCKED\",\n    NONE: \"NONE\",\n};\nconst GuardrailPiiEntityType = {\n    ADDRESS: \"ADDRESS\",\n    AGE: \"AGE\",\n    AWS_ACCESS_KEY: \"AWS_ACCESS_KEY\",\n    AWS_SECRET_KEY: \"AWS_SECRET_KEY\",\n    CA_HEALTH_NUMBER: \"CA_HEALTH_NUMBER\",\n    CA_SOCIAL_INSURANCE_NUMBER: \"CA_SOCIAL_INSURANCE_NUMBER\",\n    CREDIT_DEBIT_CARD_CVV: \"CREDIT_DEBIT_CARD_CVV\",\n    CREDIT_DEBIT_CARD_EXPIRY: \"CREDIT_DEBIT_CARD_EXPIRY\",\n    CREDIT_DEBIT_CARD_NUMBER: \"CREDIT_DEBIT_CARD_NUMBER\",\n    DRIVER_ID: \"DRIVER_ID\",\n    EMAIL: \"EMAIL\",\n    INTERNATIONAL_BANK_ACCOUNT_NUMBER: \"INTERNATIONAL_BANK_ACCOUNT_NUMBER\",\n    IP_ADDRESS: \"IP_ADDRESS\",\n    LICENSE_PLATE: \"LICENSE_PLATE\",\n    MAC_ADDRESS: \"MAC_ADDRESS\",\n    NAME: \"NAME\",\n    PASSWORD: \"PASSWORD\",\n    PHONE: \"PHONE\",\n    PIN: \"PIN\",\n    SWIFT_CODE: \"SWIFT_CODE\",\n    UK_NATIONAL_HEALTH_SERVICE_NUMBER: \"UK_NATIONAL_HEALTH_SERVICE_NUMBER\",\n    UK_NATIONAL_INSURANCE_NUMBER: \"UK_NATIONAL_INSURANCE_NUMBER\",\n    UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER: \"UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER\",\n    URL: \"URL\",\n    USERNAME: \"USERNAME\",\n    US_BANK_ACCOUNT_NUMBER: \"US_BANK_ACCOUNT_NUMBER\",\n    US_BANK_ROUTING_NUMBER: \"US_BANK_ROUTING_NUMBER\",\n    US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER: \"US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER\",\n    US_PASSPORT_NUMBER: \"US_PASSPORT_NUMBER\",\n    US_SOCIAL_SECURITY_NUMBER: \"US_SOCIAL_SECURITY_NUMBER\",\n    VEHICLE_IDENTIFICATION_NUMBER: \"VEHICLE_IDENTIFICATION_NUMBER\",\n};\nconst GuardrailTopicPolicyAction = {\n    BLOCKED: \"BLOCKED\",\n    NONE: \"NONE\",\n};\nconst GuardrailTopicType = {\n    DENY: \"DENY\",\n};\nconst GuardrailWordPolicyAction = {\n    BLOCKED: \"BLOCKED\",\n    NONE: \"NONE\",\n};\nconst GuardrailManagedWordType = {\n    PROFANITY: \"PROFANITY\",\n};\nconst GuardrailTrace = {\n    DISABLED: \"disabled\",\n    ENABLED: \"enabled\",\n    ENABLED_FULL: \"enabled_full\",\n};\nconst CachePointType = {\n    DEFAULT: \"default\",\n};\nconst DocumentFormat = {\n    CSV: \"csv\",\n    DOC: \"doc\",\n    DOCX: \"docx\",\n    HTML: \"html\",\n    MD: \"md\",\n    PDF: \"pdf\",\n    TXT: \"txt\",\n    XLS: \"xls\",\n    XLSX: \"xlsx\",\n};\nvar DocumentSource;\n(function (DocumentSource) {\n    DocumentSource.visit = (value, visitor) => {\n        if (value.bytes !== undefined)\n            return visitor.bytes(value.bytes);\n        if (value.s3Location !== undefined)\n            return visitor.s3Location(value.s3Location);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(DocumentSource || (DocumentSource = {}));\nconst GuardrailConverseImageFormat = {\n    JPEG: \"jpeg\",\n    PNG: \"png\",\n};\nvar GuardrailConverseImageSource;\n(function (GuardrailConverseImageSource) {\n    GuardrailConverseImageSource.visit = (value, visitor) => {\n        if (value.bytes !== undefined)\n            return visitor.bytes(value.bytes);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(GuardrailConverseImageSource || (GuardrailConverseImageSource = {}));\nconst GuardrailConverseContentQualifier = {\n    GROUNDING_SOURCE: \"grounding_source\",\n    GUARD_CONTENT: \"guard_content\",\n    QUERY: \"query\",\n};\nvar GuardrailConverseContentBlock;\n(function (GuardrailConverseContentBlock) {\n    GuardrailConverseContentBlock.visit = (value, visitor) => {\n        if (value.text !== undefined)\n            return visitor.text(value.text);\n        if (value.image !== undefined)\n            return visitor.image(value.image);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(GuardrailConverseContentBlock || (GuardrailConverseContentBlock = {}));\nconst ImageFormat = {\n    GIF: \"gif\",\n    JPEG: \"jpeg\",\n    PNG: \"png\",\n    WEBP: \"webp\",\n};\nvar ImageSource;\n(function (ImageSource) {\n    ImageSource.visit = (value, visitor) => {\n        if (value.bytes !== undefined)\n            return visitor.bytes(value.bytes);\n        if (value.s3Location !== undefined)\n            return visitor.s3Location(value.s3Location);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(ImageSource || (ImageSource = {}));\nvar ReasoningContentBlock;\n(function (ReasoningContentBlock) {\n    ReasoningContentBlock.visit = (value, visitor) => {\n        if (value.reasoningText !== undefined)\n            return visitor.reasoningText(value.reasoningText);\n        if (value.redactedContent !== undefined)\n            return visitor.redactedContent(value.redactedContent);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(ReasoningContentBlock || (ReasoningContentBlock = {}));\nconst VideoFormat = {\n    FLV: \"flv\",\n    MKV: \"mkv\",\n    MOV: \"mov\",\n    MP4: \"mp4\",\n    MPEG: \"mpeg\",\n    MPG: \"mpg\",\n    THREE_GP: \"three_gp\",\n    WEBM: \"webm\",\n    WMV: \"wmv\",\n};\nvar VideoSource;\n(function (VideoSource) {\n    VideoSource.visit = (value, visitor) => {\n        if (value.bytes !== undefined)\n            return visitor.bytes(value.bytes);\n        if (value.s3Location !== undefined)\n            return visitor.s3Location(value.s3Location);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(VideoSource || (VideoSource = {}));\nvar ToolResultContentBlock;\n(function (ToolResultContentBlock) {\n    ToolResultContentBlock.visit = (value, visitor) => {\n        if (value.json !== undefined)\n            return visitor.json(value.json);\n        if (value.text !== undefined)\n            return visitor.text(value.text);\n        if (value.image !== undefined)\n            return visitor.image(value.image);\n        if (value.document !== undefined)\n            return visitor.document(value.document);\n        if (value.video !== undefined)\n            return visitor.video(value.video);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(ToolResultContentBlock || (ToolResultContentBlock = {}));\nconst ToolResultStatus = {\n    ERROR: \"error\",\n    SUCCESS: \"success\",\n};\nvar ContentBlock;\n(function (ContentBlock) {\n    ContentBlock.visit = (value, visitor) => {\n        if (value.text !== undefined)\n            return visitor.text(value.text);\n        if (value.image !== undefined)\n            return visitor.image(value.image);\n        if (value.document !== undefined)\n            return visitor.document(value.document);\n        if (value.video !== undefined)\n            return visitor.video(value.video);\n        if (value.toolUse !== undefined)\n            return visitor.toolUse(value.toolUse);\n        if (value.toolResult !== undefined)\n            return visitor.toolResult(value.toolResult);\n        if (value.guardContent !== undefined)\n            return visitor.guardContent(value.guardContent);\n        if (value.cachePoint !== undefined)\n            return visitor.cachePoint(value.cachePoint);\n        if (value.reasoningContent !== undefined)\n            return visitor.reasoningContent(value.reasoningContent);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(ContentBlock || (ContentBlock = {}));\nconst ConversationRole = {\n    ASSISTANT: \"assistant\",\n    USER: \"user\",\n};\nconst PerformanceConfigLatency = {\n    OPTIMIZED: \"optimized\",\n    STANDARD: \"standard\",\n};\nvar PromptVariableValues;\n(function (PromptVariableValues) {\n    PromptVariableValues.visit = (value, visitor) => {\n        if (value.text !== undefined)\n            return visitor.text(value.text);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(PromptVariableValues || (PromptVariableValues = {}));\nvar SystemContentBlock;\n(function (SystemContentBlock) {\n    SystemContentBlock.visit = (value, visitor) => {\n        if (value.text !== undefined)\n            return visitor.text(value.text);\n        if (value.guardContent !== undefined)\n            return visitor.guardContent(value.guardContent);\n        if (value.cachePoint !== undefined)\n            return visitor.cachePoint(value.cachePoint);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(SystemContentBlock || (SystemContentBlock = {}));\nvar ToolChoice;\n(function (ToolChoice) {\n    ToolChoice.visit = (value, visitor) => {\n        if (value.auto !== undefined)\n            return visitor.auto(value.auto);\n        if (value.any !== undefined)\n            return visitor.any(value.any);\n        if (value.tool !== undefined)\n            return visitor.tool(value.tool);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(ToolChoice || (ToolChoice = {}));\nvar ToolInputSchema;\n(function (ToolInputSchema) {\n    ToolInputSchema.visit = (value, visitor) => {\n        if (value.json !== undefined)\n            return visitor.json(value.json);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(ToolInputSchema || (ToolInputSchema = {}));\nvar Tool;\n(function (Tool) {\n    Tool.visit = (value, visitor) => {\n        if (value.toolSpec !== undefined)\n            return visitor.toolSpec(value.toolSpec);\n        if (value.cachePoint !== undefined)\n            return visitor.cachePoint(value.cachePoint);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(Tool || (Tool = {}));\nvar ConverseOutput;\n(function (ConverseOutput) {\n    ConverseOutput.visit = (value, visitor) => {\n        if (value.message !== undefined)\n            return visitor.message(value.message);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(ConverseOutput || (ConverseOutput = {}));\nconst StopReason = {\n    CONTENT_FILTERED: \"content_filtered\",\n    END_TURN: \"end_turn\",\n    GUARDRAIL_INTERVENED: \"guardrail_intervened\",\n    MAX_TOKENS: \"max_tokens\",\n    STOP_SEQUENCE: \"stop_sequence\",\n    TOOL_USE: \"tool_use\",\n};\nclass ModelErrorException extends _BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntimeServiceException {\n    name = \"ModelErrorException\";\n    $fault = \"client\";\n    originalStatusCode;\n    resourceName;\n    constructor(opts) {\n        super({\n            name: \"ModelErrorException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ModelErrorException.prototype);\n        this.originalStatusCode = opts.originalStatusCode;\n        this.resourceName = opts.resourceName;\n    }\n}\nclass ModelNotReadyException extends _BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntimeServiceException {\n    name = \"ModelNotReadyException\";\n    $fault = \"client\";\n    $retryable = {};\n    constructor(opts) {\n        super({\n            name: \"ModelNotReadyException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ModelNotReadyException.prototype);\n    }\n}\nclass ModelTimeoutException extends _BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntimeServiceException {\n    name = \"ModelTimeoutException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"ModelTimeoutException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ModelTimeoutException.prototype);\n    }\n}\nconst GuardrailStreamProcessingMode = {\n    ASYNC: \"async\",\n    SYNC: \"sync\",\n};\nvar ReasoningContentBlockDelta;\n(function (ReasoningContentBlockDelta) {\n    ReasoningContentBlockDelta.visit = (value, visitor) => {\n        if (value.text !== undefined)\n            return visitor.text(value.text);\n        if (value.redactedContent !== undefined)\n            return visitor.redactedContent(value.redactedContent);\n        if (value.signature !== undefined)\n            return visitor.signature(value.signature);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(ReasoningContentBlockDelta || (ReasoningContentBlockDelta = {}));\nvar ContentBlockDelta;\n(function (ContentBlockDelta) {\n    ContentBlockDelta.visit = (value, visitor) => {\n        if (value.text !== undefined)\n            return visitor.text(value.text);\n        if (value.toolUse !== undefined)\n            return visitor.toolUse(value.toolUse);\n        if (value.reasoningContent !== undefined)\n            return visitor.reasoningContent(value.reasoningContent);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(ContentBlockDelta || (ContentBlockDelta = {}));\nvar ContentBlockStart;\n(function (ContentBlockStart) {\n    ContentBlockStart.visit = (value, visitor) => {\n        if (value.toolUse !== undefined)\n            return visitor.toolUse(value.toolUse);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(ContentBlockStart || (ContentBlockStart = {}));\nclass ModelStreamErrorException extends _BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntimeServiceException {\n    name = \"ModelStreamErrorException\";\n    $fault = \"client\";\n    originalStatusCode;\n    originalMessage;\n    constructor(opts) {\n        super({\n            name: \"ModelStreamErrorException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ModelStreamErrorException.prototype);\n        this.originalStatusCode = opts.originalStatusCode;\n        this.originalMessage = opts.originalMessage;\n    }\n}\nvar ConverseStreamOutput;\n(function (ConverseStreamOutput) {\n    ConverseStreamOutput.visit = (value, visitor) => {\n        if (value.messageStart !== undefined)\n            return visitor.messageStart(value.messageStart);\n        if (value.contentBlockStart !== undefined)\n            return visitor.contentBlockStart(value.contentBlockStart);\n        if (value.contentBlockDelta !== undefined)\n            return visitor.contentBlockDelta(value.contentBlockDelta);\n        if (value.contentBlockStop !== undefined)\n            return visitor.contentBlockStop(value.contentBlockStop);\n        if (value.messageStop !== undefined)\n            return visitor.messageStop(value.messageStop);\n        if (value.metadata !== undefined)\n            return visitor.metadata(value.metadata);\n        if (value.internalServerException !== undefined)\n            return visitor.internalServerException(value.internalServerException);\n        if (value.modelStreamErrorException !== undefined)\n            return visitor.modelStreamErrorException(value.modelStreamErrorException);\n        if (value.validationException !== undefined)\n            return visitor.validationException(value.validationException);\n        if (value.throttlingException !== undefined)\n            return visitor.throttlingException(value.throttlingException);\n        if (value.serviceUnavailableException !== undefined)\n            return visitor.serviceUnavailableException(value.serviceUnavailableException);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(ConverseStreamOutput || (ConverseStreamOutput = {}));\nconst Trace = {\n    DISABLED: \"DISABLED\",\n    ENABLED: \"ENABLED\",\n    ENABLED_FULL: \"ENABLED_FULL\",\n};\nvar InvokeModelWithBidirectionalStreamInput;\n(function (InvokeModelWithBidirectionalStreamInput) {\n    InvokeModelWithBidirectionalStreamInput.visit = (value, visitor) => {\n        if (value.chunk !== undefined)\n            return visitor.chunk(value.chunk);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(InvokeModelWithBidirectionalStreamInput || (InvokeModelWithBidirectionalStreamInput = {}));\nvar InvokeModelWithBidirectionalStreamOutput;\n(function (InvokeModelWithBidirectionalStreamOutput) {\n    InvokeModelWithBidirectionalStreamOutput.visit = (value, visitor) => {\n        if (value.chunk !== undefined)\n            return visitor.chunk(value.chunk);\n        if (value.internalServerException !== undefined)\n            return visitor.internalServerException(value.internalServerException);\n        if (value.modelStreamErrorException !== undefined)\n            return visitor.modelStreamErrorException(value.modelStreamErrorException);\n        if (value.validationException !== undefined)\n            return visitor.validationException(value.validationException);\n        if (value.throttlingException !== undefined)\n            return visitor.throttlingException(value.throttlingException);\n        if (value.modelTimeoutException !== undefined)\n            return visitor.modelTimeoutException(value.modelTimeoutException);\n        if (value.serviceUnavailableException !== undefined)\n            return visitor.serviceUnavailableException(value.serviceUnavailableException);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(InvokeModelWithBidirectionalStreamOutput || (InvokeModelWithBidirectionalStreamOutput = {}));\nvar ResponseStream;\n(function (ResponseStream) {\n    ResponseStream.visit = (value, visitor) => {\n        if (value.chunk !== undefined)\n            return visitor.chunk(value.chunk);\n        if (value.internalServerException !== undefined)\n            return visitor.internalServerException(value.internalServerException);\n        if (value.modelStreamErrorException !== undefined)\n            return visitor.modelStreamErrorException(value.modelStreamErrorException);\n        if (value.validationException !== undefined)\n            return visitor.validationException(value.validationException);\n        if (value.throttlingException !== undefined)\n            return visitor.throttlingException(value.throttlingException);\n        if (value.modelTimeoutException !== undefined)\n            return visitor.modelTimeoutException(value.modelTimeoutException);\n        if (value.serviceUnavailableException !== undefined)\n            return visitor.serviceUnavailableException(value.serviceUnavailableException);\n        return visitor._(value.$unknown[0], value.$unknown[1]);\n    };\n})(ResponseStream || (ResponseStream = {}));\nconst GetAsyncInvokeResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.failureMessage && { failureMessage: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n    ...(obj.outputDataConfig && { outputDataConfig: obj.outputDataConfig }),\n});\nconst AsyncInvokeSummaryFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.failureMessage && { failureMessage: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n    ...(obj.outputDataConfig && { outputDataConfig: obj.outputDataConfig }),\n});\nconst ListAsyncInvokesResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.asyncInvokeSummaries && {\n        asyncInvokeSummaries: obj.asyncInvokeSummaries.map((item) => AsyncInvokeSummaryFilterSensitiveLog(item)),\n    }),\n});\nconst StartAsyncInvokeRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.modelInput && { modelInput: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n    ...(obj.outputDataConfig && { outputDataConfig: obj.outputDataConfig }),\n});\nconst GuardrailImageSourceFilterSensitiveLog = (obj) => {\n    if (obj.bytes !== undefined)\n        return { bytes: obj.bytes };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst GuardrailImageBlockFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.source && { source: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst GuardrailContentBlockFilterSensitiveLog = (obj) => {\n    if (obj.text !== undefined)\n        return { text: obj.text };\n    if (obj.image !== undefined)\n        return { image: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst ApplyGuardrailRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.content && { content: obj.content.map((item) => GuardrailContentBlockFilterSensitiveLog(item)) }),\n});\nconst GuardrailConverseImageSourceFilterSensitiveLog = (obj) => {\n    if (obj.bytes !== undefined)\n        return { bytes: obj.bytes };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst GuardrailConverseImageBlockFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.source && { source: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst GuardrailConverseContentBlockFilterSensitiveLog = (obj) => {\n    if (obj.text !== undefined)\n        return { text: obj.text };\n    if (obj.image !== undefined)\n        return { image: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst ReasoningTextBlockFilterSensitiveLog = (obj) => ({\n    ...obj,\n});\nconst ReasoningContentBlockFilterSensitiveLog = (obj) => {\n    if (obj.reasoningText !== undefined)\n        return { reasoningText: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING };\n    if (obj.redactedContent !== undefined)\n        return { redactedContent: obj.redactedContent };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst ContentBlockFilterSensitiveLog = (obj) => {\n    if (obj.text !== undefined)\n        return { text: obj.text };\n    if (obj.image !== undefined)\n        return { image: obj.image };\n    if (obj.document !== undefined)\n        return { document: obj.document };\n    if (obj.video !== undefined)\n        return { video: obj.video };\n    if (obj.toolUse !== undefined)\n        return { toolUse: obj.toolUse };\n    if (obj.toolResult !== undefined)\n        return { toolResult: obj.toolResult };\n    if (obj.guardContent !== undefined)\n        return { guardContent: GuardrailConverseContentBlockFilterSensitiveLog(obj.guardContent) };\n    if (obj.cachePoint !== undefined)\n        return { cachePoint: obj.cachePoint };\n    if (obj.reasoningContent !== undefined)\n        return { reasoningContent: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst MessageFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.content && { content: obj.content.map((item) => ContentBlockFilterSensitiveLog(item)) }),\n});\nconst SystemContentBlockFilterSensitiveLog = (obj) => {\n    if (obj.text !== undefined)\n        return { text: obj.text };\n    if (obj.guardContent !== undefined)\n        return { guardContent: GuardrailConverseContentBlockFilterSensitiveLog(obj.guardContent) };\n    if (obj.cachePoint !== undefined)\n        return { cachePoint: obj.cachePoint };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst ConverseRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.messages && { messages: obj.messages.map((item) => MessageFilterSensitiveLog(item)) }),\n    ...(obj.system && { system: obj.system.map((item) => SystemContentBlockFilterSensitiveLog(item)) }),\n    ...(obj.toolConfig && { toolConfig: obj.toolConfig }),\n    ...(obj.promptVariables && { promptVariables: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n    ...(obj.requestMetadata && { requestMetadata: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst ConverseOutputFilterSensitiveLog = (obj) => {\n    if (obj.message !== undefined)\n        return { message: MessageFilterSensitiveLog(obj.message) };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst ConverseResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.output && { output: ConverseOutputFilterSensitiveLog(obj.output) }),\n});\nconst ConverseStreamRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.messages && { messages: obj.messages.map((item) => MessageFilterSensitiveLog(item)) }),\n    ...(obj.system && { system: obj.system.map((item) => SystemContentBlockFilterSensitiveLog(item)) }),\n    ...(obj.toolConfig && { toolConfig: obj.toolConfig }),\n    ...(obj.promptVariables && { promptVariables: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n    ...(obj.requestMetadata && { requestMetadata: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst ReasoningContentBlockDeltaFilterSensitiveLog = (obj) => {\n    if (obj.text !== undefined)\n        return { text: obj.text };\n    if (obj.redactedContent !== undefined)\n        return { redactedContent: obj.redactedContent };\n    if (obj.signature !== undefined)\n        return { signature: obj.signature };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst ContentBlockDeltaFilterSensitiveLog = (obj) => {\n    if (obj.text !== undefined)\n        return { text: obj.text };\n    if (obj.toolUse !== undefined)\n        return { toolUse: obj.toolUse };\n    if (obj.reasoningContent !== undefined)\n        return { reasoningContent: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst ContentBlockDeltaEventFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.delta && { delta: ContentBlockDeltaFilterSensitiveLog(obj.delta) }),\n});\nconst ConverseStreamOutputFilterSensitiveLog = (obj) => {\n    if (obj.messageStart !== undefined)\n        return { messageStart: obj.messageStart };\n    if (obj.contentBlockStart !== undefined)\n        return { contentBlockStart: obj.contentBlockStart };\n    if (obj.contentBlockDelta !== undefined)\n        return { contentBlockDelta: ContentBlockDeltaEventFilterSensitiveLog(obj.contentBlockDelta) };\n    if (obj.contentBlockStop !== undefined)\n        return { contentBlockStop: obj.contentBlockStop };\n    if (obj.messageStop !== undefined)\n        return { messageStop: obj.messageStop };\n    if (obj.metadata !== undefined)\n        return { metadata: obj.metadata };\n    if (obj.internalServerException !== undefined)\n        return { internalServerException: obj.internalServerException };\n    if (obj.modelStreamErrorException !== undefined)\n        return { modelStreamErrorException: obj.modelStreamErrorException };\n    if (obj.validationException !== undefined)\n        return { validationException: obj.validationException };\n    if (obj.throttlingException !== undefined)\n        return { throttlingException: obj.throttlingException };\n    if (obj.serviceUnavailableException !== undefined)\n        return { serviceUnavailableException: obj.serviceUnavailableException };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst ConverseStreamResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.stream && { stream: \"STREAMING_CONTENT\" }),\n});\nconst InvokeModelRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.body && { body: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst InvokeModelResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.body && { body: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst BidirectionalInputPayloadPartFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.bytes && { bytes: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst InvokeModelWithBidirectionalStreamInputFilterSensitiveLog = (obj) => {\n    if (obj.chunk !== undefined)\n        return { chunk: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.body && { body: \"STREAMING_CONTENT\" }),\n});\nconst BidirectionalOutputPayloadPartFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.bytes && { bytes: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst InvokeModelWithBidirectionalStreamOutputFilterSensitiveLog = (obj) => {\n    if (obj.chunk !== undefined)\n        return { chunk: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING };\n    if (obj.internalServerException !== undefined)\n        return { internalServerException: obj.internalServerException };\n    if (obj.modelStreamErrorException !== undefined)\n        return { modelStreamErrorException: obj.modelStreamErrorException };\n    if (obj.validationException !== undefined)\n        return { validationException: obj.validationException };\n    if (obj.throttlingException !== undefined)\n        return { throttlingException: obj.throttlingException };\n    if (obj.modelTimeoutException !== undefined)\n        return { modelTimeoutException: obj.modelTimeoutException };\n    if (obj.serviceUnavailableException !== undefined)\n        return { serviceUnavailableException: obj.serviceUnavailableException };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.body && { body: \"STREAMING_CONTENT\" }),\n});\nconst InvokeModelWithResponseStreamRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.body && { body: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst PayloadPartFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.bytes && { bytes: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst ResponseStreamFilterSensitiveLog = (obj) => {\n    if (obj.chunk !== undefined)\n        return { chunk: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING };\n    if (obj.internalServerException !== undefined)\n        return { internalServerException: obj.internalServerException };\n    if (obj.modelStreamErrorException !== undefined)\n        return { modelStreamErrorException: obj.modelStreamErrorException };\n    if (obj.validationException !== undefined)\n        return { validationException: obj.validationException };\n    if (obj.throttlingException !== undefined)\n        return { throttlingException: obj.throttlingException };\n    if (obj.modelTimeoutException !== undefined)\n        return { modelTimeoutException: obj.modelTimeoutException };\n    if (obj.serviceUnavailableException !== undefined)\n        return { serviceUnavailableException: obj.serviceUnavailableException };\n    if (obj.$unknown !== undefined)\n        return { [obj.$unknown[0]]: \"UNKNOWN\" };\n};\nconst InvokeModelWithResponseStreamResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.body && { body: \"STREAMING_CONTENT\" }),\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/pagination/ListAsyncInvokesPaginator.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/pagination/ListAsyncInvokesPaginator.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paginateListAsyncInvokes: () => (/* binding */ paginateListAsyncInvokes)\n/* harmony export */ });\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _BedrockRuntimeClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../BedrockRuntimeClient */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/BedrockRuntimeClient.js\");\n/* harmony import */ var _commands_ListAsyncInvokesCommand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../commands/ListAsyncInvokesCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/commands/ListAsyncInvokesCommand.js\");\n\n\n\nconst paginateListAsyncInvokes = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.createPaginator)(_BedrockRuntimeClient__WEBPACK_IMPORTED_MODULE_1__.BedrockRuntimeClient, _commands_ListAsyncInvokesCommand__WEBPACK_IMPORTED_MODULE_2__.ListAsyncInvokesCommand, \"nextToken\", \"nextToken\", \"maxResults\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvcGFnaW5hdGlvbi9MaXN0QXN5bmNJbnZva2VzUGFnaW5hdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0M7QUFDZ0I7QUFDZ0I7QUFDeEUsaUNBQWlDLDZEQUFlLENBQUMsdUVBQW9CLEVBQUUsc0ZBQXVCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvcGFnaW5hdGlvbi9MaXN0QXN5bmNJbnZva2VzUGFnaW5hdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVBhZ2luYXRvciB9IGZyb20gXCJAc21pdGh5L2NvcmVcIjtcbmltcG9ydCB7IEJlZHJvY2tSdW50aW1lQ2xpZW50IH0gZnJvbSBcIi4uL0JlZHJvY2tSdW50aW1lQ2xpZW50XCI7XG5pbXBvcnQgeyBMaXN0QXN5bmNJbnZva2VzQ29tbWFuZCwgfSBmcm9tIFwiLi4vY29tbWFuZHMvTGlzdEFzeW5jSW52b2tlc0NvbW1hbmRcIjtcbmV4cG9ydCBjb25zdCBwYWdpbmF0ZUxpc3RBc3luY0ludm9rZXMgPSBjcmVhdGVQYWdpbmF0b3IoQmVkcm9ja1J1bnRpbWVDbGllbnQsIExpc3RBc3luY0ludm9rZXNDb21tYW5kLCBcIm5leHRUb2tlblwiLCBcIm5leHRUb2tlblwiLCBcIm1heFJlc3VsdHNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/pagination/ListAsyncInvokesPaginator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/pagination/index.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/pagination/index.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   paginateListAsyncInvokes: () => (/* reexport safe */ _ListAsyncInvokesPaginator__WEBPACK_IMPORTED_MODULE_0__.paginateListAsyncInvokes)\n/* harmony export */ });\n/* harmony import */ var _ListAsyncInvokesPaginator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ListAsyncInvokesPaginator */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/pagination/ListAsyncInvokesPaginator.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvcGFnaW5hdGlvbi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE2QjtBQUNlIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LWJlZHJvY2stcnVudGltZUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtYmVkcm9jay1ydW50aW1lL2Rpc3QtZXMvcGFnaW5hdGlvbi9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9JbnRlcmZhY2VzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9MaXN0QXN5bmNJbnZva2VzUGFnaW5hdG9yXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/pagination/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/protocols/Aws_restJson1.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/protocols/Aws_restJson1.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   de_ApplyGuardrailCommand: () => (/* binding */ de_ApplyGuardrailCommand),\n/* harmony export */   de_ConverseCommand: () => (/* binding */ de_ConverseCommand),\n/* harmony export */   de_ConverseStreamCommand: () => (/* binding */ de_ConverseStreamCommand),\n/* harmony export */   de_GetAsyncInvokeCommand: () => (/* binding */ de_GetAsyncInvokeCommand),\n/* harmony export */   de_InvokeModelCommand: () => (/* binding */ de_InvokeModelCommand),\n/* harmony export */   de_InvokeModelWithBidirectionalStreamCommand: () => (/* binding */ de_InvokeModelWithBidirectionalStreamCommand),\n/* harmony export */   de_InvokeModelWithResponseStreamCommand: () => (/* binding */ de_InvokeModelWithResponseStreamCommand),\n/* harmony export */   de_ListAsyncInvokesCommand: () => (/* binding */ de_ListAsyncInvokesCommand),\n/* harmony export */   de_StartAsyncInvokeCommand: () => (/* binding */ de_StartAsyncInvokeCommand),\n/* harmony export */   se_ApplyGuardrailCommand: () => (/* binding */ se_ApplyGuardrailCommand),\n/* harmony export */   se_ConverseCommand: () => (/* binding */ se_ConverseCommand),\n/* harmony export */   se_ConverseStreamCommand: () => (/* binding */ se_ConverseStreamCommand),\n/* harmony export */   se_GetAsyncInvokeCommand: () => (/* binding */ se_GetAsyncInvokeCommand),\n/* harmony export */   se_InvokeModelCommand: () => (/* binding */ se_InvokeModelCommand),\n/* harmony export */   se_InvokeModelWithBidirectionalStreamCommand: () => (/* binding */ se_InvokeModelWithBidirectionalStreamCommand),\n/* harmony export */   se_InvokeModelWithResponseStreamCommand: () => (/* binding */ se_InvokeModelWithResponseStreamCommand),\n/* harmony export */   se_ListAsyncInvokesCommand: () => (/* binding */ se_ListAsyncInvokesCommand),\n/* harmony export */   se_StartAsyncInvokeCommand: () => (/* binding */ se_StartAsyncInvokeCommand)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/json/parseJsonBody.js\");\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/json/awsExpectUnion.js\");\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var _models_BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../models/BedrockRuntimeServiceException */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/BedrockRuntimeServiceException.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/models/models_0.js\");\n\n\n\n\n\n\nconst se_ApplyGuardrailCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = {\n        \"content-type\": \"application/json\",\n    };\n    b.bp(\"/guardrail/{guardrailIdentifier}/version/{guardrailVersion}/apply\");\n    b.p(\"guardrailIdentifier\", () => input.guardrailIdentifier, \"{guardrailIdentifier}\", false);\n    b.p(\"guardrailVersion\", () => input.guardrailVersion, \"{guardrailVersion}\", false);\n    let body;\n    body = JSON.stringify((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        content: (_) => se_GuardrailContentBlockList(_, context),\n        outputScope: [],\n        source: [],\n    }));\n    b.m(\"POST\").h(headers).b(body);\n    return b.build();\n};\nconst se_ConverseCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = {\n        \"content-type\": \"application/json\",\n    };\n    b.bp(\"/model/{modelId}/converse\");\n    b.p(\"modelId\", () => input.modelId, \"{modelId}\", false);\n    let body;\n    body = JSON.stringify((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        additionalModelRequestFields: (_) => se_Document(_, context),\n        additionalModelResponseFieldPaths: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(_),\n        guardrailConfig: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(_),\n        inferenceConfig: (_) => se_InferenceConfiguration(_, context),\n        messages: (_) => se_Messages(_, context),\n        performanceConfig: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(_),\n        promptVariables: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(_),\n        requestMetadata: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(_),\n        system: (_) => se_SystemContentBlocks(_, context),\n        toolConfig: (_) => se_ToolConfiguration(_, context),\n    }));\n    b.m(\"POST\").h(headers).b(body);\n    return b.build();\n};\nconst se_ConverseStreamCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = {\n        \"content-type\": \"application/json\",\n    };\n    b.bp(\"/model/{modelId}/converse-stream\");\n    b.p(\"modelId\", () => input.modelId, \"{modelId}\", false);\n    let body;\n    body = JSON.stringify((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        additionalModelRequestFields: (_) => se_Document(_, context),\n        additionalModelResponseFieldPaths: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(_),\n        guardrailConfig: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(_),\n        inferenceConfig: (_) => se_InferenceConfiguration(_, context),\n        messages: (_) => se_Messages(_, context),\n        performanceConfig: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(_),\n        promptVariables: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(_),\n        requestMetadata: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(_),\n        system: (_) => se_SystemContentBlocks(_, context),\n        toolConfig: (_) => se_ToolConfiguration(_, context),\n    }));\n    b.m(\"POST\").h(headers).b(body);\n    return b.build();\n};\nconst se_GetAsyncInvokeCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = {};\n    b.bp(\"/async-invoke/{invocationArn}\");\n    b.p(\"invocationArn\", () => input.invocationArn, \"{invocationArn}\", false);\n    let body;\n    b.m(\"GET\").h(headers).b(body);\n    return b.build();\n};\nconst se_InvokeModelCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({}, _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.isSerializableHeaderValue, {\n        [_ct]: input[_cT] || \"application/octet-stream\",\n        [_a]: input[_a],\n        [_xabt]: input[_t],\n        [_xabg]: input[_gI],\n        [_xabg_]: input[_gV],\n        [_xabpl]: input[_pCL],\n    });\n    b.bp(\"/model/{modelId}/invoke\");\n    b.p(\"modelId\", () => input.modelId, \"{modelId}\", false);\n    let body;\n    if (input.body !== undefined) {\n        body = input.body;\n    }\n    b.m(\"POST\").h(headers).b(body);\n    return b.build();\n};\nconst se_InvokeModelWithBidirectionalStreamCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = {\n        \"content-type\": \"application/json\",\n    };\n    b.bp(\"/model/{modelId}/invoke-with-bidirectional-stream\");\n    b.p(\"modelId\", () => input.modelId, \"{modelId}\", false);\n    let body;\n    if (input.body !== undefined) {\n        body = se_InvokeModelWithBidirectionalStreamInput(input.body, context);\n    }\n    b.m(\"POST\").h(headers).b(body);\n    return b.build();\n};\nconst se_InvokeModelWithResponseStreamCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({}, _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.isSerializableHeaderValue, {\n        [_ct]: input[_cT] || \"application/octet-stream\",\n        [_xaba]: input[_a],\n        [_xabt]: input[_t],\n        [_xabg]: input[_gI],\n        [_xabg_]: input[_gV],\n        [_xabpl]: input[_pCL],\n    });\n    b.bp(\"/model/{modelId}/invoke-with-response-stream\");\n    b.p(\"modelId\", () => input.modelId, \"{modelId}\", false);\n    let body;\n    if (input.body !== undefined) {\n        body = input.body;\n    }\n    b.m(\"POST\").h(headers).b(body);\n    return b.build();\n};\nconst se_ListAsyncInvokesCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = {};\n    b.bp(\"/async-invoke\");\n    const query = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        [_sTA]: [() => input.submitTimeAfter !== void 0, () => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.serializeDateTime)(input[_sTA]).toString()],\n        [_sTB]: [() => input.submitTimeBefore !== void 0, () => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.serializeDateTime)(input[_sTB]).toString()],\n        [_sE]: [, input[_sE]],\n        [_mR]: [() => input.maxResults !== void 0, () => input[_mR].toString()],\n        [_nT]: [, input[_nT]],\n        [_sB]: [, input[_sB]],\n        [_sO]: [, input[_sO]],\n    });\n    let body;\n    b.m(\"GET\").h(headers).q(query).b(body);\n    return b.build();\n};\nconst se_StartAsyncInvokeCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = {\n        \"content-type\": \"application/json\",\n    };\n    b.bp(\"/async-invoke\");\n    let body;\n    body = JSON.stringify((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        clientRequestToken: [true, (_) => _ ?? (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])()],\n        modelId: [],\n        modelInput: (_) => se_ModelInputPayload(_, context),\n        outputDataConfig: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(_),\n        tags: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(_),\n    }));\n    b.m(\"POST\").h(headers).b(body);\n    return b.build();\n};\nconst de_ApplyGuardrailCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectObject)(await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context)), \"body\");\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        action: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        actionReason: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        assessments: (_) => de_GuardrailAssessmentList(_, context),\n        guardrailCoverage: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        outputs: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        usage: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nconst de_ConverseCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectObject)(await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context)), \"body\");\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        additionalModelResponseFields: (_) => de_Document(_, context),\n        metrics: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        output: (_) => de_ConverseOutput((0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.awsExpectUnion)(_), context),\n        performanceConfig: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        stopReason: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        trace: (_) => de_ConverseTrace(_, context),\n        usage: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nconst de_ConverseStreamCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = output.body;\n    contents.stream = de_ConverseStreamOutput(data, context);\n    return contents;\n};\nconst de_GetAsyncInvokeCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectObject)(await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context)), \"body\");\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        clientRequestToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        endTime: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.parseRfc3339DateTimeWithOffset)(_)),\n        failureMessage: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        invocationArn: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        lastModifiedTime: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.parseRfc3339DateTimeWithOffset)(_)),\n        modelArn: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        outputDataConfig: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)((0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.awsExpectUnion)(_)),\n        status: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        submitTime: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.parseRfc3339DateTimeWithOffset)(_)),\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nconst de_InvokeModelCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n        [_cT]: [, output.headers[_ct]],\n        [_pCL]: [, output.headers[_xabpl]],\n    });\n    const data = await (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.collectBody)(output.body, context);\n    contents.body = data;\n    return contents;\n};\nconst de_InvokeModelWithBidirectionalStreamCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = output.body;\n    contents.body = de_InvokeModelWithBidirectionalStreamOutput(data, context);\n    return contents;\n};\nconst de_InvokeModelWithResponseStreamCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n        [_cT]: [, output.headers[_xabct]],\n        [_pCL]: [, output.headers[_xabpl]],\n    });\n    const data = output.body;\n    contents.body = de_ResponseStream(data, context);\n    return contents;\n};\nconst de_ListAsyncInvokesCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectObject)(await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context)), \"body\");\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        asyncInvokeSummaries: (_) => de_AsyncInvokeSummaries(_, context),\n        nextToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nconst de_StartAsyncInvokeCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectObject)(await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context)), \"body\");\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        invocationArn: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nconst de_CommandError = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonErrorBody)(output.body, context),\n    };\n    const errorCode = (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.loadRestJsonErrorCode)(output, parsedOutput.body);\n    switch (errorCode) {\n        case \"AccessDeniedException\":\n        case \"com.amazonaws.bedrockruntime#AccessDeniedException\":\n            throw await de_AccessDeniedExceptionRes(parsedOutput, context);\n        case \"InternalServerException\":\n        case \"com.amazonaws.bedrockruntime#InternalServerException\":\n            throw await de_InternalServerExceptionRes(parsedOutput, context);\n        case \"ResourceNotFoundException\":\n        case \"com.amazonaws.bedrockruntime#ResourceNotFoundException\":\n            throw await de_ResourceNotFoundExceptionRes(parsedOutput, context);\n        case \"ServiceQuotaExceededException\":\n        case \"com.amazonaws.bedrockruntime#ServiceQuotaExceededException\":\n            throw await de_ServiceQuotaExceededExceptionRes(parsedOutput, context);\n        case \"ThrottlingException\":\n        case \"com.amazonaws.bedrockruntime#ThrottlingException\":\n            throw await de_ThrottlingExceptionRes(parsedOutput, context);\n        case \"ValidationException\":\n        case \"com.amazonaws.bedrockruntime#ValidationException\":\n            throw await de_ValidationExceptionRes(parsedOutput, context);\n        case \"ModelErrorException\":\n        case \"com.amazonaws.bedrockruntime#ModelErrorException\":\n            throw await de_ModelErrorExceptionRes(parsedOutput, context);\n        case \"ModelNotReadyException\":\n        case \"com.amazonaws.bedrockruntime#ModelNotReadyException\":\n            throw await de_ModelNotReadyExceptionRes(parsedOutput, context);\n        case \"ModelTimeoutException\":\n        case \"com.amazonaws.bedrockruntime#ModelTimeoutException\":\n            throw await de_ModelTimeoutExceptionRes(parsedOutput, context);\n        case \"ServiceUnavailableException\":\n        case \"com.amazonaws.bedrockruntime#ServiceUnavailableException\":\n            throw await de_ServiceUnavailableExceptionRes(parsedOutput, context);\n        case \"ModelStreamErrorException\":\n        case \"com.amazonaws.bedrockruntime#ModelStreamErrorException\":\n            throw await de_ModelStreamErrorExceptionRes(parsedOutput, context);\n        case \"ConflictException\":\n        case \"com.amazonaws.bedrockruntime#ConflictException\":\n            throw await de_ConflictExceptionRes(parsedOutput, context);\n        default:\n            const parsedBody = parsedOutput.body;\n            return throwDefaultError({\n                output,\n                parsedBody,\n                errorCode,\n            });\n    }\n};\nconst throwDefaultError = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.withBaseException)(_models_BedrockRuntimeServiceException__WEBPACK_IMPORTED_MODULE_5__.BedrockRuntimeServiceException);\nconst de_AccessDeniedExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_6__.AccessDeniedException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_ConflictExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ConflictException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_InternalServerExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_6__.InternalServerException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_ModelErrorExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        originalStatusCode: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectInt32,\n        resourceName: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ModelErrorException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_ModelNotReadyExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ModelNotReadyException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_ModelStreamErrorExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        originalMessage: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        originalStatusCode: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectInt32,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ModelStreamErrorException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_ModelTimeoutExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ModelTimeoutException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_ResourceNotFoundExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ResourceNotFoundException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_ServiceQuotaExceededExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ServiceQuotaExceededException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_ServiceUnavailableExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ServiceUnavailableException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_ThrottlingExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ThrottlingException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_ValidationExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ValidationException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst se_InvokeModelWithBidirectionalStreamInput = (input, context) => {\n    const eventMarshallingVisitor = (event) => _models_models_0__WEBPACK_IMPORTED_MODULE_6__.InvokeModelWithBidirectionalStreamInput.visit(event, {\n        chunk: (value) => se_BidirectionalInputPayloadPart_event(value, context),\n        _: (value) => value,\n    });\n    return context.eventStreamMarshaller.serialize(input, eventMarshallingVisitor);\n};\nconst se_BidirectionalInputPayloadPart_event = (input, context) => {\n    const headers = {\n        \":event-type\": { type: \"string\", value: \"chunk\" },\n        \":message-type\": { type: \"string\", value: \"event\" },\n        \":content-type\": { type: \"string\", value: \"application/json\" },\n    };\n    let body = new Uint8Array();\n    body = se_BidirectionalInputPayloadPart(input, context);\n    body = context.utf8Decoder(JSON.stringify(body));\n    return { headers, body };\n};\nconst de_ConverseStreamOutput = (output, context) => {\n    return context.eventStreamMarshaller.deserialize(output, async (event) => {\n        if (event[\"messageStart\"] != null) {\n            return {\n                messageStart: await de_MessageStartEvent_event(event[\"messageStart\"], context),\n            };\n        }\n        if (event[\"contentBlockStart\"] != null) {\n            return {\n                contentBlockStart: await de_ContentBlockStartEvent_event(event[\"contentBlockStart\"], context),\n            };\n        }\n        if (event[\"contentBlockDelta\"] != null) {\n            return {\n                contentBlockDelta: await de_ContentBlockDeltaEvent_event(event[\"contentBlockDelta\"], context),\n            };\n        }\n        if (event[\"contentBlockStop\"] != null) {\n            return {\n                contentBlockStop: await de_ContentBlockStopEvent_event(event[\"contentBlockStop\"], context),\n            };\n        }\n        if (event[\"messageStop\"] != null) {\n            return {\n                messageStop: await de_MessageStopEvent_event(event[\"messageStop\"], context),\n            };\n        }\n        if (event[\"metadata\"] != null) {\n            return {\n                metadata: await de_ConverseStreamMetadataEvent_event(event[\"metadata\"], context),\n            };\n        }\n        if (event[\"internalServerException\"] != null) {\n            return {\n                internalServerException: await de_InternalServerException_event(event[\"internalServerException\"], context),\n            };\n        }\n        if (event[\"modelStreamErrorException\"] != null) {\n            return {\n                modelStreamErrorException: await de_ModelStreamErrorException_event(event[\"modelStreamErrorException\"], context),\n            };\n        }\n        if (event[\"validationException\"] != null) {\n            return {\n                validationException: await de_ValidationException_event(event[\"validationException\"], context),\n            };\n        }\n        if (event[\"throttlingException\"] != null) {\n            return {\n                throttlingException: await de_ThrottlingException_event(event[\"throttlingException\"], context),\n            };\n        }\n        if (event[\"serviceUnavailableException\"] != null) {\n            return {\n                serviceUnavailableException: await de_ServiceUnavailableException_event(event[\"serviceUnavailableException\"], context),\n            };\n        }\n        return { $unknown: output };\n    });\n};\nconst de_InvokeModelWithBidirectionalStreamOutput = (output, context) => {\n    return context.eventStreamMarshaller.deserialize(output, async (event) => {\n        if (event[\"chunk\"] != null) {\n            return {\n                chunk: await de_BidirectionalOutputPayloadPart_event(event[\"chunk\"], context),\n            };\n        }\n        if (event[\"internalServerException\"] != null) {\n            return {\n                internalServerException: await de_InternalServerException_event(event[\"internalServerException\"], context),\n            };\n        }\n        if (event[\"modelStreamErrorException\"] != null) {\n            return {\n                modelStreamErrorException: await de_ModelStreamErrorException_event(event[\"modelStreamErrorException\"], context),\n            };\n        }\n        if (event[\"validationException\"] != null) {\n            return {\n                validationException: await de_ValidationException_event(event[\"validationException\"], context),\n            };\n        }\n        if (event[\"throttlingException\"] != null) {\n            return {\n                throttlingException: await de_ThrottlingException_event(event[\"throttlingException\"], context),\n            };\n        }\n        if (event[\"modelTimeoutException\"] != null) {\n            return {\n                modelTimeoutException: await de_ModelTimeoutException_event(event[\"modelTimeoutException\"], context),\n            };\n        }\n        if (event[\"serviceUnavailableException\"] != null) {\n            return {\n                serviceUnavailableException: await de_ServiceUnavailableException_event(event[\"serviceUnavailableException\"], context),\n            };\n        }\n        return { $unknown: output };\n    });\n};\nconst de_ResponseStream = (output, context) => {\n    return context.eventStreamMarshaller.deserialize(output, async (event) => {\n        if (event[\"chunk\"] != null) {\n            return {\n                chunk: await de_PayloadPart_event(event[\"chunk\"], context),\n            };\n        }\n        if (event[\"internalServerException\"] != null) {\n            return {\n                internalServerException: await de_InternalServerException_event(event[\"internalServerException\"], context),\n            };\n        }\n        if (event[\"modelStreamErrorException\"] != null) {\n            return {\n                modelStreamErrorException: await de_ModelStreamErrorException_event(event[\"modelStreamErrorException\"], context),\n            };\n        }\n        if (event[\"validationException\"] != null) {\n            return {\n                validationException: await de_ValidationException_event(event[\"validationException\"], context),\n            };\n        }\n        if (event[\"throttlingException\"] != null) {\n            return {\n                throttlingException: await de_ThrottlingException_event(event[\"throttlingException\"], context),\n            };\n        }\n        if (event[\"modelTimeoutException\"] != null) {\n            return {\n                modelTimeoutException: await de_ModelTimeoutException_event(event[\"modelTimeoutException\"], context),\n            };\n        }\n        if (event[\"serviceUnavailableException\"] != null) {\n            return {\n                serviceUnavailableException: await de_ServiceUnavailableException_event(event[\"serviceUnavailableException\"], context),\n            };\n        }\n        return { $unknown: output };\n    });\n};\nconst de_BidirectionalOutputPayloadPart_event = async (output, context) => {\n    const contents = {};\n    const data = await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context);\n    Object.assign(contents, de_BidirectionalOutputPayloadPart(data, context));\n    return contents;\n};\nconst de_ContentBlockDeltaEvent_event = async (output, context) => {\n    const contents = {};\n    const data = await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context);\n    Object.assign(contents, de_ContentBlockDeltaEvent(data, context));\n    return contents;\n};\nconst de_ContentBlockStartEvent_event = async (output, context) => {\n    const contents = {};\n    const data = await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context);\n    Object.assign(contents, (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(data));\n    return contents;\n};\nconst de_ContentBlockStopEvent_event = async (output, context) => {\n    const contents = {};\n    const data = await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context);\n    Object.assign(contents, (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(data));\n    return contents;\n};\nconst de_ConverseStreamMetadataEvent_event = async (output, context) => {\n    const contents = {};\n    const data = await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context);\n    Object.assign(contents, de_ConverseStreamMetadataEvent(data, context));\n    return contents;\n};\nconst de_InternalServerException_event = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context),\n    };\n    return de_InternalServerExceptionRes(parsedOutput, context);\n};\nconst de_MessageStartEvent_event = async (output, context) => {\n    const contents = {};\n    const data = await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context);\n    Object.assign(contents, (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(data));\n    return contents;\n};\nconst de_MessageStopEvent_event = async (output, context) => {\n    const contents = {};\n    const data = await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context);\n    Object.assign(contents, de_MessageStopEvent(data, context));\n    return contents;\n};\nconst de_ModelStreamErrorException_event = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context),\n    };\n    return de_ModelStreamErrorExceptionRes(parsedOutput, context);\n};\nconst de_ModelTimeoutException_event = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context),\n    };\n    return de_ModelTimeoutExceptionRes(parsedOutput, context);\n};\nconst de_PayloadPart_event = async (output, context) => {\n    const contents = {};\n    const data = await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context);\n    Object.assign(contents, de_PayloadPart(data, context));\n    return contents;\n};\nconst de_ServiceUnavailableException_event = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context),\n    };\n    return de_ServiceUnavailableExceptionRes(parsedOutput, context);\n};\nconst de_ThrottlingException_event = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context),\n    };\n    return de_ThrottlingExceptionRes(parsedOutput, context);\n};\nconst de_ValidationException_event = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_3__.parseJsonBody)(output.body, context),\n    };\n    return de_ValidationExceptionRes(parsedOutput, context);\n};\nconst se_BidirectionalInputPayloadPart = (input, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        bytes: context.base64Encoder,\n    });\n};\nconst se_ContentBlock = (input, context) => {\n    return _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ContentBlock.visit(input, {\n        cachePoint: (value) => ({ cachePoint: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(value) }),\n        document: (value) => ({ document: se_DocumentBlock(value, context) }),\n        guardContent: (value) => ({ guardContent: se_GuardrailConverseContentBlock(value, context) }),\n        image: (value) => ({ image: se_ImageBlock(value, context) }),\n        reasoningContent: (value) => ({ reasoningContent: se_ReasoningContentBlock(value, context) }),\n        text: (value) => ({ text: value }),\n        toolResult: (value) => ({ toolResult: se_ToolResultBlock(value, context) }),\n        toolUse: (value) => ({ toolUse: se_ToolUseBlock(value, context) }),\n        video: (value) => ({ video: se_VideoBlock(value, context) }),\n        _: (name, value) => ({ [name]: value }),\n    });\n};\nconst se_ContentBlocks = (input, context) => {\n    return input\n        .filter((e) => e != null)\n        .map((entry) => {\n        return se_ContentBlock(entry, context);\n    });\n};\nconst se_DocumentBlock = (input, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        format: [],\n        name: [],\n        source: (_) => se_DocumentSource(_, context),\n    });\n};\nconst se_DocumentSource = (input, context) => {\n    return _models_models_0__WEBPACK_IMPORTED_MODULE_6__.DocumentSource.visit(input, {\n        bytes: (value) => ({ bytes: context.base64Encoder(value) }),\n        s3Location: (value) => ({ s3Location: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(value) }),\n        _: (name, value) => ({ [name]: value }),\n    });\n};\nconst se_GuardrailContentBlock = (input, context) => {\n    return _models_models_0__WEBPACK_IMPORTED_MODULE_6__.GuardrailContentBlock.visit(input, {\n        image: (value) => ({ image: se_GuardrailImageBlock(value, context) }),\n        text: (value) => ({ text: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(value) }),\n        _: (name, value) => ({ [name]: value }),\n    });\n};\nconst se_GuardrailContentBlockList = (input, context) => {\n    return input\n        .filter((e) => e != null)\n        .map((entry) => {\n        return se_GuardrailContentBlock(entry, context);\n    });\n};\nconst se_GuardrailConverseContentBlock = (input, context) => {\n    return _models_models_0__WEBPACK_IMPORTED_MODULE_6__.GuardrailConverseContentBlock.visit(input, {\n        image: (value) => ({ image: se_GuardrailConverseImageBlock(value, context) }),\n        text: (value) => ({ text: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(value) }),\n        _: (name, value) => ({ [name]: value }),\n    });\n};\nconst se_GuardrailConverseImageBlock = (input, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        format: [],\n        source: (_) => se_GuardrailConverseImageSource(_, context),\n    });\n};\nconst se_GuardrailConverseImageSource = (input, context) => {\n    return _models_models_0__WEBPACK_IMPORTED_MODULE_6__.GuardrailConverseImageSource.visit(input, {\n        bytes: (value) => ({ bytes: context.base64Encoder(value) }),\n        _: (name, value) => ({ [name]: value }),\n    });\n};\nconst se_GuardrailImageBlock = (input, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        format: [],\n        source: (_) => se_GuardrailImageSource(_, context),\n    });\n};\nconst se_GuardrailImageSource = (input, context) => {\n    return _models_models_0__WEBPACK_IMPORTED_MODULE_6__.GuardrailImageSource.visit(input, {\n        bytes: (value) => ({ bytes: context.base64Encoder(value) }),\n        _: (name, value) => ({ [name]: value }),\n    });\n};\nconst se_ImageBlock = (input, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        format: [],\n        source: (_) => se_ImageSource(_, context),\n    });\n};\nconst se_ImageSource = (input, context) => {\n    return _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ImageSource.visit(input, {\n        bytes: (value) => ({ bytes: context.base64Encoder(value) }),\n        s3Location: (value) => ({ s3Location: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(value) }),\n        _: (name, value) => ({ [name]: value }),\n    });\n};\nconst se_InferenceConfiguration = (input, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        maxTokens: [],\n        stopSequences: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        temperature: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.serializeFloat,\n        topP: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.serializeFloat,\n    });\n};\nconst se_Message = (input, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        content: (_) => se_ContentBlocks(_, context),\n        role: [],\n    });\n};\nconst se_Messages = (input, context) => {\n    return input\n        .filter((e) => e != null)\n        .map((entry) => {\n        return se_Message(entry, context);\n    });\n};\nconst se_ModelInputPayload = (input, context) => {\n    return input;\n};\nconst se_ReasoningContentBlock = (input, context) => {\n    return _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ReasoningContentBlock.visit(input, {\n        reasoningText: (value) => ({ reasoningText: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(value) }),\n        redactedContent: (value) => ({ redactedContent: context.base64Encoder(value) }),\n        _: (name, value) => ({ [name]: value }),\n    });\n};\nconst se_SystemContentBlock = (input, context) => {\n    return _models_models_0__WEBPACK_IMPORTED_MODULE_6__.SystemContentBlock.visit(input, {\n        cachePoint: (value) => ({ cachePoint: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(value) }),\n        guardContent: (value) => ({ guardContent: se_GuardrailConverseContentBlock(value, context) }),\n        text: (value) => ({ text: value }),\n        _: (name, value) => ({ [name]: value }),\n    });\n};\nconst se_SystemContentBlocks = (input, context) => {\n    return input\n        .filter((e) => e != null)\n        .map((entry) => {\n        return se_SystemContentBlock(entry, context);\n    });\n};\nconst se_Tool = (input, context) => {\n    return _models_models_0__WEBPACK_IMPORTED_MODULE_6__.Tool.visit(input, {\n        cachePoint: (value) => ({ cachePoint: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(value) }),\n        toolSpec: (value) => ({ toolSpec: se_ToolSpecification(value, context) }),\n        _: (name, value) => ({ [name]: value }),\n    });\n};\nconst se_ToolConfiguration = (input, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        toolChoice: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        tools: (_) => se_Tools(_, context),\n    });\n};\nconst se_ToolInputSchema = (input, context) => {\n    return _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ToolInputSchema.visit(input, {\n        json: (value) => ({ json: se_Document(value, context) }),\n        _: (name, value) => ({ [name]: value }),\n    });\n};\nconst se_ToolResultBlock = (input, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        content: (_) => se_ToolResultContentBlocks(_, context),\n        status: [],\n        toolUseId: [],\n    });\n};\nconst se_ToolResultContentBlock = (input, context) => {\n    return _models_models_0__WEBPACK_IMPORTED_MODULE_6__.ToolResultContentBlock.visit(input, {\n        document: (value) => ({ document: se_DocumentBlock(value, context) }),\n        image: (value) => ({ image: se_ImageBlock(value, context) }),\n        json: (value) => ({ json: se_Document(value, context) }),\n        text: (value) => ({ text: value }),\n        video: (value) => ({ video: se_VideoBlock(value, context) }),\n        _: (name, value) => ({ [name]: value }),\n    });\n};\nconst se_ToolResultContentBlocks = (input, context) => {\n    return input\n        .filter((e) => e != null)\n        .map((entry) => {\n        return se_ToolResultContentBlock(entry, context);\n    });\n};\nconst se_Tools = (input, context) => {\n    return input\n        .filter((e) => e != null)\n        .map((entry) => {\n        return se_Tool(entry, context);\n    });\n};\nconst se_ToolSpecification = (input, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        description: [],\n        inputSchema: (_) => se_ToolInputSchema(_, context),\n        name: [],\n    });\n};\nconst se_ToolUseBlock = (input, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        input: (_) => se_Document(_, context),\n        name: [],\n        toolUseId: [],\n    });\n};\nconst se_VideoBlock = (input, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        format: [],\n        source: (_) => se_VideoSource(_, context),\n    });\n};\nconst se_VideoSource = (input, context) => {\n    return _models_models_0__WEBPACK_IMPORTED_MODULE_6__.VideoSource.visit(input, {\n        bytes: (value) => ({ bytes: context.base64Encoder(value) }),\n        s3Location: (value) => ({ s3Location: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(value) }),\n        _: (name, value) => ({ [name]: value }),\n    });\n};\nconst se_Document = (input, context) => {\n    return input;\n};\nconst de_AsyncInvokeSummaries = (output, context) => {\n    const retVal = (output || [])\n        .filter((e) => e != null)\n        .map((entry) => {\n        return de_AsyncInvokeSummary(entry, context);\n    });\n    return retVal;\n};\nconst de_AsyncInvokeSummary = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        clientRequestToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        endTime: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.parseRfc3339DateTimeWithOffset)(_)),\n        failureMessage: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        invocationArn: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        lastModifiedTime: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.parseRfc3339DateTimeWithOffset)(_)),\n        modelArn: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        outputDataConfig: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)((0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.awsExpectUnion)(_)),\n        status: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        submitTime: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.parseRfc3339DateTimeWithOffset)(_)),\n    });\n};\nconst de_BidirectionalOutputPayloadPart = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        bytes: context.base64Decoder,\n    });\n};\nconst de_ContentBlock = (output, context) => {\n    if (output.cachePoint != null) {\n        return {\n            cachePoint: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(output.cachePoint),\n        };\n    }\n    if (output.document != null) {\n        return {\n            document: de_DocumentBlock(output.document, context),\n        };\n    }\n    if (output.guardContent != null) {\n        return {\n            guardContent: de_GuardrailConverseContentBlock((0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.awsExpectUnion)(output.guardContent), context),\n        };\n    }\n    if (output.image != null) {\n        return {\n            image: de_ImageBlock(output.image, context),\n        };\n    }\n    if (output.reasoningContent != null) {\n        return {\n            reasoningContent: de_ReasoningContentBlock((0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.awsExpectUnion)(output.reasoningContent), context),\n        };\n    }\n    if ((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output.text) !== undefined) {\n        return { text: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output.text) };\n    }\n    if (output.toolResult != null) {\n        return {\n            toolResult: de_ToolResultBlock(output.toolResult, context),\n        };\n    }\n    if (output.toolUse != null) {\n        return {\n            toolUse: de_ToolUseBlock(output.toolUse, context),\n        };\n    }\n    if (output.video != null) {\n        return {\n            video: de_VideoBlock(output.video, context),\n        };\n    }\n    return { $unknown: Object.entries(output)[0] };\n};\nconst de_ContentBlockDelta = (output, context) => {\n    if (output.reasoningContent != null) {\n        return {\n            reasoningContent: de_ReasoningContentBlockDelta((0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.awsExpectUnion)(output.reasoningContent), context),\n        };\n    }\n    if ((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output.text) !== undefined) {\n        return { text: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output.text) };\n    }\n    if (output.toolUse != null) {\n        return {\n            toolUse: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(output.toolUse),\n        };\n    }\n    return { $unknown: Object.entries(output)[0] };\n};\nconst de_ContentBlockDeltaEvent = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        contentBlockIndex: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectInt32,\n        delta: (_) => de_ContentBlockDelta((0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.awsExpectUnion)(_), context),\n    });\n};\nconst de_ContentBlocks = (output, context) => {\n    const retVal = (output || [])\n        .filter((e) => e != null)\n        .map((entry) => {\n        return de_ContentBlock((0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.awsExpectUnion)(entry), context);\n    });\n    return retVal;\n};\nconst de_ConverseOutput = (output, context) => {\n    if (output.message != null) {\n        return {\n            message: de_Message(output.message, context),\n        };\n    }\n    return { $unknown: Object.entries(output)[0] };\n};\nconst de_ConverseStreamMetadataEvent = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        metrics: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        performanceConfig: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        trace: (_) => de_ConverseStreamTrace(_, context),\n        usage: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n    });\n};\nconst de_ConverseStreamTrace = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        guardrail: (_) => de_GuardrailTraceAssessment(_, context),\n        promptRouter: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n    });\n};\nconst de_ConverseTrace = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        guardrail: (_) => de_GuardrailTraceAssessment(_, context),\n        promptRouter: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n    });\n};\nconst de_DocumentBlock = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        format: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        name: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        source: (_) => de_DocumentSource((0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.awsExpectUnion)(_), context),\n    });\n};\nconst de_DocumentSource = (output, context) => {\n    if (output.bytes != null) {\n        return {\n            bytes: context.base64Decoder(output.bytes),\n        };\n    }\n    if (output.s3Location != null) {\n        return {\n            s3Location: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(output.s3Location),\n        };\n    }\n    return { $unknown: Object.entries(output)[0] };\n};\nconst de_GuardrailAssessment = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        contentPolicy: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        contextualGroundingPolicy: (_) => de_GuardrailContextualGroundingPolicyAssessment(_, context),\n        invocationMetrics: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        sensitiveInformationPolicy: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        topicPolicy: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        wordPolicy: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n    });\n};\nconst de_GuardrailAssessmentList = (output, context) => {\n    const retVal = (output || [])\n        .filter((e) => e != null)\n        .map((entry) => {\n        return de_GuardrailAssessment(entry, context);\n    });\n    return retVal;\n};\nconst de_GuardrailAssessmentListMap = (output, context) => {\n    return Object.entries(output).reduce((acc, [key, value]) => {\n        if (value === null) {\n            return acc;\n        }\n        acc[key] = de_GuardrailAssessmentList(value, context);\n        return acc;\n    }, {});\n};\nconst de_GuardrailAssessmentMap = (output, context) => {\n    return Object.entries(output).reduce((acc, [key, value]) => {\n        if (value === null) {\n            return acc;\n        }\n        acc[key] = de_GuardrailAssessment(value, context);\n        return acc;\n    }, {});\n};\nconst de_GuardrailContextualGroundingFilter = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        action: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        detected: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectBoolean,\n        score: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.limitedParseDouble,\n        threshold: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.limitedParseDouble,\n        type: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n};\nconst de_GuardrailContextualGroundingFilters = (output, context) => {\n    const retVal = (output || [])\n        .filter((e) => e != null)\n        .map((entry) => {\n        return de_GuardrailContextualGroundingFilter(entry, context);\n    });\n    return retVal;\n};\nconst de_GuardrailContextualGroundingPolicyAssessment = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        filters: (_) => de_GuardrailContextualGroundingFilters(_, context),\n    });\n};\nconst de_GuardrailConverseContentBlock = (output, context) => {\n    if (output.image != null) {\n        return {\n            image: de_GuardrailConverseImageBlock(output.image, context),\n        };\n    }\n    if (output.text != null) {\n        return {\n            text: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(output.text),\n        };\n    }\n    return { $unknown: Object.entries(output)[0] };\n};\nconst de_GuardrailConverseImageBlock = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        format: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        source: (_) => de_GuardrailConverseImageSource((0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.awsExpectUnion)(_), context),\n    });\n};\nconst de_GuardrailConverseImageSource = (output, context) => {\n    if (output.bytes != null) {\n        return {\n            bytes: context.base64Decoder(output.bytes),\n        };\n    }\n    return { $unknown: Object.entries(output)[0] };\n};\nconst de_GuardrailTraceAssessment = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        actionReason: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        inputAssessment: (_) => de_GuardrailAssessmentMap(_, context),\n        modelOutput: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        outputAssessments: (_) => de_GuardrailAssessmentListMap(_, context),\n    });\n};\nconst de_ImageBlock = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        format: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        source: (_) => de_ImageSource((0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.awsExpectUnion)(_), context),\n    });\n};\nconst de_ImageSource = (output, context) => {\n    if (output.bytes != null) {\n        return {\n            bytes: context.base64Decoder(output.bytes),\n        };\n    }\n    if (output.s3Location != null) {\n        return {\n            s3Location: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(output.s3Location),\n        };\n    }\n    return { $unknown: Object.entries(output)[0] };\n};\nconst de_Message = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        content: (_) => de_ContentBlocks(_, context),\n        role: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n};\nconst de_MessageStopEvent = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        additionalModelResponseFields: (_) => de_Document(_, context),\n        stopReason: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n};\nconst de_PayloadPart = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        bytes: context.base64Decoder,\n    });\n};\nconst de_ReasoningContentBlock = (output, context) => {\n    if (output.reasoningText != null) {\n        return {\n            reasoningText: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(output.reasoningText),\n        };\n    }\n    if (output.redactedContent != null) {\n        return {\n            redactedContent: context.base64Decoder(output.redactedContent),\n        };\n    }\n    return { $unknown: Object.entries(output)[0] };\n};\nconst de_ReasoningContentBlockDelta = (output, context) => {\n    if (output.redactedContent != null) {\n        return {\n            redactedContent: context.base64Decoder(output.redactedContent),\n        };\n    }\n    if ((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output.signature) !== undefined) {\n        return { signature: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output.signature) };\n    }\n    if ((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output.text) !== undefined) {\n        return { text: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output.text) };\n    }\n    return { $unknown: Object.entries(output)[0] };\n};\nconst de_ToolResultBlock = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        content: (_) => de_ToolResultContentBlocks(_, context),\n        status: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        toolUseId: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n};\nconst de_ToolResultContentBlock = (output, context) => {\n    if (output.document != null) {\n        return {\n            document: de_DocumentBlock(output.document, context),\n        };\n    }\n    if (output.image != null) {\n        return {\n            image: de_ImageBlock(output.image, context),\n        };\n    }\n    if (output.json != null) {\n        return {\n            json: de_Document(output.json, context),\n        };\n    }\n    if ((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output.text) !== undefined) {\n        return { text: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output.text) };\n    }\n    if (output.video != null) {\n        return {\n            video: de_VideoBlock(output.video, context),\n        };\n    }\n    return { $unknown: Object.entries(output)[0] };\n};\nconst de_ToolResultContentBlocks = (output, context) => {\n    const retVal = (output || [])\n        .filter((e) => e != null)\n        .map((entry) => {\n        return de_ToolResultContentBlock((0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.awsExpectUnion)(entry), context);\n    });\n    return retVal;\n};\nconst de_ToolUseBlock = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        input: (_) => de_Document(_, context),\n        name: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        toolUseId: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n};\nconst de_VideoBlock = (output, context) => {\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(output, {\n        format: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        source: (_) => de_VideoSource((0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.awsExpectUnion)(_), context),\n    });\n};\nconst de_VideoSource = (output, context) => {\n    if (output.bytes != null) {\n        return {\n            bytes: context.base64Decoder(output.bytes),\n        };\n    }\n    if (output.s3Location != null) {\n        return {\n            s3Location: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(output.s3Location),\n        };\n    }\n    return { $unknown: Object.entries(output)[0] };\n};\nconst de_Document = (output, context) => {\n    return output;\n};\nconst deserializeMetadata = (output) => ({\n    httpStatusCode: output.statusCode,\n    requestId: output.headers[\"x-amzn-requestid\"] ?? output.headers[\"x-amzn-request-id\"] ?? output.headers[\"x-amz-request-id\"],\n    extendedRequestId: output.headers[\"x-amz-id-2\"],\n    cfId: output.headers[\"x-amz-cf-id\"],\n});\nconst collectBodyString = (streamBody, context) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.collectBody)(streamBody, context).then((body) => context.utf8Encoder(body));\nconst _a = \"accept\";\nconst _cT = \"contentType\";\nconst _ct = \"content-type\";\nconst _gI = \"guardrailIdentifier\";\nconst _gV = \"guardrailVersion\";\nconst _mR = \"maxResults\";\nconst _nT = \"nextToken\";\nconst _pCL = \"performanceConfigLatency\";\nconst _sB = \"sortBy\";\nconst _sE = \"statusEquals\";\nconst _sO = \"sortOrder\";\nconst _sTA = \"submitTimeAfter\";\nconst _sTB = \"submitTimeBefore\";\nconst _t = \"trace\";\nconst _xaba = \"x-amzn-bedrock-accept\";\nconst _xabct = \"x-amzn-bedrock-content-type\";\nconst _xabg = \"x-amzn-bedrock-guardrailidentifier\";\nconst _xabg_ = \"x-amzn-bedrock-guardrailversion\";\nconst _xabpl = \"x-amzn-bedrock-performanceconfig-latency\";\nconst _xabt = \"x-amzn-bedrock-trace\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/protocols/Aws_restJson1.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/runtimeConfig.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/runtimeConfig.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRuntimeConfig: () => (/* binding */ getRuntimeConfig)\n/* harmony export */ });\n/* harmony import */ var _package_json__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../package.json */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/package.json\");\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/emitWarningIfUnsupportedVersion.js\");\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/NODE_AUTH_SCHEME_PREFERENCE_OPTIONS.js\");\n/* harmony import */ var _aws_sdk_credential_provider_node__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/credential-provider-node */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-node@3.830.0/node_modules/@aws-sdk/credential-provider-node/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_eventstream_handler_node__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/eventstream-handler-node */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_util_user_agent_node__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/util-user-agent-node */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/index.js\");\n/* harmony import */ var _smithy_config_resolver__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/config-resolver */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_eventstream_serde_node__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @smithy/eventstream-serde-node */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/index.js\");\n/* harmony import */ var _smithy_hash_node__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @smithy/hash-node */ \"(rsc)/./node_modules/.pnpm/@smithy+hash-node@4.0.4/node_modules/@smithy/hash-node/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @smithy/middleware-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/index.js\");\n/* harmony import */ var _smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @smithy/node-config-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+node-config-provider@4.1.3/node_modules/@smithy/node-config-provider/dist-es/index.js\");\n/* harmony import */ var _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @smithy/node-http-handler */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/index.js\");\n/* harmony import */ var _smithy_util_body_length_node__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @smithy/util-body-length-node */ \"(rsc)/./node_modules/.pnpm/@smithy+util-body-length-node@4.0.0/node_modules/@smithy/util-body-length-node/dist-es/index.js\");\n/* harmony import */ var _smithy_util_retry__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @smithy/util-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js\");\n/* harmony import */ var _runtimeConfig_shared__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./runtimeConfig.shared */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/runtimeConfig.shared.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _smithy_util_defaults_mode_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @smithy/util-defaults-mode-node */ \"(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst getRuntimeConfig = (config) => {\n    (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_11__.emitWarningIfUnsupportedVersion)(process.version);\n    const defaultsMode = (0,_smithy_util_defaults_mode_node__WEBPACK_IMPORTED_MODULE_12__.resolveDefaultsModeConfig)(config);\n    const defaultConfigProvider = () => defaultsMode().then(_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_11__.loadConfigsForDefaultMode);\n    const clientSharedValues = (0,_runtimeConfig_shared__WEBPACK_IMPORTED_MODULE_13__.getRuntimeConfig)(config);\n    (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_14__.emitWarningIfUnsupportedVersion)(process.version);\n    const loaderConfig = {\n        profile: config?.profile,\n        logger: clientSharedValues.logger,\n    };\n    return {\n        ...clientSharedValues,\n        ...config,\n        runtime: \"node\",\n        defaultsMode,\n        authSchemePreference: config?.authSchemePreference ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_7__.loadConfig)(_aws_sdk_core__WEBPACK_IMPORTED_MODULE_15__.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS, loaderConfig),\n        bodyLengthChecker: config?.bodyLengthChecker ?? _smithy_util_body_length_node__WEBPACK_IMPORTED_MODULE_9__.calculateBodyLength,\n        credentialDefaultProvider: config?.credentialDefaultProvider ?? _aws_sdk_credential_provider_node__WEBPACK_IMPORTED_MODULE_0__.defaultProvider,\n        defaultUserAgentProvider: config?.defaultUserAgentProvider ??\n            (0,_aws_sdk_util_user_agent_node__WEBPACK_IMPORTED_MODULE_2__.createDefaultUserAgentProvider)({ serviceId: clientSharedValues.serviceId, clientVersion: _package_json__WEBPACK_IMPORTED_MODULE_16__.version }),\n        eventStreamPayloadHandlerProvider: config?.eventStreamPayloadHandlerProvider ?? _aws_sdk_eventstream_handler_node__WEBPACK_IMPORTED_MODULE_1__.eventStreamPayloadHandlerProvider,\n        eventStreamSerdeProvider: config?.eventStreamSerdeProvider ?? _smithy_eventstream_serde_node__WEBPACK_IMPORTED_MODULE_4__.eventStreamSerdeProvider,\n        maxAttempts: config?.maxAttempts ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_7__.loadConfig)(_smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_6__.NODE_MAX_ATTEMPT_CONFIG_OPTIONS, config),\n        region: config?.region ??\n            (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_7__.loadConfig)(_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_3__.NODE_REGION_CONFIG_OPTIONS, { ..._smithy_config_resolver__WEBPACK_IMPORTED_MODULE_3__.NODE_REGION_CONFIG_FILE_OPTIONS, ...loaderConfig }),\n        requestHandler: _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_8__.NodeHttp2Handler.create(config?.requestHandler ?? (async () => ({ ...(await defaultConfigProvider()), disableConcurrentStreams: true }))),\n        retryMode: config?.retryMode ??\n            (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_7__.loadConfig)({\n                ..._smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_6__.NODE_RETRY_MODE_CONFIG_OPTIONS,\n                default: async () => (await defaultConfigProvider()).retryMode || _smithy_util_retry__WEBPACK_IMPORTED_MODULE_10__.DEFAULT_RETRY_MODE,\n            }, config),\n        sha256: config?.sha256 ?? _smithy_hash_node__WEBPACK_IMPORTED_MODULE_5__.Hash.bind(null, \"sha256\"),\n        streamCollector: config?.streamCollector ?? _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_8__.streamCollector,\n        useDualstackEndpoint: config?.useDualstackEndpoint ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_7__.loadConfig)(_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_3__.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS, loaderConfig),\n        useFipsEndpoint: config?.useFipsEndpoint ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_7__.loadConfig)(_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_3__.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS, loaderConfig),\n        userAgentAppId: config?.userAgentAppId ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_7__.loadConfig)(_aws_sdk_util_user_agent_node__WEBPACK_IMPORTED_MODULE_2__.NODE_APP_ID_CONFIG_OPTIONS, loaderConfig),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/runtimeConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/runtimeConfig.shared.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/runtimeConfig.shared.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRuntimeConfig: () => (/* binding */ getRuntimeConfig)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/AwsSdkSigV4Signer.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _smithy_url_parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/url-parser */ \"(rsc)/./node_modules/.pnpm/@smithy+url-parser@4.0.4/node_modules/@smithy/url-parser/dist-es/index.js\");\n/* harmony import */ var _smithy_util_base64__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/util-base64 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n/* harmony import */ var _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./auth/httpAuthSchemeProvider */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/auth/httpAuthSchemeProvider.js\");\n/* harmony import */ var _endpoint_endpointResolver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./endpoint/endpointResolver */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/endpoint/endpointResolver.js\");\n\n\n\n\n\n\n\nconst getRuntimeConfig = (config) => {\n    return {\n        apiVersion: \"2023-09-30\",\n        base64Decoder: config?.base64Decoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_2__.fromBase64,\n        base64Encoder: config?.base64Encoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_2__.toBase64,\n        disableHostPrefix: config?.disableHostPrefix ?? false,\n        endpointProvider: config?.endpointProvider ?? _endpoint_endpointResolver__WEBPACK_IMPORTED_MODULE_4__.defaultEndpointResolver,\n        extensions: config?.extensions ?? [],\n        httpAuthSchemeProvider: config?.httpAuthSchemeProvider ?? _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_5__.defaultBedrockRuntimeHttpAuthSchemeProvider,\n        httpAuthSchemes: config?.httpAuthSchemes ?? [\n            {\n                schemeId: \"aws.auth#sigv4\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"aws.auth#sigv4\"),\n                signer: new _aws_sdk_core__WEBPACK_IMPORTED_MODULE_6__.AwsSdkSigV4Signer(),\n            },\n        ],\n        logger: config?.logger ?? new _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.NoOpLogger(),\n        serviceId: config?.serviceId ?? \"Bedrock Runtime\",\n        urlParser: config?.urlParser ?? _smithy_url_parser__WEBPACK_IMPORTED_MODULE_1__.parseUrl,\n        utf8Decoder: config?.utf8Decoder ?? _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_3__.fromUtf8,\n        utf8Encoder: config?.utf8Encoder ?? _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_3__.toUtf8,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/runtimeConfig.shared.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/runtimeExtensions.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/runtimeExtensions.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveRuntimeExtensions: () => (/* binding */ resolveRuntimeExtensions)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_region_config_resolver__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/region-config-resolver */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _auth_httpAuthExtensionConfiguration__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./auth/httpAuthExtensionConfiguration */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/auth/httpAuthExtensionConfiguration.js\");\n\n\n\n\nconst resolveRuntimeExtensions = (runtimeConfig, extensions) => {\n    const extensionConfiguration = Object.assign((0,_aws_sdk_region_config_resolver__WEBPACK_IMPORTED_MODULE_0__.getAwsRegionExtensionConfiguration)(runtimeConfig), (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.getDefaultExtensionConfiguration)(runtimeConfig), (0,_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__.getHttpHandlerExtensionConfiguration)(runtimeConfig), (0,_auth_httpAuthExtensionConfiguration__WEBPACK_IMPORTED_MODULE_3__.getHttpAuthExtensionConfiguration)(runtimeConfig));\n    extensions.forEach((extension) => extension.configure(extensionConfiguration));\n    return Object.assign(runtimeConfig, (0,_aws_sdk_region_config_resolver__WEBPACK_IMPORTED_MODULE_0__.resolveAwsRegionExtensionConfiguration)(extensionConfiguration), (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.resolveDefaultRuntimeConfig)(extensionConfiguration), (0,_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__.resolveHttpHandlerRuntimeConfig)(extensionConfiguration), (0,_auth_httpAuthExtensionConfiguration__WEBPACK_IMPORTED_MODULE_3__.resolveHttpAuthRuntimeConfig)(extensionConfiguration));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/runtimeExtensions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/package.json":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/package.json ***!
  \******************************************************************************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"@aws-sdk/client-bedrock-runtime","description":"AWS SDK for JavaScript Bedrock Runtime Client for Node.js, Browser and React Native","version":"3.830.0","scripts":{"build":"concurrently \'yarn:build:cjs\' \'yarn:build:es\' \'yarn:build:types\'","build:cjs":"node ../../scripts/compilation/inline client-bedrock-runtime","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4","clean":"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo bedrock-runtime"},"main":"./dist-cjs/index.js","types":"./dist-types/index.d.ts","module":"./dist-es/index.js","sideEffects":false,"dependencies":{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.826.0","@aws-sdk/credential-provider-node":"3.830.0","@aws-sdk/eventstream-handler-node":"3.821.0","@aws-sdk/middleware-eventstream":"3.821.0","@aws-sdk/middleware-host-header":"3.821.0","@aws-sdk/middleware-logger":"3.821.0","@aws-sdk/middleware-recursion-detection":"3.821.0","@aws-sdk/middleware-user-agent":"3.828.0","@aws-sdk/region-config-resolver":"3.821.0","@aws-sdk/types":"3.821.0","@aws-sdk/util-endpoints":"3.828.0","@aws-sdk/util-user-agent-browser":"3.821.0","@aws-sdk/util-user-agent-node":"3.828.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.5.3","@smithy/eventstream-serde-browser":"^4.0.4","@smithy/eventstream-serde-config-resolver":"^4.1.2","@smithy/eventstream-serde-node":"^4.0.4","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.11","@smithy/middleware-retry":"^4.1.12","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.3","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.19","@smithy/util-defaults-mode-node":"^4.0.19","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.5","@smithy/util-stream":"^4.2.2","@smithy/util-utf8":"^4.0.0","@types/uuid":"^9.0.1","tslib":"^2.6.2","uuid":"^9.0.1"},"devDependencies":{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69","concurrently":"7.0.0","downlevel-dts":"0.10.1","rimraf":"3.0.2","typescript":"~5.8.3"},"engines":{"node":">=18.0.0"},"typesVersions":{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},"files":["dist-*/**"],"author":{"name":"AWS SDK for JavaScript Team","url":"https://aws.amazon.com/javascript/"},"license":"Apache-2.0","browser":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},"homepage":"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-bedrock-runtime","repository":{"type":"git","url":"https://github.com/aws/aws-sdk-js-v3.git","directory":"clients/client-bedrock-runtime"}}');

/***/ })

};
;