"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+eventstream-serde-universal@4.0.4";
exports.ids = ["vendor-chunks/@smithy+eventstream-serde-universal@4.0.4"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/EventStreamMarshaller.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/EventStreamMarshaller.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventStreamMarshaller: () => (/* binding */ EventStreamMarshaller)\n/* harmony export */ });\n/* harmony import */ var _smithy_eventstream_codec__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/eventstream-codec */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/index.js\");\n/* harmony import */ var _getChunkedStream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getChunkedStream */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/getChunkedStream.js\");\n/* harmony import */ var _getUnmarshalledStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getUnmarshalledStream */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/getUnmarshalledStream.js\");\n\n\n\nclass EventStreamMarshaller {\n    constructor({ utf8Encoder, utf8Decoder }) {\n        this.eventStreamCodec = new _smithy_eventstream_codec__WEBPACK_IMPORTED_MODULE_0__.EventStreamCodec(utf8Encoder, utf8Decoder);\n        this.utfEncoder = utf8Encoder;\n    }\n    deserialize(body, deserializer) {\n        const inputStream = (0,_getChunkedStream__WEBPACK_IMPORTED_MODULE_1__.getChunkedStream)(body);\n        return new _smithy_eventstream_codec__WEBPACK_IMPORTED_MODULE_0__.SmithyMessageDecoderStream({\n            messageStream: new _smithy_eventstream_codec__WEBPACK_IMPORTED_MODULE_0__.MessageDecoderStream({ inputStream, decoder: this.eventStreamCodec }),\n            deserializer: (0,_getUnmarshalledStream__WEBPACK_IMPORTED_MODULE_2__.getMessageUnmarshaller)(deserializer, this.utfEncoder),\n        });\n    }\n    serialize(inputStream, serializer) {\n        return new _smithy_eventstream_codec__WEBPACK_IMPORTED_MODULE_0__.MessageEncoderStream({\n            messageStream: new _smithy_eventstream_codec__WEBPACK_IMPORTED_MODULE_0__.SmithyMessageEncoderStream({ inputStream, serializer }),\n            encoder: this.eventStreamCodec,\n            includeEndFrame: true,\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/EventStreamMarshaller.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/getChunkedStream.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/getChunkedStream.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getChunkedStream: () => (/* binding */ getChunkedStream)\n/* harmony export */ });\nfunction getChunkedStream(source) {\n    let currentMessageTotalLength = 0;\n    let currentMessagePendingLength = 0;\n    let currentMessage = null;\n    let messageLengthBuffer = null;\n    const allocateMessage = (size) => {\n        if (typeof size !== \"number\") {\n            throw new Error(\"Attempted to allocate an event message where size was not a number: \" + size);\n        }\n        currentMessageTotalLength = size;\n        currentMessagePendingLength = 4;\n        currentMessage = new Uint8Array(size);\n        const currentMessageView = new DataView(currentMessage.buffer);\n        currentMessageView.setUint32(0, size, false);\n    };\n    const iterator = async function* () {\n        const sourceIterator = source[Symbol.asyncIterator]();\n        while (true) {\n            const { value, done } = await sourceIterator.next();\n            if (done) {\n                if (!currentMessageTotalLength) {\n                    return;\n                }\n                else if (currentMessageTotalLength === currentMessagePendingLength) {\n                    yield currentMessage;\n                }\n                else {\n                    throw new Error(\"Truncated event message received.\");\n                }\n                return;\n            }\n            const chunkLength = value.length;\n            let currentOffset = 0;\n            while (currentOffset < chunkLength) {\n                if (!currentMessage) {\n                    const bytesRemaining = chunkLength - currentOffset;\n                    if (!messageLengthBuffer) {\n                        messageLengthBuffer = new Uint8Array(4);\n                    }\n                    const numBytesForTotal = Math.min(4 - currentMessagePendingLength, bytesRemaining);\n                    messageLengthBuffer.set(value.slice(currentOffset, currentOffset + numBytesForTotal), currentMessagePendingLength);\n                    currentMessagePendingLength += numBytesForTotal;\n                    currentOffset += numBytesForTotal;\n                    if (currentMessagePendingLength < 4) {\n                        break;\n                    }\n                    allocateMessage(new DataView(messageLengthBuffer.buffer).getUint32(0, false));\n                    messageLengthBuffer = null;\n                }\n                const numBytesToWrite = Math.min(currentMessageTotalLength - currentMessagePendingLength, chunkLength - currentOffset);\n                currentMessage.set(value.slice(currentOffset, currentOffset + numBytesToWrite), currentMessagePendingLength);\n                currentMessagePendingLength += numBytesToWrite;\n                currentOffset += numBytesToWrite;\n                if (currentMessageTotalLength && currentMessageTotalLength === currentMessagePendingLength) {\n                    yield currentMessage;\n                    currentMessage = null;\n                    currentMessageTotalLength = 0;\n                    currentMessagePendingLength = 0;\n                }\n            }\n        }\n    };\n    return {\n        [Symbol.asyncIterator]: iterator,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/getChunkedStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/getUnmarshalledStream.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/getUnmarshalledStream.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getMessageUnmarshaller: () => (/* binding */ getMessageUnmarshaller),\n/* harmony export */   getUnmarshalledStream: () => (/* binding */ getUnmarshalledStream)\n/* harmony export */ });\nfunction getUnmarshalledStream(source, options) {\n    const messageUnmarshaller = getMessageUnmarshaller(options.deserializer, options.toUtf8);\n    return {\n        [Symbol.asyncIterator]: async function* () {\n            for await (const chunk of source) {\n                const message = options.eventStreamCodec.decode(chunk);\n                const type = await messageUnmarshaller(message);\n                if (type === undefined)\n                    continue;\n                yield type;\n            }\n        },\n    };\n}\nfunction getMessageUnmarshaller(deserializer, toUtf8) {\n    return async function (message) {\n        const { value: messageType } = message.headers[\":message-type\"];\n        if (messageType === \"error\") {\n            const unmodeledError = new Error(message.headers[\":error-message\"].value || \"UnknownError\");\n            unmodeledError.name = message.headers[\":error-code\"].value;\n            throw unmodeledError;\n        }\n        else if (messageType === \"exception\") {\n            const code = message.headers[\":exception-type\"].value;\n            const exception = { [code]: message };\n            const deserializedException = await deserializer(exception);\n            if (deserializedException.$unknown) {\n                const error = new Error(toUtf8(message.body));\n                error.name = code;\n                throw error;\n            }\n            throw deserializedException[code];\n        }\n        else if (messageType === \"event\") {\n            const event = {\n                [message.headers[\":event-type\"].value]: message,\n            };\n            const deserialized = await deserializer(event);\n            if (deserialized.$unknown)\n                return;\n            return deserialized;\n        }\n        else {\n            throw Error(`Unrecognizable event type: ${message.headers[\":event-type\"].value}`);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/getUnmarshalledStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/index.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/index.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventStreamMarshaller: () => (/* reexport safe */ _EventStreamMarshaller__WEBPACK_IMPORTED_MODULE_0__.EventStreamMarshaller),\n/* harmony export */   eventStreamSerdeProvider: () => (/* reexport safe */ _provider__WEBPACK_IMPORTED_MODULE_1__.eventStreamSerdeProvider)\n/* harmony export */ });\n/* harmony import */ var _EventStreamMarshaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EventStreamMarshaller */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/EventStreamMarshaller.js\");\n/* harmony import */ var _provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./provider */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/provider.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1zZXJkZS11bml2ZXJzYWxANC4wLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvZXZlbnRzdHJlYW0tc2VyZGUtdW5pdmVyc2FsL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF3QztBQUNiIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1zZXJkZS11bml2ZXJzYWxANC4wLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvZXZlbnRzdHJlYW0tc2VyZGUtdW5pdmVyc2FsL2Rpc3QtZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vRXZlbnRTdHJlYW1NYXJzaGFsbGVyXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9wcm92aWRlclwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/provider.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/provider.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventStreamSerdeProvider: () => (/* binding */ eventStreamSerdeProvider)\n/* harmony export */ });\n/* harmony import */ var _EventStreamMarshaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EventStreamMarshaller */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/EventStreamMarshaller.js\");\n\nconst eventStreamSerdeProvider = (options) => new _EventStreamMarshaller__WEBPACK_IMPORTED_MODULE_0__.EventStreamMarshaller(options);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1zZXJkZS11bml2ZXJzYWxANC4wLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvZXZlbnRzdHJlYW0tc2VyZGUtdW5pdmVyc2FsL2Rpc3QtZXMvcHJvdmlkZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0U7QUFDekQsa0RBQWtELHlFQUFxQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrZXZlbnRzdHJlYW0tc2VyZGUtdW5pdmVyc2FsQDQuMC40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2V2ZW50c3RyZWFtLXNlcmRlLXVuaXZlcnNhbC9kaXN0LWVzL3Byb3ZpZGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEV2ZW50U3RyZWFtTWFyc2hhbGxlciB9IGZyb20gXCIuL0V2ZW50U3RyZWFtTWFyc2hhbGxlclwiO1xuZXhwb3J0IGNvbnN0IGV2ZW50U3RyZWFtU2VyZGVQcm92aWRlciA9IChvcHRpb25zKSA9PiBuZXcgRXZlbnRTdHJlYW1NYXJzaGFsbGVyKG9wdGlvbnMpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/provider.js\n");

/***/ })

};
;