"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@0no-co+graphql.web@1.0.8_graphql@16.9.0";
exports.ids = ["vendor-chunks/@0no-co+graphql.web@1.0.8_graphql@16.9.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@0no-co+graphql.web@1.0.8_graphql@16.9.0/node_modules/@0no-co/graphql.web/dist/graphql.web.mjs":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@0no-co+graphql.web@1.0.8_graphql@16.9.0/node_modules/@0no-co/graphql.web/dist/graphql.web.mjs ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BREAK: () => (/* binding */ c),\n/* harmony export */   GraphQLError: () => (/* binding */ GraphQLError),\n/* harmony export */   Kind: () => (/* binding */ e),\n/* harmony export */   OperationTypeNode: () => (/* binding */ r),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseType: () => (/* binding */ parseType),\n/* harmony export */   parseValue: () => (/* binding */ parseValue),\n/* harmony export */   print: () => (/* binding */ print),\n/* harmony export */   printBlockString: () => (/* binding */ printBlockString),\n/* harmony export */   printString: () => (/* binding */ printString),\n/* harmony export */   valueFromASTUntyped: () => (/* binding */ valueFromASTUntyped),\n/* harmony export */   valueFromTypeNode: () => (/* binding */ valueFromTypeNode),\n/* harmony export */   visit: () => (/* binding */ visit)\n/* harmony export */ });\nvar e = {\n  NAME: \"Name\",\n  DOCUMENT: \"Document\",\n  OPERATION_DEFINITION: \"OperationDefinition\",\n  VARIABLE_DEFINITION: \"VariableDefinition\",\n  SELECTION_SET: \"SelectionSet\",\n  FIELD: \"Field\",\n  ARGUMENT: \"Argument\",\n  FRAGMENT_SPREAD: \"FragmentSpread\",\n  INLINE_FRAGMENT: \"InlineFragment\",\n  FRAGMENT_DEFINITION: \"FragmentDefinition\",\n  VARIABLE: \"Variable\",\n  INT: \"IntValue\",\n  FLOAT: \"FloatValue\",\n  STRING: \"StringValue\",\n  BOOLEAN: \"BooleanValue\",\n  NULL: \"NullValue\",\n  ENUM: \"EnumValue\",\n  LIST: \"ListValue\",\n  OBJECT: \"ObjectValue\",\n  OBJECT_FIELD: \"ObjectField\",\n  DIRECTIVE: \"Directive\",\n  NAMED_TYPE: \"NamedType\",\n  LIST_TYPE: \"ListType\",\n  NON_NULL_TYPE: \"NonNullType\"\n};\n\nvar r = {\n  QUERY: \"query\",\n  MUTATION: \"mutation\",\n  SUBSCRIPTION: \"subscription\"\n};\n\nclass GraphQLError extends Error {\n  constructor(e, r, i, n, a, t, l) {\n    super(e);\n    this.name = \"GraphQLError\";\n    this.message = e;\n    if (a) {\n      this.path = a;\n    }\n    if (r) {\n      this.nodes = Array.isArray(r) ? r : [ r ];\n    }\n    if (i) {\n      this.source = i;\n    }\n    if (n) {\n      this.positions = n;\n    }\n    if (t) {\n      this.originalError = t;\n    }\n    var o = l;\n    if (!o && t) {\n      var u = t.extensions;\n      if (u && \"object\" == typeof u) {\n        o = u;\n      }\n    }\n    this.extensions = o || {};\n  }\n  toJSON() {\n    return {\n      ...this,\n      message: this.message\n    };\n  }\n  toString() {\n    return this.message;\n  }\n  get [Symbol.toStringTag]() {\n    return \"GraphQLError\";\n  }\n}\n\nvar i;\n\nvar n;\n\nfunction error(e) {\n  return new GraphQLError(`Syntax Error: Unexpected token at ${n} in ${e}`);\n}\n\nfunction advance(e) {\n  e.lastIndex = n;\n  if (e.test(i)) {\n    return i.slice(n, n = e.lastIndex);\n  }\n}\n\nvar a = / +(?=[^\\s])/y;\n\nfunction blockString(e) {\n  var r = e.split(\"\\n\");\n  var i = \"\";\n  var n = 0;\n  var t = 0;\n  var l = r.length - 1;\n  for (var o = 0; o < r.length; o++) {\n    a.lastIndex = 0;\n    if (a.test(r[o])) {\n      if (o && (!n || a.lastIndex < n)) {\n        n = a.lastIndex;\n      }\n      t = t || o;\n      l = o;\n    }\n  }\n  for (var u = t; u <= l; u++) {\n    if (u !== t) {\n      i += \"\\n\";\n    }\n    i += r[u].slice(n).replace(/\\\\\"\"\"/g, '\"\"\"');\n  }\n  return i;\n}\n\nfunction ignored() {\n  for (var e = 0 | i.charCodeAt(n++); 9 === e || 10 === e || 13 === e || 32 === e || 35 === e || 44 === e || 65279 === e; e = 0 | i.charCodeAt(n++)) {\n    if (35 === e) {\n      while (10 !== (e = i.charCodeAt(n++)) && 13 !== e) {}\n    }\n  }\n  n--;\n}\n\nvar t = /[_A-Za-z]\\w*/y;\n\nvar l = new RegExp(\"(?:(null|true|false)|\\\\$(\" + t.source + ')|(-?\\\\d+)((?:\\\\.\\\\d+)?[eE][+-]?\\\\d+|\\\\.\\\\d+)?|(\"\"\"(?:\"\"\"|(?:[\\\\s\\\\S]*?[^\\\\\\\\])\"\"\"))|(\"(?:\"|[^\\\\r\\\\n]*?[^\\\\\\\\]\"))|(' + t.source + \"))\", \"y\");\n\nvar o = function(e) {\n  e[e.Const = 1] = \"Const\";\n  e[e.Var = 2] = \"Var\";\n  e[e.Int = 3] = \"Int\";\n  e[e.Float = 4] = \"Float\";\n  e[e.BlockString = 5] = \"BlockString\";\n  e[e.String = 6] = \"String\";\n  e[e.Enum = 7] = \"Enum\";\n  return e;\n}(o || {});\n\nvar u = /\\\\/;\n\nfunction value(e) {\n  var r;\n  var a;\n  l.lastIndex = n;\n  if (91 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    var d = [];\n    while (93 !== i.charCodeAt(n)) {\n      d.push(value(e));\n    }\n    n++;\n    ignored();\n    return {\n      kind: \"ListValue\",\n      values: d\n    };\n  } else if (123 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    var v = [];\n    while (125 !== i.charCodeAt(n)) {\n      if (null == (r = advance(t))) {\n        throw error(\"ObjectField\");\n      }\n      ignored();\n      if (58 !== i.charCodeAt(n++)) {\n        throw error(\"ObjectField\");\n      }\n      ignored();\n      v.push({\n        kind: \"ObjectField\",\n        name: {\n          kind: \"Name\",\n          value: r\n        },\n        value: value(e)\n      });\n    }\n    n++;\n    ignored();\n    return {\n      kind: \"ObjectValue\",\n      fields: v\n    };\n  } else if (null != (a = l.exec(i))) {\n    n = l.lastIndex;\n    ignored();\n    if (null != (r = a[o.Const])) {\n      return \"null\" === r ? {\n        kind: \"NullValue\"\n      } : {\n        kind: \"BooleanValue\",\n        value: \"true\" === r\n      };\n    } else if (null != (r = a[o.Var])) {\n      if (e) {\n        throw error(\"Variable\");\n      } else {\n        return {\n          kind: \"Variable\",\n          name: {\n            kind: \"Name\",\n            value: r\n          }\n        };\n      }\n    } else if (null != (r = a[o.Int])) {\n      var s;\n      if (null != (s = a[o.Float])) {\n        return {\n          kind: \"FloatValue\",\n          value: r + s\n        };\n      } else {\n        return {\n          kind: \"IntValue\",\n          value: r\n        };\n      }\n    } else if (null != (r = a[o.BlockString])) {\n      return {\n        kind: \"StringValue\",\n        value: blockString(r.slice(3, -3)),\n        block: !0\n      };\n    } else if (null != (r = a[o.String])) {\n      return {\n        kind: \"StringValue\",\n        value: u.test(r) ? JSON.parse(r) : r.slice(1, -1),\n        block: !1\n      };\n    } else if (null != (r = a[o.Enum])) {\n      return {\n        kind: \"EnumValue\",\n        value: r\n      };\n    }\n  }\n  throw error(\"Value\");\n}\n\nfunction arguments_(e) {\n  if (40 === i.charCodeAt(n)) {\n    var r = [];\n    n++;\n    ignored();\n    var a;\n    do {\n      if (null == (a = advance(t))) {\n        throw error(\"Argument\");\n      }\n      ignored();\n      if (58 !== i.charCodeAt(n++)) {\n        throw error(\"Argument\");\n      }\n      ignored();\n      r.push({\n        kind: \"Argument\",\n        name: {\n          kind: \"Name\",\n          value: a\n        },\n        value: value(e)\n      });\n    } while (41 !== i.charCodeAt(n));\n    n++;\n    ignored();\n    return r;\n  }\n}\n\nfunction directives(e) {\n  if (64 === i.charCodeAt(n)) {\n    var r = [];\n    var a;\n    do {\n      n++;\n      if (null == (a = advance(t))) {\n        throw error(\"Directive\");\n      }\n      ignored();\n      r.push({\n        kind: \"Directive\",\n        name: {\n          kind: \"Name\",\n          value: a\n        },\n        arguments: arguments_(e)\n      });\n    } while (64 === i.charCodeAt(n));\n    return r;\n  }\n}\n\nfunction type() {\n  var e;\n  var r = 0;\n  while (91 === i.charCodeAt(n)) {\n    r++;\n    n++;\n    ignored();\n  }\n  if (null == (e = advance(t))) {\n    throw error(\"NamedType\");\n  }\n  ignored();\n  var a = {\n    kind: \"NamedType\",\n    name: {\n      kind: \"Name\",\n      value: e\n    }\n  };\n  do {\n    if (33 === i.charCodeAt(n)) {\n      n++;\n      ignored();\n      a = {\n        kind: \"NonNullType\",\n        type: a\n      };\n    }\n    if (r) {\n      if (93 !== i.charCodeAt(n++)) {\n        throw error(\"NamedType\");\n      }\n      ignored();\n      a = {\n        kind: \"ListType\",\n        type: a\n      };\n    }\n  } while (r--);\n  return a;\n}\n\nvar d = new RegExp(\"(?:(\\\\.{3})|(\" + t.source + \"))\", \"y\");\n\nvar v = function(e) {\n  e[e.Spread = 1] = \"Spread\";\n  e[e.Name = 2] = \"Name\";\n  return e;\n}(v || {});\n\nfunction selectionSet() {\n  var e = [];\n  var r;\n  var a;\n  do {\n    d.lastIndex = n;\n    if (null != (a = d.exec(i))) {\n      n = d.lastIndex;\n      if (null != a[v.Spread]) {\n        ignored();\n        var l = advance(t);\n        if (null != l && \"on\" !== l) {\n          ignored();\n          e.push({\n            kind: \"FragmentSpread\",\n            name: {\n              kind: \"Name\",\n              value: l\n            },\n            directives: directives(!1)\n          });\n        } else {\n          ignored();\n          if (\"on\" === l) {\n            if (null == (l = advance(t))) {\n              throw error(\"NamedType\");\n            }\n            ignored();\n          }\n          var o = directives(!1);\n          if (123 !== i.charCodeAt(n++)) {\n            throw error(\"InlineFragment\");\n          }\n          ignored();\n          e.push({\n            kind: \"InlineFragment\",\n            typeCondition: l ? {\n              kind: \"NamedType\",\n              name: {\n                kind: \"Name\",\n                value: l\n              }\n            } : void 0,\n            directives: o,\n            selectionSet: selectionSet()\n          });\n        }\n      } else if (null != (r = a[v.Name])) {\n        var u = void 0;\n        ignored();\n        if (58 === i.charCodeAt(n)) {\n          n++;\n          ignored();\n          u = r;\n          if (null == (r = advance(t))) {\n            throw error(\"Field\");\n          }\n          ignored();\n        }\n        var s = arguments_(!1);\n        ignored();\n        var c = directives(!1);\n        var f = void 0;\n        if (123 === i.charCodeAt(n)) {\n          n++;\n          ignored();\n          f = selectionSet();\n        }\n        e.push({\n          kind: \"Field\",\n          alias: u ? {\n            kind: \"Name\",\n            value: u\n          } : void 0,\n          name: {\n            kind: \"Name\",\n            value: r\n          },\n          arguments: s,\n          directives: c,\n          selectionSet: f\n        });\n      }\n    } else {\n      throw error(\"SelectionSet\");\n    }\n  } while (125 !== i.charCodeAt(n));\n  n++;\n  ignored();\n  return {\n    kind: \"SelectionSet\",\n    selections: e\n  };\n}\n\nfunction fragmentDefinition() {\n  var e;\n  var r;\n  if (null == (e = advance(t))) {\n    throw error(\"FragmentDefinition\");\n  }\n  ignored();\n  if (\"on\" !== advance(t)) {\n    throw error(\"FragmentDefinition\");\n  }\n  ignored();\n  if (null == (r = advance(t))) {\n    throw error(\"FragmentDefinition\");\n  }\n  ignored();\n  var a = directives(!1);\n  if (123 !== i.charCodeAt(n++)) {\n    throw error(\"FragmentDefinition\");\n  }\n  ignored();\n  return {\n    kind: \"FragmentDefinition\",\n    name: {\n      kind: \"Name\",\n      value: e\n    },\n    typeCondition: {\n      kind: \"NamedType\",\n      name: {\n        kind: \"Name\",\n        value: r\n      }\n    },\n    directives: a,\n    selectionSet: selectionSet()\n  };\n}\n\nvar s = /(?:query|mutation|subscription|fragment)/y;\n\nfunction operationDefinition(e) {\n  var r;\n  var a;\n  var l;\n  if (e) {\n    ignored();\n    r = advance(t);\n    a = function variableDefinitions() {\n      ignored();\n      if (40 === i.charCodeAt(n)) {\n        var e = [];\n        n++;\n        ignored();\n        var r;\n        do {\n          if (36 !== i.charCodeAt(n++)) {\n            throw error(\"Variable\");\n          }\n          if (null == (r = advance(t))) {\n            throw error(\"Variable\");\n          }\n          ignored();\n          if (58 !== i.charCodeAt(n++)) {\n            throw error(\"VariableDefinition\");\n          }\n          ignored();\n          var a = type();\n          var l = void 0;\n          if (61 === i.charCodeAt(n)) {\n            n++;\n            ignored();\n            l = value(!0);\n          }\n          ignored();\n          e.push({\n            kind: \"VariableDefinition\",\n            variable: {\n              kind: \"Variable\",\n              name: {\n                kind: \"Name\",\n                value: r\n              }\n            },\n            type: a,\n            defaultValue: l,\n            directives: directives(!0)\n          });\n        } while (41 !== i.charCodeAt(n));\n        n++;\n        ignored();\n        return e;\n      }\n    }();\n    l = directives(!1);\n  }\n  if (123 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    return {\n      kind: \"OperationDefinition\",\n      operation: e || \"query\",\n      name: r ? {\n        kind: \"Name\",\n        value: r\n      } : void 0,\n      variableDefinitions: a,\n      directives: l,\n      selectionSet: selectionSet()\n    };\n  }\n}\n\nfunction parse(e, r) {\n  i = \"string\" == typeof e.body ? e.body : e;\n  n = 0;\n  return function document() {\n    var e;\n    var r;\n    ignored();\n    var a = [];\n    do {\n      if (\"fragment\" === (e = advance(s))) {\n        ignored();\n        a.push(fragmentDefinition());\n      } else if (null != (r = operationDefinition(e))) {\n        a.push(r);\n      } else {\n        throw error(\"Document\");\n      }\n    } while (n < i.length);\n    return {\n      kind: \"Document\",\n      definitions: a\n    };\n  }();\n}\n\nfunction parseValue(e, r) {\n  i = \"string\" == typeof e.body ? e.body : e;\n  n = 0;\n  ignored();\n  return value(!1);\n}\n\nfunction parseType(e, r) {\n  i = \"string\" == typeof e.body ? e.body : e;\n  n = 0;\n  return type();\n}\n\nvar c = {};\n\nfunction visit(e, r) {\n  var i = [];\n  var n = [];\n  try {\n    var a = function traverse(e, a, t) {\n      var l = !1;\n      var o = r[e.kind] && r[e.kind].enter || r[e.kind] || r.enter;\n      var u = o && o.call(r, e, a, t, n, i);\n      if (!1 === u) {\n        return e;\n      } else if (null === u) {\n        return null;\n      } else if (u === c) {\n        throw c;\n      } else if (u && \"string\" == typeof u.kind) {\n        l = u !== e;\n        e = u;\n      }\n      if (t) {\n        i.push(t);\n      }\n      var d;\n      var v = {\n        ...e\n      };\n      for (var s in e) {\n        n.push(s);\n        var f = e[s];\n        if (Array.isArray(f)) {\n          var m = [];\n          for (var g = 0; g < f.length; g++) {\n            if (null != f[g] && \"string\" == typeof f[g].kind) {\n              i.push(e);\n              n.push(g);\n              d = traverse(f[g], g, f);\n              n.pop();\n              i.pop();\n              if (null == d) {\n                l = !0;\n              } else {\n                l = l || d !== f[g];\n                m.push(d);\n              }\n            }\n          }\n          f = m;\n        } else if (null != f && \"string\" == typeof f.kind) {\n          if (void 0 !== (d = traverse(f, s, e))) {\n            l = l || f !== d;\n            f = d;\n          }\n        }\n        n.pop();\n        if (l) {\n          v[s] = f;\n        }\n      }\n      if (t) {\n        i.pop();\n      }\n      var p = r[e.kind] && r[e.kind].leave || r.leave;\n      var h = p && p.call(r, e, a, t, n, i);\n      if (h === c) {\n        throw c;\n      } else if (void 0 !== h) {\n        return h;\n      } else if (void 0 !== u) {\n        return l ? v : u;\n      } else {\n        return l ? v : e;\n      }\n    }(e);\n    return void 0 !== a && !1 !== a ? a : e;\n  } catch (r) {\n    if (r !== c) {\n      throw r;\n    }\n    return e;\n  }\n}\n\nfunction mapJoin(e, r, i) {\n  var n = \"\";\n  for (var a = 0; a < e.length; a++) {\n    if (a) {\n      n += r;\n    }\n    n += i(e[a]);\n  }\n  return n;\n}\n\nfunction printString(e) {\n  return JSON.stringify(e);\n}\n\nfunction printBlockString(e) {\n  return '\"\"\"\\n' + e.replace(/\"\"\"/g, '\\\\\"\"\"') + '\\n\"\"\"';\n}\n\nvar f = \"\\n\";\n\nvar m = {\n  OperationDefinition(e) {\n    var r = e.operation;\n    if (e.name) {\n      r += \" \" + e.name.value;\n    }\n    if (e.variableDefinitions && e.variableDefinitions.length) {\n      if (!e.name) {\n        r += \" \";\n      }\n      r += \"(\" + mapJoin(e.variableDefinitions, \", \", m.VariableDefinition) + \")\";\n    }\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", m.Directive);\n    }\n    return \"query\" !== r ? r + \" \" + m.SelectionSet(e.selectionSet) : m.SelectionSet(e.selectionSet);\n  },\n  VariableDefinition(e) {\n    var r = m.Variable(e.variable) + \": \" + _print(e.type);\n    if (e.defaultValue) {\n      r += \" = \" + _print(e.defaultValue);\n    }\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", m.Directive);\n    }\n    return r;\n  },\n  Field(e) {\n    var r = e.alias ? e.alias.value + \": \" + e.name.value : e.name.value;\n    if (e.arguments && e.arguments.length) {\n      var i = mapJoin(e.arguments, \", \", m.Argument);\n      if (r.length + i.length + 2 > 80) {\n        r += \"(\" + (f += \"  \") + mapJoin(e.arguments, f, m.Argument) + (f = f.slice(0, -2)) + \")\";\n      } else {\n        r += \"(\" + i + \")\";\n      }\n    }\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", m.Directive);\n    }\n    if (e.selectionSet) {\n      r += \" \" + m.SelectionSet(e.selectionSet);\n    }\n    return r;\n  },\n  StringValue(e) {\n    if (e.block) {\n      return printBlockString(e.value).replace(/\\n/g, f);\n    } else {\n      return printString(e.value);\n    }\n  },\n  BooleanValue: e => \"\" + e.value,\n  NullValue: e => \"null\",\n  IntValue: e => e.value,\n  FloatValue: e => e.value,\n  EnumValue: e => e.value,\n  Name: e => e.value,\n  Variable: e => \"$\" + e.name.value,\n  ListValue: e => \"[\" + mapJoin(e.values, \", \", _print) + \"]\",\n  ObjectValue: e => \"{\" + mapJoin(e.fields, \", \", m.ObjectField) + \"}\",\n  ObjectField: e => e.name.value + \": \" + _print(e.value),\n  Document(e) {\n    if (!e.definitions || !e.definitions.length) {\n      return \"\";\n    }\n    return mapJoin(e.definitions, \"\\n\\n\", _print);\n  },\n  SelectionSet: e => \"{\" + (f += \"  \") + mapJoin(e.selections, f, _print) + (f = f.slice(0, -2)) + \"}\",\n  Argument: e => e.name.value + \": \" + _print(e.value),\n  FragmentSpread(e) {\n    var r = \"...\" + e.name.value;\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", m.Directive);\n    }\n    return r;\n  },\n  InlineFragment(e) {\n    var r = \"...\";\n    if (e.typeCondition) {\n      r += \" on \" + e.typeCondition.name.value;\n    }\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", m.Directive);\n    }\n    return r += \" \" + m.SelectionSet(e.selectionSet);\n  },\n  FragmentDefinition(e) {\n    var r = \"fragment \" + e.name.value;\n    r += \" on \" + e.typeCondition.name.value;\n    if (e.directives && e.directives.length) {\n      r += \" \" + mapJoin(e.directives, \" \", m.Directive);\n    }\n    return r + \" \" + m.SelectionSet(e.selectionSet);\n  },\n  Directive(e) {\n    var r = \"@\" + e.name.value;\n    if (e.arguments && e.arguments.length) {\n      r += \"(\" + mapJoin(e.arguments, \", \", m.Argument) + \")\";\n    }\n    return r;\n  },\n  NamedType: e => e.name.value,\n  ListType: e => \"[\" + _print(e.type) + \"]\",\n  NonNullType: e => _print(e.type) + \"!\"\n};\n\nvar _print = e => m[e.kind](e);\n\nfunction print(e) {\n  f = \"\\n\";\n  return m[e.kind] ? m[e.kind](e) : \"\";\n}\n\nfunction valueFromASTUntyped(e, r) {\n  switch (e.kind) {\n   case \"NullValue\":\n    return null;\n\n   case \"IntValue\":\n    return parseInt(e.value, 10);\n\n   case \"FloatValue\":\n    return parseFloat(e.value);\n\n   case \"StringValue\":\n   case \"EnumValue\":\n   case \"BooleanValue\":\n    return e.value;\n\n   case \"ListValue\":\n    var i = [];\n    for (var n of e.values) {\n      i.push(valueFromASTUntyped(n, r));\n    }\n    return i;\n\n   case \"ObjectValue\":\n    var a = Object.create(null);\n    for (var t of e.fields) {\n      a[t.name.value] = valueFromASTUntyped(t.value, r);\n    }\n    return a;\n\n   case \"Variable\":\n    return r && r[e.name.value];\n  }\n}\n\nfunction valueFromTypeNode(e, r, i) {\n  if (\"Variable\" === e.kind) {\n    return i ? valueFromTypeNode(i[e.name.value], r, i) : void 0;\n  } else if (\"NonNullType\" === r.kind) {\n    return \"NullValue\" !== e.kind ? valueFromTypeNode(e, r, i) : void 0;\n  } else if (\"NullValue\" === e.kind) {\n    return null;\n  } else if (\"ListType\" === r.kind) {\n    if (\"ListValue\" === e.kind) {\n      var n = [];\n      for (var a of e.values) {\n        var t = valueFromTypeNode(a, r.type, i);\n        if (void 0 === t) {\n          return;\n        } else {\n          n.push(t);\n        }\n      }\n      return n;\n    }\n  } else if (\"NamedType\" === r.kind) {\n    switch (r.name.value) {\n     case \"Int\":\n     case \"Float\":\n     case \"String\":\n     case \"Bool\":\n      return r.name.value + \"Value\" === e.kind ? valueFromASTUntyped(e, i) : void 0;\n\n     default:\n      return valueFromASTUntyped(e, i);\n    }\n  }\n}\n\n\n//# sourceMappingURL=graphql.web.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@0no-co+graphql.web@1.0.8_graphql@16.9.0/node_modules/@0no-co/graphql.web/dist/graphql.web.mjs\n");

/***/ })

};
;