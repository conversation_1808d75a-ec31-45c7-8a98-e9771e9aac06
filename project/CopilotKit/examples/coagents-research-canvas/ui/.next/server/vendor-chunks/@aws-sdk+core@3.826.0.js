"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+core@3.826.0";
exports.ids = ["vendor-chunks/@aws-sdk+core@3.826.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/emitWarningIfUnsupportedVersion.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/emitWarningIfUnsupportedVersion.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emitWarningIfUnsupportedVersion: () => (/* binding */ emitWarningIfUnsupportedVersion),\n/* harmony export */   state: () => (/* binding */ state)\n/* harmony export */ });\nconst state = {\n    warningEmitted: false,\n};\nconst emitWarningIfUnsupportedVersion = (version) => {\n    if (version && !state.warningEmitted && parseInt(version.substring(1, version.indexOf(\".\"))) < 18) {\n        state.warningEmitted = true;\n        process.emitWarning(`NodeDeprecationWarning: The AWS SDK for JavaScript (v3) will\nno longer support Node.js 16.x on January 6, 2025.\n\nTo continue receiving updates to AWS services, bug fixes, and security\nupdates please upgrade to a supported Node.js LTS version.\n\nMore information can be found at: https://a.co/74kJMmI`);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9jbGllbnQvZW1pdFdhcm5pbmdJZlVuc3VwcG9ydGVkVmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytjb3JlQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL2NsaWVudC9lbWl0V2FybmluZ0lmVW5zdXBwb3J0ZWRWZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBzdGF0ZSA9IHtcbiAgICB3YXJuaW5nRW1pdHRlZDogZmFsc2UsXG59O1xuZXhwb3J0IGNvbnN0IGVtaXRXYXJuaW5nSWZVbnN1cHBvcnRlZFZlcnNpb24gPSAodmVyc2lvbikgPT4ge1xuICAgIGlmICh2ZXJzaW9uICYmICFzdGF0ZS53YXJuaW5nRW1pdHRlZCAmJiBwYXJzZUludCh2ZXJzaW9uLnN1YnN0cmluZygxLCB2ZXJzaW9uLmluZGV4T2YoXCIuXCIpKSkgPCAxOCkge1xuICAgICAgICBzdGF0ZS53YXJuaW5nRW1pdHRlZCA9IHRydWU7XG4gICAgICAgIHByb2Nlc3MuZW1pdFdhcm5pbmcoYE5vZGVEZXByZWNhdGlvbldhcm5pbmc6IFRoZSBBV1MgU0RLIGZvciBKYXZhU2NyaXB0ICh2Mykgd2lsbFxubm8gbG9uZ2VyIHN1cHBvcnQgTm9kZS5qcyAxNi54IG9uIEphbnVhcnkgNiwgMjAyNS5cblxuVG8gY29udGludWUgcmVjZWl2aW5nIHVwZGF0ZXMgdG8gQVdTIHNlcnZpY2VzLCBidWcgZml4ZXMsIGFuZCBzZWN1cml0eVxudXBkYXRlcyBwbGVhc2UgdXBncmFkZSB0byBhIHN1cHBvcnRlZCBOb2RlLmpzIExUUyB2ZXJzaW9uLlxuXG5Nb3JlIGluZm9ybWF0aW9uIGNhbiBiZSBmb3VuZCBhdDogaHR0cHM6Ly9hLmNvLzc0a0pNbUlgKTtcbiAgICB9XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/emitWarningIfUnsupportedVersion.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setCredentialFeature: () => (/* binding */ setCredentialFeature)\n/* harmony export */ });\nfunction setCredentialFeature(credentials, feature, value) {\n    if (!credentials.$source) {\n        credentials.$source = {};\n    }\n    credentials.$source[feature] = value;\n    return credentials;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9jbGllbnQvc2V0Q3JlZGVudGlhbEZlYXR1cmUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9jbGllbnQvc2V0Q3JlZGVudGlhbEZlYXR1cmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHNldENyZWRlbnRpYWxGZWF0dXJlKGNyZWRlbnRpYWxzLCBmZWF0dXJlLCB2YWx1ZSkge1xuICAgIGlmICghY3JlZGVudGlhbHMuJHNvdXJjZSkge1xuICAgICAgICBjcmVkZW50aWFscy4kc291cmNlID0ge307XG4gICAgfVxuICAgIGNyZWRlbnRpYWxzLiRzb3VyY2VbZmVhdHVyZV0gPSB2YWx1ZTtcbiAgICByZXR1cm4gY3JlZGVudGlhbHM7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setFeature.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setFeature.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setFeature: () => (/* binding */ setFeature)\n/* harmony export */ });\nfunction setFeature(context, feature, value) {\n    if (!context.__aws_sdk_context) {\n        context.__aws_sdk_context = {\n            features: {},\n        };\n    }\n    else if (!context.__aws_sdk_context.features) {\n        context.__aws_sdk_context.features = {};\n    }\n    context.__aws_sdk_context.features[feature] = value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9jbGllbnQvc2V0RmVhdHVyZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0Esd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9jbGllbnQvc2V0RmVhdHVyZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gc2V0RmVhdHVyZShjb250ZXh0LCBmZWF0dXJlLCB2YWx1ZSkge1xuICAgIGlmICghY29udGV4dC5fX2F3c19zZGtfY29udGV4dCkge1xuICAgICAgICBjb250ZXh0Ll9fYXdzX3Nka19jb250ZXh0ID0ge1xuICAgICAgICAgICAgZmVhdHVyZXM6IHt9LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBlbHNlIGlmICghY29udGV4dC5fX2F3c19zZGtfY29udGV4dC5mZWF0dXJlcykge1xuICAgICAgICBjb250ZXh0Ll9fYXdzX3Nka19jb250ZXh0LmZlYXR1cmVzID0ge307XG4gICAgfVxuICAgIGNvbnRleHQuX19hd3Nfc2RrX2NvbnRleHQuZmVhdHVyZXNbZmVhdHVyZV0gPSB2YWx1ZTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setFeature.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/AwsSdkSigV4Signer.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/AwsSdkSigV4Signer.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AWSSDKSigV4Signer: () => (/* binding */ AWSSDKSigV4Signer),\n/* harmony export */   AwsSdkSigV4Signer: () => (/* binding */ AwsSdkSigV4Signer),\n/* harmony export */   validateSigningProperties: () => (/* binding */ validateSigningProperties)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getSkewCorrectedDate.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getDateHeader.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getUpdatedSystemClockOffset.js\");\n\n\nconst throwSigningPropertyError = (name, property) => {\n    if (!property) {\n        throw new Error(`Property \\`${name}\\` is not resolved for AWS SDK SigV4Auth`);\n    }\n    return property;\n};\nconst validateSigningProperties = async (signingProperties) => {\n    const context = throwSigningPropertyError(\"context\", signingProperties.context);\n    const config = throwSigningPropertyError(\"config\", signingProperties.config);\n    const authScheme = context.endpointV2?.properties?.authSchemes?.[0];\n    const signerFunction = throwSigningPropertyError(\"signer\", config.signer);\n    const signer = await signerFunction(authScheme);\n    const signingRegion = signingProperties?.signingRegion;\n    const signingRegionSet = signingProperties?.signingRegionSet;\n    const signingName = signingProperties?.signingName;\n    return {\n        config,\n        signer,\n        signingRegion,\n        signingRegionSet,\n        signingName,\n    };\n};\nclass AwsSdkSigV4Signer {\n    async sign(httpRequest, identity, signingProperties) {\n        if (!_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.isInstance(httpRequest)) {\n            throw new Error(\"The request is not an instance of `HttpRequest` and cannot be signed\");\n        }\n        const validatedProps = await validateSigningProperties(signingProperties);\n        const { config, signer } = validatedProps;\n        let { signingRegion, signingName } = validatedProps;\n        const handlerExecutionContext = signingProperties.context;\n        if (handlerExecutionContext?.authSchemes?.length ?? 0 > 1) {\n            const [first, second] = handlerExecutionContext.authSchemes;\n            if (first?.name === \"sigv4a\" && second?.name === \"sigv4\") {\n                signingRegion = second?.signingRegion ?? signingRegion;\n                signingName = second?.signingName ?? signingName;\n            }\n        }\n        const signedRequest = await signer.sign(httpRequest, {\n            signingDate: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getSkewCorrectedDate)(config.systemClockOffset),\n            signingRegion: signingRegion,\n            signingService: signingName,\n        });\n        return signedRequest;\n    }\n    errorHandler(signingProperties) {\n        return (error) => {\n            const serverTime = error.ServerTime ?? (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getDateHeader)(error.$response);\n            if (serverTime) {\n                const config = throwSigningPropertyError(\"config\", signingProperties.config);\n                const initialSystemClockOffset = config.systemClockOffset;\n                config.systemClockOffset = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.getUpdatedSystemClockOffset)(serverTime, config.systemClockOffset);\n                const clockSkewCorrected = config.systemClockOffset !== initialSystemClockOffset;\n                if (clockSkewCorrected && error.$metadata) {\n                    error.$metadata.clockSkewCorrected = true;\n                }\n            }\n            throw error;\n        };\n    }\n    successHandler(httpResponse, signingProperties) {\n        const dateHeader = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.getDateHeader)(httpResponse);\n        if (dateHeader) {\n            const config = throwSigningPropertyError(\"config\", signingProperties.config);\n            config.systemClockOffset = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.getUpdatedSystemClockOffset)(dateHeader, config.systemClockOffset);\n        }\n    }\n}\nconst AWSSDKSigV4Signer = AwsSdkSigV4Signer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/AwsSdkSigV4Signer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/NODE_AUTH_SCHEME_PREFERENCE_OPTIONS.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/NODE_AUTH_SCHEME_PREFERENCE_OPTIONS.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NODE_AUTH_SCHEME_PREFERENCE_OPTIONS: () => (/* binding */ NODE_AUTH_SCHEME_PREFERENCE_OPTIONS)\n/* harmony export */ });\n/* harmony import */ var _utils_getArrayForCommaSeparatedString__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/getArrayForCommaSeparatedString */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getArrayForCommaSeparatedString.js\");\n/* harmony import */ var _utils_getBearerTokenEnvKey__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/getBearerTokenEnvKey */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getBearerTokenEnvKey.js\");\n\n\nconst NODE_AUTH_SCHEME_PREFERENCE_ENV_KEY = \"AWS_AUTH_SCHEME_PREFERENCE\";\nconst NODE_AUTH_SCHEME_PREFERENCE_CONFIG_KEY = \"auth_scheme_preference\";\nconst NODE_AUTH_SCHEME_PREFERENCE_OPTIONS = {\n    environmentVariableSelector: (env, options) => {\n        if (options?.signingName) {\n            const bearerTokenKey = (0,_utils_getBearerTokenEnvKey__WEBPACK_IMPORTED_MODULE_0__.getBearerTokenEnvKey)(options.signingName);\n            if (bearerTokenKey in env)\n                return [\"httpBearerAuth\"];\n        }\n        if (!(NODE_AUTH_SCHEME_PREFERENCE_ENV_KEY in env))\n            return undefined;\n        return (0,_utils_getArrayForCommaSeparatedString__WEBPACK_IMPORTED_MODULE_1__.getArrayForCommaSeparatedString)(env[NODE_AUTH_SCHEME_PREFERENCE_ENV_KEY]);\n    },\n    configFileSelector: (profile) => {\n        if (!(NODE_AUTH_SCHEME_PREFERENCE_CONFIG_KEY in profile))\n            return undefined;\n        return (0,_utils_getArrayForCommaSeparatedString__WEBPACK_IMPORTED_MODULE_1__.getArrayForCommaSeparatedString)(profile[NODE_AUTH_SCHEME_PREFERENCE_CONFIG_KEY]);\n    },\n    default: [],\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/NODE_AUTH_SCHEME_PREFERENCE_OPTIONS.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/resolveAwsSdkSigV4Config.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/resolveAwsSdkSigV4Config.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveAWSSDKSigV4Config: () => (/* binding */ resolveAWSSDKSigV4Config),\n/* harmony export */   resolveAwsSdkSigV4Config: () => (/* binding */ resolveAwsSdkSigV4Config)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/core/client */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\");\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_signature_v4__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/signature-v4 */ \"(rsc)/./node_modules/.pnpm/@smithy+signature-v4@5.1.2/node_modules/@smithy/signature-v4/dist-es/index.js\");\n\n\n\nconst resolveAwsSdkSigV4Config = (config) => {\n    let inputCredentials = config.credentials;\n    let isUserSupplied = !!config.credentials;\n    let resolvedCredentials = undefined;\n    Object.defineProperty(config, \"credentials\", {\n        set(credentials) {\n            if (credentials && credentials !== inputCredentials && credentials !== resolvedCredentials) {\n                isUserSupplied = true;\n            }\n            inputCredentials = credentials;\n            const memoizedProvider = normalizeCredentialProvider(config, {\n                credentials: inputCredentials,\n                credentialDefaultProvider: config.credentialDefaultProvider,\n            });\n            const boundProvider = bindCallerConfig(config, memoizedProvider);\n            if (isUserSupplied && !boundProvider.attributed) {\n                resolvedCredentials = async (options) => boundProvider(options).then((creds) => (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_2__.setCredentialFeature)(creds, \"CREDENTIALS_CODE\", \"e\"));\n                resolvedCredentials.memoized = boundProvider.memoized;\n                resolvedCredentials.configBound = boundProvider.configBound;\n                resolvedCredentials.attributed = true;\n            }\n            else {\n                resolvedCredentials = boundProvider;\n            }\n        },\n        get() {\n            return resolvedCredentials;\n        },\n        enumerable: true,\n        configurable: true,\n    });\n    config.credentials = inputCredentials;\n    const { signingEscapePath = true, systemClockOffset = config.systemClockOffset || 0, sha256, } = config;\n    let signer;\n    if (config.signer) {\n        signer = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(config.signer);\n    }\n    else if (config.regionInfoProvider) {\n        signer = () => (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(config.region)()\n            .then(async (region) => [\n            (await config.regionInfoProvider(region, {\n                useFipsEndpoint: await config.useFipsEndpoint(),\n                useDualstackEndpoint: await config.useDualstackEndpoint(),\n            })) || {},\n            region,\n        ])\n            .then(([regionInfo, region]) => {\n            const { signingRegion, signingService } = regionInfo;\n            config.signingRegion = config.signingRegion || signingRegion || region;\n            config.signingName = config.signingName || signingService || config.serviceId;\n            const params = {\n                ...config,\n                credentials: config.credentials,\n                region: config.signingRegion,\n                service: config.signingName,\n                sha256,\n                uriEscapePath: signingEscapePath,\n            };\n            const SignerCtor = config.signerConstructor || _smithy_signature_v4__WEBPACK_IMPORTED_MODULE_1__.SignatureV4;\n            return new SignerCtor(params);\n        });\n    }\n    else {\n        signer = async (authScheme) => {\n            authScheme = Object.assign({}, {\n                name: \"sigv4\",\n                signingName: config.signingName || config.defaultSigningName,\n                signingRegion: await (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(config.region)(),\n                properties: {},\n            }, authScheme);\n            const signingRegion = authScheme.signingRegion;\n            const signingService = authScheme.signingName;\n            config.signingRegion = config.signingRegion || signingRegion;\n            config.signingName = config.signingName || signingService || config.serviceId;\n            const params = {\n                ...config,\n                credentials: config.credentials,\n                region: config.signingRegion,\n                service: config.signingName,\n                sha256,\n                uriEscapePath: signingEscapePath,\n            };\n            const SignerCtor = config.signerConstructor || _smithy_signature_v4__WEBPACK_IMPORTED_MODULE_1__.SignatureV4;\n            return new SignerCtor(params);\n        };\n    }\n    const resolvedConfig = Object.assign(config, {\n        systemClockOffset,\n        signingEscapePath,\n        signer,\n    });\n    return resolvedConfig;\n};\nconst resolveAWSSDKSigV4Config = resolveAwsSdkSigV4Config;\nfunction normalizeCredentialProvider(config, { credentials, credentialDefaultProvider, }) {\n    let credentialsProvider;\n    if (credentials) {\n        if (!credentials?.memoized) {\n            credentialsProvider = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.memoizeIdentityProvider)(credentials, _smithy_core__WEBPACK_IMPORTED_MODULE_0__.isIdentityExpired, _smithy_core__WEBPACK_IMPORTED_MODULE_0__.doesIdentityRequireRefresh);\n        }\n        else {\n            credentialsProvider = credentials;\n        }\n    }\n    else {\n        if (credentialDefaultProvider) {\n            credentialsProvider = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(credentialDefaultProvider(Object.assign({}, config, {\n                parentClientConfig: config,\n            })));\n        }\n        else {\n            credentialsProvider = async () => {\n                throw new Error(\"@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.\");\n            };\n        }\n    }\n    credentialsProvider.memoized = true;\n    return credentialsProvider;\n}\nfunction bindCallerConfig(config, credentialsProvider) {\n    if (credentialsProvider.configBound) {\n        return credentialsProvider;\n    }\n    const fn = async (options) => credentialsProvider({ ...options, callerClientConfig: config });\n    fn.memoized = credentialsProvider.memoized;\n    fn.configBound = true;\n    return fn;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/resolveAwsSdkSigV4Config.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getArrayForCommaSeparatedString.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getArrayForCommaSeparatedString.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getArrayForCommaSeparatedString: () => (/* binding */ getArrayForCommaSeparatedString)\n/* harmony export */ });\nconst getArrayForCommaSeparatedString = (str) => typeof str === \"string\" && str.length > 0 ? str.split(\",\").map((item) => item.trim()) : [];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9odHRwQXV0aFNjaGVtZXMvdXRpbHMvZ2V0QXJyYXlGb3JDb21tYVNlcGFyYXRlZFN0cmluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8iLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytjb3JlQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL2h0dHBBdXRoU2NoZW1lcy91dGlscy9nZXRBcnJheUZvckNvbW1hU2VwYXJhdGVkU3RyaW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBnZXRBcnJheUZvckNvbW1hU2VwYXJhdGVkU3RyaW5nID0gKHN0cikgPT4gdHlwZW9mIHN0ciA9PT0gXCJzdHJpbmdcIiAmJiBzdHIubGVuZ3RoID4gMCA/IHN0ci5zcGxpdChcIixcIikubWFwKChpdGVtKSA9PiBpdGVtLnRyaW0oKSkgOiBbXTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getArrayForCommaSeparatedString.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getBearerTokenEnvKey.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getBearerTokenEnvKey.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBearerTokenEnvKey: () => (/* binding */ getBearerTokenEnvKey)\n/* harmony export */ });\nconst getBearerTokenEnvKey = (signingName) => `AWS_BEARER_TOKEN_${signingName.replace(/[\\s-]/g, \"_\").toUpperCase()}`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9odHRwQXV0aFNjaGVtZXMvdXRpbHMvZ2V0QmVhcmVyVG9rZW5FbnZLZXkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLGtFQUFrRSxpREFBaUQiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytjb3JlQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL2h0dHBBdXRoU2NoZW1lcy91dGlscy9nZXRCZWFyZXJUb2tlbkVudktleS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgZ2V0QmVhcmVyVG9rZW5FbnZLZXkgPSAoc2lnbmluZ05hbWUpID0+IGBBV1NfQkVBUkVSX1RPS0VOXyR7c2lnbmluZ05hbWUucmVwbGFjZSgvW1xccy1dL2csIFwiX1wiKS50b1VwcGVyQ2FzZSgpfWA7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getBearerTokenEnvKey.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getDateHeader.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getDateHeader.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDateHeader: () => (/* binding */ getDateHeader)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n\nconst getDateHeader = (response) => _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpResponse.isInstance(response) ? response.headers?.date ?? response.headers?.Date : undefined;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9odHRwQXV0aFNjaGVtZXMvdXRpbHMvZ2V0RGF0ZUhlYWRlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUM5QyxvQ0FBb0MsK0RBQVkiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytjb3JlQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL2h0dHBBdXRoU2NoZW1lcy91dGlscy9nZXREYXRlSGVhZGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEh0dHBSZXNwb25zZSB9IGZyb20gXCJAc21pdGh5L3Byb3RvY29sLWh0dHBcIjtcbmV4cG9ydCBjb25zdCBnZXREYXRlSGVhZGVyID0gKHJlc3BvbnNlKSA9PiBIdHRwUmVzcG9uc2UuaXNJbnN0YW5jZShyZXNwb25zZSkgPyByZXNwb25zZS5oZWFkZXJzPy5kYXRlID8/IHJlc3BvbnNlLmhlYWRlcnM/LkRhdGUgOiB1bmRlZmluZWQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getDateHeader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getSkewCorrectedDate.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getSkewCorrectedDate.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSkewCorrectedDate: () => (/* binding */ getSkewCorrectedDate)\n/* harmony export */ });\nconst getSkewCorrectedDate = (systemClockOffset) => new Date(Date.now() + systemClockOffset);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9odHRwQXV0aFNjaGVtZXMvdXRpbHMvZ2V0U2tld0NvcnJlY3RlZERhdGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9odHRwQXV0aFNjaGVtZXMvdXRpbHMvZ2V0U2tld0NvcnJlY3RlZERhdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGdldFNrZXdDb3JyZWN0ZWREYXRlID0gKHN5c3RlbUNsb2NrT2Zmc2V0KSA9PiBuZXcgRGF0ZShEYXRlLm5vdygpICsgc3lzdGVtQ2xvY2tPZmZzZXQpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getSkewCorrectedDate.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getUpdatedSystemClockOffset.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getUpdatedSystemClockOffset.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUpdatedSystemClockOffset: () => (/* binding */ getUpdatedSystemClockOffset)\n/* harmony export */ });\n/* harmony import */ var _isClockSkewed__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isClockSkewed */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/isClockSkewed.js\");\n\nconst getUpdatedSystemClockOffset = (clockTime, currentSystemClockOffset) => {\n    const clockTimeInMs = Date.parse(clockTime);\n    if ((0,_isClockSkewed__WEBPACK_IMPORTED_MODULE_0__.isClockSkewed)(clockTimeInMs, currentSystemClockOffset)) {\n        return clockTimeInMs - Date.now();\n    }\n    return currentSystemClockOffset;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9odHRwQXV0aFNjaGVtZXMvdXRpbHMvZ2V0VXBkYXRlZFN5c3RlbUNsb2NrT2Zmc2V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdEO0FBQ3pDO0FBQ1A7QUFDQSxRQUFRLDZEQUFhO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9odHRwQXV0aFNjaGVtZXMvdXRpbHMvZ2V0VXBkYXRlZFN5c3RlbUNsb2NrT2Zmc2V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzQ2xvY2tTa2V3ZWQgfSBmcm9tIFwiLi9pc0Nsb2NrU2tld2VkXCI7XG5leHBvcnQgY29uc3QgZ2V0VXBkYXRlZFN5c3RlbUNsb2NrT2Zmc2V0ID0gKGNsb2NrVGltZSwgY3VycmVudFN5c3RlbUNsb2NrT2Zmc2V0KSA9PiB7XG4gICAgY29uc3QgY2xvY2tUaW1lSW5NcyA9IERhdGUucGFyc2UoY2xvY2tUaW1lKTtcbiAgICBpZiAoaXNDbG9ja1NrZXdlZChjbG9ja1RpbWVJbk1zLCBjdXJyZW50U3lzdGVtQ2xvY2tPZmZzZXQpKSB7XG4gICAgICAgIHJldHVybiBjbG9ja1RpbWVJbk1zIC0gRGF0ZS5ub3coKTtcbiAgICB9XG4gICAgcmV0dXJuIGN1cnJlbnRTeXN0ZW1DbG9ja09mZnNldDtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getUpdatedSystemClockOffset.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/isClockSkewed.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/isClockSkewed.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isClockSkewed: () => (/* binding */ isClockSkewed)\n/* harmony export */ });\n/* harmony import */ var _getSkewCorrectedDate__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getSkewCorrectedDate */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/getSkewCorrectedDate.js\");\n\nconst isClockSkewed = (clockTime, systemClockOffset) => Math.abs((0,_getSkewCorrectedDate__WEBPACK_IMPORTED_MODULE_0__.getSkewCorrectedDate)(systemClockOffset).getTime() - clockTime) >= 300000;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9odHRwQXV0aFNjaGVtZXMvdXRpbHMvaXNDbG9ja1NrZXdlZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4RDtBQUN2RCxpRUFBaUUsMkVBQW9CIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9odHRwQXV0aFNjaGVtZXMvdXRpbHMvaXNDbG9ja1NrZXdlZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRTa2V3Q29ycmVjdGVkRGF0ZSB9IGZyb20gXCIuL2dldFNrZXdDb3JyZWN0ZWREYXRlXCI7XG5leHBvcnQgY29uc3QgaXNDbG9ja1NrZXdlZCA9IChjbG9ja1RpbWUsIHN5c3RlbUNsb2NrT2Zmc2V0KSA9PiBNYXRoLmFicyhnZXRTa2V3Q29ycmVjdGVkRGF0ZShzeXN0ZW1DbG9ja09mZnNldCkuZ2V0VGltZSgpIC0gY2xvY2tUaW1lKSA+PSAzMDAwMDA7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/utils/isClockSkewed.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/common.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/common.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   collectBodyString: () => (/* binding */ collectBodyString)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n\nconst collectBodyString = (streamBody, context) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.collectBody)(streamBody, context).then((body) => context.utf8Encoder(body));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9wcm90b2NvbHMvY29tbW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBQzdDLG1EQUFtRCxrRUFBVyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK2NvcmVAMy44MjYuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY29yZS9kaXN0LWVzL3N1Ym1vZHVsZXMvcHJvdG9jb2xzL2NvbW1vbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb2xsZWN0Qm9keSB9IGZyb20gXCJAc21pdGh5L3NtaXRoeS1jbGllbnRcIjtcbmV4cG9ydCBjb25zdCBjb2xsZWN0Qm9keVN0cmluZyA9IChzdHJlYW1Cb2R5LCBjb250ZXh0KSA9PiBjb2xsZWN0Qm9keShzdHJlYW1Cb2R5LCBjb250ZXh0KS50aGVuKChib2R5KSA9PiBjb250ZXh0LnV0ZjhFbmNvZGVyKGJvZHkpKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/json/awsExpectUnion.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/json/awsExpectUnion.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   awsExpectUnion: () => (/* binding */ awsExpectUnion)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n\nconst awsExpectUnion = (value) => {\n    if (value == null) {\n        return undefined;\n    }\n    if (typeof value === \"object\" && \"__type\" in value) {\n        delete value.__type;\n    }\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.expectUnion)(value);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9wcm90b2NvbHMvanNvbi9hd3NFeHBlY3RVbmlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvRDtBQUM3QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsa0VBQVc7QUFDdEIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytjb3JlQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3Byb3RvY29scy9qc29uL2F3c0V4cGVjdFVuaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGV4cGVjdFVuaW9uIH0gZnJvbSBcIkBzbWl0aHkvc21pdGh5LWNsaWVudFwiO1xuZXhwb3J0IGNvbnN0IGF3c0V4cGVjdFVuaW9uID0gKHZhbHVlKSA9PiB7XG4gICAgaWYgKHZhbHVlID09IG51bGwpIHtcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gXCJvYmplY3RcIiAmJiBcIl9fdHlwZVwiIGluIHZhbHVlKSB7XG4gICAgICAgIGRlbGV0ZSB2YWx1ZS5fX3R5cGU7XG4gICAgfVxuICAgIHJldHVybiBleHBlY3RVbmlvbih2YWx1ZSk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/json/awsExpectUnion.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/json/parseJsonBody.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/json/parseJsonBody.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadRestJsonErrorCode: () => (/* binding */ loadRestJsonErrorCode),\n/* harmony export */   parseJsonBody: () => (/* binding */ parseJsonBody),\n/* harmony export */   parseJsonErrorBody: () => (/* binding */ parseJsonErrorBody)\n/* harmony export */ });\n/* harmony import */ var _common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../common */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/common.js\");\n\nconst parseJsonBody = (streamBody, context) => (0,_common__WEBPACK_IMPORTED_MODULE_0__.collectBodyString)(streamBody, context).then((encoded) => {\n    if (encoded.length) {\n        try {\n            return JSON.parse(encoded);\n        }\n        catch (e) {\n            if (e?.name === \"SyntaxError\") {\n                Object.defineProperty(e, \"$responseBodyText\", {\n                    value: encoded,\n                });\n            }\n            throw e;\n        }\n    }\n    return {};\n});\nconst parseJsonErrorBody = async (errorBody, context) => {\n    const value = await parseJsonBody(errorBody, context);\n    value.message = value.message ?? value.Message;\n    return value;\n};\nconst loadRestJsonErrorCode = (output, data) => {\n    const findKey = (object, key) => Object.keys(object).find((k) => k.toLowerCase() === key.toLowerCase());\n    const sanitizeErrorCode = (rawValue) => {\n        let cleanValue = rawValue;\n        if (typeof cleanValue === \"number\") {\n            cleanValue = cleanValue.toString();\n        }\n        if (cleanValue.indexOf(\",\") >= 0) {\n            cleanValue = cleanValue.split(\",\")[0];\n        }\n        if (cleanValue.indexOf(\":\") >= 0) {\n            cleanValue = cleanValue.split(\":\")[0];\n        }\n        if (cleanValue.indexOf(\"#\") >= 0) {\n            cleanValue = cleanValue.split(\"#\")[1];\n        }\n        return cleanValue;\n    };\n    const headerKey = findKey(output.headers, \"x-amzn-errortype\");\n    if (headerKey !== undefined) {\n        return sanitizeErrorCode(output.headers[headerKey]);\n    }\n    if (data && typeof data === \"object\") {\n        const codeKey = findKey(data, \"code\");\n        if (codeKey && data[codeKey] !== undefined) {\n            return sanitizeErrorCode(data[codeKey]);\n        }\n        if (data[\"__type\"] !== undefined) {\n            return sanitizeErrorCode(data[\"__type\"]);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/json/parseJsonBody.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/xml/parseXmlBody.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/xml/parseXmlBody.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadRestXmlErrorCode: () => (/* binding */ loadRestXmlErrorCode),\n/* harmony export */   parseXmlBody: () => (/* binding */ parseXmlBody),\n/* harmony export */   parseXmlErrorBody: () => (/* binding */ parseXmlErrorBody)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var fast_xml_parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fast-xml-parser */ \"(rsc)/./node_modules/.pnpm/fast-xml-parser@4.4.1/node_modules/fast-xml-parser/src/fxp.js\");\n/* harmony import */ var fast_xml_parser__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fast_xml_parser__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../common */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/common.js\");\n\n\n\nconst parseXmlBody = (streamBody, context) => (0,_common__WEBPACK_IMPORTED_MODULE_2__.collectBodyString)(streamBody, context).then((encoded) => {\n    if (encoded.length) {\n        const parser = new fast_xml_parser__WEBPACK_IMPORTED_MODULE_1__.XMLParser({\n            attributeNamePrefix: \"\",\n            htmlEntities: true,\n            ignoreAttributes: false,\n            ignoreDeclaration: true,\n            parseTagValue: false,\n            trimValues: false,\n            tagValueProcessor: (_, val) => (val.trim() === \"\" && val.includes(\"\\n\") ? \"\" : undefined),\n        });\n        parser.addEntity(\"#xD\", \"\\r\");\n        parser.addEntity(\"#10\", \"\\n\");\n        let parsedObj;\n        try {\n            parsedObj = parser.parse(encoded, true);\n        }\n        catch (e) {\n            if (e && typeof e === \"object\") {\n                Object.defineProperty(e, \"$responseBodyText\", {\n                    value: encoded,\n                });\n            }\n            throw e;\n        }\n        const textNodeName = \"#text\";\n        const key = Object.keys(parsedObj)[0];\n        const parsedObjToReturn = parsedObj[key];\n        if (parsedObjToReturn[textNodeName]) {\n            parsedObjToReturn[key] = parsedObjToReturn[textNodeName];\n            delete parsedObjToReturn[textNodeName];\n        }\n        return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.getValueFromTextNode)(parsedObjToReturn);\n    }\n    return {};\n});\nconst parseXmlErrorBody = async (errorBody, context) => {\n    const value = await parseXmlBody(errorBody, context);\n    if (value.Error) {\n        value.Error.message = value.Error.message ?? value.Error.Message;\n    }\n    return value;\n};\nconst loadRestXmlErrorCode = (output, data) => {\n    if (data?.Error?.Code !== undefined) {\n        return data.Error.Code;\n    }\n    if (data?.Code !== undefined) {\n        return data.Code;\n    }\n    if (output.statusCode == 404) {\n        return \"NotFound\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9wcm90b2NvbHMveG1sL3BhcnNlWG1sQm9keS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTZEO0FBQ2pCO0FBQ0U7QUFDdkMsOENBQThDLDBEQUFpQjtBQUN0RTtBQUNBLDJCQUEyQixzREFBUztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsMkVBQW9CO0FBQ25DO0FBQ0E7QUFDQSxDQUFDO0FBQ007QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY29yZUAzLjgyNi4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9wcm90b2NvbHMveG1sL3BhcnNlWG1sQm9keS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRWYWx1ZUZyb21UZXh0Tm9kZSB9IGZyb20gXCJAc21pdGh5L3NtaXRoeS1jbGllbnRcIjtcbmltcG9ydCB7IFhNTFBhcnNlciB9IGZyb20gXCJmYXN0LXhtbC1wYXJzZXJcIjtcbmltcG9ydCB7IGNvbGxlY3RCb2R5U3RyaW5nIH0gZnJvbSBcIi4uL2NvbW1vblwiO1xuZXhwb3J0IGNvbnN0IHBhcnNlWG1sQm9keSA9IChzdHJlYW1Cb2R5LCBjb250ZXh0KSA9PiBjb2xsZWN0Qm9keVN0cmluZyhzdHJlYW1Cb2R5LCBjb250ZXh0KS50aGVuKChlbmNvZGVkKSA9PiB7XG4gICAgaWYgKGVuY29kZWQubGVuZ3RoKSB7XG4gICAgICAgIGNvbnN0IHBhcnNlciA9IG5ldyBYTUxQYXJzZXIoe1xuICAgICAgICAgICAgYXR0cmlidXRlTmFtZVByZWZpeDogXCJcIixcbiAgICAgICAgICAgIGh0bWxFbnRpdGllczogdHJ1ZSxcbiAgICAgICAgICAgIGlnbm9yZUF0dHJpYnV0ZXM6IGZhbHNlLFxuICAgICAgICAgICAgaWdub3JlRGVjbGFyYXRpb246IHRydWUsXG4gICAgICAgICAgICBwYXJzZVRhZ1ZhbHVlOiBmYWxzZSxcbiAgICAgICAgICAgIHRyaW1WYWx1ZXM6IGZhbHNlLFxuICAgICAgICAgICAgdGFnVmFsdWVQcm9jZXNzb3I6IChfLCB2YWwpID0+ICh2YWwudHJpbSgpID09PSBcIlwiICYmIHZhbC5pbmNsdWRlcyhcIlxcblwiKSA/IFwiXCIgOiB1bmRlZmluZWQpLFxuICAgICAgICB9KTtcbiAgICAgICAgcGFyc2VyLmFkZEVudGl0eShcIiN4RFwiLCBcIlxcclwiKTtcbiAgICAgICAgcGFyc2VyLmFkZEVudGl0eShcIiMxMFwiLCBcIlxcblwiKTtcbiAgICAgICAgbGV0IHBhcnNlZE9iajtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIHBhcnNlZE9iaiA9IHBhcnNlci5wYXJzZShlbmNvZGVkLCB0cnVlKTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgaWYgKGUgJiYgdHlwZW9mIGUgPT09IFwib2JqZWN0XCIpIHtcbiAgICAgICAgICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoZSwgXCIkcmVzcG9uc2VCb2R5VGV4dFwiLCB7XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiBlbmNvZGVkLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhyb3cgZTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB0ZXh0Tm9kZU5hbWUgPSBcIiN0ZXh0XCI7XG4gICAgICAgIGNvbnN0IGtleSA9IE9iamVjdC5rZXlzKHBhcnNlZE9iailbMF07XG4gICAgICAgIGNvbnN0IHBhcnNlZE9ialRvUmV0dXJuID0gcGFyc2VkT2JqW2tleV07XG4gICAgICAgIGlmIChwYXJzZWRPYmpUb1JldHVyblt0ZXh0Tm9kZU5hbWVdKSB7XG4gICAgICAgICAgICBwYXJzZWRPYmpUb1JldHVybltrZXldID0gcGFyc2VkT2JqVG9SZXR1cm5bdGV4dE5vZGVOYW1lXTtcbiAgICAgICAgICAgIGRlbGV0ZSBwYXJzZWRPYmpUb1JldHVyblt0ZXh0Tm9kZU5hbWVdO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBnZXRWYWx1ZUZyb21UZXh0Tm9kZShwYXJzZWRPYmpUb1JldHVybik7XG4gICAgfVxuICAgIHJldHVybiB7fTtcbn0pO1xuZXhwb3J0IGNvbnN0IHBhcnNlWG1sRXJyb3JCb2R5ID0gYXN5bmMgKGVycm9yQm9keSwgY29udGV4dCkgPT4ge1xuICAgIGNvbnN0IHZhbHVlID0gYXdhaXQgcGFyc2VYbWxCb2R5KGVycm9yQm9keSwgY29udGV4dCk7XG4gICAgaWYgKHZhbHVlLkVycm9yKSB7XG4gICAgICAgIHZhbHVlLkVycm9yLm1lc3NhZ2UgPSB2YWx1ZS5FcnJvci5tZXNzYWdlID8/IHZhbHVlLkVycm9yLk1lc3NhZ2U7XG4gICAgfVxuICAgIHJldHVybiB2YWx1ZTtcbn07XG5leHBvcnQgY29uc3QgbG9hZFJlc3RYbWxFcnJvckNvZGUgPSAob3V0cHV0LCBkYXRhKSA9PiB7XG4gICAgaWYgKGRhdGE/LkVycm9yPy5Db2RlICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcmV0dXJuIGRhdGEuRXJyb3IuQ29kZTtcbiAgICB9XG4gICAgaWYgKGRhdGE/LkNvZGUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICByZXR1cm4gZGF0YS5Db2RlO1xuICAgIH1cbiAgICBpZiAob3V0cHV0LnN0YXR1c0NvZGUgPT0gNDA0KSB7XG4gICAgICAgIHJldHVybiBcIk5vdEZvdW5kXCI7XG4gICAgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/xml/parseXmlBody.js\n");

/***/ })

};
;