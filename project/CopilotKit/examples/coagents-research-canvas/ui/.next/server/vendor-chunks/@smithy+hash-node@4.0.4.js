"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+hash-node@4.0.4";
exports.ids = ["vendor-chunks/@smithy+hash-node@4.0.4"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+hash-node@4.0.4/node_modules/@smithy/hash-node/dist-es/index.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+hash-node@4.0.4/node_modules/@smithy/hash-node/dist-es/index.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hash: () => (/* binding */ Hash)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-buffer-from */ \"(rsc)/./node_modules/.pnpm/@smithy+util-buffer-from@4.0.0/node_modules/@smithy/util-buffer-from/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n/* harmony import */ var buffer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! buffer */ \"buffer\");\n/* harmony import */ var buffer__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(buffer__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nclass Hash {\n    constructor(algorithmIdentifier, secret) {\n        this.algorithmIdentifier = algorithmIdentifier;\n        this.secret = secret;\n        this.reset();\n    }\n    update(toHash, encoding) {\n        this.hash.update((0,_smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__.toUint8Array)(castSourceData(toHash, encoding)));\n    }\n    digest() {\n        return Promise.resolve(this.hash.digest());\n    }\n    reset() {\n        this.hash = this.secret\n            ? (0,crypto__WEBPACK_IMPORTED_MODULE_3__.createHmac)(this.algorithmIdentifier, castSourceData(this.secret))\n            : (0,crypto__WEBPACK_IMPORTED_MODULE_3__.createHash)(this.algorithmIdentifier);\n    }\n}\nfunction castSourceData(toCast, encoding) {\n    if (buffer__WEBPACK_IMPORTED_MODULE_2__.Buffer.isBuffer(toCast)) {\n        return toCast;\n    }\n    if (typeof toCast === \"string\") {\n        return (0,_smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_0__.fromString)(toCast, encoding);\n    }\n    if (ArrayBuffer.isView(toCast)) {\n        return (0,_smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_0__.fromArrayBuffer)(toCast.buffer, toCast.byteOffset, toCast.byteLength);\n    }\n    return (0,_smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_0__.fromArrayBuffer)(toCast);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+hash-node@4.0.4/node_modules/@smithy/hash-node/dist-es/index.js\n");

/***/ })

};
;