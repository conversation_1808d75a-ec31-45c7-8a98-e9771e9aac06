"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+credential-provider-env@3.826.0";
exports.ids = ["vendor-chunks/@aws-sdk+credential-provider-env@3.826.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-env@3.826.0/node_modules/@aws-sdk/credential-provider-env/dist-es/fromEnv.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-env@3.826.0/node_modules/@aws-sdk/credential-provider-env/dist-es/fromEnv.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ENV_ACCOUNT_ID: () => (/* binding */ ENV_ACCOUNT_ID),\n/* harmony export */   ENV_CREDENTIAL_SCOPE: () => (/* binding */ ENV_CREDENTIAL_SCOPE),\n/* harmony export */   ENV_EXPIRATION: () => (/* binding */ ENV_EXPIRATION),\n/* harmony export */   ENV_KEY: () => (/* binding */ ENV_KEY),\n/* harmony export */   ENV_SECRET: () => (/* binding */ ENV_SECRET),\n/* harmony export */   ENV_SESSION: () => (/* binding */ ENV_SESSION),\n/* harmony export */   fromEnv: () => (/* binding */ fromEnv)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/core/client */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\");\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n\n\nconst ENV_KEY = \"AWS_ACCESS_KEY_ID\";\nconst ENV_SECRET = \"AWS_SECRET_ACCESS_KEY\";\nconst ENV_SESSION = \"AWS_SESSION_TOKEN\";\nconst ENV_EXPIRATION = \"AWS_CREDENTIAL_EXPIRATION\";\nconst ENV_CREDENTIAL_SCOPE = \"AWS_CREDENTIAL_SCOPE\";\nconst ENV_ACCOUNT_ID = \"AWS_ACCOUNT_ID\";\nconst fromEnv = (init) => async () => {\n    init?.logger?.debug(\"@aws-sdk/credential-provider-env - fromEnv\");\n    const accessKeyId = process.env[ENV_KEY];\n    const secretAccessKey = process.env[ENV_SECRET];\n    const sessionToken = process.env[ENV_SESSION];\n    const expiry = process.env[ENV_EXPIRATION];\n    const credentialScope = process.env[ENV_CREDENTIAL_SCOPE];\n    const accountId = process.env[ENV_ACCOUNT_ID];\n    if (accessKeyId && secretAccessKey) {\n        const credentials = {\n            accessKeyId,\n            secretAccessKey,\n            ...(sessionToken && { sessionToken }),\n            ...(expiry && { expiration: new Date(expiry) }),\n            ...(credentialScope && { credentialScope }),\n            ...(accountId && { accountId }),\n        };\n        (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_1__.setCredentialFeature)(credentials, \"CREDENTIALS_ENV_VARS\", \"g\");\n        return credentials;\n    }\n    throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(\"Unable to find environment variable credentials.\", { logger: init?.logger });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-env@3.826.0/node_modules/@aws-sdk/credential-provider-env/dist-es/fromEnv.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-env@3.826.0/node_modules/@aws-sdk/credential-provider-env/dist-es/index.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-env@3.826.0/node_modules/@aws-sdk/credential-provider-env/dist-es/index.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ENV_ACCOUNT_ID: () => (/* reexport safe */ _fromEnv__WEBPACK_IMPORTED_MODULE_0__.ENV_ACCOUNT_ID),\n/* harmony export */   ENV_CREDENTIAL_SCOPE: () => (/* reexport safe */ _fromEnv__WEBPACK_IMPORTED_MODULE_0__.ENV_CREDENTIAL_SCOPE),\n/* harmony export */   ENV_EXPIRATION: () => (/* reexport safe */ _fromEnv__WEBPACK_IMPORTED_MODULE_0__.ENV_EXPIRATION),\n/* harmony export */   ENV_KEY: () => (/* reexport safe */ _fromEnv__WEBPACK_IMPORTED_MODULE_0__.ENV_KEY),\n/* harmony export */   ENV_SECRET: () => (/* reexport safe */ _fromEnv__WEBPACK_IMPORTED_MODULE_0__.ENV_SECRET),\n/* harmony export */   ENV_SESSION: () => (/* reexport safe */ _fromEnv__WEBPACK_IMPORTED_MODULE_0__.ENV_SESSION),\n/* harmony export */   fromEnv: () => (/* reexport safe */ _fromEnv__WEBPACK_IMPORTED_MODULE_0__.fromEnv)\n/* harmony export */ });\n/* harmony import */ var _fromEnv__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fromEnv */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-env@3.826.0/node_modules/@aws-sdk/credential-provider-env/dist-es/fromEnv.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1lbnZAMy44MjYuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1lbnYvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK2NyZWRlbnRpYWwtcHJvdmlkZXItZW52QDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItZW52L2Rpc3QtZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vZnJvbUVudlwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-env@3.826.0/node_modules/@aws-sdk/credential-provider-env/dist-es/index.js\n");

/***/ })

};
;