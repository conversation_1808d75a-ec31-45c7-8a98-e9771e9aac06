"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ag-ui+langgraph@0.0.4_openai@4.85.1_ws@8.18.0_zod@3.23.8__react@19.0.0";
exports.ids = ["vendor-chunks/@ag-ui+langgraph@0.0.4_openai@4.85.1_ws@8.18.0_zod@3.23.8__react@19.0.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@ag-ui+langgraph@0.0.4_openai@4.85.1_ws@8.18.0_zod@3.23.8__react@19.0.0/node_modules/@ag-ui/langgraph/dist/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ag-ui+langgraph@0.0.4_openai@4.85.1_ws@8.18.0_zod@3.23.8__react@19.0.0/node_modules/@ag-ui/langgraph/dist/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomEventNames: () => (/* binding */ pt),\n/* harmony export */   LangGraphAgent: () => (/* binding */ it),\n/* harmony export */   LangGraphEventTypes: () => (/* binding */ ut)\n/* harmony export */ });\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs */ \"(rsc)/./node_modules/.pnpm/rxjs@7.8.1/node_modules/rxjs/dist/esm5/internal/Observable.js\");\n/* harmony import */ var _langchain_langgraph_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @langchain/langgraph-sdk */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/index.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var _langchain_core_messages__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @langchain/core/messages */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/messages.js\");\n/* harmony import */ var _ag_ui_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ag-ui/client */ \"(rsc)/./node_modules/.pnpm/@ag-ui+client@0.0.28/node_modules/@ag-ui/client/dist/index.mjs\");\n/* harmony import */ var _ag_ui_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ag-ui/client */ \"(rsc)/./node_modules/.pnpm/@ag-ui+core@0.0.28/node_modules/@ag-ui/core/dist/index.mjs\");\nvar lt=Object.defineProperty,dt=Object.defineProperties;var ht=Object.getOwnPropertyDescriptors;var z=Object.getOwnPropertySymbols;var gt=Object.prototype.hasOwnProperty,ct=Object.prototype.propertyIsEnumerable;var Q=(a,t)=>(t=Symbol[a])?t:Symbol.for(\"Symbol.\"+a);var Z=(a,t,e)=>t in a?lt(a,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):a[t]=e,u=(a,t)=>{for(var e in t||(t={}))gt.call(t,e)&&Z(a,e,t[e]);if(z)for(var e of z(t))ct.call(t,e)&&Z(a,e,t[e]);return a},y=(a,t)=>dt(a,ht(t));var tt=(a,t,e)=>(t=a[Q(\"asyncIterator\")])?t.call(a):(a=a[Q(\"iterator\")](),t={},e=(s,n)=>(n=a[s])&&(t[s]=r=>new Promise((c,h,d)=>(r=n.call(a,r),d=r.done,Promise.resolve(r.value).then(m=>c({value:m,done:d}),h)))),e(\"next\"),e(\"return\"),t);var ut=(o=>(o.OnChainStart=\"on_chain_start\",o.OnChainStream=\"on_chain_stream\",o.OnChainEnd=\"on_chain_end\",o.OnChatModelStart=\"on_chat_model_start\",o.OnChatModelStream=\"on_chat_model_stream\",o.OnChatModelEnd=\"on_chat_model_end\",o.OnToolStart=\"on_tool_start\",o.OnToolEnd=\"on_tool_end\",o.OnCustomEvent=\"on_custom_event\",o.OnInterrupt=\"on_interrupt\",o))(ut||{}),pt=(n=>(n.ManuallyEmitMessage=\"manually_emit_message\",n.ManuallyEmitToolCall=\"manually_emit_tool_call\",n.ManuallyEmitState=\"manually_emit_state\",n.Exit=\"exit\",n))(pt||{});var A=[\"tools\"];function P(a,t){return Object.fromEntries(Object.entries(a).filter(([e])=>t.includes(e)))}function et({mode:a,state:t,schemaKeys:e}){let s=a===\"start\"?t:null;return s&&(e!=null&&e.input)&&(s=P(s,[...A,...e.input])),s}function st(a){return a.map(t=>{var e;switch(t.type){case\"human\":return{id:t.id,role:\"user\",content:x(I(t.content))};case\"ai\":let s=I(t.content);return{id:t.id,role:\"assistant\",content:s?x(s):\"\",toolCalls:(e=t.tool_calls)==null?void 0:e.map(n=>({id:n.id,type:\"function\",function:{name:n.name,arguments:JSON.stringify(n.args)}}))};case\"system\":return{id:t.id,role:\"system\",content:x(I(t.content))};case\"tool\":return{id:t.id,role:\"tool\",content:x(I(t.content)),toolCallId:t.tool_call_id};default:throw new Error(\"message type returned from LangGraph is not supported.\")}})}function nt(a){return a.map((t,e)=>{var s,n;switch(t.role){case\"user\":return{id:t.id,role:t.role,content:t.content,type:\"human\"};case\"assistant\":return{id:t.id,type:\"ai\",role:t.role,content:(s=t.content)!=null?s:\"\",tool_calls:((n=t.toolCalls)!=null?n:[]).map(r=>({id:r.id,name:r.function.name,args:JSON.parse(r.function.arguments),type:\"tool_call\"}))};case\"system\":return{id:t.id,role:t.role,content:t.content,type:\"system\"};case\"tool\":return{content:t.content,role:t.role,type:t.role,tool_call_id:t.toolCallId,id:t.id};default:throw console.error(`Message role ${t.role} is not implemented`),new Error(\"message role is not supported.\")}})}function x(a){return typeof a==\"string\"?a:JSON.stringify(a)}function at(a){var e,s,n,r,c;let t=(e=a.chunk)==null?void 0:e.content;if(t&&Array.isArray(t)&&t.length&&t[0])return t[0].thinking?{text:t[0].thinking,type:\"text\",index:t[0].index}:null;if((r=(n=(s=a.chunk.additional_kwargs)==null?void 0:s.reasoning)==null?void 0:n.summary)!=null&&r[0]){let h=(c=a.chunk.additional_kwargs)==null?void 0:c.reasoning.summary[0];return!h||!h.text?null:{type:\"text\",text:h.text,index:h.index}}return null}function I(a){var t;if(!a)return null;if(typeof a==\"string\")return a;if(Array.isArray(a)&&a.length){let e=(t=a.find(s=>s.type===\"text\"))==null?void 0:t.text;return e!=null?e:null}return null}var it=class extends _ag_ui_client__WEBPACK_IMPORTED_MODULE_3__.AbstractAgent{constructor(t){var e,s;super(t),this.messagesInProcess={},this.agentName=t.agentName,this.graphId=t.graphId,this.assistantConfig=t.assistantConfig,this.thinkingProcess=null,this.client=(s=t==null?void 0:t.client)!=null?s:new _langchain_langgraph_sdk__WEBPACK_IMPORTED_MODULE_0__.Client({apiUrl:t.deploymentUrl,apiKey:t.langsmithApiKey,defaultHeaders:u({},(e=t.propertyHeaders)!=null?e:{})})}dispatchEvent(t){return this.subscriber.next(t),!0}run(t){return this.activeRun={id:t.runId,threadId:t.threadId},new rxjs__WEBPACK_IMPORTED_MODULE_4__.Observable(e=>(this.handleStreamEvents(t,e),()=>{}))}async handleStreamEvents(t,e){var D,H,K,U,F,J,X,B,j,$,Y,V,W;let{threadId:s,state:n,messages:r,tools:c,context:h,forwardedProps:d}=t;this.subscriber=e;let m=!1;this.activeRun.manuallyEmittedState=null,this.activeRun.nodeName=(D=t.forwardedProps)==null?void 0:D.nodeName;let o=s!=null?s:(0,crypto__WEBPACK_IMPORTED_MODULE_1__.randomUUID)();this.assistant||(this.assistant=await this.getAssistant());let l=await this.getOrCreateThread(o);this.activeRun.threadId=l.thread_id;let v=(H=await this.client.threads.getState(l.thread_id))!=null?H:{values:{}},g=v.values,k=nt(r);n.messages=g.messages,n=this.langGraphDefaultMergeState(n,k,c);let N=o&&this.activeRun.nodeName!=\"__end__\"&&this.activeRun.nodeName?\"continue\":\"start\";N===\"continue\"&&!((K=d==null?void 0:d.command)!=null&&K.resume)&&await this.client.threads.updateState(o,{values:n,asNode:this.activeRun.nodeName}),this.activeRun.schemaKeys=await this.getSchemaKeys();let O=et({mode:N,state:n,schemaKeys:this.activeRun.schemaKeys}),w=await this.client.assistants.getGraph(this.assistant.assistant_id),T,R=[this.assistantConfig,d==null?void 0:d.config].filter(Boolean);R.length&&(T=await this.mergeConfigs({configs:R,assistant:this.assistant,schemaKeys:this.activeRun.schemaKeys}));let C=y(u({},d),{streamMode:(U=d==null?void 0:d.streamMode)!=null?U:[\"events\",\"values\",\"updates\"],input:O,config:T}),_=(X=(J=(F=v.tasks)==null?void 0:F[0])==null?void 0:J.interrupts)!=null?X:[];if(_!=null&&_.length&&!((B=d==null?void 0:d.command)!=null&&B.resume))return this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.RUN_STARTED,threadId:o,runId:t.runId}),_.forEach(E=>{this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.CUSTOM,name:\"on_interrupt\",value:typeof E.value==\"string\"?E.value:JSON.stringify(E.value),rawEvent:E})}),this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.RUN_FINISHED,threadId:o,runId:t.runId}),e.complete();let S=this.client.runs.stream(o,this.assistant.assistant_id,C);this.activeRun.prevNodeName=null;let b={},L=n;try{this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.RUN_STARTED,threadId:o,runId:this.activeRun.id});try{for(var ce=tt(S),ue,pe,me;ue=!(pe=await ce.next()).done;ue=!1){let p=pe.value;if(!C.streamMode.includes(p.event))continue;let G=p;if(p.event===\"error\"){this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.RUN_ERROR,message:p.data.message,rawEvent:p});break}if(p.event===\"updates\")continue;if(p.event===\"values\"){b=G.data;continue}let M=G.data,f=M.metadata.langgraph_node,q=M.event,rt=M.metadata;if(this.activeRun.id=rt.run_id,f&&f!==this.activeRun.nodeName&&(this.activeRun.nodeName&&this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.STEP_FINISHED,stepName:this.activeRun.nodeName}),f&&(this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.STEP_STARTED,stepName:f}),this.activeRun.nodeName=f)),m=m||q===\"on_custom_event\"&&M.name===\"exit\",this.activeRun.exitingNode=this.activeRun.nodeName===f&&q===\"on_chain_end\",this.activeRun.exitingNode&&(this.activeRun.manuallyEmittedState=null),w.nodes.some(ot=>ot.id===f)&&(this.activeRun.nodeName=f),L=(j=this.activeRun.manuallyEmittedState)!=null?j:b,!this.activeRun.nodeName)continue;(JSON.stringify(L)!==JSON.stringify(n)||this.activeRun.prevNodeName!=this.activeRun.nodeName||this.activeRun.exitingNode)&&!this.getMessageInProgress(this.activeRun.id)&&(n=L,this.activeRun.prevNodeName=this.activeRun.nodeName,this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.STATE_SNAPSHOT,snapshot:this.getStateSnapshot(n),rawEvent:G})),this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.RAW,event:M}),this.handleSingleEvent(M,n)}}catch(pe){me=[pe]}finally{try{ue&&(pe=ce.return)&&await pe.call(ce)}finally{if(me)throw me[0]}}n=await this.client.threads.getState(o);let E=(V=(Y=($=n.tasks)==null?void 0:$[0])==null?void 0:Y.interrupts)!=null?V:[];return this.activeRun.nodeName=E?this.activeRun.nodeName:Object.keys(n.metadata.writes)[0],E.forEach(p=>{this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.CUSTOM,name:\"on_interrupt\",value:typeof p.value==\"string\"?p.value:JSON.stringify(p.value),rawEvent:p})}),this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.STATE_SNAPSHOT,snapshot:this.getStateSnapshot(n.values)}),this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.MESSAGES_SNAPSHOT,messages:st((W=n.values.messages)!=null?W:[])}),this.activeRun.nodeName&&this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.STEP_FINISHED,stepName:this.activeRun.nodeName}),this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.RUN_FINISHED,threadId:o,runId:this.activeRun.id}),e.complete()}catch(E){return e.error(E)}}handleSingleEvent(t,e){var s,n,r,c,h,d;switch(t.event){case\"on_chat_model_stream\":let m=(s=t.metadata[\"emit-messages\"])!=null?s:!0,o=(n=t.metadata[\"emit-tool-calls\"])!=null?n:!0;if(t.data.chunk.response_metadata.finish_reason)return;let l=this.getMessageInProgress(this.activeRun.id),v=!!(l!=null&&l.id),g=(r=t.data.chunk.tool_call_chunks)==null?void 0:r[0],k=(c=t.metadata.predict_state)==null?void 0:c.some(S=>S.tool===(g==null?void 0:g.name)),N=!v&&(g==null?void 0:g.name),O=v&&(l==null?void 0:l.toolCallId)&&(g==null?void 0:g.args),w=v&&(l==null?void 0:l.toolCallId)&&!g,T=at(t.data),R=I(t.data.chunk.content),C=!!(!g&&R),_=v&&!(l!=null&&l.toolCallId)&&!C;if(T){this.handleThinkingEvent(T);break}if(!T&&this.thinkingProcess&&(this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.THINKING_TEXT_MESSAGE_END}),this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.THINKING_END}),this.thinkingProcess=null),k&&this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.CUSTOM,name:\"PredictState\",value:t.metadata.predict_state}),w){this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TOOL_CALL_END,toolCallId:l==null?void 0:l.toolCallId,rawEvent:t})&&(this.messagesInProcess[this.activeRun.id]=null);break}if(_){this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TEXT_MESSAGE_END,messageId:l.id,rawEvent:t})&&(this.messagesInProcess[this.activeRun.id]=null);break}if(N&&o){this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TOOL_CALL_START,toolCallId:g.id,toolCallName:g.name,parentMessageId:t.data.chunk.id,rawEvent:t})&&this.setMessageInProgress(this.activeRun.id,{id:t.data.chunk.id,toolCallId:g.id,toolCallName:g.name});break}if(O&&o){this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TOOL_CALL_ARGS,toolCallId:l==null?void 0:l.toolCallId,delta:g.args,rawEvent:t});break}if(C&&m){l||(this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TEXT_MESSAGE_START,role:\"assistant\",messageId:t.data.chunk.id,rawEvent:t}),this.setMessageInProgress(this.activeRun.id,{id:t.data.chunk.id,toolCallId:null,toolCallName:null}),l=this.getMessageInProgress(this.activeRun.id)),this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TEXT_MESSAGE_CONTENT,messageId:l.id,delta:R,rawEvent:t});break}break;case\"on_chat_model_end\":if((h=this.getMessageInProgress(this.activeRun.id))!=null&&h.toolCallId){this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TOOL_CALL_END,toolCallId:this.getMessageInProgress(this.activeRun.id).toolCallId,rawEvent:t})&&(this.messagesInProcess[this.activeRun.id]=null);break}if((d=this.getMessageInProgress(this.activeRun.id))!=null&&d.id){this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TEXT_MESSAGE_END,messageId:this.getMessageInProgress(this.activeRun.id).id,rawEvent:t})&&(this.messagesInProcess[this.activeRun.id]=null);break}break;case\"on_custom_event\":if(t.name===\"manually_emit_message\"){this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TEXT_MESSAGE_START,role:\"assistant\",messageId:t.data.message_id,rawEvent:t}),this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TEXT_MESSAGE_CONTENT,messageId:t.data.message_id,delta:t.data.message,rawEvent:t}),this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TEXT_MESSAGE_END,messageId:t.data.message_id,rawEvent:t});break}if(t.name===\"manually_emit_tool_call\"){this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TOOL_CALL_START,toolCallId:t.data.id,toolCallName:t.data.name,parentMessageId:t.data.id,rawEvent:t}),this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TOOL_CALL_ARGS,toolCallId:t.data.id,delta:t.data.args,rawEvent:t}),this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.TOOL_CALL_END,toolCallId:t.data.id,rawEvent:t});break}t.name===\"manually_emit_state\"&&(this.activeRun.manuallyEmittedState=t.data,this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.STATE_SNAPSHOT,snapshot:this.getStateSnapshot(e),rawEvent:t})),this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.CUSTOM,name:t.name,value:t.data,rawEvent:t});break}}handleThinkingEvent(t){var s;if(!t||!t.type||!t.text)return;let e=t.index;(s=this.thinkingProcess)!=null&&s.index&&this.thinkingProcess.index!==e&&(this.thinkingProcess.type&&this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.THINKING_TEXT_MESSAGE_END}),this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.THINKING_END}),this.thinkingProcess=null),this.thinkingProcess||(this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.THINKING_START}),this.thinkingProcess={index:e}),this.thinkingProcess.type!==t.type&&(this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.THINKING_TEXT_MESSAGE_START}),this.thinkingProcess.type=t.type),this.thinkingProcess.type&&this.dispatchEvent({type:_ag_ui_client__WEBPACK_IMPORTED_MODULE_5__.EventType.THINKING_TEXT_MESSAGE_CONTENT,delta:t.text})}getStateSnapshot(t){let e=this.activeRun.schemaKeys;return e!=null&&e.output&&(t=P(t,[...A,...e.output])),t}async getOrCreateThread(t){let e;try{try{e=await this.getThread(t)}catch(s){e=await this.createThread({threadId:t})}}catch(s){throw new Error(`Failed to create thread: ${s.message}`)}return e}async getThread(t){return this.client.threads.get(t)}async createThread(t){return this.client.threads.create(t)}async mergeConfigs({configs:t,assistant:e,schemaKeys:s}){return t.reduce((n,r)=>{var l;let c=n.configurable;r.configurable&&(c=s!=null&&s.config?P(r==null?void 0:r.configurable,[...A,...(l=s==null?void 0:s.config)!=null?l:[]]):r==null?void 0:r.configurable);let h=y(u(u({},n),r),{configurable:c}),d=n.recursion_limit==null&&r.recursion_limit===25,m=JSON.stringify(h)!==JSON.stringify(n),o=d&&JSON.stringify(y(u({},h),{recursion_limit:null}))===JSON.stringify(y(u({},n),{recursion_limit:null}));return m&&!o?u(u({},n),h):n},e.config)}getMessageInProgress(t){return this.messagesInProcess[t]}setMessageInProgress(t,e){this.messagesInProcess=y(u({},this.messagesInProcess),{[t]:u(u({},this.messagesInProcess[t]),e)})}async getAssistant(){let t=await this.client.assistants.search(),e=t.find(s=>s.graph_id===this.graphId);if(!e)throw console.error(`\n      No agent found with graph ID ${this.graphId} found..\n\n      \n      These are the available agents: [${t.map(s=>`${s.graph_id} (ID: ${s.assistant_id})`).join(\", \")}]\n      `),new Error(\"No agent id found\");return e}async getSchemaKeys(){var e,s,n;let t=[\"messages\"];try{let r=await this.client.assistants.getSchemas(this.assistant.assistant_id),c=null;if((e=r.config_schema)!=null&&e.properties&&(c=Object.keys(r.config_schema.properties)),!((s=r.input_schema)!=null&&s.properties)||!((n=r.output_schema)!=null&&n.properties))return{config:[],input:t,output:t};let h=Object.keys(r.input_schema.properties),d=Object.keys(r.output_schema.properties);return{input:h&&h.length?[...h,...t]:null,output:d&&d.length?[...d,...t]:null,config:c}}catch(r){return{config:[],input:t,output:t}}}langGraphDefaultMergeState(t,e,s){var m;e.length>0&&\"role\"in e[0]&&e[0].role===\"system\"&&(e=e.slice(1));let n=t.messages||[],r=new Set(n.map(o=>o.id)),c=new Set(e.map(o=>o.id)),h=[];e.length<n.length&&(h=n.filter(o=>!c.has(o.id)).map(o=>new _langchain_core_messages__WEBPACK_IMPORTED_MODULE_2__.RemoveMessage({id:o.id})));let d=e.filter(o=>!r.has(o.id));return y(u({},t),{messages:[...h,...d],tools:[...(m=t.tools)!=null?m:[],...s]})}};\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@ag-ui+langgraph@0.0.4_openai@4.85.1_ws@8.18.0_zod@3.23.8__react@19.0.0/node_modules/@ag-ui/langgraph/dist/index.mjs\n");

/***/ })

};
;