/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@whatwg-node+fetch@0.9.21";
exports.ids = ["vendor-chunks/@whatwg-node+fetch@0.9.21"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/create-node-ponyfill.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/create-node-ponyfill.js ***!
  \*******************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const shouldSkipPonyfill = __webpack_require__(/*! ./shouldSkipPonyfill */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/shouldSkipPonyfill.js\");\n\nmodule.exports = function createNodePonyfill(opts = {}) {\n  const ponyfills = {};\n  \n  ponyfills.URLPattern = globalThis.URLPattern;\n\n  // We call this previously to patch `Bun`\n  if (!ponyfills.URLPattern) {\n    const urlPatternModule = __webpack_require__(/*! urlpattern-polyfill */ \"(rsc)/./node_modules/.pnpm/urlpattern-polyfill@10.0.0/node_modules/urlpattern-polyfill/index.cjs\");\n    ponyfills.URLPattern = urlPatternModule.URLPattern;\n  }\n\n  if (opts.skipPonyfill || shouldSkipPonyfill()) {\n    return {\n      fetch: globalThis.fetch,\n      Headers: globalThis.Headers,\n      Request: globalThis.Request,\n      Response: globalThis.Response,\n      FormData: globalThis.FormData,\n      ReadableStream: globalThis.ReadableStream,\n      WritableStream: globalThis.WritableStream,\n      TransformStream: globalThis.TransformStream,\n      CompressionStream: globalThis.CompressionStream,\n      DecompressionStream: globalThis.DecompressionStream,\n      Blob: globalThis.Blob,\n      File: globalThis.File,\n      crypto: globalThis.crypto,\n      btoa: globalThis.btoa,\n      TextEncoder: globalThis.TextEncoder,\n      TextDecoder: globalThis.TextDecoder,\n      URLPattern: ponyfills.URLPattern,\n      URL: globalThis.URL,\n      URLSearchParams: globalThis.URLSearchParams\n    };\n  }\n\n  const newNodeFetch = __webpack_require__(/*! @whatwg-node/node-fetch */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/index.js\");\n\n  ponyfills.fetch = newNodeFetch.fetch;\n  ponyfills.Request = newNodeFetch.Request;\n  ponyfills.Response = newNodeFetch.Response;\n  ponyfills.Headers = newNodeFetch.Headers;\n  ponyfills.FormData = newNodeFetch.FormData;\n  ponyfills.ReadableStream = newNodeFetch.ReadableStream;\n\n  ponyfills.URL = newNodeFetch.URL;\n  ponyfills.URLSearchParams = newNodeFetch.URLSearchParams;\n\n  ponyfills.WritableStream = newNodeFetch.WritableStream;\n  ponyfills.TransformStream = newNodeFetch.TransformStream;\n  ponyfills.CompressionStream = newNodeFetch.CompressionStream;\n  ponyfills.DecompressionStream = newNodeFetch.DecompressionStream;\n\n  ponyfills.Blob = newNodeFetch.Blob;\n  ponyfills.File = newNodeFetch.File;\n  ponyfills.crypto = globalThis.crypto;\n  ponyfills.btoa = newNodeFetch.btoa;\n  ponyfills.TextEncoder = newNodeFetch.TextEncoder;\n  ponyfills.TextDecoder = newNodeFetch.TextDecoder;\n\n  if (opts.formDataLimits) {\n    ponyfills.Body = class Body extends newNodeFetch.Body {\n      constructor(body, userOpts) {\n        super(body, {\n          formDataLimits: opts.formDataLimits,\n          ...userOpts,\n        });\n      }\n    }\n    ponyfills.Request = class Request extends newNodeFetch.Request {\n      constructor(input, userOpts) {\n        super(input, {\n          formDataLimits: opts.formDataLimits,\n          ...userOpts,\n        });\n      }\n    }\n    ponyfills.Response = class Response extends newNodeFetch.Response {\n      constructor(body, userOpts) {\n        super(body, {\n          formDataLimits: opts.formDataLimits,\n          ...userOpts,\n        });\n      }\n    }\n  }\n\n  if (!ponyfills.crypto) {\n    const cryptoModule = __webpack_require__(/*! crypto */ \"crypto\");\n    ponyfills.crypto = cryptoModule.webcrypto;\n  }\n\n  return ponyfills;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/create-node-ponyfill.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/node-ponyfill.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/node-ponyfill.js ***!
  \************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nconst createNodePonyfill = __webpack_require__(/*! ./create-node-ponyfill */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/create-node-ponyfill.js\");\nconst shouldSkipPonyfill = __webpack_require__(/*! ./shouldSkipPonyfill */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/shouldSkipPonyfill.js\");\nconst ponyfills = createNodePonyfill();\n\nif (!shouldSkipPonyfill()) {\n  try {\n    const nodelibcurlName = 'node-libcurl'\n    globalThis.libcurl = globalThis.libcurl || __webpack_require__(\"(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist sync recursive\")(nodelibcurlName);\n  } catch (e) { }\n}\n\nmodule.exports.fetch = ponyfills.fetch;\nmodule.exports.Headers = ponyfills.Headers;\nmodule.exports.Request = ponyfills.Request;\nmodule.exports.Response = ponyfills.Response;\nmodule.exports.FormData = ponyfills.FormData;\nmodule.exports.ReadableStream = ponyfills.ReadableStream;\nmodule.exports.WritableStream = ponyfills.WritableStream;\nmodule.exports.TransformStream = ponyfills.TransformStream;\nmodule.exports.CompressionStream = ponyfills.CompressionStream;\nmodule.exports.DecompressionStream = ponyfills.DecompressionStream;\nmodule.exports.Blob = ponyfills.Blob;\nmodule.exports.File = ponyfills.File;\nmodule.exports.crypto = ponyfills.crypto;\nmodule.exports.btoa = ponyfills.btoa;\nmodule.exports.TextEncoder = ponyfills.TextEncoder;\nmodule.exports.TextDecoder = ponyfills.TextDecoder;\nmodule.exports.URLPattern = ponyfills.URLPattern;\nmodule.exports.URL = ponyfills.URL;\nmodule.exports.URLSearchParams = ponyfills.URLSearchParams;\n\nexports.createFetch = createNodePonyfill;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/node-ponyfill.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/shouldSkipPonyfill.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/shouldSkipPonyfill.js ***!
  \*****************************************************************************************************************/
/***/ ((module) => {

eval("\nfunction isNextJs() {\n  return Object.keys(globalThis).some(key => key.startsWith('__NEXT'))\n}\n\nmodule.exports = function shouldSkipPonyfill() {\n  // Bun and Deno already have a Fetch API\n  if (globalThis.Deno) {\n    return true\n  }\n  if (process.versions.bun) {\n    return true\n  }\n  if (isNextJs()) {\n    return true\n  }\n  return false\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHdoYXR3Zy1ub2RlK2ZldGNoQDAuOS4yMS9ub2RlX21vZHVsZXMvQHdoYXR3Zy1ub2RlL2ZldGNoL2Rpc3Qvc2hvdWxkU2tpcFBvbnlmaWxsLmpzIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ad2hhdHdnLW5vZGUrZmV0Y2hAMC45LjIxL25vZGVfbW9kdWxlcy9Ad2hhdHdnLW5vZGUvZmV0Y2gvZGlzdC9zaG91bGRTa2lwUG9ueWZpbGwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXG5mdW5jdGlvbiBpc05leHRKcygpIHtcbiAgcmV0dXJuIE9iamVjdC5rZXlzKGdsb2JhbFRoaXMpLnNvbWUoa2V5ID0+IGtleS5zdGFydHNXaXRoKCdfX05FWFQnKSlcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBzaG91bGRTa2lwUG9ueWZpbGwoKSB7XG4gIC8vIEJ1biBhbmQgRGVubyBhbHJlYWR5IGhhdmUgYSBGZXRjaCBBUElcbiAgaWYgKGdsb2JhbFRoaXMuRGVubykge1xuICAgIHJldHVybiB0cnVlXG4gIH1cbiAgaWYgKHByb2Nlc3MudmVyc2lvbnMuYnVuKSB7XG4gICAgcmV0dXJuIHRydWVcbiAgfVxuICBpZiAoaXNOZXh0SnMoKSkge1xuICAgIHJldHVybiB0cnVlXG4gIH1cbiAgcmV0dXJuIGZhbHNlXG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/shouldSkipPonyfill.js\n");

/***/ })

};
;