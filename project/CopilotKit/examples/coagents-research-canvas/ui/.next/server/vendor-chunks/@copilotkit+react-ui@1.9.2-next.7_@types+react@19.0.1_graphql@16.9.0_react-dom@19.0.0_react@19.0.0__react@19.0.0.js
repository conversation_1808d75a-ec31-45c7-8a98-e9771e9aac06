"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0";
exports.ids = ["vendor-chunks/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/index.css":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/index.css ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2a5b7752ab7c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtdWlAMS45LjItbmV4dC43X0B0eXBlcytyZWFjdEAxOS4wLjFfZ3JhcGhxbEAxNi45LjBfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvQGNvcGlsb3RraXQvcmVhY3QtdWkvZGlzdC9pbmRleC5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AY29waWxvdGtpdCtyZWFjdC11aUAxLjkuMi1uZXh0LjdfQHR5cGVzK3JlYWN0QDE5LjAuMV9ncmFwaHFsQDE2LjkuMF9yZWFjdC1kb21AMTkuMC4wX3JlYWN0QDE5LjAuMF9fcmVhY3RAMTkuMC4wL25vZGVfbW9kdWxlcy9AY29waWxvdGtpdC9yZWFjdC11aS9kaXN0L2luZGV4LmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjJhNWI3NzUyYWI3Y1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/index.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-2II3Q27P.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-2II3Q27P.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RenderAgentStateMessage: () => (/* binding */ RenderAgentStateMessage)\n/* harmony export */ });\n/* harmony import */ var _chunk_L3GZ7TXC_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-L3GZ7TXC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-L3GZ7TXC.mjs\");\n/* harmony import */ var _chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-MRXNTQOX.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-MRXNTQOX.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n// src/components/chat/messages/RenderAgentStateMessage.tsx\n\n\nfunction RenderAgentStateMessage(_a) {\n  var _b = _a, {\n    AssistantMessage: AssistantMessage2 = _chunk_L3GZ7TXC_mjs__WEBPACK_IMPORTED_MODULE_1__.AssistantMessage\n  } = _b, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_b, [\n    \"AssistantMessage\"\n  ]);\n  const { chatComponentsCache } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__.useCopilotContext)();\n  const { message, inProgress, index, isCurrentMessage } = props;\n  if (message.isAgentStateMessage()) {\n    let render;\n    if (chatComponentsCache.current !== null) {\n      render = chatComponentsCache.current.coAgentStateRenders[`${message.agentName}-${message.nodeName}`] || chatComponentsCache.current.coAgentStateRenders[`${message.agentName}-global`];\n    }\n    if (render) {\n      if (typeof render === \"string\") {\n        if (isCurrentMessage && inProgress) {\n          return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n            AssistantMessage2,\n            {\n              rawData: message,\n              message: render,\n              \"data-message-role\": \"assistant\",\n              isLoading: true,\n              isGenerating: true\n            },\n            index\n          );\n        } else {\n          return null;\n        }\n      } else {\n        const state = message.state;\n        let status = message.active ? \"inProgress\" : \"complete\";\n        const toRender = render({\n          status,\n          state,\n          nodeName: message.nodeName\n        });\n        if (!toRender && status === \"complete\") {\n          return null;\n        }\n        if (!toRender && isCurrentMessage && inProgress) {\n          return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n            AssistantMessage2,\n            {\n              \"data-message-role\": \"assistant\",\n              rawData: message,\n              isLoading: true,\n              isGenerating: true\n            },\n            index\n          );\n        } else if (!toRender) {\n          return null;\n        }\n        if (typeof toRender === \"string\") {\n          return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n            AssistantMessage2,\n            {\n              rawData: message,\n              message: toRender,\n              isLoading: true,\n              isGenerating: true,\n              \"data-message-role\": \"assistant\"\n            },\n            index\n          );\n        } else {\n          return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n            AssistantMessage2,\n            {\n              rawData: message,\n              \"data-message-role\": \"agent-state-render\",\n              isLoading: false,\n              isGenerating: false,\n              subComponent: toRender\n            },\n            index\n          );\n        }\n      }\n    } else if (!inProgress || !isCurrentMessage) {\n      return null;\n    } else {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n        AssistantMessage2,\n        {\n          rawData: message,\n          isLoading: true,\n          isGenerating: true,\n          \"data-message-role\": \"assistant\"\n        },\n        index\n      );\n    }\n  }\n}\n\n\n//# sourceMappingURL=chunk-2II3Q27P.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-2II3Q27P.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-32MUWKL3.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-32MUWKL3.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RenderTextMessage: () => (/* binding */ RenderTextMessage)\n/* harmony export */ });\n/* harmony import */ var _chunk_L3GZ7TXC_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-L3GZ7TXC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-L3GZ7TXC.mjs\");\n/* harmony import */ var _chunk_HWMFMBJC_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-HWMFMBJC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-HWMFMBJC.mjs\");\n/* harmony import */ var _chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-MRXNTQOX.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-MRXNTQOX.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n// src/components/chat/messages/RenderTextMessage.tsx\n\nfunction RenderTextMessage(_a) {\n  var _b = _a, {\n    UserMessage: UserMessage2 = _chunk_HWMFMBJC_mjs__WEBPACK_IMPORTED_MODULE_1__.UserMessage,\n    AssistantMessage: AssistantMessage2 = _chunk_L3GZ7TXC_mjs__WEBPACK_IMPORTED_MODULE_2__.AssistantMessage\n  } = _b, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_3__.__objRest)(_b, [\n    \"UserMessage\",\n    \"AssistantMessage\"\n  ]);\n  const {\n    message,\n    inProgress,\n    index,\n    isCurrentMessage,\n    onRegenerate,\n    onCopy,\n    onThumbsUp,\n    onThumbsDown,\n    markdownTagRenderers\n  } = props;\n  if (message.isTextMessage()) {\n    if (message.role === \"user\") {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n        UserMessage2,\n        {\n          \"data-message-role\": \"user\",\n          message: message.content,\n          rawData: message\n        },\n        index\n      );\n    } else if (message.role == \"assistant\") {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n        AssistantMessage2,\n        {\n          \"data-message-role\": \"assistant\",\n          message: message.content,\n          rawData: message,\n          isLoading: inProgress && isCurrentMessage && !message.content,\n          isGenerating: inProgress && isCurrentMessage && !!message.content,\n          isCurrentMessage,\n          onRegenerate: () => onRegenerate == null ? void 0 : onRegenerate(message.id),\n          onCopy,\n          onThumbsUp,\n          onThumbsDown,\n          markdownTagRenderers\n        },\n        index\n      );\n    }\n  }\n}\n\n\n//# sourceMappingURL=chunk-32MUWKL3.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-32MUWKL3.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-54JAUBUJ.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-54JAUBUJ.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCopyToClipboard: () => (/* binding */ useCopyToClipboard)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/hooks/use-copy-to-clipboard.tsx\n\nfunction useCopyToClipboard({ timeout = 2e3 }) {\n  const [isCopied, setIsCopied] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const copyToClipboard = (value) => {\n    var _a;\n    if (typeof window === \"undefined\" || !((_a = navigator.clipboard) == null ? void 0 : _a.writeText)) {\n      return;\n    }\n    if (!value) {\n      return;\n    }\n    navigator.clipboard.writeText(value).then(() => {\n      setIsCopied(true);\n      setTimeout(() => {\n        setIsCopied(false);\n      }, timeout);\n    });\n  };\n  return { isCopied, copyToClipboard };\n}\n\n\n//# sourceMappingURL=chunk-54JAUBUJ.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtdWlAMS45LjItbmV4dC43X0B0eXBlcytyZWFjdEAxOS4wLjFfZ3JhcGhxbEAxNi45LjBfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvQGNvcGlsb3RraXQvcmVhY3QtdWkvZGlzdC9jaHVuay01NEpBVUJVSi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQiw4QkFBOEIsZUFBZTtBQUM3QyxrQ0FBa0MsMkNBQWM7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBLFdBQVc7QUFDWDs7QUFJRTtBQUNGIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtdWlAMS45LjItbmV4dC43X0B0eXBlcytyZWFjdEAxOS4wLjFfZ3JhcGhxbEAxNi45LjBfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvQGNvcGlsb3RraXQvcmVhY3QtdWkvZGlzdC9jaHVuay01NEpBVUJVSi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2hvb2tzL3VzZS1jb3B5LXRvLWNsaXBib2FyZC50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlQ29weVRvQ2xpcGJvYXJkKHsgdGltZW91dCA9IDJlMyB9KSB7XG4gIGNvbnN0IFtpc0NvcGllZCwgc2V0SXNDb3BpZWRdID0gUmVhY3QudXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBjb3B5VG9DbGlwYm9hcmQgPSAodmFsdWUpID0+IHtcbiAgICB2YXIgX2E7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09IFwidW5kZWZpbmVkXCIgfHwgISgoX2EgPSBuYXZpZ2F0b3IuY2xpcGJvYXJkKSA9PSBudWxsID8gdm9pZCAwIDogX2Eud3JpdGVUZXh0KSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBpZiAoIXZhbHVlKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KHZhbHVlKS50aGVuKCgpID0+IHtcbiAgICAgIHNldElzQ29waWVkKHRydWUpO1xuICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHNldElzQ29waWVkKGZhbHNlKTtcbiAgICAgIH0sIHRpbWVvdXQpO1xuICAgIH0pO1xuICB9O1xuICByZXR1cm4geyBpc0NvcGllZCwgY29weVRvQ2xpcGJvYXJkIH07XG59XG5cbmV4cG9ydCB7XG4gIHVzZUNvcHlUb0NsaXBib2FyZFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNodW5rLTU0SkFVQlVKLm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-54JAUBUJ.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-B3D7U7TJ.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-B3D7U7TJ.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Messages: () => (/* binding */ Messages),\n/* harmony export */   useScrollToBottom: () => (/* binding */ useScrollToBottom)\n/* harmony export */ });\n/* harmony import */ var _chunk_IEMQ2SQW_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-IEMQ2SQW.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-IEMQ2SQW.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-ROUIRR4B.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-CMQV4XNY.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n// src/components/chat/Messages.tsx\n\n\n\n\nvar Messages = ({\n  messages,\n  inProgress,\n  children,\n  RenderTextMessage,\n  RenderActionExecutionMessage,\n  RenderAgentStateMessage,\n  RenderResultMessage,\n  RenderImageMessage,\n  AssistantMessage,\n  UserMessage,\n  onRegenerate,\n  onCopy,\n  onThumbsUp,\n  onThumbsDown,\n  markdownTagRenderers\n}) => {\n  const context = (0,_chunk_IEMQ2SQW_mjs__WEBPACK_IMPORTED_MODULE_2__.useChatContext)();\n  const initialMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => makeInitialMessages(context.labels.initial),\n    [context.labels.initial]\n  );\n  messages = [...initialMessages, ...messages];\n  const actionResults = {};\n  for (let i = 0; i < messages.length; i++) {\n    if (messages[i].isActionExecutionMessage()) {\n      const id = messages[i].id;\n      const resultMessage = messages.find(\n        (message) => message.isResultMessage() && message.actionExecutionId === id\n      );\n      if (resultMessage) {\n        actionResults[id] = _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_3__.ResultMessage.decodeResult(resultMessage.result || \"\");\n      }\n    }\n  }\n  const { messagesContainerRef, messagesEndRef } = useScrollToBottom(messages);\n  const interrupt = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__.useLangGraphInterruptRender)();\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { className: \"copilotKitMessages\", ref: messagesContainerRef, children: [\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { className: \"copilotKitMessagesContainer\", children: [\n      messages.map((message, index) => {\n        const isCurrentMessage = index === messages.length - 1;\n        if (message.isTextMessage()) {\n          return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n            RenderTextMessage,\n            {\n              message,\n              inProgress,\n              index,\n              isCurrentMessage,\n              AssistantMessage,\n              UserMessage,\n              onRegenerate,\n              onCopy,\n              onThumbsUp,\n              onThumbsDown,\n              markdownTagRenderers\n            },\n            index\n          );\n        } else if (message.isActionExecutionMessage()) {\n          return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n            RenderActionExecutionMessage,\n            {\n              message,\n              inProgress,\n              index,\n              isCurrentMessage,\n              actionResult: actionResults[message.id],\n              AssistantMessage,\n              UserMessage\n            },\n            index\n          );\n        } else if (message.isAgentStateMessage()) {\n          return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n            RenderAgentStateMessage,\n            {\n              message,\n              inProgress,\n              index,\n              isCurrentMessage,\n              AssistantMessage,\n              UserMessage\n            },\n            index\n          );\n        } else if (message.isResultMessage()) {\n          return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n            RenderResultMessage,\n            {\n              message,\n              inProgress,\n              index,\n              isCurrentMessage,\n              AssistantMessage,\n              UserMessage\n            },\n            index\n          );\n        } else if (message.isImageMessage && message.isImageMessage()) {\n          return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n            RenderImageMessage,\n            {\n              message,\n              inProgress,\n              index,\n              isCurrentMessage,\n              AssistantMessage,\n              UserMessage,\n              onRegenerate,\n              onCopy,\n              onThumbsUp,\n              onThumbsDown\n            },\n            index\n          );\n        }\n      }),\n      interrupt\n    ] }),\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"footer\", { className: \"copilotKitMessagesFooter\", ref: messagesEndRef, children })\n  ] });\n};\nfunction makeInitialMessages(initial) {\n  let initialArray = [];\n  if (initial) {\n    if (Array.isArray(initial)) {\n      initialArray.push(...initial);\n    } else {\n      initialArray.push(initial);\n    }\n  }\n  return initialArray.map(\n    (message) => new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_3__.TextMessage({\n      role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_3__.Role.Assistant,\n      content: message\n    })\n  );\n}\nfunction useScrollToBottom(messages) {\n  const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const messagesContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const isProgrammaticScrollRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const isUserScrollUpRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const scrollToBottom = () => {\n    if (messagesContainerRef.current && messagesEndRef.current) {\n      isProgrammaticScrollRef.current = true;\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  };\n  const handleScroll = () => {\n    if (isProgrammaticScrollRef.current) {\n      isProgrammaticScrollRef.current = false;\n      return;\n    }\n    if (messagesContainerRef.current) {\n      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;\n      isUserScrollUpRef.current = scrollTop + clientHeight < scrollHeight;\n    }\n  };\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const container = messagesContainerRef.current;\n    if (container) {\n      container.addEventListener(\"scroll\", handleScroll);\n    }\n    return () => {\n      if (container) {\n        container.removeEventListener(\"scroll\", handleScroll);\n      }\n    };\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const container = messagesContainerRef.current;\n    if (!container) {\n      return;\n    }\n    const mutationObserver = new MutationObserver(() => {\n      if (!isUserScrollUpRef.current) {\n        scrollToBottom();\n      }\n    });\n    mutationObserver.observe(container, {\n      childList: true,\n      subtree: true,\n      characterData: true\n    });\n    return () => {\n      mutationObserver.disconnect();\n    };\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    isUserScrollUpRef.current = false;\n    scrollToBottom();\n  }, [messages.filter((m) => m.isTextMessage() && m.role === _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_3__.Role.User).length]);\n  return { messagesEndRef, messagesContainerRef };\n}\n\n\n//# sourceMappingURL=chunk-B3D7U7TJ.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-B3D7U7TJ.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-C7OB63U5.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-C7OB63U5.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RenderResultMessage: () => (/* binding */ RenderResultMessage)\n/* harmony export */ });\n/* harmony import */ var _chunk_L3GZ7TXC_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-L3GZ7TXC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-L3GZ7TXC.mjs\");\n/* harmony import */ var _chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-MRXNTQOX.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-MRXNTQOX.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n// src/components/chat/messages/RenderResultMessage.tsx\n\nfunction RenderResultMessage(_a) {\n  var _b = _a, {\n    AssistantMessage: AssistantMessage2 = _chunk_L3GZ7TXC_mjs__WEBPACK_IMPORTED_MODULE_1__.AssistantMessage\n  } = _b, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_b, [\n    \"AssistantMessage\"\n  ]);\n  const { message, inProgress, index, isCurrentMessage } = props;\n  if (message.isResultMessage() && inProgress && isCurrentMessage) {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n      AssistantMessage2,\n      {\n        \"data-message-role\": \"assistant\",\n        rawData: message,\n        isLoading: true,\n        isGenerating: true\n      },\n      index\n    );\n  } else {\n    return null;\n  }\n}\n\n\n//# sourceMappingURL=chunk-C7OB63U5.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-C7OB63U5.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-CGEAG65D.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-CGEAG65D.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PoweredByTag: () => (/* binding */ PoweredByTag)\n/* harmony export */ });\n/* harmony import */ var _chunk_JGMFJZMG_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-JGMFJZMG.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-JGMFJZMG.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n// src/components/chat/PoweredByTag.tsx\n\nfunction PoweredByTag({ showPoweredBy = true }) {\n  const isDark = (0,_chunk_JGMFJZMG_mjs__WEBPACK_IMPORTED_MODULE_1__.useDarkMode)();\n  if (!showPoweredBy) {\n    return null;\n  }\n  const poweredByStyle = {\n    visibility: \"visible\",\n    display: \"block\",\n    position: \"static\",\n    textAlign: \"center\",\n    fontSize: \"12px\",\n    padding: \"3px 0\",\n    color: isDark ? \"rgb(69, 69, 69)\" : \"rgb(214, 214, 214)\"\n  };\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", { className: \"poweredBy\", style: poweredByStyle, children: \"Powered by CopilotKit\" }) });\n}\n\n\n//# sourceMappingURL=chunk-CGEAG65D.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-CGEAG65D.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-HWMFMBJC.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-HWMFMBJC.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserMessage: () => (/* binding */ UserMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/components/chat/messages/UserMessage.tsx\n\nvar UserMessage = (props) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: \"copilotKitMessage copilotKitUserMessage\", children: props.subComponent || props.message });\n};\n\n\n//# sourceMappingURL=chunk-HWMFMBJC.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtdWlAMS45LjItbmV4dC43X0B0eXBlcytyZWFjdEAxOS4wLjFfZ3JhcGhxbEAxNi45LjBfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvQGNvcGlsb3RraXQvcmVhY3QtdWkvZGlzdC9jaHVuay1IV01GTUJKQy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUN3QztBQUN4QztBQUNBLHlCQUF5QixzREFBRyxVQUFVLHFHQUFxRztBQUMzSTs7QUFJRTtBQUNGIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtdWlAMS45LjItbmV4dC43X0B0eXBlcytyZWFjdEAxOS4wLjFfZ3JhcGhxbEAxNi45LjBfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvQGNvcGlsb3RraXQvcmVhY3QtdWkvZGlzdC9jaHVuay1IV01GTUJKQy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2NvbXBvbmVudHMvY2hhdC9tZXNzYWdlcy9Vc2VyTWVzc2FnZS50c3hcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIFVzZXJNZXNzYWdlID0gKHByb3BzKSA9PiB7XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFwiZGl2XCIsIHsgY2xhc3NOYW1lOiBcImNvcGlsb3RLaXRNZXNzYWdlIGNvcGlsb3RLaXRVc2VyTWVzc2FnZVwiLCBjaGlsZHJlbjogcHJvcHMuc3ViQ29tcG9uZW50IHx8IHByb3BzLm1lc3NhZ2UgfSk7XG59O1xuXG5leHBvcnQge1xuICBVc2VyTWVzc2FnZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNodW5rLUhXTUZNQkpDLm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-HWMFMBJC.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-IEMQ2SQW.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-IEMQ2SQW.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatContext: () => (/* binding */ ChatContext),\n/* harmony export */   ChatContextProvider: () => (/* binding */ ChatContextProvider),\n/* harmony export */   useChatContext: () => (/* binding */ useChatContext)\n/* harmony export */ });\n/* harmony import */ var _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-XWG3L6QC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-XWG3L6QC.mjs\");\n/* harmony import */ var _chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-MRXNTQOX.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-MRXNTQOX.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n// src/components/chat/ChatContext.tsx\n\n\nvar ChatContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nfunction useChatContext() {\n  const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(ChatContext);\n  if (context === void 0) {\n    throw new Error(\n      \"Context not found. Did you forget to wrap your app in a <ChatContextProvider> component?\"\n    );\n  }\n  return context;\n}\nvar ChatContextProvider = ({\n  // temperature,\n  // instructions,\n  // maxFeedback,\n  labels,\n  icons,\n  children,\n  open,\n  setOpen\n}) => {\n  const memoizedLabels = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({}, {\n      initial: \"\",\n      title: \"CopilotKit\",\n      placeholder: \"Type a message...\",\n      error: \"\\u274C An error occurred. Please try again.\",\n      stopGenerating: \"Stop generating\",\n      regenerateResponse: \"Regenerate response\",\n      copyToClipboard: \"Copy to clipboard\",\n      thumbsUp: \"Thumbs up\",\n      thumbsDown: \"Thumbs down\",\n      copied: \"Copied!\"\n    }), labels),\n    [labels]\n  );\n  const memoizedIcons = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({}, {\n      openIcon: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.OpenIcon,\n      closeIcon: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.CloseIcon,\n      headerCloseIcon: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.HeaderCloseIcon,\n      sendIcon: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.SendIcon,\n      activityIcon: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.ActivityIcon,\n      spinnerIcon: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.SpinnerIcon,\n      stopIcon: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.StopIcon,\n      regenerateIcon: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.RegenerateIcon,\n      pushToTalkIcon: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.MicrophoneIcon,\n      copyIcon: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.CopyIcon,\n      thumbsUpIcon: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.ThumbsUpIcon,\n      thumbsDownIcon: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.ThumbsDownIcon,\n      uploadIcon: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.UploadIcon\n    }), icons),\n    [icons]\n  );\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      labels: memoizedLabels,\n      icons: memoizedIcons,\n      open,\n      setOpen\n    }),\n    [memoizedLabels, memoizedIcons, open, setOpen]\n  );\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ChatContext.Provider, { value: context, children });\n};\n\n\n//# sourceMappingURL=chunk-IEMQ2SQW.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-IEMQ2SQW.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-IMBPSLL4.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-IMBPSLL4.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Suggestion: () => (/* binding */ Suggestion),\n/* harmony export */   reloadSuggestions: () => (/* binding */ reloadSuggestions)\n/* harmony export */ });\n/* harmony import */ var _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-XWG3L6QC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-XWG3L6QC.mjs\");\n/* harmony import */ var _chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-MRXNTQOX.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-MRXNTQOX.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-RN3ZRHI7.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-2KQ6HEWZ.mjs\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-WM3ARNBD.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n// src/components/chat/Suggestion.tsx\n\n\n\n\nfunction Suggestion({ title, onClick, partial, className }) {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n    \"button\",\n    {\n      disabled: partial,\n      onClick: (e) => {\n        e.preventDefault();\n        onClick();\n      },\n      className: className || (partial ? \"suggestion loading\" : \"suggestion\"),\n      \"data-test-id\": \"suggestion\",\n      children: partial ? _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_1__.SmallSpinnerIcon : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { children: title })\n    }\n  );\n}\nvar reloadSuggestions = (context, chatSuggestionConfiguration, setCurrentSuggestions, abortControllerRef) => (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__async)(void 0, null, function* () {\n  const abortController = abortControllerRef.current;\n  const tools = JSON.stringify(\n    Object.values(context.actions).map((action) => ({\n      name: action.name,\n      description: action.description,\n      jsonSchema: JSON.stringify((0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_3__.actionParametersToJsonSchema)(action.parameters))\n    }))\n  );\n  const allSuggestions = [];\n  for (const config of Object.values(chatSuggestionConfiguration)) {\n    try {\n      const numOfSuggestionsInstructions = config.minSuggestions === 0 ? `Produce up to ${config.maxSuggestions} suggestions. If there are no highly relevant suggestions you can think of, provide an empty array.` : `Produce between ${config.minSuggestions} and ${config.maxSuggestions} suggestions.`;\n      const result = yield (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_4__.extract)({\n        context,\n        instructions: \"Suggest what the user could say next. Provide clear, highly relevant suggestions. Do not literally suggest function calls. \",\n        data: config.instructions + \"\\n\\n\" + numOfSuggestionsInstructions + \"\\n\\nAvailable tools: \" + tools + \"\\n\\n\",\n        requestType: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_5__.CopilotRequestType.Task,\n        parameters: [\n          {\n            name: \"suggestions\",\n            type: \"object[]\",\n            attributes: [\n              {\n                name: \"title\",\n                description: \"The title of the suggestion. This is shown as a button and should be short.\",\n                type: \"string\"\n              },\n              {\n                name: \"message\",\n                description: \"The message to send when the suggestion is clicked. This should be a clear, complete sentence and will be sent as an instruction to the AI.\",\n                type: \"string\"\n              }\n            ]\n          }\n        ],\n        include: {\n          messages: true,\n          readable: true\n        },\n        abortSignal: abortController == null ? void 0 : abortController.signal,\n        stream: ({ status, args }) => {\n          const suggestions = args.suggestions || [];\n          const newSuggestions = [];\n          for (let i = 0; i < suggestions.length; i++) {\n            if (config.maxSuggestions !== void 0 && i >= config.maxSuggestions) {\n              break;\n            }\n            const { title, message } = suggestions[i];\n            const partial = i == suggestions.length - 1 && status !== \"complete\";\n            newSuggestions.push({\n              title,\n              message,\n              partial,\n              className: config.className\n            });\n          }\n          setCurrentSuggestions([...allSuggestions, ...newSuggestions]);\n        }\n      });\n      allSuggestions.push(...result.suggestions);\n    } catch (error) {\n      console.error(\"Error loading suggestions\", error);\n    }\n  }\n  if (abortControllerRef.current === abortController) {\n    abortControllerRef.current = null;\n  }\n});\n\n\n//# sourceMappingURL=chunk-IMBPSLL4.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-IMBPSLL4.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-JGMFJZMG.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-JGMFJZMG.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDarkMode: () => (/* binding */ useDarkMode)\n/* harmony export */ });\n// src/hooks/use-dark-mode.ts\nvar useDarkMode = () => {\n  if (typeof window === \"undefined\")\n    return false;\n  return document.documentElement.classList.contains(\"dark\") || document.body.classList.contains(\"dark\") || document.documentElement.getAttribute(\"data-theme\") === \"dark\" || document.body.getAttribute(\"data-theme\") === \"dark\" || window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n};\n\n\n//# sourceMappingURL=chunk-JGMFJZMG.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtdWlAMS45LjItbmV4dC43X0B0eXBlcytyZWFjdEAxOS4wLjFfZ3JhcGhxbEAxNi45LjBfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvQGNvcGlsb3RraXQvcmVhY3QtdWkvZGlzdC9jaHVuay1KR01GSlpNRy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFJRTtBQUNGIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtdWlAMS45LjItbmV4dC43X0B0eXBlcytyZWFjdEAxOS4wLjFfZ3JhcGhxbEAxNi45LjBfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvQGNvcGlsb3RraXQvcmVhY3QtdWkvZGlzdC9jaHVuay1KR01GSlpNRy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2hvb2tzL3VzZS1kYXJrLW1vZGUudHNcbnZhciB1c2VEYXJrTW9kZSA9ICgpID0+IHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09IFwidW5kZWZpbmVkXCIpXG4gICAgcmV0dXJuIGZhbHNlO1xuICByZXR1cm4gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5jb250YWlucyhcImRhcmtcIikgfHwgZG9jdW1lbnQuYm9keS5jbGFzc0xpc3QuY29udGFpbnMoXCJkYXJrXCIpIHx8IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5nZXRBdHRyaWJ1dGUoXCJkYXRhLXRoZW1lXCIpID09PSBcImRhcmtcIiB8fCBkb2N1bWVudC5ib2R5LmdldEF0dHJpYnV0ZShcImRhdGEtdGhlbWVcIikgPT09IFwiZGFya1wiIHx8IHdpbmRvdy5tYXRjaE1lZGlhKFwiKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKVwiKS5tYXRjaGVzO1xufTtcblxuZXhwb3J0IHtcbiAgdXNlRGFya01vZGVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jaHVuay1KR01GSlpNRy5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-JGMFJZMG.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-KENCH7RN.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-KENCH7RN.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CodeBlock: () => (/* binding */ CodeBlock),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   programmingLanguages: () => (/* binding */ programmingLanguages)\n/* harmony export */ });\n/* harmony import */ var _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-XWG3L6QC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-XWG3L6QC.mjs\");\n/* harmony import */ var _chunk_54JAUBUJ_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-54JAUBUJ.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-54JAUBUJ.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-syntax-highlighter */ \"(ssr)/./node_modules/.pnpm/react-syntax-highlighter@15.6.1_react@19.0.0/node_modules/react-syntax-highlighter/dist/esm/prism.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n// src/components/chat/CodeBlock.tsx\n\n\n\nvar programmingLanguages = {\n  javascript: \".js\",\n  python: \".py\",\n  java: \".java\",\n  c: \".c\",\n  cpp: \".cpp\",\n  \"c++\": \".cpp\",\n  \"c#\": \".cs\",\n  ruby: \".rb\",\n  php: \".php\",\n  swift: \".swift\",\n  \"objective-c\": \".m\",\n  kotlin: \".kt\",\n  typescript: \".ts\",\n  go: \".go\",\n  perl: \".pl\",\n  rust: \".rs\",\n  scala: \".scala\",\n  haskell: \".hs\",\n  lua: \".lua\",\n  shell: \".sh\",\n  sql: \".sql\",\n  html: \".html\",\n  css: \".css\"\n  // add more file extensions here, make sure the key is same as language prop in CodeBlock.tsx component\n};\nvar generateRandomString = (length, lowercase = false) => {\n  const chars = \"ABCDEFGHJKLMNPQRSTUVWXY3456789\";\n  let result = \"\";\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return lowercase ? result.toLowerCase() : result;\n};\nvar CodeBlock = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(({ language, value }) => {\n  const { isCopied, copyToClipboard } = (0,_chunk_54JAUBUJ_mjs__WEBPACK_IMPORTED_MODULE_2__.useCopyToClipboard)({ timeout: 2e3 });\n  const downloadAsFile = () => {\n    if (typeof window === \"undefined\") {\n      return;\n    }\n    const fileExtension = programmingLanguages[language] || \".file\";\n    const suggestedFileName = `file-${generateRandomString(3, true)}${fileExtension}`;\n    const fileName = window.prompt(\"Enter file name\", suggestedFileName);\n    if (!fileName) {\n      return;\n    }\n    const blob = new Blob([value], { type: \"text/plain\" });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.download = fileName;\n    link.href = url;\n    link.style.display = \"none\";\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n  const onCopy = () => {\n    if (isCopied)\n      return;\n    copyToClipboard(value);\n  };\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { className: \"copilotKitCodeBlock\", children: [\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { className: \"copilotKitCodeBlockToolbar\", children: [\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", { className: \"copilotKitCodeBlockToolbarLanguage\", children: language }),\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { className: \"copilotKitCodeBlockToolbarButtons\", children: [\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"button\", { className: \"copilotKitCodeBlockToolbarButton\", onClick: downloadAsFile, children: _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.DownloadIcon }),\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"button\", { className: \"copilotKitCodeBlockToolbarButton\", onClick: onCopy, children: isCopied ? _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.CheckIcon : _chunk_XWG3L6QC_mjs__WEBPACK_IMPORTED_MODULE_3__.CopyIcon })\n      ] })\n    ] }),\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n      {\n        language,\n        style: highlightStyle,\n        PreTag: \"div\",\n        customStyle: {\n          margin: 0,\n          borderBottomLeftRadius: \"0.375rem\",\n          borderBottomRightRadius: \"0.375rem\"\n        },\n        children: value\n      }\n    )\n  ] });\n});\nCodeBlock.displayName = \"CodeBlock\";\nvar highlightStyle = {\n  'pre[class*=\"language-\"]': {\n    color: \"#d4d4d4\",\n    fontSize: \"13px\",\n    textShadow: \"none\",\n    fontFamily: 'Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace',\n    direction: \"ltr\",\n    textAlign: \"left\",\n    whiteSpace: \"pre\",\n    wordSpacing: \"normal\",\n    wordBreak: \"normal\",\n    lineHeight: \"1.5\",\n    MozTabSize: \"4\",\n    OTabSize: \"4\",\n    tabSize: \"4\",\n    WebkitHyphens: \"none\",\n    MozHyphens: \"none\",\n    msHyphens: \"none\",\n    hyphens: \"none\",\n    padding: \"1em\",\n    margin: \".5em 0\",\n    overflow: \"auto\",\n    background: \"#1e1e1e\"\n  },\n  'code[class*=\"language-\"]': {\n    color: \"#d4d4d4\",\n    fontSize: \"13px\",\n    textShadow: \"none\",\n    fontFamily: 'Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace',\n    direction: \"ltr\",\n    textAlign: \"left\",\n    whiteSpace: \"pre\",\n    wordSpacing: \"normal\",\n    wordBreak: \"normal\",\n    lineHeight: \"1.5\",\n    MozTabSize: \"4\",\n    OTabSize: \"4\",\n    tabSize: \"4\",\n    WebkitHyphens: \"none\",\n    MozHyphens: \"none\",\n    msHyphens: \"none\",\n    hyphens: \"none\"\n  },\n  'pre[class*=\"language-\"]::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\"\n  },\n  'code[class*=\"language-\"]::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\"\n  },\n  'pre[class*=\"language-\"] *::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\"\n  },\n  'code[class*=\"language-\"] *::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\"\n  },\n  ':not(pre) > code[class*=\"language-\"]': {\n    padding: \".1em .3em\",\n    borderRadius: \".3em\",\n    color: \"#db4c69\",\n    background: \"#1e1e1e\"\n  },\n  \".namespace\": {\n    Opacity: \".7\"\n  },\n  \"doctype.doctype-tag\": {\n    color: \"#569CD6\"\n  },\n  \"doctype.name\": {\n    color: \"#9cdcfe\"\n  },\n  comment: {\n    color: \"#6a9955\"\n  },\n  prolog: {\n    color: \"#6a9955\"\n  },\n  punctuation: {\n    color: \"#d4d4d4\"\n  },\n  \".language-html .language-css .token.punctuation\": {\n    color: \"#d4d4d4\"\n  },\n  \".language-html .language-javascript .token.punctuation\": {\n    color: \"#d4d4d4\"\n  },\n  property: {\n    color: \"#9cdcfe\"\n  },\n  tag: {\n    color: \"#569cd6\"\n  },\n  boolean: {\n    color: \"#569cd6\"\n  },\n  number: {\n    color: \"#b5cea8\"\n  },\n  constant: {\n    color: \"#9cdcfe\"\n  },\n  symbol: {\n    color: \"#b5cea8\"\n  },\n  inserted: {\n    color: \"#b5cea8\"\n  },\n  unit: {\n    color: \"#b5cea8\"\n  },\n  selector: {\n    color: \"#d7ba7d\"\n  },\n  \"attr-name\": {\n    color: \"#9cdcfe\"\n  },\n  string: {\n    color: \"#ce9178\"\n  },\n  char: {\n    color: \"#ce9178\"\n  },\n  builtin: {\n    color: \"#ce9178\"\n  },\n  deleted: {\n    color: \"#ce9178\"\n  },\n  \".language-css .token.string.url\": {\n    textDecoration: \"underline\"\n  },\n  operator: {\n    color: \"#d4d4d4\"\n  },\n  entity: {\n    color: \"#569cd6\"\n  },\n  \"operator.arrow\": {\n    color: \"#569CD6\"\n  },\n  atrule: {\n    color: \"#ce9178\"\n  },\n  \"atrule.rule\": {\n    color: \"#c586c0\"\n  },\n  \"atrule.url\": {\n    color: \"#9cdcfe\"\n  },\n  \"atrule.url.function\": {\n    color: \"#dcdcaa\"\n  },\n  \"atrule.url.punctuation\": {\n    color: \"#d4d4d4\"\n  },\n  keyword: {\n    color: \"#569CD6\"\n  },\n  \"keyword.module\": {\n    color: \"#c586c0\"\n  },\n  \"keyword.control-flow\": {\n    color: \"#c586c0\"\n  },\n  function: {\n    color: \"#dcdcaa\"\n  },\n  \"function.maybe-class-name\": {\n    color: \"#dcdcaa\"\n  },\n  regex: {\n    color: \"#d16969\"\n  },\n  important: {\n    color: \"#569cd6\"\n  },\n  italic: {\n    fontStyle: \"italic\"\n  },\n  \"class-name\": {\n    color: \"#4ec9b0\"\n  },\n  \"maybe-class-name\": {\n    color: \"#4ec9b0\"\n  },\n  console: {\n    color: \"#9cdcfe\"\n  },\n  parameter: {\n    color: \"#9cdcfe\"\n  },\n  interpolation: {\n    color: \"#9cdcfe\"\n  },\n  \"punctuation.interpolation-punctuation\": {\n    color: \"#569cd6\"\n  },\n  variable: {\n    color: \"#9cdcfe\"\n  },\n  \"imports.maybe-class-name\": {\n    color: \"#9cdcfe\"\n  },\n  \"exports.maybe-class-name\": {\n    color: \"#9cdcfe\"\n  },\n  escape: {\n    color: \"#d7ba7d\"\n  },\n  \"tag.punctuation\": {\n    color: \"#808080\"\n  },\n  cdata: {\n    color: \"#808080\"\n  },\n  \"attr-value\": {\n    color: \"#ce9178\"\n  },\n  \"attr-value.punctuation\": {\n    color: \"#ce9178\"\n  },\n  \"attr-value.punctuation.attr-equals\": {\n    color: \"#d4d4d4\"\n  },\n  namespace: {\n    color: \"#4ec9b0\"\n  },\n  'pre[class*=\"language-javascript\"]': {\n    color: \"#9cdcfe\"\n  },\n  'code[class*=\"language-javascript\"]': {\n    color: \"#9cdcfe\"\n  },\n  'pre[class*=\"language-jsx\"]': {\n    color: \"#9cdcfe\"\n  },\n  'code[class*=\"language-jsx\"]': {\n    color: \"#9cdcfe\"\n  },\n  'pre[class*=\"language-typescript\"]': {\n    color: \"#9cdcfe\"\n  },\n  'code[class*=\"language-typescript\"]': {\n    color: \"#9cdcfe\"\n  },\n  'pre[class*=\"language-tsx\"]': {\n    color: \"#9cdcfe\"\n  },\n  'code[class*=\"language-tsx\"]': {\n    color: \"#9cdcfe\"\n  },\n  'pre[class*=\"language-css\"]': {\n    color: \"#ce9178\"\n  },\n  'code[class*=\"language-css\"]': {\n    color: \"#ce9178\"\n  },\n  'pre[class*=\"language-html\"]': {\n    color: \"#d4d4d4\"\n  },\n  'code[class*=\"language-html\"]': {\n    color: \"#d4d4d4\"\n  },\n  \".language-regex .token.anchor\": {\n    color: \"#dcdcaa\"\n  },\n  \".language-html .token.punctuation\": {\n    color: \"#808080\"\n  },\n  'pre[class*=\"language-\"] > code[class*=\"language-\"]': {\n    position: \"relative\",\n    zIndex: \"1\"\n  },\n  \".line-highlight.line-highlight\": {\n    background: \"#f7ebc6\",\n    boxShadow: \"inset 5px 0 0 #f7d87c\",\n    zIndex: \"0\"\n  }\n};\n\n\n//# sourceMappingURL=chunk-KENCH7RN.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-KENCH7RN.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-KQEMBE47.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-KQEMBE47.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CopilotChat: () => (/* binding */ CopilotChat),\n/* harmony export */   WrappedCopilotChat: () => (/* binding */ WrappedCopilotChat),\n/* harmony export */   useCopilotChatLogic: () => (/* binding */ useCopilotChatLogic)\n/* harmony export */ });\n/* harmony import */ var _chunk_UKCPOBQM_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-UKCPOBQM.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-UKCPOBQM.mjs\");\n/* harmony import */ var _chunk_2II3Q27P_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-2II3Q27P.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-2II3Q27P.mjs\");\n/* harmony import */ var _chunk_ULDQXCED_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./chunk-ULDQXCED.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-ULDQXCED.mjs\");\n/* harmony import */ var _chunk_C7OB63U5_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./chunk-C7OB63U5.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-C7OB63U5.mjs\");\n/* harmony import */ var _chunk_32MUWKL3_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-32MUWKL3.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-32MUWKL3.mjs\");\n/* harmony import */ var _chunk_L3GZ7TXC_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./chunk-L3GZ7TXC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-L3GZ7TXC.mjs\");\n/* harmony import */ var _chunk_HWMFMBJC_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./chunk-HWMFMBJC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-HWMFMBJC.mjs\");\n/* harmony import */ var _chunk_QGSPTXOV_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./chunk-QGSPTXOV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-QGSPTXOV.mjs\");\n/* harmony import */ var _chunk_IMBPSLL4_mjs__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./chunk-IMBPSLL4.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-IMBPSLL4.mjs\");\n/* harmony import */ var _chunk_PLHTVHUW_mjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./chunk-PLHTVHUW.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-PLHTVHUW.mjs\");\n/* harmony import */ var _chunk_X3LV7OXQ_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./chunk-X3LV7OXQ.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-X3LV7OXQ.mjs\");\n/* harmony import */ var _chunk_B3D7U7TJ_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-B3D7U7TJ.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-B3D7U7TJ.mjs\");\n/* harmony import */ var _chunk_IEMQ2SQW_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./chunk-IEMQ2SQW.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-IEMQ2SQW.mjs\");\n/* harmony import */ var _chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./chunk-MRXNTQOX.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-MRXNTQOX.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-I4JPQECN.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-DCTJZ742.mjs\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-ROUIRR4B.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-VNNKZIFB.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-ZHEEHGLS.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// src/components/chat/Chat.tsx\n\n\n\n\n\n\nfunction CopilotChat({\n  instructions,\n  onSubmitMessage,\n  makeSystemMessage,\n  onInProgress,\n  onStopGeneration,\n  onReloadMessages,\n  onRegenerate,\n  onCopy,\n  onThumbsUp,\n  onThumbsDown,\n  markdownTagRenderers,\n  Messages: Messages2 = _chunk_B3D7U7TJ_mjs__WEBPACK_IMPORTED_MODULE_2__.Messages,\n  RenderTextMessage: RenderTextMessage2 = _chunk_32MUWKL3_mjs__WEBPACK_IMPORTED_MODULE_3__.RenderTextMessage,\n  RenderActionExecutionMessage: RenderActionExecutionMessage2 = _chunk_UKCPOBQM_mjs__WEBPACK_IMPORTED_MODULE_4__.RenderActionExecutionMessage,\n  RenderAgentStateMessage: RenderAgentStateMessage2 = _chunk_2II3Q27P_mjs__WEBPACK_IMPORTED_MODULE_5__.RenderAgentStateMessage,\n  RenderResultMessage: RenderResultMessage2 = _chunk_C7OB63U5_mjs__WEBPACK_IMPORTED_MODULE_6__.RenderResultMessage,\n  RenderImageMessage: RenderImageMessage2 = _chunk_ULDQXCED_mjs__WEBPACK_IMPORTED_MODULE_7__.RenderImageMessage,\n  RenderSuggestionsList = _chunk_QGSPTXOV_mjs__WEBPACK_IMPORTED_MODULE_8__.Suggestions,\n  Input: Input2 = _chunk_X3LV7OXQ_mjs__WEBPACK_IMPORTED_MODULE_9__.Input,\n  className,\n  icons,\n  labels,\n  AssistantMessage: AssistantMessage2 = _chunk_L3GZ7TXC_mjs__WEBPACK_IMPORTED_MODULE_10__.AssistantMessage,\n  UserMessage: UserMessage2 = _chunk_HWMFMBJC_mjs__WEBPACK_IMPORTED_MODULE_11__.UserMessage,\n  imageUploadsEnabled,\n  inputFileAccept = \"image/*\",\n  hideStopButton\n}) {\n  const { additionalInstructions, setChatInstructions } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_12__.useCopilotContext)();\n  const [selectedImages, setSelectedImages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!imageUploadsEnabled)\n      return;\n    const handlePaste = (e) => (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__async)(this, null, function* () {\n      var _a, _b;\n      const target = e.target;\n      if (!((_a = target.parentElement) == null ? void 0 : _a.classList.contains(\"copilotKitInput\")))\n        return;\n      const items = Array.from(((_b = e.clipboardData) == null ? void 0 : _b.items) || []);\n      const imageItems = items.filter((item) => item.type.startsWith(\"image/\"));\n      if (imageItems.length === 0)\n        return;\n      e.preventDefault();\n      const imagePromises = imageItems.map((item) => {\n        const file = item.getAsFile();\n        if (!file)\n          return Promise.resolve(null);\n        return new Promise((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = (e2) => {\n            var _a2, _b2;\n            const base64String = (_b2 = (_a2 = e2.target) == null ? void 0 : _a2.result) == null ? void 0 : _b2.split(\",\")[1];\n            if (base64String) {\n              resolve({\n                contentType: file.type,\n                bytes: base64String\n              });\n            } else {\n              resolve(null);\n            }\n          };\n          reader.onerror = reject;\n          reader.readAsDataURL(file);\n        });\n      });\n      try {\n        const loadedImages = (yield Promise.all(imagePromises)).filter((img) => img !== null);\n        setSelectedImages((prev) => [...prev, ...loadedImages]);\n      } catch (error) {\n        console.error(\"Error processing pasted images:\", error);\n      }\n    });\n    document.addEventListener(\"paste\", handlePaste);\n    return () => document.removeEventListener(\"paste\", handlePaste);\n  }, [imageUploadsEnabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!(additionalInstructions == null ? void 0 : additionalInstructions.length)) {\n      setChatInstructions(instructions || \"\");\n      return;\n    }\n    const combinedAdditionalInstructions = [\n      instructions,\n      \"Additionally, follow these instructions:\",\n      ...additionalInstructions.map((instruction) => `- ${instruction}`)\n    ];\n    console.log(\"combinedAdditionalInstructions\", combinedAdditionalInstructions);\n    setChatInstructions(combinedAdditionalInstructions.join(\"\\n\") || \"\");\n  }, [instructions, additionalInstructions]);\n  const {\n    visibleMessages,\n    isLoading,\n    currentSuggestions,\n    sendMessage,\n    stopGeneration,\n    reloadMessages\n  } = useCopilotChatLogic(\n    makeSystemMessage,\n    onInProgress,\n    onSubmitMessage,\n    onStopGeneration,\n    onReloadMessages\n  );\n  const handleSendMessage = (text) => {\n    const images = selectedImages;\n    setSelectedImages([]);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = \"\";\n    }\n    return sendMessage(text, images);\n  };\n  const chatContext = react__WEBPACK_IMPORTED_MODULE_0__.useContext(_chunk_IEMQ2SQW_mjs__WEBPACK_IMPORTED_MODULE_14__.ChatContext);\n  const isVisible = chatContext ? chatContext.open : true;\n  const handleRegenerate = (messageId) => {\n    if (onRegenerate) {\n      onRegenerate(messageId);\n    }\n    reloadMessages(messageId);\n  };\n  const handleCopy = (message) => {\n    if (onCopy) {\n      onCopy(message);\n    }\n  };\n  const handleImageUpload = (event) => (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__async)(this, null, function* () {\n    if (!event.target.files || event.target.files.length === 0) {\n      return;\n    }\n    const files = Array.from(event.target.files).filter((file) => file.type.startsWith(\"image/\"));\n    if (files.length === 0)\n      return;\n    const fileReadPromises = files.map((file) => {\n      return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          var _a, _b;\n          const base64String = ((_b = (_a = e.target) == null ? void 0 : _a.result) == null ? void 0 : _b.split(\",\")[1]) || \"\";\n          if (base64String) {\n            resolve({\n              contentType: file.type,\n              bytes: base64String\n            });\n          }\n        };\n        reader.onerror = reject;\n        reader.readAsDataURL(file);\n      });\n    });\n    try {\n      const loadedImages = yield Promise.all(fileReadPromises);\n      setSelectedImages((prev) => [...prev, ...loadedImages]);\n    } catch (error) {\n      console.error(\"Error reading files:\", error);\n    }\n  });\n  const removeSelectedImage = (index) => {\n    setSelectedImages((prev) => prev.filter((_, i) => i !== index));\n  };\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(WrappedCopilotChat, { icons, labels, className, children: [\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      Messages2,\n      {\n        AssistantMessage: AssistantMessage2,\n        UserMessage: UserMessage2,\n        RenderTextMessage: RenderTextMessage2,\n        RenderActionExecutionMessage: RenderActionExecutionMessage2,\n        RenderAgentStateMessage: RenderAgentStateMessage2,\n        RenderResultMessage: RenderResultMessage2,\n        RenderImageMessage: RenderImageMessage2,\n        messages: visibleMessages,\n        inProgress: isLoading,\n        onRegenerate: handleRegenerate,\n        onCopy: handleCopy,\n        onThumbsUp,\n        onThumbsDown,\n        markdownTagRenderers,\n        children: currentSuggestions.length > 0 && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          RenderSuggestionsList,\n          {\n            onSuggestionClick: handleSendMessage,\n            suggestions: currentSuggestions\n          }\n        )\n      }\n    ),\n    imageUploadsEnabled && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children: [\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_chunk_PLHTVHUW_mjs__WEBPACK_IMPORTED_MODULE_15__.ImageUploadQueue, { images: selectedImages, onRemoveImage: removeSelectedImage }),\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n        \"input\",\n        {\n          type: \"file\",\n          multiple: true,\n          ref: fileInputRef,\n          onChange: handleImageUpload,\n          accept: inputFileAccept,\n          style: { display: \"none\" }\n        }\n      )\n    ] }),\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      Input2,\n      {\n        inProgress: isLoading,\n        onSend: handleSendMessage,\n        isVisible,\n        onStop: stopGeneration,\n        onUpload: imageUploadsEnabled ? () => {\n          var _a;\n          return (_a = fileInputRef.current) == null ? void 0 : _a.click();\n        } : void 0,\n        hideStopButton\n      }\n    )\n  ] });\n}\nfunction WrappedCopilotChat({\n  children,\n  icons,\n  labels,\n  className\n}) {\n  const chatContext = react__WEBPACK_IMPORTED_MODULE_0__.useContext(_chunk_IEMQ2SQW_mjs__WEBPACK_IMPORTED_MODULE_14__.ChatContext);\n  if (!chatContext) {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_chunk_IEMQ2SQW_mjs__WEBPACK_IMPORTED_MODULE_14__.ChatContextProvider, { icons, labels, open: true, setOpen: () => {\n    }, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", { className: `copilotKitChat ${className != null ? className : \"\"}`, children }) });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n}\nvar SUGGESTIONS_DEBOUNCE_TIMEOUT = 1e3;\nvar useCopilotChatLogic = (makeSystemMessage, onInProgress, onSubmitMessage, onStopGeneration, onReloadMessages) => {\n  var _a;\n  const {\n    visibleMessages,\n    appendMessage,\n    reloadMessages: defaultReloadMessages,\n    stopGeneration: defaultStopGeneration,\n    runChatCompletion,\n    isLoading\n  } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_16__.useCopilotChat)({\n    id: (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_17__.randomId)(),\n    makeSystemMessage\n  });\n  const [currentSuggestions, setCurrentSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const suggestionsAbortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const debounceTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const abortSuggestions = () => {\n    var _a2;\n    (_a2 = suggestionsAbortControllerRef.current) == null ? void 0 : _a2.abort();\n    suggestionsAbortControllerRef.current = null;\n  };\n  const generalContext = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_12__.useCopilotContext)();\n  const messagesContext = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_18__.useCopilotMessagesContext)();\n  const context = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__spreadValues)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__spreadValues)({}, generalContext), messagesContext);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    onInProgress == null ? void 0 : onInProgress(isLoading);\n    abortSuggestions();\n    debounceTimerRef.current = setTimeout(\n      () => {\n        if (!isLoading && Object.keys(context.chatSuggestionConfiguration).length !== 0) {\n          suggestionsAbortControllerRef.current = new AbortController();\n          (0,_chunk_IMBPSLL4_mjs__WEBPACK_IMPORTED_MODULE_19__.reloadSuggestions)(\n            context,\n            context.chatSuggestionConfiguration,\n            setCurrentSuggestions,\n            suggestionsAbortControllerRef\n          );\n        }\n      },\n      currentSuggestions.length == 0 ? 0 : SUGGESTIONS_DEBOUNCE_TIMEOUT\n    );\n    return () => {\n      clearTimeout(debounceTimerRef.current);\n    };\n  }, [\n    isLoading,\n    context.chatSuggestionConfiguration,\n    // hackish way to trigger suggestions reload on reset, but better than moving suggestions to the\n    // global context\n    visibleMessages.length == 0\n  ]);\n  const sendMessage = (messageContent, imagesToUse) => (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__async)(void 0, null, function* () {\n    const images = imagesToUse || [];\n    abortSuggestions();\n    setCurrentSuggestions([]);\n    let firstMessage = null;\n    if (messageContent.trim().length > 0) {\n      const textMessage = new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_20__.TextMessage({\n        content: messageContent,\n        role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_20__.Role.User\n      });\n      if (onSubmitMessage) {\n        try {\n          yield onSubmitMessage(messageContent);\n        } catch (error) {\n          console.error(\"Error in onSubmitMessage:\", error);\n        }\n      }\n      yield appendMessage(textMessage, { followUp: images.length === 0 });\n      if (!firstMessage) {\n        firstMessage = textMessage;\n      }\n    }\n    if (images.length > 0) {\n      for (let i = 0; i < images.length; i++) {\n        const imageMessage = new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_20__.ImageMessage({\n          format: images[i].contentType.replace(\"image/\", \"\"),\n          bytes: images[i].bytes,\n          role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_20__.Role.User\n        });\n        yield appendMessage(imageMessage, { followUp: i === images.length - 1 });\n        if (!firstMessage) {\n          firstMessage = imageMessage;\n        }\n      }\n    }\n    if (!firstMessage) {\n      return new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_20__.TextMessage({ content: \"\", role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_20__.Role.User });\n    }\n    return firstMessage;\n  });\n  const messages = visibleMessages;\n  const { setMessages } = messagesContext;\n  const currentAgentName = (_a = generalContext.agentSession) == null ? void 0 : _a.agentName;\n  const restartCurrentAgent = (hint) => (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__async)(void 0, null, function* () {\n    if (generalContext.agentSession) {\n      generalContext.setAgentSession((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__spreadValues)({}, generalContext.agentSession), {\n        nodeName: void 0,\n        threadId: void 0\n      }));\n      generalContext.setCoagentStates((prevAgentStates) => {\n        return (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__spreadValues)({}, prevAgentStates), {\n          [generalContext.agentSession.agentName]: (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__spreadValues)({}, prevAgentStates[generalContext.agentSession.agentName]), {\n            threadId: void 0,\n            nodeName: void 0,\n            runId: void 0\n          })\n        });\n      });\n    }\n  });\n  const runCurrentAgent = (hint) => (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__async)(void 0, null, function* () {\n    if (generalContext.agentSession) {\n      yield (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_21__.runAgent)(\n        generalContext.agentSession.agentName,\n        context,\n        appendMessage,\n        runChatCompletion,\n        hint\n      );\n    }\n  });\n  const stopCurrentAgent = () => {\n    if (generalContext.agentSession) {\n      (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_21__.stopAgent)(generalContext.agentSession.agentName, context);\n    }\n  };\n  const setCurrentAgentState = (state) => {\n    if (generalContext.agentSession) {\n      generalContext.setCoagentStates((prevAgentStates) => {\n        return (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_13__.__spreadValues)({}, prevAgentStates), {\n          [generalContext.agentSession.agentName]: {\n            state\n          }\n        });\n      });\n    }\n  };\n  function stopGeneration() {\n    if (onStopGeneration) {\n      onStopGeneration({\n        messages,\n        setMessages,\n        stopGeneration: defaultStopGeneration,\n        currentAgentName,\n        restartCurrentAgent,\n        stopCurrentAgent,\n        runCurrentAgent,\n        setCurrentAgentState\n      });\n    } else {\n      defaultStopGeneration();\n    }\n  }\n  function reloadMessages(messageId) {\n    if (onReloadMessages) {\n      onReloadMessages({\n        messages,\n        setMessages,\n        stopGeneration: defaultStopGeneration,\n        currentAgentName,\n        restartCurrentAgent,\n        stopCurrentAgent,\n        runCurrentAgent,\n        setCurrentAgentState,\n        messageId\n      });\n    } else {\n      defaultReloadMessages(messageId);\n    }\n  }\n  return {\n    visibleMessages,\n    isLoading,\n    currentSuggestions,\n    sendMessage,\n    stopGeneration,\n    reloadMessages\n  };\n};\n\n\n//# sourceMappingURL=chunk-KQEMBE47.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-KQEMBE47.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-L3GZ7TXC.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-L3GZ7TXC.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssistantMessage: () => (/* binding */ AssistantMessage)\n/* harmony export */ });\n/* harmony import */ var _chunk_YTXEWDNC_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-YTXEWDNC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-YTXEWDNC.mjs\");\n/* harmony import */ var _chunk_IEMQ2SQW_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-IEMQ2SQW.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-IEMQ2SQW.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n// src/components/chat/messages/AssistantMessage.tsx\n\n\nvar AssistantMessage = (props) => {\n  const { icons, labels } = (0,_chunk_IEMQ2SQW_mjs__WEBPACK_IMPORTED_MODULE_2__.useChatContext)();\n  const {\n    message,\n    isLoading,\n    subComponent,\n    onRegenerate,\n    onCopy,\n    onThumbsUp,\n    onThumbsDown,\n    isCurrentMessage,\n    rawData,\n    markdownTagRenderers\n  } = props;\n  const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const handleCopy = () => {\n    if (message && onCopy) {\n      navigator.clipboard.writeText(message);\n      setCopied(true);\n      onCopy(message);\n      setTimeout(() => setCopied(false), 2e3);\n    } else if (message) {\n      navigator.clipboard.writeText(message);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2e3);\n    }\n  };\n  const handleRegenerate = () => {\n    if (onRegenerate) {\n      onRegenerate();\n    }\n  };\n  const handleThumbsUp = () => {\n    const fullMessage = rawData;\n    if (onThumbsUp && fullMessage) {\n      onThumbsUp(fullMessage);\n    }\n  };\n  const handleThumbsDown = () => {\n    const fullMessage = rawData;\n    if (onThumbsDown && fullMessage) {\n      onThumbsDown(fullMessage);\n    }\n  };\n  const LoadingIcon = () => /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", { children: icons.activityIcon });\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children: [\n    (message || isLoading) && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { className: \"copilotKitMessage copilotKitAssistantMessage\", children: [\n      message && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_chunk_YTXEWDNC_mjs__WEBPACK_IMPORTED_MODULE_3__.Markdown, { content: message || \"\", components: markdownTagRenderers }),\n      isLoading && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(LoadingIcon, {}),\n      message && !isLoading && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\n        \"div\",\n        {\n          className: `copilotKitMessageControls ${isCurrentMessage ? \"currentMessage\" : \"\"}`,\n          children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n              \"button\",\n              {\n                className: \"copilotKitMessageControlButton\",\n                onClick: handleRegenerate,\n                \"aria-label\": labels.regenerateResponse,\n                title: labels.regenerateResponse,\n                children: icons.regenerateIcon\n              }\n            ),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n              \"button\",\n              {\n                className: \"copilotKitMessageControlButton\",\n                onClick: handleCopy,\n                \"aria-label\": labels.copyToClipboard,\n                title: labels.copyToClipboard,\n                children: copied ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", { style: { fontSize: \"10px\", fontWeight: \"bold\" }, children: \"\\u2713\" }) : icons.copyIcon\n              }\n            ),\n            onThumbsUp && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n              \"button\",\n              {\n                className: \"copilotKitMessageControlButton\",\n                onClick: handleThumbsUp,\n                \"aria-label\": labels.thumbsUp,\n                title: labels.thumbsUp,\n                children: icons.thumbsUpIcon\n              }\n            ),\n            onThumbsDown && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n              \"button\",\n              {\n                className: \"copilotKitMessageControlButton\",\n                onClick: handleThumbsDown,\n                \"aria-label\": labels.thumbsDown,\n                title: labels.thumbsDown,\n                children: icons.thumbsDownIcon\n              }\n            )\n          ]\n        }\n      )\n    ] }),\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", { style: { marginBottom: \"0.5rem\" }, children: subComponent })\n  ] });\n};\n\n\n//# sourceMappingURL=chunk-L3GZ7TXC.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-L3GZ7TXC.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-MRXNTQOX.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-MRXNTQOX.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __async: () => (/* binding */ __async),\n/* harmony export */   __objRest: () => (/* binding */ __objRest),\n/* harmony export */   __spreadProps: () => (/* binding */ __spreadProps),\n/* harmony export */   __spreadValues: () => (/* binding */ __spreadValues)\n/* harmony export */ });\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n\n//# sourceMappingURL=chunk-MRXNTQOX.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-MRXNTQOX.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-PLHTVHUW.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-PLHTVHUW.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageUploadQueue: () => (/* binding */ ImageUploadQueue)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/components/chat/ImageUploadQueue.tsx\n\nvar ImageUploadQueue = ({\n  images,\n  onRemoveImage,\n  className = \"\"\n}) => {\n  if (images.length === 0)\n    return null;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n    \"div\",\n    {\n      className: `copilotKitImageUploadQueue ${className}`,\n      style: {\n        display: \"flex\",\n        flexWrap: \"wrap\",\n        gap: \"8px\",\n        margin: \"8px\",\n        padding: \"8px\"\n      },\n      children: images.map((image, index) => /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\n        \"div\",\n        {\n          className: \"copilotKitImageUploadQueueItem\",\n          style: {\n            position: \"relative\",\n            display: \"inline-block\",\n            width: \"60px\",\n            height: \"60px\",\n            borderRadius: \"4px\",\n            overflow: \"hidden\"\n          },\n          children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n              \"img\",\n              {\n                src: `data:${image.contentType};base64,${image.bytes}`,\n                alt: `Selected image ${index + 1}`,\n                style: {\n                  width: \"100%\",\n                  height: \"100%\",\n                  objectFit: \"cover\"\n                }\n              }\n            ),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n              \"button\",\n              {\n                onClick: () => onRemoveImage(index),\n                className: \"copilotKitImageUploadQueueRemoveButton\",\n                style: {\n                  position: \"absolute\",\n                  top: \"2px\",\n                  right: \"2px\",\n                  background: \"rgba(0,0,0,0.6)\",\n                  color: \"white\",\n                  border: \"none\",\n                  borderRadius: \"50%\",\n                  width: \"18px\",\n                  height: \"18px\",\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  justifyContent: \"center\",\n                  cursor: \"pointer\",\n                  fontSize: \"10px\",\n                  padding: 0\n                },\n                children: \"\\u2715\"\n              }\n            )\n          ]\n        },\n        index\n      ))\n    }\n  );\n};\n\n\n//# sourceMappingURL=chunk-PLHTVHUW.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-PLHTVHUW.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-QGSPTXOV.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-QGSPTXOV.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Suggestions: () => (/* binding */ Suggestions)\n/* harmony export */ });\n/* harmony import */ var _chunk_IMBPSLL4_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-IMBPSLL4.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-IMBPSLL4.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n// src/components/chat/Suggestions.tsx\n\nfunction Suggestions({ suggestions, onSuggestionClick }) {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: \"suggestions\", children: suggestions.map((suggestion, index) => /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n    _chunk_IMBPSLL4_mjs__WEBPACK_IMPORTED_MODULE_1__.Suggestion,\n    {\n      title: suggestion.title,\n      message: suggestion.message,\n      partial: suggestion.partial,\n      className: suggestion.className,\n      onClick: () => onSuggestionClick(suggestion.message)\n    },\n    index\n  )) });\n}\n\n\n//# sourceMappingURL=chunk-QGSPTXOV.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtdWlAMS45LjItbmV4dC43X0B0eXBlcytyZWFjdEAxOS4wLjFfZ3JhcGhxbEAxNi45LjBfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvQGNvcGlsb3RraXQvcmVhY3QtdWkvZGlzdC9jaHVuay1RR1NQVFhPVi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBRThCOztBQUU5QjtBQUN3QztBQUN4Qyx1QkFBdUIsZ0NBQWdDO0FBQ3ZELHlCQUF5QixzREFBRyxVQUFVLDJGQUEyRixzREFBRztBQUNwSSxJQUFJLDJEQUFVO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsTUFBTTtBQUNOOztBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AY29waWxvdGtpdCtyZWFjdC11aUAxLjkuMi1uZXh0LjdfQHR5cGVzK3JlYWN0QDE5LjAuMV9ncmFwaHFsQDE2LjkuMF9yZWFjdC1kb21AMTkuMC4wX3JlYWN0QDE5LjAuMF9fcmVhY3RAMTkuMC4wL25vZGVfbW9kdWxlcy9AY29waWxvdGtpdC9yZWFjdC11aS9kaXN0L2NodW5rLVFHU1BUWE9WLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBTdWdnZXN0aW9uXG59IGZyb20gXCIuL2NodW5rLUlNQlBTTEw0Lm1qc1wiO1xuXG4vLyBzcmMvY29tcG9uZW50cy9jaGF0L1N1Z2dlc3Rpb25zLnRzeFxuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5mdW5jdGlvbiBTdWdnZXN0aW9ucyh7IHN1Z2dlc3Rpb25zLCBvblN1Z2dlc3Rpb25DbGljayB9KSB7XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFwiZGl2XCIsIHsgY2xhc3NOYW1lOiBcInN1Z2dlc3Rpb25zXCIsIGNoaWxkcmVuOiBzdWdnZXN0aW9ucy5tYXAoKHN1Z2dlc3Rpb24sIGluZGV4KSA9PiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgIFN1Z2dlc3Rpb24sXG4gICAge1xuICAgICAgdGl0bGU6IHN1Z2dlc3Rpb24udGl0bGUsXG4gICAgICBtZXNzYWdlOiBzdWdnZXN0aW9uLm1lc3NhZ2UsXG4gICAgICBwYXJ0aWFsOiBzdWdnZXN0aW9uLnBhcnRpYWwsXG4gICAgICBjbGFzc05hbWU6IHN1Z2dlc3Rpb24uY2xhc3NOYW1lLFxuICAgICAgb25DbGljazogKCkgPT4gb25TdWdnZXN0aW9uQ2xpY2soc3VnZ2VzdGlvbi5tZXNzYWdlKVxuICAgIH0sXG4gICAgaW5kZXhcbiAgKSkgfSk7XG59XG5cbmV4cG9ydCB7XG4gIFN1Z2dlc3Rpb25zXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y2h1bmstUUdTUFRYT1YubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-QGSPTXOV.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-S5MBUNGN.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-S5MBUNGN.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkMicrophonePermission: () => (/* binding */ checkMicrophonePermission),\n/* harmony export */   requestMicAndPlaybackPermission: () => (/* binding */ requestMicAndPlaybackPermission),\n/* harmony export */   usePushToTalk: () => (/* binding */ usePushToTalk)\n/* harmony export */ });\n/* harmony import */ var _chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-MRXNTQOX.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-MRXNTQOX.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-DCTJZ742.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n// src/hooks/use-push-to-talk.tsx\n\n\nvar checkMicrophonePermission = () => (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_1__.__async)(void 0, null, function* () {\n  try {\n    const permissionStatus = yield navigator.permissions.query({\n      name: \"microphone\"\n    });\n    if (permissionStatus.state === \"granted\") {\n      return true;\n    } else {\n      return false;\n    }\n  } catch (err) {\n    console.error(\"Error checking microphone permission\", err);\n  }\n});\nvar requestMicAndPlaybackPermission = () => (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_1__.__async)(void 0, null, function* () {\n  try {\n    const stream = yield navigator.mediaDevices.getUserMedia({ audio: true });\n    const audioContext = new window.AudioContext();\n    yield audioContext.resume();\n    return { stream, audioContext };\n  } catch (err) {\n    console.error(\"Error requesting microphone and playback permissions\", err);\n    return null;\n  }\n});\nvar startRecording = (mediaStreamRef, mediaRecorderRef, audioContextRef, recordedChunks, onStop) => (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_1__.__async)(void 0, null, function* () {\n  if (!mediaStreamRef.current || !audioContextRef.current) {\n    mediaStreamRef.current = yield navigator.mediaDevices.getUserMedia({ audio: true });\n    audioContextRef.current = new window.AudioContext();\n    yield audioContextRef.current.resume();\n  }\n  mediaRecorderRef.current = new MediaRecorder(mediaStreamRef.current);\n  mediaRecorderRef.current.start(1e3);\n  mediaRecorderRef.current.ondataavailable = (event) => {\n    recordedChunks.push(event.data);\n  };\n  mediaRecorderRef.current.onstop = onStop;\n});\nvar stopRecording = (mediaRecorderRef) => {\n  if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n    mediaRecorderRef.current.stop();\n  }\n};\nvar transcribeAudio = (recordedChunks, transcribeAudioUrl) => (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_1__.__async)(void 0, null, function* () {\n  const completeBlob = new Blob(recordedChunks, { type: \"audio/mp4\" });\n  const formData = new FormData();\n  formData.append(\"file\", completeBlob, \"recording.mp4\");\n  const response = yield fetch(transcribeAudioUrl, {\n    method: \"POST\",\n    body: formData\n  });\n  if (!response.ok) {\n    throw new Error(`Error: ${response.statusText}`);\n  }\n  const transcription = yield response.json();\n  return transcription.text;\n});\nvar playAudioResponse = (text, textToSpeechUrl, audioContext) => {\n  const encodedText = encodeURIComponent(text);\n  const url = `${textToSpeechUrl}?text=${encodedText}`;\n  fetch(url).then((response) => response.arrayBuffer()).then((arrayBuffer) => audioContext.decodeAudioData(arrayBuffer)).then((audioBuffer) => {\n    const source = audioContext.createBufferSource();\n    source.buffer = audioBuffer;\n    source.connect(audioContext.destination);\n    source.start(0);\n  }).catch((error) => {\n    console.error(\"Error with decoding audio data\", error);\n  });\n};\nvar usePushToTalk = ({\n  sendFunction,\n  inProgress\n}) => {\n  const [pushToTalkState, setPushToTalkState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"idle\");\n  const mediaStreamRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const recordedChunks = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const generalContext = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_2__.useCopilotContext)();\n  const messagesContext = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__.useCopilotMessagesContext)();\n  const context = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_1__.__spreadValues)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_1__.__spreadValues)({}, generalContext), messagesContext);\n  const [startReadingFromMessageId, setStartReadingFromMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (pushToTalkState === \"recording\") {\n      startRecording(\n        mediaStreamRef,\n        mediaRecorderRef,\n        audioContextRef,\n        recordedChunks.current,\n        () => {\n          setPushToTalkState(\"transcribing\");\n        }\n      );\n    } else {\n      stopRecording(mediaRecorderRef);\n      if (pushToTalkState === \"transcribing\") {\n        transcribeAudio(recordedChunks.current, context.copilotApiConfig.transcribeAudioUrl).then(\n          (transcription) => (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_1__.__async)(void 0, null, function* () {\n            recordedChunks.current = [];\n            setPushToTalkState(\"idle\");\n            const message = yield sendFunction(transcription);\n            setStartReadingFromMessageId(message.id);\n          })\n        );\n      }\n    }\n    return () => {\n      stopRecording(mediaRecorderRef);\n    };\n  }, [pushToTalkState]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (inProgress === false && startReadingFromMessageId) {\n      const lastMessageIndex = context.messages.findIndex(\n        (message) => message.id === startReadingFromMessageId\n      );\n      const messagesAfterLast = context.messages.slice(lastMessageIndex + 1).filter(\n        (message) => message.isTextMessage() && message.role === \"assistant\"\n      );\n      const text = messagesAfterLast.map((message) => message.content).join(\"\\n\");\n      playAudioResponse(text, context.copilotApiConfig.textToSpeechUrl, audioContextRef.current);\n      setStartReadingFromMessageId(null);\n    }\n  }, [startReadingFromMessageId, inProgress]);\n  return { pushToTalkState, setPushToTalkState };\n};\n\n\n//# sourceMappingURL=chunk-S5MBUNGN.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-S5MBUNGN.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-UKCPOBQM.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-UKCPOBQM.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RenderActionExecutionMessage: () => (/* binding */ RenderActionExecutionMessage)\n/* harmony export */ });\n/* harmony import */ var _chunk_L3GZ7TXC_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-L3GZ7TXC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-L3GZ7TXC.mjs\");\n/* harmony import */ var _chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-MRXNTQOX.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-MRXNTQOX.mjs\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-WM3ARNBD.mjs\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n// src/components/chat/messages/RenderActionExecutionMessage.tsx\n\n\n\nfunction RenderActionExecutionMessage(_a) {\n  var _b = _a, {\n    AssistantMessage: AssistantMessage2 = _chunk_L3GZ7TXC_mjs__WEBPACK_IMPORTED_MODULE_1__.AssistantMessage\n  } = _b, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_b, [\n    \"AssistantMessage\"\n  ]);\n  const { chatComponentsCache } = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__.useCopilotContext)();\n  const { message, inProgress, index, isCurrentMessage, actionResult } = props;\n  if (message.isActionExecutionMessage()) {\n    if (chatComponentsCache.current !== null && (chatComponentsCache.current.actions[message.name] || chatComponentsCache.current.actions[\"*\"])) {\n      const render = chatComponentsCache.current.actions[message.name] || chatComponentsCache.current.actions[\"*\"];\n      if (typeof render === \"string\") {\n        if (isCurrentMessage && inProgress) {\n          return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n            AssistantMessage2,\n            {\n              rawData: message,\n              \"data-message-role\": \"assistant\",\n              isLoading: false,\n              isGenerating: true,\n              message: render\n            },\n            index\n          );\n        } else {\n          return null;\n        }\n      } else {\n        const args = message.arguments;\n        let status = \"inProgress\";\n        if (actionResult !== void 0) {\n          status = \"complete\";\n        } else if (message.status.code !== _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_4__.MessageStatusCode.Pending) {\n          status = \"executing\";\n        }\n        try {\n          const toRender = render({\n            status,\n            args,\n            result: actionResult,\n            name: message.name\n          });\n          if (!toRender && status === \"complete\") {\n            return null;\n          }\n          if (typeof toRender === \"string\") {\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n              AssistantMessage2,\n              {\n                rawData: message,\n                \"data-message-role\": \"assistant\",\n                isLoading: false,\n                isGenerating: false,\n                message: toRender\n              },\n              index\n            );\n          } else {\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n              AssistantMessage2,\n              {\n                rawData: message,\n                \"data-message-role\": \"action-render\",\n                isLoading: false,\n                isGenerating: false,\n                subComponent: toRender\n              },\n              index\n            );\n          }\n        } catch (e) {\n          console.error(`Error executing render function for action ${message.name}: ${e}`);\n          return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n            AssistantMessage2,\n            {\n              rawData: message,\n              \"data-message-role\": \"assistant\",\n              isLoading: false,\n              isGenerating: false,\n              subComponent: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { className: \"copilotKitMessage copilotKitAssistantMessage\", children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"b\", { children: [\n                  \"\\u274C Error executing render function for action \",\n                  message.name,\n                  \":\"\n                ] }),\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"pre\", { children: e instanceof Error ? e.message : String(e) })\n              ] })\n            },\n            index\n          );\n        }\n      }\n    } else if (!inProgress || !isCurrentMessage) {\n      return null;\n    } else {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n        AssistantMessage2,\n        {\n          rawData: message,\n          \"data-message-role\": \"assistant\",\n          isLoading: true,\n          isGenerating: true\n        },\n        index\n      );\n    }\n  }\n}\n\n\n//# sourceMappingURL=chunk-UKCPOBQM.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-UKCPOBQM.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-ULDQXCED.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-ULDQXCED.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RenderImageMessage: () => (/* binding */ RenderImageMessage)\n/* harmony export */ });\n/* harmony import */ var _chunk_L3GZ7TXC_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-L3GZ7TXC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-L3GZ7TXC.mjs\");\n/* harmony import */ var _chunk_HWMFMBJC_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-HWMFMBJC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-HWMFMBJC.mjs\");\n/* harmony import */ var _chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-MRXNTQOX.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-MRXNTQOX.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n// src/components/chat/messages/RenderImageMessage.tsx\n\nfunction RenderImageMessage(_a) {\n  var _b = _a, {\n    UserMessage: UserMessage2 = _chunk_HWMFMBJC_mjs__WEBPACK_IMPORTED_MODULE_1__.UserMessage,\n    AssistantMessage: AssistantMessage2 = _chunk_L3GZ7TXC_mjs__WEBPACK_IMPORTED_MODULE_2__.AssistantMessage\n  } = _b, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_3__.__objRest)(_b, [\n    \"UserMessage\",\n    \"AssistantMessage\"\n  ]);\n  const {\n    message,\n    inProgress,\n    index,\n    isCurrentMessage,\n    onRegenerate,\n    onCopy,\n    onThumbsUp,\n    onThumbsDown\n  } = props;\n  if (message.isImageMessage()) {\n    const imageData = `data:${message.format};base64,${message.bytes}`;\n    const imageComponent = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", { className: \"copilotKitImage\", children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n      \"img\",\n      {\n        src: imageData,\n        alt: \"User uploaded image\",\n        style: { maxWidth: \"100%\", maxHeight: \"300px\", borderRadius: \"8px\" }\n      }\n    ) });\n    if (message.role === \"user\") {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n        UserMessage2,\n        {\n          \"data-message-role\": \"user\",\n          message: \"\",\n          rawData: message,\n          subComponent: imageComponent\n        },\n        index\n      );\n    } else if (message.role === \"assistant\") {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n        AssistantMessage2,\n        {\n          \"data-message-role\": \"assistant\",\n          message: \"\",\n          rawData: message,\n          subComponent: imageComponent,\n          isLoading: inProgress && isCurrentMessage && !message.bytes,\n          isGenerating: inProgress && isCurrentMessage && !!message.bytes,\n          isCurrentMessage,\n          onRegenerate: () => onRegenerate == null ? void 0 : onRegenerate(message.id),\n          onCopy,\n          onThumbsUp,\n          onThumbsDown\n        },\n        index\n      );\n    }\n  }\n  return null;\n}\n\n\n//# sourceMappingURL=chunk-ULDQXCED.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtdWlAMS45LjItbmV4dC43X0B0eXBlcytyZWFjdEAxOS4wLjFfZ3JhcGhxbEAxNi45LjBfcmVhY3QtZG9tQDE5LjAuMF9yZWFjdEAxOS4wLjBfX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvQGNvcGlsb3RraXQvcmVhY3QtdWkvZGlzdC9jaHVuay1VTERRWENFRC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFOEI7QUFHQTtBQUdBOztBQUU5QjtBQUN3QztBQUN4QztBQUNBO0FBQ0EsZ0NBQWdDLDREQUFXO0FBQzNDLDBDQUEwQyxpRUFBZ0I7QUFDMUQsSUFBSSxjQUFjLDhEQUFTO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBLDhCQUE4QixnQkFBZ0IsU0FBUyxjQUFjO0FBQ3JFLDJDQUEyQyxzREFBRyxVQUFVLHdEQUF3RCxzREFBRztBQUNuSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLE9BQU87QUFDUDtBQUNBLDZCQUE2QixzREFBRztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLE1BQU07QUFDTiw2QkFBNkIsc0RBQUc7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AY29waWxvdGtpdCtyZWFjdC11aUAxLjkuMi1uZXh0LjdfQHR5cGVzK3JlYWN0QDE5LjAuMV9ncmFwaHFsQDE2LjkuMF9yZWFjdC1kb21AMTkuMC4wX3JlYWN0QDE5LjAuMF9fcmVhY3RAMTkuMC4wL25vZGVfbW9kdWxlcy9AY29waWxvdGtpdC9yZWFjdC11aS9kaXN0L2NodW5rLVVMRFFYQ0VELm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBBc3Npc3RhbnRNZXNzYWdlXG59IGZyb20gXCIuL2NodW5rLUwzR1o3VFhDLm1qc1wiO1xuaW1wb3J0IHtcbiAgVXNlck1lc3NhZ2Vcbn0gZnJvbSBcIi4vY2h1bmstSFdNRk1CSkMubWpzXCI7XG5pbXBvcnQge1xuICBfX29ialJlc3Rcbn0gZnJvbSBcIi4vY2h1bmstTVJYTlRRT1gubWpzXCI7XG5cbi8vIHNyYy9jb21wb25lbnRzL2NoYXQvbWVzc2FnZXMvUmVuZGVySW1hZ2VNZXNzYWdlLnRzeFxuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5mdW5jdGlvbiBSZW5kZXJJbWFnZU1lc3NhZ2UoX2EpIHtcbiAgdmFyIF9iID0gX2EsIHtcbiAgICBVc2VyTWVzc2FnZTogVXNlck1lc3NhZ2UyID0gVXNlck1lc3NhZ2UsXG4gICAgQXNzaXN0YW50TWVzc2FnZTogQXNzaXN0YW50TWVzc2FnZTIgPSBBc3Npc3RhbnRNZXNzYWdlXG4gIH0gPSBfYiwgcHJvcHMgPSBfX29ialJlc3QoX2IsIFtcbiAgICBcIlVzZXJNZXNzYWdlXCIsXG4gICAgXCJBc3Npc3RhbnRNZXNzYWdlXCJcbiAgXSk7XG4gIGNvbnN0IHtcbiAgICBtZXNzYWdlLFxuICAgIGluUHJvZ3Jlc3MsXG4gICAgaW5kZXgsXG4gICAgaXNDdXJyZW50TWVzc2FnZSxcbiAgICBvblJlZ2VuZXJhdGUsXG4gICAgb25Db3B5LFxuICAgIG9uVGh1bWJzVXAsXG4gICAgb25UaHVtYnNEb3duXG4gIH0gPSBwcm9wcztcbiAgaWYgKG1lc3NhZ2UuaXNJbWFnZU1lc3NhZ2UoKSkge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IGBkYXRhOiR7bWVzc2FnZS5mb3JtYXR9O2Jhc2U2NCwke21lc3NhZ2UuYnl0ZXN9YDtcbiAgICBjb25zdCBpbWFnZUNvbXBvbmVudCA9IC8qIEBfX1BVUkVfXyAqLyBqc3goXCJkaXZcIiwgeyBjbGFzc05hbWU6IFwiY29waWxvdEtpdEltYWdlXCIsIGNoaWxkcmVuOiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgXCJpbWdcIixcbiAgICAgIHtcbiAgICAgICAgc3JjOiBpbWFnZURhdGEsXG4gICAgICAgIGFsdDogXCJVc2VyIHVwbG9hZGVkIGltYWdlXCIsXG4gICAgICAgIHN0eWxlOiB7IG1heFdpZHRoOiBcIjEwMCVcIiwgbWF4SGVpZ2h0OiBcIjMwMHB4XCIsIGJvcmRlclJhZGl1czogXCI4cHhcIiB9XG4gICAgICB9XG4gICAgKSB9KTtcbiAgICBpZiAobWVzc2FnZS5yb2xlID09PSBcInVzZXJcIikge1xuICAgICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgICAgIFVzZXJNZXNzYWdlMixcbiAgICAgICAge1xuICAgICAgICAgIFwiZGF0YS1tZXNzYWdlLXJvbGVcIjogXCJ1c2VyXCIsXG4gICAgICAgICAgbWVzc2FnZTogXCJcIixcbiAgICAgICAgICByYXdEYXRhOiBtZXNzYWdlLFxuICAgICAgICAgIHN1YkNvbXBvbmVudDogaW1hZ2VDb21wb25lbnRcbiAgICAgICAgfSxcbiAgICAgICAgaW5kZXhcbiAgICAgICk7XG4gICAgfSBlbHNlIGlmIChtZXNzYWdlLnJvbGUgPT09IFwiYXNzaXN0YW50XCIpIHtcbiAgICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgICBBc3Npc3RhbnRNZXNzYWdlMixcbiAgICAgICAge1xuICAgICAgICAgIFwiZGF0YS1tZXNzYWdlLXJvbGVcIjogXCJhc3Npc3RhbnRcIixcbiAgICAgICAgICBtZXNzYWdlOiBcIlwiLFxuICAgICAgICAgIHJhd0RhdGE6IG1lc3NhZ2UsXG4gICAgICAgICAgc3ViQ29tcG9uZW50OiBpbWFnZUNvbXBvbmVudCxcbiAgICAgICAgICBpc0xvYWRpbmc6IGluUHJvZ3Jlc3MgJiYgaXNDdXJyZW50TWVzc2FnZSAmJiAhbWVzc2FnZS5ieXRlcyxcbiAgICAgICAgICBpc0dlbmVyYXRpbmc6IGluUHJvZ3Jlc3MgJiYgaXNDdXJyZW50TWVzc2FnZSAmJiAhIW1lc3NhZ2UuYnl0ZXMsXG4gICAgICAgICAgaXNDdXJyZW50TWVzc2FnZSxcbiAgICAgICAgICBvblJlZ2VuZXJhdGU6ICgpID0+IG9uUmVnZW5lcmF0ZSA9PSBudWxsID8gdm9pZCAwIDogb25SZWdlbmVyYXRlKG1lc3NhZ2UuaWQpLFxuICAgICAgICAgIG9uQ29weSxcbiAgICAgICAgICBvblRodW1ic1VwLFxuICAgICAgICAgIG9uVGh1bWJzRG93blxuICAgICAgICB9LFxuICAgICAgICBpbmRleFxuICAgICAgKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIG51bGw7XG59XG5cbmV4cG9ydCB7XG4gIFJlbmRlckltYWdlTWVzc2FnZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNodW5rLVVMRFFYQ0VELm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-ULDQXCED.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-X3LV7OXQ.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-X3LV7OXQ.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var _chunk_CGEAG65D_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./chunk-CGEAG65D.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-CGEAG65D.mjs\");\n/* harmony import */ var _chunk_YQFVRDNC_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-YQFVRDNC.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-YQFVRDNC.mjs\");\n/* harmony import */ var _chunk_IEMQ2SQW_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-IEMQ2SQW.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-IEMQ2SQW.mjs\");\n/* harmony import */ var _chunk_S5MBUNGN_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-S5MBUNGN.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-S5MBUNGN.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\n// src/components/chat/Input.tsx\n\n\n\nvar MAX_NEWLINES = 6;\nvar Input = ({\n  inProgress,\n  onSend,\n  isVisible = false,\n  onStop,\n  onUpload,\n  hideStopButton = false\n}) => {\n  var _a, _b;\n  const context = (0,_chunk_IEMQ2SQW_mjs__WEBPACK_IMPORTED_MODULE_2__.useChatContext)();\n  const copilotContext = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_3__.useCopilotContext)();\n  const showPoweredBy = !((_a = copilotContext.copilotApiConfig) == null ? void 0 : _a.publicApiKey);\n  const pushToTalkConfigured = copilotContext.copilotApiConfig.textToSpeechUrl !== void 0 && copilotContext.copilotApiConfig.transcribeAudioUrl !== void 0;\n  const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const handleDivClick = (event) => {\n    var _a2;\n    const target = event.target;\n    if (target.closest(\"button\"))\n      return;\n    if (target.tagName === \"TEXTAREA\")\n      return;\n    (_a2 = textareaRef.current) == null ? void 0 : _a2.focus();\n  };\n  const [text, setText] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const send = () => {\n    var _a2;\n    if (inProgress)\n      return;\n    onSend(text);\n    setText(\"\");\n    (_a2 = textareaRef.current) == null ? void 0 : _a2.focus();\n  };\n  const { pushToTalkState, setPushToTalkState } = (0,_chunk_S5MBUNGN_mjs__WEBPACK_IMPORTED_MODULE_4__.usePushToTalk)({\n    sendFunction: onSend,\n    inProgress\n  });\n  const isInProgress = inProgress || pushToTalkState === \"transcribing\";\n  const buttonIcon = isInProgress && !hideStopButton ? context.icons.stopIcon : context.icons.sendIcon;\n  const showPushToTalk = pushToTalkConfigured && (pushToTalkState === \"idle\" || pushToTalkState === \"recording\") && !inProgress;\n  const canSend = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    var _a2;\n    const interruptEvent = (_a2 = copilotContext.langGraphInterruptAction) == null ? void 0 : _a2.event;\n    const interruptInProgress = (interruptEvent == null ? void 0 : interruptEvent.name) === \"LangGraphInterruptEvent\" && !(interruptEvent == null ? void 0 : interruptEvent.response);\n    return !isInProgress && text.trim().length > 0 && pushToTalkState === \"idle\" && !interruptInProgress;\n  }, [(_b = copilotContext.langGraphInterruptAction) == null ? void 0 : _b.event, isInProgress, text, pushToTalkState]);\n  const canStop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return isInProgress && !hideStopButton;\n  }, [isInProgress, hideStopButton]);\n  const sendDisabled = !canSend && !canStop;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { className: `copilotKitInputContainer ${showPoweredBy ? \"poweredByContainer\" : \"\"}`, children: [\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { className: \"copilotKitInput\", onClick: handleDivClick, children: [\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n        _chunk_YQFVRDNC_mjs__WEBPACK_IMPORTED_MODULE_5__.Textarea_default,\n        {\n          ref: textareaRef,\n          placeholder: context.labels.placeholder,\n          autoFocus: false,\n          maxRows: MAX_NEWLINES,\n          value: text,\n          onChange: (event) => setText(event.target.value),\n          onKeyDown: (event) => {\n            if (event.key === \"Enter\" && !event.shiftKey) {\n              event.preventDefault();\n              if (canSend) {\n                send();\n              }\n            }\n          }\n        }\n      ),\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { className: \"copilotKitInputControls\", children: [\n        onUpload && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"button\", { onClick: onUpload, className: \"copilotKitInputControlButton\", children: context.icons.uploadIcon }),\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", { style: { flexGrow: 1 } }),\n        showPushToTalk && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          \"button\",\n          {\n            onClick: () => setPushToTalkState(pushToTalkState === \"idle\" ? \"recording\" : \"transcribing\"),\n            className: pushToTalkState === \"recording\" ? \"copilotKitInputControlButton copilotKitPushToTalkRecording\" : \"copilotKitInputControlButton\",\n            children: context.icons.pushToTalkIcon\n          }\n        ),\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          \"button\",\n          {\n            disabled: sendDisabled,\n            onClick: isInProgress && !hideStopButton ? onStop : send,\n            \"data-copilotkit-in-progress\": inProgress,\n            \"data-test-id\": inProgress ? \"copilot-chat-request-in-progress\" : \"copilot-chat-ready\",\n            className: \"copilotKitInputControlButton\",\n            children: buttonIcon\n          }\n        )\n      ] })\n    ] }),\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_chunk_CGEAG65D_mjs__WEBPACK_IMPORTED_MODULE_6__.PoweredByTag, { showPoweredBy })\n  ] });\n};\n\n\n//# sourceMappingURL=chunk-X3LV7OXQ.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-X3LV7OXQ.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-XWG3L6QC.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-XWG3L6QC.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActivityIcon: () => (/* binding */ ActivityIcon),\n/* harmony export */   CheckIcon: () => (/* binding */ CheckIcon),\n/* harmony export */   CloseIcon: () => (/* binding */ CloseIcon),\n/* harmony export */   CopyIcon: () => (/* binding */ CopyIcon),\n/* harmony export */   DownloadIcon: () => (/* binding */ DownloadIcon),\n/* harmony export */   HeaderCloseIcon: () => (/* binding */ HeaderCloseIcon),\n/* harmony export */   MicrophoneIcon: () => (/* binding */ MicrophoneIcon),\n/* harmony export */   OpenIcon: () => (/* binding */ OpenIcon),\n/* harmony export */   RegenerateIcon: () => (/* binding */ RegenerateIcon),\n/* harmony export */   SendIcon: () => (/* binding */ SendIcon),\n/* harmony export */   SmallSpinnerIcon: () => (/* binding */ SmallSpinnerIcon),\n/* harmony export */   SpinnerIcon: () => (/* binding */ SpinnerIcon),\n/* harmony export */   StopIcon: () => (/* binding */ StopIcon),\n/* harmony export */   ThumbsDownIcon: () => (/* binding */ ThumbsDownIcon),\n/* harmony export */   ThumbsUpIcon: () => (/* binding */ ThumbsUpIcon),\n/* harmony export */   UploadIcon: () => (/* binding */ UploadIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/components/chat/Icons.tsx\n\nvar OpenIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    width: \"24\",\n    height: \"24\",\n    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"g\", { transform: \"translate(24, 0) scale(-1, 1)\", children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n      \"path\",\n      {\n        fillRule: \"evenodd\",\n        d: \"M5.337 21.718a6.707 6.707 0 01-.533-.074.75.75 0 01-.44-1.223 3.73 3.73 0 00.814-1.686c.023-.115-.022-.317-.254-.543C3.274 16.587 2.25 14.41 2.25 12c0-5.03 4.428-9 9.75-9s9.75 3.97 9.75 9c0 5.03-4.428 9-9.75 9-.833 0-1.643-.097-2.417-.279a6.721 6.721 0 01-4.246.997z\",\n        clipRule: \"evenodd\"\n      }\n    ) })\n  }\n);\nvar CloseIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: \"1.5\",\n    stroke: \"currentColor\",\n    width: \"24\",\n    height: \"24\",\n    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { strokeLinecap: \"round\", strokeLinejoin: \"round\", d: \"M19.5 8.25l-7.5 7.5-7.5-7.5\" })\n  }\n);\nvar HeaderCloseIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: \"1.5\",\n    stroke: \"currentColor\",\n    width: \"24\",\n    height: \"24\",\n    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { strokeLinecap: \"round\", strokeLinejoin: \"round\", d: \"M6 18L18 6M6 6l12 12\" })\n  }\n);\nvar SendIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: \"1.5\",\n    stroke: \"currentColor\",\n    width: \"24\",\n    height: \"24\",\n    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { strokeLinecap: \"round\", strokeLinejoin: \"round\", d: \"M12 19V5m0 0l-7 7m7-7l7 7\" })\n  }\n);\nvar MicrophoneIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: \"1.5\",\n    stroke: \"currentColor\",\n    width: \"24\",\n    height: \"24\",\n    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n      \"path\",\n      {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z\"\n      }\n    )\n  }\n);\nvar StopIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: \"1.5\",\n    stroke: \"currentColor\",\n    width: \"24\",\n    height: \"24\",\n    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n      \"path\",\n      {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M5.25 7.5A2.25 2.25 0 017.5 5.25h9a2.25 2.25 0 012.25 2.25v9a2.25 2.25 0 01-2.25 2.25h-9a2.25 2.25 0 01-2.25-2.25v-9z\"\n      }\n    )\n  }\n);\nvar RegenerateIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: \"2\",\n    stroke: \"currentColor\",\n    width: \"16\",\n    height: \"16\",\n    style: { minWidth: \"16px\", minHeight: \"16px\" },\n    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n      \"path\",\n      {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99\"\n      }\n    )\n  }\n);\nvar CopyIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: \"2\",\n    stroke: \"currentColor\",\n    width: \"16\",\n    height: \"16\",\n    style: { minWidth: \"16px\", minHeight: \"16px\" },\n    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n      \"path\",\n      {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\n      }\n    )\n  }\n);\nvar SmallSpinnerIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: \"copilotKitSpinner\", style: { width: \"13px\", height: \"13px\" } });\nvar SpinnerIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: \"copilotKitSpinner\", style: { width: \"24px\", height: \"24px\" } });\nvar ActivityIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { style: { display: \"flex\", alignItems: \"center\", gap: \"4px\" }, children: [\n  /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: \"copilotKitActivityDot\", style: { animationDelay: \"0s\" } }),\n  /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: \"copilotKitActivityDot\", style: { animationDelay: \"0.2s\" } }),\n  /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", { className: \"copilotKitActivityDot\", style: { animationDelay: \"0.4s\" } })\n] });\nvar ThumbsUpIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: \"2\",\n    stroke: \"currentColor\",\n    width: \"16\",\n    height: \"16\",\n    style: { minWidth: \"16px\", minHeight: \"16px\" },\n    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n      \"path\",\n      {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M6.633 10.5c.806 0 1.533-.446 2.031-1.08a9.041 9.041 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75A2.25 2.25 0 0116.5 4.5c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H13.48c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23H5.904M14.25 9h2.25M5.904 18.75c.083.205.173.405.27.602.197.4-.078.898-.523.898h-.908c-.889 0-1.713-.518-1.972-1.368a12 12 0 01-.521-3.507c0-1.553.295-3.036.831-4.398C3.387 10.203 4.167 9.75 5 9.75h1.053c.472 0 .745.556.5.96a8.958 8.958 0 00-1.302 4.665c0 1.194.232 2.333.654 3.375z\"\n      }\n    )\n  }\n);\nvar ThumbsDownIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: \"2\",\n    stroke: \"currentColor\",\n    width: \"16\",\n    height: \"16\",\n    style: { minWidth: \"16px\", minHeight: \"16px\" },\n    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n      \"path\",\n      {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M7.5 15h2.25m8.024-9.75c.011.05.028.1.052.148.591 1.2.924 2.55.924 3.977a8.96 8.96 0 01-.999 4.125m.023-8.25c-.076-.365.183-.75.575-.75h.908c.889 0 1.713.518 1.972 1.368.339 1.11.521 2.287.521 3.507 0 1.553-.295 3.036-.831 4.398C20.613 14.547 19.833 15 19 15h-1.053c-.472 0-.745-.556-.5-.96a8.95 8.95 0 00.303-.54m.023-8.25H16.48a4.5 4.5 0 01-1.423-.23l-3.114-1.04a4.5 4.5 0 00-1.423-.23H6.504c-.618 0-1.217.247-1.605.729A11.95 11.95 0 002.25 12c0 .434.023.863.068 1.285C2.427 14.306 3.346 15 4.372 15h3.126c.618 0 .991.724.725 1.282A7.471 7.471 0 007.5 19.5a2.25 2.25 0 002.25 2.25.75.75 0 00.75-.75v-.633c0-.573.11-1.14.322-1.672.304-.76.93-1.33 1.653-1.715a9.04 9.04 0 002.86-2.4c.498-.634 1.226-1.08 2.032-1.08h.384\"\n      }\n    )\n  }\n);\nvar DownloadIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: \"2\",\n    stroke: \"currentColor\",\n    width: \"16\",\n    height: \"16\",\n    style: { minWidth: \"16px\", minHeight: \"16px\" },\n    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n      \"path\",\n      {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3\"\n      }\n    )\n  }\n);\nvar UploadIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: \"1.5\",\n    stroke: \"currentColor\",\n    width: \"24\",\n    height: \"24\",\n    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { strokeLinecap: \"round\", strokeLinejoin: \"round\", d: \"M12 4.5v15m7.5-7.5h-15\" })\n  }\n);\nvar CheckIcon = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: \"2\",\n    stroke: \"currentColor\",\n    width: \"16\",\n    height: \"16\",\n    style: { minWidth: \"16px\", minHeight: \"16px\" },\n    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { strokeLinecap: \"round\", strokeLinejoin: \"round\", d: \"M4.5 12.75l6 6 9-13.5\" })\n  }\n);\n\n\n//# sourceMappingURL=chunk-XWG3L6QC.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-XWG3L6QC.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-YQFVRDNC.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-YQFVRDNC.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea_default: () => (/* binding */ Textarea_default)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/components/chat/Textarea.tsx\n\n\nvar AutoResizingTextarea = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(\n  ({ maxRows = 1, placeholder, value, onChange, onKeyDown, autoFocus }, ref) => {\n    const internalTextareaRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [maxHeight, setMaxHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => internalTextareaRef.current);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n      const calculateMaxHeight = () => {\n        const textarea = internalTextareaRef.current;\n        if (textarea) {\n          textarea.style.height = \"auto\";\n          const singleRowHeight = textarea.scrollHeight;\n          setMaxHeight(singleRowHeight * maxRows);\n          if (autoFocus) {\n            textarea.focus();\n          }\n        }\n      };\n      calculateMaxHeight();\n    }, [maxRows]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n      const textarea = internalTextareaRef.current;\n      if (textarea) {\n        textarea.style.height = \"auto\";\n        textarea.style.height = `${Math.min(textarea.scrollHeight, maxHeight)}px`;\n      }\n    }, [value, maxHeight]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      \"textarea\",\n      {\n        ref: internalTextareaRef,\n        value,\n        onChange,\n        onKeyDown,\n        placeholder,\n        style: {\n          overflow: \"auto\",\n          resize: \"none\",\n          maxHeight: `${maxHeight}px`\n        },\n        rows: 1\n      }\n    );\n  }\n);\nvar Textarea_default = AutoResizingTextarea;\n\n\n//# sourceMappingURL=chunk-YQFVRDNC.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-YQFVRDNC.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-YTXEWDNC.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-YTXEWDNC.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Markdown: () => (/* binding */ Markdown)\n/* harmony export */ });\n/* harmony import */ var _chunk_KENCH7RN_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-KENCH7RN.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-KENCH7RN.mjs\");\n/* harmony import */ var _chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-MRXNTQOX.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-MRXNTQOX.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/.pnpm/react-markdown@10.1.0_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/.pnpm/remark-gfm@4.0.1/node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remark-math */ \"(ssr)/./node_modules/.pnpm/remark-math@6.0.0/node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_raw__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rehype-raw */ \"(ssr)/./node_modules/.pnpm/rehype-raw@7.0.0/node_modules/rehype-raw/lib/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n// src/components/chat/Markdown.tsx\n\n\n\n\n\n\nvar defaultComponents = {\n  a(_a) {\n    var _b = _a, { children } = _b, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_b, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"a\", (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({ className: \"copilotKitMarkdownElement\" }, props), { target: \"_blank\", rel: \"noopener noreferrer\", children }));\n  },\n  // @ts-expect-error -- inline\n  code(_c) {\n    var _d = _c, { children, className, inline } = _d, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_d, [\"children\", \"className\", \"inline\"]);\n    if (Array.isArray(children) && children.length) {\n      if (children[0] == \"\\u258D\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          \"span\",\n          {\n            style: {\n              animation: \"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite\",\n              marginTop: \"0.25rem\"\n            },\n            children: \"\\u258D\"\n          }\n        );\n      }\n      children[0] = (children == null ? void 0 : children[0]).replace(\"`\\u258D`\", \"\\u258D\");\n    }\n    const match = /language-(\\w+)/.exec(className || \"\");\n    if (inline) {\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"code\", (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({ className }, props), { children }));\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _chunk_KENCH7RN_mjs__WEBPACK_IMPORTED_MODULE_3__.CodeBlock,\n      (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({\n        language: match && match[1] || \"\",\n        value: String(children).replace(/\\n$/, \"\")\n      }, props),\n      Math.random()\n    );\n  },\n  h1: (_e) => {\n    var _f = _e, { children } = _f, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_f, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"h1\", (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({ className: \"copilotKitMarkdownElement\" }, props), { children }));\n  },\n  h2: (_g) => {\n    var _h = _g, { children } = _h, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_h, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"h2\", (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({ className: \"copilotKitMarkdownElement\" }, props), { children }));\n  },\n  h3: (_i) => {\n    var _j = _i, { children } = _j, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_j, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"h3\", (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({ className: \"copilotKitMarkdownElement\" }, props), { children }));\n  },\n  h4: (_k) => {\n    var _l = _k, { children } = _l, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_l, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"h4\", (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({ className: \"copilotKitMarkdownElement\" }, props), { children }));\n  },\n  h5: (_m) => {\n    var _n = _m, { children } = _n, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_n, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"h5\", (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({ className: \"copilotKitMarkdownElement\" }, props), { children }));\n  },\n  h6: (_o) => {\n    var _p = _o, { children } = _p, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_p, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"h6\", (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({ className: \"copilotKitMarkdownElement\" }, props), { children }));\n  },\n  p: (_q) => {\n    var _r = _q, { children } = _r, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_r, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"p\", (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({ className: \"copilotKitMarkdownElement\" }, props), { children }));\n  },\n  pre: (_s) => {\n    var _t = _s, { children } = _t, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_t, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"pre\", (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({ className: \"copilotKitMarkdownElement\" }, props), { children }));\n  },\n  blockquote: (_u) => {\n    var _v = _u, { children } = _v, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_v, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"blockquote\", (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({ className: \"copilotKitMarkdownElement\" }, props), { children }));\n  },\n  ul: (_w) => {\n    var _x = _w, { children } = _x, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_x, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"ul\", (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({ className: \"copilotKitMarkdownElement\" }, props), { children }));\n  },\n  li: (_y) => {\n    var _z = _y, { children } = _z, props = (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_z, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"li\", (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({ className: \"copilotKitMarkdownElement\" }, props), { children }));\n  }\n};\nvar MemoizedReactMarkdown = (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(\n  react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown,\n  (prevProps, nextProps) => prevProps.children === nextProps.children && prevProps.components === nextProps.components\n);\nvar Markdown = ({ content, components }) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", { className: \"copilotKitMarkdown\", children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    MemoizedReactMarkdown,\n    {\n      components: (0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)((0,_chunk_MRXNTQOX_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({}, defaultComponents), components),\n      remarkPlugins: [remark_gfm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], remark_math__WEBPACK_IMPORTED_MODULE_6__[\"default\"]],\n      rehypePlugins: [rehype_raw__WEBPACK_IMPORTED_MODULE_7__[\"default\"]],\n      children: content\n    }\n  ) });\n};\n\n\n//# sourceMappingURL=chunk-YTXEWDNC.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-YTXEWDNC.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-Z4XPPVZT.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-Z4XPPVZT.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCopilotChatSuggestions: () => (/* binding */ useCopilotChatSuggestions)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _copilotkit_react_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @copilotkit/react-core */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-VNNKZIFB.mjs\");\n// src/hooks/use-copilot-chat-suggestions.tsx\n\n\n\nfunction useCopilotChatSuggestions({\n  available = \"enabled\",\n  instructions,\n  className,\n  minSuggestions = 1,\n  maxSuggestions = 3\n}, dependencies = []) {\n  const context = (0,_copilotkit_react_core__WEBPACK_IMPORTED_MODULE_1__.useCopilotContext)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (available === \"disabled\")\n      return;\n    const id = (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__.randomId)();\n    context.addChatSuggestionConfiguration(id, {\n      instructions,\n      minSuggestions,\n      maxSuggestions,\n      className\n    });\n    return () => {\n      context.removeChatSuggestionConfiguration(id);\n    };\n  }, [...dependencies, instructions, minSuggestions, maxSuggestions, className, available]);\n}\n\n\n//# sourceMappingURL=chunk-Z4XPPVZT.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-ui@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-ui/dist/chunk-Z4XPPVZT.mjs\n");

/***/ })

};
;