"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+token-providers@3.830.0";
exports.ids = ["vendor-chunks/@aws-sdk+token-providers@3.830.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/constants.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/constants.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EXPIRE_WINDOW_MS: () => (/* binding */ EXPIRE_WINDOW_MS),\n/* harmony export */   REFRESH_MESSAGE: () => (/* binding */ REFRESH_MESSAGE)\n/* harmony export */ });\nconst EXPIRE_WINDOW_MS = 5 * 60 * 1000;\nconst REFRESH_MESSAGE = `To refresh this SSO session run 'aws sso login' with the corresponding profile.`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdG9rZW4tcHJvdmlkZXJzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Rva2VuLXByb3ZpZGVycy9kaXN0LWVzL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayt0b2tlbi1wcm92aWRlcnNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdG9rZW4tcHJvdmlkZXJzL2Rpc3QtZXMvY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBFWFBJUkVfV0lORE9XX01TID0gNSAqIDYwICogMTAwMDtcbmV4cG9ydCBjb25zdCBSRUZSRVNIX01FU1NBR0UgPSBgVG8gcmVmcmVzaCB0aGlzIFNTTyBzZXNzaW9uIHJ1biAnYXdzIHNzbyBsb2dpbicgd2l0aCB0aGUgY29ycmVzcG9uZGluZyBwcm9maWxlLmA7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/fromSso.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/fromSso.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromSso: () => (/* binding */ fromSso)\n/* harmony export */ });\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var _smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/shared-ini-file-loader */ \"(rsc)/./node_modules/.pnpm/@smithy+shared-ini-file-loader@4.0.4/node_modules/@smithy/shared-ini-file-loader/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/constants.js\");\n/* harmony import */ var _getNewSsoOidcToken__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./getNewSsoOidcToken */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/getNewSsoOidcToken.js\");\n/* harmony import */ var _validateTokenExpiry__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./validateTokenExpiry */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/validateTokenExpiry.js\");\n/* harmony import */ var _validateTokenKey__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./validateTokenKey */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/validateTokenKey.js\");\n/* harmony import */ var _writeSSOTokenToFile__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./writeSSOTokenToFile */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/writeSSOTokenToFile.js\");\n\n\n\n\n\n\n\nconst lastRefreshAttemptTime = new Date(0);\nconst fromSso = (_init = {}) => async ({ callerClientConfig } = {}) => {\n    const init = {\n        ..._init,\n        parentClientConfig: {\n            ...callerClientConfig,\n            ..._init.parentClientConfig,\n        },\n    };\n    init.logger?.debug(\"@aws-sdk/token-providers - fromSso\");\n    const profiles = await (0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_1__.parseKnownFiles)(init);\n    const profileName = (0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_1__.getProfileName)({\n        profile: init.profile ?? callerClientConfig?.profile,\n    });\n    const profile = profiles[profileName];\n    if (!profile) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.TokenProviderError(`Profile '${profileName}' could not be found in shared credentials file.`, false);\n    }\n    else if (!profile[\"sso_session\"]) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.TokenProviderError(`Profile '${profileName}' is missing required property 'sso_session'.`);\n    }\n    const ssoSessionName = profile[\"sso_session\"];\n    const ssoSessions = await (0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_1__.loadSsoSessionData)(init);\n    const ssoSession = ssoSessions[ssoSessionName];\n    if (!ssoSession) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.TokenProviderError(`Sso session '${ssoSessionName}' could not be found in shared credentials file.`, false);\n    }\n    for (const ssoSessionRequiredKey of [\"sso_start_url\", \"sso_region\"]) {\n        if (!ssoSession[ssoSessionRequiredKey]) {\n            throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.TokenProviderError(`Sso session '${ssoSessionName}' is missing required property '${ssoSessionRequiredKey}'.`, false);\n        }\n    }\n    const ssoStartUrl = ssoSession[\"sso_start_url\"];\n    const ssoRegion = ssoSession[\"sso_region\"];\n    let ssoToken;\n    try {\n        ssoToken = await (0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_1__.getSSOTokenFromFile)(ssoSessionName);\n    }\n    catch (e) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.TokenProviderError(`The SSO session token associated with profile=${profileName} was not found or is invalid. ${_constants__WEBPACK_IMPORTED_MODULE_2__.REFRESH_MESSAGE}`, false);\n    }\n    (0,_validateTokenKey__WEBPACK_IMPORTED_MODULE_3__.validateTokenKey)(\"accessToken\", ssoToken.accessToken);\n    (0,_validateTokenKey__WEBPACK_IMPORTED_MODULE_3__.validateTokenKey)(\"expiresAt\", ssoToken.expiresAt);\n    const { accessToken, expiresAt } = ssoToken;\n    const existingToken = { token: accessToken, expiration: new Date(expiresAt) };\n    if (existingToken.expiration.getTime() - Date.now() > _constants__WEBPACK_IMPORTED_MODULE_2__.EXPIRE_WINDOW_MS) {\n        return existingToken;\n    }\n    if (Date.now() - lastRefreshAttemptTime.getTime() < 30 * 1000) {\n        (0,_validateTokenExpiry__WEBPACK_IMPORTED_MODULE_4__.validateTokenExpiry)(existingToken);\n        return existingToken;\n    }\n    (0,_validateTokenKey__WEBPACK_IMPORTED_MODULE_3__.validateTokenKey)(\"clientId\", ssoToken.clientId, true);\n    (0,_validateTokenKey__WEBPACK_IMPORTED_MODULE_3__.validateTokenKey)(\"clientSecret\", ssoToken.clientSecret, true);\n    (0,_validateTokenKey__WEBPACK_IMPORTED_MODULE_3__.validateTokenKey)(\"refreshToken\", ssoToken.refreshToken, true);\n    try {\n        lastRefreshAttemptTime.setTime(Date.now());\n        const newSsoOidcToken = await (0,_getNewSsoOidcToken__WEBPACK_IMPORTED_MODULE_5__.getNewSsoOidcToken)(ssoToken, ssoRegion, init);\n        (0,_validateTokenKey__WEBPACK_IMPORTED_MODULE_3__.validateTokenKey)(\"accessToken\", newSsoOidcToken.accessToken);\n        (0,_validateTokenKey__WEBPACK_IMPORTED_MODULE_3__.validateTokenKey)(\"expiresIn\", newSsoOidcToken.expiresIn);\n        const newTokenExpiration = new Date(Date.now() + newSsoOidcToken.expiresIn * 1000);\n        try {\n            await (0,_writeSSOTokenToFile__WEBPACK_IMPORTED_MODULE_6__.writeSSOTokenToFile)(ssoSessionName, {\n                ...ssoToken,\n                accessToken: newSsoOidcToken.accessToken,\n                expiresAt: newTokenExpiration.toISOString(),\n                refreshToken: newSsoOidcToken.refreshToken,\n            });\n        }\n        catch (error) {\n        }\n        return {\n            token: newSsoOidcToken.accessToken,\n            expiration: newTokenExpiration,\n        };\n    }\n    catch (error) {\n        (0,_validateTokenExpiry__WEBPACK_IMPORTED_MODULE_4__.validateTokenExpiry)(existingToken);\n        return existingToken;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/fromSso.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/getNewSsoOidcToken.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/getNewSsoOidcToken.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNewSsoOidcToken: () => (/* binding */ getNewSsoOidcToken)\n/* harmony export */ });\n/* harmony import */ var _getSsoOidcClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getSsoOidcClient */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/getSsoOidcClient.js\");\n\nconst getNewSsoOidcToken = async (ssoToken, ssoRegion, init = {}) => {\n    const { CreateTokenCommand } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@aws-sdk+nested-clients@3.830.0\").then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/nested-clients/sso-oidc */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/index.js\"));\n    const ssoOidcClient = await (0,_getSsoOidcClient__WEBPACK_IMPORTED_MODULE_0__.getSsoOidcClient)(ssoRegion, init);\n    return ssoOidcClient.send(new CreateTokenCommand({\n        clientId: ssoToken.clientId,\n        clientSecret: ssoToken.clientSecret,\n        refreshToken: ssoToken.refreshToken,\n        grantType: \"refresh_token\",\n    }));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdG9rZW4tcHJvdmlkZXJzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Rva2VuLXByb3ZpZGVycy9kaXN0LWVzL2dldE5ld1Nzb09pZGNUb2tlbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRDtBQUMvQyxnRUFBZ0U7QUFDdkUsWUFBWSxxQkFBcUIsUUFBUSwwVEFBMEM7QUFDbkYsZ0NBQWdDLG1FQUFnQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdG9rZW4tcHJvdmlkZXJzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Rva2VuLXByb3ZpZGVycy9kaXN0LWVzL2dldE5ld1Nzb09pZGNUb2tlbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRTc29PaWRjQ2xpZW50IH0gZnJvbSBcIi4vZ2V0U3NvT2lkY0NsaWVudFwiO1xuZXhwb3J0IGNvbnN0IGdldE5ld1Nzb09pZGNUb2tlbiA9IGFzeW5jIChzc29Ub2tlbiwgc3NvUmVnaW9uLCBpbml0ID0ge30pID0+IHtcbiAgICBjb25zdCB7IENyZWF0ZVRva2VuQ29tbWFuZCB9ID0gYXdhaXQgaW1wb3J0KFwiQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvc3NvLW9pZGNcIik7XG4gICAgY29uc3Qgc3NvT2lkY0NsaWVudCA9IGF3YWl0IGdldFNzb09pZGNDbGllbnQoc3NvUmVnaW9uLCBpbml0KTtcbiAgICByZXR1cm4gc3NvT2lkY0NsaWVudC5zZW5kKG5ldyBDcmVhdGVUb2tlbkNvbW1hbmQoe1xuICAgICAgICBjbGllbnRJZDogc3NvVG9rZW4uY2xpZW50SWQsXG4gICAgICAgIGNsaWVudFNlY3JldDogc3NvVG9rZW4uY2xpZW50U2VjcmV0LFxuICAgICAgICByZWZyZXNoVG9rZW46IHNzb1Rva2VuLnJlZnJlc2hUb2tlbixcbiAgICAgICAgZ3JhbnRUeXBlOiBcInJlZnJlc2hfdG9rZW5cIixcbiAgICB9KSk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/getNewSsoOidcToken.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/getSsoOidcClient.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/getSsoOidcClient.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSsoOidcClient: () => (/* binding */ getSsoOidcClient)\n/* harmony export */ });\nconst getSsoOidcClient = async (ssoRegion, init = {}) => {\n    const { SSOOIDCClient } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@aws-sdk+nested-clients@3.830.0\").then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/nested-clients/sso-oidc */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/index.js\"));\n    const ssoOidcClient = new SSOOIDCClient(Object.assign({}, init.clientConfig ?? {}, {\n        region: ssoRegion ?? init.clientConfig?.region,\n        logger: init.clientConfig?.logger ?? init.parentClientConfig?.logger,\n    }));\n    return ssoOidcClient;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdG9rZW4tcHJvdmlkZXJzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Rva2VuLXByb3ZpZGVycy9kaXN0LWVzL2dldFNzb09pZGNDbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLG9EQUFvRDtBQUMzRCxZQUFZLGdCQUFnQixRQUFRLDBUQUEwQztBQUM5RSw0REFBNEQseUJBQXlCO0FBQ3JGO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3Rva2VuLXByb3ZpZGVyc0AzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay90b2tlbi1wcm92aWRlcnMvZGlzdC1lcy9nZXRTc29PaWRjQ2xpZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBnZXRTc29PaWRjQ2xpZW50ID0gYXN5bmMgKHNzb1JlZ2lvbiwgaW5pdCA9IHt9KSA9PiB7XG4gICAgY29uc3QgeyBTU09PSURDQ2xpZW50IH0gPSBhd2FpdCBpbXBvcnQoXCJAYXdzLXNkay9uZXN0ZWQtY2xpZW50cy9zc28tb2lkY1wiKTtcbiAgICBjb25zdCBzc29PaWRjQ2xpZW50ID0gbmV3IFNTT09JRENDbGllbnQoT2JqZWN0LmFzc2lnbih7fSwgaW5pdC5jbGllbnRDb25maWcgPz8ge30sIHtcbiAgICAgICAgcmVnaW9uOiBzc29SZWdpb24gPz8gaW5pdC5jbGllbnRDb25maWc/LnJlZ2lvbixcbiAgICAgICAgbG9nZ2VyOiBpbml0LmNsaWVudENvbmZpZz8ubG9nZ2VyID8/IGluaXQucGFyZW50Q2xpZW50Q29uZmlnPy5sb2dnZXIsXG4gICAgfSkpO1xuICAgIHJldHVybiBzc29PaWRjQ2xpZW50O1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/getSsoOidcClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/validateTokenExpiry.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/validateTokenExpiry.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateTokenExpiry: () => (/* binding */ validateTokenExpiry)\n/* harmony export */ });\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/constants.js\");\n\n\nconst validateTokenExpiry = (token) => {\n    if (token.expiration && token.expiration.getTime() < Date.now()) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.TokenProviderError(`Token is expired. ${_constants__WEBPACK_IMPORTED_MODULE_1__.REFRESH_MESSAGE}`, false);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdG9rZW4tcHJvdmlkZXJzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Rva2VuLXByb3ZpZGVycy9kaXN0LWVzL3ZhbGlkYXRlVG9rZW5FeHBpcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStEO0FBQ2pCO0FBQ3ZDO0FBQ1A7QUFDQSxrQkFBa0IseUVBQWtCLHNCQUFzQix1REFBZSxDQUFDO0FBQzFFO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayt0b2tlbi1wcm92aWRlcnNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdG9rZW4tcHJvdmlkZXJzL2Rpc3QtZXMvdmFsaWRhdGVUb2tlbkV4cGlyeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUb2tlblByb3ZpZGVyRXJyb3IgfSBmcm9tIFwiQHNtaXRoeS9wcm9wZXJ0eS1wcm92aWRlclwiO1xuaW1wb3J0IHsgUkVGUkVTSF9NRVNTQUdFIH0gZnJvbSBcIi4vY29uc3RhbnRzXCI7XG5leHBvcnQgY29uc3QgdmFsaWRhdGVUb2tlbkV4cGlyeSA9ICh0b2tlbikgPT4ge1xuICAgIGlmICh0b2tlbi5leHBpcmF0aW9uICYmIHRva2VuLmV4cGlyYXRpb24uZ2V0VGltZSgpIDwgRGF0ZS5ub3coKSkge1xuICAgICAgICB0aHJvdyBuZXcgVG9rZW5Qcm92aWRlckVycm9yKGBUb2tlbiBpcyBleHBpcmVkLiAke1JFRlJFU0hfTUVTU0FHRX1gLCBmYWxzZSk7XG4gICAgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/validateTokenExpiry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/validateTokenKey.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/validateTokenKey.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateTokenKey: () => (/* binding */ validateTokenKey)\n/* harmony export */ });\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/constants.js\");\n\n\nconst validateTokenKey = (key, value, forRefresh = false) => {\n    if (typeof value === \"undefined\") {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.TokenProviderError(`Value not present for '${key}' in SSO Token${forRefresh ? \". Cannot refresh\" : \"\"}. ${_constants__WEBPACK_IMPORTED_MODULE_1__.REFRESH_MESSAGE}`, false);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdG9rZW4tcHJvdmlkZXJzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Rva2VuLXByb3ZpZGVycy9kaXN0LWVzL3ZhbGlkYXRlVG9rZW5LZXkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStEO0FBQ2pCO0FBQ3ZDO0FBQ1A7QUFDQSxrQkFBa0IseUVBQWtCLDJCQUEyQixJQUFJLGdCQUFnQixxQ0FBcUMsSUFBSSx1REFBZSxDQUFDO0FBQzVJO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayt0b2tlbi1wcm92aWRlcnNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdG9rZW4tcHJvdmlkZXJzL2Rpc3QtZXMvdmFsaWRhdGVUb2tlbktleS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUb2tlblByb3ZpZGVyRXJyb3IgfSBmcm9tIFwiQHNtaXRoeS9wcm9wZXJ0eS1wcm92aWRlclwiO1xuaW1wb3J0IHsgUkVGUkVTSF9NRVNTQUdFIH0gZnJvbSBcIi4vY29uc3RhbnRzXCI7XG5leHBvcnQgY29uc3QgdmFsaWRhdGVUb2tlbktleSA9IChrZXksIHZhbHVlLCBmb3JSZWZyZXNoID0gZmFsc2UpID0+IHtcbiAgICBpZiAodHlwZW9mIHZhbHVlID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgIHRocm93IG5ldyBUb2tlblByb3ZpZGVyRXJyb3IoYFZhbHVlIG5vdCBwcmVzZW50IGZvciAnJHtrZXl9JyBpbiBTU08gVG9rZW4ke2ZvclJlZnJlc2ggPyBcIi4gQ2Fubm90IHJlZnJlc2hcIiA6IFwiXCJ9LiAke1JFRlJFU0hfTUVTU0FHRX1gLCBmYWxzZSk7XG4gICAgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/validateTokenKey.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/writeSSOTokenToFile.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/writeSSOTokenToFile.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   writeSSOTokenToFile: () => (/* binding */ writeSSOTokenToFile)\n/* harmony export */ });\n/* harmony import */ var _smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/shared-ini-file-loader */ \"(rsc)/./node_modules/.pnpm/@smithy+shared-ini-file-loader@4.0.4/node_modules/@smithy/shared-ini-file-loader/dist-es/index.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst { writeFile } = fs__WEBPACK_IMPORTED_MODULE_1__.promises;\nconst writeSSOTokenToFile = (id, ssoToken) => {\n    const tokenFilepath = (0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_0__.getSSOTokenFilepath)(id);\n    const tokenString = JSON.stringify(ssoToken, null, 2);\n    return writeFile(tokenFilepath, tokenString);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdG9rZW4tcHJvdmlkZXJzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Rva2VuLXByb3ZpZGVycy9kaXN0LWVzL3dyaXRlU1NPVG9rZW5Ub0ZpbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFxRTtBQUN6QjtBQUM1QyxRQUFRLFlBQVksRUFBRSx3Q0FBVTtBQUN6QjtBQUNQLDBCQUEwQixtRkFBbUI7QUFDN0M7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdG9rZW4tcHJvdmlkZXJzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3Rva2VuLXByb3ZpZGVycy9kaXN0LWVzL3dyaXRlU1NPVG9rZW5Ub0ZpbGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0U1NPVG9rZW5GaWxlcGF0aCB9IGZyb20gXCJAc21pdGh5L3NoYXJlZC1pbmktZmlsZS1sb2FkZXJcIjtcbmltcG9ydCB7IHByb21pc2VzIGFzIGZzUHJvbWlzZXMgfSBmcm9tIFwiZnNcIjtcbmNvbnN0IHsgd3JpdGVGaWxlIH0gPSBmc1Byb21pc2VzO1xuZXhwb3J0IGNvbnN0IHdyaXRlU1NPVG9rZW5Ub0ZpbGUgPSAoaWQsIHNzb1Rva2VuKSA9PiB7XG4gICAgY29uc3QgdG9rZW5GaWxlcGF0aCA9IGdldFNTT1Rva2VuRmlsZXBhdGgoaWQpO1xuICAgIGNvbnN0IHRva2VuU3RyaW5nID0gSlNPTi5zdHJpbmdpZnkoc3NvVG9rZW4sIG51bGwsIDIpO1xuICAgIHJldHVybiB3cml0ZUZpbGUodG9rZW5GaWxlcGF0aCwgdG9rZW5TdHJpbmcpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/writeSSOTokenToFile.js\n");

/***/ })

};
;