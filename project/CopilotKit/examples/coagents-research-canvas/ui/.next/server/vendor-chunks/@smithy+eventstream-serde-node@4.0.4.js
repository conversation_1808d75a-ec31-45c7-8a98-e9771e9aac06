"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+eventstream-serde-node@4.0.4";
exports.ids = ["vendor-chunks/@smithy+eventstream-serde-node@4.0.4"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/EventStreamMarshaller.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/EventStreamMarshaller.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventStreamMarshaller: () => (/* binding */ EventStreamMarshaller)\n/* harmony export */ });\n/* harmony import */ var _smithy_eventstream_serde_universal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/eventstream-serde-universal */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-universal@4.0.4/node_modules/@smithy/eventstream-serde-universal/dist-es/index.js\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/utils.js\");\n\n\n\nclass EventStreamMarshaller {\n    constructor({ utf8Encoder, utf8Decoder }) {\n        this.universalMarshaller = new _smithy_eventstream_serde_universal__WEBPACK_IMPORTED_MODULE_0__.EventStreamMarshaller({\n            utf8Decoder,\n            utf8Encoder,\n        });\n    }\n    deserialize(body, deserializer) {\n        const bodyIterable = typeof body[Symbol.asyncIterator] === \"function\" ? body : (0,_utils__WEBPACK_IMPORTED_MODULE_2__.readabletoIterable)(body);\n        return this.universalMarshaller.deserialize(bodyIterable, deserializer);\n    }\n    serialize(input, serializer) {\n        return stream__WEBPACK_IMPORTED_MODULE_1__.Readable.from(this.universalMarshaller.serialize(input, serializer));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1zZXJkZS1ub2RlQDQuMC40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2V2ZW50c3RyZWFtLXNlcmRlLW5vZGUvZGlzdC1lcy9FdmVudFN0cmVhbU1hcnNoYWxsZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBOEc7QUFDNUU7QUFDVztBQUN0QztBQUNQLGtCQUFrQiwwQkFBMEI7QUFDNUMsdUNBQXVDLHNGQUE4QjtBQUNyRTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSx1RkFBdUYsMERBQWtCO0FBQ3pHO0FBQ0E7QUFDQTtBQUNBLGVBQWUsNENBQVE7QUFDdkI7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrZXZlbnRzdHJlYW0tc2VyZGUtbm9kZUA0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1zZXJkZS1ub2RlL2Rpc3QtZXMvRXZlbnRTdHJlYW1NYXJzaGFsbGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEV2ZW50U3RyZWFtTWFyc2hhbGxlciBhcyBVbml2ZXJzYWxFdmVudFN0cmVhbU1hcnNoYWxsZXIgfSBmcm9tIFwiQHNtaXRoeS9ldmVudHN0cmVhbS1zZXJkZS11bml2ZXJzYWxcIjtcbmltcG9ydCB7IFJlYWRhYmxlIH0gZnJvbSBcInN0cmVhbVwiO1xuaW1wb3J0IHsgcmVhZGFibGV0b0l0ZXJhYmxlIH0gZnJvbSBcIi4vdXRpbHNcIjtcbmV4cG9ydCBjbGFzcyBFdmVudFN0cmVhbU1hcnNoYWxsZXIge1xuICAgIGNvbnN0cnVjdG9yKHsgdXRmOEVuY29kZXIsIHV0ZjhEZWNvZGVyIH0pIHtcbiAgICAgICAgdGhpcy51bml2ZXJzYWxNYXJzaGFsbGVyID0gbmV3IFVuaXZlcnNhbEV2ZW50U3RyZWFtTWFyc2hhbGxlcih7XG4gICAgICAgICAgICB1dGY4RGVjb2RlcixcbiAgICAgICAgICAgIHV0ZjhFbmNvZGVyLFxuICAgICAgICB9KTtcbiAgICB9XG4gICAgZGVzZXJpYWxpemUoYm9keSwgZGVzZXJpYWxpemVyKSB7XG4gICAgICAgIGNvbnN0IGJvZHlJdGVyYWJsZSA9IHR5cGVvZiBib2R5W1N5bWJvbC5hc3luY0l0ZXJhdG9yXSA9PT0gXCJmdW5jdGlvblwiID8gYm9keSA6IHJlYWRhYmxldG9JdGVyYWJsZShib2R5KTtcbiAgICAgICAgcmV0dXJuIHRoaXMudW5pdmVyc2FsTWFyc2hhbGxlci5kZXNlcmlhbGl6ZShib2R5SXRlcmFibGUsIGRlc2VyaWFsaXplcik7XG4gICAgfVxuICAgIHNlcmlhbGl6ZShpbnB1dCwgc2VyaWFsaXplcikge1xuICAgICAgICByZXR1cm4gUmVhZGFibGUuZnJvbSh0aGlzLnVuaXZlcnNhbE1hcnNoYWxsZXIuc2VyaWFsaXplKGlucHV0LCBzZXJpYWxpemVyKSk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/EventStreamMarshaller.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/index.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/index.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventStreamMarshaller: () => (/* reexport safe */ _EventStreamMarshaller__WEBPACK_IMPORTED_MODULE_0__.EventStreamMarshaller),\n/* harmony export */   eventStreamSerdeProvider: () => (/* reexport safe */ _provider__WEBPACK_IMPORTED_MODULE_1__.eventStreamSerdeProvider)\n/* harmony export */ });\n/* harmony import */ var _EventStreamMarshaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EventStreamMarshaller */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/EventStreamMarshaller.js\");\n/* harmony import */ var _provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./provider */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/provider.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1zZXJkZS1ub2RlQDQuMC40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2V2ZW50c3RyZWFtLXNlcmRlLW5vZGUvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXdDO0FBQ2IiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2V2ZW50c3RyZWFtLXNlcmRlLW5vZGVANC4wLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvZXZlbnRzdHJlYW0tc2VyZGUtbm9kZS9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL0V2ZW50U3RyZWFtTWFyc2hhbGxlclwiO1xuZXhwb3J0ICogZnJvbSBcIi4vcHJvdmlkZXJcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/provider.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/provider.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventStreamSerdeProvider: () => (/* binding */ eventStreamSerdeProvider)\n/* harmony export */ });\n/* harmony import */ var _EventStreamMarshaller__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EventStreamMarshaller */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/EventStreamMarshaller.js\");\n\nconst eventStreamSerdeProvider = (options) => new _EventStreamMarshaller__WEBPACK_IMPORTED_MODULE_0__.EventStreamMarshaller(options);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1zZXJkZS1ub2RlQDQuMC40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2V2ZW50c3RyZWFtLXNlcmRlLW5vZGUvZGlzdC1lcy9wcm92aWRlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRTtBQUN6RCxrREFBa0QseUVBQXFCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1zZXJkZS1ub2RlQDQuMC40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2V2ZW50c3RyZWFtLXNlcmRlLW5vZGUvZGlzdC1lcy9wcm92aWRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFdmVudFN0cmVhbU1hcnNoYWxsZXIgfSBmcm9tIFwiLi9FdmVudFN0cmVhbU1hcnNoYWxsZXJcIjtcbmV4cG9ydCBjb25zdCBldmVudFN0cmVhbVNlcmRlUHJvdmlkZXIgPSAob3B0aW9ucykgPT4gbmV3IEV2ZW50U3RyZWFtTWFyc2hhbGxlcihvcHRpb25zKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/provider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/utils.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/utils.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   readabletoIterable: () => (/* binding */ readabletoIterable)\n/* harmony export */ });\nasync function* readabletoIterable(readStream) {\n    let streamEnded = false;\n    let generationEnded = false;\n    const records = new Array();\n    readStream.on(\"error\", (err) => {\n        if (!streamEnded) {\n            streamEnded = true;\n        }\n        if (err) {\n            throw err;\n        }\n    });\n    readStream.on(\"data\", (data) => {\n        records.push(data);\n    });\n    readStream.on(\"end\", () => {\n        streamEnded = true;\n    });\n    while (!generationEnded) {\n        const value = await new Promise((resolve) => setTimeout(() => resolve(records.shift()), 0));\n        if (value) {\n            yield value;\n        }\n        generationEnded = streamEnded && records.length === 0;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1zZXJkZS1ub2RlQDQuMC40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2V2ZW50c3RyZWFtLXNlcmRlLW5vZGUvZGlzdC1lcy91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrZXZlbnRzdHJlYW0tc2VyZGUtbm9kZUA0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1zZXJkZS1ub2RlL2Rpc3QtZXMvdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGFzeW5jIGZ1bmN0aW9uKiByZWFkYWJsZXRvSXRlcmFibGUocmVhZFN0cmVhbSkge1xuICAgIGxldCBzdHJlYW1FbmRlZCA9IGZhbHNlO1xuICAgIGxldCBnZW5lcmF0aW9uRW5kZWQgPSBmYWxzZTtcbiAgICBjb25zdCByZWNvcmRzID0gbmV3IEFycmF5KCk7XG4gICAgcmVhZFN0cmVhbS5vbihcImVycm9yXCIsIChlcnIpID0+IHtcbiAgICAgICAgaWYgKCFzdHJlYW1FbmRlZCkge1xuICAgICAgICAgICAgc3RyZWFtRW5kZWQgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgIHRocm93IGVycjtcbiAgICAgICAgfVxuICAgIH0pO1xuICAgIHJlYWRTdHJlYW0ub24oXCJkYXRhXCIsIChkYXRhKSA9PiB7XG4gICAgICAgIHJlY29yZHMucHVzaChkYXRhKTtcbiAgICB9KTtcbiAgICByZWFkU3RyZWFtLm9uKFwiZW5kXCIsICgpID0+IHtcbiAgICAgICAgc3RyZWFtRW5kZWQgPSB0cnVlO1xuICAgIH0pO1xuICAgIHdoaWxlICghZ2VuZXJhdGlvbkVuZGVkKSB7XG4gICAgICAgIGNvbnN0IHZhbHVlID0gYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQoKCkgPT4gcmVzb2x2ZShyZWNvcmRzLnNoaWZ0KCkpLCAwKSk7XG4gICAgICAgIGlmICh2YWx1ZSkge1xuICAgICAgICAgICAgeWllbGQgdmFsdWU7XG4gICAgICAgIH1cbiAgICAgICAgZ2VuZXJhdGlvbkVuZGVkID0gc3RyZWFtRW5kZWQgJiYgcmVjb3Jkcy5sZW5ndGggPT09IDA7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-node@4.0.4/node_modules/@smithy/eventstream-serde-node/dist-es/utils.js\n");

/***/ })

};
;