"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ag-ui+proto@0.0.28";
exports.ids = ["vendor-chunks/@ag-ui+proto@0.0.28"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@ag-ui+proto@0.0.28/node_modules/@ag-ui/proto/dist/index.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ag-ui+proto@0.0.28/node_modules/@ag-ui/proto/dist/index.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AGUI_MEDIA_TYPE: () => (/* binding */ AGUI_MEDIA_TYPE),\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\n/* harmony import */ var _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ag-ui/core */ \"(rsc)/./node_modules/.pnpm/@ag-ui+core@0.0.28/node_modules/@ag-ui/core/dist/index.mjs\");\n/* harmony import */ var _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @bufbuild/protobuf/wire */ \"(rsc)/./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/wire/binary-encoding.js\");\n// src/proto.ts\n\n\n// src/generated/events.ts\n\n\n// src/generated/google/protobuf/struct.ts\n\nfunction createBaseStruct() {\n  return { fields: {} };\n}\nvar Struct = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    Object.entries(message.fields).forEach(([key, value]) => {\n      if (value !== void 0) {\n        Struct_FieldsEntry.encode({ key, value }, writer.uint32(10).fork()).join();\n      }\n    });\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseStruct();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          const entry1 = Struct_FieldsEntry.decode(reader, reader.uint32());\n          if (entry1.value !== void 0) {\n            message.fields[entry1.key] = entry1.value;\n          }\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return Struct.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a;\n    const message = createBaseStruct();\n    message.fields = Object.entries((_a = object.fields) != null ? _a : {}).reduce(\n      (acc, [key, value]) => {\n        if (value !== void 0) {\n          acc[key] = value;\n        }\n        return acc;\n      },\n      {}\n    );\n    return message;\n  },\n  wrap(object) {\n    const struct = createBaseStruct();\n    if (object !== void 0) {\n      for (const key of Object.keys(object)) {\n        struct.fields[key] = object[key];\n      }\n    }\n    return struct;\n  },\n  unwrap(message) {\n    const object = {};\n    if (message.fields) {\n      for (const key of Object.keys(message.fields)) {\n        object[key] = message.fields[key];\n      }\n    }\n    return object;\n  }\n};\nfunction createBaseStruct_FieldsEntry() {\n  return { key: \"\", value: void 0 };\n}\nvar Struct_FieldsEntry = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.key !== \"\") {\n      writer.uint32(10).string(message.key);\n    }\n    if (message.value !== void 0) {\n      Value.encode(Value.wrap(message.value), writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseStruct_FieldsEntry();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.key = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.value = Value.unwrap(Value.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return Struct_FieldsEntry.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b;\n    const message = createBaseStruct_FieldsEntry();\n    message.key = (_a = object.key) != null ? _a : \"\";\n    message.value = (_b = object.value) != null ? _b : void 0;\n    return message;\n  }\n};\nfunction createBaseValue() {\n  return {\n    nullValue: void 0,\n    numberValue: void 0,\n    stringValue: void 0,\n    boolValue: void 0,\n    structValue: void 0,\n    listValue: void 0\n  };\n}\nvar Value = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.nullValue !== void 0) {\n      writer.uint32(8).int32(message.nullValue);\n    }\n    if (message.numberValue !== void 0) {\n      writer.uint32(17).double(message.numberValue);\n    }\n    if (message.stringValue !== void 0) {\n      writer.uint32(26).string(message.stringValue);\n    }\n    if (message.boolValue !== void 0) {\n      writer.uint32(32).bool(message.boolValue);\n    }\n    if (message.structValue !== void 0) {\n      Struct.encode(Struct.wrap(message.structValue), writer.uint32(42).fork()).join();\n    }\n    if (message.listValue !== void 0) {\n      ListValue.encode(ListValue.wrap(message.listValue), writer.uint32(50).fork()).join();\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseValue();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 8) {\n            break;\n          }\n          message.nullValue = reader.int32();\n          continue;\n        }\n        case 2: {\n          if (tag !== 17) {\n            break;\n          }\n          message.numberValue = reader.double();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.stringValue = reader.string();\n          continue;\n        }\n        case 4: {\n          if (tag !== 32) {\n            break;\n          }\n          message.boolValue = reader.bool();\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n          message.structValue = Struct.unwrap(Struct.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 6: {\n          if (tag !== 50) {\n            break;\n          }\n          message.listValue = ListValue.unwrap(ListValue.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return Value.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b, _c, _d, _e, _f;\n    const message = createBaseValue();\n    message.nullValue = (_a = object.nullValue) != null ? _a : void 0;\n    message.numberValue = (_b = object.numberValue) != null ? _b : void 0;\n    message.stringValue = (_c = object.stringValue) != null ? _c : void 0;\n    message.boolValue = (_d = object.boolValue) != null ? _d : void 0;\n    message.structValue = (_e = object.structValue) != null ? _e : void 0;\n    message.listValue = (_f = object.listValue) != null ? _f : void 0;\n    return message;\n  },\n  wrap(value) {\n    const result = createBaseValue();\n    if (value === null) {\n      result.nullValue = 0 /* NULL_VALUE */;\n    } else if (typeof value === \"boolean\") {\n      result.boolValue = value;\n    } else if (typeof value === \"number\") {\n      result.numberValue = value;\n    } else if (typeof value === \"string\") {\n      result.stringValue = value;\n    } else if (globalThis.Array.isArray(value)) {\n      result.listValue = value;\n    } else if (typeof value === \"object\") {\n      result.structValue = value;\n    } else if (typeof value !== \"undefined\") {\n      throw new globalThis.Error(\"Unsupported any value type: \" + typeof value);\n    }\n    return result;\n  },\n  unwrap(message) {\n    if (message.stringValue !== void 0) {\n      return message.stringValue;\n    } else if ((message == null ? void 0 : message.numberValue) !== void 0) {\n      return message.numberValue;\n    } else if ((message == null ? void 0 : message.boolValue) !== void 0) {\n      return message.boolValue;\n    } else if ((message == null ? void 0 : message.structValue) !== void 0) {\n      return message.structValue;\n    } else if ((message == null ? void 0 : message.listValue) !== void 0) {\n      return message.listValue;\n    } else if ((message == null ? void 0 : message.nullValue) !== void 0) {\n      return null;\n    }\n    return void 0;\n  }\n};\nfunction createBaseListValue() {\n  return { values: [] };\n}\nvar ListValue = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    for (const v of message.values) {\n      Value.encode(Value.wrap(v), writer.uint32(10).fork()).join();\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseListValue();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.values.push(Value.unwrap(Value.decode(reader, reader.uint32())));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return ListValue.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a;\n    const message = createBaseListValue();\n    message.values = ((_a = object.values) == null ? void 0 : _a.map((e) => e)) || [];\n    return message;\n  },\n  wrap(array) {\n    const result = createBaseListValue();\n    result.values = array != null ? array : [];\n    return result;\n  },\n  unwrap(message) {\n    if ((message == null ? void 0 : message.hasOwnProperty(\"values\")) && globalThis.Array.isArray(message.values)) {\n      return message.values;\n    } else {\n      return message;\n    }\n  }\n};\n\n// src/generated/patch.ts\n\nvar JsonPatchOperationType = /* @__PURE__ */ ((JsonPatchOperationType2) => {\n  JsonPatchOperationType2[JsonPatchOperationType2[\"ADD\"] = 0] = \"ADD\";\n  JsonPatchOperationType2[JsonPatchOperationType2[\"REMOVE\"] = 1] = \"REMOVE\";\n  JsonPatchOperationType2[JsonPatchOperationType2[\"REPLACE\"] = 2] = \"REPLACE\";\n  JsonPatchOperationType2[JsonPatchOperationType2[\"MOVE\"] = 3] = \"MOVE\";\n  JsonPatchOperationType2[JsonPatchOperationType2[\"COPY\"] = 4] = \"COPY\";\n  JsonPatchOperationType2[JsonPatchOperationType2[\"TEST\"] = 5] = \"TEST\";\n  JsonPatchOperationType2[JsonPatchOperationType2[\"UNRECOGNIZED\"] = -1] = \"UNRECOGNIZED\";\n  return JsonPatchOperationType2;\n})(JsonPatchOperationType || {});\nfunction createBaseJsonPatchOperation() {\n  return { op: 0, path: \"\", from: void 0, value: void 0 };\n}\nvar JsonPatchOperation = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.op !== 0) {\n      writer.uint32(8).int32(message.op);\n    }\n    if (message.path !== \"\") {\n      writer.uint32(18).string(message.path);\n    }\n    if (message.from !== void 0) {\n      writer.uint32(26).string(message.from);\n    }\n    if (message.value !== void 0) {\n      Value.encode(Value.wrap(message.value), writer.uint32(34).fork()).join();\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseJsonPatchOperation();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 8) {\n            break;\n          }\n          message.op = reader.int32();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.path = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.from = reader.string();\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n          message.value = Value.unwrap(Value.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return JsonPatchOperation.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b, _c, _d;\n    const message = createBaseJsonPatchOperation();\n    message.op = (_a = object.op) != null ? _a : 0;\n    message.path = (_b = object.path) != null ? _b : \"\";\n    message.from = (_c = object.from) != null ? _c : void 0;\n    message.value = (_d = object.value) != null ? _d : void 0;\n    return message;\n  }\n};\n\n// src/generated/types.ts\n\nfunction createBaseToolCall() {\n  return { id: \"\", type: \"\", function: void 0 };\n}\nvar ToolCall = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.id !== \"\") {\n      writer.uint32(10).string(message.id);\n    }\n    if (message.type !== \"\") {\n      writer.uint32(18).string(message.type);\n    }\n    if (message.function !== void 0) {\n      ToolCall_Function.encode(message.function, writer.uint32(26).fork()).join();\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseToolCall();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.id = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.type = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.function = ToolCall_Function.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return ToolCall.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b;\n    const message = createBaseToolCall();\n    message.id = (_a = object.id) != null ? _a : \"\";\n    message.type = (_b = object.type) != null ? _b : \"\";\n    message.function = object.function !== void 0 && object.function !== null ? ToolCall_Function.fromPartial(object.function) : void 0;\n    return message;\n  }\n};\nfunction createBaseToolCall_Function() {\n  return { name: \"\", arguments: \"\" };\n}\nvar ToolCall_Function = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.name !== \"\") {\n      writer.uint32(10).string(message.name);\n    }\n    if (message.arguments !== \"\") {\n      writer.uint32(18).string(message.arguments);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseToolCall_Function();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.name = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.arguments = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return ToolCall_Function.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b;\n    const message = createBaseToolCall_Function();\n    message.name = (_a = object.name) != null ? _a : \"\";\n    message.arguments = (_b = object.arguments) != null ? _b : \"\";\n    return message;\n  }\n};\nfunction createBaseMessage() {\n  return { id: \"\", role: \"\", content: void 0, name: void 0, toolCalls: [], toolCallId: void 0 };\n}\nvar Message = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.id !== \"\") {\n      writer.uint32(10).string(message.id);\n    }\n    if (message.role !== \"\") {\n      writer.uint32(18).string(message.role);\n    }\n    if (message.content !== void 0) {\n      writer.uint32(26).string(message.content);\n    }\n    if (message.name !== void 0) {\n      writer.uint32(34).string(message.name);\n    }\n    for (const v of message.toolCalls) {\n      ToolCall.encode(v, writer.uint32(42).fork()).join();\n    }\n    if (message.toolCallId !== void 0) {\n      writer.uint32(50).string(message.toolCallId);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.id = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.role = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.content = reader.string();\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n          message.name = reader.string();\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n          message.toolCalls.push(ToolCall.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 6: {\n          if (tag !== 50) {\n            break;\n          }\n          message.toolCallId = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return Message.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b, _c, _d, _e, _f;\n    const message = createBaseMessage();\n    message.id = (_a = object.id) != null ? _a : \"\";\n    message.role = (_b = object.role) != null ? _b : \"\";\n    message.content = (_c = object.content) != null ? _c : void 0;\n    message.name = (_d = object.name) != null ? _d : void 0;\n    message.toolCalls = ((_e = object.toolCalls) == null ? void 0 : _e.map((e) => ToolCall.fromPartial(e))) || [];\n    message.toolCallId = (_f = object.toolCallId) != null ? _f : void 0;\n    return message;\n  }\n};\n\n// src/generated/events.ts\nvar EventType = /* @__PURE__ */ ((EventType3) => {\n  EventType3[EventType3[\"TEXT_MESSAGE_START\"] = 0] = \"TEXT_MESSAGE_START\";\n  EventType3[EventType3[\"TEXT_MESSAGE_CONTENT\"] = 1] = \"TEXT_MESSAGE_CONTENT\";\n  EventType3[EventType3[\"TEXT_MESSAGE_END\"] = 2] = \"TEXT_MESSAGE_END\";\n  EventType3[EventType3[\"TOOL_CALL_START\"] = 3] = \"TOOL_CALL_START\";\n  EventType3[EventType3[\"TOOL_CALL_ARGS\"] = 4] = \"TOOL_CALL_ARGS\";\n  EventType3[EventType3[\"TOOL_CALL_END\"] = 5] = \"TOOL_CALL_END\";\n  EventType3[EventType3[\"STATE_SNAPSHOT\"] = 6] = \"STATE_SNAPSHOT\";\n  EventType3[EventType3[\"STATE_DELTA\"] = 7] = \"STATE_DELTA\";\n  EventType3[EventType3[\"MESSAGES_SNAPSHOT\"] = 8] = \"MESSAGES_SNAPSHOT\";\n  EventType3[EventType3[\"RAW\"] = 9] = \"RAW\";\n  EventType3[EventType3[\"CUSTOM\"] = 10] = \"CUSTOM\";\n  EventType3[EventType3[\"RUN_STARTED\"] = 11] = \"RUN_STARTED\";\n  EventType3[EventType3[\"RUN_FINISHED\"] = 12] = \"RUN_FINISHED\";\n  EventType3[EventType3[\"RUN_ERROR\"] = 13] = \"RUN_ERROR\";\n  EventType3[EventType3[\"STEP_STARTED\"] = 14] = \"STEP_STARTED\";\n  EventType3[EventType3[\"STEP_FINISHED\"] = 15] = \"STEP_FINISHED\";\n  EventType3[EventType3[\"UNRECOGNIZED\"] = -1] = \"UNRECOGNIZED\";\n  return EventType3;\n})(EventType || {});\nfunction createBaseBaseEvent() {\n  return { type: 0, timestamp: void 0, rawEvent: void 0 };\n}\nvar BaseEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.type !== 0) {\n      writer.uint32(8).int32(message.type);\n    }\n    if (message.timestamp !== void 0) {\n      writer.uint32(16).int64(message.timestamp);\n    }\n    if (message.rawEvent !== void 0) {\n      Value.encode(Value.wrap(message.rawEvent), writer.uint32(26).fork()).join();\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseBaseEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 8) {\n            break;\n          }\n          message.type = reader.int32();\n          continue;\n        }\n        case 2: {\n          if (tag !== 16) {\n            break;\n          }\n          message.timestamp = longToNumber(reader.int64());\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.rawEvent = Value.unwrap(Value.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return BaseEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b, _c;\n    const message = createBaseBaseEvent();\n    message.type = (_a = object.type) != null ? _a : 0;\n    message.timestamp = (_b = object.timestamp) != null ? _b : void 0;\n    message.rawEvent = (_c = object.rawEvent) != null ? _c : void 0;\n    return message;\n  }\n};\nfunction createBaseTextMessageStartEvent() {\n  return { baseEvent: void 0, messageId: \"\", role: void 0 };\n}\nvar TextMessageStartEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.messageId !== \"\") {\n      writer.uint32(18).string(message.messageId);\n    }\n    if (message.role !== void 0) {\n      writer.uint32(26).string(message.role);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseTextMessageStartEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.messageId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.role = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return TextMessageStartEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b;\n    const message = createBaseTextMessageStartEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.messageId = (_a = object.messageId) != null ? _a : \"\";\n    message.role = (_b = object.role) != null ? _b : void 0;\n    return message;\n  }\n};\nfunction createBaseTextMessageContentEvent() {\n  return { baseEvent: void 0, messageId: \"\", delta: \"\" };\n}\nvar TextMessageContentEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.messageId !== \"\") {\n      writer.uint32(18).string(message.messageId);\n    }\n    if (message.delta !== \"\") {\n      writer.uint32(26).string(message.delta);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseTextMessageContentEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.messageId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.delta = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return TextMessageContentEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b;\n    const message = createBaseTextMessageContentEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.messageId = (_a = object.messageId) != null ? _a : \"\";\n    message.delta = (_b = object.delta) != null ? _b : \"\";\n    return message;\n  }\n};\nfunction createBaseTextMessageEndEvent() {\n  return { baseEvent: void 0, messageId: \"\" };\n}\nvar TextMessageEndEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.messageId !== \"\") {\n      writer.uint32(18).string(message.messageId);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseTextMessageEndEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.messageId = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return TextMessageEndEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a;\n    const message = createBaseTextMessageEndEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.messageId = (_a = object.messageId) != null ? _a : \"\";\n    return message;\n  }\n};\nfunction createBaseToolCallStartEvent() {\n  return { baseEvent: void 0, toolCallId: \"\", toolCallName: \"\", parentMessageId: void 0 };\n}\nvar ToolCallStartEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.toolCallId !== \"\") {\n      writer.uint32(18).string(message.toolCallId);\n    }\n    if (message.toolCallName !== \"\") {\n      writer.uint32(26).string(message.toolCallName);\n    }\n    if (message.parentMessageId !== void 0) {\n      writer.uint32(34).string(message.parentMessageId);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseToolCallStartEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.toolCallId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.toolCallName = reader.string();\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n          message.parentMessageId = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return ToolCallStartEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b, _c;\n    const message = createBaseToolCallStartEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.toolCallId = (_a = object.toolCallId) != null ? _a : \"\";\n    message.toolCallName = (_b = object.toolCallName) != null ? _b : \"\";\n    message.parentMessageId = (_c = object.parentMessageId) != null ? _c : void 0;\n    return message;\n  }\n};\nfunction createBaseToolCallArgsEvent() {\n  return { baseEvent: void 0, toolCallId: \"\", delta: \"\" };\n}\nvar ToolCallArgsEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.toolCallId !== \"\") {\n      writer.uint32(18).string(message.toolCallId);\n    }\n    if (message.delta !== \"\") {\n      writer.uint32(26).string(message.delta);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseToolCallArgsEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.toolCallId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.delta = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return ToolCallArgsEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b;\n    const message = createBaseToolCallArgsEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.toolCallId = (_a = object.toolCallId) != null ? _a : \"\";\n    message.delta = (_b = object.delta) != null ? _b : \"\";\n    return message;\n  }\n};\nfunction createBaseToolCallEndEvent() {\n  return { baseEvent: void 0, toolCallId: \"\" };\n}\nvar ToolCallEndEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.toolCallId !== \"\") {\n      writer.uint32(18).string(message.toolCallId);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseToolCallEndEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.toolCallId = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return ToolCallEndEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a;\n    const message = createBaseToolCallEndEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.toolCallId = (_a = object.toolCallId) != null ? _a : \"\";\n    return message;\n  }\n};\nfunction createBaseStateSnapshotEvent() {\n  return { baseEvent: void 0, snapshot: void 0 };\n}\nvar StateSnapshotEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.snapshot !== void 0) {\n      Value.encode(Value.wrap(message.snapshot), writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseStateSnapshotEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.snapshot = Value.unwrap(Value.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return StateSnapshotEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a;\n    const message = createBaseStateSnapshotEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.snapshot = (_a = object.snapshot) != null ? _a : void 0;\n    return message;\n  }\n};\nfunction createBaseStateDeltaEvent() {\n  return { baseEvent: void 0, delta: [] };\n}\nvar StateDeltaEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    for (const v of message.delta) {\n      JsonPatchOperation.encode(v, writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseStateDeltaEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.delta.push(JsonPatchOperation.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return StateDeltaEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a;\n    const message = createBaseStateDeltaEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.delta = ((_a = object.delta) == null ? void 0 : _a.map((e) => JsonPatchOperation.fromPartial(e))) || [];\n    return message;\n  }\n};\nfunction createBaseMessagesSnapshotEvent() {\n  return { baseEvent: void 0, messages: [] };\n}\nvar MessagesSnapshotEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    for (const v of message.messages) {\n      Message.encode(v, writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseMessagesSnapshotEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.messages.push(Message.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return MessagesSnapshotEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a;\n    const message = createBaseMessagesSnapshotEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.messages = ((_a = object.messages) == null ? void 0 : _a.map((e) => Message.fromPartial(e))) || [];\n    return message;\n  }\n};\nfunction createBaseRawEvent() {\n  return { baseEvent: void 0, event: void 0, source: void 0 };\n}\nvar RawEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.event !== void 0) {\n      Value.encode(Value.wrap(message.event), writer.uint32(18).fork()).join();\n    }\n    if (message.source !== void 0) {\n      writer.uint32(26).string(message.source);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseRawEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.event = Value.unwrap(Value.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.source = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return RawEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b;\n    const message = createBaseRawEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.event = (_a = object.event) != null ? _a : void 0;\n    message.source = (_b = object.source) != null ? _b : void 0;\n    return message;\n  }\n};\nfunction createBaseCustomEvent() {\n  return { baseEvent: void 0, name: \"\", value: void 0 };\n}\nvar CustomEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.name !== \"\") {\n      writer.uint32(18).string(message.name);\n    }\n    if (message.value !== void 0) {\n      Value.encode(Value.wrap(message.value), writer.uint32(26).fork()).join();\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseCustomEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.name = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.value = Value.unwrap(Value.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return CustomEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b;\n    const message = createBaseCustomEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.name = (_a = object.name) != null ? _a : \"\";\n    message.value = (_b = object.value) != null ? _b : void 0;\n    return message;\n  }\n};\nfunction createBaseRunStartedEvent() {\n  return { baseEvent: void 0, threadId: \"\", runId: \"\" };\n}\nvar RunStartedEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.threadId !== \"\") {\n      writer.uint32(18).string(message.threadId);\n    }\n    if (message.runId !== \"\") {\n      writer.uint32(26).string(message.runId);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseRunStartedEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.threadId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.runId = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return RunStartedEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b;\n    const message = createBaseRunStartedEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.threadId = (_a = object.threadId) != null ? _a : \"\";\n    message.runId = (_b = object.runId) != null ? _b : \"\";\n    return message;\n  }\n};\nfunction createBaseRunFinishedEvent() {\n  return { baseEvent: void 0, threadId: \"\", runId: \"\" };\n}\nvar RunFinishedEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.threadId !== \"\") {\n      writer.uint32(18).string(message.threadId);\n    }\n    if (message.runId !== \"\") {\n      writer.uint32(26).string(message.runId);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseRunFinishedEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.threadId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.runId = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return RunFinishedEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b;\n    const message = createBaseRunFinishedEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.threadId = (_a = object.threadId) != null ? _a : \"\";\n    message.runId = (_b = object.runId) != null ? _b : \"\";\n    return message;\n  }\n};\nfunction createBaseRunErrorEvent() {\n  return { baseEvent: void 0, code: void 0, message: \"\" };\n}\nvar RunErrorEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.code !== void 0) {\n      writer.uint32(18).string(message.code);\n    }\n    if (message.message !== \"\") {\n      writer.uint32(26).string(message.message);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseRunErrorEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.code = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.message = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return RunErrorEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b;\n    const message = createBaseRunErrorEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.code = (_a = object.code) != null ? _a : void 0;\n    message.message = (_b = object.message) != null ? _b : \"\";\n    return message;\n  }\n};\nfunction createBaseStepStartedEvent() {\n  return { baseEvent: void 0, stepName: \"\" };\n}\nvar StepStartedEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.stepName !== \"\") {\n      writer.uint32(18).string(message.stepName);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseStepStartedEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.stepName = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return StepStartedEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a;\n    const message = createBaseStepStartedEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.stepName = (_a = object.stepName) != null ? _a : \"\";\n    return message;\n  }\n};\nfunction createBaseStepFinishedEvent() {\n  return { baseEvent: void 0, stepName: \"\" };\n}\nvar StepFinishedEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.stepName !== \"\") {\n      writer.uint32(18).string(message.stepName);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseStepFinishedEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.stepName = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return StepFinishedEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a;\n    const message = createBaseStepFinishedEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.stepName = (_a = object.stepName) != null ? _a : \"\";\n    return message;\n  }\n};\nfunction createBaseTextMessageChunkEvent() {\n  return { baseEvent: void 0, messageId: void 0, role: void 0, delta: void 0 };\n}\nvar TextMessageChunkEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.messageId !== void 0) {\n      writer.uint32(18).string(message.messageId);\n    }\n    if (message.role !== void 0) {\n      writer.uint32(26).string(message.role);\n    }\n    if (message.delta !== void 0) {\n      writer.uint32(34).string(message.delta);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseTextMessageChunkEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.messageId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.role = reader.string();\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n          message.delta = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return TextMessageChunkEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b, _c;\n    const message = createBaseTextMessageChunkEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.messageId = (_a = object.messageId) != null ? _a : void 0;\n    message.role = (_b = object.role) != null ? _b : void 0;\n    message.delta = (_c = object.delta) != null ? _c : void 0;\n    return message;\n  }\n};\nfunction createBaseToolCallChunkEvent() {\n  return {\n    baseEvent: void 0,\n    toolCallId: void 0,\n    toolCallName: void 0,\n    parentMessageId: void 0,\n    delta: void 0\n  };\n}\nvar ToolCallChunkEvent = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.baseEvent !== void 0) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.toolCallId !== void 0) {\n      writer.uint32(18).string(message.toolCallId);\n    }\n    if (message.toolCallName !== void 0) {\n      writer.uint32(26).string(message.toolCallName);\n    }\n    if (message.parentMessageId !== void 0) {\n      writer.uint32(34).string(message.parentMessageId);\n    }\n    if (message.delta !== void 0) {\n      writer.uint32(42).string(message.delta);\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseToolCallChunkEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.toolCallId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.toolCallName = reader.string();\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n          message.parentMessageId = reader.string();\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n          message.delta = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return ToolCallChunkEvent.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    var _a, _b, _c, _d;\n    const message = createBaseToolCallChunkEvent();\n    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;\n    message.toolCallId = (_a = object.toolCallId) != null ? _a : void 0;\n    message.toolCallName = (_b = object.toolCallName) != null ? _b : void 0;\n    message.parentMessageId = (_c = object.parentMessageId) != null ? _c : void 0;\n    message.delta = (_d = object.delta) != null ? _d : void 0;\n    return message;\n  }\n};\nfunction createBaseEvent() {\n  return {\n    textMessageStart: void 0,\n    textMessageContent: void 0,\n    textMessageEnd: void 0,\n    toolCallStart: void 0,\n    toolCallArgs: void 0,\n    toolCallEnd: void 0,\n    stateSnapshot: void 0,\n    stateDelta: void 0,\n    messagesSnapshot: void 0,\n    raw: void 0,\n    custom: void 0,\n    runStarted: void 0,\n    runFinished: void 0,\n    runError: void 0,\n    stepStarted: void 0,\n    stepFinished: void 0,\n    textMessageChunk: void 0,\n    toolCallChunk: void 0\n  };\n}\nvar Event = {\n  encode(message, writer = new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryWriter()) {\n    if (message.textMessageStart !== void 0) {\n      TextMessageStartEvent.encode(message.textMessageStart, writer.uint32(10).fork()).join();\n    }\n    if (message.textMessageContent !== void 0) {\n      TextMessageContentEvent.encode(message.textMessageContent, writer.uint32(18).fork()).join();\n    }\n    if (message.textMessageEnd !== void 0) {\n      TextMessageEndEvent.encode(message.textMessageEnd, writer.uint32(26).fork()).join();\n    }\n    if (message.toolCallStart !== void 0) {\n      ToolCallStartEvent.encode(message.toolCallStart, writer.uint32(34).fork()).join();\n    }\n    if (message.toolCallArgs !== void 0) {\n      ToolCallArgsEvent.encode(message.toolCallArgs, writer.uint32(42).fork()).join();\n    }\n    if (message.toolCallEnd !== void 0) {\n      ToolCallEndEvent.encode(message.toolCallEnd, writer.uint32(50).fork()).join();\n    }\n    if (message.stateSnapshot !== void 0) {\n      StateSnapshotEvent.encode(message.stateSnapshot, writer.uint32(58).fork()).join();\n    }\n    if (message.stateDelta !== void 0) {\n      StateDeltaEvent.encode(message.stateDelta, writer.uint32(66).fork()).join();\n    }\n    if (message.messagesSnapshot !== void 0) {\n      MessagesSnapshotEvent.encode(message.messagesSnapshot, writer.uint32(74).fork()).join();\n    }\n    if (message.raw !== void 0) {\n      RawEvent.encode(message.raw, writer.uint32(82).fork()).join();\n    }\n    if (message.custom !== void 0) {\n      CustomEvent.encode(message.custom, writer.uint32(90).fork()).join();\n    }\n    if (message.runStarted !== void 0) {\n      RunStartedEvent.encode(message.runStarted, writer.uint32(98).fork()).join();\n    }\n    if (message.runFinished !== void 0) {\n      RunFinishedEvent.encode(message.runFinished, writer.uint32(106).fork()).join();\n    }\n    if (message.runError !== void 0) {\n      RunErrorEvent.encode(message.runError, writer.uint32(114).fork()).join();\n    }\n    if (message.stepStarted !== void 0) {\n      StepStartedEvent.encode(message.stepStarted, writer.uint32(122).fork()).join();\n    }\n    if (message.stepFinished !== void 0) {\n      StepFinishedEvent.encode(message.stepFinished, writer.uint32(130).fork()).join();\n    }\n    if (message.textMessageChunk !== void 0) {\n      TextMessageChunkEvent.encode(message.textMessageChunk, writer.uint32(138).fork()).join();\n    }\n    if (message.toolCallChunk !== void 0) {\n      ToolCallChunkEvent.encode(message.toolCallChunk, writer.uint32(146).fork()).join();\n    }\n    return writer;\n  },\n  decode(input, length) {\n    const reader = input instanceof _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader ? input : new _bufbuild_protobuf_wire__WEBPACK_IMPORTED_MODULE_1__.BinaryReader(input);\n    let end = length === void 0 ? reader.len : reader.pos + length;\n    const message = createBaseEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n          message.textMessageStart = TextMessageStartEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n          message.textMessageContent = TextMessageContentEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n          message.textMessageEnd = TextMessageEndEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n          message.toolCallStart = ToolCallStartEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n          message.toolCallArgs = ToolCallArgsEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 6: {\n          if (tag !== 50) {\n            break;\n          }\n          message.toolCallEnd = ToolCallEndEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 7: {\n          if (tag !== 58) {\n            break;\n          }\n          message.stateSnapshot = StateSnapshotEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 8: {\n          if (tag !== 66) {\n            break;\n          }\n          message.stateDelta = StateDeltaEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 9: {\n          if (tag !== 74) {\n            break;\n          }\n          message.messagesSnapshot = MessagesSnapshotEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 10: {\n          if (tag !== 82) {\n            break;\n          }\n          message.raw = RawEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 11: {\n          if (tag !== 90) {\n            break;\n          }\n          message.custom = CustomEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 12: {\n          if (tag !== 98) {\n            break;\n          }\n          message.runStarted = RunStartedEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 13: {\n          if (tag !== 106) {\n            break;\n          }\n          message.runFinished = RunFinishedEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 14: {\n          if (tag !== 114) {\n            break;\n          }\n          message.runError = RunErrorEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 15: {\n          if (tag !== 122) {\n            break;\n          }\n          message.stepStarted = StepStartedEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 16: {\n          if (tag !== 130) {\n            break;\n          }\n          message.stepFinished = StepFinishedEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 17: {\n          if (tag !== 138) {\n            break;\n          }\n          message.textMessageChunk = TextMessageChunkEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 18: {\n          if (tag !== 146) {\n            break;\n          }\n          message.toolCallChunk = ToolCallChunkEvent.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n  create(base) {\n    return Event.fromPartial(base != null ? base : {});\n  },\n  fromPartial(object) {\n    const message = createBaseEvent();\n    message.textMessageStart = object.textMessageStart !== void 0 && object.textMessageStart !== null ? TextMessageStartEvent.fromPartial(object.textMessageStart) : void 0;\n    message.textMessageContent = object.textMessageContent !== void 0 && object.textMessageContent !== null ? TextMessageContentEvent.fromPartial(object.textMessageContent) : void 0;\n    message.textMessageEnd = object.textMessageEnd !== void 0 && object.textMessageEnd !== null ? TextMessageEndEvent.fromPartial(object.textMessageEnd) : void 0;\n    message.toolCallStart = object.toolCallStart !== void 0 && object.toolCallStart !== null ? ToolCallStartEvent.fromPartial(object.toolCallStart) : void 0;\n    message.toolCallArgs = object.toolCallArgs !== void 0 && object.toolCallArgs !== null ? ToolCallArgsEvent.fromPartial(object.toolCallArgs) : void 0;\n    message.toolCallEnd = object.toolCallEnd !== void 0 && object.toolCallEnd !== null ? ToolCallEndEvent.fromPartial(object.toolCallEnd) : void 0;\n    message.stateSnapshot = object.stateSnapshot !== void 0 && object.stateSnapshot !== null ? StateSnapshotEvent.fromPartial(object.stateSnapshot) : void 0;\n    message.stateDelta = object.stateDelta !== void 0 && object.stateDelta !== null ? StateDeltaEvent.fromPartial(object.stateDelta) : void 0;\n    message.messagesSnapshot = object.messagesSnapshot !== void 0 && object.messagesSnapshot !== null ? MessagesSnapshotEvent.fromPartial(object.messagesSnapshot) : void 0;\n    message.raw = object.raw !== void 0 && object.raw !== null ? RawEvent.fromPartial(object.raw) : void 0;\n    message.custom = object.custom !== void 0 && object.custom !== null ? CustomEvent.fromPartial(object.custom) : void 0;\n    message.runStarted = object.runStarted !== void 0 && object.runStarted !== null ? RunStartedEvent.fromPartial(object.runStarted) : void 0;\n    message.runFinished = object.runFinished !== void 0 && object.runFinished !== null ? RunFinishedEvent.fromPartial(object.runFinished) : void 0;\n    message.runError = object.runError !== void 0 && object.runError !== null ? RunErrorEvent.fromPartial(object.runError) : void 0;\n    message.stepStarted = object.stepStarted !== void 0 && object.stepStarted !== null ? StepStartedEvent.fromPartial(object.stepStarted) : void 0;\n    message.stepFinished = object.stepFinished !== void 0 && object.stepFinished !== null ? StepFinishedEvent.fromPartial(object.stepFinished) : void 0;\n    message.textMessageChunk = object.textMessageChunk !== void 0 && object.textMessageChunk !== null ? TextMessageChunkEvent.fromPartial(object.textMessageChunk) : void 0;\n    message.toolCallChunk = object.toolCallChunk !== void 0 && object.toolCallChunk !== null ? ToolCallChunkEvent.fromPartial(object.toolCallChunk) : void 0;\n    return message;\n  }\n};\nfunction longToNumber(int64) {\n  const num = globalThis.Number(int64.toString());\n  if (num > globalThis.Number.MAX_SAFE_INTEGER) {\n    throw new globalThis.Error(\"Value is larger than Number.MAX_SAFE_INTEGER\");\n  }\n  if (num < globalThis.Number.MIN_SAFE_INTEGER) {\n    throw new globalThis.Error(\"Value is smaller than Number.MIN_SAFE_INTEGER\");\n  }\n  return num;\n}\n\n// src/proto.ts\nfunction toCamelCase(str) {\n  return str.toLowerCase().replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());\n}\nfunction encode(event) {\n  const oneofField = toCamelCase(event.type);\n  const { type, timestamp, rawEvent, ...rest } = event;\n  if (type === _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.MESSAGES_SNAPSHOT) {\n    rest.messages = rest.messages.map((message) => {\n      const untypedMessage = message;\n      if (untypedMessage.toolCalls === void 0) {\n        return { ...message, toolCalls: [] };\n      }\n      return message;\n    });\n  }\n  if (type === _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STATE_DELTA) {\n    rest.delta = rest.delta.map((operation) => ({\n      ...operation,\n      op: JsonPatchOperationType[operation.op.toUpperCase()]\n    }));\n  }\n  const eventMessage = {\n    [oneofField]: {\n      baseEvent: {\n        type: EventType[event.type],\n        timestamp,\n        rawEvent\n      },\n      ...rest\n    }\n  };\n  return Event.encode(eventMessage).finish();\n}\nfunction decode(data) {\n  var _a;\n  const event = Event.decode(data);\n  const decoded = Object.values(event).find((value) => value !== void 0);\n  if (!decoded) {\n    throw new Error(\"Invalid event\");\n  }\n  decoded.type = EventType[decoded.baseEvent.type];\n  decoded.timestamp = decoded.baseEvent.timestamp;\n  decoded.rawEvent = decoded.baseEvent.rawEvent;\n  if (decoded.type === _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.MESSAGES_SNAPSHOT) {\n    for (const message of decoded.messages) {\n      const untypedMessage = message;\n      if (((_a = untypedMessage.toolCalls) == null ? void 0 : _a.length) === 0) {\n        untypedMessage.toolCalls = void 0;\n      }\n    }\n  }\n  if (decoded.type === _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventType.STATE_DELTA) {\n    for (const operation of decoded.delta) {\n      operation.op = JsonPatchOperationType[operation.op].toLowerCase();\n      Object.keys(operation).forEach((key) => {\n        if (operation[key] === void 0) {\n          delete operation[key];\n        }\n      });\n    }\n  }\n  Object.keys(decoded).forEach((key) => {\n    if (decoded[key] === void 0) {\n      delete decoded[key];\n    }\n  });\n  return _ag_ui_core__WEBPACK_IMPORTED_MODULE_0__.EventSchemas.parse(decoded);\n}\n\n// src/index.ts\nvar AGUI_MEDIA_TYPE = \"application/vnd.ag-ui.event+proto\";\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@ag-ui+proto@0.0.28/node_modules/@ag-ui/proto/dist/index.mjs\n");

/***/ })

};
;