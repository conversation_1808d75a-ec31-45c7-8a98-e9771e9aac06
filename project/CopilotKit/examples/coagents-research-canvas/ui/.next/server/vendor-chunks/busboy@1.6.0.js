"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/busboy@1.6.0";
exports.ids = ["vendor-chunks/busboy@1.6.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/index.js ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { parseContentType } = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/utils.js\");\n\nfunction getInstance(cfg) {\n  const headers = cfg.headers;\n  const conType = parseContentType(headers['content-type']);\n  if (!conType)\n    throw new Error('Malformed content type');\n\n  for (const type of TYPES) {\n    const matched = type.detect(conType);\n    if (!matched)\n      continue;\n\n    const instanceCfg = {\n      limits: cfg.limits,\n      headers,\n      conType,\n      highWaterMark: undefined,\n      fileHwm: undefined,\n      defCharset: undefined,\n      defParamCharset: undefined,\n      preservePath: false,\n    };\n    if (cfg.highWaterMark)\n      instanceCfg.highWaterMark = cfg.highWaterMark;\n    if (cfg.fileHwm)\n      instanceCfg.fileHwm = cfg.fileHwm;\n    instanceCfg.defCharset = cfg.defCharset;\n    instanceCfg.defParamCharset = cfg.defParamCharset;\n    instanceCfg.preservePath = cfg.preservePath;\n    return new type(instanceCfg);\n  }\n\n  throw new Error(`Unsupported content type: ${headers['content-type']}`);\n}\n\n// Note: types are explicitly listed here for easier bundling\n// See: https://github.com/mscdex/busboy/issues/121\nconst TYPES = [\n  __webpack_require__(/*! ./types/multipart */ \"(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/types/multipart.js\"),\n  __webpack_require__(/*! ./types/urlencoded */ \"(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/types/urlencoded.js\"),\n].filter(function(typemod) { return typeof typemod.detect === 'function'; });\n\nmodule.exports = (cfg) => {\n  if (typeof cfg !== 'object' || cfg === null)\n    cfg = {};\n\n  if (typeof cfg.headers !== 'object'\n      || cfg.headers === null\n      || typeof cfg.headers['content-type'] !== 'string') {\n    throw new Error('Missing Content-Type');\n  }\n\n  return getInstance(cfg);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/types/multipart.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/types/multipart.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { Readable, Writable } = __webpack_require__(/*! stream */ \"stream\");\n\nconst StreamSearch = __webpack_require__(/*! streamsearch */ \"(rsc)/./node_modules/.pnpm/streamsearch@1.1.0/node_modules/streamsearch/lib/sbmh.js\");\n\nconst {\n  basename,\n  convertToUTF8,\n  getDecoder,\n  parseContentType,\n  parseDisposition,\n} = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/utils.js\");\n\nconst BUF_CRLF = Buffer.from('\\r\\n');\nconst BUF_CR = Buffer.from('\\r');\nconst BUF_DASH = Buffer.from('-');\n\nfunction noop() {}\n\nconst MAX_HEADER_PAIRS = 2000; // From node\nconst MAX_HEADER_SIZE = 16 * 1024; // From node (its default value)\n\nconst HPARSER_NAME = 0;\nconst HPARSER_PRE_OWS = 1;\nconst HPARSER_VALUE = 2;\nclass HeaderParser {\n  constructor(cb) {\n    this.header = Object.create(null);\n    this.pairCount = 0;\n    this.byteCount = 0;\n    this.state = HPARSER_NAME;\n    this.name = '';\n    this.value = '';\n    this.crlf = 0;\n    this.cb = cb;\n  }\n\n  reset() {\n    this.header = Object.create(null);\n    this.pairCount = 0;\n    this.byteCount = 0;\n    this.state = HPARSER_NAME;\n    this.name = '';\n    this.value = '';\n    this.crlf = 0;\n  }\n\n  push(chunk, pos, end) {\n    let start = pos;\n    while (pos < end) {\n      switch (this.state) {\n        case HPARSER_NAME: {\n          let done = false;\n          for (; pos < end; ++pos) {\n            if (this.byteCount === MAX_HEADER_SIZE)\n              return -1;\n            ++this.byteCount;\n            const code = chunk[pos];\n            if (TOKEN[code] !== 1) {\n              if (code !== 58/* ':' */)\n                return -1;\n              this.name += chunk.latin1Slice(start, pos);\n              if (this.name.length === 0)\n                return -1;\n              ++pos;\n              done = true;\n              this.state = HPARSER_PRE_OWS;\n              break;\n            }\n          }\n          if (!done) {\n            this.name += chunk.latin1Slice(start, pos);\n            break;\n          }\n          // FALLTHROUGH\n        }\n        case HPARSER_PRE_OWS: {\n          // Skip optional whitespace\n          let done = false;\n          for (; pos < end; ++pos) {\n            if (this.byteCount === MAX_HEADER_SIZE)\n              return -1;\n            ++this.byteCount;\n            const code = chunk[pos];\n            if (code !== 32/* ' ' */ && code !== 9/* '\\t' */) {\n              start = pos;\n              done = true;\n              this.state = HPARSER_VALUE;\n              break;\n            }\n          }\n          if (!done)\n            break;\n          // FALLTHROUGH\n        }\n        case HPARSER_VALUE:\n          switch (this.crlf) {\n            case 0: // Nothing yet\n              for (; pos < end; ++pos) {\n                if (this.byteCount === MAX_HEADER_SIZE)\n                  return -1;\n                ++this.byteCount;\n                const code = chunk[pos];\n                if (FIELD_VCHAR[code] !== 1) {\n                  if (code !== 13/* '\\r' */)\n                    return -1;\n                  ++this.crlf;\n                  break;\n                }\n              }\n              this.value += chunk.latin1Slice(start, pos++);\n              break;\n            case 1: // Received CR\n              if (this.byteCount === MAX_HEADER_SIZE)\n                return -1;\n              ++this.byteCount;\n              if (chunk[pos++] !== 10/* '\\n' */)\n                return -1;\n              ++this.crlf;\n              break;\n            case 2: { // Received CR LF\n              if (this.byteCount === MAX_HEADER_SIZE)\n                return -1;\n              ++this.byteCount;\n              const code = chunk[pos];\n              if (code === 32/* ' ' */ || code === 9/* '\\t' */) {\n                // Folded value\n                start = pos;\n                this.crlf = 0;\n              } else {\n                if (++this.pairCount < MAX_HEADER_PAIRS) {\n                  this.name = this.name.toLowerCase();\n                  if (this.header[this.name] === undefined)\n                    this.header[this.name] = [this.value];\n                  else\n                    this.header[this.name].push(this.value);\n                }\n                if (code === 13/* '\\r' */) {\n                  ++this.crlf;\n                  ++pos;\n                } else {\n                  // Assume start of next header field name\n                  start = pos;\n                  this.crlf = 0;\n                  this.state = HPARSER_NAME;\n                  this.name = '';\n                  this.value = '';\n                }\n              }\n              break;\n            }\n            case 3: { // Received CR LF CR\n              if (this.byteCount === MAX_HEADER_SIZE)\n                return -1;\n              ++this.byteCount;\n              if (chunk[pos++] !== 10/* '\\n' */)\n                return -1;\n              // End of header\n              const header = this.header;\n              this.reset();\n              this.cb(header);\n              return pos;\n            }\n          }\n          break;\n      }\n    }\n\n    return pos;\n  }\n}\n\nclass FileStream extends Readable {\n  constructor(opts, owner) {\n    super(opts);\n    this.truncated = false;\n    this._readcb = null;\n    this.once('end', () => {\n      // We need to make sure that we call any outstanding _writecb() that is\n      // associated with this file so that processing of the rest of the form\n      // can continue. This may not happen if the file stream ends right after\n      // backpressure kicks in, so we force it here.\n      this._read();\n      if (--owner._fileEndsLeft === 0 && owner._finalcb) {\n        const cb = owner._finalcb;\n        owner._finalcb = null;\n        // Make sure other 'end' event handlers get a chance to be executed\n        // before busboy's 'finish' event is emitted\n        process.nextTick(cb);\n      }\n    });\n  }\n  _read(n) {\n    const cb = this._readcb;\n    if (cb) {\n      this._readcb = null;\n      cb();\n    }\n  }\n}\n\nconst ignoreData = {\n  push: (chunk, pos) => {},\n  destroy: () => {},\n};\n\nfunction callAndUnsetCb(self, err) {\n  const cb = self._writecb;\n  self._writecb = null;\n  if (err)\n    self.destroy(err);\n  else if (cb)\n    cb();\n}\n\nfunction nullDecoder(val, hint) {\n  return val;\n}\n\nclass Multipart extends Writable {\n  constructor(cfg) {\n    const streamOpts = {\n      autoDestroy: true,\n      emitClose: true,\n      highWaterMark: (typeof cfg.highWaterMark === 'number'\n                      ? cfg.highWaterMark\n                      : undefined),\n    };\n    super(streamOpts);\n\n    if (!cfg.conType.params || typeof cfg.conType.params.boundary !== 'string')\n      throw new Error('Multipart: Boundary not found');\n\n    const boundary = cfg.conType.params.boundary;\n    const paramDecoder = (typeof cfg.defParamCharset === 'string'\n                            && cfg.defParamCharset\n                          ? getDecoder(cfg.defParamCharset)\n                          : nullDecoder);\n    const defCharset = (cfg.defCharset || 'utf8');\n    const preservePath = cfg.preservePath;\n    const fileOpts = {\n      autoDestroy: true,\n      emitClose: true,\n      highWaterMark: (typeof cfg.fileHwm === 'number'\n                      ? cfg.fileHwm\n                      : undefined),\n    };\n\n    const limits = cfg.limits;\n    const fieldSizeLimit = (limits && typeof limits.fieldSize === 'number'\n                            ? limits.fieldSize\n                            : 1 * 1024 * 1024);\n    const fileSizeLimit = (limits && typeof limits.fileSize === 'number'\n                           ? limits.fileSize\n                           : Infinity);\n    const filesLimit = (limits && typeof limits.files === 'number'\n                        ? limits.files\n                        : Infinity);\n    const fieldsLimit = (limits && typeof limits.fields === 'number'\n                         ? limits.fields\n                         : Infinity);\n    const partsLimit = (limits && typeof limits.parts === 'number'\n                        ? limits.parts\n                        : Infinity);\n\n    let parts = -1; // Account for initial boundary\n    let fields = 0;\n    let files = 0;\n    let skipPart = false;\n\n    this._fileEndsLeft = 0;\n    this._fileStream = undefined;\n    this._complete = false;\n    let fileSize = 0;\n\n    let field;\n    let fieldSize = 0;\n    let partCharset;\n    let partEncoding;\n    let partType;\n    let partName;\n    let partTruncated = false;\n\n    let hitFilesLimit = false;\n    let hitFieldsLimit = false;\n\n    this._hparser = null;\n    const hparser = new HeaderParser((header) => {\n      this._hparser = null;\n      skipPart = false;\n\n      partType = 'text/plain';\n      partCharset = defCharset;\n      partEncoding = '7bit';\n      partName = undefined;\n      partTruncated = false;\n\n      let filename;\n      if (!header['content-disposition']) {\n        skipPart = true;\n        return;\n      }\n\n      const disp = parseDisposition(header['content-disposition'][0],\n                                    paramDecoder);\n      if (!disp || disp.type !== 'form-data') {\n        skipPart = true;\n        return;\n      }\n\n      if (disp.params) {\n        if (disp.params.name)\n          partName = disp.params.name;\n\n        if (disp.params['filename*'])\n          filename = disp.params['filename*'];\n        else if (disp.params.filename)\n          filename = disp.params.filename;\n\n        if (filename !== undefined && !preservePath)\n          filename = basename(filename);\n      }\n\n      if (header['content-type']) {\n        const conType = parseContentType(header['content-type'][0]);\n        if (conType) {\n          partType = `${conType.type}/${conType.subtype}`;\n          if (conType.params && typeof conType.params.charset === 'string')\n            partCharset = conType.params.charset.toLowerCase();\n        }\n      }\n\n      if (header['content-transfer-encoding'])\n        partEncoding = header['content-transfer-encoding'][0].toLowerCase();\n\n      if (partType === 'application/octet-stream' || filename !== undefined) {\n        // File\n\n        if (files === filesLimit) {\n          if (!hitFilesLimit) {\n            hitFilesLimit = true;\n            this.emit('filesLimit');\n          }\n          skipPart = true;\n          return;\n        }\n        ++files;\n\n        if (this.listenerCount('file') === 0) {\n          skipPart = true;\n          return;\n        }\n\n        fileSize = 0;\n        this._fileStream = new FileStream(fileOpts, this);\n        ++this._fileEndsLeft;\n        this.emit(\n          'file',\n          partName,\n          this._fileStream,\n          { filename,\n            encoding: partEncoding,\n            mimeType: partType }\n        );\n      } else {\n        // Non-file\n\n        if (fields === fieldsLimit) {\n          if (!hitFieldsLimit) {\n            hitFieldsLimit = true;\n            this.emit('fieldsLimit');\n          }\n          skipPart = true;\n          return;\n        }\n        ++fields;\n\n        if (this.listenerCount('field') === 0) {\n          skipPart = true;\n          return;\n        }\n\n        field = [];\n        fieldSize = 0;\n      }\n    });\n\n    let matchPostBoundary = 0;\n    const ssCb = (isMatch, data, start, end, isDataSafe) => {\nretrydata:\n      while (data) {\n        if (this._hparser !== null) {\n          const ret = this._hparser.push(data, start, end);\n          if (ret === -1) {\n            this._hparser = null;\n            hparser.reset();\n            this.emit('error', new Error('Malformed part header'));\n            break;\n          }\n          start = ret;\n        }\n\n        if (start === end)\n          break;\n\n        if (matchPostBoundary !== 0) {\n          if (matchPostBoundary === 1) {\n            switch (data[start]) {\n              case 45: // '-'\n                // Try matching '--' after boundary\n                matchPostBoundary = 2;\n                ++start;\n                break;\n              case 13: // '\\r'\n                // Try matching CR LF before header\n                matchPostBoundary = 3;\n                ++start;\n                break;\n              default:\n                matchPostBoundary = 0;\n            }\n            if (start === end)\n              return;\n          }\n\n          if (matchPostBoundary === 2) {\n            matchPostBoundary = 0;\n            if (data[start] === 45/* '-' */) {\n              // End of multipart data\n              this._complete = true;\n              this._bparser = ignoreData;\n              return;\n            }\n            // We saw something other than '-', so put the dash we consumed\n            // \"back\"\n            const writecb = this._writecb;\n            this._writecb = noop;\n            ssCb(false, BUF_DASH, 0, 1, false);\n            this._writecb = writecb;\n          } else if (matchPostBoundary === 3) {\n            matchPostBoundary = 0;\n            if (data[start] === 10/* '\\n' */) {\n              ++start;\n              if (parts >= partsLimit)\n                break;\n              // Prepare the header parser\n              this._hparser = hparser;\n              if (start === end)\n                break;\n              // Process the remaining data as a header\n              continue retrydata;\n            } else {\n              // We saw something other than LF, so put the CR we consumed\n              // \"back\"\n              const writecb = this._writecb;\n              this._writecb = noop;\n              ssCb(false, BUF_CR, 0, 1, false);\n              this._writecb = writecb;\n            }\n          }\n        }\n\n        if (!skipPart) {\n          if (this._fileStream) {\n            let chunk;\n            const actualLen = Math.min(end - start, fileSizeLimit - fileSize);\n            if (!isDataSafe) {\n              chunk = Buffer.allocUnsafe(actualLen);\n              data.copy(chunk, 0, start, start + actualLen);\n            } else {\n              chunk = data.slice(start, start + actualLen);\n            }\n\n            fileSize += chunk.length;\n            if (fileSize === fileSizeLimit) {\n              if (chunk.length > 0)\n                this._fileStream.push(chunk);\n              this._fileStream.emit('limit');\n              this._fileStream.truncated = true;\n              skipPart = true;\n            } else if (!this._fileStream.push(chunk)) {\n              if (this._writecb)\n                this._fileStream._readcb = this._writecb;\n              this._writecb = null;\n            }\n          } else if (field !== undefined) {\n            let chunk;\n            const actualLen = Math.min(\n              end - start,\n              fieldSizeLimit - fieldSize\n            );\n            if (!isDataSafe) {\n              chunk = Buffer.allocUnsafe(actualLen);\n              data.copy(chunk, 0, start, start + actualLen);\n            } else {\n              chunk = data.slice(start, start + actualLen);\n            }\n\n            fieldSize += actualLen;\n            field.push(chunk);\n            if (fieldSize === fieldSizeLimit) {\n              skipPart = true;\n              partTruncated = true;\n            }\n          }\n        }\n\n        break;\n      }\n\n      if (isMatch) {\n        matchPostBoundary = 1;\n\n        if (this._fileStream) {\n          // End the active file stream if the previous part was a file\n          this._fileStream.push(null);\n          this._fileStream = null;\n        } else if (field !== undefined) {\n          let data;\n          switch (field.length) {\n            case 0:\n              data = '';\n              break;\n            case 1:\n              data = convertToUTF8(field[0], partCharset, 0);\n              break;\n            default:\n              data = convertToUTF8(\n                Buffer.concat(field, fieldSize),\n                partCharset,\n                0\n              );\n          }\n          field = undefined;\n          fieldSize = 0;\n          this.emit(\n            'field',\n            partName,\n            data,\n            { nameTruncated: false,\n              valueTruncated: partTruncated,\n              encoding: partEncoding,\n              mimeType: partType }\n          );\n        }\n\n        if (++parts === partsLimit)\n          this.emit('partsLimit');\n      }\n    };\n    this._bparser = new StreamSearch(`\\r\\n--${boundary}`, ssCb);\n\n    this._writecb = null;\n    this._finalcb = null;\n\n    // Just in case there is no preamble\n    this.write(BUF_CRLF);\n  }\n\n  static detect(conType) {\n    return (conType.type === 'multipart' && conType.subtype === 'form-data');\n  }\n\n  _write(chunk, enc, cb) {\n    this._writecb = cb;\n    this._bparser.push(chunk, 0);\n    if (this._writecb)\n      callAndUnsetCb(this);\n  }\n\n  _destroy(err, cb) {\n    this._hparser = null;\n    this._bparser = ignoreData;\n    if (!err)\n      err = checkEndState(this);\n    const fileStream = this._fileStream;\n    if (fileStream) {\n      this._fileStream = null;\n      fileStream.destroy(err);\n    }\n    cb(err);\n  }\n\n  _final(cb) {\n    this._bparser.destroy();\n    if (!this._complete)\n      return cb(new Error('Unexpected end of form'));\n    if (this._fileEndsLeft)\n      this._finalcb = finalcb.bind(null, this, cb);\n    else\n      finalcb(this, cb);\n  }\n}\n\nfunction finalcb(self, cb, err) {\n  if (err)\n    return cb(err);\n  err = checkEndState(self);\n  cb(err);\n}\n\nfunction checkEndState(self) {\n  if (self._hparser)\n    return new Error('Malformed part header');\n  const fileStream = self._fileStream;\n  if (fileStream) {\n    self._fileStream = null;\n    fileStream.destroy(new Error('Unexpected end of file'));\n  }\n  if (!self._complete)\n    return new Error('Unexpected end of form');\n}\n\nconst TOKEN = [\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0,\n  0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n];\n\nconst FIELD_VCHAR = [\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n];\n\nmodule.exports = Multipart;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/types/multipart.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/types/urlencoded.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/types/urlencoded.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { Writable } = __webpack_require__(/*! stream */ \"stream\");\n\nconst { getDecoder } = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/utils.js\");\n\nclass URLEncoded extends Writable {\n  constructor(cfg) {\n    const streamOpts = {\n      autoDestroy: true,\n      emitClose: true,\n      highWaterMark: (typeof cfg.highWaterMark === 'number'\n                      ? cfg.highWaterMark\n                      : undefined),\n    };\n    super(streamOpts);\n\n    let charset = (cfg.defCharset || 'utf8');\n    if (cfg.conType.params && typeof cfg.conType.params.charset === 'string')\n      charset = cfg.conType.params.charset;\n\n    this.charset = charset;\n\n    const limits = cfg.limits;\n    this.fieldSizeLimit = (limits && typeof limits.fieldSize === 'number'\n                           ? limits.fieldSize\n                           : 1 * 1024 * 1024);\n    this.fieldsLimit = (limits && typeof limits.fields === 'number'\n                        ? limits.fields\n                        : Infinity);\n    this.fieldNameSizeLimit = (\n      limits && typeof limits.fieldNameSize === 'number'\n      ? limits.fieldNameSize\n      : 100\n    );\n\n    this._inKey = true;\n    this._keyTrunc = false;\n    this._valTrunc = false;\n    this._bytesKey = 0;\n    this._bytesVal = 0;\n    this._fields = 0;\n    this._key = '';\n    this._val = '';\n    this._byte = -2;\n    this._lastPos = 0;\n    this._encode = 0;\n    this._decoder = getDecoder(charset);\n  }\n\n  static detect(conType) {\n    return (conType.type === 'application'\n            && conType.subtype === 'x-www-form-urlencoded');\n  }\n\n  _write(chunk, enc, cb) {\n    if (this._fields >= this.fieldsLimit)\n      return cb();\n\n    let i = 0;\n    const len = chunk.length;\n    this._lastPos = 0;\n\n    // Check if we last ended mid-percent-encoded byte\n    if (this._byte !== -2) {\n      i = readPctEnc(this, chunk, i, len);\n      if (i === -1)\n        return cb(new Error('Malformed urlencoded form'));\n      if (i >= len)\n        return cb();\n      if (this._inKey)\n        ++this._bytesKey;\n      else\n        ++this._bytesVal;\n    }\n\nmain:\n    while (i < len) {\n      if (this._inKey) {\n        // Parsing key\n\n        i = skipKeyBytes(this, chunk, i, len);\n\n        while (i < len) {\n          switch (chunk[i]) {\n            case 61: // '='\n              if (this._lastPos < i)\n                this._key += chunk.latin1Slice(this._lastPos, i);\n              this._lastPos = ++i;\n              this._key = this._decoder(this._key, this._encode);\n              this._encode = 0;\n              this._inKey = false;\n              continue main;\n            case 38: // '&'\n              if (this._lastPos < i)\n                this._key += chunk.latin1Slice(this._lastPos, i);\n              this._lastPos = ++i;\n              this._key = this._decoder(this._key, this._encode);\n              this._encode = 0;\n              if (this._bytesKey > 0) {\n                this.emit(\n                  'field',\n                  this._key,\n                  '',\n                  { nameTruncated: this._keyTrunc,\n                    valueTruncated: false,\n                    encoding: this.charset,\n                    mimeType: 'text/plain' }\n                );\n              }\n              this._key = '';\n              this._val = '';\n              this._keyTrunc = false;\n              this._valTrunc = false;\n              this._bytesKey = 0;\n              this._bytesVal = 0;\n              if (++this._fields >= this.fieldsLimit) {\n                this.emit('fieldsLimit');\n                return cb();\n              }\n              continue;\n            case 43: // '+'\n              if (this._lastPos < i)\n                this._key += chunk.latin1Slice(this._lastPos, i);\n              this._key += ' ';\n              this._lastPos = i + 1;\n              break;\n            case 37: // '%'\n              if (this._encode === 0)\n                this._encode = 1;\n              if (this._lastPos < i)\n                this._key += chunk.latin1Slice(this._lastPos, i);\n              this._lastPos = i + 1;\n              this._byte = -1;\n              i = readPctEnc(this, chunk, i + 1, len);\n              if (i === -1)\n                return cb(new Error('Malformed urlencoded form'));\n              if (i >= len)\n                return cb();\n              ++this._bytesKey;\n              i = skipKeyBytes(this, chunk, i, len);\n              continue;\n          }\n          ++i;\n          ++this._bytesKey;\n          i = skipKeyBytes(this, chunk, i, len);\n        }\n        if (this._lastPos < i)\n          this._key += chunk.latin1Slice(this._lastPos, i);\n      } else {\n        // Parsing value\n\n        i = skipValBytes(this, chunk, i, len);\n\n        while (i < len) {\n          switch (chunk[i]) {\n            case 38: // '&'\n              if (this._lastPos < i)\n                this._val += chunk.latin1Slice(this._lastPos, i);\n              this._lastPos = ++i;\n              this._inKey = true;\n              this._val = this._decoder(this._val, this._encode);\n              this._encode = 0;\n              if (this._bytesKey > 0 || this._bytesVal > 0) {\n                this.emit(\n                  'field',\n                  this._key,\n                  this._val,\n                  { nameTruncated: this._keyTrunc,\n                    valueTruncated: this._valTrunc,\n                    encoding: this.charset,\n                    mimeType: 'text/plain' }\n                );\n              }\n              this._key = '';\n              this._val = '';\n              this._keyTrunc = false;\n              this._valTrunc = false;\n              this._bytesKey = 0;\n              this._bytesVal = 0;\n              if (++this._fields >= this.fieldsLimit) {\n                this.emit('fieldsLimit');\n                return cb();\n              }\n              continue main;\n            case 43: // '+'\n              if (this._lastPos < i)\n                this._val += chunk.latin1Slice(this._lastPos, i);\n              this._val += ' ';\n              this._lastPos = i + 1;\n              break;\n            case 37: // '%'\n              if (this._encode === 0)\n                this._encode = 1;\n              if (this._lastPos < i)\n                this._val += chunk.latin1Slice(this._lastPos, i);\n              this._lastPos = i + 1;\n              this._byte = -1;\n              i = readPctEnc(this, chunk, i + 1, len);\n              if (i === -1)\n                return cb(new Error('Malformed urlencoded form'));\n              if (i >= len)\n                return cb();\n              ++this._bytesVal;\n              i = skipValBytes(this, chunk, i, len);\n              continue;\n          }\n          ++i;\n          ++this._bytesVal;\n          i = skipValBytes(this, chunk, i, len);\n        }\n        if (this._lastPos < i)\n          this._val += chunk.latin1Slice(this._lastPos, i);\n      }\n    }\n\n    cb();\n  }\n\n  _final(cb) {\n    if (this._byte !== -2)\n      return cb(new Error('Malformed urlencoded form'));\n    if (!this._inKey || this._bytesKey > 0 || this._bytesVal > 0) {\n      if (this._inKey)\n        this._key = this._decoder(this._key, this._encode);\n      else\n        this._val = this._decoder(this._val, this._encode);\n      this.emit(\n        'field',\n        this._key,\n        this._val,\n        { nameTruncated: this._keyTrunc,\n          valueTruncated: this._valTrunc,\n          encoding: this.charset,\n          mimeType: 'text/plain' }\n      );\n    }\n    cb();\n  }\n}\n\nfunction readPctEnc(self, chunk, pos, len) {\n  if (pos >= len)\n    return len;\n\n  if (self._byte === -1) {\n    // We saw a '%' but no hex characters yet\n    const hexUpper = HEX_VALUES[chunk[pos++]];\n    if (hexUpper === -1)\n      return -1;\n\n    if (hexUpper >= 8)\n      self._encode = 2; // Indicate high bits detected\n\n    if (pos < len) {\n      // Both hex characters are in this chunk\n      const hexLower = HEX_VALUES[chunk[pos++]];\n      if (hexLower === -1)\n        return -1;\n\n      if (self._inKey)\n        self._key += String.fromCharCode((hexUpper << 4) + hexLower);\n      else\n        self._val += String.fromCharCode((hexUpper << 4) + hexLower);\n\n      self._byte = -2;\n      self._lastPos = pos;\n    } else {\n      // Only one hex character was available in this chunk\n      self._byte = hexUpper;\n    }\n  } else {\n    // We saw only one hex character so far\n    const hexLower = HEX_VALUES[chunk[pos++]];\n    if (hexLower === -1)\n      return -1;\n\n    if (self._inKey)\n      self._key += String.fromCharCode((self._byte << 4) + hexLower);\n    else\n      self._val += String.fromCharCode((self._byte << 4) + hexLower);\n\n    self._byte = -2;\n    self._lastPos = pos;\n  }\n\n  return pos;\n}\n\nfunction skipKeyBytes(self, chunk, pos, len) {\n  // Skip bytes if we've truncated\n  if (self._bytesKey > self.fieldNameSizeLimit) {\n    if (!self._keyTrunc) {\n      if (self._lastPos < pos)\n        self._key += chunk.latin1Slice(self._lastPos, pos - 1);\n    }\n    self._keyTrunc = true;\n    for (; pos < len; ++pos) {\n      const code = chunk[pos];\n      if (code === 61/* '=' */ || code === 38/* '&' */)\n        break;\n      ++self._bytesKey;\n    }\n    self._lastPos = pos;\n  }\n\n  return pos;\n}\n\nfunction skipValBytes(self, chunk, pos, len) {\n  // Skip bytes if we've truncated\n  if (self._bytesVal > self.fieldSizeLimit) {\n    if (!self._valTrunc) {\n      if (self._lastPos < pos)\n        self._val += chunk.latin1Slice(self._lastPos, pos - 1);\n    }\n    self._valTrunc = true;\n    for (; pos < len; ++pos) {\n      if (chunk[pos] === 38/* '&' */)\n        break;\n      ++self._bytesVal;\n    }\n    self._lastPos = pos;\n  }\n\n  return pos;\n}\n\n/* eslint-disable no-multi-spaces */\nconst HEX_VALUES = [\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n   0,  1,  2,  3,  4,  5,  6,  7,  8,  9, -1, -1, -1, -1, -1, -1,\n  -1, 10, 11, 12, 13, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, 10, 11, 12, 13, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n];\n/* eslint-enable no-multi-spaces */\n\nmodule.exports = URLEncoded;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/types/urlencoded.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/utils.js":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/utils.js ***!
  \**************************************************************************/
/***/ (function(module) {

eval("\n\nfunction parseContentType(str) {\n  if (str.length === 0)\n    return;\n\n  const params = Object.create(null);\n  let i = 0;\n\n  // Parse type\n  for (; i < str.length; ++i) {\n    const code = str.charCodeAt(i);\n    if (TOKEN[code] !== 1) {\n      if (code !== 47/* '/' */ || i === 0)\n        return;\n      break;\n    }\n  }\n  // Check for type without subtype\n  if (i === str.length)\n    return;\n\n  const type = str.slice(0, i).toLowerCase();\n\n  // Parse subtype\n  const subtypeStart = ++i;\n  for (; i < str.length; ++i) {\n    const code = str.charCodeAt(i);\n    if (TOKEN[code] !== 1) {\n      // Make sure we have a subtype\n      if (i === subtypeStart)\n        return;\n\n      if (parseContentTypeParams(str, i, params) === undefined)\n        return;\n      break;\n    }\n  }\n  // Make sure we have a subtype\n  if (i === subtypeStart)\n    return;\n\n  const subtype = str.slice(subtypeStart, i).toLowerCase();\n\n  return { type, subtype, params };\n}\n\nfunction parseContentTypeParams(str, i, params) {\n  while (i < str.length) {\n    // Consume whitespace\n    for (; i < str.length; ++i) {\n      const code = str.charCodeAt(i);\n      if (code !== 32/* ' ' */ && code !== 9/* '\\t' */)\n        break;\n    }\n\n    // Ended on whitespace\n    if (i === str.length)\n      break;\n\n    // Check for malformed parameter\n    if (str.charCodeAt(i++) !== 59/* ';' */)\n      return;\n\n    // Consume whitespace\n    for (; i < str.length; ++i) {\n      const code = str.charCodeAt(i);\n      if (code !== 32/* ' ' */ && code !== 9/* '\\t' */)\n        break;\n    }\n\n    // Ended on whitespace (malformed)\n    if (i === str.length)\n      return;\n\n    let name;\n    const nameStart = i;\n    // Parse parameter name\n    for (; i < str.length; ++i) {\n      const code = str.charCodeAt(i);\n      if (TOKEN[code] !== 1) {\n        if (code !== 61/* '=' */)\n          return;\n        break;\n      }\n    }\n\n    // No value (malformed)\n    if (i === str.length)\n      return;\n\n    name = str.slice(nameStart, i);\n    ++i; // Skip over '='\n\n    // No value (malformed)\n    if (i === str.length)\n      return;\n\n    let value = '';\n    let valueStart;\n    if (str.charCodeAt(i) === 34/* '\"' */) {\n      valueStart = ++i;\n      let escaping = false;\n      // Parse quoted value\n      for (; i < str.length; ++i) {\n        const code = str.charCodeAt(i);\n        if (code === 92/* '\\\\' */) {\n          if (escaping) {\n            valueStart = i;\n            escaping = false;\n          } else {\n            value += str.slice(valueStart, i);\n            escaping = true;\n          }\n          continue;\n        }\n        if (code === 34/* '\"' */) {\n          if (escaping) {\n            valueStart = i;\n            escaping = false;\n            continue;\n          }\n          value += str.slice(valueStart, i);\n          break;\n        }\n        if (escaping) {\n          valueStart = i - 1;\n          escaping = false;\n        }\n        // Invalid unescaped quoted character (malformed)\n        if (QDTEXT[code] !== 1)\n          return;\n      }\n\n      // No end quote (malformed)\n      if (i === str.length)\n        return;\n\n      ++i; // Skip over double quote\n    } else {\n      valueStart = i;\n      // Parse unquoted value\n      for (; i < str.length; ++i) {\n        const code = str.charCodeAt(i);\n        if (TOKEN[code] !== 1) {\n          // No value (malformed)\n          if (i === valueStart)\n            return;\n          break;\n        }\n      }\n      value = str.slice(valueStart, i);\n    }\n\n    name = name.toLowerCase();\n    if (params[name] === undefined)\n      params[name] = value;\n  }\n\n  return params;\n}\n\nfunction parseDisposition(str, defDecoder) {\n  if (str.length === 0)\n    return;\n\n  const params = Object.create(null);\n  let i = 0;\n\n  for (; i < str.length; ++i) {\n    const code = str.charCodeAt(i);\n    if (TOKEN[code] !== 1) {\n      if (parseDispositionParams(str, i, params, defDecoder) === undefined)\n        return;\n      break;\n    }\n  }\n\n  const type = str.slice(0, i).toLowerCase();\n\n  return { type, params };\n}\n\nfunction parseDispositionParams(str, i, params, defDecoder) {\n  while (i < str.length) {\n    // Consume whitespace\n    for (; i < str.length; ++i) {\n      const code = str.charCodeAt(i);\n      if (code !== 32/* ' ' */ && code !== 9/* '\\t' */)\n        break;\n    }\n\n    // Ended on whitespace\n    if (i === str.length)\n      break;\n\n    // Check for malformed parameter\n    if (str.charCodeAt(i++) !== 59/* ';' */)\n      return;\n\n    // Consume whitespace\n    for (; i < str.length; ++i) {\n      const code = str.charCodeAt(i);\n      if (code !== 32/* ' ' */ && code !== 9/* '\\t' */)\n        break;\n    }\n\n    // Ended on whitespace (malformed)\n    if (i === str.length)\n      return;\n\n    let name;\n    const nameStart = i;\n    // Parse parameter name\n    for (; i < str.length; ++i) {\n      const code = str.charCodeAt(i);\n      if (TOKEN[code] !== 1) {\n        if (code === 61/* '=' */)\n          break;\n        return;\n      }\n    }\n\n    // No value (malformed)\n    if (i === str.length)\n      return;\n\n    let value = '';\n    let valueStart;\n    let charset;\n    //~ let lang;\n    name = str.slice(nameStart, i);\n    if (name.charCodeAt(name.length - 1) === 42/* '*' */) {\n      // Extended value\n\n      const charsetStart = ++i;\n      // Parse charset name\n      for (; i < str.length; ++i) {\n        const code = str.charCodeAt(i);\n        if (CHARSET[code] !== 1) {\n          if (code !== 39/* '\\'' */)\n            return;\n          break;\n        }\n      }\n\n      // Incomplete charset (malformed)\n      if (i === str.length)\n        return;\n\n      charset = str.slice(charsetStart, i);\n      ++i; // Skip over the '\\''\n\n      //~ const langStart = ++i;\n      // Parse language name\n      for (; i < str.length; ++i) {\n        const code = str.charCodeAt(i);\n        if (code === 39/* '\\'' */)\n          break;\n      }\n\n      // Incomplete language (malformed)\n      if (i === str.length)\n        return;\n\n      //~ lang = str.slice(langStart, i);\n      ++i; // Skip over the '\\''\n\n      // No value (malformed)\n      if (i === str.length)\n        return;\n\n      valueStart = i;\n\n      let encode = 0;\n      // Parse value\n      for (; i < str.length; ++i) {\n        const code = str.charCodeAt(i);\n        if (EXTENDED_VALUE[code] !== 1) {\n          if (code === 37/* '%' */) {\n            let hexUpper;\n            let hexLower;\n            if (i + 2 < str.length\n                && (hexUpper = HEX_VALUES[str.charCodeAt(i + 1)]) !== -1\n                && (hexLower = HEX_VALUES[str.charCodeAt(i + 2)]) !== -1) {\n              const byteVal = (hexUpper << 4) + hexLower;\n              value += str.slice(valueStart, i);\n              value += String.fromCharCode(byteVal);\n              i += 2;\n              valueStart = i + 1;\n              if (byteVal >= 128)\n                encode = 2;\n              else if (encode === 0)\n                encode = 1;\n              continue;\n            }\n            // '%' disallowed in non-percent encoded contexts (malformed)\n            return;\n          }\n          break;\n        }\n      }\n\n      value += str.slice(valueStart, i);\n      value = convertToUTF8(value, charset, encode);\n      if (value === undefined)\n        return;\n    } else {\n      // Non-extended value\n\n      ++i; // Skip over '='\n\n      // No value (malformed)\n      if (i === str.length)\n        return;\n\n      if (str.charCodeAt(i) === 34/* '\"' */) {\n        valueStart = ++i;\n        let escaping = false;\n        // Parse quoted value\n        for (; i < str.length; ++i) {\n          const code = str.charCodeAt(i);\n          if (code === 92/* '\\\\' */) {\n            if (escaping) {\n              valueStart = i;\n              escaping = false;\n            } else {\n              value += str.slice(valueStart, i);\n              escaping = true;\n            }\n            continue;\n          }\n          if (code === 34/* '\"' */) {\n            if (escaping) {\n              valueStart = i;\n              escaping = false;\n              continue;\n            }\n            value += str.slice(valueStart, i);\n            break;\n          }\n          if (escaping) {\n            valueStart = i - 1;\n            escaping = false;\n          }\n          // Invalid unescaped quoted character (malformed)\n          if (QDTEXT[code] !== 1)\n            return;\n        }\n\n        // No end quote (malformed)\n        if (i === str.length)\n          return;\n\n        ++i; // Skip over double quote\n      } else {\n        valueStart = i;\n        // Parse unquoted value\n        for (; i < str.length; ++i) {\n          const code = str.charCodeAt(i);\n          if (TOKEN[code] !== 1) {\n            // No value (malformed)\n            if (i === valueStart)\n              return;\n            break;\n          }\n        }\n        value = str.slice(valueStart, i);\n      }\n\n      value = defDecoder(value, 2);\n      if (value === undefined)\n        return;\n    }\n\n    name = name.toLowerCase();\n    if (params[name] === undefined)\n      params[name] = value;\n  }\n\n  return params;\n}\n\nfunction getDecoder(charset) {\n  let lc;\n  while (true) {\n    switch (charset) {\n      case 'utf-8':\n      case 'utf8':\n        return decoders.utf8;\n      case 'latin1':\n      case 'ascii': // TODO: Make these a separate, strict decoder?\n      case 'us-ascii':\n      case 'iso-8859-1':\n      case 'iso8859-1':\n      case 'iso88591':\n      case 'iso_8859-1':\n      case 'windows-1252':\n      case 'iso_8859-1:1987':\n      case 'cp1252':\n      case 'x-cp1252':\n        return decoders.latin1;\n      case 'utf16le':\n      case 'utf-16le':\n      case 'ucs2':\n      case 'ucs-2':\n        return decoders.utf16le;\n      case 'base64':\n        return decoders.base64;\n      default:\n        if (lc === undefined) {\n          lc = true;\n          charset = charset.toLowerCase();\n          continue;\n        }\n        return decoders.other.bind(charset);\n    }\n  }\n}\n\nconst decoders = {\n  utf8: (data, hint) => {\n    if (data.length === 0)\n      return '';\n    if (typeof data === 'string') {\n      // If `data` never had any percent-encoded bytes or never had any that\n      // were outside of the ASCII range, then we can safely just return the\n      // input since UTF-8 is ASCII compatible\n      if (hint < 2)\n        return data;\n\n      data = Buffer.from(data, 'latin1');\n    }\n    return data.utf8Slice(0, data.length);\n  },\n\n  latin1: (data, hint) => {\n    if (data.length === 0)\n      return '';\n    if (typeof data === 'string')\n      return data;\n    return data.latin1Slice(0, data.length);\n  },\n\n  utf16le: (data, hint) => {\n    if (data.length === 0)\n      return '';\n    if (typeof data === 'string')\n      data = Buffer.from(data, 'latin1');\n    return data.ucs2Slice(0, data.length);\n  },\n\n  base64: (data, hint) => {\n    if (data.length === 0)\n      return '';\n    if (typeof data === 'string')\n      data = Buffer.from(data, 'latin1');\n    return data.base64Slice(0, data.length);\n  },\n\n  other: (data, hint) => {\n    if (data.length === 0)\n      return '';\n    if (typeof data === 'string')\n      data = Buffer.from(data, 'latin1');\n    try {\n      const decoder = new TextDecoder(this);\n      return decoder.decode(data);\n    } catch {}\n  },\n};\n\nfunction convertToUTF8(data, charset, hint) {\n  const decode = getDecoder(charset);\n  if (decode)\n    return decode(data, hint);\n}\n\nfunction basename(path) {\n  if (typeof path !== 'string')\n    return '';\n  for (let i = path.length - 1; i >= 0; --i) {\n    switch (path.charCodeAt(i)) {\n      case 0x2F: // '/'\n      case 0x5C: // '\\'\n        path = path.slice(i + 1);\n        return (path === '..' || path === '.' ? '' : path);\n    }\n  }\n  return (path === '..' || path === '.' ? '' : path);\n}\n\nconst TOKEN = [\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 1, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 1, 1, 0,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0,\n  0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n];\n\nconst QDTEXT = [\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n];\n\nconst CHARSET = [\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 1, 0, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0,\n  0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n];\n\nconst EXTENDED_VALUE = [\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 1, 0, 1, 1, 0, 1, 0, 0, 0, 0, 1, 0, 1, 1, 0,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0,\n  0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n];\n\n/* eslint-disable no-multi-spaces */\nconst HEX_VALUES = [\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n   0,  1,  2,  3,  4,  5,  6,  7,  8,  9, -1, -1, -1, -1, -1, -1,\n  -1, 10, 11, 12, 13, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, 10, 11, 12, 13, 14, 15, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n];\n/* eslint-enable no-multi-spaces */\n\nmodule.exports = {\n  basename,\n  convertToUTF8,\n  getDecoder,\n  parseContentType,\n  parseDisposition,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/utils.js\n");

/***/ })

};
;