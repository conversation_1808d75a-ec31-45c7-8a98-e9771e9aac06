"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@cfworker+json-schema@4.1.1";
exports.ids = ["vendor-chunks/@cfworker+json-schema@4.1.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/deep-compare-strict.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/deep-compare-strict.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.deepCompareStrict = deepCompareStrict;\nfunction deepCompareStrict(a, b) {\n    const typeofa = typeof a;\n    if (typeofa !== typeof b) {\n        return false;\n    }\n    if (Array.isArray(a)) {\n        if (!Array.isArray(b)) {\n            return false;\n        }\n        const length = a.length;\n        if (length !== b.length) {\n            return false;\n        }\n        for (let i = 0; i < length; i++) {\n            if (!deepCompareStrict(a[i], b[i])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    if (typeofa === 'object') {\n        if (!a || !b) {\n            return a === b;\n        }\n        const aKeys = Object.keys(a);\n        const bKeys = Object.keys(b);\n        const length = aKeys.length;\n        if (length !== bKeys.length) {\n            return false;\n        }\n        for (const k of aKeys) {\n            if (!deepCompareStrict(a[k], b[k])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    return a === b;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/deep-compare-strict.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/dereference.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/dereference.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.initialBaseURI = exports.ignoredKeyword = exports.schemaMapKeyword = exports.schemaArrayKeyword = exports.schemaKeyword = void 0;\nexports.dereference = dereference;\nconst pointer_js_1 = __webpack_require__(/*! ./pointer.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/pointer.js\");\nexports.schemaKeyword = {\n    additionalItems: true,\n    unevaluatedItems: true,\n    items: true,\n    contains: true,\n    additionalProperties: true,\n    unevaluatedProperties: true,\n    propertyNames: true,\n    not: true,\n    if: true,\n    then: true,\n    else: true\n};\nexports.schemaArrayKeyword = {\n    prefixItems: true,\n    items: true,\n    allOf: true,\n    anyOf: true,\n    oneOf: true\n};\nexports.schemaMapKeyword = {\n    $defs: true,\n    definitions: true,\n    properties: true,\n    patternProperties: true,\n    dependentSchemas: true\n};\nexports.ignoredKeyword = {\n    id: true,\n    $id: true,\n    $ref: true,\n    $schema: true,\n    $anchor: true,\n    $vocabulary: true,\n    $comment: true,\n    default: true,\n    enum: true,\n    const: true,\n    required: true,\n    type: true,\n    maximum: true,\n    minimum: true,\n    exclusiveMaximum: true,\n    exclusiveMinimum: true,\n    multipleOf: true,\n    maxLength: true,\n    minLength: true,\n    pattern: true,\n    format: true,\n    maxItems: true,\n    minItems: true,\n    uniqueItems: true,\n    maxProperties: true,\n    minProperties: true\n};\nexports.initialBaseURI = typeof self !== 'undefined' &&\n    self.location &&\n    self.location.origin !== 'null'\n    ?\n        new URL(self.location.origin + self.location.pathname + location.search)\n    : new URL('https://github.com/cfworker');\nfunction dereference(schema, lookup = Object.create(null), baseURI = exports.initialBaseURI, basePointer = '') {\n    if (schema && typeof schema === 'object' && !Array.isArray(schema)) {\n        const id = schema.$id || schema.id;\n        if (id) {\n            const url = new URL(id, baseURI.href);\n            if (url.hash.length > 1) {\n                lookup[url.href] = schema;\n            }\n            else {\n                url.hash = '';\n                if (basePointer === '') {\n                    baseURI = url;\n                }\n                else {\n                    dereference(schema, lookup, baseURI);\n                }\n            }\n        }\n    }\n    else if (schema !== true && schema !== false) {\n        return lookup;\n    }\n    const schemaURI = baseURI.href + (basePointer ? '#' + basePointer : '');\n    if (lookup[schemaURI] !== undefined) {\n        throw new Error(`Duplicate schema URI \"${schemaURI}\".`);\n    }\n    lookup[schemaURI] = schema;\n    if (schema === true || schema === false) {\n        return lookup;\n    }\n    if (schema.__absolute_uri__ === undefined) {\n        Object.defineProperty(schema, '__absolute_uri__', {\n            enumerable: false,\n            value: schemaURI\n        });\n    }\n    if (schema.$ref && schema.__absolute_ref__ === undefined) {\n        const url = new URL(schema.$ref, baseURI.href);\n        url.hash = url.hash;\n        Object.defineProperty(schema, '__absolute_ref__', {\n            enumerable: false,\n            value: url.href\n        });\n    }\n    if (schema.$recursiveRef && schema.__absolute_recursive_ref__ === undefined) {\n        const url = new URL(schema.$recursiveRef, baseURI.href);\n        url.hash = url.hash;\n        Object.defineProperty(schema, '__absolute_recursive_ref__', {\n            enumerable: false,\n            value: url.href\n        });\n    }\n    if (schema.$anchor) {\n        const url = new URL('#' + schema.$anchor, baseURI.href);\n        lookup[url.href] = schema;\n    }\n    for (let key in schema) {\n        if (exports.ignoredKeyword[key]) {\n            continue;\n        }\n        const keyBase = `${basePointer}/${(0, pointer_js_1.encodePointer)(key)}`;\n        const subSchema = schema[key];\n        if (Array.isArray(subSchema)) {\n            if (exports.schemaArrayKeyword[key]) {\n                const length = subSchema.length;\n                for (let i = 0; i < length; i++) {\n                    dereference(subSchema[i], lookup, baseURI, `${keyBase}/${i}`);\n                }\n            }\n        }\n        else if (exports.schemaMapKeyword[key]) {\n            for (let subKey in subSchema) {\n                dereference(subSchema[subKey], lookup, baseURI, `${keyBase}/${(0, pointer_js_1.encodePointer)(subKey)}`);\n            }\n        }\n        else {\n            dereference(subSchema, lookup, baseURI, keyBase);\n        }\n    }\n    return lookup;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/dereference.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/format.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/format.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.format = void 0;\nconst DATE = /^(\\d\\d\\d\\d)-(\\d\\d)-(\\d\\d)$/;\nconst DAYS = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nconst TIME = /^(\\d\\d):(\\d\\d):(\\d\\d)(\\.\\d+)?(z|[+-]\\d\\d(?::?\\d\\d)?)?$/i;\nconst HOSTNAME = /^(?=.{1,253}\\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\\.?$/i;\nconst URIREF = /^(?:[a-z][a-z0-9+\\-.]*:)?(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'\"()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*|\\/(?:(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\\?(?:[a-z0-9\\-._~!$&'\"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'\"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;\nconst URITEMPLATE = /^(?:(?:[^\\x00-\\x20\"'<>%\\\\^`{|}]|%[0-9a-f]{2})|\\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?)*\\})*$/i;\nconst URL_ = /^(?:(?:https?|ftp):\\/\\/)(?:\\S+(?::\\S*)?@)?(?:(?!10(?:\\.\\d{1,3}){3})(?!127(?:\\.\\d{1,3}){3})(?!169\\.254(?:\\.\\d{1,3}){2})(?!192\\.168(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u{00a1}-\\u{ffff}0-9]+-?)*[a-z\\u{00a1}-\\u{ffff}0-9]+)(?:\\.(?:[a-z\\u{00a1}-\\u{ffff}0-9]+-?)*[a-z\\u{00a1}-\\u{ffff}0-9]+)*(?:\\.(?:[a-z\\u{00a1}-\\u{ffff}]{2,})))(?::\\d{2,5})?(?:\\/[^\\s]*)?$/iu;\nconst UUID = /^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i;\nconst JSON_POINTER = /^(?:\\/(?:[^~/]|~0|~1)*)*$/;\nconst JSON_POINTER_URI_FRAGMENT = /^#(?:\\/(?:[a-z0-9_\\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i;\nconst RELATIVE_JSON_POINTER = /^(?:0|[1-9][0-9]*)(?:#|(?:\\/(?:[^~/]|~0|~1)*)*)$/;\nconst EMAIL = (input) => {\n    if (input[0] === '\"')\n        return false;\n    const [name, host, ...rest] = input.split('@');\n    if (!name ||\n        !host ||\n        rest.length !== 0 ||\n        name.length > 64 ||\n        host.length > 253)\n        return false;\n    if (name[0] === '.' || name.endsWith('.') || name.includes('..'))\n        return false;\n    if (!/^[a-z0-9.-]+$/i.test(host) ||\n        !/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+$/i.test(name))\n        return false;\n    return host\n        .split('.')\n        .every(part => /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$/i.test(part));\n};\nconst IPV4 = /^(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$/;\nconst IPV6 = /^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))$/i;\nconst DURATION = (input) => input.length > 1 &&\n    input.length < 80 &&\n    (/^P\\d+([.,]\\d+)?W$/.test(input) ||\n        (/^P[\\dYMDTHS]*(\\d[.,]\\d+)?[YMDHS]$/.test(input) &&\n            /^P([.,\\d]+Y)?([.,\\d]+M)?([.,\\d]+D)?(T([.,\\d]+H)?([.,\\d]+M)?([.,\\d]+S)?)?$/.test(input)));\nfunction bind(r) {\n    return r.test.bind(r);\n}\nexports.format = {\n    date,\n    time: time.bind(undefined, false),\n    'date-time': date_time,\n    duration: DURATION,\n    uri,\n    'uri-reference': bind(URIREF),\n    'uri-template': bind(URITEMPLATE),\n    url: bind(URL_),\n    email: EMAIL,\n    hostname: bind(HOSTNAME),\n    ipv4: bind(IPV4),\n    ipv6: bind(IPV6),\n    regex: regex,\n    uuid: bind(UUID),\n    'json-pointer': bind(JSON_POINTER),\n    'json-pointer-uri-fragment': bind(JSON_POINTER_URI_FRAGMENT),\n    'relative-json-pointer': bind(RELATIVE_JSON_POINTER)\n};\nfunction isLeapYear(year) {\n    return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\nfunction date(str) {\n    const matches = str.match(DATE);\n    if (!matches)\n        return false;\n    const year = +matches[1];\n    const month = +matches[2];\n    const day = +matches[3];\n    return (month >= 1 &&\n        month <= 12 &&\n        day >= 1 &&\n        day <= (month == 2 && isLeapYear(year) ? 29 : DAYS[month]));\n}\nfunction time(full, str) {\n    const matches = str.match(TIME);\n    if (!matches)\n        return false;\n    const hour = +matches[1];\n    const minute = +matches[2];\n    const second = +matches[3];\n    const timeZone = !!matches[5];\n    return (((hour <= 23 && minute <= 59 && second <= 59) ||\n        (hour == 23 && minute == 59 && second == 60)) &&\n        (!full || timeZone));\n}\nconst DATE_TIME_SEPARATOR = /t|\\s/i;\nfunction date_time(str) {\n    const dateTime = str.split(DATE_TIME_SEPARATOR);\n    return dateTime.length == 2 && date(dateTime[0]) && time(true, dateTime[1]);\n}\nconst NOT_URI_FRAGMENT = /\\/|:/;\nconst URI_PATTERN = /^(?:[a-z][a-z0-9+\\-.]*:)(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\\?(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;\nfunction uri(str) {\n    return NOT_URI_FRAGMENT.test(str) && URI_PATTERN.test(str);\n}\nconst Z_ANCHOR = /[^\\\\]\\\\Z/;\nfunction regex(str) {\n    if (Z_ANCHOR.test(str))\n        return false;\n    try {\n        new RegExp(str, 'u');\n        return true;\n    }\n    catch (e) {\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/format.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/index.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/index.js ***!
  \******************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./deep-compare-strict.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/deep-compare-strict.js\"), exports);\n__exportStar(__webpack_require__(/*! ./dereference.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/dereference.js\"), exports);\n__exportStar(__webpack_require__(/*! ./format.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/format.js\"), exports);\n__exportStar(__webpack_require__(/*! ./pointer.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/pointer.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./ucs2-length.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/ucs2-length.js\"), exports);\n__exportStar(__webpack_require__(/*! ./validate.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/validate.js\"), exports);\n__exportStar(__webpack_require__(/*! ./validator.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/validator.js\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/pointer.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/pointer.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.encodePointer = encodePointer;\nexports.escapePointer = escapePointer;\nfunction encodePointer(p) {\n    return encodeURI(escapePointer(p));\n}\nfunction escapePointer(p) {\n    return p.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNmd29ya2VyK2pzb24tc2NoZW1hQDQuMS4xL25vZGVfbW9kdWxlcy9AY2Z3b3JrZXIvanNvbi1zY2hlbWEvZGlzdC9jb21tb25qcy9wb2ludGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHFCQUFxQjtBQUNyQixxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGNmd29ya2VyK2pzb24tc2NoZW1hQDQuMS4xL25vZGVfbW9kdWxlcy9AY2Z3b3JrZXIvanNvbi1zY2hlbWEvZGlzdC9jb21tb25qcy9wb2ludGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5lbmNvZGVQb2ludGVyID0gZW5jb2RlUG9pbnRlcjtcbmV4cG9ydHMuZXNjYXBlUG9pbnRlciA9IGVzY2FwZVBvaW50ZXI7XG5mdW5jdGlvbiBlbmNvZGVQb2ludGVyKHApIHtcbiAgICByZXR1cm4gZW5jb2RlVVJJKGVzY2FwZVBvaW50ZXIocCkpO1xufVxuZnVuY3Rpb24gZXNjYXBlUG9pbnRlcihwKSB7XG4gICAgcmV0dXJuIHAucmVwbGFjZSgvfi9nLCAnfjAnKS5yZXBsYWNlKC9cXC8vZywgJ34xJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/pointer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/types.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/types.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.OutputFormat = void 0;\nvar OutputFormat;\n(function (OutputFormat) {\n    OutputFormat[OutputFormat[\"Flag\"] = 1] = \"Flag\";\n    OutputFormat[OutputFormat[\"Basic\"] = 2] = \"Basic\";\n    OutputFormat[OutputFormat[\"Detailed\"] = 4] = \"Detailed\";\n})(OutputFormat || (exports.OutputFormat = OutputFormat = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNmd29ya2VyK2pzb24tc2NoZW1hQDQuMS4xL25vZGVfbW9kdWxlcy9AY2Z3b3JrZXIvanNvbi1zY2hlbWEvZGlzdC9jb21tb25qcy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsbUJBQW1CLG9CQUFvQixvQkFBb0IiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AY2Z3b3JrZXIranNvbi1zY2hlbWFANC4xLjEvbm9kZV9tb2R1bGVzL0BjZndvcmtlci9qc29uLXNjaGVtYS9kaXN0L2NvbW1vbmpzL3R5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5PdXRwdXRGb3JtYXQgPSB2b2lkIDA7XG52YXIgT3V0cHV0Rm9ybWF0O1xuKGZ1bmN0aW9uIChPdXRwdXRGb3JtYXQpIHtcbiAgICBPdXRwdXRGb3JtYXRbT3V0cHV0Rm9ybWF0W1wiRmxhZ1wiXSA9IDFdID0gXCJGbGFnXCI7XG4gICAgT3V0cHV0Rm9ybWF0W091dHB1dEZvcm1hdFtcIkJhc2ljXCJdID0gMl0gPSBcIkJhc2ljXCI7XG4gICAgT3V0cHV0Rm9ybWF0W091dHB1dEZvcm1hdFtcIkRldGFpbGVkXCJdID0gNF0gPSBcIkRldGFpbGVkXCI7XG59KShPdXRwdXRGb3JtYXQgfHwgKGV4cG9ydHMuT3V0cHV0Rm9ybWF0ID0gT3V0cHV0Rm9ybWF0ID0ge30pKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/ucs2-length.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/ucs2-length.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ucs2length = ucs2length;\nfunction ucs2length(s) {\n    let result = 0;\n    let length = s.length;\n    let index = 0;\n    let charCode;\n    while (index < length) {\n        result++;\n        charCode = s.charCodeAt(index++);\n        if (charCode >= 0xd800 && charCode <= 0xdbff && index < length) {\n            charCode = s.charCodeAt(index);\n            if ((charCode & 0xfc00) == 0xdc00) {\n                index++;\n            }\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNmd29ya2VyK2pzb24tc2NoZW1hQDQuMS4xL25vZGVfbW9kdWxlcy9AY2Z3b3JrZXIvanNvbi1zY2hlbWEvZGlzdC9jb21tb25qcy91Y3MyLWxlbmd0aC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BjZndvcmtlcitqc29uLXNjaGVtYUA0LjEuMS9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvY29tbW9uanMvdWNzMi1sZW5ndGguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnVjczJsZW5ndGggPSB1Y3MybGVuZ3RoO1xuZnVuY3Rpb24gdWNzMmxlbmd0aChzKSB7XG4gICAgbGV0IHJlc3VsdCA9IDA7XG4gICAgbGV0IGxlbmd0aCA9IHMubGVuZ3RoO1xuICAgIGxldCBpbmRleCA9IDA7XG4gICAgbGV0IGNoYXJDb2RlO1xuICAgIHdoaWxlIChpbmRleCA8IGxlbmd0aCkge1xuICAgICAgICByZXN1bHQrKztcbiAgICAgICAgY2hhckNvZGUgPSBzLmNoYXJDb2RlQXQoaW5kZXgrKyk7XG4gICAgICAgIGlmIChjaGFyQ29kZSA+PSAweGQ4MDAgJiYgY2hhckNvZGUgPD0gMHhkYmZmICYmIGluZGV4IDwgbGVuZ3RoKSB7XG4gICAgICAgICAgICBjaGFyQ29kZSA9IHMuY2hhckNvZGVBdChpbmRleCk7XG4gICAgICAgICAgICBpZiAoKGNoYXJDb2RlICYgMHhmYzAwKSA9PSAweGRjMDApIHtcbiAgICAgICAgICAgICAgICBpbmRleCsrO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/ucs2-length.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/validate.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/validate.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.validate = validate;\nconst deep_compare_strict_js_1 = __webpack_require__(/*! ./deep-compare-strict.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/deep-compare-strict.js\");\nconst dereference_js_1 = __webpack_require__(/*! ./dereference.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/dereference.js\");\nconst format_js_1 = __webpack_require__(/*! ./format.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/format.js\");\nconst pointer_js_1 = __webpack_require__(/*! ./pointer.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/pointer.js\");\nconst ucs2_length_js_1 = __webpack_require__(/*! ./ucs2-length.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/ucs2-length.js\");\nfunction validate(instance, schema, draft = '2019-09', lookup = (0, dereference_js_1.dereference)(schema), shortCircuit = true, recursiveAnchor = null, instanceLocation = '#', schemaLocation = '#', evaluated = Object.create(null)) {\n    if (schema === true) {\n        return { valid: true, errors: [] };\n    }\n    if (schema === false) {\n        return {\n            valid: false,\n            errors: [\n                {\n                    instanceLocation,\n                    keyword: 'false',\n                    keywordLocation: instanceLocation,\n                    error: 'False boolean schema.'\n                }\n            ]\n        };\n    }\n    const rawInstanceType = typeof instance;\n    let instanceType;\n    switch (rawInstanceType) {\n        case 'boolean':\n        case 'number':\n        case 'string':\n            instanceType = rawInstanceType;\n            break;\n        case 'object':\n            if (instance === null) {\n                instanceType = 'null';\n            }\n            else if (Array.isArray(instance)) {\n                instanceType = 'array';\n            }\n            else {\n                instanceType = 'object';\n            }\n            break;\n        default:\n            throw new Error(`Instances of \"${rawInstanceType}\" type are not supported.`);\n    }\n    const { $ref, $recursiveRef, $recursiveAnchor, type: $type, const: $const, enum: $enum, required: $required, not: $not, anyOf: $anyOf, allOf: $allOf, oneOf: $oneOf, if: $if, then: $then, else: $else, format: $format, properties: $properties, patternProperties: $patternProperties, additionalProperties: $additionalProperties, unevaluatedProperties: $unevaluatedProperties, minProperties: $minProperties, maxProperties: $maxProperties, propertyNames: $propertyNames, dependentRequired: $dependentRequired, dependentSchemas: $dependentSchemas, dependencies: $dependencies, prefixItems: $prefixItems, items: $items, additionalItems: $additionalItems, unevaluatedItems: $unevaluatedItems, contains: $contains, minContains: $minContains, maxContains: $maxContains, minItems: $minItems, maxItems: $maxItems, uniqueItems: $uniqueItems, minimum: $minimum, maximum: $maximum, exclusiveMinimum: $exclusiveMinimum, exclusiveMaximum: $exclusiveMaximum, multipleOf: $multipleOf, minLength: $minLength, maxLength: $maxLength, pattern: $pattern, __absolute_ref__, __absolute_recursive_ref__ } = schema;\n    const errors = [];\n    if ($recursiveAnchor === true && recursiveAnchor === null) {\n        recursiveAnchor = schema;\n    }\n    if ($recursiveRef === '#') {\n        const refSchema = recursiveAnchor === null\n            ? lookup[__absolute_recursive_ref__]\n            : recursiveAnchor;\n        const keywordLocation = `${schemaLocation}/$recursiveRef`;\n        const result = validate(instance, recursiveAnchor === null ? schema : recursiveAnchor, draft, lookup, shortCircuit, refSchema, instanceLocation, keywordLocation, evaluated);\n        if (!result.valid) {\n            errors.push({\n                instanceLocation,\n                keyword: '$recursiveRef',\n                keywordLocation,\n                error: 'A subschema had errors.'\n            }, ...result.errors);\n        }\n    }\n    if ($ref !== undefined) {\n        const uri = __absolute_ref__ || $ref;\n        const refSchema = lookup[uri];\n        if (refSchema === undefined) {\n            let message = `Unresolved $ref \"${$ref}\".`;\n            if (__absolute_ref__ && __absolute_ref__ !== $ref) {\n                message += `  Absolute URI \"${__absolute_ref__}\".`;\n            }\n            message += `\\nKnown schemas:\\n- ${Object.keys(lookup).join('\\n- ')}`;\n            throw new Error(message);\n        }\n        const keywordLocation = `${schemaLocation}/$ref`;\n        const result = validate(instance, refSchema, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, keywordLocation, evaluated);\n        if (!result.valid) {\n            errors.push({\n                instanceLocation,\n                keyword: '$ref',\n                keywordLocation,\n                error: 'A subschema had errors.'\n            }, ...result.errors);\n        }\n        if (draft === '4' || draft === '7') {\n            return { valid: errors.length === 0, errors };\n        }\n    }\n    if (Array.isArray($type)) {\n        let length = $type.length;\n        let valid = false;\n        for (let i = 0; i < length; i++) {\n            if (instanceType === $type[i] ||\n                ($type[i] === 'integer' &&\n                    instanceType === 'number' &&\n                    instance % 1 === 0 &&\n                    instance === instance)) {\n                valid = true;\n                break;\n            }\n        }\n        if (!valid) {\n            errors.push({\n                instanceLocation,\n                keyword: 'type',\n                keywordLocation: `${schemaLocation}/type`,\n                error: `Instance type \"${instanceType}\" is invalid. Expected \"${$type.join('\", \"')}\".`\n            });\n        }\n    }\n    else if ($type === 'integer') {\n        if (instanceType !== 'number' || instance % 1 || instance !== instance) {\n            errors.push({\n                instanceLocation,\n                keyword: 'type',\n                keywordLocation: `${schemaLocation}/type`,\n                error: `Instance type \"${instanceType}\" is invalid. Expected \"${$type}\".`\n            });\n        }\n    }\n    else if ($type !== undefined && instanceType !== $type) {\n        errors.push({\n            instanceLocation,\n            keyword: 'type',\n            keywordLocation: `${schemaLocation}/type`,\n            error: `Instance type \"${instanceType}\" is invalid. Expected \"${$type}\".`\n        });\n    }\n    if ($const !== undefined) {\n        if (instanceType === 'object' || instanceType === 'array') {\n            if (!(0, deep_compare_strict_js_1.deepCompareStrict)(instance, $const)) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'const',\n                    keywordLocation: `${schemaLocation}/const`,\n                    error: `Instance does not match ${JSON.stringify($const)}.`\n                });\n            }\n        }\n        else if (instance !== $const) {\n            errors.push({\n                instanceLocation,\n                keyword: 'const',\n                keywordLocation: `${schemaLocation}/const`,\n                error: `Instance does not match ${JSON.stringify($const)}.`\n            });\n        }\n    }\n    if ($enum !== undefined) {\n        if (instanceType === 'object' || instanceType === 'array') {\n            if (!$enum.some(value => (0, deep_compare_strict_js_1.deepCompareStrict)(instance, value))) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'enum',\n                    keywordLocation: `${schemaLocation}/enum`,\n                    error: `Instance does not match any of ${JSON.stringify($enum)}.`\n                });\n            }\n        }\n        else if (!$enum.some(value => instance === value)) {\n            errors.push({\n                instanceLocation,\n                keyword: 'enum',\n                keywordLocation: `${schemaLocation}/enum`,\n                error: `Instance does not match any of ${JSON.stringify($enum)}.`\n            });\n        }\n    }\n    if ($not !== undefined) {\n        const keywordLocation = `${schemaLocation}/not`;\n        const result = validate(instance, $not, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, keywordLocation);\n        if (result.valid) {\n            errors.push({\n                instanceLocation,\n                keyword: 'not',\n                keywordLocation,\n                error: 'Instance matched \"not\" schema.'\n            });\n        }\n    }\n    let subEvaluateds = [];\n    if ($anyOf !== undefined) {\n        const keywordLocation = `${schemaLocation}/anyOf`;\n        const errorsLength = errors.length;\n        let anyValid = false;\n        for (let i = 0; i < $anyOf.length; i++) {\n            const subSchema = $anyOf[i];\n            const subEvaluated = Object.create(evaluated);\n            const result = validate(instance, subSchema, draft, lookup, shortCircuit, $recursiveAnchor === true ? recursiveAnchor : null, instanceLocation, `${keywordLocation}/${i}`, subEvaluated);\n            errors.push(...result.errors);\n            anyValid = anyValid || result.valid;\n            if (result.valid) {\n                subEvaluateds.push(subEvaluated);\n            }\n        }\n        if (anyValid) {\n            errors.length = errorsLength;\n        }\n        else {\n            errors.splice(errorsLength, 0, {\n                instanceLocation,\n                keyword: 'anyOf',\n                keywordLocation,\n                error: 'Instance does not match any subschemas.'\n            });\n        }\n    }\n    if ($allOf !== undefined) {\n        const keywordLocation = `${schemaLocation}/allOf`;\n        const errorsLength = errors.length;\n        let allValid = true;\n        for (let i = 0; i < $allOf.length; i++) {\n            const subSchema = $allOf[i];\n            const subEvaluated = Object.create(evaluated);\n            const result = validate(instance, subSchema, draft, lookup, shortCircuit, $recursiveAnchor === true ? recursiveAnchor : null, instanceLocation, `${keywordLocation}/${i}`, subEvaluated);\n            errors.push(...result.errors);\n            allValid = allValid && result.valid;\n            if (result.valid) {\n                subEvaluateds.push(subEvaluated);\n            }\n        }\n        if (allValid) {\n            errors.length = errorsLength;\n        }\n        else {\n            errors.splice(errorsLength, 0, {\n                instanceLocation,\n                keyword: 'allOf',\n                keywordLocation,\n                error: `Instance does not match every subschema.`\n            });\n        }\n    }\n    if ($oneOf !== undefined) {\n        const keywordLocation = `${schemaLocation}/oneOf`;\n        const errorsLength = errors.length;\n        const matches = $oneOf.filter((subSchema, i) => {\n            const subEvaluated = Object.create(evaluated);\n            const result = validate(instance, subSchema, draft, lookup, shortCircuit, $recursiveAnchor === true ? recursiveAnchor : null, instanceLocation, `${keywordLocation}/${i}`, subEvaluated);\n            errors.push(...result.errors);\n            if (result.valid) {\n                subEvaluateds.push(subEvaluated);\n            }\n            return result.valid;\n        }).length;\n        if (matches === 1) {\n            errors.length = errorsLength;\n        }\n        else {\n            errors.splice(errorsLength, 0, {\n                instanceLocation,\n                keyword: 'oneOf',\n                keywordLocation,\n                error: `Instance does not match exactly one subschema (${matches} matches).`\n            });\n        }\n    }\n    if (instanceType === 'object' || instanceType === 'array') {\n        Object.assign(evaluated, ...subEvaluateds);\n    }\n    if ($if !== undefined) {\n        const keywordLocation = `${schemaLocation}/if`;\n        const conditionResult = validate(instance, $if, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, keywordLocation, evaluated).valid;\n        if (conditionResult) {\n            if ($then !== undefined) {\n                const thenResult = validate(instance, $then, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${schemaLocation}/then`, evaluated);\n                if (!thenResult.valid) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'if',\n                        keywordLocation,\n                        error: `Instance does not match \"then\" schema.`\n                    }, ...thenResult.errors);\n                }\n            }\n        }\n        else if ($else !== undefined) {\n            const elseResult = validate(instance, $else, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${schemaLocation}/else`, evaluated);\n            if (!elseResult.valid) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'if',\n                    keywordLocation,\n                    error: `Instance does not match \"else\" schema.`\n                }, ...elseResult.errors);\n            }\n        }\n    }\n    if (instanceType === 'object') {\n        if ($required !== undefined) {\n            for (const key of $required) {\n                if (!(key in instance)) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'required',\n                        keywordLocation: `${schemaLocation}/required`,\n                        error: `Instance does not have required property \"${key}\".`\n                    });\n                }\n            }\n        }\n        const keys = Object.keys(instance);\n        if ($minProperties !== undefined && keys.length < $minProperties) {\n            errors.push({\n                instanceLocation,\n                keyword: 'minProperties',\n                keywordLocation: `${schemaLocation}/minProperties`,\n                error: `Instance does not have at least ${$minProperties} properties.`\n            });\n        }\n        if ($maxProperties !== undefined && keys.length > $maxProperties) {\n            errors.push({\n                instanceLocation,\n                keyword: 'maxProperties',\n                keywordLocation: `${schemaLocation}/maxProperties`,\n                error: `Instance does not have at least ${$maxProperties} properties.`\n            });\n        }\n        if ($propertyNames !== undefined) {\n            const keywordLocation = `${schemaLocation}/propertyNames`;\n            for (const key in instance) {\n                const subInstancePointer = `${instanceLocation}/${(0, pointer_js_1.encodePointer)(key)}`;\n                const result = validate(key, $propertyNames, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, keywordLocation);\n                if (!result.valid) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'propertyNames',\n                        keywordLocation,\n                        error: `Property name \"${key}\" does not match schema.`\n                    }, ...result.errors);\n                }\n            }\n        }\n        if ($dependentRequired !== undefined) {\n            const keywordLocation = `${schemaLocation}/dependantRequired`;\n            for (const key in $dependentRequired) {\n                if (key in instance) {\n                    const required = $dependentRequired[key];\n                    for (const dependantKey of required) {\n                        if (!(dependantKey in instance)) {\n                            errors.push({\n                                instanceLocation,\n                                keyword: 'dependentRequired',\n                                keywordLocation,\n                                error: `Instance has \"${key}\" but does not have \"${dependantKey}\".`\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if ($dependentSchemas !== undefined) {\n            for (const key in $dependentSchemas) {\n                const keywordLocation = `${schemaLocation}/dependentSchemas`;\n                if (key in instance) {\n                    const result = validate(instance, $dependentSchemas[key], draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${keywordLocation}/${(0, pointer_js_1.encodePointer)(key)}`, evaluated);\n                    if (!result.valid) {\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'dependentSchemas',\n                            keywordLocation,\n                            error: `Instance has \"${key}\" but does not match dependant schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n        if ($dependencies !== undefined) {\n            const keywordLocation = `${schemaLocation}/dependencies`;\n            for (const key in $dependencies) {\n                if (key in instance) {\n                    const propsOrSchema = $dependencies[key];\n                    if (Array.isArray(propsOrSchema)) {\n                        for (const dependantKey of propsOrSchema) {\n                            if (!(dependantKey in instance)) {\n                                errors.push({\n                                    instanceLocation,\n                                    keyword: 'dependencies',\n                                    keywordLocation,\n                                    error: `Instance has \"${key}\" but does not have \"${dependantKey}\".`\n                                });\n                            }\n                        }\n                    }\n                    else {\n                        const result = validate(instance, propsOrSchema, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${keywordLocation}/${(0, pointer_js_1.encodePointer)(key)}`);\n                        if (!result.valid) {\n                            errors.push({\n                                instanceLocation,\n                                keyword: 'dependencies',\n                                keywordLocation,\n                                error: `Instance has \"${key}\" but does not match dependant schema.`\n                            }, ...result.errors);\n                        }\n                    }\n                }\n            }\n        }\n        const thisEvaluated = Object.create(null);\n        let stop = false;\n        if ($properties !== undefined) {\n            const keywordLocation = `${schemaLocation}/properties`;\n            for (const key in $properties) {\n                if (!(key in instance)) {\n                    continue;\n                }\n                const subInstancePointer = `${instanceLocation}/${(0, pointer_js_1.encodePointer)(key)}`;\n                const result = validate(instance[key], $properties[key], draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, `${keywordLocation}/${(0, pointer_js_1.encodePointer)(key)}`);\n                if (result.valid) {\n                    evaluated[key] = thisEvaluated[key] = true;\n                }\n                else {\n                    stop = shortCircuit;\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'properties',\n                        keywordLocation,\n                        error: `Property \"${key}\" does not match schema.`\n                    }, ...result.errors);\n                    if (stop)\n                        break;\n                }\n            }\n        }\n        if (!stop && $patternProperties !== undefined) {\n            const keywordLocation = `${schemaLocation}/patternProperties`;\n            for (const pattern in $patternProperties) {\n                const regex = new RegExp(pattern, 'u');\n                const subSchema = $patternProperties[pattern];\n                for (const key in instance) {\n                    if (!regex.test(key)) {\n                        continue;\n                    }\n                    const subInstancePointer = `${instanceLocation}/${(0, pointer_js_1.encodePointer)(key)}`;\n                    const result = validate(instance[key], subSchema, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, `${keywordLocation}/${(0, pointer_js_1.encodePointer)(pattern)}`);\n                    if (result.valid) {\n                        evaluated[key] = thisEvaluated[key] = true;\n                    }\n                    else {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'patternProperties',\n                            keywordLocation,\n                            error: `Property \"${key}\" matches pattern \"${pattern}\" but does not match associated schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n        if (!stop && $additionalProperties !== undefined) {\n            const keywordLocation = `${schemaLocation}/additionalProperties`;\n            for (const key in instance) {\n                if (thisEvaluated[key]) {\n                    continue;\n                }\n                const subInstancePointer = `${instanceLocation}/${(0, pointer_js_1.encodePointer)(key)}`;\n                const result = validate(instance[key], $additionalProperties, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, keywordLocation);\n                if (result.valid) {\n                    evaluated[key] = true;\n                }\n                else {\n                    stop = shortCircuit;\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'additionalProperties',\n                        keywordLocation,\n                        error: `Property \"${key}\" does not match additional properties schema.`\n                    }, ...result.errors);\n                }\n            }\n        }\n        else if (!stop && $unevaluatedProperties !== undefined) {\n            const keywordLocation = `${schemaLocation}/unevaluatedProperties`;\n            for (const key in instance) {\n                if (!evaluated[key]) {\n                    const subInstancePointer = `${instanceLocation}/${(0, pointer_js_1.encodePointer)(key)}`;\n                    const result = validate(instance[key], $unevaluatedProperties, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, keywordLocation);\n                    if (result.valid) {\n                        evaluated[key] = true;\n                    }\n                    else {\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'unevaluatedProperties',\n                            keywordLocation,\n                            error: `Property \"${key}\" does not match unevaluated properties schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n    }\n    else if (instanceType === 'array') {\n        if ($maxItems !== undefined && instance.length > $maxItems) {\n            errors.push({\n                instanceLocation,\n                keyword: 'maxItems',\n                keywordLocation: `${schemaLocation}/maxItems`,\n                error: `Array has too many items (${instance.length} > ${$maxItems}).`\n            });\n        }\n        if ($minItems !== undefined && instance.length < $minItems) {\n            errors.push({\n                instanceLocation,\n                keyword: 'minItems',\n                keywordLocation: `${schemaLocation}/minItems`,\n                error: `Array has too few items (${instance.length} < ${$minItems}).`\n            });\n        }\n        const length = instance.length;\n        let i = 0;\n        let stop = false;\n        if ($prefixItems !== undefined) {\n            const keywordLocation = `${schemaLocation}/prefixItems`;\n            const length2 = Math.min($prefixItems.length, length);\n            for (; i < length2; i++) {\n                const result = validate(instance[i], $prefixItems[i], draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, `${keywordLocation}/${i}`);\n                evaluated[i] = true;\n                if (!result.valid) {\n                    stop = shortCircuit;\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'prefixItems',\n                        keywordLocation,\n                        error: `Items did not match schema.`\n                    }, ...result.errors);\n                    if (stop)\n                        break;\n                }\n            }\n        }\n        if ($items !== undefined) {\n            const keywordLocation = `${schemaLocation}/items`;\n            if (Array.isArray($items)) {\n                const length2 = Math.min($items.length, length);\n                for (; i < length2; i++) {\n                    const result = validate(instance[i], $items[i], draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, `${keywordLocation}/${i}`);\n                    evaluated[i] = true;\n                    if (!result.valid) {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'items',\n                            keywordLocation,\n                            error: `Items did not match schema.`\n                        }, ...result.errors);\n                        if (stop)\n                            break;\n                    }\n                }\n            }\n            else {\n                for (; i < length; i++) {\n                    const result = validate(instance[i], $items, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, keywordLocation);\n                    evaluated[i] = true;\n                    if (!result.valid) {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'items',\n                            keywordLocation,\n                            error: `Items did not match schema.`\n                        }, ...result.errors);\n                        if (stop)\n                            break;\n                    }\n                }\n            }\n            if (!stop && $additionalItems !== undefined) {\n                const keywordLocation = `${schemaLocation}/additionalItems`;\n                for (; i < length; i++) {\n                    const result = validate(instance[i], $additionalItems, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, keywordLocation);\n                    evaluated[i] = true;\n                    if (!result.valid) {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'additionalItems',\n                            keywordLocation,\n                            error: `Items did not match additional items schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n        if ($contains !== undefined) {\n            if (length === 0 && $minContains === undefined) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'contains',\n                    keywordLocation: `${schemaLocation}/contains`,\n                    error: `Array is empty. It must contain at least one item matching the schema.`\n                });\n            }\n            else if ($minContains !== undefined && length < $minContains) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'minContains',\n                    keywordLocation: `${schemaLocation}/minContains`,\n                    error: `Array has less items (${length}) than minContains (${$minContains}).`\n                });\n            }\n            else {\n                const keywordLocation = `${schemaLocation}/contains`;\n                const errorsLength = errors.length;\n                let contained = 0;\n                for (let j = 0; j < length; j++) {\n                    const result = validate(instance[j], $contains, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${j}`, keywordLocation);\n                    if (result.valid) {\n                        evaluated[j] = true;\n                        contained++;\n                    }\n                    else {\n                        errors.push(...result.errors);\n                    }\n                }\n                if (contained >= ($minContains || 0)) {\n                    errors.length = errorsLength;\n                }\n                if ($minContains === undefined &&\n                    $maxContains === undefined &&\n                    contained === 0) {\n                    errors.splice(errorsLength, 0, {\n                        instanceLocation,\n                        keyword: 'contains',\n                        keywordLocation,\n                        error: `Array does not contain item matching schema.`\n                    });\n                }\n                else if ($minContains !== undefined && contained < $minContains) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'minContains',\n                        keywordLocation: `${schemaLocation}/minContains`,\n                        error: `Array must contain at least ${$minContains} items matching schema. Only ${contained} items were found.`\n                    });\n                }\n                else if ($maxContains !== undefined && contained > $maxContains) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'maxContains',\n                        keywordLocation: `${schemaLocation}/maxContains`,\n                        error: `Array may contain at most ${$maxContains} items matching schema. ${contained} items were found.`\n                    });\n                }\n            }\n        }\n        if (!stop && $unevaluatedItems !== undefined) {\n            const keywordLocation = `${schemaLocation}/unevaluatedItems`;\n            for (i; i < length; i++) {\n                if (evaluated[i]) {\n                    continue;\n                }\n                const result = validate(instance[i], $unevaluatedItems, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, keywordLocation);\n                evaluated[i] = true;\n                if (!result.valid) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'unevaluatedItems',\n                        keywordLocation,\n                        error: `Items did not match unevaluated items schema.`\n                    }, ...result.errors);\n                }\n            }\n        }\n        if ($uniqueItems) {\n            for (let j = 0; j < length; j++) {\n                const a = instance[j];\n                const ao = typeof a === 'object' && a !== null;\n                for (let k = 0; k < length; k++) {\n                    if (j === k) {\n                        continue;\n                    }\n                    const b = instance[k];\n                    const bo = typeof b === 'object' && b !== null;\n                    if (a === b || (ao && bo && (0, deep_compare_strict_js_1.deepCompareStrict)(a, b))) {\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'uniqueItems',\n                            keywordLocation: `${schemaLocation}/uniqueItems`,\n                            error: `Duplicate items at indexes ${j} and ${k}.`\n                        });\n                        j = Number.MAX_SAFE_INTEGER;\n                        k = Number.MAX_SAFE_INTEGER;\n                    }\n                }\n            }\n        }\n    }\n    else if (instanceType === 'number') {\n        if (draft === '4') {\n            if ($minimum !== undefined &&\n                (($exclusiveMinimum === true && instance <= $minimum) ||\n                    instance < $minimum)) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'minimum',\n                    keywordLocation: `${schemaLocation}/minimum`,\n                    error: `${instance} is less than ${$exclusiveMinimum ? 'or equal to ' : ''} ${$minimum}.`\n                });\n            }\n            if ($maximum !== undefined &&\n                (($exclusiveMaximum === true && instance >= $maximum) ||\n                    instance > $maximum)) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'maximum',\n                    keywordLocation: `${schemaLocation}/maximum`,\n                    error: `${instance} is greater than ${$exclusiveMaximum ? 'or equal to ' : ''} ${$maximum}.`\n                });\n            }\n        }\n        else {\n            if ($minimum !== undefined && instance < $minimum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'minimum',\n                    keywordLocation: `${schemaLocation}/minimum`,\n                    error: `${instance} is less than ${$minimum}.`\n                });\n            }\n            if ($maximum !== undefined && instance > $maximum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'maximum',\n                    keywordLocation: `${schemaLocation}/maximum`,\n                    error: `${instance} is greater than ${$maximum}.`\n                });\n            }\n            if ($exclusiveMinimum !== undefined && instance <= $exclusiveMinimum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'exclusiveMinimum',\n                    keywordLocation: `${schemaLocation}/exclusiveMinimum`,\n                    error: `${instance} is less than ${$exclusiveMinimum}.`\n                });\n            }\n            if ($exclusiveMaximum !== undefined && instance >= $exclusiveMaximum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'exclusiveMaximum',\n                    keywordLocation: `${schemaLocation}/exclusiveMaximum`,\n                    error: `${instance} is greater than or equal to ${$exclusiveMaximum}.`\n                });\n            }\n        }\n        if ($multipleOf !== undefined) {\n            const remainder = instance % $multipleOf;\n            if (Math.abs(0 - remainder) >= 1.1920929e-7 &&\n                Math.abs($multipleOf - remainder) >= 1.1920929e-7) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'multipleOf',\n                    keywordLocation: `${schemaLocation}/multipleOf`,\n                    error: `${instance} is not a multiple of ${$multipleOf}.`\n                });\n            }\n        }\n    }\n    else if (instanceType === 'string') {\n        const length = $minLength === undefined && $maxLength === undefined\n            ? 0\n            : (0, ucs2_length_js_1.ucs2length)(instance);\n        if ($minLength !== undefined && length < $minLength) {\n            errors.push({\n                instanceLocation,\n                keyword: 'minLength',\n                keywordLocation: `${schemaLocation}/minLength`,\n                error: `String is too short (${length} < ${$minLength}).`\n            });\n        }\n        if ($maxLength !== undefined && length > $maxLength) {\n            errors.push({\n                instanceLocation,\n                keyword: 'maxLength',\n                keywordLocation: `${schemaLocation}/maxLength`,\n                error: `String is too long (${length} > ${$maxLength}).`\n            });\n        }\n        if ($pattern !== undefined && !new RegExp($pattern, 'u').test(instance)) {\n            errors.push({\n                instanceLocation,\n                keyword: 'pattern',\n                keywordLocation: `${schemaLocation}/pattern`,\n                error: `String does not match pattern.`\n            });\n        }\n        if ($format !== undefined &&\n            format_js_1.format[$format] &&\n            !format_js_1.format[$format](instance)) {\n            errors.push({\n                instanceLocation,\n                keyword: 'format',\n                keywordLocation: `${schemaLocation}/format`,\n                error: `String does not match format \"${$format}\".`\n            });\n        }\n    }\n    return { valid: errors.length === 0, errors };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/validate.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/validator.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/validator.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Validator = void 0;\nconst dereference_js_1 = __webpack_require__(/*! ./dereference.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/dereference.js\");\nconst validate_js_1 = __webpack_require__(/*! ./validate.js */ \"(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/validate.js\");\nclass Validator {\n    schema;\n    draft;\n    shortCircuit;\n    lookup;\n    constructor(schema, draft = '2019-09', shortCircuit = true) {\n        this.schema = schema;\n        this.draft = draft;\n        this.shortCircuit = shortCircuit;\n        this.lookup = (0, dereference_js_1.dereference)(schema);\n    }\n    validate(instance) {\n        return (0, validate_js_1.validate)(instance, this.schema, this.draft, this.lookup, this.shortCircuit);\n    }\n    addSchema(schema, id) {\n        if (id) {\n            schema = { ...schema, $id: id };\n        }\n        (0, dereference_js_1.dereference)(schema, this.lookup);\n    }\n}\nexports.Validator = Validator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@cfworker+json-schema@4.1.1/node_modules/@cfworker/json-schema/dist/commonjs/validator.js\n");

/***/ })

};
;