"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+middleware-content-length@4.0.4";
exports.ids = ["vendor-chunks/@smithy+middleware-content-length@4.0.4"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-content-length@4.0.4/node_modules/@smithy/middleware-content-length/dist-es/index.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-content-length@4.0.4/node_modules/@smithy/middleware-content-length/dist-es/index.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contentLengthMiddleware: () => (/* binding */ contentLengthMiddleware),\n/* harmony export */   contentLengthMiddlewareOptions: () => (/* binding */ contentLengthMiddlewareOptions),\n/* harmony export */   getContentLengthPlugin: () => (/* binding */ getContentLengthPlugin)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n\nconst CONTENT_LENGTH_HEADER = \"content-length\";\nfunction contentLengthMiddleware(bodyLengthChecker) {\n    return (next) => async (args) => {\n        const request = args.request;\n        if (_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.isInstance(request)) {\n            const { body, headers } = request;\n            if (body &&\n                Object.keys(headers)\n                    .map((str) => str.toLowerCase())\n                    .indexOf(CONTENT_LENGTH_HEADER) === -1) {\n                try {\n                    const length = bodyLengthChecker(body);\n                    request.headers = {\n                        ...request.headers,\n                        [CONTENT_LENGTH_HEADER]: String(length),\n                    };\n                }\n                catch (error) {\n                }\n            }\n        }\n        return next({\n            ...args,\n            request,\n        });\n    };\n}\nconst contentLengthMiddlewareOptions = {\n    step: \"build\",\n    tags: [\"SET_CONTENT_LENGTH\", \"CONTENT_LENGTH\"],\n    name: \"contentLengthMiddleware\",\n    override: true,\n};\nconst getContentLengthPlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(contentLengthMiddleware(options.bodyLengthChecker), contentLengthMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-content-length@4.0.4/node_modules/@smithy/middleware-content-length/dist-es/index.js\n");

/***/ })

};
;