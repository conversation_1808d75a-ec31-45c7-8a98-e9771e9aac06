/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3";
exports.ids = ["vendor-chunks/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3/node_modules/@langchain/community/dist/llms/ollama.cjs":
/*!************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3/node_modules/@langchain/community/dist/llms/ollama.cjs ***!
  \************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Ollama = void 0;\nconst outputs_1 = __webpack_require__(/*! @langchain/core/outputs */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/outputs.cjs\");\nconst llms_1 = __webpack_require__(/*! @langchain/core/language_models/llms */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/language_models/llms.cjs\");\nconst ollama_js_1 = __webpack_require__(/*! ../utils/ollama.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3/node_modules/@langchain/community/dist/utils/ollama.cjs\");\n/**\n * @deprecated Ollama LLM has moved to the `@langchain/ollama` package. Please install it using `npm install @langchain/ollama` and import it from there.\n *\n * Class that represents the Ollama language model. It extends the base\n * LLM class and implements the OllamaInput interface.\n * @example\n * ```typescript\n * const ollama = new Ollama({\n *   baseUrl: \"http://api.example.com\",\n *   model: \"llama2\",\n * });\n *\n * // Streaming translation from English to German\n * const stream = await ollama.stream(\n *   `Translate \"I love programming\" into German.`\n * );\n *\n * const chunks = [];\n * for await (const chunk of stream) {\n *   chunks.push(chunk);\n * }\n *\n * console.log(chunks.join(\"\"));\n * ```\n */\nclass Ollama extends llms_1.LLM {\n    static lc_name() {\n        return \"Ollama\";\n    }\n    constructor(fields) {\n        super(fields);\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"model\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"llama2\"\n        });\n        Object.defineProperty(this, \"baseUrl\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"http://localhost:11434\"\n        });\n        Object.defineProperty(this, \"keepAlive\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"5m\"\n        });\n        Object.defineProperty(this, \"embeddingOnly\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"f16KV\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"frequencyPenalty\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"headers\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"logitsAll\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"lowVram\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"mainGpu\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"mirostat\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"mirostatEta\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"mirostatTau\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"numBatch\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"numCtx\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"numGpu\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"numGqa\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"numKeep\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"numPredict\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"numThread\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"penalizeNewline\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"presencePenalty\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"repeatLastN\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"repeatPenalty\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"ropeFrequencyBase\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"ropeFrequencyScale\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"temperature\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"stop\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"tfsZ\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"topK\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"topP\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"typicalP\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"useMLock\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"useMMap\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"vocabOnly\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"format\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.model = fields.model ?? this.model;\n        this.baseUrl = fields.baseUrl?.endsWith(\"/\")\n            ? fields.baseUrl.slice(0, -1)\n            : fields.baseUrl ?? this.baseUrl;\n        this.keepAlive = fields.keepAlive ?? this.keepAlive;\n        this.headers = fields.headers ?? this.headers;\n        this.embeddingOnly = fields.embeddingOnly;\n        this.f16KV = fields.f16KV;\n        this.frequencyPenalty = fields.frequencyPenalty;\n        this.logitsAll = fields.logitsAll;\n        this.lowVram = fields.lowVram;\n        this.mainGpu = fields.mainGpu;\n        this.mirostat = fields.mirostat;\n        this.mirostatEta = fields.mirostatEta;\n        this.mirostatTau = fields.mirostatTau;\n        this.numBatch = fields.numBatch;\n        this.numCtx = fields.numCtx;\n        this.numGpu = fields.numGpu;\n        this.numGqa = fields.numGqa;\n        this.numKeep = fields.numKeep;\n        this.numPredict = fields.numPredict;\n        this.numThread = fields.numThread;\n        this.penalizeNewline = fields.penalizeNewline;\n        this.presencePenalty = fields.presencePenalty;\n        this.repeatLastN = fields.repeatLastN;\n        this.repeatPenalty = fields.repeatPenalty;\n        this.ropeFrequencyBase = fields.ropeFrequencyBase;\n        this.ropeFrequencyScale = fields.ropeFrequencyScale;\n        this.temperature = fields.temperature;\n        this.stop = fields.stop;\n        this.tfsZ = fields.tfsZ;\n        this.topK = fields.topK;\n        this.topP = fields.topP;\n        this.typicalP = fields.typicalP;\n        this.useMLock = fields.useMLock;\n        this.useMMap = fields.useMMap;\n        this.vocabOnly = fields.vocabOnly;\n        this.format = fields.format;\n    }\n    _llmType() {\n        return \"ollama\";\n    }\n    invocationParams(options) {\n        return {\n            model: this.model,\n            format: this.format,\n            keep_alive: this.keepAlive,\n            images: options?.images,\n            options: {\n                embedding_only: this.embeddingOnly,\n                f16_kv: this.f16KV,\n                frequency_penalty: this.frequencyPenalty,\n                logits_all: this.logitsAll,\n                low_vram: this.lowVram,\n                main_gpu: this.mainGpu,\n                mirostat: this.mirostat,\n                mirostat_eta: this.mirostatEta,\n                mirostat_tau: this.mirostatTau,\n                num_batch: this.numBatch,\n                num_ctx: this.numCtx,\n                num_gpu: this.numGpu,\n                num_gqa: this.numGqa,\n                num_keep: this.numKeep,\n                num_predict: this.numPredict,\n                num_thread: this.numThread,\n                penalize_newline: this.penalizeNewline,\n                presence_penalty: this.presencePenalty,\n                repeat_last_n: this.repeatLastN,\n                repeat_penalty: this.repeatPenalty,\n                rope_frequency_base: this.ropeFrequencyBase,\n                rope_frequency_scale: this.ropeFrequencyScale,\n                temperature: this.temperature,\n                stop: options?.stop ?? this.stop,\n                tfs_z: this.tfsZ,\n                top_k: this.topK,\n                top_p: this.topP,\n                typical_p: this.typicalP,\n                use_mlock: this.useMLock,\n                use_mmap: this.useMMap,\n                vocab_only: this.vocabOnly,\n            },\n        };\n    }\n    async *_streamResponseChunks(prompt, options, runManager) {\n        const stream = await this.caller.call(async () => (0, ollama_js_1.createOllamaGenerateStream)(this.baseUrl, { ...this.invocationParams(options), prompt }, {\n            ...options,\n            headers: this.headers,\n        }));\n        for await (const chunk of stream) {\n            if (!chunk.done) {\n                yield new outputs_1.GenerationChunk({\n                    text: chunk.response,\n                    generationInfo: {\n                        ...chunk,\n                        response: undefined,\n                    },\n                });\n                await runManager?.handleLLMNewToken(chunk.response ?? \"\");\n            }\n            else {\n                yield new outputs_1.GenerationChunk({\n                    text: \"\",\n                    generationInfo: {\n                        model: chunk.model,\n                        total_duration: chunk.total_duration,\n                        load_duration: chunk.load_duration,\n                        prompt_eval_count: chunk.prompt_eval_count,\n                        prompt_eval_duration: chunk.prompt_eval_duration,\n                        eval_count: chunk.eval_count,\n                        eval_duration: chunk.eval_duration,\n                    },\n                });\n            }\n        }\n    }\n    /** @ignore */\n    async _call(prompt, options, runManager) {\n        const chunks = [];\n        for await (const chunk of this._streamResponseChunks(prompt, options, runManager)) {\n            chunks.push(chunk.text);\n        }\n        return chunks.join(\"\");\n    }\n}\nexports.Ollama = Ollama;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3/node_modules/@langchain/community/dist/llms/ollama.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3/node_modules/@langchain/community/dist/utils/ollama.cjs":
/*!*************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3/node_modules/@langchain/community/dist/utils/ollama.cjs ***!
  \*************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createOllamaChatStream = exports.createOllamaGenerateStream = void 0;\nconst stream_1 = __webpack_require__(/*! @langchain/core/utils/stream */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/stream.cjs\");\nasync function* createOllamaStream(url, params, options) {\n    let formattedUrl = url;\n    if (formattedUrl.startsWith(\"http://localhost:\")) {\n        // Node 18 has issues with resolving \"localhost\"\n        // See https://github.com/node-fetch/node-fetch/issues/1624\n        formattedUrl = formattedUrl.replace(\"http://localhost:\", \"http://127.0.0.1:\");\n    }\n    const response = await fetch(formattedUrl, {\n        method: \"POST\",\n        body: JSON.stringify(params),\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...options.headers,\n        },\n        signal: options.signal,\n    });\n    if (!response.ok) {\n        let error;\n        const responseText = await response.text();\n        try {\n            const json = JSON.parse(responseText);\n            error = new Error(`Ollama call failed with status code ${response.status}: ${json.error}`);\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        }\n        catch (e) {\n            error = new Error(`Ollama call failed with status code ${response.status}: ${responseText}`);\n        }\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        error.response = response;\n        throw error;\n    }\n    if (!response.body) {\n        throw new Error(\"Could not begin Ollama stream. Please check the given URL and try again.\");\n    }\n    const stream = stream_1.IterableReadableStream.fromReadableStream(response.body);\n    const decoder = new TextDecoder();\n    let extra = \"\";\n    for await (const chunk of stream) {\n        const decoded = extra + decoder.decode(chunk);\n        const lines = decoded.split(\"\\n\");\n        extra = lines.pop() || \"\";\n        for (const line of lines) {\n            try {\n                yield JSON.parse(line);\n            }\n            catch (e) {\n                console.warn(`Received a non-JSON parseable chunk: ${line}`);\n            }\n        }\n    }\n}\nasync function* createOllamaGenerateStream(baseUrl, params, options) {\n    yield* createOllamaStream(`${baseUrl}/api/generate`, params, options);\n}\nexports.createOllamaGenerateStream = createOllamaGenerateStream;\nasync function* createOllamaChatStream(baseUrl, params, options) {\n    yield* createOllamaStream(`${baseUrl}/api/chat`, params, options);\n}\nexports.createOllamaChatStream = createOllamaChatStream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3/node_modules/@langchain/community/dist/utils/ollama.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3/node_modules/@langchain/community/llms/ollama.cjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3/node_modules/@langchain/community/llms/ollama.cjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ../dist/llms/ollama.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3/node_modules/@langchain/community/dist/llms/ollama.cjs\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitjb21tdW5pdHlAMC4zLjI5X0Bhd3MtY3J5cHRvK3NoYTI1Ni1qc0A1LjIuMF9AYXdzLXNkaytjbGllbnQtYmVkcm9jay1hZ2VudC1yXzE0NGY4YmI3NmM2ZTAxZjI3OWMyOGE3M2RiY2M1ZWEzL25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2NvbW11bml0eS9sbG1zL29sbGFtYS5janMiLCJtYXBwaW5ncyI6IkFBQUEsaVJBQW1EIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitjb21tdW5pdHlAMC4zLjI5X0Bhd3MtY3J5cHRvK3NoYTI1Ni1qc0A1LjIuMF9AYXdzLXNkaytjbGllbnQtYmVkcm9jay1hZ2VudC1yXzE0NGY4YmI3NmM2ZTAxZjI3OWMyOGE3M2RiY2M1ZWEzL25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2NvbW11bml0eS9sbG1zL29sbGFtYS5janMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuLi9kaXN0L2xsbXMvb2xsYW1hLmNqcycpOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+community@0.3.29_@aws-crypto+sha256-js@5.2.0_@aws-sdk+client-bedrock-agent-r_144f8bb76c6e01f279c28a73dbcc5ea3/node_modules/@langchain/community/llms/ollama.cjs\n");

/***/ })

};
;