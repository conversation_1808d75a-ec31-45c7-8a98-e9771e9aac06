"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+eventstream-handler-node@3.821.0";
exports.ids = ["vendor-chunks/@aws-sdk+eventstream-handler-node@3.821.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/EventSigningStream.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/EventSigningStream.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventSigningStream: () => (/* binding */ EventSigningStream)\n/* harmony export */ });\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_0__);\n\nclass EventSigningStream extends stream__WEBPACK_IMPORTED_MODULE_0__.Transform {\n    priorSignature;\n    messageSigner;\n    eventStreamCodec;\n    systemClockOffsetProvider;\n    constructor(options) {\n        super({\n            autoDestroy: true,\n            readableObjectMode: true,\n            writableObjectMode: true,\n            ...options,\n        });\n        this.priorSignature = options.priorSignature;\n        this.eventStreamCodec = options.eventStreamCodec;\n        this.messageSigner = options.messageSigner;\n        this.systemClockOffsetProvider = options.systemClockOffsetProvider;\n    }\n    async _transform(chunk, encoding, callback) {\n        try {\n            const now = new Date(Date.now() + (await this.systemClockOffsetProvider()));\n            const dateHeader = {\n                \":date\": { type: \"timestamp\", value: now },\n            };\n            const signedMessage = await this.messageSigner.sign({\n                message: {\n                    body: chunk,\n                    headers: dateHeader,\n                },\n                priorSignature: this.priorSignature,\n            }, {\n                signingDate: now,\n            });\n            this.priorSignature = signedMessage.signature;\n            const serializedSigned = this.eventStreamCodec.encode({\n                headers: {\n                    ...dateHeader,\n                    \":chunk-signature\": {\n                        type: \"binary\",\n                        value: getSignatureBinary(signedMessage.signature),\n                    },\n                },\n                body: chunk,\n            });\n            this.push(serializedSigned);\n            return callback();\n        }\n        catch (err) {\n            callback(err);\n        }\n    }\n}\nfunction getSignatureBinary(signature) {\n    const buf = Buffer.from(signature, \"hex\");\n    return new Uint8Array(buf.buffer, buf.byteOffset, buf.byteLength / Uint8Array.BYTES_PER_ELEMENT);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrZXZlbnRzdHJlYW0taGFuZGxlci1ub2RlQDMuODIxLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2V2ZW50c3RyZWFtLWhhbmRsZXItbm9kZS9kaXN0LWVzL0V2ZW50U2lnbmluZ1N0cmVhbS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUM7QUFDNUIsaUNBQWlDLDZDQUFTO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQiwrQkFBK0I7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLGFBQWE7QUFDYjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQixpQkFBaUI7QUFDakI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrZXZlbnRzdHJlYW0taGFuZGxlci1ub2RlQDMuODIxLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2V2ZW50c3RyZWFtLWhhbmRsZXItbm9kZS9kaXN0LWVzL0V2ZW50U2lnbmluZ1N0cmVhbS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUcmFuc2Zvcm0gfSBmcm9tIFwic3RyZWFtXCI7XG5leHBvcnQgY2xhc3MgRXZlbnRTaWduaW5nU3RyZWFtIGV4dGVuZHMgVHJhbnNmb3JtIHtcbiAgICBwcmlvclNpZ25hdHVyZTtcbiAgICBtZXNzYWdlU2lnbmVyO1xuICAgIGV2ZW50U3RyZWFtQ29kZWM7XG4gICAgc3lzdGVtQ2xvY2tPZmZzZXRQcm92aWRlcjtcbiAgICBjb25zdHJ1Y3RvcihvcHRpb25zKSB7XG4gICAgICAgIHN1cGVyKHtcbiAgICAgICAgICAgIGF1dG9EZXN0cm95OiB0cnVlLFxuICAgICAgICAgICAgcmVhZGFibGVPYmplY3RNb2RlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGVPYmplY3RNb2RlOiB0cnVlLFxuICAgICAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMucHJpb3JTaWduYXR1cmUgPSBvcHRpb25zLnByaW9yU2lnbmF0dXJlO1xuICAgICAgICB0aGlzLmV2ZW50U3RyZWFtQ29kZWMgPSBvcHRpb25zLmV2ZW50U3RyZWFtQ29kZWM7XG4gICAgICAgIHRoaXMubWVzc2FnZVNpZ25lciA9IG9wdGlvbnMubWVzc2FnZVNpZ25lcjtcbiAgICAgICAgdGhpcy5zeXN0ZW1DbG9ja09mZnNldFByb3ZpZGVyID0gb3B0aW9ucy5zeXN0ZW1DbG9ja09mZnNldFByb3ZpZGVyO1xuICAgIH1cbiAgICBhc3luYyBfdHJhbnNmb3JtKGNodW5rLCBlbmNvZGluZywgY2FsbGJhY2spIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKERhdGUubm93KCkgKyAoYXdhaXQgdGhpcy5zeXN0ZW1DbG9ja09mZnNldFByb3ZpZGVyKCkpKTtcbiAgICAgICAgICAgIGNvbnN0IGRhdGVIZWFkZXIgPSB7XG4gICAgICAgICAgICAgICAgXCI6ZGF0ZVwiOiB7IHR5cGU6IFwidGltZXN0YW1wXCIsIHZhbHVlOiBub3cgfSxcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBjb25zdCBzaWduZWRNZXNzYWdlID0gYXdhaXQgdGhpcy5tZXNzYWdlU2lnbmVyLnNpZ24oe1xuICAgICAgICAgICAgICAgIG1lc3NhZ2U6IHtcbiAgICAgICAgICAgICAgICAgICAgYm9keTogY2h1bmssXG4gICAgICAgICAgICAgICAgICAgIGhlYWRlcnM6IGRhdGVIZWFkZXIsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBwcmlvclNpZ25hdHVyZTogdGhpcy5wcmlvclNpZ25hdHVyZSxcbiAgICAgICAgICAgIH0sIHtcbiAgICAgICAgICAgICAgICBzaWduaW5nRGF0ZTogbm93LFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB0aGlzLnByaW9yU2lnbmF0dXJlID0gc2lnbmVkTWVzc2FnZS5zaWduYXR1cmU7XG4gICAgICAgICAgICBjb25zdCBzZXJpYWxpemVkU2lnbmVkID0gdGhpcy5ldmVudFN0cmVhbUNvZGVjLmVuY29kZSh7XG4gICAgICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICAgICAgICAuLi5kYXRlSGVhZGVyLFxuICAgICAgICAgICAgICAgICAgICBcIjpjaHVuay1zaWduYXR1cmVcIjoge1xuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogXCJiaW5hcnlcIixcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBnZXRTaWduYXR1cmVCaW5hcnkoc2lnbmVkTWVzc2FnZS5zaWduYXR1cmUpLFxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgYm9keTogY2h1bmssXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRoaXMucHVzaChzZXJpYWxpemVkU2lnbmVkKTtcbiAgICAgICAgICAgIHJldHVybiBjYWxsYmFjaygpO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgIGNhbGxiYWNrKGVycik7XG4gICAgICAgIH1cbiAgICB9XG59XG5mdW5jdGlvbiBnZXRTaWduYXR1cmVCaW5hcnkoc2lnbmF0dXJlKSB7XG4gICAgY29uc3QgYnVmID0gQnVmZmVyLmZyb20oc2lnbmF0dXJlLCBcImhleFwiKTtcbiAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoYnVmLmJ1ZmZlciwgYnVmLmJ5dGVPZmZzZXQsIGJ1Zi5ieXRlTGVuZ3RoIC8gVWludDhBcnJheS5CWVRFU19QRVJfRUxFTUVOVCk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/EventSigningStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/EventStreamPayloadHandler.js":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/EventStreamPayloadHandler.js ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventStreamPayloadHandler: () => (/* binding */ EventStreamPayloadHandler)\n/* harmony export */ });\n/* harmony import */ var _smithy_eventstream_codec__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/eventstream-codec */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/index.js\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _EventSigningStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EventSigningStream */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/EventSigningStream.js\");\n\n\n\nclass EventStreamPayloadHandler {\n    messageSigner;\n    eventStreamCodec;\n    systemClockOffsetProvider;\n    constructor(options) {\n        this.messageSigner = options.messageSigner;\n        this.eventStreamCodec = new _smithy_eventstream_codec__WEBPACK_IMPORTED_MODULE_0__.EventStreamCodec(options.utf8Encoder, options.utf8Decoder);\n        this.systemClockOffsetProvider = async () => options.systemClockOffset ?? 0;\n    }\n    async handle(next, args, context = {}) {\n        const request = args.request;\n        const { body: payload, query } = request;\n        if (!(payload instanceof stream__WEBPACK_IMPORTED_MODULE_1__.Readable)) {\n            throw new Error(\"Eventstream payload must be a Readable stream.\");\n        }\n        const payloadStream = payload;\n        request.body = new stream__WEBPACK_IMPORTED_MODULE_1__.PassThrough({\n            objectMode: true,\n        });\n        const match = request.headers?.authorization?.match(/Signature=([\\w]+)$/);\n        const priorSignature = match?.[1] ?? query?.[\"X-Amz-Signature\"] ?? \"\";\n        const signingStream = new _EventSigningStream__WEBPACK_IMPORTED_MODULE_2__.EventSigningStream({\n            priorSignature,\n            eventStreamCodec: this.eventStreamCodec,\n            messageSigner: await this.messageSigner(),\n            systemClockOffsetProvider: this.systemClockOffsetProvider,\n        });\n        (0,stream__WEBPACK_IMPORTED_MODULE_1__.pipeline)(payloadStream, signingStream, request.body, (err) => {\n            if (err) {\n                throw err;\n            }\n        });\n        let result;\n        try {\n            result = await next(args);\n        }\n        catch (e) {\n            request.body.end();\n            throw e;\n        }\n        return result;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/EventStreamPayloadHandler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/index.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/index.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventStreamPayloadHandlerProvider: () => (/* reexport safe */ _provider__WEBPACK_IMPORTED_MODULE_0__.eventStreamPayloadHandlerProvider)\n/* harmony export */ });\n/* harmony import */ var _provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./provider */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/provider.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrZXZlbnRzdHJlYW0taGFuZGxlci1ub2RlQDMuODIxLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2V2ZW50c3RyZWFtLWhhbmRsZXItbm9kZS9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStEIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrZXZlbnRzdHJlYW0taGFuZGxlci1ub2RlQDMuODIxLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2V2ZW50c3RyZWFtLWhhbmRsZXItbm9kZS9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGV2ZW50U3RyZWFtUGF5bG9hZEhhbmRsZXJQcm92aWRlciB9IGZyb20gXCIuL3Byb3ZpZGVyXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/provider.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/provider.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventStreamPayloadHandlerProvider: () => (/* binding */ eventStreamPayloadHandlerProvider)\n/* harmony export */ });\n/* harmony import */ var _EventStreamPayloadHandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EventStreamPayloadHandler */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/EventStreamPayloadHandler.js\");\n\nconst eventStreamPayloadHandlerProvider = (options) => new _EventStreamPayloadHandler__WEBPACK_IMPORTED_MODULE_0__.EventStreamPayloadHandler(options);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrZXZlbnRzdHJlYW0taGFuZGxlci1ub2RlQDMuODIxLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2V2ZW50c3RyZWFtLWhhbmRsZXItbm9kZS9kaXN0LWVzL3Byb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXdFO0FBQ2pFLDJEQUEyRCxpRkFBeUIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytldmVudHN0cmVhbS1oYW5kbGVyLW5vZGVAMy44MjEuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvZXZlbnRzdHJlYW0taGFuZGxlci1ub2RlL2Rpc3QtZXMvcHJvdmlkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRXZlbnRTdHJlYW1QYXlsb2FkSGFuZGxlciB9IGZyb20gXCIuL0V2ZW50U3RyZWFtUGF5bG9hZEhhbmRsZXJcIjtcbmV4cG9ydCBjb25zdCBldmVudFN0cmVhbVBheWxvYWRIYW5kbGVyUHJvdmlkZXIgPSAob3B0aW9ucykgPT4gbmV3IEV2ZW50U3RyZWFtUGF5bG9hZEhhbmRsZXIob3B0aW9ucyk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+eventstream-handler-node@3.821.0/node_modules/@aws-sdk/eventstream-handler-node/dist-es/provider.js\n");

/***/ })

};
;