"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@repeaterjs+repeater@3.0.6";
exports.ids = ["vendor-chunks/@repeaterjs+repeater@3.0.6"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@repeaterjs+repeater@3.0.6/node_modules/@repeaterjs/repeater/cjs/repeater.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@repeaterjs+repeater@3.0.6/node_modules/@repeaterjs/repeater/cjs/repeater.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nfunction __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nfunction __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nfunction __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nfunction __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\n\n/** An error subclass which is thrown when there are too many pending push or next operations on a single repeater. */\r\nvar RepeaterOverflowError = /** @class */ (function (_super) {\r\n    __extends(RepeaterOverflowError, _super);\r\n    function RepeaterOverflowError(message) {\r\n        var _this = _super.call(this, message) || this;\r\n        Object.defineProperty(_this, \"name\", {\r\n            value: \"RepeaterOverflowError\",\r\n            enumerable: false,\r\n        });\r\n        if (typeof Object.setPrototypeOf === \"function\") {\r\n            Object.setPrototypeOf(_this, _this.constructor.prototype);\r\n        }\r\n        else {\r\n            _this.__proto__ = _this.constructor.prototype;\r\n        }\r\n        if (typeof Error.captureStackTrace === \"function\") {\r\n            Error.captureStackTrace(_this, _this.constructor);\r\n        }\r\n        return _this;\r\n    }\r\n    return RepeaterOverflowError;\r\n}(Error));\r\n/** A buffer which allows you to push a set amount of values to the repeater without pushes waiting or throwing errors. */\r\nvar FixedBuffer = /** @class */ (function () {\r\n    function FixedBuffer(capacity) {\r\n        if (capacity < 0) {\r\n            throw new RangeError(\"Capacity may not be less than 0\");\r\n        }\r\n        this._c = capacity;\r\n        this._q = [];\r\n    }\r\n    Object.defineProperty(FixedBuffer.prototype, \"empty\", {\r\n        get: function () {\r\n            return this._q.length === 0;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(FixedBuffer.prototype, \"full\", {\r\n        get: function () {\r\n            return this._q.length >= this._c;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    FixedBuffer.prototype.add = function (value) {\r\n        if (this.full) {\r\n            throw new Error(\"Buffer full\");\r\n        }\r\n        else {\r\n            this._q.push(value);\r\n        }\r\n    };\r\n    FixedBuffer.prototype.remove = function () {\r\n        if (this.empty) {\r\n            throw new Error(\"Buffer empty\");\r\n        }\r\n        return this._q.shift();\r\n    };\r\n    return FixedBuffer;\r\n}());\r\n// TODO: Use a circular buffer here.\r\n/** Sliding buffers allow you to push a set amount of values to the repeater without pushes waiting or throwing errors. If the number of values exceeds the capacity set in the constructor, the buffer will discard the earliest values added. */\r\nvar SlidingBuffer = /** @class */ (function () {\r\n    function SlidingBuffer(capacity) {\r\n        if (capacity < 1) {\r\n            throw new RangeError(\"Capacity may not be less than 1\");\r\n        }\r\n        this._c = capacity;\r\n        this._q = [];\r\n    }\r\n    Object.defineProperty(SlidingBuffer.prototype, \"empty\", {\r\n        get: function () {\r\n            return this._q.length === 0;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(SlidingBuffer.prototype, \"full\", {\r\n        get: function () {\r\n            return false;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    SlidingBuffer.prototype.add = function (value) {\r\n        while (this._q.length >= this._c) {\r\n            this._q.shift();\r\n        }\r\n        this._q.push(value);\r\n    };\r\n    SlidingBuffer.prototype.remove = function () {\r\n        if (this.empty) {\r\n            throw new Error(\"Buffer empty\");\r\n        }\r\n        return this._q.shift();\r\n    };\r\n    return SlidingBuffer;\r\n}());\r\n/** Dropping buffers allow you to push a set amount of values to the repeater without the push function waiting or throwing errors. If the number of values exceeds the capacity set in the constructor, the buffer will discard the latest values added. */\r\nvar DroppingBuffer = /** @class */ (function () {\r\n    function DroppingBuffer(capacity) {\r\n        if (capacity < 1) {\r\n            throw new RangeError(\"Capacity may not be less than 1\");\r\n        }\r\n        this._c = capacity;\r\n        this._q = [];\r\n    }\r\n    Object.defineProperty(DroppingBuffer.prototype, \"empty\", {\r\n        get: function () {\r\n            return this._q.length === 0;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(DroppingBuffer.prototype, \"full\", {\r\n        get: function () {\r\n            return false;\r\n        },\r\n        enumerable: false,\r\n        configurable: true\r\n    });\r\n    DroppingBuffer.prototype.add = function (value) {\r\n        if (this._q.length < this._c) {\r\n            this._q.push(value);\r\n        }\r\n    };\r\n    DroppingBuffer.prototype.remove = function () {\r\n        if (this.empty) {\r\n            throw new Error(\"Buffer empty\");\r\n        }\r\n        return this._q.shift();\r\n    };\r\n    return DroppingBuffer;\r\n}());\r\n/** Makes sure promise-likes don’t cause unhandled rejections. */\r\nfunction swallow(value) {\r\n    if (value != null && typeof value.then === \"function\") {\r\n        value.then(NOOP, NOOP);\r\n    }\r\n}\r\n/*** REPEATER STATES ***/\r\n/** The following is an enumeration of all possible repeater states. These states are ordered, and a repeater may only advance to higher states. */\r\n/** The initial state of the repeater. */\r\nvar Initial = 0;\r\n/** Repeaters advance to this state the first time the next method is called on the repeater. */\r\nvar Started = 1;\r\n/** Repeaters advance to this state when the stop function is called. */\r\nvar Stopped = 2;\r\n/** Repeaters advance to this state when there are no values left to be pulled from the repeater. */\r\nvar Done = 3;\r\n/** Repeaters advance to this state if an error is thrown into the repeater. */\r\nvar Rejected = 4;\r\n/** The maximum number of push or next operations which may exist on a single repeater. */\r\nvar MAX_QUEUE_LENGTH = 1024;\r\nvar NOOP = function () { };\r\n/** A helper function used to mimic the behavior of async generators where the final iteration is consumed. */\r\nfunction consumeExecution(r) {\r\n    var err = r.err;\r\n    var execution = Promise.resolve(r.execution).then(function (value) {\r\n        if (err != null) {\r\n            throw err;\r\n        }\r\n        return value;\r\n    });\r\n    r.err = undefined;\r\n    r.execution = execution.then(function () { return undefined; }, function () { return undefined; });\r\n    return r.pending === undefined ? execution : r.pending.then(function () { return execution; });\r\n}\r\n/** A helper function for building iterations from values. Promises are unwrapped, so that iterations never have their value property set to a promise. */\r\nfunction createIteration(r, value) {\r\n    var done = r.state >= Done;\r\n    return Promise.resolve(value).then(function (value) {\r\n        if (!done && r.state >= Rejected) {\r\n            return consumeExecution(r).then(function (value) { return ({\r\n                value: value,\r\n                done: true,\r\n            }); });\r\n        }\r\n        return { value: value, done: done };\r\n    });\r\n}\r\n/**\r\n * This function is bound and passed to the executor as the stop argument.\r\n *\r\n * Advances state to Stopped.\r\n */\r\nfunction stop(r, err) {\r\n    var e_1, _a;\r\n    if (r.state >= Stopped) {\r\n        return;\r\n    }\r\n    r.state = Stopped;\r\n    r.onnext();\r\n    r.onstop();\r\n    if (r.err == null) {\r\n        r.err = err;\r\n    }\r\n    if (r.pushes.length === 0 &&\r\n        (typeof r.buffer === \"undefined\" || r.buffer.empty)) {\r\n        finish(r);\r\n    }\r\n    else {\r\n        try {\r\n            for (var _b = __values(r.pushes), _d = _b.next(); !_d.done; _d = _b.next()) {\r\n                var push_1 = _d.value;\r\n                push_1.resolve();\r\n            }\r\n        }\r\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\r\n        finally {\r\n            try {\r\n                if (_d && !_d.done && (_a = _b.return)) _a.call(_b);\r\n            }\r\n            finally { if (e_1) throw e_1.error; }\r\n        }\r\n    }\r\n}\r\n/**\r\n * The difference between stopping a repeater vs finishing a repeater is that stopping a repeater allows next to continue to drain values from the push queue and buffer, while finishing a repeater will clear all pending values and end iteration immediately. Once, a repeater is finished, all iterations will have the done property set to true.\r\n *\r\n * Advances state to Done.\r\n */\r\nfunction finish(r) {\r\n    var e_2, _a;\r\n    if (r.state >= Done) {\r\n        return;\r\n    }\r\n    if (r.state < Stopped) {\r\n        stop(r);\r\n    }\r\n    r.state = Done;\r\n    r.buffer = undefined;\r\n    try {\r\n        for (var _b = __values(r.nexts), _d = _b.next(); !_d.done; _d = _b.next()) {\r\n            var next = _d.value;\r\n            var execution = r.pending === undefined\r\n                ? consumeExecution(r)\r\n                : r.pending.then(function () { return consumeExecution(r); });\r\n            next.resolve(createIteration(r, execution));\r\n        }\r\n    }\r\n    catch (e_2_1) { e_2 = { error: e_2_1 }; }\r\n    finally {\r\n        try {\r\n            if (_d && !_d.done && (_a = _b.return)) _a.call(_b);\r\n        }\r\n        finally { if (e_2) throw e_2.error; }\r\n    }\r\n    r.pushes = [];\r\n    r.nexts = [];\r\n}\r\n/**\r\n * Called when a promise passed to push rejects, or when a push call is unhandled.\r\n *\r\n * Advances state to Rejected.\r\n */\r\nfunction reject(r) {\r\n    if (r.state >= Rejected) {\r\n        return;\r\n    }\r\n    if (r.state < Done) {\r\n        finish(r);\r\n    }\r\n    r.state = Rejected;\r\n}\r\n/** This function is bound and passed to the executor as the push argument. */\r\nfunction push(r, value) {\r\n    swallow(value);\r\n    if (r.pushes.length >= MAX_QUEUE_LENGTH) {\r\n        throw new RepeaterOverflowError(\"No more than \" + MAX_QUEUE_LENGTH + \" pending calls to push are allowed on a single repeater.\");\r\n    }\r\n    else if (r.state >= Stopped) {\r\n        return Promise.resolve(undefined);\r\n    }\r\n    var valueP = r.pending === undefined\r\n        ? Promise.resolve(value)\r\n        : r.pending.then(function () { return value; });\r\n    valueP = valueP.catch(function (err) {\r\n        if (r.state < Stopped) {\r\n            r.err = err;\r\n        }\r\n        reject(r);\r\n        return undefined; // void :(\r\n    });\r\n    var nextP;\r\n    if (r.nexts.length) {\r\n        var next_1 = r.nexts.shift();\r\n        next_1.resolve(createIteration(r, valueP));\r\n        if (r.nexts.length) {\r\n            nextP = Promise.resolve(r.nexts[0].value);\r\n        }\r\n        else if (typeof r.buffer !== \"undefined\" && !r.buffer.full) {\r\n            nextP = Promise.resolve(undefined);\r\n        }\r\n        else {\r\n            nextP = new Promise(function (resolve) { return (r.onnext = resolve); });\r\n        }\r\n    }\r\n    else if (typeof r.buffer !== \"undefined\" && !r.buffer.full) {\r\n        r.buffer.add(valueP);\r\n        nextP = Promise.resolve(undefined);\r\n    }\r\n    else {\r\n        nextP = new Promise(function (resolve) { return r.pushes.push({ resolve: resolve, value: valueP }); });\r\n    }\r\n    // If an error is thrown into the repeater via the next or throw methods, we give the repeater a chance to handle this by rejecting the promise returned from push. If the push call is not immediately handled we throw the next iteration of the repeater.\r\n    // To check that the promise returned from push is floating, we modify the then and catch methods of the returned promise so that they flip the floating flag. The push function actually does not return a promise, because modern engines do not call the then and catch methods on native promises. By making next a plain old javascript object, we ensure that the then and catch methods will be called.\r\n    var floating = true;\r\n    var next = {};\r\n    var unhandled = nextP.catch(function (err) {\r\n        if (floating) {\r\n            throw err;\r\n        }\r\n        return undefined; // void :(\r\n    });\r\n    next.then = function (onfulfilled, onrejected) {\r\n        floating = false;\r\n        return Promise.prototype.then.call(nextP, onfulfilled, onrejected);\r\n    };\r\n    next.catch = function (onrejected) {\r\n        floating = false;\r\n        return Promise.prototype.catch.call(nextP, onrejected);\r\n    };\r\n    next.finally = nextP.finally.bind(nextP);\r\n    r.pending = valueP\r\n        .then(function () { return unhandled; })\r\n        .catch(function (err) {\r\n        r.err = err;\r\n        reject(r);\r\n    });\r\n    return next;\r\n}\r\n/**\r\n * Creates the stop callable promise which is passed to the executor\r\n */\r\nfunction createStop(r) {\r\n    var stop1 = stop.bind(null, r);\r\n    var stopP = new Promise(function (resolve) { return (r.onstop = resolve); });\r\n    stop1.then = stopP.then.bind(stopP);\r\n    stop1.catch = stopP.catch.bind(stopP);\r\n    stop1.finally = stopP.finally.bind(stopP);\r\n    return stop1;\r\n}\r\n/**\r\n * Calls the executor passed into the constructor. This function is called the first time the next method is called on the repeater.\r\n *\r\n * Advances state to Started.\r\n */\r\nfunction execute(r) {\r\n    if (r.state >= Started) {\r\n        return;\r\n    }\r\n    r.state = Started;\r\n    var push1 = push.bind(null, r);\r\n    var stop1 = createStop(r);\r\n    r.execution = new Promise(function (resolve) { return resolve(r.executor(push1, stop1)); });\r\n    // TODO: We should consider stopping all repeaters when the executor settles.\r\n    r.execution.catch(function () { return stop(r); });\r\n}\r\nvar records = new WeakMap();\r\n// NOTE: While repeaters implement and are assignable to the AsyncGenerator interface, and you can use the types interchangeably, we don’t use typescript’s implements syntax here because this would make supporting earlier versions of typescript trickier. This is because TypeScript version 3.6 changed the iterator types by adding the TReturn and TNext type parameters.\r\nvar Repeater = /** @class */ (function () {\r\n    function Repeater(executor, buffer) {\r\n        records.set(this, {\r\n            executor: executor,\r\n            buffer: buffer,\r\n            err: undefined,\r\n            state: Initial,\r\n            pushes: [],\r\n            nexts: [],\r\n            pending: undefined,\r\n            execution: undefined,\r\n            onnext: NOOP,\r\n            onstop: NOOP,\r\n        });\r\n    }\r\n    Repeater.prototype.next = function (value) {\r\n        swallow(value);\r\n        var r = records.get(this);\r\n        if (r === undefined) {\r\n            throw new Error(\"WeakMap error\");\r\n        }\r\n        if (r.nexts.length >= MAX_QUEUE_LENGTH) {\r\n            throw new RepeaterOverflowError(\"No more than \" + MAX_QUEUE_LENGTH + \" pending calls to next are allowed on a single repeater.\");\r\n        }\r\n        if (r.state <= Initial) {\r\n            execute(r);\r\n        }\r\n        r.onnext(value);\r\n        if (typeof r.buffer !== \"undefined\" && !r.buffer.empty) {\r\n            var result = createIteration(r, r.buffer.remove());\r\n            if (r.pushes.length) {\r\n                var push_2 = r.pushes.shift();\r\n                r.buffer.add(push_2.value);\r\n                r.onnext = push_2.resolve;\r\n            }\r\n            return result;\r\n        }\r\n        else if (r.pushes.length) {\r\n            var push_3 = r.pushes.shift();\r\n            r.onnext = push_3.resolve;\r\n            return createIteration(r, push_3.value);\r\n        }\r\n        else if (r.state >= Stopped) {\r\n            finish(r);\r\n            return createIteration(r, consumeExecution(r));\r\n        }\r\n        return new Promise(function (resolve) { return r.nexts.push({ resolve: resolve, value: value }); });\r\n    };\r\n    Repeater.prototype.return = function (value) {\r\n        swallow(value);\r\n        var r = records.get(this);\r\n        if (r === undefined) {\r\n            throw new Error(\"WeakMap error\");\r\n        }\r\n        finish(r);\r\n        // We override the execution because return should always return the value passed in.\r\n        r.execution = Promise.resolve(r.execution).then(function () { return value; });\r\n        return createIteration(r, consumeExecution(r));\r\n    };\r\n    Repeater.prototype.throw = function (err) {\r\n        var r = records.get(this);\r\n        if (r === undefined) {\r\n            throw new Error(\"WeakMap error\");\r\n        }\r\n        if (r.state <= Initial ||\r\n            r.state >= Stopped ||\r\n            (typeof r.buffer !== \"undefined\" && !r.buffer.empty)) {\r\n            finish(r);\r\n            // If r.err is already set, that mean the repeater has already produced an error, so we throw that error rather than the error passed in, because doing so might be more informative for the caller.\r\n            if (r.err == null) {\r\n                r.err = err;\r\n            }\r\n            return createIteration(r, consumeExecution(r));\r\n        }\r\n        return this.next(Promise.reject(err));\r\n    };\r\n    Repeater.prototype[Symbol.asyncIterator] = function () {\r\n        return this;\r\n    };\r\n    // TODO: Remove these static methods from the class.\r\n    Repeater.race = race;\r\n    Repeater.merge = merge;\r\n    Repeater.zip = zip;\r\n    Repeater.latest = latest;\r\n    return Repeater;\r\n}());\r\n/*** COMBINATOR FUNCTIONS ***/\r\n// TODO: move these combinators to their own file.\r\nfunction getIterators(values, options) {\r\n    var e_3, _a;\r\n    var iters = [];\r\n    var _loop_1 = function (value) {\r\n        if (value != null && typeof value[Symbol.asyncIterator] === \"function\") {\r\n            iters.push(value[Symbol.asyncIterator]());\r\n        }\r\n        else if (value != null && typeof value[Symbol.iterator] === \"function\") {\r\n            iters.push(value[Symbol.iterator]());\r\n        }\r\n        else {\r\n            iters.push((function valueToAsyncIterator() {\r\n                return __asyncGenerator(this, arguments, function valueToAsyncIterator_1() {\r\n                    return __generator(this, function (_a) {\r\n                        switch (_a.label) {\r\n                            case 0:\r\n                                if (!options.yieldValues) return [3 /*break*/, 3];\r\n                                return [4 /*yield*/, __await(value)];\r\n                            case 1: return [4 /*yield*/, _a.sent()];\r\n                            case 2:\r\n                                _a.sent();\r\n                                _a.label = 3;\r\n                            case 3:\r\n                                if (!options.returnValues) return [3 /*break*/, 5];\r\n                                return [4 /*yield*/, __await(value)];\r\n                            case 4: return [2 /*return*/, _a.sent()];\r\n                            case 5: return [2 /*return*/];\r\n                        }\r\n                    });\r\n                });\r\n            })());\r\n        }\r\n    };\r\n    try {\r\n        for (var values_1 = __values(values), values_1_1 = values_1.next(); !values_1_1.done; values_1_1 = values_1.next()) {\r\n            var value = values_1_1.value;\r\n            _loop_1(value);\r\n        }\r\n    }\r\n    catch (e_3_1) { e_3 = { error: e_3_1 }; }\r\n    finally {\r\n        try {\r\n            if (values_1_1 && !values_1_1.done && (_a = values_1.return)) _a.call(values_1);\r\n        }\r\n        finally { if (e_3) throw e_3.error; }\r\n    }\r\n    return iters;\r\n}\r\n// NOTE: whenever you see any variables called `advance` or `advances`, know that it is a hack to get around the fact that `Promise.race` leaks memory. These variables are intended to be set to the resolve function of a promise which is constructed and awaited as an alternative to Promise.race. For more information, see this comment in the Node.js issue tracker: https://github.com/nodejs/node/issues/17469#issuecomment-685216777.\r\nfunction race(contenders) {\r\n    var _this = this;\r\n    var iters = getIterators(contenders, { returnValues: true });\r\n    return new Repeater(function (push, stop) { return __awaiter(_this, void 0, void 0, function () {\r\n        var advance, stopped, finalIteration, iteration, i_1, _loop_2;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    if (!iters.length) {\r\n                        stop();\r\n                        return [2 /*return*/];\r\n                    }\r\n                    stopped = false;\r\n                    stop.then(function () {\r\n                        advance();\r\n                        stopped = true;\r\n                    });\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, , 5, 7]);\r\n                    iteration = void 0;\r\n                    i_1 = 0;\r\n                    _loop_2 = function () {\r\n                        var j, iters_1, iters_1_1, iter;\r\n                        var e_4, _a;\r\n                        return __generator(this, function (_b) {\r\n                            switch (_b.label) {\r\n                                case 0:\r\n                                    j = i_1;\r\n                                    try {\r\n                                        for (iters_1 = (e_4 = void 0, __values(iters)), iters_1_1 = iters_1.next(); !iters_1_1.done; iters_1_1 = iters_1.next()) {\r\n                                            iter = iters_1_1.value;\r\n                                            Promise.resolve(iter.next()).then(function (iteration) {\r\n                                                if (iteration.done) {\r\n                                                    stop();\r\n                                                    if (finalIteration === undefined) {\r\n                                                        finalIteration = iteration;\r\n                                                    }\r\n                                                }\r\n                                                else if (i_1 === j) {\r\n                                                    // This iterator has won, advance i and resolve the promise.\r\n                                                    i_1++;\r\n                                                    advance(iteration);\r\n                                                }\r\n                                            }, function (err) { return stop(err); });\r\n                                        }\r\n                                    }\r\n                                    catch (e_4_1) { e_4 = { error: e_4_1 }; }\r\n                                    finally {\r\n                                        try {\r\n                                            if (iters_1_1 && !iters_1_1.done && (_a = iters_1.return)) _a.call(iters_1);\r\n                                        }\r\n                                        finally { if (e_4) throw e_4.error; }\r\n                                    }\r\n                                    return [4 /*yield*/, new Promise(function (resolve) { return (advance = resolve); })];\r\n                                case 1:\r\n                                    iteration = _b.sent();\r\n                                    if (!(iteration !== undefined)) return [3 /*break*/, 3];\r\n                                    return [4 /*yield*/, push(iteration.value)];\r\n                                case 2:\r\n                                    _b.sent();\r\n                                    _b.label = 3;\r\n                                case 3: return [2 /*return*/];\r\n                            }\r\n                        });\r\n                    };\r\n                    _a.label = 2;\r\n                case 2:\r\n                    if (!!stopped) return [3 /*break*/, 4];\r\n                    return [5 /*yield**/, _loop_2()];\r\n                case 3:\r\n                    _a.sent();\r\n                    return [3 /*break*/, 2];\r\n                case 4: return [2 /*return*/, finalIteration && finalIteration.value];\r\n                case 5:\r\n                    stop();\r\n                    return [4 /*yield*/, Promise.race(iters.map(function (iter) { return iter.return && iter.return(); }))];\r\n                case 6:\r\n                    _a.sent();\r\n                    return [7 /*endfinally*/];\r\n                case 7: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); });\r\n}\r\nfunction merge(contenders) {\r\n    var _this = this;\r\n    var iters = getIterators(contenders, { yieldValues: true });\r\n    return new Repeater(function (push, stop) { return __awaiter(_this, void 0, void 0, function () {\r\n        var advances, stopped, finalIteration;\r\n        var _this = this;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    if (!iters.length) {\r\n                        stop();\r\n                        return [2 /*return*/];\r\n                    }\r\n                    advances = [];\r\n                    stopped = false;\r\n                    stop.then(function () {\r\n                        var e_5, _a;\r\n                        stopped = true;\r\n                        try {\r\n                            for (var advances_1 = __values(advances), advances_1_1 = advances_1.next(); !advances_1_1.done; advances_1_1 = advances_1.next()) {\r\n                                var advance = advances_1_1.value;\r\n                                advance();\r\n                            }\r\n                        }\r\n                        catch (e_5_1) { e_5 = { error: e_5_1 }; }\r\n                        finally {\r\n                            try {\r\n                                if (advances_1_1 && !advances_1_1.done && (_a = advances_1.return)) _a.call(advances_1);\r\n                            }\r\n                            finally { if (e_5) throw e_5.error; }\r\n                        }\r\n                    });\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, , 3, 4]);\r\n                    return [4 /*yield*/, Promise.all(iters.map(function (iter, i) { return __awaiter(_this, void 0, void 0, function () {\r\n                            var iteration, _a;\r\n                            return __generator(this, function (_b) {\r\n                                switch (_b.label) {\r\n                                    case 0:\r\n                                        _b.trys.push([0, , 6, 9]);\r\n                                        _b.label = 1;\r\n                                    case 1:\r\n                                        if (!!stopped) return [3 /*break*/, 5];\r\n                                        Promise.resolve(iter.next()).then(function (iteration) { return advances[i](iteration); }, function (err) { return stop(err); });\r\n                                        return [4 /*yield*/, new Promise(function (resolve) {\r\n                                                advances[i] = resolve;\r\n                                            })];\r\n                                    case 2:\r\n                                        iteration = _b.sent();\r\n                                        if (!(iteration !== undefined)) return [3 /*break*/, 4];\r\n                                        if (iteration.done) {\r\n                                            finalIteration = iteration;\r\n                                            return [2 /*return*/];\r\n                                        }\r\n                                        return [4 /*yield*/, push(iteration.value)];\r\n                                    case 3:\r\n                                        _b.sent();\r\n                                        _b.label = 4;\r\n                                    case 4: return [3 /*break*/, 1];\r\n                                    case 5: return [3 /*break*/, 9];\r\n                                    case 6:\r\n                                        _a = iter.return;\r\n                                        if (!_a) return [3 /*break*/, 8];\r\n                                        return [4 /*yield*/, iter.return()];\r\n                                    case 7:\r\n                                        _a = (_b.sent());\r\n                                        _b.label = 8;\r\n                                    case 8:\r\n                                        return [7 /*endfinally*/];\r\n                                    case 9: return [2 /*return*/];\r\n                                }\r\n                            });\r\n                        }); }))];\r\n                case 2:\r\n                    _a.sent();\r\n                    return [2 /*return*/, finalIteration && finalIteration.value];\r\n                case 3:\r\n                    stop();\r\n                    return [7 /*endfinally*/];\r\n                case 4: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); });\r\n}\r\nfunction zip(contenders) {\r\n    var _this = this;\r\n    var iters = getIterators(contenders, { returnValues: true });\r\n    return new Repeater(function (push, stop) { return __awaiter(_this, void 0, void 0, function () {\r\n        var advance, stopped, iterations, values;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    if (!iters.length) {\r\n                        stop();\r\n                        return [2 /*return*/, []];\r\n                    }\r\n                    stopped = false;\r\n                    stop.then(function () {\r\n                        advance();\r\n                        stopped = true;\r\n                    });\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, , 6, 8]);\r\n                    _a.label = 2;\r\n                case 2:\r\n                    if (!!stopped) return [3 /*break*/, 5];\r\n                    Promise.all(iters.map(function (iter) { return iter.next(); })).then(function (iterations) { return advance(iterations); }, function (err) { return stop(err); });\r\n                    return [4 /*yield*/, new Promise(function (resolve) { return (advance = resolve); })];\r\n                case 3:\r\n                    iterations = _a.sent();\r\n                    if (iterations === undefined) {\r\n                        return [2 /*return*/];\r\n                    }\r\n                    values = iterations.map(function (iteration) { return iteration.value; });\r\n                    if (iterations.some(function (iteration) { return iteration.done; })) {\r\n                        return [2 /*return*/, values];\r\n                    }\r\n                    return [4 /*yield*/, push(values)];\r\n                case 4:\r\n                    _a.sent();\r\n                    return [3 /*break*/, 2];\r\n                case 5: return [3 /*break*/, 8];\r\n                case 6:\r\n                    stop();\r\n                    return [4 /*yield*/, Promise.all(iters.map(function (iter) { return iter.return && iter.return(); }))];\r\n                case 7:\r\n                    _a.sent();\r\n                    return [7 /*endfinally*/];\r\n                case 8: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); });\r\n}\r\nfunction latest(contenders) {\r\n    var _this = this;\r\n    var iters = getIterators(contenders, {\r\n        yieldValues: true,\r\n        returnValues: true,\r\n    });\r\n    return new Repeater(function (push, stop) { return __awaiter(_this, void 0, void 0, function () {\r\n        var advance, advances, stopped, iterations_1, values_2;\r\n        var _this = this;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    if (!iters.length) {\r\n                        stop();\r\n                        return [2 /*return*/, []];\r\n                    }\r\n                    advances = [];\r\n                    stopped = false;\r\n                    stop.then(function () {\r\n                        var e_6, _a;\r\n                        advance();\r\n                        try {\r\n                            for (var advances_2 = __values(advances), advances_2_1 = advances_2.next(); !advances_2_1.done; advances_2_1 = advances_2.next()) {\r\n                                var advance1 = advances_2_1.value;\r\n                                advance1();\r\n                            }\r\n                        }\r\n                        catch (e_6_1) { e_6 = { error: e_6_1 }; }\r\n                        finally {\r\n                            try {\r\n                                if (advances_2_1 && !advances_2_1.done && (_a = advances_2.return)) _a.call(advances_2);\r\n                            }\r\n                            finally { if (e_6) throw e_6.error; }\r\n                        }\r\n                        stopped = true;\r\n                    });\r\n                    _a.label = 1;\r\n                case 1:\r\n                    _a.trys.push([1, , 5, 7]);\r\n                    Promise.all(iters.map(function (iter) { return iter.next(); })).then(function (iterations) { return advance(iterations); }, function (err) { return stop(err); });\r\n                    return [4 /*yield*/, new Promise(function (resolve) { return (advance = resolve); })];\r\n                case 2:\r\n                    iterations_1 = _a.sent();\r\n                    if (iterations_1 === undefined) {\r\n                        return [2 /*return*/];\r\n                    }\r\n                    values_2 = iterations_1.map(function (iteration) { return iteration.value; });\r\n                    if (iterations_1.every(function (iteration) { return iteration.done; })) {\r\n                        return [2 /*return*/, values_2];\r\n                    }\r\n                    // We continuously yield and mutate the same values array so we shallow copy it each time it is pushed.\r\n                    return [4 /*yield*/, push(values_2.slice())];\r\n                case 3:\r\n                    // We continuously yield and mutate the same values array so we shallow copy it each time it is pushed.\r\n                    _a.sent();\r\n                    return [4 /*yield*/, Promise.all(iters.map(function (iter, i) { return __awaiter(_this, void 0, void 0, function () {\r\n                            var iteration;\r\n                            return __generator(this, function (_a) {\r\n                                switch (_a.label) {\r\n                                    case 0:\r\n                                        if (iterations_1[i].done) {\r\n                                            return [2 /*return*/, iterations_1[i].value];\r\n                                        }\r\n                                        _a.label = 1;\r\n                                    case 1:\r\n                                        if (!!stopped) return [3 /*break*/, 4];\r\n                                        Promise.resolve(iter.next()).then(function (iteration) { return advances[i](iteration); }, function (err) { return stop(err); });\r\n                                        return [4 /*yield*/, new Promise(function (resolve) { return (advances[i] = resolve); })];\r\n                                    case 2:\r\n                                        iteration = _a.sent();\r\n                                        if (iteration === undefined) {\r\n                                            return [2 /*return*/, iterations_1[i].value];\r\n                                        }\r\n                                        else if (iteration.done) {\r\n                                            return [2 /*return*/, iteration.value];\r\n                                        }\r\n                                        values_2[i] = iteration.value;\r\n                                        return [4 /*yield*/, push(values_2.slice())];\r\n                                    case 3:\r\n                                        _a.sent();\r\n                                        return [3 /*break*/, 1];\r\n                                    case 4: return [2 /*return*/];\r\n                                }\r\n                            });\r\n                        }); }))];\r\n                case 4: return [2 /*return*/, _a.sent()];\r\n                case 5:\r\n                    stop();\r\n                    return [4 /*yield*/, Promise.all(iters.map(function (iter) { return iter.return && iter.return(); }))];\r\n                case 6:\r\n                    _a.sent();\r\n                    return [7 /*endfinally*/];\r\n                case 7: return [2 /*return*/];\r\n            }\r\n        });\r\n    }); });\r\n}\n\nexports.DroppingBuffer = DroppingBuffer;\nexports.FixedBuffer = FixedBuffer;\nexports.MAX_QUEUE_LENGTH = MAX_QUEUE_LENGTH;\nexports.Repeater = Repeater;\nexports.RepeaterOverflowError = RepeaterOverflowError;\nexports.SlidingBuffer = SlidingBuffer;\n//# sourceMappingURL=repeater.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@repeaterjs+repeater@3.0.6/node_modules/@repeaterjs/repeater/cjs/repeater.js\n");

/***/ })

};
;