"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+util-retry@4.0.5";
exports.ids = ["vendor-chunks/@smithy+util-retry@4.0.5"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/AdaptiveRetryStrategy.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/AdaptiveRetryStrategy.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdaptiveRetryStrategy: () => (/* binding */ AdaptiveRetryStrategy)\n/* harmony export */ });\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/config.js\");\n/* harmony import */ var _DefaultRateLimiter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DefaultRateLimiter */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/DefaultRateLimiter.js\");\n/* harmony import */ var _StandardRetryStrategy__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./StandardRetryStrategy */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/StandardRetryStrategy.js\");\n\n\n\nclass AdaptiveRetryStrategy {\n    constructor(maxAttemptsProvider, options) {\n        this.maxAttemptsProvider = maxAttemptsProvider;\n        this.mode = _config__WEBPACK_IMPORTED_MODULE_0__.RETRY_MODES.ADAPTIVE;\n        const { rateLimiter } = options ?? {};\n        this.rateLimiter = rateLimiter ?? new _DefaultRateLimiter__WEBPACK_IMPORTED_MODULE_1__.DefaultRateLimiter();\n        this.standardRetryStrategy = new _StandardRetryStrategy__WEBPACK_IMPORTED_MODULE_2__.StandardRetryStrategy(maxAttemptsProvider);\n    }\n    async acquireInitialRetryToken(retryTokenScope) {\n        await this.rateLimiter.getSendToken();\n        return this.standardRetryStrategy.acquireInitialRetryToken(retryTokenScope);\n    }\n    async refreshRetryTokenForRetry(tokenToRenew, errorInfo) {\n        this.rateLimiter.updateClientSendingRate(errorInfo);\n        return this.standardRetryStrategy.refreshRetryTokenForRetry(tokenToRenew, errorInfo);\n    }\n    recordSuccess(token) {\n        this.rateLimiter.updateClientSendingRate({});\n        this.standardRetryStrategy.recordSuccess(token);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXJldHJ5QDQuMC41L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtcmV0cnkvZGlzdC1lcy9BZGFwdGl2ZVJldHJ5U3RyYXRlZ3kuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUF1QztBQUNtQjtBQUNNO0FBQ3pEO0FBQ1A7QUFDQTtBQUNBLG9CQUFvQixnREFBVztBQUMvQixnQkFBZ0IsY0FBYztBQUM5Qiw4Q0FBOEMsbUVBQWtCO0FBQ2hFLHlDQUF5Qyx5RUFBcUI7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQ7QUFDbkQ7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXJldHJ5QDQuMC41L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtcmV0cnkvZGlzdC1lcy9BZGFwdGl2ZVJldHJ5U3RyYXRlZ3kuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUkVUUllfTU9ERVMgfSBmcm9tIFwiLi9jb25maWdcIjtcbmltcG9ydCB7IERlZmF1bHRSYXRlTGltaXRlciB9IGZyb20gXCIuL0RlZmF1bHRSYXRlTGltaXRlclwiO1xuaW1wb3J0IHsgU3RhbmRhcmRSZXRyeVN0cmF0ZWd5IH0gZnJvbSBcIi4vU3RhbmRhcmRSZXRyeVN0cmF0ZWd5XCI7XG5leHBvcnQgY2xhc3MgQWRhcHRpdmVSZXRyeVN0cmF0ZWd5IHtcbiAgICBjb25zdHJ1Y3RvcihtYXhBdHRlbXB0c1Byb3ZpZGVyLCBvcHRpb25zKSB7XG4gICAgICAgIHRoaXMubWF4QXR0ZW1wdHNQcm92aWRlciA9IG1heEF0dGVtcHRzUHJvdmlkZXI7XG4gICAgICAgIHRoaXMubW9kZSA9IFJFVFJZX01PREVTLkFEQVBUSVZFO1xuICAgICAgICBjb25zdCB7IHJhdGVMaW1pdGVyIH0gPSBvcHRpb25zID8/IHt9O1xuICAgICAgICB0aGlzLnJhdGVMaW1pdGVyID0gcmF0ZUxpbWl0ZXIgPz8gbmV3IERlZmF1bHRSYXRlTGltaXRlcigpO1xuICAgICAgICB0aGlzLnN0YW5kYXJkUmV0cnlTdHJhdGVneSA9IG5ldyBTdGFuZGFyZFJldHJ5U3RyYXRlZ3kobWF4QXR0ZW1wdHNQcm92aWRlcik7XG4gICAgfVxuICAgIGFzeW5jIGFjcXVpcmVJbml0aWFsUmV0cnlUb2tlbihyZXRyeVRva2VuU2NvcGUpIHtcbiAgICAgICAgYXdhaXQgdGhpcy5yYXRlTGltaXRlci5nZXRTZW5kVG9rZW4oKTtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RhbmRhcmRSZXRyeVN0cmF0ZWd5LmFjcXVpcmVJbml0aWFsUmV0cnlUb2tlbihyZXRyeVRva2VuU2NvcGUpO1xuICAgIH1cbiAgICBhc3luYyByZWZyZXNoUmV0cnlUb2tlbkZvclJldHJ5KHRva2VuVG9SZW5ldywgZXJyb3JJbmZvKSB7XG4gICAgICAgIHRoaXMucmF0ZUxpbWl0ZXIudXBkYXRlQ2xpZW50U2VuZGluZ1JhdGUoZXJyb3JJbmZvKTtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RhbmRhcmRSZXRyeVN0cmF0ZWd5LnJlZnJlc2hSZXRyeVRva2VuRm9yUmV0cnkodG9rZW5Ub1JlbmV3LCBlcnJvckluZm8pO1xuICAgIH1cbiAgICByZWNvcmRTdWNjZXNzKHRva2VuKSB7XG4gICAgICAgIHRoaXMucmF0ZUxpbWl0ZXIudXBkYXRlQ2xpZW50U2VuZGluZ1JhdGUoe30pO1xuICAgICAgICB0aGlzLnN0YW5kYXJkUmV0cnlTdHJhdGVneS5yZWNvcmRTdWNjZXNzKHRva2VuKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/AdaptiveRetryStrategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/ConfiguredRetryStrategy.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/ConfiguredRetryStrategy.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfiguredRetryStrategy: () => (/* binding */ ConfiguredRetryStrategy)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/constants.js\");\n/* harmony import */ var _StandardRetryStrategy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./StandardRetryStrategy */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/StandardRetryStrategy.js\");\n\n\nclass ConfiguredRetryStrategy extends _StandardRetryStrategy__WEBPACK_IMPORTED_MODULE_1__.StandardRetryStrategy {\n    constructor(maxAttempts, computeNextBackoffDelay = _constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_RETRY_DELAY_BASE) {\n        super(typeof maxAttempts === \"function\" ? maxAttempts : async () => maxAttempts);\n        if (typeof computeNextBackoffDelay === \"number\") {\n            this.computeNextBackoffDelay = () => computeNextBackoffDelay;\n        }\n        else {\n            this.computeNextBackoffDelay = computeNextBackoffDelay;\n        }\n    }\n    async refreshRetryTokenForRetry(tokenToRenew, errorInfo) {\n        const token = await super.refreshRetryTokenForRetry(tokenToRenew, errorInfo);\n        token.getRetryDelay = () => this.computeNextBackoffDelay(token.getRetryCount());\n        return token;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXJldHJ5QDQuMC41L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtcmV0cnkvZGlzdC1lcy9Db25maWd1cmVkUmV0cnlTdHJhdGVneS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBdUQ7QUFDUztBQUN6RCxzQ0FBc0MseUVBQXFCO0FBQ2xFLHVEQUF1RCxnRUFBd0I7QUFDL0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1yZXRyeUA0LjAuNS9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXJldHJ5L2Rpc3QtZXMvQ29uZmlndXJlZFJldHJ5U3RyYXRlZ3kuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgREVGQVVMVF9SRVRSWV9ERUxBWV9CQVNFIH0gZnJvbSBcIi4vY29uc3RhbnRzXCI7XG5pbXBvcnQgeyBTdGFuZGFyZFJldHJ5U3RyYXRlZ3kgfSBmcm9tIFwiLi9TdGFuZGFyZFJldHJ5U3RyYXRlZ3lcIjtcbmV4cG9ydCBjbGFzcyBDb25maWd1cmVkUmV0cnlTdHJhdGVneSBleHRlbmRzIFN0YW5kYXJkUmV0cnlTdHJhdGVneSB7XG4gICAgY29uc3RydWN0b3IobWF4QXR0ZW1wdHMsIGNvbXB1dGVOZXh0QmFja29mZkRlbGF5ID0gREVGQVVMVF9SRVRSWV9ERUxBWV9CQVNFKSB7XG4gICAgICAgIHN1cGVyKHR5cGVvZiBtYXhBdHRlbXB0cyA9PT0gXCJmdW5jdGlvblwiID8gbWF4QXR0ZW1wdHMgOiBhc3luYyAoKSA9PiBtYXhBdHRlbXB0cyk7XG4gICAgICAgIGlmICh0eXBlb2YgY29tcHV0ZU5leHRCYWNrb2ZmRGVsYXkgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgICAgIHRoaXMuY29tcHV0ZU5leHRCYWNrb2ZmRGVsYXkgPSAoKSA9PiBjb21wdXRlTmV4dEJhY2tvZmZEZWxheTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuY29tcHV0ZU5leHRCYWNrb2ZmRGVsYXkgPSBjb21wdXRlTmV4dEJhY2tvZmZEZWxheTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBhc3luYyByZWZyZXNoUmV0cnlUb2tlbkZvclJldHJ5KHRva2VuVG9SZW5ldywgZXJyb3JJbmZvKSB7XG4gICAgICAgIGNvbnN0IHRva2VuID0gYXdhaXQgc3VwZXIucmVmcmVzaFJldHJ5VG9rZW5Gb3JSZXRyeSh0b2tlblRvUmVuZXcsIGVycm9ySW5mbyk7XG4gICAgICAgIHRva2VuLmdldFJldHJ5RGVsYXkgPSAoKSA9PiB0aGlzLmNvbXB1dGVOZXh0QmFja29mZkRlbGF5KHRva2VuLmdldFJldHJ5Q291bnQoKSk7XG4gICAgICAgIHJldHVybiB0b2tlbjtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/ConfiguredRetryStrategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/DefaultRateLimiter.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/DefaultRateLimiter.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultRateLimiter: () => (/* binding */ DefaultRateLimiter)\n/* harmony export */ });\n/* harmony import */ var _smithy_service_error_classification__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/service-error-classification */ \"(rsc)/./node_modules/.pnpm/@smithy+service-error-classification@4.0.5/node_modules/@smithy/service-error-classification/dist-es/index.js\");\n\nclass DefaultRateLimiter {\n    constructor(options) {\n        this.currentCapacity = 0;\n        this.enabled = false;\n        this.lastMaxRate = 0;\n        this.measuredTxRate = 0;\n        this.requestCount = 0;\n        this.lastTimestamp = 0;\n        this.timeWindow = 0;\n        this.beta = options?.beta ?? 0.7;\n        this.minCapacity = options?.minCapacity ?? 1;\n        this.minFillRate = options?.minFillRate ?? 0.5;\n        this.scaleConstant = options?.scaleConstant ?? 0.4;\n        this.smooth = options?.smooth ?? 0.8;\n        const currentTimeInSeconds = this.getCurrentTimeInSeconds();\n        this.lastThrottleTime = currentTimeInSeconds;\n        this.lastTxRateBucket = Math.floor(this.getCurrentTimeInSeconds());\n        this.fillRate = this.minFillRate;\n        this.maxCapacity = this.minCapacity;\n    }\n    getCurrentTimeInSeconds() {\n        return Date.now() / 1000;\n    }\n    async getSendToken() {\n        return this.acquireTokenBucket(1);\n    }\n    async acquireTokenBucket(amount) {\n        if (!this.enabled) {\n            return;\n        }\n        this.refillTokenBucket();\n        if (amount > this.currentCapacity) {\n            const delay = ((amount - this.currentCapacity) / this.fillRate) * 1000;\n            await new Promise((resolve) => DefaultRateLimiter.setTimeoutFn(resolve, delay));\n        }\n        this.currentCapacity = this.currentCapacity - amount;\n    }\n    refillTokenBucket() {\n        const timestamp = this.getCurrentTimeInSeconds();\n        if (!this.lastTimestamp) {\n            this.lastTimestamp = timestamp;\n            return;\n        }\n        const fillAmount = (timestamp - this.lastTimestamp) * this.fillRate;\n        this.currentCapacity = Math.min(this.maxCapacity, this.currentCapacity + fillAmount);\n        this.lastTimestamp = timestamp;\n    }\n    updateClientSendingRate(response) {\n        let calculatedRate;\n        this.updateMeasuredRate();\n        if ((0,_smithy_service_error_classification__WEBPACK_IMPORTED_MODULE_0__.isThrottlingError)(response)) {\n            const rateToUse = !this.enabled ? this.measuredTxRate : Math.min(this.measuredTxRate, this.fillRate);\n            this.lastMaxRate = rateToUse;\n            this.calculateTimeWindow();\n            this.lastThrottleTime = this.getCurrentTimeInSeconds();\n            calculatedRate = this.cubicThrottle(rateToUse);\n            this.enableTokenBucket();\n        }\n        else {\n            this.calculateTimeWindow();\n            calculatedRate = this.cubicSuccess(this.getCurrentTimeInSeconds());\n        }\n        const newRate = Math.min(calculatedRate, 2 * this.measuredTxRate);\n        this.updateTokenBucketRate(newRate);\n    }\n    calculateTimeWindow() {\n        this.timeWindow = this.getPrecise(Math.pow((this.lastMaxRate * (1 - this.beta)) / this.scaleConstant, 1 / 3));\n    }\n    cubicThrottle(rateToUse) {\n        return this.getPrecise(rateToUse * this.beta);\n    }\n    cubicSuccess(timestamp) {\n        return this.getPrecise(this.scaleConstant * Math.pow(timestamp - this.lastThrottleTime - this.timeWindow, 3) + this.lastMaxRate);\n    }\n    enableTokenBucket() {\n        this.enabled = true;\n    }\n    updateTokenBucketRate(newRate) {\n        this.refillTokenBucket();\n        this.fillRate = Math.max(newRate, this.minFillRate);\n        this.maxCapacity = Math.max(newRate, this.minCapacity);\n        this.currentCapacity = Math.min(this.currentCapacity, this.maxCapacity);\n    }\n    updateMeasuredRate() {\n        const t = this.getCurrentTimeInSeconds();\n        const timeBucket = Math.floor(t * 2) / 2;\n        this.requestCount++;\n        if (timeBucket > this.lastTxRateBucket) {\n            const currentRate = this.requestCount / (timeBucket - this.lastTxRateBucket);\n            this.measuredTxRate = this.getPrecise(currentRate * this.smooth + this.measuredTxRate * (1 - this.smooth));\n            this.requestCount = 0;\n            this.lastTxRateBucket = timeBucket;\n        }\n    }\n    getPrecise(num) {\n        return parseFloat(num.toFixed(8));\n    }\n}\nDefaultRateLimiter.setTimeoutFn = setTimeout;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/DefaultRateLimiter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/StandardRetryStrategy.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/StandardRetryStrategy.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StandardRetryStrategy: () => (/* binding */ StandardRetryStrategy)\n/* harmony export */ });\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/config.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/constants.js\");\n/* harmony import */ var _defaultRetryBackoffStrategy__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./defaultRetryBackoffStrategy */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/defaultRetryBackoffStrategy.js\");\n/* harmony import */ var _defaultRetryToken__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./defaultRetryToken */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/defaultRetryToken.js\");\n\n\n\n\nclass StandardRetryStrategy {\n    constructor(maxAttempts) {\n        this.maxAttempts = maxAttempts;\n        this.mode = _config__WEBPACK_IMPORTED_MODULE_0__.RETRY_MODES.STANDARD;\n        this.capacity = _constants__WEBPACK_IMPORTED_MODULE_1__.INITIAL_RETRY_TOKENS;\n        this.retryBackoffStrategy = (0,_defaultRetryBackoffStrategy__WEBPACK_IMPORTED_MODULE_2__.getDefaultRetryBackoffStrategy)();\n        this.maxAttemptsProvider = typeof maxAttempts === \"function\" ? maxAttempts : async () => maxAttempts;\n    }\n    async acquireInitialRetryToken(retryTokenScope) {\n        return (0,_defaultRetryToken__WEBPACK_IMPORTED_MODULE_3__.createDefaultRetryToken)({\n            retryDelay: _constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_RETRY_DELAY_BASE,\n            retryCount: 0,\n        });\n    }\n    async refreshRetryTokenForRetry(token, errorInfo) {\n        const maxAttempts = await this.getMaxAttempts();\n        if (this.shouldRetry(token, errorInfo, maxAttempts)) {\n            const errorType = errorInfo.errorType;\n            this.retryBackoffStrategy.setDelayBase(errorType === \"THROTTLING\" ? _constants__WEBPACK_IMPORTED_MODULE_1__.THROTTLING_RETRY_DELAY_BASE : _constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_RETRY_DELAY_BASE);\n            const delayFromErrorType = this.retryBackoffStrategy.computeNextBackoffDelay(token.getRetryCount());\n            const retryDelay = errorInfo.retryAfterHint\n                ? Math.max(errorInfo.retryAfterHint.getTime() - Date.now() || 0, delayFromErrorType)\n                : delayFromErrorType;\n            const capacityCost = this.getCapacityCost(errorType);\n            this.capacity -= capacityCost;\n            return (0,_defaultRetryToken__WEBPACK_IMPORTED_MODULE_3__.createDefaultRetryToken)({\n                retryDelay,\n                retryCount: token.getRetryCount() + 1,\n                retryCost: capacityCost,\n            });\n        }\n        throw new Error(\"No retry token available\");\n    }\n    recordSuccess(token) {\n        this.capacity = Math.max(_constants__WEBPACK_IMPORTED_MODULE_1__.INITIAL_RETRY_TOKENS, this.capacity + (token.getRetryCost() ?? _constants__WEBPACK_IMPORTED_MODULE_1__.NO_RETRY_INCREMENT));\n    }\n    getCapacity() {\n        return this.capacity;\n    }\n    async getMaxAttempts() {\n        try {\n            return await this.maxAttemptsProvider();\n        }\n        catch (error) {\n            console.warn(`Max attempts provider could not resolve. Using default of ${_config__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_MAX_ATTEMPTS}`);\n            return _config__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_MAX_ATTEMPTS;\n        }\n    }\n    shouldRetry(tokenToRenew, errorInfo, maxAttempts) {\n        const attempts = tokenToRenew.getRetryCount() + 1;\n        return (attempts < maxAttempts &&\n            this.capacity >= this.getCapacityCost(errorInfo.errorType) &&\n            this.isRetryableError(errorInfo.errorType));\n    }\n    getCapacityCost(errorType) {\n        return errorType === \"TRANSIENT\" ? _constants__WEBPACK_IMPORTED_MODULE_1__.TIMEOUT_RETRY_COST : _constants__WEBPACK_IMPORTED_MODULE_1__.RETRY_COST;\n    }\n    isRetryableError(errorType) {\n        return errorType === \"THROTTLING\" || errorType === \"TRANSIENT\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/StandardRetryStrategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/config.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/config.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_MAX_ATTEMPTS: () => (/* binding */ DEFAULT_MAX_ATTEMPTS),\n/* harmony export */   DEFAULT_RETRY_MODE: () => (/* binding */ DEFAULT_RETRY_MODE),\n/* harmony export */   RETRY_MODES: () => (/* binding */ RETRY_MODES)\n/* harmony export */ });\nvar RETRY_MODES;\n(function (RETRY_MODES) {\n    RETRY_MODES[\"STANDARD\"] = \"standard\";\n    RETRY_MODES[\"ADAPTIVE\"] = \"adaptive\";\n})(RETRY_MODES || (RETRY_MODES = {}));\nconst DEFAULT_MAX_ATTEMPTS = 3;\nconst DEFAULT_RETRY_MODE = RETRY_MODES.STANDARD;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXJldHJ5QDQuMC41L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtcmV0cnkvZGlzdC1lcy9jb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxDQUFDLGtDQUFrQztBQUM1QjtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXJldHJ5QDQuMC41L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtcmV0cnkvZGlzdC1lcy9jb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBSRVRSWV9NT0RFUztcbihmdW5jdGlvbiAoUkVUUllfTU9ERVMpIHtcbiAgICBSRVRSWV9NT0RFU1tcIlNUQU5EQVJEXCJdID0gXCJzdGFuZGFyZFwiO1xuICAgIFJFVFJZX01PREVTW1wiQURBUFRJVkVcIl0gPSBcImFkYXB0aXZlXCI7XG59KShSRVRSWV9NT0RFUyB8fCAoUkVUUllfTU9ERVMgPSB7fSkpO1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfTUFYX0FUVEVNUFRTID0gMztcbmV4cG9ydCBjb25zdCBERUZBVUxUX1JFVFJZX01PREUgPSBSRVRSWV9NT0RFUy5TVEFOREFSRDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/config.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/constants.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/constants.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_RETRY_DELAY_BASE: () => (/* binding */ DEFAULT_RETRY_DELAY_BASE),\n/* harmony export */   INITIAL_RETRY_TOKENS: () => (/* binding */ INITIAL_RETRY_TOKENS),\n/* harmony export */   INVOCATION_ID_HEADER: () => (/* binding */ INVOCATION_ID_HEADER),\n/* harmony export */   MAXIMUM_RETRY_DELAY: () => (/* binding */ MAXIMUM_RETRY_DELAY),\n/* harmony export */   NO_RETRY_INCREMENT: () => (/* binding */ NO_RETRY_INCREMENT),\n/* harmony export */   REQUEST_HEADER: () => (/* binding */ REQUEST_HEADER),\n/* harmony export */   RETRY_COST: () => (/* binding */ RETRY_COST),\n/* harmony export */   THROTTLING_RETRY_DELAY_BASE: () => (/* binding */ THROTTLING_RETRY_DELAY_BASE),\n/* harmony export */   TIMEOUT_RETRY_COST: () => (/* binding */ TIMEOUT_RETRY_COST)\n/* harmony export */ });\nconst DEFAULT_RETRY_DELAY_BASE = 100;\nconst MAXIMUM_RETRY_DELAY = 20 * 1000;\nconst THROTTLING_RETRY_DELAY_BASE = 500;\nconst INITIAL_RETRY_TOKENS = 500;\nconst RETRY_COST = 5;\nconst TIMEOUT_RETRY_COST = 10;\nconst NO_RETRY_INCREMENT = 1;\nconst INVOCATION_ID_HEADER = \"amz-sdk-invocation-id\";\nconst REQUEST_HEADER = \"amz-sdk-request\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXJldHJ5QDQuMC41L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtcmV0cnkvZGlzdC1lcy9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXJldHJ5QDQuMC41L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtcmV0cnkvZGlzdC1lcy9jb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IERFRkFVTFRfUkVUUllfREVMQVlfQkFTRSA9IDEwMDtcbmV4cG9ydCBjb25zdCBNQVhJTVVNX1JFVFJZX0RFTEFZID0gMjAgKiAxMDAwO1xuZXhwb3J0IGNvbnN0IFRIUk9UVExJTkdfUkVUUllfREVMQVlfQkFTRSA9IDUwMDtcbmV4cG9ydCBjb25zdCBJTklUSUFMX1JFVFJZX1RPS0VOUyA9IDUwMDtcbmV4cG9ydCBjb25zdCBSRVRSWV9DT1NUID0gNTtcbmV4cG9ydCBjb25zdCBUSU1FT1VUX1JFVFJZX0NPU1QgPSAxMDtcbmV4cG9ydCBjb25zdCBOT19SRVRSWV9JTkNSRU1FTlQgPSAxO1xuZXhwb3J0IGNvbnN0IElOVk9DQVRJT05fSURfSEVBREVSID0gXCJhbXotc2RrLWludm9jYXRpb24taWRcIjtcbmV4cG9ydCBjb25zdCBSRVFVRVNUX0hFQURFUiA9IFwiYW16LXNkay1yZXF1ZXN0XCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/defaultRetryBackoffStrategy.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/defaultRetryBackoffStrategy.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaultRetryBackoffStrategy: () => (/* binding */ getDefaultRetryBackoffStrategy)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/constants.js\");\n\nconst getDefaultRetryBackoffStrategy = () => {\n    let delayBase = _constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_RETRY_DELAY_BASE;\n    const computeNextBackoffDelay = (attempts) => {\n        return Math.floor(Math.min(_constants__WEBPACK_IMPORTED_MODULE_0__.MAXIMUM_RETRY_DELAY, Math.random() * 2 ** attempts * delayBase));\n    };\n    const setDelayBase = (delay) => {\n        delayBase = delay;\n    };\n    return {\n        computeNextBackoffDelay,\n        setDelayBase,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXJldHJ5QDQuMC41L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtcmV0cnkvZGlzdC1lcy9kZWZhdWx0UmV0cnlCYWNrb2ZmU3RyYXRlZ3kuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEU7QUFDckU7QUFDUCxvQkFBb0IsZ0VBQXdCO0FBQzVDO0FBQ0EsbUNBQW1DLDJEQUFtQjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtcmV0cnlANC4wLjUvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1yZXRyeS9kaXN0LWVzL2RlZmF1bHRSZXRyeUJhY2tvZmZTdHJhdGVneS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBERUZBVUxUX1JFVFJZX0RFTEFZX0JBU0UsIE1BWElNVU1fUkVUUllfREVMQVkgfSBmcm9tIFwiLi9jb25zdGFudHNcIjtcbmV4cG9ydCBjb25zdCBnZXREZWZhdWx0UmV0cnlCYWNrb2ZmU3RyYXRlZ3kgPSAoKSA9PiB7XG4gICAgbGV0IGRlbGF5QmFzZSA9IERFRkFVTFRfUkVUUllfREVMQVlfQkFTRTtcbiAgICBjb25zdCBjb21wdXRlTmV4dEJhY2tvZmZEZWxheSA9IChhdHRlbXB0cykgPT4ge1xuICAgICAgICByZXR1cm4gTWF0aC5mbG9vcihNYXRoLm1pbihNQVhJTVVNX1JFVFJZX0RFTEFZLCBNYXRoLnJhbmRvbSgpICogMiAqKiBhdHRlbXB0cyAqIGRlbGF5QmFzZSkpO1xuICAgIH07XG4gICAgY29uc3Qgc2V0RGVsYXlCYXNlID0gKGRlbGF5KSA9PiB7XG4gICAgICAgIGRlbGF5QmFzZSA9IGRlbGF5O1xuICAgIH07XG4gICAgcmV0dXJuIHtcbiAgICAgICAgY29tcHV0ZU5leHRCYWNrb2ZmRGVsYXksXG4gICAgICAgIHNldERlbGF5QmFzZSxcbiAgICB9O1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/defaultRetryBackoffStrategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/defaultRetryToken.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/defaultRetryToken.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDefaultRetryToken: () => (/* binding */ createDefaultRetryToken)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/constants.js\");\n\nconst createDefaultRetryToken = ({ retryDelay, retryCount, retryCost, }) => {\n    const getRetryCount = () => retryCount;\n    const getRetryDelay = () => Math.min(_constants__WEBPACK_IMPORTED_MODULE_0__.MAXIMUM_RETRY_DELAY, retryDelay);\n    const getRetryCost = () => retryCost;\n    return {\n        getRetryCount,\n        getRetryDelay,\n        getRetryCost,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXJldHJ5QDQuMC41L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtcmV0cnkvZGlzdC1lcy9kZWZhdWx0UmV0cnlUb2tlbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRDtBQUMzQyxtQ0FBbUMsb0NBQW9DO0FBQzlFO0FBQ0EseUNBQXlDLDJEQUFtQjtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1yZXRyeUA0LjAuNS9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXJldHJ5L2Rpc3QtZXMvZGVmYXVsdFJldHJ5VG9rZW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTUFYSU1VTV9SRVRSWV9ERUxBWSB9IGZyb20gXCIuL2NvbnN0YW50c1wiO1xuZXhwb3J0IGNvbnN0IGNyZWF0ZURlZmF1bHRSZXRyeVRva2VuID0gKHsgcmV0cnlEZWxheSwgcmV0cnlDb3VudCwgcmV0cnlDb3N0LCB9KSA9PiB7XG4gICAgY29uc3QgZ2V0UmV0cnlDb3VudCA9ICgpID0+IHJldHJ5Q291bnQ7XG4gICAgY29uc3QgZ2V0UmV0cnlEZWxheSA9ICgpID0+IE1hdGgubWluKE1BWElNVU1fUkVUUllfREVMQVksIHJldHJ5RGVsYXkpO1xuICAgIGNvbnN0IGdldFJldHJ5Q29zdCA9ICgpID0+IHJldHJ5Q29zdDtcbiAgICByZXR1cm4ge1xuICAgICAgICBnZXRSZXRyeUNvdW50LFxuICAgICAgICBnZXRSZXRyeURlbGF5LFxuICAgICAgICBnZXRSZXRyeUNvc3QsXG4gICAgfTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/defaultRetryToken.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdaptiveRetryStrategy: () => (/* reexport safe */ _AdaptiveRetryStrategy__WEBPACK_IMPORTED_MODULE_0__.AdaptiveRetryStrategy),\n/* harmony export */   ConfiguredRetryStrategy: () => (/* reexport safe */ _ConfiguredRetryStrategy__WEBPACK_IMPORTED_MODULE_1__.ConfiguredRetryStrategy),\n/* harmony export */   DEFAULT_MAX_ATTEMPTS: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_4__.DEFAULT_MAX_ATTEMPTS),\n/* harmony export */   DEFAULT_RETRY_DELAY_BASE: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_5__.DEFAULT_RETRY_DELAY_BASE),\n/* harmony export */   DEFAULT_RETRY_MODE: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_4__.DEFAULT_RETRY_MODE),\n/* harmony export */   DefaultRateLimiter: () => (/* reexport safe */ _DefaultRateLimiter__WEBPACK_IMPORTED_MODULE_2__.DefaultRateLimiter),\n/* harmony export */   INITIAL_RETRY_TOKENS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_5__.INITIAL_RETRY_TOKENS),\n/* harmony export */   INVOCATION_ID_HEADER: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_5__.INVOCATION_ID_HEADER),\n/* harmony export */   MAXIMUM_RETRY_DELAY: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_5__.MAXIMUM_RETRY_DELAY),\n/* harmony export */   NO_RETRY_INCREMENT: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_5__.NO_RETRY_INCREMENT),\n/* harmony export */   REQUEST_HEADER: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_5__.REQUEST_HEADER),\n/* harmony export */   RETRY_COST: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_5__.RETRY_COST),\n/* harmony export */   RETRY_MODES: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_4__.RETRY_MODES),\n/* harmony export */   StandardRetryStrategy: () => (/* reexport safe */ _StandardRetryStrategy__WEBPACK_IMPORTED_MODULE_3__.StandardRetryStrategy),\n/* harmony export */   THROTTLING_RETRY_DELAY_BASE: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_5__.THROTTLING_RETRY_DELAY_BASE),\n/* harmony export */   TIMEOUT_RETRY_COST: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_5__.TIMEOUT_RETRY_COST)\n/* harmony export */ });\n/* harmony import */ var _AdaptiveRetryStrategy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AdaptiveRetryStrategy */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/AdaptiveRetryStrategy.js\");\n/* harmony import */ var _ConfiguredRetryStrategy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ConfiguredRetryStrategy */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/ConfiguredRetryStrategy.js\");\n/* harmony import */ var _DefaultRateLimiter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DefaultRateLimiter */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/DefaultRateLimiter.js\");\n/* harmony import */ var _StandardRetryStrategy__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StandardRetryStrategy */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/StandardRetryStrategy.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/config.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/constants.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/types.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXJldHJ5QDQuMC41L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtcmV0cnkvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF3QztBQUNFO0FBQ0w7QUFDRztBQUNmO0FBQ0c7QUFDSiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1yZXRyeUA0LjAuNS9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXJldHJ5L2Rpc3QtZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vQWRhcHRpdmVSZXRyeVN0cmF0ZWd5XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9Db25maWd1cmVkUmV0cnlTdHJhdGVneVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vRGVmYXVsdFJhdGVMaW1pdGVyXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9TdGFuZGFyZFJldHJ5U3RyYXRlZ3lcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2NvbmZpZ1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vY29uc3RhbnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi90eXBlc1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/types.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/types.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXJldHJ5QDQuMC41L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtcmV0cnkvZGlzdC1lcy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiO0FBQVUiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtcmV0cnlANC4wLjUvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1yZXRyeS9kaXN0LWVzL3R5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/types.js\n");

/***/ })

};
;