"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+credential-provider-http@3.826.0";
exports.ids = ["vendor-chunks/@aws-sdk+credential-provider-http@3.826.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/checkUrl.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/checkUrl.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkUrl: () => (/* binding */ checkUrl)\n/* harmony export */ });\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n\nconst LOOPBACK_CIDR_IPv4 = \"*********/8\";\nconst LOOPBACK_CIDR_IPv6 = \"::1/128\";\nconst ECS_CONTAINER_HOST = \"*************\";\nconst EKS_CONTAINER_HOST_IPv4 = \"*************3\";\nconst EKS_CONTAINER_HOST_IPv6 = \"[fd00:ec2::23]\";\nconst checkUrl = (url, logger) => {\n    if (url.protocol === \"https:\") {\n        return;\n    }\n    if (url.hostname === ECS_CONTAINER_HOST ||\n        url.hostname === EKS_CONTAINER_HOST_IPv4 ||\n        url.hostname === EKS_CONTAINER_HOST_IPv6) {\n        return;\n    }\n    if (url.hostname.includes(\"[\")) {\n        if (url.hostname === \"[::1]\" || url.hostname === \"[0000:0000:0000:0000:0000:0000:0000:0001]\") {\n            return;\n        }\n    }\n    else {\n        if (url.hostname === \"localhost\") {\n            return;\n        }\n        const ipComponents = url.hostname.split(\".\");\n        const inRange = (component) => {\n            const num = parseInt(component, 10);\n            return 0 <= num && num <= 255;\n        };\n        if (ipComponents[0] === \"127\" &&\n            inRange(ipComponents[1]) &&\n            inRange(ipComponents[2]) &&\n            inRange(ipComponents[3]) &&\n            ipComponents.length === 4) {\n            return;\n        }\n    }\n    throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`URL not accepted. It must either be HTTPS or match one of the following:\n  - loopback CIDR *********/8 or [::1/128]\n  - ECS container host *************\n  - EKS container host *************3 or [fd00:ec2::23]`, { logger });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/checkUrl.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/fromHttp.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/fromHttp.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromHttp: () => (/* binding */ fromHttp)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @aws-sdk/core/client */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\");\n/* harmony import */ var _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/node-http-handler */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/index.js\");\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _checkUrl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./checkUrl */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/checkUrl.js\");\n/* harmony import */ var _requestHelpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./requestHelpers */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/requestHelpers.js\");\n/* harmony import */ var _retry_wrapper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./retry-wrapper */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/retry-wrapper.js\");\n\n\n\n\n\n\n\nconst AWS_CONTAINER_CREDENTIALS_RELATIVE_URI = \"AWS_CONTAINER_CREDENTIALS_RELATIVE_URI\";\nconst DEFAULT_LINK_LOCAL_HOST = \"http://*************\";\nconst AWS_CONTAINER_CREDENTIALS_FULL_URI = \"AWS_CONTAINER_CREDENTIALS_FULL_URI\";\nconst AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE = \"AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE\";\nconst AWS_CONTAINER_AUTHORIZATION_TOKEN = \"AWS_CONTAINER_AUTHORIZATION_TOKEN\";\nconst fromHttp = (options = {}) => {\n    options.logger?.debug(\"@aws-sdk/credential-provider-http - fromHttp\");\n    let host;\n    const relative = options.awsContainerCredentialsRelativeUri ?? process.env[AWS_CONTAINER_CREDENTIALS_RELATIVE_URI];\n    const full = options.awsContainerCredentialsFullUri ?? process.env[AWS_CONTAINER_CREDENTIALS_FULL_URI];\n    const token = options.awsContainerAuthorizationToken ?? process.env[AWS_CONTAINER_AUTHORIZATION_TOKEN];\n    const tokenFile = options.awsContainerAuthorizationTokenFile ?? process.env[AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE];\n    const warn = options.logger?.constructor?.name === \"NoOpLogger\" || !options.logger ? console.warn : options.logger.warn;\n    if (relative && full) {\n        warn(\"@aws-sdk/credential-provider-http: \" +\n            \"you have set both awsContainerCredentialsRelativeUri and awsContainerCredentialsFullUri.\");\n        warn(\"awsContainerCredentialsFullUri will take precedence.\");\n    }\n    if (token && tokenFile) {\n        warn(\"@aws-sdk/credential-provider-http: \" +\n            \"you have set both awsContainerAuthorizationToken and awsContainerAuthorizationTokenFile.\");\n        warn(\"awsContainerAuthorizationToken will take precedence.\");\n    }\n    if (full) {\n        host = full;\n    }\n    else if (relative) {\n        host = `${DEFAULT_LINK_LOCAL_HOST}${relative}`;\n    }\n    else {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_1__.CredentialsProviderError(`No HTTP credential provider host provided.\nSet AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI.`, { logger: options.logger });\n    }\n    const url = new URL(host);\n    (0,_checkUrl__WEBPACK_IMPORTED_MODULE_3__.checkUrl)(url, options.logger);\n    const requestHandler = new _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_0__.NodeHttpHandler({\n        requestTimeout: options.timeout ?? 1000,\n        connectionTimeout: options.timeout ?? 1000,\n    });\n    return (0,_retry_wrapper__WEBPACK_IMPORTED_MODULE_5__.retryWrapper)(async () => {\n        const request = (0,_requestHelpers__WEBPACK_IMPORTED_MODULE_4__.createGetRequest)(url);\n        if (token) {\n            request.headers.Authorization = token;\n        }\n        else if (tokenFile) {\n            request.headers.Authorization = (await fs_promises__WEBPACK_IMPORTED_MODULE_2___default().readFile(tokenFile)).toString();\n        }\n        try {\n            const result = await requestHandler.handle(request);\n            return (0,_requestHelpers__WEBPACK_IMPORTED_MODULE_4__.getCredentials)(result.response).then((creds) => (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_6__.setCredentialFeature)(creds, \"CREDENTIALS_HTTP\", \"z\"));\n        }\n        catch (e) {\n            throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_1__.CredentialsProviderError(String(e), { logger: options.logger });\n        }\n    }, options.maxRetries ?? 3, options.timeout ?? 1000);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/fromHttp.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/requestHelpers.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/requestHelpers.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createGetRequest: () => (/* binding */ createGetRequest),\n/* harmony export */   getCredentials: () => (/* binding */ getCredentials)\n/* harmony export */ });\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _smithy_util_stream__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/util-stream */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/index.js\");\n\n\n\n\nfunction createGetRequest(url) {\n    return new _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__.HttpRequest({\n        protocol: url.protocol,\n        hostname: url.hostname,\n        port: Number(url.port),\n        path: url.pathname,\n        query: Array.from(url.searchParams.entries()).reduce((acc, [k, v]) => {\n            acc[k] = v;\n            return acc;\n        }, {}),\n        fragment: url.hash,\n    });\n}\nasync function getCredentials(response, logger) {\n    const stream = (0,_smithy_util_stream__WEBPACK_IMPORTED_MODULE_3__.sdkStreamMixin)(response.body);\n    const str = await stream.transformToString();\n    if (response.statusCode === 200) {\n        const parsed = JSON.parse(str);\n        if (typeof parsed.AccessKeyId !== \"string\" ||\n            typeof parsed.SecretAccessKey !== \"string\" ||\n            typeof parsed.Token !== \"string\" ||\n            typeof parsed.Expiration !== \"string\") {\n            throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(\"HTTP credential provider response not of the required format, an object matching: \" +\n                \"{ AccessKeyId: string, SecretAccessKey: string, Token: string, Expiration: string(rfc3339) }\", { logger });\n        }\n        return {\n            accessKeyId: parsed.AccessKeyId,\n            secretAccessKey: parsed.SecretAccessKey,\n            sessionToken: parsed.Token,\n            expiration: (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.parseRfc3339DateTime)(parsed.Expiration),\n        };\n    }\n    if (response.statusCode >= 400 && response.statusCode < 500) {\n        let parsedBody = {};\n        try {\n            parsedBody = JSON.parse(str);\n        }\n        catch (e) { }\n        throw Object.assign(new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`Server responded with status: ${response.statusCode}`, { logger }), {\n            Code: parsedBody.Code,\n            Message: parsedBody.Message,\n        });\n    }\n    throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`Server responded with status: ${response.statusCode}`, { logger });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1odHRwQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItaHR0cC9kaXN0LWVzL2Zyb21IdHRwL3JlcXVlc3RIZWxwZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFxRTtBQUNqQjtBQUNTO0FBQ1I7QUFDOUM7QUFDUCxlQUFlLDhEQUFXO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxJQUFJO0FBQ2I7QUFDQSxLQUFLO0FBQ0w7QUFDTztBQUNQLG1CQUFtQixtRUFBYztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwrRUFBd0I7QUFDOUMsbUJBQW1CLDBGQUEwRixLQUFLLFFBQVE7QUFDMUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QiwyRUFBb0I7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQywrRUFBd0Isa0NBQWtDLG9CQUFvQixLQUFLLFFBQVE7QUFDM0g7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLGNBQWMsK0VBQXdCLGtDQUFrQyxvQkFBb0IsS0FBSyxRQUFRO0FBQ3pHIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1odHRwQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItaHR0cC9kaXN0LWVzL2Zyb21IdHRwL3JlcXVlc3RIZWxwZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENyZWRlbnRpYWxzUHJvdmlkZXJFcnJvciB9IGZyb20gXCJAc21pdGh5L3Byb3BlcnR5LXByb3ZpZGVyXCI7XG5pbXBvcnQgeyBIdHRwUmVxdWVzdCB9IGZyb20gXCJAc21pdGh5L3Byb3RvY29sLWh0dHBcIjtcbmltcG9ydCB7IHBhcnNlUmZjMzMzOURhdGVUaW1lIH0gZnJvbSBcIkBzbWl0aHkvc21pdGh5LWNsaWVudFwiO1xuaW1wb3J0IHsgc2RrU3RyZWFtTWl4aW4gfSBmcm9tIFwiQHNtaXRoeS91dGlsLXN0cmVhbVwiO1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUdldFJlcXVlc3QodXJsKSB7XG4gICAgcmV0dXJuIG5ldyBIdHRwUmVxdWVzdCh7XG4gICAgICAgIHByb3RvY29sOiB1cmwucHJvdG9jb2wsXG4gICAgICAgIGhvc3RuYW1lOiB1cmwuaG9zdG5hbWUsXG4gICAgICAgIHBvcnQ6IE51bWJlcih1cmwucG9ydCksXG4gICAgICAgIHBhdGg6IHVybC5wYXRobmFtZSxcbiAgICAgICAgcXVlcnk6IEFycmF5LmZyb20odXJsLnNlYXJjaFBhcmFtcy5lbnRyaWVzKCkpLnJlZHVjZSgoYWNjLCBbaywgdl0pID0+IHtcbiAgICAgICAgICAgIGFjY1trXSA9IHY7XG4gICAgICAgICAgICByZXR1cm4gYWNjO1xuICAgICAgICB9LCB7fSksXG4gICAgICAgIGZyYWdtZW50OiB1cmwuaGFzaCxcbiAgICB9KTtcbn1cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRDcmVkZW50aWFscyhyZXNwb25zZSwgbG9nZ2VyKSB7XG4gICAgY29uc3Qgc3RyZWFtID0gc2RrU3RyZWFtTWl4aW4ocmVzcG9uc2UuYm9keSk7XG4gICAgY29uc3Qgc3RyID0gYXdhaXQgc3RyZWFtLnRyYW5zZm9ybVRvU3RyaW5nKCk7XG4gICAgaWYgKHJlc3BvbnNlLnN0YXR1c0NvZGUgPT09IDIwMCkge1xuICAgICAgICBjb25zdCBwYXJzZWQgPSBKU09OLnBhcnNlKHN0cik7XG4gICAgICAgIGlmICh0eXBlb2YgcGFyc2VkLkFjY2Vzc0tleUlkICE9PSBcInN0cmluZ1wiIHx8XG4gICAgICAgICAgICB0eXBlb2YgcGFyc2VkLlNlY3JldEFjY2Vzc0tleSAhPT0gXCJzdHJpbmdcIiB8fFxuICAgICAgICAgICAgdHlwZW9mIHBhcnNlZC5Ub2tlbiAhPT0gXCJzdHJpbmdcIiB8fFxuICAgICAgICAgICAgdHlwZW9mIHBhcnNlZC5FeHBpcmF0aW9uICE9PSBcInN0cmluZ1wiKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgQ3JlZGVudGlhbHNQcm92aWRlckVycm9yKFwiSFRUUCBjcmVkZW50aWFsIHByb3ZpZGVyIHJlc3BvbnNlIG5vdCBvZiB0aGUgcmVxdWlyZWQgZm9ybWF0LCBhbiBvYmplY3QgbWF0Y2hpbmc6IFwiICtcbiAgICAgICAgICAgICAgICBcInsgQWNjZXNzS2V5SWQ6IHN0cmluZywgU2VjcmV0QWNjZXNzS2V5OiBzdHJpbmcsIFRva2VuOiBzdHJpbmcsIEV4cGlyYXRpb246IHN0cmluZyhyZmMzMzM5KSB9XCIsIHsgbG9nZ2VyIH0pO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBhY2Nlc3NLZXlJZDogcGFyc2VkLkFjY2Vzc0tleUlkLFxuICAgICAgICAgICAgc2VjcmV0QWNjZXNzS2V5OiBwYXJzZWQuU2VjcmV0QWNjZXNzS2V5LFxuICAgICAgICAgICAgc2Vzc2lvblRva2VuOiBwYXJzZWQuVG9rZW4sXG4gICAgICAgICAgICBleHBpcmF0aW9uOiBwYXJzZVJmYzMzMzlEYXRlVGltZShwYXJzZWQuRXhwaXJhdGlvbiksXG4gICAgICAgIH07XG4gICAgfVxuICAgIGlmIChyZXNwb25zZS5zdGF0dXNDb2RlID49IDQwMCAmJiByZXNwb25zZS5zdGF0dXNDb2RlIDwgNTAwKSB7XG4gICAgICAgIGxldCBwYXJzZWRCb2R5ID0ge307XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBwYXJzZWRCb2R5ID0gSlNPTi5wYXJzZShzdHIpO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlKSB7IH1cbiAgICAgICAgdGhyb3cgT2JqZWN0LmFzc2lnbihuZXcgQ3JlZGVudGlhbHNQcm92aWRlckVycm9yKGBTZXJ2ZXIgcmVzcG9uZGVkIHdpdGggc3RhdHVzOiAke3Jlc3BvbnNlLnN0YXR1c0NvZGV9YCwgeyBsb2dnZXIgfSksIHtcbiAgICAgICAgICAgIENvZGU6IHBhcnNlZEJvZHkuQ29kZSxcbiAgICAgICAgICAgIE1lc3NhZ2U6IHBhcnNlZEJvZHkuTWVzc2FnZSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHRocm93IG5ldyBDcmVkZW50aWFsc1Byb3ZpZGVyRXJyb3IoYFNlcnZlciByZXNwb25kZWQgd2l0aCBzdGF0dXM6ICR7cmVzcG9uc2Uuc3RhdHVzQ29kZX1gLCB7IGxvZ2dlciB9KTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/requestHelpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/retry-wrapper.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/retry-wrapper.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   retryWrapper: () => (/* binding */ retryWrapper)\n/* harmony export */ });\nconst retryWrapper = (toRetry, maxRetries, delayMs) => {\n    return async () => {\n        for (let i = 0; i < maxRetries; ++i) {\n            try {\n                return await toRetry();\n            }\n            catch (e) {\n                await new Promise((resolve) => setTimeout(resolve, delayMs));\n            }\n        }\n        return await toRetry();\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1odHRwQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItaHR0cC9kaXN0LWVzL2Zyb21IdHRwL3JldHJ5LXdyYXBwZXIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQSx3QkFBd0IsZ0JBQWdCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1odHRwQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItaHR0cC9kaXN0LWVzL2Zyb21IdHRwL3JldHJ5LXdyYXBwZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHJldHJ5V3JhcHBlciA9ICh0b1JldHJ5LCBtYXhSZXRyaWVzLCBkZWxheU1zKSA9PiB7XG4gICAgcmV0dXJuIGFzeW5jICgpID0+IHtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBtYXhSZXRyaWVzOyArK2kpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGF3YWl0IHRvUmV0cnkoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgYXdhaXQgbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgZGVsYXlNcykpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBhd2FpdCB0b1JldHJ5KCk7XG4gICAgfTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/retry-wrapper.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/index.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/index.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromHttp: () => (/* reexport safe */ _fromHttp_fromHttp__WEBPACK_IMPORTED_MODULE_0__.fromHttp)\n/* harmony export */ });\n/* harmony import */ var _fromHttp_fromHttp__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fromHttp/fromHttp */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/fromHttp/fromHttp.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1odHRwQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItaHR0cC9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1odHRwQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItaHR0cC9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGZyb21IdHRwIH0gZnJvbSBcIi4vZnJvbUh0dHAvZnJvbUh0dHBcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/index.js\n");

/***/ })

};
;