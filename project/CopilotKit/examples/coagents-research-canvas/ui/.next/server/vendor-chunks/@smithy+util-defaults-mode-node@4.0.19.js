"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+util-defaults-mode-node@4.0.19";
exports.ids = ["vendor-chunks/@smithy+util-defaults-mode-node@4.0.19"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/constants.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/constants.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AWS_DEFAULT_REGION_ENV: () => (/* binding */ AWS_DEFAULT_REGION_ENV),\n/* harmony export */   AWS_EXECUTION_ENV: () => (/* binding */ AWS_EXECUTION_ENV),\n/* harmony export */   AWS_REGION_ENV: () => (/* binding */ AWS_REGION_ENV),\n/* harmony export */   DEFAULTS_MODE_OPTIONS: () => (/* binding */ DEFAULTS_MODE_OPTIONS),\n/* harmony export */   ENV_IMDS_DISABLED: () => (/* binding */ ENV_IMDS_DISABLED),\n/* harmony export */   IMDS_REGION_PATH: () => (/* binding */ IMDS_REGION_PATH)\n/* harmony export */ });\nconst AWS_EXECUTION_ENV = \"AWS_EXECUTION_ENV\";\nconst AWS_REGION_ENV = \"AWS_REGION\";\nconst AWS_DEFAULT_REGION_ENV = \"AWS_DEFAULT_REGION\";\nconst ENV_IMDS_DISABLED = \"AWS_EC2_METADATA_DISABLED\";\nconst DEFAULTS_MODE_OPTIONS = [\"in-region\", \"cross-region\", \"mobile\", \"standard\", \"legacy\"];\nconst IMDS_REGION_PATH = \"/latest/meta-data/placement/region\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWRlZmF1bHRzLW1vZGUtbm9kZUA0LjAuMTkvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1kZWZhdWx0cy1tb2RlLW5vZGUvZGlzdC1lcy9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWRlZmF1bHRzLW1vZGUtbm9kZUA0LjAuMTkvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1kZWZhdWx0cy1tb2RlLW5vZGUvZGlzdC1lcy9jb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IEFXU19FWEVDVVRJT05fRU5WID0gXCJBV1NfRVhFQ1VUSU9OX0VOVlwiO1xuZXhwb3J0IGNvbnN0IEFXU19SRUdJT05fRU5WID0gXCJBV1NfUkVHSU9OXCI7XG5leHBvcnQgY29uc3QgQVdTX0RFRkFVTFRfUkVHSU9OX0VOViA9IFwiQVdTX0RFRkFVTFRfUkVHSU9OXCI7XG5leHBvcnQgY29uc3QgRU5WX0lNRFNfRElTQUJMRUQgPSBcIkFXU19FQzJfTUVUQURBVEFfRElTQUJMRURcIjtcbmV4cG9ydCBjb25zdCBERUZBVUxUU19NT0RFX09QVElPTlMgPSBbXCJpbi1yZWdpb25cIiwgXCJjcm9zcy1yZWdpb25cIiwgXCJtb2JpbGVcIiwgXCJzdGFuZGFyZFwiLCBcImxlZ2FjeVwiXTtcbmV4cG9ydCBjb25zdCBJTURTX1JFR0lPTl9QQVRIID0gXCIvbGF0ZXN0L21ldGEtZGF0YS9wbGFjZW1lbnQvcmVnaW9uXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/defaultsModeConfig.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/defaultsModeConfig.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NODE_DEFAULTS_MODE_CONFIG_OPTIONS: () => (/* binding */ NODE_DEFAULTS_MODE_CONFIG_OPTIONS)\n/* harmony export */ });\nconst AWS_DEFAULTS_MODE_ENV = \"AWS_DEFAULTS_MODE\";\nconst AWS_DEFAULTS_MODE_CONFIG = \"defaults_mode\";\nconst NODE_DEFAULTS_MODE_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => {\n        return env[AWS_DEFAULTS_MODE_ENV];\n    },\n    configFileSelector: (profile) => {\n        return profile[AWS_DEFAULTS_MODE_CONFIG];\n    },\n    default: \"legacy\",\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWRlZmF1bHRzLW1vZGUtbm9kZUA0LjAuMTkvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1kZWZhdWx0cy1tb2RlLW5vZGUvZGlzdC1lcy9kZWZhdWx0c01vZGVDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWRlZmF1bHRzLW1vZGUtbm9kZUA0LjAuMTkvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1kZWZhdWx0cy1tb2RlLW5vZGUvZGlzdC1lcy9kZWZhdWx0c01vZGVDb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgQVdTX0RFRkFVTFRTX01PREVfRU5WID0gXCJBV1NfREVGQVVMVFNfTU9ERVwiO1xuY29uc3QgQVdTX0RFRkFVTFRTX01PREVfQ09ORklHID0gXCJkZWZhdWx0c19tb2RlXCI7XG5leHBvcnQgY29uc3QgTk9ERV9ERUZBVUxUU19NT0RFX0NPTkZJR19PUFRJT05TID0ge1xuICAgIGVudmlyb25tZW50VmFyaWFibGVTZWxlY3RvcjogKGVudikgPT4ge1xuICAgICAgICByZXR1cm4gZW52W0FXU19ERUZBVUxUU19NT0RFX0VOVl07XG4gICAgfSxcbiAgICBjb25maWdGaWxlU2VsZWN0b3I6IChwcm9maWxlKSA9PiB7XG4gICAgICAgIHJldHVybiBwcm9maWxlW0FXU19ERUZBVUxUU19NT0RFX0NPTkZJR107XG4gICAgfSxcbiAgICBkZWZhdWx0OiBcImxlZ2FjeVwiLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/defaultsModeConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/index.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/index.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveDefaultsModeConfig: () => (/* reexport safe */ _resolveDefaultsModeConfig__WEBPACK_IMPORTED_MODULE_0__.resolveDefaultsModeConfig)\n/* harmony export */ });\n/* harmony import */ var _resolveDefaultsModeConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./resolveDefaultsModeConfig */ \"(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/resolveDefaultsModeConfig.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWRlZmF1bHRzLW1vZGUtbm9kZUA0LjAuMTkvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1kZWZhdWx0cy1tb2RlLW5vZGUvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1kZWZhdWx0cy1tb2RlLW5vZGVANC4wLjE5L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZGVmYXVsdHMtbW9kZS1ub2RlL2Rpc3QtZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vcmVzb2x2ZURlZmF1bHRzTW9kZUNvbmZpZ1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/resolveDefaultsModeConfig.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/resolveDefaultsModeConfig.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveDefaultsModeConfig: () => (/* binding */ resolveDefaultsModeConfig)\n/* harmony export */ });\n/* harmony import */ var _smithy_config_resolver__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/config-resolver */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/node-config-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+node-config-provider@4.1.3/node_modules/@smithy/node-config-provider/dist-es/index.js\");\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/constants.js\");\n/* harmony import */ var _defaultsModeConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./defaultsModeConfig */ \"(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/defaultsModeConfig.js\");\n\n\n\n\n\nconst resolveDefaultsModeConfig = ({ region = (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_1__.loadConfig)(_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_0__.NODE_REGION_CONFIG_OPTIONS), defaultsMode = (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_1__.loadConfig)(_defaultsModeConfig__WEBPACK_IMPORTED_MODULE_4__.NODE_DEFAULTS_MODE_CONFIG_OPTIONS), } = {}) => (0,_smithy_property_provider__WEBPACK_IMPORTED_MODULE_2__.memoize)(async () => {\n    const mode = typeof defaultsMode === \"function\" ? await defaultsMode() : defaultsMode;\n    switch (mode?.toLowerCase()) {\n        case \"auto\":\n            return resolveNodeDefaultsModeAuto(region);\n        case \"in-region\":\n        case \"cross-region\":\n        case \"mobile\":\n        case \"standard\":\n        case \"legacy\":\n            return Promise.resolve(mode?.toLocaleLowerCase());\n        case undefined:\n            return Promise.resolve(\"legacy\");\n        default:\n            throw new Error(`Invalid parameter for \"defaultsMode\", expect ${_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULTS_MODE_OPTIONS.join(\", \")}, got ${mode}`);\n    }\n});\nconst resolveNodeDefaultsModeAuto = async (clientRegion) => {\n    if (clientRegion) {\n        const resolvedRegion = typeof clientRegion === \"function\" ? await clientRegion() : clientRegion;\n        const inferredRegion = await inferPhysicalRegion();\n        if (!inferredRegion) {\n            return \"standard\";\n        }\n        if (resolvedRegion === inferredRegion) {\n            return \"in-region\";\n        }\n        else {\n            return \"cross-region\";\n        }\n    }\n    return \"standard\";\n};\nconst inferPhysicalRegion = async () => {\n    if (process.env[_constants__WEBPACK_IMPORTED_MODULE_3__.AWS_EXECUTION_ENV] && (process.env[_constants__WEBPACK_IMPORTED_MODULE_3__.AWS_REGION_ENV] || process.env[_constants__WEBPACK_IMPORTED_MODULE_3__.AWS_DEFAULT_REGION_ENV])) {\n        return process.env[_constants__WEBPACK_IMPORTED_MODULE_3__.AWS_REGION_ENV] ?? process.env[_constants__WEBPACK_IMPORTED_MODULE_3__.AWS_DEFAULT_REGION_ENV];\n    }\n    if (!process.env[_constants__WEBPACK_IMPORTED_MODULE_3__.ENV_IMDS_DISABLED]) {\n        try {\n            const { getInstanceMetadataEndpoint, httpRequest } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@smithy+credential-provider-imds@4.0.6\").then(__webpack_require__.bind(__webpack_require__, /*! @smithy/credential-provider-imds */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/index.js\"));\n            const endpoint = await getInstanceMetadataEndpoint();\n            return (await httpRequest({ ...endpoint, path: _constants__WEBPACK_IMPORTED_MODULE_3__.IMDS_REGION_PATH })).toString();\n        }\n        catch (e) {\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/resolveDefaultsModeConfig.js\n");

/***/ })

};
;