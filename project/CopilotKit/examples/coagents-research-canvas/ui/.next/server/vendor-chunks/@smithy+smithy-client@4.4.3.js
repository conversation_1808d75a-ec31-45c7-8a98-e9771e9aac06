"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+smithy-client@4.4.3";
exports.ids = ["vendor-chunks/@smithy+smithy-client@4.4.3"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/NoOpLogger.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/NoOpLogger.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoOpLogger: () => (/* binding */ NoOpLogger)\n/* harmony export */ });\nclass NoOpLogger {\n    trace() { }\n    debug() { }\n    info() { }\n    warn() { }\n    error() { }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9Ob09wTG9nZ2VyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrc21pdGh5LWNsaWVudEA0LjQuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9zbWl0aHktY2xpZW50L2Rpc3QtZXMvTm9PcExvZ2dlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgTm9PcExvZ2dlciB7XG4gICAgdHJhY2UoKSB7IH1cbiAgICBkZWJ1ZygpIHsgfVxuICAgIGluZm8oKSB7IH1cbiAgICB3YXJuKCkgeyB9XG4gICAgZXJyb3IoKSB7IH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/NoOpLogger.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/client.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/client.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* binding */ Client)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_stack__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-stack */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-stack@4.0.4/node_modules/@smithy/middleware-stack/dist-es/index.js\");\n\nclass Client {\n    constructor(config) {\n        this.config = config;\n        this.middlewareStack = (0,_smithy_middleware_stack__WEBPACK_IMPORTED_MODULE_0__.constructStack)();\n    }\n    send(command, optionsOrCb, cb) {\n        const options = typeof optionsOrCb !== \"function\" ? optionsOrCb : undefined;\n        const callback = typeof optionsOrCb === \"function\" ? optionsOrCb : cb;\n        const useHandlerCache = options === undefined && this.config.cacheMiddleware === true;\n        let handler;\n        if (useHandlerCache) {\n            if (!this.handlers) {\n                this.handlers = new WeakMap();\n            }\n            const handlers = this.handlers;\n            if (handlers.has(command.constructor)) {\n                handler = handlers.get(command.constructor);\n            }\n            else {\n                handler = command.resolveMiddleware(this.middlewareStack, this.config, options);\n                handlers.set(command.constructor, handler);\n            }\n        }\n        else {\n            delete this.handlers;\n            handler = command.resolveMiddleware(this.middlewareStack, this.config, options);\n        }\n        if (callback) {\n            handler(command)\n                .then((result) => callback(null, result.output), (err) => callback(err))\n                .catch(() => { });\n        }\n        else {\n            return handler(command).then((result) => result.output);\n        }\n    }\n    destroy() {\n        this.config?.requestHandler?.destroy?.();\n        delete this.handlers;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/collect-stream-body.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/collect-stream-body.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   collectBody: () => (/* reexport safe */ _smithy_core_protocols__WEBPACK_IMPORTED_MODULE_0__.collectBody)\n/* harmony export */ });\n/* harmony import */ var _smithy_core_protocols__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core/protocols */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/index.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9jb2xsZWN0LXN0cmVhbS1ib2R5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFEIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9jb2xsZWN0LXN0cmVhbS1ib2R5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGNvbGxlY3RCb2R5IH0gZnJvbSBcIkBzbWl0aHkvY29yZS9wcm90b2NvbHNcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/collect-stream-body.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/command.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/command.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Command: () => (/* binding */ Command)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_stack__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-stack */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-stack@4.0.4/node_modules/@smithy/middleware-stack/dist-es/index.js\");\n/* harmony import */ var _smithy_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/types */ \"(rsc)/./node_modules/.pnpm/@smithy+types@4.3.1/node_modules/@smithy/types/dist-es/index.js\");\n\n\nclass Command {\n    constructor() {\n        this.middlewareStack = (0,_smithy_middleware_stack__WEBPACK_IMPORTED_MODULE_0__.constructStack)();\n    }\n    static classBuilder() {\n        return new ClassBuilder();\n    }\n    resolveMiddlewareWithContext(clientStack, configuration, options, { middlewareFn, clientName, commandName, inputFilterSensitiveLog, outputFilterSensitiveLog, smithyContext, additionalContext, CommandCtor, }) {\n        for (const mw of middlewareFn.bind(this)(CommandCtor, clientStack, configuration, options)) {\n            this.middlewareStack.use(mw);\n        }\n        const stack = clientStack.concat(this.middlewareStack);\n        const { logger } = configuration;\n        const handlerExecutionContext = {\n            logger,\n            clientName,\n            commandName,\n            inputFilterSensitiveLog,\n            outputFilterSensitiveLog,\n            [_smithy_types__WEBPACK_IMPORTED_MODULE_1__.SMITHY_CONTEXT_KEY]: {\n                commandInstance: this,\n                ...smithyContext,\n            },\n            ...additionalContext,\n        };\n        const { requestHandler } = configuration;\n        return stack.resolve((request) => requestHandler.handle(request.request, options || {}), handlerExecutionContext);\n    }\n}\nclass ClassBuilder {\n    constructor() {\n        this._init = () => { };\n        this._ep = {};\n        this._middlewareFn = () => [];\n        this._commandName = \"\";\n        this._clientName = \"\";\n        this._additionalContext = {};\n        this._smithyContext = {};\n        this._inputFilterSensitiveLog = (_) => _;\n        this._outputFilterSensitiveLog = (_) => _;\n        this._serializer = null;\n        this._deserializer = null;\n    }\n    init(cb) {\n        this._init = cb;\n    }\n    ep(endpointParameterInstructions) {\n        this._ep = endpointParameterInstructions;\n        return this;\n    }\n    m(middlewareSupplier) {\n        this._middlewareFn = middlewareSupplier;\n        return this;\n    }\n    s(service, operation, smithyContext = {}) {\n        this._smithyContext = {\n            service,\n            operation,\n            ...smithyContext,\n        };\n        return this;\n    }\n    c(additionalContext = {}) {\n        this._additionalContext = additionalContext;\n        return this;\n    }\n    n(clientName, commandName) {\n        this._clientName = clientName;\n        this._commandName = commandName;\n        return this;\n    }\n    f(inputFilter = (_) => _, outputFilter = (_) => _) {\n        this._inputFilterSensitiveLog = inputFilter;\n        this._outputFilterSensitiveLog = outputFilter;\n        return this;\n    }\n    ser(serializer) {\n        this._serializer = serializer;\n        return this;\n    }\n    de(deserializer) {\n        this._deserializer = deserializer;\n        return this;\n    }\n    sc(operation) {\n        this._operationSchema = operation;\n        this._smithyContext.operationSchema = operation;\n        return this;\n    }\n    build() {\n        const closure = this;\n        let CommandRef;\n        return (CommandRef = class extends Command {\n            static getEndpointParameterInstructions() {\n                return closure._ep;\n            }\n            constructor(...[input]) {\n                super();\n                this.serialize = closure._serializer;\n                this.deserialize = closure._deserializer;\n                this.input = input ?? {};\n                closure._init(this);\n                this.schema = closure._operationSchema;\n            }\n            resolveMiddleware(stack, configuration, options) {\n                return this.resolveMiddlewareWithContext(stack, configuration, options, {\n                    CommandCtor: CommandRef,\n                    middlewareFn: closure._middlewareFn,\n                    clientName: closure._clientName,\n                    commandName: closure._commandName,\n                    inputFilterSensitiveLog: closure._inputFilterSensitiveLog,\n                    outputFilterSensitiveLog: closure._outputFilterSensitiveLog,\n                    smithyContext: closure._smithyContext,\n                    additionalContext: closure._additionalContext,\n                });\n            }\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/command.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/constants.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/constants.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SENSITIVE_STRING: () => (/* binding */ SENSITIVE_STRING)\n/* harmony export */ });\nconst SENSITIVE_STRING = \"***SensitiveInformation***\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9jb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFNFTlNJVElWRV9TVFJJTkcgPSBcIioqKlNlbnNpdGl2ZUluZm9ybWF0aW9uKioqXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/create-aggregated-client.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/create-aggregated-client.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAggregatedClient: () => (/* binding */ createAggregatedClient)\n/* harmony export */ });\nconst createAggregatedClient = (commands, Client) => {\n    for (const command of Object.keys(commands)) {\n        const CommandCtor = commands[command];\n        const methodImpl = async function (args, optionsOrCb, cb) {\n            const command = new CommandCtor(args);\n            if (typeof optionsOrCb === \"function\") {\n                this.send(command, optionsOrCb);\n            }\n            else if (typeof cb === \"function\") {\n                if (typeof optionsOrCb !== \"object\")\n                    throw new Error(`Expected http options but got ${typeof optionsOrCb}`);\n                this.send(command, optionsOrCb || {}, cb);\n            }\n            else {\n                return this.send(command, optionsOrCb);\n            }\n        };\n        const methodName = (command[0].toLowerCase() + command.slice(1)).replace(/Command$/, \"\");\n        Client.prototype[methodName] = methodImpl;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/create-aggregated-client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/default-error-handler.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/default-error-handler.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   throwDefaultError: () => (/* binding */ throwDefaultError),\n/* harmony export */   withBaseException: () => (/* binding */ withBaseException)\n/* harmony export */ });\n/* harmony import */ var _exceptions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exceptions */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/exceptions.js\");\n\nconst throwDefaultError = ({ output, parsedBody, exceptionCtor, errorCode }) => {\n    const $metadata = deserializeMetadata(output);\n    const statusCode = $metadata.httpStatusCode ? $metadata.httpStatusCode + \"\" : undefined;\n    const response = new exceptionCtor({\n        name: parsedBody?.code || parsedBody?.Code || errorCode || statusCode || \"UnknownError\",\n        $fault: \"client\",\n        $metadata,\n    });\n    throw (0,_exceptions__WEBPACK_IMPORTED_MODULE_0__.decorateServiceException)(response, parsedBody);\n};\nconst withBaseException = (ExceptionCtor) => {\n    return ({ output, parsedBody, errorCode }) => {\n        throwDefaultError({ output, parsedBody, exceptionCtor: ExceptionCtor, errorCode });\n    };\n};\nconst deserializeMetadata = (output) => ({\n    httpStatusCode: output.statusCode,\n    requestId: output.headers[\"x-amzn-requestid\"] ?? output.headers[\"x-amzn-request-id\"] ?? output.headers[\"x-amz-request-id\"],\n    extendedRequestId: output.headers[\"x-amz-id-2\"],\n    cfId: output.headers[\"x-amz-cf-id\"],\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/default-error-handler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/defaults-mode.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/defaults-mode.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadConfigsForDefaultMode: () => (/* binding */ loadConfigsForDefaultMode)\n/* harmony export */ });\nconst loadConfigsForDefaultMode = (mode) => {\n    switch (mode) {\n        case \"standard\":\n            return {\n                retryMode: \"standard\",\n                connectionTimeout: 3100,\n            };\n        case \"in-region\":\n            return {\n                retryMode: \"standard\",\n                connectionTimeout: 1100,\n            };\n        case \"cross-region\":\n            return {\n                retryMode: \"standard\",\n                connectionTimeout: 3100,\n            };\n        case \"mobile\":\n            return {\n                retryMode: \"standard\",\n                connectionTimeout: 30000,\n            };\n        default:\n            return {};\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9kZWZhdWx0cy1tb2RlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9kZWZhdWx0cy1tb2RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBsb2FkQ29uZmlnc0ZvckRlZmF1bHRNb2RlID0gKG1vZGUpID0+IHtcbiAgICBzd2l0Y2ggKG1vZGUpIHtcbiAgICAgICAgY2FzZSBcInN0YW5kYXJkXCI6XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHJldHJ5TW9kZTogXCJzdGFuZGFyZFwiLFxuICAgICAgICAgICAgICAgIGNvbm5lY3Rpb25UaW1lb3V0OiAzMTAwLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgY2FzZSBcImluLXJlZ2lvblwiOlxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICByZXRyeU1vZGU6IFwic3RhbmRhcmRcIixcbiAgICAgICAgICAgICAgICBjb25uZWN0aW9uVGltZW91dDogMTEwMCxcbiAgICAgICAgICAgIH07XG4gICAgICAgIGNhc2UgXCJjcm9zcy1yZWdpb25cIjpcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgcmV0cnlNb2RlOiBcInN0YW5kYXJkXCIsXG4gICAgICAgICAgICAgICAgY29ubmVjdGlvblRpbWVvdXQ6IDMxMDAsXG4gICAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwibW9iaWxlXCI6XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHJldHJ5TW9kZTogXCJzdGFuZGFyZFwiLFxuICAgICAgICAgICAgICAgIGNvbm5lY3Rpb25UaW1lb3V0OiAzMDAwMCxcbiAgICAgICAgICAgIH07XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICByZXR1cm4ge307XG4gICAgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/defaults-mode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/emitWarningIfUnsupportedVersion.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/emitWarningIfUnsupportedVersion.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   emitWarningIfUnsupportedVersion: () => (/* binding */ emitWarningIfUnsupportedVersion)\n/* harmony export */ });\nlet warningEmitted = false;\nconst emitWarningIfUnsupportedVersion = (version) => {\n    if (version && !warningEmitted && parseInt(version.substring(1, version.indexOf(\".\"))) < 16) {\n        warningEmitted = true;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9lbWl0V2FybmluZ0lmVW5zdXBwb3J0ZWRWZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3NtaXRoeS1jbGllbnRANC40LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvc21pdGh5LWNsaWVudC9kaXN0LWVzL2VtaXRXYXJuaW5nSWZVbnN1cHBvcnRlZFZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IHdhcm5pbmdFbWl0dGVkID0gZmFsc2U7XG5leHBvcnQgY29uc3QgZW1pdFdhcm5pbmdJZlVuc3VwcG9ydGVkVmVyc2lvbiA9ICh2ZXJzaW9uKSA9PiB7XG4gICAgaWYgKHZlcnNpb24gJiYgIXdhcm5pbmdFbWl0dGVkICYmIHBhcnNlSW50KHZlcnNpb24uc3Vic3RyaW5nKDEsIHZlcnNpb24uaW5kZXhPZihcIi5cIikpKSA8IDE2KSB7XG4gICAgICAgIHdhcm5pbmdFbWl0dGVkID0gdHJ1ZTtcbiAgICB9XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/emitWarningIfUnsupportedVersion.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/exceptions.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/exceptions.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServiceException: () => (/* binding */ ServiceException),\n/* harmony export */   decorateServiceException: () => (/* binding */ decorateServiceException)\n/* harmony export */ });\nclass ServiceException extends Error {\n    constructor(options) {\n        super(options.message);\n        Object.setPrototypeOf(this, Object.getPrototypeOf(this).constructor.prototype);\n        this.name = options.name;\n        this.$fault = options.$fault;\n        this.$metadata = options.$metadata;\n    }\n    static isInstance(value) {\n        if (!value)\n            return false;\n        const candidate = value;\n        return (ServiceException.prototype.isPrototypeOf(candidate) ||\n            (Boolean(candidate.$fault) &&\n                Boolean(candidate.$metadata) &&\n                (candidate.$fault === \"client\" || candidate.$fault === \"server\")));\n    }\n    static [Symbol.hasInstance](instance) {\n        if (!instance)\n            return false;\n        const candidate = instance;\n        if (this === ServiceException) {\n            return ServiceException.isInstance(instance);\n        }\n        if (ServiceException.isInstance(instance)) {\n            if (candidate.name && this.name) {\n                return this.prototype.isPrototypeOf(instance) || candidate.name === this.name;\n            }\n            return this.prototype.isPrototypeOf(instance);\n        }\n        return false;\n    }\n}\nconst decorateServiceException = (exception, additions = {}) => {\n    Object.entries(additions)\n        .filter(([, v]) => v !== undefined)\n        .forEach(([k, v]) => {\n        if (exception[k] == undefined || exception[k] === \"\") {\n            exception[k] = v;\n        }\n    });\n    const message = exception.message || exception.Message || \"UnknownError\";\n    exception.message = message;\n    delete exception.Message;\n    return exception;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/exceptions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extended-encode-uri-component.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extended-encode-uri-component.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extendedEncodeURIComponent: () => (/* reexport safe */ _smithy_core_protocols__WEBPACK_IMPORTED_MODULE_0__.extendedEncodeURIComponent)\n/* harmony export */ });\n/* harmony import */ var _smithy_core_protocols__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core/protocols */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/index.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9leHRlbmRlZC1lbmNvZGUtdXJpLWNvbXBvbmVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvRSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrc21pdGh5LWNsaWVudEA0LjQuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9zbWl0aHktY2xpZW50L2Rpc3QtZXMvZXh0ZW5kZWQtZW5jb2RlLXVyaS1jb21wb25lbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZXh0ZW5kZWRFbmNvZGVVUklDb21wb25lbnQgfSBmcm9tIFwiQHNtaXRoeS9jb3JlL3Byb3RvY29sc1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extended-encode-uri-component.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/checksum.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/checksum.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlgorithmId: () => (/* reexport safe */ _smithy_types__WEBPACK_IMPORTED_MODULE_0__.AlgorithmId),\n/* harmony export */   getChecksumConfiguration: () => (/* binding */ getChecksumConfiguration),\n/* harmony export */   resolveChecksumRuntimeConfig: () => (/* binding */ resolveChecksumRuntimeConfig)\n/* harmony export */ });\n/* harmony import */ var _smithy_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/types */ \"(rsc)/./node_modules/.pnpm/@smithy+types@4.3.1/node_modules/@smithy/types/dist-es/index.js\");\n\n\nconst getChecksumConfiguration = (runtimeConfig) => {\n    const checksumAlgorithms = [];\n    for (const id in _smithy_types__WEBPACK_IMPORTED_MODULE_0__.AlgorithmId) {\n        const algorithmId = _smithy_types__WEBPACK_IMPORTED_MODULE_0__.AlgorithmId[id];\n        if (runtimeConfig[algorithmId] === undefined) {\n            continue;\n        }\n        checksumAlgorithms.push({\n            algorithmId: () => algorithmId,\n            checksumConstructor: () => runtimeConfig[algorithmId],\n        });\n    }\n    return {\n        addChecksumAlgorithm(algo) {\n            checksumAlgorithms.push(algo);\n        },\n        checksumAlgorithms() {\n            return checksumAlgorithms;\n        },\n    };\n};\nconst resolveChecksumRuntimeConfig = (clientConfig) => {\n    const runtimeConfig = {};\n    clientConfig.checksumAlgorithms().forEach((checksumAlgorithm) => {\n        runtimeConfig[checksumAlgorithm.algorithmId()] = checksumAlgorithm.checksumConstructor();\n    });\n    return runtimeConfig;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/checksum.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/defaultExtensionConfiguration.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/defaultExtensionConfiguration.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaultClientConfiguration: () => (/* binding */ getDefaultClientConfiguration),\n/* harmony export */   getDefaultExtensionConfiguration: () => (/* binding */ getDefaultExtensionConfiguration),\n/* harmony export */   resolveDefaultRuntimeConfig: () => (/* binding */ resolveDefaultRuntimeConfig)\n/* harmony export */ });\n/* harmony import */ var _checksum__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./checksum */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/checksum.js\");\n/* harmony import */ var _retry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retry */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/retry.js\");\n\n\nconst getDefaultExtensionConfiguration = (runtimeConfig) => {\n    return Object.assign((0,_checksum__WEBPACK_IMPORTED_MODULE_0__.getChecksumConfiguration)(runtimeConfig), (0,_retry__WEBPACK_IMPORTED_MODULE_1__.getRetryConfiguration)(runtimeConfig));\n};\nconst getDefaultClientConfiguration = getDefaultExtensionConfiguration;\nconst resolveDefaultRuntimeConfig = (config) => {\n    return Object.assign((0,_checksum__WEBPACK_IMPORTED_MODULE_0__.resolveChecksumRuntimeConfig)(config), (0,_retry__WEBPACK_IMPORTED_MODULE_1__.resolveRetryRuntimeConfig)(config));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9leHRlbnNpb25zL2RlZmF1bHRFeHRlbnNpb25Db25maWd1cmF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW9GO0FBQ1Q7QUFDcEU7QUFDUCx5QkFBeUIsbUVBQXdCLGlCQUFpQiw2REFBcUI7QUFDdkY7QUFDTztBQUNBO0FBQ1AseUJBQXlCLHVFQUE0QixVQUFVLGlFQUF5QjtBQUN4RiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrc21pdGh5LWNsaWVudEA0LjQuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9zbWl0aHktY2xpZW50L2Rpc3QtZXMvZXh0ZW5zaW9ucy9kZWZhdWx0RXh0ZW5zaW9uQ29uZmlndXJhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRDaGVja3N1bUNvbmZpZ3VyYXRpb24sIHJlc29sdmVDaGVja3N1bVJ1bnRpbWVDb25maWcgfSBmcm9tIFwiLi9jaGVja3N1bVwiO1xuaW1wb3J0IHsgZ2V0UmV0cnlDb25maWd1cmF0aW9uLCByZXNvbHZlUmV0cnlSdW50aW1lQ29uZmlnIH0gZnJvbSBcIi4vcmV0cnlcIjtcbmV4cG9ydCBjb25zdCBnZXREZWZhdWx0RXh0ZW5zaW9uQ29uZmlndXJhdGlvbiA9IChydW50aW1lQ29uZmlnKSA9PiB7XG4gICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oZ2V0Q2hlY2tzdW1Db25maWd1cmF0aW9uKHJ1bnRpbWVDb25maWcpLCBnZXRSZXRyeUNvbmZpZ3VyYXRpb24ocnVudGltZUNvbmZpZykpO1xufTtcbmV4cG9ydCBjb25zdCBnZXREZWZhdWx0Q2xpZW50Q29uZmlndXJhdGlvbiA9IGdldERlZmF1bHRFeHRlbnNpb25Db25maWd1cmF0aW9uO1xuZXhwb3J0IGNvbnN0IHJlc29sdmVEZWZhdWx0UnVudGltZUNvbmZpZyA9IChjb25maWcpID0+IHtcbiAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihyZXNvbHZlQ2hlY2tzdW1SdW50aW1lQ29uZmlnKGNvbmZpZyksIHJlc29sdmVSZXRyeVJ1bnRpbWVDb25maWcoY29uZmlnKSk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/defaultExtensionConfiguration.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/index.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/index.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaultClientConfiguration: () => (/* reexport safe */ _defaultExtensionConfiguration__WEBPACK_IMPORTED_MODULE_0__.getDefaultClientConfiguration),\n/* harmony export */   getDefaultExtensionConfiguration: () => (/* reexport safe */ _defaultExtensionConfiguration__WEBPACK_IMPORTED_MODULE_0__.getDefaultExtensionConfiguration),\n/* harmony export */   resolveDefaultRuntimeConfig: () => (/* reexport safe */ _defaultExtensionConfiguration__WEBPACK_IMPORTED_MODULE_0__.resolveDefaultRuntimeConfig)\n/* harmony export */ });\n/* harmony import */ var _defaultExtensionConfiguration__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultExtensionConfiguration */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/defaultExtensionConfiguration.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9leHRlbnNpb25zL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZ0QiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3NtaXRoeS1jbGllbnRANC40LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvc21pdGh5LWNsaWVudC9kaXN0LWVzL2V4dGVuc2lvbnMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vZGVmYXVsdEV4dGVuc2lvbkNvbmZpZ3VyYXRpb25cIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/retry.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/retry.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRetryConfiguration: () => (/* binding */ getRetryConfiguration),\n/* harmony export */   resolveRetryRuntimeConfig: () => (/* binding */ resolveRetryRuntimeConfig)\n/* harmony export */ });\nconst getRetryConfiguration = (runtimeConfig) => {\n    return {\n        setRetryStrategy(retryStrategy) {\n            runtimeConfig.retryStrategy = retryStrategy;\n        },\n        retryStrategy() {\n            return runtimeConfig.retryStrategy;\n        },\n    };\n};\nconst resolveRetryRuntimeConfig = (retryStrategyConfiguration) => {\n    const runtimeConfig = {};\n    runtimeConfig.retryStrategy = retryStrategyConfiguration.retryStrategy();\n    return runtimeConfig;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9leHRlbnNpb25zL3JldHJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrc21pdGh5LWNsaWVudEA0LjQuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9zbWl0aHktY2xpZW50L2Rpc3QtZXMvZXh0ZW5zaW9ucy9yZXRyeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgZ2V0UmV0cnlDb25maWd1cmF0aW9uID0gKHJ1bnRpbWVDb25maWcpID0+IHtcbiAgICByZXR1cm4ge1xuICAgICAgICBzZXRSZXRyeVN0cmF0ZWd5KHJldHJ5U3RyYXRlZ3kpIHtcbiAgICAgICAgICAgIHJ1bnRpbWVDb25maWcucmV0cnlTdHJhdGVneSA9IHJldHJ5U3RyYXRlZ3k7XG4gICAgICAgIH0sXG4gICAgICAgIHJldHJ5U3RyYXRlZ3koKSB7XG4gICAgICAgICAgICByZXR1cm4gcnVudGltZUNvbmZpZy5yZXRyeVN0cmF0ZWd5O1xuICAgICAgICB9LFxuICAgIH07XG59O1xuZXhwb3J0IGNvbnN0IHJlc29sdmVSZXRyeVJ1bnRpbWVDb25maWcgPSAocmV0cnlTdHJhdGVneUNvbmZpZ3VyYXRpb24pID0+IHtcbiAgICBjb25zdCBydW50aW1lQ29uZmlnID0ge307XG4gICAgcnVudGltZUNvbmZpZy5yZXRyeVN0cmF0ZWd5ID0gcmV0cnlTdHJhdGVneUNvbmZpZ3VyYXRpb24ucmV0cnlTdHJhdGVneSgpO1xuICAgIHJldHVybiBydW50aW1lQ29uZmlnO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/retry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/get-array-if-single-item.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/get-array-if-single-item.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getArrayIfSingleItem: () => (/* binding */ getArrayIfSingleItem)\n/* harmony export */ });\nconst getArrayIfSingleItem = (mayBeArray) => Array.isArray(mayBeArray) ? mayBeArray : [mayBeArray];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9nZXQtYXJyYXktaWYtc2luZ2xlLWl0ZW0uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9nZXQtYXJyYXktaWYtc2luZ2xlLWl0ZW0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGdldEFycmF5SWZTaW5nbGVJdGVtID0gKG1heUJlQXJyYXkpID0+IEFycmF5LmlzQXJyYXkobWF5QmVBcnJheSkgPyBtYXlCZUFycmF5IDogW21heUJlQXJyYXldO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/get-array-if-single-item.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/get-value-from-text-node.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/get-value-from-text-node.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getValueFromTextNode: () => (/* binding */ getValueFromTextNode)\n/* harmony export */ });\nconst getValueFromTextNode = (obj) => {\n    const textNodeName = \"#text\";\n    for (const key in obj) {\n        if (obj.hasOwnProperty(key) && obj[key][textNodeName] !== undefined) {\n            obj[key] = obj[key][textNodeName];\n        }\n        else if (typeof obj[key] === \"object\" && obj[key] !== null) {\n            obj[key] = getValueFromTextNode(obj[key]);\n        }\n    }\n    return obj;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9nZXQtdmFsdWUtZnJvbS10ZXh0LW5vZGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrc21pdGh5LWNsaWVudEA0LjQuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9zbWl0aHktY2xpZW50L2Rpc3QtZXMvZ2V0LXZhbHVlLWZyb20tdGV4dC1ub2RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBnZXRWYWx1ZUZyb21UZXh0Tm9kZSA9IChvYmopID0+IHtcbiAgICBjb25zdCB0ZXh0Tm9kZU5hbWUgPSBcIiN0ZXh0XCI7XG4gICAgZm9yIChjb25zdCBrZXkgaW4gb2JqKSB7XG4gICAgICAgIGlmIChvYmouaGFzT3duUHJvcGVydHkoa2V5KSAmJiBvYmpba2V5XVt0ZXh0Tm9kZU5hbWVdICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIG9ialtrZXldID0gb2JqW2tleV1bdGV4dE5vZGVOYW1lXTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICh0eXBlb2Ygb2JqW2tleV0gPT09IFwib2JqZWN0XCIgJiYgb2JqW2tleV0gIT09IG51bGwpIHtcbiAgICAgICAgICAgIG9ialtrZXldID0gZ2V0VmFsdWVGcm9tVGV4dE5vZGUob2JqW2tleV0pO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBvYmo7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/get-value-from-text-node.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.Client),\n/* harmony export */   Command: () => (/* reexport safe */ _command__WEBPACK_IMPORTED_MODULE_2__.Command),\n/* harmony export */   LazyJsonString: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.LazyJsonString),\n/* harmony export */   NoOpLogger: () => (/* reexport safe */ _NoOpLogger__WEBPACK_IMPORTED_MODULE_14__.NoOpLogger),\n/* harmony export */   NumericValue: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.NumericValue),\n/* harmony export */   SENSITIVE_STRING: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_3__.SENSITIVE_STRING),\n/* harmony export */   ServiceException: () => (/* reexport safe */ _exceptions__WEBPACK_IMPORTED_MODULE_8__.ServiceException),\n/* harmony export */   _json: () => (/* reexport safe */ _serde_json__WEBPACK_IMPORTED_MODULE_18__._json),\n/* harmony export */   collectBody: () => (/* reexport safe */ _collect_stream_body__WEBPACK_IMPORTED_MODULE_1__.collectBody),\n/* harmony export */   convertMap: () => (/* reexport safe */ _object_mapping__WEBPACK_IMPORTED_MODULE_15__.convertMap),\n/* harmony export */   copyDocumentWithTransform: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.copyDocumentWithTransform),\n/* harmony export */   createAggregatedClient: () => (/* reexport safe */ _create_aggregated_client__WEBPACK_IMPORTED_MODULE_4__.createAggregatedClient),\n/* harmony export */   dateToUtcString: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.dateToUtcString),\n/* harmony export */   decorateServiceException: () => (/* reexport safe */ _exceptions__WEBPACK_IMPORTED_MODULE_8__.decorateServiceException),\n/* harmony export */   emitWarningIfUnsupportedVersion: () => (/* reexport safe */ _emitWarningIfUnsupportedVersion__WEBPACK_IMPORTED_MODULE_7__.emitWarningIfUnsupportedVersion),\n/* harmony export */   expectBoolean: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.expectBoolean),\n/* harmony export */   expectByte: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.expectByte),\n/* harmony export */   expectFloat32: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.expectFloat32),\n/* harmony export */   expectInt: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.expectInt),\n/* harmony export */   expectInt32: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.expectInt32),\n/* harmony export */   expectLong: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.expectLong),\n/* harmony export */   expectNonNull: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.expectNonNull),\n/* harmony export */   expectNumber: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.expectNumber),\n/* harmony export */   expectObject: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.expectObject),\n/* harmony export */   expectShort: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.expectShort),\n/* harmony export */   expectString: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.expectString),\n/* harmony export */   expectUnion: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.expectUnion),\n/* harmony export */   extendedEncodeURIComponent: () => (/* reexport safe */ _extended_encode_uri_component__WEBPACK_IMPORTED_MODULE_9__.extendedEncodeURIComponent),\n/* harmony export */   getArrayIfSingleItem: () => (/* reexport safe */ _get_array_if_single_item__WEBPACK_IMPORTED_MODULE_11__.getArrayIfSingleItem),\n/* harmony export */   getDefaultClientConfiguration: () => (/* reexport safe */ _extensions__WEBPACK_IMPORTED_MODULE_10__.getDefaultClientConfiguration),\n/* harmony export */   getDefaultExtensionConfiguration: () => (/* reexport safe */ _extensions__WEBPACK_IMPORTED_MODULE_10__.getDefaultExtensionConfiguration),\n/* harmony export */   getValueFromTextNode: () => (/* reexport safe */ _get_value_from_text_node__WEBPACK_IMPORTED_MODULE_12__.getValueFromTextNode),\n/* harmony export */   handleFloat: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.handleFloat),\n/* harmony export */   isSerializableHeaderValue: () => (/* reexport safe */ _is_serializable_header_value__WEBPACK_IMPORTED_MODULE_13__.isSerializableHeaderValue),\n/* harmony export */   limitedParseDouble: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.limitedParseDouble),\n/* harmony export */   limitedParseFloat: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.limitedParseFloat),\n/* harmony export */   limitedParseFloat32: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.limitedParseFloat32),\n/* harmony export */   loadConfigsForDefaultMode: () => (/* reexport safe */ _defaults_mode__WEBPACK_IMPORTED_MODULE_6__.loadConfigsForDefaultMode),\n/* harmony export */   logger: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.logger),\n/* harmony export */   map: () => (/* reexport safe */ _object_mapping__WEBPACK_IMPORTED_MODULE_15__.map),\n/* harmony export */   nv: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.nv),\n/* harmony export */   parseBoolean: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.parseBoolean),\n/* harmony export */   parseEpochTimestamp: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.parseEpochTimestamp),\n/* harmony export */   parseRfc3339DateTime: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.parseRfc3339DateTime),\n/* harmony export */   parseRfc3339DateTimeWithOffset: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.parseRfc3339DateTimeWithOffset),\n/* harmony export */   parseRfc7231DateTime: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.parseRfc7231DateTime),\n/* harmony export */   quoteHeader: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.quoteHeader),\n/* harmony export */   resolveDefaultRuntimeConfig: () => (/* reexport safe */ _extensions__WEBPACK_IMPORTED_MODULE_10__.resolveDefaultRuntimeConfig),\n/* harmony export */   resolvedPath: () => (/* reexport safe */ _resolve_path__WEBPACK_IMPORTED_MODULE_16__.resolvedPath),\n/* harmony export */   serializeDateTime: () => (/* reexport safe */ _ser_utils__WEBPACK_IMPORTED_MODULE_17__.serializeDateTime),\n/* harmony export */   serializeFloat: () => (/* reexport safe */ _ser_utils__WEBPACK_IMPORTED_MODULE_17__.serializeFloat),\n/* harmony export */   splitEvery: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.splitEvery),\n/* harmony export */   splitHeader: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.splitHeader),\n/* harmony export */   strictParseByte: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.strictParseByte),\n/* harmony export */   strictParseDouble: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.strictParseDouble),\n/* harmony export */   strictParseFloat: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.strictParseFloat),\n/* harmony export */   strictParseFloat32: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.strictParseFloat32),\n/* harmony export */   strictParseInt: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.strictParseInt),\n/* harmony export */   strictParseInt32: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.strictParseInt32),\n/* harmony export */   strictParseLong: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.strictParseLong),\n/* harmony export */   strictParseShort: () => (/* reexport safe */ _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__.strictParseShort),\n/* harmony export */   take: () => (/* reexport safe */ _object_mapping__WEBPACK_IMPORTED_MODULE_15__.take),\n/* harmony export */   throwDefaultError: () => (/* reexport safe */ _default_error_handler__WEBPACK_IMPORTED_MODULE_5__.throwDefaultError),\n/* harmony export */   withBaseException: () => (/* reexport safe */ _default_error_handler__WEBPACK_IMPORTED_MODULE_5__.withBaseException)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/client.js\");\n/* harmony import */ var _collect_stream_body__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./collect-stream-body */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/collect-stream-body.js\");\n/* harmony import */ var _command__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./command */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/command.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/constants.js\");\n/* harmony import */ var _create_aggregated_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./create-aggregated-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/create-aggregated-client.js\");\n/* harmony import */ var _default_error_handler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./default-error-handler */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/default-error-handler.js\");\n/* harmony import */ var _defaults_mode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./defaults-mode */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/defaults-mode.js\");\n/* harmony import */ var _emitWarningIfUnsupportedVersion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./emitWarningIfUnsupportedVersion */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/emitWarningIfUnsupportedVersion.js\");\n/* harmony import */ var _exceptions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./exceptions */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/exceptions.js\");\n/* harmony import */ var _extended_encode_uri_component__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./extended-encode-uri-component */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extended-encode-uri-component.js\");\n/* harmony import */ var _extensions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./extensions */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/extensions/index.js\");\n/* harmony import */ var _get_array_if_single_item__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./get-array-if-single-item */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/get-array-if-single-item.js\");\n/* harmony import */ var _get_value_from_text_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./get-value-from-text-node */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/get-value-from-text-node.js\");\n/* harmony import */ var _is_serializable_header_value__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./is-serializable-header-value */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/is-serializable-header-value.js\");\n/* harmony import */ var _NoOpLogger__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./NoOpLogger */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/NoOpLogger.js\");\n/* harmony import */ var _object_mapping__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./object-mapping */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/object-mapping.js\");\n/* harmony import */ var _resolve_path__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./resolve-path */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/resolve-path.js\");\n/* harmony import */ var _ser_utils__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./ser-utils */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/ser-utils.js\");\n/* harmony import */ var _serde_json__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./serde-json */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/serde-json.js\");\n/* harmony import */ var _smithy_core_serde__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @smithy/core/serde */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBeUI7QUFDYTtBQUNaO0FBQ0U7QUFDZTtBQUNIO0FBQ1I7QUFDa0I7QUFDckI7QUFDbUI7QUFDbkI7QUFDYztBQUNBO0FBQ0k7QUFDbEI7QUFDSTtBQUNGO0FBQ0g7QUFDQztBQUNNIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9jbGllbnRcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2NvbGxlY3Qtc3RyZWFtLWJvZHlcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2NvbW1hbmRcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2NvbnN0YW50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vY3JlYXRlLWFnZ3JlZ2F0ZWQtY2xpZW50XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9kZWZhdWx0LWVycm9yLWhhbmRsZXJcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2RlZmF1bHRzLW1vZGVcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2VtaXRXYXJuaW5nSWZVbnN1cHBvcnRlZFZlcnNpb25cIjtcbmV4cG9ydCAqIGZyb20gXCIuL2V4Y2VwdGlvbnNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2V4dGVuZGVkLWVuY29kZS11cmktY29tcG9uZW50XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9leHRlbnNpb25zXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9nZXQtYXJyYXktaWYtc2luZ2xlLWl0ZW1cIjtcbmV4cG9ydCAqIGZyb20gXCIuL2dldC12YWx1ZS1mcm9tLXRleHQtbm9kZVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vaXMtc2VyaWFsaXphYmxlLWhlYWRlci12YWx1ZVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vTm9PcExvZ2dlclwiO1xuZXhwb3J0ICogZnJvbSBcIi4vb2JqZWN0LW1hcHBpbmdcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3Jlc29sdmUtcGF0aFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vc2VyLXV0aWxzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zZXJkZS1qc29uXCI7XG5leHBvcnQgKiBmcm9tIFwiQHNtaXRoeS9jb3JlL3NlcmRlXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/is-serializable-header-value.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/is-serializable-header-value.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSerializableHeaderValue: () => (/* binding */ isSerializableHeaderValue)\n/* harmony export */ });\nconst isSerializableHeaderValue = (value) => {\n    return value != null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9pcy1zZXJpYWxpemFibGUtaGVhZGVyLXZhbHVlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3NtaXRoeS1jbGllbnRANC40LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvc21pdGh5LWNsaWVudC9kaXN0LWVzL2lzLXNlcmlhbGl6YWJsZS1oZWFkZXItdmFsdWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGlzU2VyaWFsaXphYmxlSGVhZGVyVmFsdWUgPSAodmFsdWUpID0+IHtcbiAgICByZXR1cm4gdmFsdWUgIT0gbnVsbDtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/is-serializable-header-value.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/object-mapping.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/object-mapping.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertMap: () => (/* binding */ convertMap),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   take: () => (/* binding */ take)\n/* harmony export */ });\nfunction map(arg0, arg1, arg2) {\n    let target;\n    let filter;\n    let instructions;\n    if (typeof arg1 === \"undefined\" && typeof arg2 === \"undefined\") {\n        target = {};\n        instructions = arg0;\n    }\n    else {\n        target = arg0;\n        if (typeof arg1 === \"function\") {\n            filter = arg1;\n            instructions = arg2;\n            return mapWithFilter(target, filter, instructions);\n        }\n        else {\n            instructions = arg1;\n        }\n    }\n    for (const key of Object.keys(instructions)) {\n        if (!Array.isArray(instructions[key])) {\n            target[key] = instructions[key];\n            continue;\n        }\n        applyInstruction(target, null, instructions, key);\n    }\n    return target;\n}\nconst convertMap = (target) => {\n    const output = {};\n    for (const [k, v] of Object.entries(target || {})) {\n        output[k] = [, v];\n    }\n    return output;\n};\nconst take = (source, instructions) => {\n    const out = {};\n    for (const key in instructions) {\n        applyInstruction(out, source, instructions, key);\n    }\n    return out;\n};\nconst mapWithFilter = (target, filter, instructions) => {\n    return map(target, Object.entries(instructions).reduce((_instructions, [key, value]) => {\n        if (Array.isArray(value)) {\n            _instructions[key] = value;\n        }\n        else {\n            if (typeof value === \"function\") {\n                _instructions[key] = [filter, value()];\n            }\n            else {\n                _instructions[key] = [filter, value];\n            }\n        }\n        return _instructions;\n    }, {}));\n};\nconst applyInstruction = (target, source, instructions, targetKey) => {\n    if (source !== null) {\n        let instruction = instructions[targetKey];\n        if (typeof instruction === \"function\") {\n            instruction = [, instruction];\n        }\n        const [filter = nonNullish, valueFn = pass, sourceKey = targetKey] = instruction;\n        if ((typeof filter === \"function\" && filter(source[sourceKey])) || (typeof filter !== \"function\" && !!filter)) {\n            target[targetKey] = valueFn(source[sourceKey]);\n        }\n        return;\n    }\n    let [filter, value] = instructions[targetKey];\n    if (typeof value === \"function\") {\n        let _value;\n        const defaultFilterPassed = filter === undefined && (_value = value()) != null;\n        const customFilterPassed = (typeof filter === \"function\" && !!filter(void 0)) || (typeof filter !== \"function\" && !!filter);\n        if (defaultFilterPassed) {\n            target[targetKey] = _value;\n        }\n        else if (customFilterPassed) {\n            target[targetKey] = value();\n        }\n    }\n    else {\n        const defaultFilterPassed = filter === undefined && value != null;\n        const customFilterPassed = (typeof filter === \"function\" && !!filter(value)) || (typeof filter !== \"function\" && !!filter);\n        if (defaultFilterPassed || customFilterPassed) {\n            target[targetKey] = value;\n        }\n    }\n};\nconst nonNullish = (_) => _ != null;\nconst pass = (_) => _;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/object-mapping.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/resolve-path.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/resolve-path.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolvedPath: () => (/* reexport safe */ _smithy_core_protocols__WEBPACK_IMPORTED_MODULE_0__.resolvedPath)\n/* harmony export */ });\n/* harmony import */ var _smithy_core_protocols__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core/protocols */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/index.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9yZXNvbHZlLXBhdGguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0QiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3NtaXRoeS1jbGllbnRANC40LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvc21pdGh5LWNsaWVudC9kaXN0LWVzL3Jlc29sdmUtcGF0aC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyByZXNvbHZlZFBhdGggfSBmcm9tIFwiQHNtaXRoeS9jb3JlL3Byb3RvY29sc1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/resolve-path.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/ser-utils.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/ser-utils.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serializeDateTime: () => (/* binding */ serializeDateTime),\n/* harmony export */   serializeFloat: () => (/* binding */ serializeFloat)\n/* harmony export */ });\nconst serializeFloat = (value) => {\n    if (value !== value) {\n        return \"NaN\";\n    }\n    switch (value) {\n        case Infinity:\n            return \"Infinity\";\n        case -Infinity:\n            return \"-Infinity\";\n        default:\n            return value;\n    }\n};\nconst serializeDateTime = (date) => date.toISOString().replace(\".000Z\", \"Z\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9zZXItdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9zZXItdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHNlcmlhbGl6ZUZsb2F0ID0gKHZhbHVlKSA9PiB7XG4gICAgaWYgKHZhbHVlICE9PSB2YWx1ZSkge1xuICAgICAgICByZXR1cm4gXCJOYU5cIjtcbiAgICB9XG4gICAgc3dpdGNoICh2YWx1ZSkge1xuICAgICAgICBjYXNlIEluZmluaXR5OlxuICAgICAgICAgICAgcmV0dXJuIFwiSW5maW5pdHlcIjtcbiAgICAgICAgY2FzZSAtSW5maW5pdHk6XG4gICAgICAgICAgICByZXR1cm4gXCItSW5maW5pdHlcIjtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9XG59O1xuZXhwb3J0IGNvbnN0IHNlcmlhbGl6ZURhdGVUaW1lID0gKGRhdGUpID0+IGRhdGUudG9JU09TdHJpbmcoKS5yZXBsYWNlKFwiLjAwMFpcIiwgXCJaXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/ser-utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/serde-json.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/serde-json.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _json: () => (/* binding */ _json)\n/* harmony export */ });\nconst _json = (obj) => {\n    if (obj == null) {\n        return {};\n    }\n    if (Array.isArray(obj)) {\n        return obj.filter((_) => _ != null).map(_json);\n    }\n    if (typeof obj === \"object\") {\n        const target = {};\n        for (const key of Object.keys(obj)) {\n            if (obj[key] == null) {\n                continue;\n            }\n            target[key] = _json(obj[key]);\n        }\n        return target;\n    }\n    return obj;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStzbWl0aHktY2xpZW50QDQuNC4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L3NtaXRoeS1jbGllbnQvZGlzdC1lcy9zZXJkZS1qc29uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrc21pdGh5LWNsaWVudEA0LjQuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9zbWl0aHktY2xpZW50L2Rpc3QtZXMvc2VyZGUtanNvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgX2pzb24gPSAob2JqKSA9PiB7XG4gICAgaWYgKG9iaiA9PSBudWxsKSB7XG4gICAgICAgIHJldHVybiB7fTtcbiAgICB9XG4gICAgaWYgKEFycmF5LmlzQXJyYXkob2JqKSkge1xuICAgICAgICByZXR1cm4gb2JqLmZpbHRlcigoXykgPT4gXyAhPSBudWxsKS5tYXAoX2pzb24pO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIG9iaiA9PT0gXCJvYmplY3RcIikge1xuICAgICAgICBjb25zdCB0YXJnZXQgPSB7fTtcbiAgICAgICAgZm9yIChjb25zdCBrZXkgb2YgT2JqZWN0LmtleXMob2JqKSkge1xuICAgICAgICAgICAgaWYgKG9ialtrZXldID09IG51bGwpIHtcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRhcmdldFtrZXldID0gX2pzb24ob2JqW2tleV0pO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0YXJnZXQ7XG4gICAgfVxuICAgIHJldHVybiBvYmo7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/serde-json.js\n");

/***/ })

};
;