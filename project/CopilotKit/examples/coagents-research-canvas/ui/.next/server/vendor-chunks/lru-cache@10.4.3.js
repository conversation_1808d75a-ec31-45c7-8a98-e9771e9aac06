"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lru-cache@10.4.3";
exports.ids = ["vendor-chunks/lru-cache@10.4.3"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/lru-cache@10.4.3/node_modules/lru-cache/dist/commonjs/index.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/lru-cache@10.4.3/node_modules/lru-cache/dist/commonjs/index.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/**\n * @module LRUCache\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.LRUCache = void 0;\nconst perf = typeof performance === 'object' &&\n    performance &&\n    typeof performance.now === 'function'\n    ? performance\n    : Date;\nconst warned = new Set();\n/* c8 ignore start */\nconst PROCESS = (typeof process === 'object' && !!process ? process : {});\n/* c8 ignore start */\nconst emitWarning = (msg, type, code, fn) => {\n    typeof PROCESS.emitWarning === 'function'\n        ? PROCESS.emitWarning(msg, type, code, fn)\n        : console.error(`[${code}] ${type}: ${msg}`);\n};\nlet AC = globalThis.AbortController;\nlet AS = globalThis.AbortSignal;\n/* c8 ignore start */\nif (typeof AC === 'undefined') {\n    //@ts-ignore\n    AS = class AbortSignal {\n        onabort;\n        _onabort = [];\n        reason;\n        aborted = false;\n        addEventListener(_, fn) {\n            this._onabort.push(fn);\n        }\n    };\n    //@ts-ignore\n    AC = class AbortController {\n        constructor() {\n            warnACPolyfill();\n        }\n        signal = new AS();\n        abort(reason) {\n            if (this.signal.aborted)\n                return;\n            //@ts-ignore\n            this.signal.reason = reason;\n            //@ts-ignore\n            this.signal.aborted = true;\n            //@ts-ignore\n            for (const fn of this.signal._onabort) {\n                fn(reason);\n            }\n            this.signal.onabort?.(reason);\n        }\n    };\n    let printACPolyfillWarning = PROCESS.env?.LRU_CACHE_IGNORE_AC_WARNING !== '1';\n    const warnACPolyfill = () => {\n        if (!printACPolyfillWarning)\n            return;\n        printACPolyfillWarning = false;\n        emitWarning('AbortController is not defined. If using lru-cache in ' +\n            'node 14, load an AbortController polyfill from the ' +\n            '`node-abort-controller` package. A minimal polyfill is ' +\n            'provided for use by LRUCache.fetch(), but it should not be ' +\n            'relied upon in other contexts (eg, passing it to other APIs that ' +\n            'use AbortController/AbortSignal might have undesirable effects). ' +\n            'You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.', 'NO_ABORT_CONTROLLER', 'ENOTSUP', warnACPolyfill);\n    };\n}\n/* c8 ignore stop */\nconst shouldWarn = (code) => !warned.has(code);\nconst TYPE = Symbol('type');\nconst isPosInt = (n) => n && n === Math.floor(n) && n > 0 && isFinite(n);\n/* c8 ignore start */\n// This is a little bit ridiculous, tbh.\n// The maximum array length is 2^32-1 or thereabouts on most JS impls.\n// And well before that point, you're caching the entire world, I mean,\n// that's ~32GB of just integers for the next/prev links, plus whatever\n// else to hold that many keys and values.  Just filling the memory with\n// zeroes at init time is brutal when you get that big.\n// But why not be complete?\n// Maybe in the future, these limits will have expanded.\nconst getUintArray = (max) => !isPosInt(max)\n    ? null\n    : max <= Math.pow(2, 8)\n        ? Uint8Array\n        : max <= Math.pow(2, 16)\n            ? Uint16Array\n            : max <= Math.pow(2, 32)\n                ? Uint32Array\n                : max <= Number.MAX_SAFE_INTEGER\n                    ? ZeroArray\n                    : null;\n/* c8 ignore stop */\nclass ZeroArray extends Array {\n    constructor(size) {\n        super(size);\n        this.fill(0);\n    }\n}\nclass Stack {\n    heap;\n    length;\n    // private constructor\n    static #constructing = false;\n    static create(max) {\n        const HeapCls = getUintArray(max);\n        if (!HeapCls)\n            return [];\n        Stack.#constructing = true;\n        const s = new Stack(max, HeapCls);\n        Stack.#constructing = false;\n        return s;\n    }\n    constructor(max, HeapCls) {\n        /* c8 ignore start */\n        if (!Stack.#constructing) {\n            throw new TypeError('instantiate Stack using Stack.create(n)');\n        }\n        /* c8 ignore stop */\n        this.heap = new HeapCls(max);\n        this.length = 0;\n    }\n    push(n) {\n        this.heap[this.length++] = n;\n    }\n    pop() {\n        return this.heap[--this.length];\n    }\n}\n/**\n * Default export, the thing you're using this module to get.\n *\n * The `K` and `V` types define the key and value types, respectively. The\n * optional `FC` type defines the type of the `context` object passed to\n * `cache.fetch()` and `cache.memo()`.\n *\n * Keys and values **must not** be `null` or `undefined`.\n *\n * All properties from the options object (with the exception of `max`,\n * `maxSize`, `fetchMethod`, `memoMethod`, `dispose` and `disposeAfter`) are\n * added as normal public members. (The listed options are read-only getters.)\n *\n * Changing any of these will alter the defaults for subsequent method calls.\n */\nclass LRUCache {\n    // options that cannot be changed without disaster\n    #max;\n    #maxSize;\n    #dispose;\n    #disposeAfter;\n    #fetchMethod;\n    #memoMethod;\n    /**\n     * {@link LRUCache.OptionsBase.ttl}\n     */\n    ttl;\n    /**\n     * {@link LRUCache.OptionsBase.ttlResolution}\n     */\n    ttlResolution;\n    /**\n     * {@link LRUCache.OptionsBase.ttlAutopurge}\n     */\n    ttlAutopurge;\n    /**\n     * {@link LRUCache.OptionsBase.updateAgeOnGet}\n     */\n    updateAgeOnGet;\n    /**\n     * {@link LRUCache.OptionsBase.updateAgeOnHas}\n     */\n    updateAgeOnHas;\n    /**\n     * {@link LRUCache.OptionsBase.allowStale}\n     */\n    allowStale;\n    /**\n     * {@link LRUCache.OptionsBase.noDisposeOnSet}\n     */\n    noDisposeOnSet;\n    /**\n     * {@link LRUCache.OptionsBase.noUpdateTTL}\n     */\n    noUpdateTTL;\n    /**\n     * {@link LRUCache.OptionsBase.maxEntrySize}\n     */\n    maxEntrySize;\n    /**\n     * {@link LRUCache.OptionsBase.sizeCalculation}\n     */\n    sizeCalculation;\n    /**\n     * {@link LRUCache.OptionsBase.noDeleteOnFetchRejection}\n     */\n    noDeleteOnFetchRejection;\n    /**\n     * {@link LRUCache.OptionsBase.noDeleteOnStaleGet}\n     */\n    noDeleteOnStaleGet;\n    /**\n     * {@link LRUCache.OptionsBase.allowStaleOnFetchAbort}\n     */\n    allowStaleOnFetchAbort;\n    /**\n     * {@link LRUCache.OptionsBase.allowStaleOnFetchRejection}\n     */\n    allowStaleOnFetchRejection;\n    /**\n     * {@link LRUCache.OptionsBase.ignoreFetchAbort}\n     */\n    ignoreFetchAbort;\n    // computed properties\n    #size;\n    #calculatedSize;\n    #keyMap;\n    #keyList;\n    #valList;\n    #next;\n    #prev;\n    #head;\n    #tail;\n    #free;\n    #disposed;\n    #sizes;\n    #starts;\n    #ttls;\n    #hasDispose;\n    #hasFetchMethod;\n    #hasDisposeAfter;\n    /**\n     * Do not call this method unless you need to inspect the\n     * inner workings of the cache.  If anything returned by this\n     * object is modified in any way, strange breakage may occur.\n     *\n     * These fields are private for a reason!\n     *\n     * @internal\n     */\n    static unsafeExposeInternals(c) {\n        return {\n            // properties\n            starts: c.#starts,\n            ttls: c.#ttls,\n            sizes: c.#sizes,\n            keyMap: c.#keyMap,\n            keyList: c.#keyList,\n            valList: c.#valList,\n            next: c.#next,\n            prev: c.#prev,\n            get head() {\n                return c.#head;\n            },\n            get tail() {\n                return c.#tail;\n            },\n            free: c.#free,\n            // methods\n            isBackgroundFetch: (p) => c.#isBackgroundFetch(p),\n            backgroundFetch: (k, index, options, context) => c.#backgroundFetch(k, index, options, context),\n            moveToTail: (index) => c.#moveToTail(index),\n            indexes: (options) => c.#indexes(options),\n            rindexes: (options) => c.#rindexes(options),\n            isStale: (index) => c.#isStale(index),\n        };\n    }\n    // Protected read-only members\n    /**\n     * {@link LRUCache.OptionsBase.max} (read-only)\n     */\n    get max() {\n        return this.#max;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.maxSize} (read-only)\n     */\n    get maxSize() {\n        return this.#maxSize;\n    }\n    /**\n     * The total computed size of items in the cache (read-only)\n     */\n    get calculatedSize() {\n        return this.#calculatedSize;\n    }\n    /**\n     * The number of items stored in the cache (read-only)\n     */\n    get size() {\n        return this.#size;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.fetchMethod} (read-only)\n     */\n    get fetchMethod() {\n        return this.#fetchMethod;\n    }\n    get memoMethod() {\n        return this.#memoMethod;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.dispose} (read-only)\n     */\n    get dispose() {\n        return this.#dispose;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.disposeAfter} (read-only)\n     */\n    get disposeAfter() {\n        return this.#disposeAfter;\n    }\n    constructor(options) {\n        const { max = 0, ttl, ttlResolution = 1, ttlAutopurge, updateAgeOnGet, updateAgeOnHas, allowStale, dispose, disposeAfter, noDisposeOnSet, noUpdateTTL, maxSize = 0, maxEntrySize = 0, sizeCalculation, fetchMethod, memoMethod, noDeleteOnFetchRejection, noDeleteOnStaleGet, allowStaleOnFetchRejection, allowStaleOnFetchAbort, ignoreFetchAbort, } = options;\n        if (max !== 0 && !isPosInt(max)) {\n            throw new TypeError('max option must be a nonnegative integer');\n        }\n        const UintArray = max ? getUintArray(max) : Array;\n        if (!UintArray) {\n            throw new Error('invalid max value: ' + max);\n        }\n        this.#max = max;\n        this.#maxSize = maxSize;\n        this.maxEntrySize = maxEntrySize || this.#maxSize;\n        this.sizeCalculation = sizeCalculation;\n        if (this.sizeCalculation) {\n            if (!this.#maxSize && !this.maxEntrySize) {\n                throw new TypeError('cannot set sizeCalculation without setting maxSize or maxEntrySize');\n            }\n            if (typeof this.sizeCalculation !== 'function') {\n                throw new TypeError('sizeCalculation set to non-function');\n            }\n        }\n        if (memoMethod !== undefined &&\n            typeof memoMethod !== 'function') {\n            throw new TypeError('memoMethod must be a function if defined');\n        }\n        this.#memoMethod = memoMethod;\n        if (fetchMethod !== undefined &&\n            typeof fetchMethod !== 'function') {\n            throw new TypeError('fetchMethod must be a function if specified');\n        }\n        this.#fetchMethod = fetchMethod;\n        this.#hasFetchMethod = !!fetchMethod;\n        this.#keyMap = new Map();\n        this.#keyList = new Array(max).fill(undefined);\n        this.#valList = new Array(max).fill(undefined);\n        this.#next = new UintArray(max);\n        this.#prev = new UintArray(max);\n        this.#head = 0;\n        this.#tail = 0;\n        this.#free = Stack.create(max);\n        this.#size = 0;\n        this.#calculatedSize = 0;\n        if (typeof dispose === 'function') {\n            this.#dispose = dispose;\n        }\n        if (typeof disposeAfter === 'function') {\n            this.#disposeAfter = disposeAfter;\n            this.#disposed = [];\n        }\n        else {\n            this.#disposeAfter = undefined;\n            this.#disposed = undefined;\n        }\n        this.#hasDispose = !!this.#dispose;\n        this.#hasDisposeAfter = !!this.#disposeAfter;\n        this.noDisposeOnSet = !!noDisposeOnSet;\n        this.noUpdateTTL = !!noUpdateTTL;\n        this.noDeleteOnFetchRejection = !!noDeleteOnFetchRejection;\n        this.allowStaleOnFetchRejection = !!allowStaleOnFetchRejection;\n        this.allowStaleOnFetchAbort = !!allowStaleOnFetchAbort;\n        this.ignoreFetchAbort = !!ignoreFetchAbort;\n        // NB: maxEntrySize is set to maxSize if it's set\n        if (this.maxEntrySize !== 0) {\n            if (this.#maxSize !== 0) {\n                if (!isPosInt(this.#maxSize)) {\n                    throw new TypeError('maxSize must be a positive integer if specified');\n                }\n            }\n            if (!isPosInt(this.maxEntrySize)) {\n                throw new TypeError('maxEntrySize must be a positive integer if specified');\n            }\n            this.#initializeSizeTracking();\n        }\n        this.allowStale = !!allowStale;\n        this.noDeleteOnStaleGet = !!noDeleteOnStaleGet;\n        this.updateAgeOnGet = !!updateAgeOnGet;\n        this.updateAgeOnHas = !!updateAgeOnHas;\n        this.ttlResolution =\n            isPosInt(ttlResolution) || ttlResolution === 0\n                ? ttlResolution\n                : 1;\n        this.ttlAutopurge = !!ttlAutopurge;\n        this.ttl = ttl || 0;\n        if (this.ttl) {\n            if (!isPosInt(this.ttl)) {\n                throw new TypeError('ttl must be a positive integer if specified');\n            }\n            this.#initializeTTLTracking();\n        }\n        // do not allow completely unbounded caches\n        if (this.#max === 0 && this.ttl === 0 && this.#maxSize === 0) {\n            throw new TypeError('At least one of max, maxSize, or ttl is required');\n        }\n        if (!this.ttlAutopurge && !this.#max && !this.#maxSize) {\n            const code = 'LRU_CACHE_UNBOUNDED';\n            if (shouldWarn(code)) {\n                warned.add(code);\n                const msg = 'TTL caching without ttlAutopurge, max, or maxSize can ' +\n                    'result in unbounded memory consumption.';\n                emitWarning(msg, 'UnboundedCacheWarning', code, LRUCache);\n            }\n        }\n    }\n    /**\n     * Return the number of ms left in the item's TTL. If item is not in cache,\n     * returns `0`. Returns `Infinity` if item is in cache without a defined TTL.\n     */\n    getRemainingTTL(key) {\n        return this.#keyMap.has(key) ? Infinity : 0;\n    }\n    #initializeTTLTracking() {\n        const ttls = new ZeroArray(this.#max);\n        const starts = new ZeroArray(this.#max);\n        this.#ttls = ttls;\n        this.#starts = starts;\n        this.#setItemTTL = (index, ttl, start = perf.now()) => {\n            starts[index] = ttl !== 0 ? start : 0;\n            ttls[index] = ttl;\n            if (ttl !== 0 && this.ttlAutopurge) {\n                const t = setTimeout(() => {\n                    if (this.#isStale(index)) {\n                        this.#delete(this.#keyList[index], 'expire');\n                    }\n                }, ttl + 1);\n                // unref() not supported on all platforms\n                /* c8 ignore start */\n                if (t.unref) {\n                    t.unref();\n                }\n                /* c8 ignore stop */\n            }\n        };\n        this.#updateItemAge = index => {\n            starts[index] = ttls[index] !== 0 ? perf.now() : 0;\n        };\n        this.#statusTTL = (status, index) => {\n            if (ttls[index]) {\n                const ttl = ttls[index];\n                const start = starts[index];\n                /* c8 ignore next */\n                if (!ttl || !start)\n                    return;\n                status.ttl = ttl;\n                status.start = start;\n                status.now = cachedNow || getNow();\n                const age = status.now - start;\n                status.remainingTTL = ttl - age;\n            }\n        };\n        // debounce calls to perf.now() to 1s so we're not hitting\n        // that costly call repeatedly.\n        let cachedNow = 0;\n        const getNow = () => {\n            const n = perf.now();\n            if (this.ttlResolution > 0) {\n                cachedNow = n;\n                const t = setTimeout(() => (cachedNow = 0), this.ttlResolution);\n                // not available on all platforms\n                /* c8 ignore start */\n                if (t.unref) {\n                    t.unref();\n                }\n                /* c8 ignore stop */\n            }\n            return n;\n        };\n        this.getRemainingTTL = key => {\n            const index = this.#keyMap.get(key);\n            if (index === undefined) {\n                return 0;\n            }\n            const ttl = ttls[index];\n            const start = starts[index];\n            if (!ttl || !start) {\n                return Infinity;\n            }\n            const age = (cachedNow || getNow()) - start;\n            return ttl - age;\n        };\n        this.#isStale = index => {\n            const s = starts[index];\n            const t = ttls[index];\n            return !!t && !!s && (cachedNow || getNow()) - s > t;\n        };\n    }\n    // conditionally set private methods related to TTL\n    #updateItemAge = () => { };\n    #statusTTL = () => { };\n    #setItemTTL = () => { };\n    /* c8 ignore stop */\n    #isStale = () => false;\n    #initializeSizeTracking() {\n        const sizes = new ZeroArray(this.#max);\n        this.#calculatedSize = 0;\n        this.#sizes = sizes;\n        this.#removeItemSize = index => {\n            this.#calculatedSize -= sizes[index];\n            sizes[index] = 0;\n        };\n        this.#requireSize = (k, v, size, sizeCalculation) => {\n            // provisionally accept background fetches.\n            // actual value size will be checked when they return.\n            if (this.#isBackgroundFetch(v)) {\n                return 0;\n            }\n            if (!isPosInt(size)) {\n                if (sizeCalculation) {\n                    if (typeof sizeCalculation !== 'function') {\n                        throw new TypeError('sizeCalculation must be a function');\n                    }\n                    size = sizeCalculation(v, k);\n                    if (!isPosInt(size)) {\n                        throw new TypeError('sizeCalculation return invalid (expect positive integer)');\n                    }\n                }\n                else {\n                    throw new TypeError('invalid size value (must be positive integer). ' +\n                        'When maxSize or maxEntrySize is used, sizeCalculation ' +\n                        'or size must be set.');\n                }\n            }\n            return size;\n        };\n        this.#addItemSize = (index, size, status) => {\n            sizes[index] = size;\n            if (this.#maxSize) {\n                const maxSize = this.#maxSize - sizes[index];\n                while (this.#calculatedSize > maxSize) {\n                    this.#evict(true);\n                }\n            }\n            this.#calculatedSize += sizes[index];\n            if (status) {\n                status.entrySize = size;\n                status.totalCalculatedSize = this.#calculatedSize;\n            }\n        };\n    }\n    #removeItemSize = _i => { };\n    #addItemSize = (_i, _s, _st) => { };\n    #requireSize = (_k, _v, size, sizeCalculation) => {\n        if (size || sizeCalculation) {\n            throw new TypeError('cannot set size without setting maxSize or maxEntrySize on cache');\n        }\n        return 0;\n    };\n    *#indexes({ allowStale = this.allowStale } = {}) {\n        if (this.#size) {\n            for (let i = this.#tail; true;) {\n                if (!this.#isValidIndex(i)) {\n                    break;\n                }\n                if (allowStale || !this.#isStale(i)) {\n                    yield i;\n                }\n                if (i === this.#head) {\n                    break;\n                }\n                else {\n                    i = this.#prev[i];\n                }\n            }\n        }\n    }\n    *#rindexes({ allowStale = this.allowStale } = {}) {\n        if (this.#size) {\n            for (let i = this.#head; true;) {\n                if (!this.#isValidIndex(i)) {\n                    break;\n                }\n                if (allowStale || !this.#isStale(i)) {\n                    yield i;\n                }\n                if (i === this.#tail) {\n                    break;\n                }\n                else {\n                    i = this.#next[i];\n                }\n            }\n        }\n    }\n    #isValidIndex(index) {\n        return (index !== undefined &&\n            this.#keyMap.get(this.#keyList[index]) === index);\n    }\n    /**\n     * Return a generator yielding `[key, value]` pairs,\n     * in order from most recently used to least recently used.\n     */\n    *entries() {\n        for (const i of this.#indexes()) {\n            if (this.#valList[i] !== undefined &&\n                this.#keyList[i] !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield [this.#keyList[i], this.#valList[i]];\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.entries}\n     *\n     * Return a generator yielding `[key, value]` pairs,\n     * in order from least recently used to most recently used.\n     */\n    *rentries() {\n        for (const i of this.#rindexes()) {\n            if (this.#valList[i] !== undefined &&\n                this.#keyList[i] !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield [this.#keyList[i], this.#valList[i]];\n            }\n        }\n    }\n    /**\n     * Return a generator yielding the keys in the cache,\n     * in order from most recently used to least recently used.\n     */\n    *keys() {\n        for (const i of this.#indexes()) {\n            const k = this.#keyList[i];\n            if (k !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield k;\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.keys}\n     *\n     * Return a generator yielding the keys in the cache,\n     * in order from least recently used to most recently used.\n     */\n    *rkeys() {\n        for (const i of this.#rindexes()) {\n            const k = this.#keyList[i];\n            if (k !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield k;\n            }\n        }\n    }\n    /**\n     * Return a generator yielding the values in the cache,\n     * in order from most recently used to least recently used.\n     */\n    *values() {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            if (v !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield this.#valList[i];\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.values}\n     *\n     * Return a generator yielding the values in the cache,\n     * in order from least recently used to most recently used.\n     */\n    *rvalues() {\n        for (const i of this.#rindexes()) {\n            const v = this.#valList[i];\n            if (v !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield this.#valList[i];\n            }\n        }\n    }\n    /**\n     * Iterating over the cache itself yields the same results as\n     * {@link LRUCache.entries}\n     */\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n    /**\n     * A String value that is used in the creation of the default string\n     * description of an object. Called by the built-in method\n     * `Object.prototype.toString`.\n     */\n    [Symbol.toStringTag] = 'LRUCache';\n    /**\n     * Find a value for which the supplied fn method returns a truthy value,\n     * similar to `Array.find()`. fn is called as `fn(value, key, cache)`.\n     */\n    find(fn, getOptions = {}) {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            if (fn(value, this.#keyList[i], this)) {\n                return this.get(this.#keyList[i], getOptions);\n            }\n        }\n    }\n    /**\n     * Call the supplied function on each item in the cache, in order from most\n     * recently used to least recently used.\n     *\n     * `fn` is called as `fn(value, key, cache)`.\n     *\n     * If `thisp` is provided, function will be called in the `this`-context of\n     * the provided object, or the cache if no `thisp` object is provided.\n     *\n     * Does not update age or recenty of use, or iterate over stale values.\n     */\n    forEach(fn, thisp = this) {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            fn.call(thisp, value, this.#keyList[i], this);\n        }\n    }\n    /**\n     * The same as {@link LRUCache.forEach} but items are iterated over in\n     * reverse order.  (ie, less recently used items are iterated over first.)\n     */\n    rforEach(fn, thisp = this) {\n        for (const i of this.#rindexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            fn.call(thisp, value, this.#keyList[i], this);\n        }\n    }\n    /**\n     * Delete any stale entries. Returns true if anything was removed,\n     * false otherwise.\n     */\n    purgeStale() {\n        let deleted = false;\n        for (const i of this.#rindexes({ allowStale: true })) {\n            if (this.#isStale(i)) {\n                this.#delete(this.#keyList[i], 'expire');\n                deleted = true;\n            }\n        }\n        return deleted;\n    }\n    /**\n     * Get the extended info about a given entry, to get its value, size, and\n     * TTL info simultaneously. Returns `undefined` if the key is not present.\n     *\n     * Unlike {@link LRUCache#dump}, which is designed to be portable and survive\n     * serialization, the `start` value is always the current timestamp, and the\n     * `ttl` is a calculated remaining time to live (negative if expired).\n     *\n     * Always returns stale values, if their info is found in the cache, so be\n     * sure to check for expirations (ie, a negative {@link LRUCache.Entry#ttl})\n     * if relevant.\n     */\n    info(key) {\n        const i = this.#keyMap.get(key);\n        if (i === undefined)\n            return undefined;\n        const v = this.#valList[i];\n        const value = this.#isBackgroundFetch(v)\n            ? v.__staleWhileFetching\n            : v;\n        if (value === undefined)\n            return undefined;\n        const entry = { value };\n        if (this.#ttls && this.#starts) {\n            const ttl = this.#ttls[i];\n            const start = this.#starts[i];\n            if (ttl && start) {\n                const remain = ttl - (perf.now() - start);\n                entry.ttl = remain;\n                entry.start = Date.now();\n            }\n        }\n        if (this.#sizes) {\n            entry.size = this.#sizes[i];\n        }\n        return entry;\n    }\n    /**\n     * Return an array of [key, {@link LRUCache.Entry}] tuples which can be\n     * passed to {@link LRLUCache#load}.\n     *\n     * The `start` fields are calculated relative to a portable `Date.now()`\n     * timestamp, even if `performance.now()` is available.\n     *\n     * Stale entries are always included in the `dump`, even if\n     * {@link LRUCache.OptionsBase.allowStale} is false.\n     *\n     * Note: this returns an actual array, not a generator, so it can be more\n     * easily passed around.\n     */\n    dump() {\n        const arr = [];\n        for (const i of this.#indexes({ allowStale: true })) {\n            const key = this.#keyList[i];\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined || key === undefined)\n                continue;\n            const entry = { value };\n            if (this.#ttls && this.#starts) {\n                entry.ttl = this.#ttls[i];\n                // always dump the start relative to a portable timestamp\n                // it's ok for this to be a bit slow, it's a rare operation.\n                const age = perf.now() - this.#starts[i];\n                entry.start = Math.floor(Date.now() - age);\n            }\n            if (this.#sizes) {\n                entry.size = this.#sizes[i];\n            }\n            arr.unshift([key, entry]);\n        }\n        return arr;\n    }\n    /**\n     * Reset the cache and load in the items in entries in the order listed.\n     *\n     * The shape of the resulting cache may be different if the same options are\n     * not used in both caches.\n     *\n     * The `start` fields are assumed to be calculated relative to a portable\n     * `Date.now()` timestamp, even if `performance.now()` is available.\n     */\n    load(arr) {\n        this.clear();\n        for (const [key, entry] of arr) {\n            if (entry.start) {\n                // entry.start is a portable timestamp, but we may be using\n                // node's performance.now(), so calculate the offset, so that\n                // we get the intended remaining TTL, no matter how long it's\n                // been on ice.\n                //\n                // it's ok for this to be a bit slow, it's a rare operation.\n                const age = Date.now() - entry.start;\n                entry.start = perf.now() - age;\n            }\n            this.set(key, entry.value, entry);\n        }\n    }\n    /**\n     * Add a value to the cache.\n     *\n     * Note: if `undefined` is specified as a value, this is an alias for\n     * {@link LRUCache#delete}\n     *\n     * Fields on the {@link LRUCache.SetOptions} options param will override\n     * their corresponding values in the constructor options for the scope\n     * of this single `set()` operation.\n     *\n     * If `start` is provided, then that will set the effective start\n     * time for the TTL calculation. Note that this must be a previous\n     * value of `performance.now()` if supported, or a previous value of\n     * `Date.now()` if not.\n     *\n     * Options object may also include `size`, which will prevent\n     * calling the `sizeCalculation` function and just use the specified\n     * number if it is a positive integer, and `noDisposeOnSet` which\n     * will prevent calling a `dispose` function in the case of\n     * overwrites.\n     *\n     * If the `size` (or return value of `sizeCalculation`) for a given\n     * entry is greater than `maxEntrySize`, then the item will not be\n     * added to the cache.\n     *\n     * Will update the recency of the entry.\n     *\n     * If the value is `undefined`, then this is an alias for\n     * `cache.delete(key)`. `undefined` is never stored in the cache.\n     */\n    set(k, v, setOptions = {}) {\n        if (v === undefined) {\n            this.delete(k);\n            return this;\n        }\n        const { ttl = this.ttl, start, noDisposeOnSet = this.noDisposeOnSet, sizeCalculation = this.sizeCalculation, status, } = setOptions;\n        let { noUpdateTTL = this.noUpdateTTL } = setOptions;\n        const size = this.#requireSize(k, v, setOptions.size || 0, sizeCalculation);\n        // if the item doesn't fit, don't do anything\n        // NB: maxEntrySize set to maxSize by default\n        if (this.maxEntrySize && size > this.maxEntrySize) {\n            if (status) {\n                status.set = 'miss';\n                status.maxEntrySizeExceeded = true;\n            }\n            // have to delete, in case something is there already.\n            this.#delete(k, 'set');\n            return this;\n        }\n        let index = this.#size === 0 ? undefined : this.#keyMap.get(k);\n        if (index === undefined) {\n            // addition\n            index = (this.#size === 0\n                ? this.#tail\n                : this.#free.length !== 0\n                    ? this.#free.pop()\n                    : this.#size === this.#max\n                        ? this.#evict(false)\n                        : this.#size);\n            this.#keyList[index] = k;\n            this.#valList[index] = v;\n            this.#keyMap.set(k, index);\n            this.#next[this.#tail] = index;\n            this.#prev[index] = this.#tail;\n            this.#tail = index;\n            this.#size++;\n            this.#addItemSize(index, size, status);\n            if (status)\n                status.set = 'add';\n            noUpdateTTL = false;\n        }\n        else {\n            // update\n            this.#moveToTail(index);\n            const oldVal = this.#valList[index];\n            if (v !== oldVal) {\n                if (this.#hasFetchMethod && this.#isBackgroundFetch(oldVal)) {\n                    oldVal.__abortController.abort(new Error('replaced'));\n                    const { __staleWhileFetching: s } = oldVal;\n                    if (s !== undefined && !noDisposeOnSet) {\n                        if (this.#hasDispose) {\n                            this.#dispose?.(s, k, 'set');\n                        }\n                        if (this.#hasDisposeAfter) {\n                            this.#disposed?.push([s, k, 'set']);\n                        }\n                    }\n                }\n                else if (!noDisposeOnSet) {\n                    if (this.#hasDispose) {\n                        this.#dispose?.(oldVal, k, 'set');\n                    }\n                    if (this.#hasDisposeAfter) {\n                        this.#disposed?.push([oldVal, k, 'set']);\n                    }\n                }\n                this.#removeItemSize(index);\n                this.#addItemSize(index, size, status);\n                this.#valList[index] = v;\n                if (status) {\n                    status.set = 'replace';\n                    const oldValue = oldVal && this.#isBackgroundFetch(oldVal)\n                        ? oldVal.__staleWhileFetching\n                        : oldVal;\n                    if (oldValue !== undefined)\n                        status.oldValue = oldValue;\n                }\n            }\n            else if (status) {\n                status.set = 'update';\n            }\n        }\n        if (ttl !== 0 && !this.#ttls) {\n            this.#initializeTTLTracking();\n        }\n        if (this.#ttls) {\n            if (!noUpdateTTL) {\n                this.#setItemTTL(index, ttl, start);\n            }\n            if (status)\n                this.#statusTTL(status, index);\n        }\n        if (!noDisposeOnSet && this.#hasDisposeAfter && this.#disposed) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n        return this;\n    }\n    /**\n     * Evict the least recently used item, returning its value or\n     * `undefined` if cache is empty.\n     */\n    pop() {\n        try {\n            while (this.#size) {\n                const val = this.#valList[this.#head];\n                this.#evict(true);\n                if (this.#isBackgroundFetch(val)) {\n                    if (val.__staleWhileFetching) {\n                        return val.__staleWhileFetching;\n                    }\n                }\n                else if (val !== undefined) {\n                    return val;\n                }\n            }\n        }\n        finally {\n            if (this.#hasDisposeAfter && this.#disposed) {\n                const dt = this.#disposed;\n                let task;\n                while ((task = dt?.shift())) {\n                    this.#disposeAfter?.(...task);\n                }\n            }\n        }\n    }\n    #evict(free) {\n        const head = this.#head;\n        const k = this.#keyList[head];\n        const v = this.#valList[head];\n        if (this.#hasFetchMethod && this.#isBackgroundFetch(v)) {\n            v.__abortController.abort(new Error('evicted'));\n        }\n        else if (this.#hasDispose || this.#hasDisposeAfter) {\n            if (this.#hasDispose) {\n                this.#dispose?.(v, k, 'evict');\n            }\n            if (this.#hasDisposeAfter) {\n                this.#disposed?.push([v, k, 'evict']);\n            }\n        }\n        this.#removeItemSize(head);\n        // if we aren't about to use the index, then null these out\n        if (free) {\n            this.#keyList[head] = undefined;\n            this.#valList[head] = undefined;\n            this.#free.push(head);\n        }\n        if (this.#size === 1) {\n            this.#head = this.#tail = 0;\n            this.#free.length = 0;\n        }\n        else {\n            this.#head = this.#next[head];\n        }\n        this.#keyMap.delete(k);\n        this.#size--;\n        return head;\n    }\n    /**\n     * Check if a key is in the cache, without updating the recency of use.\n     * Will return false if the item is stale, even though it is technically\n     * in the cache.\n     *\n     * Check if a key is in the cache, without updating the recency of\n     * use. Age is updated if {@link LRUCache.OptionsBase.updateAgeOnHas} is set\n     * to `true` in either the options or the constructor.\n     *\n     * Will return `false` if the item is stale, even though it is technically in\n     * the cache. The difference can be determined (if it matters) by using a\n     * `status` argument, and inspecting the `has` field.\n     *\n     * Will not update item age unless\n     * {@link LRUCache.OptionsBase.updateAgeOnHas} is set.\n     */\n    has(k, hasOptions = {}) {\n        const { updateAgeOnHas = this.updateAgeOnHas, status } = hasOptions;\n        const index = this.#keyMap.get(k);\n        if (index !== undefined) {\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v) &&\n                v.__staleWhileFetching === undefined) {\n                return false;\n            }\n            if (!this.#isStale(index)) {\n                if (updateAgeOnHas) {\n                    this.#updateItemAge(index);\n                }\n                if (status) {\n                    status.has = 'hit';\n                    this.#statusTTL(status, index);\n                }\n                return true;\n            }\n            else if (status) {\n                status.has = 'stale';\n                this.#statusTTL(status, index);\n            }\n        }\n        else if (status) {\n            status.has = 'miss';\n        }\n        return false;\n    }\n    /**\n     * Like {@link LRUCache#get} but doesn't update recency or delete stale\n     * items.\n     *\n     * Returns `undefined` if the item is stale, unless\n     * {@link LRUCache.OptionsBase.allowStale} is set.\n     */\n    peek(k, peekOptions = {}) {\n        const { allowStale = this.allowStale } = peekOptions;\n        const index = this.#keyMap.get(k);\n        if (index === undefined ||\n            (!allowStale && this.#isStale(index))) {\n            return;\n        }\n        const v = this.#valList[index];\n        // either stale and allowed, or forcing a refresh of non-stale value\n        return this.#isBackgroundFetch(v) ? v.__staleWhileFetching : v;\n    }\n    #backgroundFetch(k, index, options, context) {\n        const v = index === undefined ? undefined : this.#valList[index];\n        if (this.#isBackgroundFetch(v)) {\n            return v;\n        }\n        const ac = new AC();\n        const { signal } = options;\n        // when/if our AC signals, then stop listening to theirs.\n        signal?.addEventListener('abort', () => ac.abort(signal.reason), {\n            signal: ac.signal,\n        });\n        const fetchOpts = {\n            signal: ac.signal,\n            options,\n            context,\n        };\n        const cb = (v, updateCache = false) => {\n            const { aborted } = ac.signal;\n            const ignoreAbort = options.ignoreFetchAbort && v !== undefined;\n            if (options.status) {\n                if (aborted && !updateCache) {\n                    options.status.fetchAborted = true;\n                    options.status.fetchError = ac.signal.reason;\n                    if (ignoreAbort)\n                        options.status.fetchAbortIgnored = true;\n                }\n                else {\n                    options.status.fetchResolved = true;\n                }\n            }\n            if (aborted && !ignoreAbort && !updateCache) {\n                return fetchFail(ac.signal.reason);\n            }\n            // either we didn't abort, and are still here, or we did, and ignored\n            const bf = p;\n            if (this.#valList[index] === p) {\n                if (v === undefined) {\n                    if (bf.__staleWhileFetching) {\n                        this.#valList[index] = bf.__staleWhileFetching;\n                    }\n                    else {\n                        this.#delete(k, 'fetch');\n                    }\n                }\n                else {\n                    if (options.status)\n                        options.status.fetchUpdated = true;\n                    this.set(k, v, fetchOpts.options);\n                }\n            }\n            return v;\n        };\n        const eb = (er) => {\n            if (options.status) {\n                options.status.fetchRejected = true;\n                options.status.fetchError = er;\n            }\n            return fetchFail(er);\n        };\n        const fetchFail = (er) => {\n            const { aborted } = ac.signal;\n            const allowStaleAborted = aborted && options.allowStaleOnFetchAbort;\n            const allowStale = allowStaleAborted || options.allowStaleOnFetchRejection;\n            const noDelete = allowStale || options.noDeleteOnFetchRejection;\n            const bf = p;\n            if (this.#valList[index] === p) {\n                // if we allow stale on fetch rejections, then we need to ensure that\n                // the stale value is not removed from the cache when the fetch fails.\n                const del = !noDelete || bf.__staleWhileFetching === undefined;\n                if (del) {\n                    this.#delete(k, 'fetch');\n                }\n                else if (!allowStaleAborted) {\n                    // still replace the *promise* with the stale value,\n                    // since we are done with the promise at this point.\n                    // leave it untouched if we're still waiting for an\n                    // aborted background fetch that hasn't yet returned.\n                    this.#valList[index] = bf.__staleWhileFetching;\n                }\n            }\n            if (allowStale) {\n                if (options.status && bf.__staleWhileFetching !== undefined) {\n                    options.status.returnedStale = true;\n                }\n                return bf.__staleWhileFetching;\n            }\n            else if (bf.__returned === bf) {\n                throw er;\n            }\n        };\n        const pcall = (res, rej) => {\n            const fmp = this.#fetchMethod?.(k, v, fetchOpts);\n            if (fmp && fmp instanceof Promise) {\n                fmp.then(v => res(v === undefined ? undefined : v), rej);\n            }\n            // ignored, we go until we finish, regardless.\n            // defer check until we are actually aborting,\n            // so fetchMethod can override.\n            ac.signal.addEventListener('abort', () => {\n                if (!options.ignoreFetchAbort ||\n                    options.allowStaleOnFetchAbort) {\n                    res(undefined);\n                    // when it eventually resolves, update the cache.\n                    if (options.allowStaleOnFetchAbort) {\n                        res = v => cb(v, true);\n                    }\n                }\n            });\n        };\n        if (options.status)\n            options.status.fetchDispatched = true;\n        const p = new Promise(pcall).then(cb, eb);\n        const bf = Object.assign(p, {\n            __abortController: ac,\n            __staleWhileFetching: v,\n            __returned: undefined,\n        });\n        if (index === undefined) {\n            // internal, don't expose status.\n            this.set(k, bf, { ...fetchOpts.options, status: undefined });\n            index = this.#keyMap.get(k);\n        }\n        else {\n            this.#valList[index] = bf;\n        }\n        return bf;\n    }\n    #isBackgroundFetch(p) {\n        if (!this.#hasFetchMethod)\n            return false;\n        const b = p;\n        return (!!b &&\n            b instanceof Promise &&\n            b.hasOwnProperty('__staleWhileFetching') &&\n            b.__abortController instanceof AC);\n    }\n    async fetch(k, fetchOptions = {}) {\n        const { \n        // get options\n        allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, \n        // set options\n        ttl = this.ttl, noDisposeOnSet = this.noDisposeOnSet, size = 0, sizeCalculation = this.sizeCalculation, noUpdateTTL = this.noUpdateTTL, \n        // fetch exclusive options\n        noDeleteOnFetchRejection = this.noDeleteOnFetchRejection, allowStaleOnFetchRejection = this.allowStaleOnFetchRejection, ignoreFetchAbort = this.ignoreFetchAbort, allowStaleOnFetchAbort = this.allowStaleOnFetchAbort, context, forceRefresh = false, status, signal, } = fetchOptions;\n        if (!this.#hasFetchMethod) {\n            if (status)\n                status.fetch = 'get';\n            return this.get(k, {\n                allowStale,\n                updateAgeOnGet,\n                noDeleteOnStaleGet,\n                status,\n            });\n        }\n        const options = {\n            allowStale,\n            updateAgeOnGet,\n            noDeleteOnStaleGet,\n            ttl,\n            noDisposeOnSet,\n            size,\n            sizeCalculation,\n            noUpdateTTL,\n            noDeleteOnFetchRejection,\n            allowStaleOnFetchRejection,\n            allowStaleOnFetchAbort,\n            ignoreFetchAbort,\n            status,\n            signal,\n        };\n        let index = this.#keyMap.get(k);\n        if (index === undefined) {\n            if (status)\n                status.fetch = 'miss';\n            const p = this.#backgroundFetch(k, index, options, context);\n            return (p.__returned = p);\n        }\n        else {\n            // in cache, maybe already fetching\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v)) {\n                const stale = allowStale && v.__staleWhileFetching !== undefined;\n                if (status) {\n                    status.fetch = 'inflight';\n                    if (stale)\n                        status.returnedStale = true;\n                }\n                return stale ? v.__staleWhileFetching : (v.__returned = v);\n            }\n            // if we force a refresh, that means do NOT serve the cached value,\n            // unless we are already in the process of refreshing the cache.\n            const isStale = this.#isStale(index);\n            if (!forceRefresh && !isStale) {\n                if (status)\n                    status.fetch = 'hit';\n                this.#moveToTail(index);\n                if (updateAgeOnGet) {\n                    this.#updateItemAge(index);\n                }\n                if (status)\n                    this.#statusTTL(status, index);\n                return v;\n            }\n            // ok, it is stale or a forced refresh, and not already fetching.\n            // refresh the cache.\n            const p = this.#backgroundFetch(k, index, options, context);\n            const hasStale = p.__staleWhileFetching !== undefined;\n            const staleVal = hasStale && allowStale;\n            if (status) {\n                status.fetch = isStale ? 'stale' : 'refresh';\n                if (staleVal && isStale)\n                    status.returnedStale = true;\n            }\n            return staleVal ? p.__staleWhileFetching : (p.__returned = p);\n        }\n    }\n    async forceFetch(k, fetchOptions = {}) {\n        const v = await this.fetch(k, fetchOptions);\n        if (v === undefined)\n            throw new Error('fetch() returned undefined');\n        return v;\n    }\n    memo(k, memoOptions = {}) {\n        const memoMethod = this.#memoMethod;\n        if (!memoMethod) {\n            throw new Error('no memoMethod provided to constructor');\n        }\n        const { context, forceRefresh, ...options } = memoOptions;\n        const v = this.get(k, options);\n        if (!forceRefresh && v !== undefined)\n            return v;\n        const vv = memoMethod(k, v, {\n            options,\n            context,\n        });\n        this.set(k, vv, options);\n        return vv;\n    }\n    /**\n     * Return a value from the cache. Will update the recency of the cache\n     * entry found.\n     *\n     * If the key is not found, get() will return `undefined`.\n     */\n    get(k, getOptions = {}) {\n        const { allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, status, } = getOptions;\n        const index = this.#keyMap.get(k);\n        if (index !== undefined) {\n            const value = this.#valList[index];\n            const fetching = this.#isBackgroundFetch(value);\n            if (status)\n                this.#statusTTL(status, index);\n            if (this.#isStale(index)) {\n                if (status)\n                    status.get = 'stale';\n                // delete only if not an in-flight background fetch\n                if (!fetching) {\n                    if (!noDeleteOnStaleGet) {\n                        this.#delete(k, 'expire');\n                    }\n                    if (status && allowStale)\n                        status.returnedStale = true;\n                    return allowStale ? value : undefined;\n                }\n                else {\n                    if (status &&\n                        allowStale &&\n                        value.__staleWhileFetching !== undefined) {\n                        status.returnedStale = true;\n                    }\n                    return allowStale ? value.__staleWhileFetching : undefined;\n                }\n            }\n            else {\n                if (status)\n                    status.get = 'hit';\n                // if we're currently fetching it, we don't actually have it yet\n                // it's not stale, which means this isn't a staleWhileRefetching.\n                // If it's not stale, and fetching, AND has a __staleWhileFetching\n                // value, then that means the user fetched with {forceRefresh:true},\n                // so it's safe to return that value.\n                if (fetching) {\n                    return value.__staleWhileFetching;\n                }\n                this.#moveToTail(index);\n                if (updateAgeOnGet) {\n                    this.#updateItemAge(index);\n                }\n                return value;\n            }\n        }\n        else if (status) {\n            status.get = 'miss';\n        }\n    }\n    #connect(p, n) {\n        this.#prev[n] = p;\n        this.#next[p] = n;\n    }\n    #moveToTail(index) {\n        // if tail already, nothing to do\n        // if head, move head to next[index]\n        // else\n        //   move next[prev[index]] to next[index] (head has no prev)\n        //   move prev[next[index]] to prev[index]\n        // prev[index] = tail\n        // next[tail] = index\n        // tail = index\n        if (index !== this.#tail) {\n            if (index === this.#head) {\n                this.#head = this.#next[index];\n            }\n            else {\n                this.#connect(this.#prev[index], this.#next[index]);\n            }\n            this.#connect(this.#tail, index);\n            this.#tail = index;\n        }\n    }\n    /**\n     * Deletes a key out of the cache.\n     *\n     * Returns true if the key was deleted, false otherwise.\n     */\n    delete(k) {\n        return this.#delete(k, 'delete');\n    }\n    #delete(k, reason) {\n        let deleted = false;\n        if (this.#size !== 0) {\n            const index = this.#keyMap.get(k);\n            if (index !== undefined) {\n                deleted = true;\n                if (this.#size === 1) {\n                    this.#clear(reason);\n                }\n                else {\n                    this.#removeItemSize(index);\n                    const v = this.#valList[index];\n                    if (this.#isBackgroundFetch(v)) {\n                        v.__abortController.abort(new Error('deleted'));\n                    }\n                    else if (this.#hasDispose || this.#hasDisposeAfter) {\n                        if (this.#hasDispose) {\n                            this.#dispose?.(v, k, reason);\n                        }\n                        if (this.#hasDisposeAfter) {\n                            this.#disposed?.push([v, k, reason]);\n                        }\n                    }\n                    this.#keyMap.delete(k);\n                    this.#keyList[index] = undefined;\n                    this.#valList[index] = undefined;\n                    if (index === this.#tail) {\n                        this.#tail = this.#prev[index];\n                    }\n                    else if (index === this.#head) {\n                        this.#head = this.#next[index];\n                    }\n                    else {\n                        const pi = this.#prev[index];\n                        this.#next[pi] = this.#next[index];\n                        const ni = this.#next[index];\n                        this.#prev[ni] = this.#prev[index];\n                    }\n                    this.#size--;\n                    this.#free.push(index);\n                }\n            }\n        }\n        if (this.#hasDisposeAfter && this.#disposed?.length) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n        return deleted;\n    }\n    /**\n     * Clear the cache entirely, throwing away all values.\n     */\n    clear() {\n        return this.#clear('delete');\n    }\n    #clear(reason) {\n        for (const index of this.#rindexes({ allowStale: true })) {\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v)) {\n                v.__abortController.abort(new Error('deleted'));\n            }\n            else {\n                const k = this.#keyList[index];\n                if (this.#hasDispose) {\n                    this.#dispose?.(v, k, reason);\n                }\n                if (this.#hasDisposeAfter) {\n                    this.#disposed?.push([v, k, reason]);\n                }\n            }\n        }\n        this.#keyMap.clear();\n        this.#valList.fill(undefined);\n        this.#keyList.fill(undefined);\n        if (this.#ttls && this.#starts) {\n            this.#ttls.fill(0);\n            this.#starts.fill(0);\n        }\n        if (this.#sizes) {\n            this.#sizes.fill(0);\n        }\n        this.#head = 0;\n        this.#tail = 0;\n        this.#free.length = 0;\n        this.#calculatedSize = 0;\n        this.#size = 0;\n        if (this.#hasDisposeAfter && this.#disposed) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n    }\n}\nexports.LRUCache = LRUCache;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/lru-cache@10.4.3/node_modules/lru-cache/dist/commonjs/index.js\n");

/***/ })

};
;