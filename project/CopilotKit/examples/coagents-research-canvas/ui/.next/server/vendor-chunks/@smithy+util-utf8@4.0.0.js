"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+util-utf8@4.0.0";
exports.ids = ["vendor-chunks/@smithy+util-utf8@4.0.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/fromUtf8.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/fromUtf8.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromUtf8: () => (/* binding */ fromUtf8)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-buffer-from */ \"(rsc)/./node_modules/.pnpm/@smithy+util-buffer-from@4.0.0/node_modules/@smithy/util-buffer-from/dist-es/index.js\");\n\nconst fromUtf8 = (input) => {\n    const buf = (0,_smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_0__.fromString)(input, \"utf8\");\n    return new Uint8Array(buf.buffer, buf.byteOffset, buf.byteLength / Uint8Array.BYTES_PER_ELEMENT);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXV0ZjhANC4wLjAvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC11dGY4L2Rpc3QtZXMvZnJvbVV0ZjguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0Q7QUFDL0M7QUFDUCxnQkFBZ0Isb0VBQVU7QUFDMUI7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC11dGY4QDQuMC4wL25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtdXRmOC9kaXN0LWVzL2Zyb21VdGY4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZyb21TdHJpbmcgfSBmcm9tIFwiQHNtaXRoeS91dGlsLWJ1ZmZlci1mcm9tXCI7XG5leHBvcnQgY29uc3QgZnJvbVV0ZjggPSAoaW5wdXQpID0+IHtcbiAgICBjb25zdCBidWYgPSBmcm9tU3RyaW5nKGlucHV0LCBcInV0ZjhcIik7XG4gICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGJ1Zi5idWZmZXIsIGJ1Zi5ieXRlT2Zmc2V0LCBidWYuYnl0ZUxlbmd0aCAvIFVpbnQ4QXJyYXkuQllURVNfUEVSX0VMRU1FTlQpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/fromUtf8.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromUtf8: () => (/* reexport safe */ _fromUtf8__WEBPACK_IMPORTED_MODULE_0__.fromUtf8),\n/* harmony export */   toUint8Array: () => (/* reexport safe */ _toUint8Array__WEBPACK_IMPORTED_MODULE_1__.toUint8Array),\n/* harmony export */   toUtf8: () => (/* reexport safe */ _toUtf8__WEBPACK_IMPORTED_MODULE_2__.toUtf8)\n/* harmony export */ });\n/* harmony import */ var _fromUtf8__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fromUtf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/fromUtf8.js\");\n/* harmony import */ var _toUint8Array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./toUint8Array */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/toUint8Array.js\");\n/* harmony import */ var _toUtf8__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./toUtf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/toUtf8.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXV0ZjhANC4wLjAvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC11dGY4L2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTJCO0FBQ0k7QUFDTiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC11dGY4QDQuMC4wL25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtdXRmOC9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2Zyb21VdGY4XCI7XG5leHBvcnQgKiBmcm9tIFwiLi90b1VpbnQ4QXJyYXlcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3RvVXRmOFwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/toUint8Array.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/toUint8Array.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toUint8Array: () => (/* binding */ toUint8Array)\n/* harmony export */ });\n/* harmony import */ var _fromUtf8__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fromUtf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/fromUtf8.js\");\n\nconst toUint8Array = (data) => {\n    if (typeof data === \"string\") {\n        return (0,_fromUtf8__WEBPACK_IMPORTED_MODULE_0__.fromUtf8)(data);\n    }\n    if (ArrayBuffer.isView(data)) {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength / Uint8Array.BYTES_PER_ELEMENT);\n    }\n    return new Uint8Array(data);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXV0ZjhANC4wLjAvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC11dGY4L2Rpc3QtZXMvdG9VaW50OEFycmF5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDO0FBQy9CO0FBQ1A7QUFDQSxlQUFlLG1EQUFRO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC11dGY4QDQuMC4wL25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtdXRmOC9kaXN0LWVzL3RvVWludDhBcnJheS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBmcm9tVXRmOCB9IGZyb20gXCIuL2Zyb21VdGY4XCI7XG5leHBvcnQgY29uc3QgdG9VaW50OEFycmF5ID0gKGRhdGEpID0+IHtcbiAgICBpZiAodHlwZW9mIGRhdGEgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgcmV0dXJuIGZyb21VdGY4KGRhdGEpO1xuICAgIH1cbiAgICBpZiAoQXJyYXlCdWZmZXIuaXNWaWV3KGRhdGEpKSB7XG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShkYXRhLmJ1ZmZlciwgZGF0YS5ieXRlT2Zmc2V0LCBkYXRhLmJ5dGVMZW5ndGggLyBVaW50OEFycmF5LkJZVEVTX1BFUl9FTEVNRU5UKTtcbiAgICB9XG4gICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGRhdGEpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/toUint8Array.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/toUtf8.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/toUtf8.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toUtf8: () => (/* binding */ toUtf8)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-buffer-from */ \"(rsc)/./node_modules/.pnpm/@smithy+util-buffer-from@4.0.0/node_modules/@smithy/util-buffer-from/dist-es/index.js\");\n\nconst toUtf8 = (input) => {\n    if (typeof input === \"string\") {\n        return input;\n    }\n    if (typeof input !== \"object\" || typeof input.byteOffset !== \"number\" || typeof input.byteLength !== \"number\") {\n        throw new Error(\"@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.\");\n    }\n    return (0,_smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_0__.fromArrayBuffer)(input.buffer, input.byteOffset, input.byteLength).toString(\"utf8\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXV0ZjhANC4wLjAvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC11dGY4L2Rpc3QtZXMvdG9VdGY4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJEO0FBQ3BEO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyx5RUFBZTtBQUMxQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC11dGY4QDQuMC4wL25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtdXRmOC9kaXN0LWVzL3RvVXRmOC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBmcm9tQXJyYXlCdWZmZXIgfSBmcm9tIFwiQHNtaXRoeS91dGlsLWJ1ZmZlci1mcm9tXCI7XG5leHBvcnQgY29uc3QgdG9VdGY4ID0gKGlucHV0KSA9PiB7XG4gICAgaWYgKHR5cGVvZiBpbnB1dCA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICByZXR1cm4gaW5wdXQ7XG4gICAgfVxuICAgIGlmICh0eXBlb2YgaW5wdXQgIT09IFwib2JqZWN0XCIgfHwgdHlwZW9mIGlucHV0LmJ5dGVPZmZzZXQgIT09IFwibnVtYmVyXCIgfHwgdHlwZW9mIGlucHV0LmJ5dGVMZW5ndGggIT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQHNtaXRoeS91dGlsLXV0Zjg6IHRvVXRmOCBlbmNvZGVyIGZ1bmN0aW9uIG9ubHkgYWNjZXB0cyBzdHJpbmcgfCBVaW50OEFycmF5LlwiKTtcbiAgICB9XG4gICAgcmV0dXJuIGZyb21BcnJheUJ1ZmZlcihpbnB1dC5idWZmZXIsIGlucHV0LmJ5dGVPZmZzZXQsIGlucHV0LmJ5dGVMZW5ndGgpLnRvU3RyaW5nKFwidXRmOFwiKTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/toUtf8.js\n");

/***/ })

};
;