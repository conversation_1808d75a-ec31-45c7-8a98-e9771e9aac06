"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0";
exports.ids = ["vendor-chunks/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/ast-to-react.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/ast-to-react.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   childrenToReact: () => (/* binding */ childrenToReact)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_is__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-is */ \"(ssr)/./node_modules/.pnpm/react-is@18.3.1/node_modules/react-is/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/.pnpm/hast-util-whitespace@2.0.1/node_modules/hast-util-whitespace/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/find.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/hast-to-react.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/.pnpm/space-separated-tokens@2.0.2/node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/.pnpm/comma-separated-tokens@2.0.3/node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var style_to_object__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! style-to-object */ \"(ssr)/./node_modules/.pnpm/style-to-object@0.4.4/node_modules/style-to-object/index.mjs\");\n/* harmony import */ var _uri_transformer_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./uri-transformer.js */ \"(ssr)/./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/uri-transformer.js\");\n/**\n * @template T\n * @typedef {import('react').ComponentType<T>} ComponentType<T>\n */\n\n/**\n * @template {import('react').ElementType} T\n * @typedef {import('react').ComponentPropsWithoutRef<T>} ComponentPropsWithoutRef<T>\n */\n\n/**\n * @typedef {import('react').ReactNode} ReactNode\n * @typedef {import('unist').Position} Position\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').DocType} Doctype\n * @typedef {import('property-information').Info} Info\n * @typedef {import('property-information').Schema} Schema\n * @typedef {import('./complex-types.js').ReactMarkdownProps} ReactMarkdownProps\n *\n * @typedef Raw\n * @property {'raw'} type\n * @property {string} value\n *\n * @typedef Context\n * @property {Options} options\n * @property {Schema} schema\n * @property {number} listDepth\n *\n * @callback TransformLink\n * @param {string} href\n * @param {Array<ElementContent>} children\n * @param {string?} title\n * @returns {string}\n *\n * @callback TransformImage\n * @param {string} src\n * @param {string} alt\n * @param {string?} title\n * @returns {string}\n *\n * @typedef {import('react').HTMLAttributeAnchorTarget} TransformLinkTargetType\n *\n * @callback TransformLinkTarget\n * @param {string} href\n * @param {Array<ElementContent>} children\n * @param {string?} title\n * @returns {TransformLinkTargetType|undefined}\n *\n * @typedef {keyof JSX.IntrinsicElements} ReactMarkdownNames\n *\n * To do: is `data-sourcepos` typeable?\n *\n * @typedef {ComponentPropsWithoutRef<'code'> & ReactMarkdownProps & {inline?: boolean}} CodeProps\n * @typedef {ComponentPropsWithoutRef<'h1'> & ReactMarkdownProps & {level: number}} HeadingProps\n * @typedef {ComponentPropsWithoutRef<'li'> & ReactMarkdownProps & {checked: boolean|null, index: number, ordered: boolean}} LiProps\n * @typedef {ComponentPropsWithoutRef<'ol'> & ReactMarkdownProps & {depth: number, ordered: true}} OrderedListProps\n * @typedef {ComponentPropsWithoutRef<'td'> & ReactMarkdownProps & {style?: Record<string, unknown>, isHeader: false}} TableDataCellProps\n * @typedef {ComponentPropsWithoutRef<'th'> & ReactMarkdownProps & {style?: Record<string, unknown>, isHeader: true}} TableHeaderCellProps\n * @typedef {ComponentPropsWithoutRef<'tr'> & ReactMarkdownProps & {isHeader: boolean}} TableRowProps\n * @typedef {ComponentPropsWithoutRef<'ul'> & ReactMarkdownProps & {depth: number, ordered: false}} UnorderedListProps\n *\n * @typedef {ComponentType<CodeProps>} CodeComponent\n * @typedef {ComponentType<HeadingProps>} HeadingComponent\n * @typedef {ComponentType<LiProps>} LiComponent\n * @typedef {ComponentType<OrderedListProps>} OrderedListComponent\n * @typedef {ComponentType<TableDataCellProps>} TableDataCellComponent\n * @typedef {ComponentType<TableHeaderCellProps>} TableHeaderCellComponent\n * @typedef {ComponentType<TableRowProps>} TableRowComponent\n * @typedef {ComponentType<UnorderedListProps>} UnorderedListComponent\n *\n * @typedef SpecialComponents\n * @property {CodeComponent|ReactMarkdownNames} code\n * @property {HeadingComponent|ReactMarkdownNames} h1\n * @property {HeadingComponent|ReactMarkdownNames} h2\n * @property {HeadingComponent|ReactMarkdownNames} h3\n * @property {HeadingComponent|ReactMarkdownNames} h4\n * @property {HeadingComponent|ReactMarkdownNames} h5\n * @property {HeadingComponent|ReactMarkdownNames} h6\n * @property {LiComponent|ReactMarkdownNames} li\n * @property {OrderedListComponent|ReactMarkdownNames} ol\n * @property {TableDataCellComponent|ReactMarkdownNames} td\n * @property {TableHeaderCellComponent|ReactMarkdownNames} th\n * @property {TableRowComponent|ReactMarkdownNames} tr\n * @property {UnorderedListComponent|ReactMarkdownNames} ul\n *\n * @typedef {Partial<Omit<import('./complex-types.js').NormalComponents, keyof SpecialComponents> & SpecialComponents>} Components\n *\n * @typedef Options\n * @property {boolean} [sourcePos=false]\n * @property {boolean} [rawSourcePos=false]\n * @property {boolean} [skipHtml=false]\n * @property {boolean} [includeElementIndex=false]\n * @property {null|false|TransformLink} [transformLinkUri]\n * @property {TransformImage} [transformImageUri]\n * @property {TransformLinkTargetType|TransformLinkTarget} [linkTarget]\n * @property {Components} [components]\n */\n\n\n\n\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n// The table-related elements that must not contain whitespace text according\n// to React.\nconst tableElements = new Set(['table', 'thead', 'tbody', 'tfoot', 'tr'])\n\n/**\n * @param {Context} context\n * @param {Element|Root} node\n */\nfunction childrenToReact(context, node) {\n  /** @type {Array<ReactNode>} */\n  const children = []\n  let childIndex = -1\n  /** @type {Comment|Doctype|Element|Raw|Text} */\n  let child\n\n  while (++childIndex < node.children.length) {\n    child = node.children[childIndex]\n\n    if (child.type === 'element') {\n      children.push(toReact(context, child, childIndex, node))\n    } else if (child.type === 'text') {\n      // Currently, a warning is triggered by react for *any* white space in\n      // tables.\n      // So we drop it.\n      // See: <https://github.com/facebook/react/pull/7081>.\n      // See: <https://github.com/facebook/react/pull/7515>.\n      // See: <https://github.com/remarkjs/remark-react/issues/64>.\n      // See: <https://github.com/remarkjs/react-markdown/issues/576>.\n      if (\n        node.type !== 'element' ||\n        !tableElements.has(node.tagName) ||\n        !(0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__.whitespace)(child)\n      ) {\n        children.push(child.value)\n      }\n    } else if (child.type === 'raw' && !context.options.skipHtml) {\n      // Default behavior is to show (encoded) HTML.\n      children.push(child.value)\n    }\n  }\n\n  return children\n}\n\n/**\n * @param {Context} context\n * @param {Element} node\n * @param {number} index\n * @param {Element|Root} parent\n */\nfunction toReact(context, node, index, parent) {\n  const options = context.options\n  const transform =\n    options.transformLinkUri === undefined\n      ? _uri_transformer_js__WEBPACK_IMPORTED_MODULE_4__.uriTransformer\n      : options.transformLinkUri\n  const parentSchema = context.schema\n  /** @type {ReactMarkdownNames} */\n  // @ts-expect-error assume a known HTML/SVG element.\n  const name = node.tagName\n  /** @type {Record<string, unknown>} */\n  const properties = {}\n  let schema = parentSchema\n  /** @type {string} */\n  let property\n\n  if (parentSchema.space === 'html' && name === 'svg') {\n    schema = property_information__WEBPACK_IMPORTED_MODULE_5__.svg\n    context.schema = schema\n  }\n\n  if (node.properties) {\n    for (property in node.properties) {\n      if (own.call(node.properties, property)) {\n        addProperty(properties, property, node.properties[property], context)\n      }\n    }\n  }\n\n  if (name === 'ol' || name === 'ul') {\n    context.listDepth++\n  }\n\n  const children = childrenToReact(context, node)\n\n  if (name === 'ol' || name === 'ul') {\n    context.listDepth--\n  }\n\n  // Restore parent schema.\n  context.schema = parentSchema\n\n  // Nodes created by plugins do not have positional info, in which case we use\n  // an object that matches the position interface.\n  const position = node.position || {\n    start: {line: null, column: null, offset: null},\n    end: {line: null, column: null, offset: null}\n  }\n  const component =\n    options.components && own.call(options.components, name)\n      ? options.components[name]\n      : name\n  const basic = typeof component === 'string' || component === react__WEBPACK_IMPORTED_MODULE_0__.Fragment\n\n  if (!react_is__WEBPACK_IMPORTED_MODULE_1__.isValidElementType(component)) {\n    throw new TypeError(\n      `Component for name \\`${name}\\` not defined or is not renderable`\n    )\n  }\n\n  properties.key = index\n\n  if (name === 'a' && options.linkTarget) {\n    properties.target =\n      typeof options.linkTarget === 'function'\n        ? options.linkTarget(\n            String(properties.href || ''),\n            node.children,\n            typeof properties.title === 'string' ? properties.title : null\n          )\n        : options.linkTarget\n  }\n\n  if (name === 'a' && transform) {\n    properties.href = transform(\n      String(properties.href || ''),\n      node.children,\n      typeof properties.title === 'string' ? properties.title : null\n    )\n  }\n\n  if (\n    !basic &&\n    name === 'code' &&\n    parent.type === 'element' &&\n    parent.tagName !== 'pre'\n  ) {\n    properties.inline = true\n  }\n\n  if (\n    !basic &&\n    (name === 'h1' ||\n      name === 'h2' ||\n      name === 'h3' ||\n      name === 'h4' ||\n      name === 'h5' ||\n      name === 'h6')\n  ) {\n    properties.level = Number.parseInt(name.charAt(1), 10)\n  }\n\n  if (name === 'img' && options.transformImageUri) {\n    properties.src = options.transformImageUri(\n      String(properties.src || ''),\n      String(properties.alt || ''),\n      typeof properties.title === 'string' ? properties.title : null\n    )\n  }\n\n  if (!basic && name === 'li' && parent.type === 'element') {\n    const input = getInputElement(node)\n    properties.checked =\n      input && input.properties ? Boolean(input.properties.checked) : null\n    properties.index = getElementsBeforeCount(parent, node)\n    properties.ordered = parent.tagName === 'ol'\n  }\n\n  if (!basic && (name === 'ol' || name === 'ul')) {\n    properties.ordered = name === 'ol'\n    properties.depth = context.listDepth\n  }\n\n  if (name === 'td' || name === 'th') {\n    if (properties.align) {\n      if (!properties.style) properties.style = {}\n      // @ts-expect-error assume `style` is an object\n      properties.style.textAlign = properties.align\n      delete properties.align\n    }\n\n    if (!basic) {\n      properties.isHeader = name === 'th'\n    }\n  }\n\n  if (!basic && name === 'tr' && parent.type === 'element') {\n    properties.isHeader = Boolean(parent.tagName === 'thead')\n  }\n\n  // If `sourcePos` is given, pass source information (line/column info from markdown source).\n  if (options.sourcePos) {\n    properties['data-sourcepos'] = flattenPosition(position)\n  }\n\n  if (!basic && options.rawSourcePos) {\n    properties.sourcePosition = node.position\n  }\n\n  // If `includeElementIndex` is given, pass node index info to components.\n  if (!basic && options.includeElementIndex) {\n    properties.index = getElementsBeforeCount(parent, node)\n    properties.siblingCount = getElementsBeforeCount(parent)\n  }\n\n  if (!basic) {\n    properties.node = node\n  }\n\n  // Ensure no React warnings are emitted for void elements w/ children.\n  return children.length > 0\n    ? react__WEBPACK_IMPORTED_MODULE_0__.createElement(component, properties, children)\n    : react__WEBPACK_IMPORTED_MODULE_0__.createElement(component, properties)\n}\n\n/**\n * @param {Element|Root} node\n * @returns {Element?}\n */\nfunction getInputElement(node) {\n  let index = -1\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n\n    if (child.type === 'element' && child.tagName === 'input') {\n      return child\n    }\n  }\n\n  return null\n}\n\n/**\n * @param {Element|Root} parent\n * @param {Element} [node]\n * @returns {number}\n */\nfunction getElementsBeforeCount(parent, node) {\n  let index = -1\n  let count = 0\n\n  while (++index < parent.children.length) {\n    if (parent.children[index] === node) break\n    if (parent.children[index].type === 'element') count++\n  }\n\n  return count\n}\n\n/**\n * @param {Record<string, unknown>} props\n * @param {string} prop\n * @param {unknown} value\n * @param {Context} ctx\n */\nfunction addProperty(props, prop, value, ctx) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_6__.find)(ctx.schema, prop)\n  let result = value\n\n  // Ignore nullish and `NaN` values.\n  // eslint-disable-next-line no-self-compare\n  if (result === null || result === undefined || result !== result) {\n    return\n  }\n\n  // Accept `array`.\n  // Most props are space-separated.\n  if (Array.isArray(result)) {\n    result = info.commaSeparated ? (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_7__.stringify)(result) : (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_8__.stringify)(result)\n  }\n\n  if (info.property === 'style' && typeof result === 'string') {\n    result = parseStyle(result)\n  }\n\n  if (info.space && info.property) {\n    props[\n      own.call(property_information__WEBPACK_IMPORTED_MODULE_9__.hastToReact, info.property)\n        ? property_information__WEBPACK_IMPORTED_MODULE_9__.hastToReact[info.property]\n        : info.property\n    ] = result\n  } else if (info.attribute) {\n    props[info.attribute] = result\n  }\n}\n\n/**\n * @param {string} value\n * @returns {Record<string, string>}\n */\nfunction parseStyle(value) {\n  /** @type {Record<string, string>} */\n  const result = {}\n\n  try {\n    ;(0,style_to_object__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(value, iterator)\n  } catch {\n    // Silent.\n  }\n\n  return result\n\n  /**\n   * @param {string} name\n   * @param {string} v\n   */\n  function iterator(name, v) {\n    const k = name.slice(0, 4) === '-ms-' ? `ms-${name.slice(4)}` : name\n    result[k.replace(/-([a-z])/g, styleReplacer)] = v\n  }\n}\n\n/**\n * @param {unknown} _\n * @param {string} $1\n */\nfunction styleReplacer(_, $1) {\n  return $1.toUpperCase()\n}\n\n/**\n * @param {Position|{start: {line: null, column: null, offset: null}, end: {line: null, column: null, offset: null}}} pos\n * @returns {string}\n */\nfunction flattenPosition(pos) {\n  return [\n    pos.start.line,\n    ':',\n    pos.start.column,\n    '-',\n    pos.end.line,\n    ':',\n    pos.end.column\n  ]\n    .map(String)\n    .join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/ast-to-react.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/react-markdown.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/react-markdown.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactMarkdown: () => (/* binding */ ReactMarkdown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/.pnpm/vfile@5.3.7/node_modules/vfile/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unified */ \"(ssr)/./node_modules/.pnpm/unified@10.1.2/node_modules/unified/lib/index.js\");\n/* harmony import */ var remark_parse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remark-parse */ \"(ssr)/./node_modules/.pnpm/remark-parse@10.0.2/node_modules/remark-parse/lib/index.js\");\n/* harmony import */ var remark_rehype__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remark-rehype */ \"(ssr)/./node_modules/.pnpm/remark-rehype@10.1.0/node_modules/remark-rehype/lib/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/index.js\");\n/* harmony import */ var _rehype_filter_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./rehype-filter.js */ \"(ssr)/./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/rehype-filter.js\");\n/* harmony import */ var _ast_to_react_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ast-to-react.js */ \"(ssr)/./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/ast-to-react.js\");\n/**\n * @typedef {import('react').ReactNode} ReactNode\n * @typedef {import('react').ReactElement<{}>} ReactElement\n * @typedef {import('unified').PluggableList} PluggableList\n * @typedef {import('hast').Root} Root\n * @typedef {import('./rehype-filter.js').Options} FilterOptions\n * @typedef {import('./ast-to-react.js').Options} TransformOptions\n *\n * @typedef CoreOptions\n * @property {string} children\n *\n * @typedef PluginOptions\n * @property {PluggableList} [remarkPlugins=[]]\n * @property {PluggableList} [rehypePlugins=[]]\n * @property {import('remark-rehype').Options | undefined} [remarkRehypeOptions={}]\n *\n * @typedef LayoutOptions\n * @property {string} [className]\n *\n * @typedef {CoreOptions & PluginOptions & LayoutOptions & FilterOptions & TransformOptions} ReactMarkdownOptions\n *\n * @typedef Deprecation\n * @property {string} id\n * @property {string} [to]\n */\n\n\n\n\n\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\nconst changelog =\n  'https://github.com/remarkjs/react-markdown/blob/main/changelog.md'\n\n/** @type {Record<string, Deprecation>} */\nconst deprecated = {\n  plugins: {to: 'remarkPlugins', id: 'change-plugins-to-remarkplugins'},\n  renderers: {to: 'components', id: 'change-renderers-to-components'},\n  astPlugins: {id: 'remove-buggy-html-in-markdown-parser'},\n  allowDangerousHtml: {id: 'remove-buggy-html-in-markdown-parser'},\n  escapeHtml: {id: 'remove-buggy-html-in-markdown-parser'},\n  source: {to: 'children', id: 'change-source-to-children'},\n  allowNode: {\n    to: 'allowElement',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  allowedTypes: {\n    to: 'allowedElements',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  disallowedTypes: {\n    to: 'disallowedElements',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  includeNodeIndex: {\n    to: 'includeElementIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  }\n}\n\n/**\n * React component to render markdown.\n *\n * @param {ReactMarkdownOptions} options\n * @returns {ReactElement}\n */\nfunction ReactMarkdown(options) {\n  for (const key in deprecated) {\n    if (own.call(deprecated, key) && own.call(options, key)) {\n      const deprecation = deprecated[key]\n      console.warn(\n        `[react-markdown] Warning: please ${\n          deprecation.to ? `use \\`${deprecation.to}\\` instead of` : 'remove'\n        } \\`${key}\\` (see <${changelog}#${deprecation.id}> for more info)`\n      )\n      delete deprecated[key]\n    }\n  }\n\n  const processor = (0,unified__WEBPACK_IMPORTED_MODULE_1__.unified)()\n    .use(remark_parse__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n    .use(options.remarkPlugins || [])\n    .use(remark_rehype__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n      ...options.remarkRehypeOptions,\n      allowDangerousHtml: true\n    })\n    .use(options.rehypePlugins || [])\n    .use(_rehype_filter_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"], options)\n\n  const file = new vfile__WEBPACK_IMPORTED_MODULE_5__.VFile()\n\n  if (typeof options.children === 'string') {\n    file.value = options.children\n  } else if (options.children !== undefined && options.children !== null) {\n    console.warn(\n      `[react-markdown] Warning: please pass a string as \\`children\\` (not: \\`${options.children}\\`)`\n    )\n  }\n\n  const hastNode = processor.runSync(processor.parse(file), file)\n\n  if (hastNode.type !== 'root') {\n    throw new TypeError('Expected a `root` node')\n  }\n\n  /** @type {ReactElement} */\n  let result = react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    react__WEBPACK_IMPORTED_MODULE_0__.Fragment,\n    {},\n    (0,_ast_to_react_js__WEBPACK_IMPORTED_MODULE_6__.childrenToReact)({options, schema: property_information__WEBPACK_IMPORTED_MODULE_7__.html, listDepth: 0}, hastNode)\n  )\n\n  if (options.className) {\n    result = react__WEBPACK_IMPORTED_MODULE_0__.createElement('div', {className: options.className}, result)\n  }\n\n  return result\n}\n\nReactMarkdown.propTypes = {\n  // Core options:\n  children: prop_types__WEBPACK_IMPORTED_MODULE_8__.string,\n  // Layout options:\n  className: prop_types__WEBPACK_IMPORTED_MODULE_8__.string,\n  // Filter options:\n  allowElement: prop_types__WEBPACK_IMPORTED_MODULE_8__.func,\n  allowedElements: prop_types__WEBPACK_IMPORTED_MODULE_8__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_8__.string),\n  disallowedElements: prop_types__WEBPACK_IMPORTED_MODULE_8__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_8__.string),\n  unwrapDisallowed: prop_types__WEBPACK_IMPORTED_MODULE_8__.bool,\n  // Plugin options:\n  remarkPlugins: prop_types__WEBPACK_IMPORTED_MODULE_8__.arrayOf(\n    prop_types__WEBPACK_IMPORTED_MODULE_8__.oneOfType([\n      prop_types__WEBPACK_IMPORTED_MODULE_8__.object,\n      prop_types__WEBPACK_IMPORTED_MODULE_8__.func,\n      prop_types__WEBPACK_IMPORTED_MODULE_8__.arrayOf(\n        prop_types__WEBPACK_IMPORTED_MODULE_8__.oneOfType([\n          prop_types__WEBPACK_IMPORTED_MODULE_8__.bool,\n          prop_types__WEBPACK_IMPORTED_MODULE_8__.string,\n          prop_types__WEBPACK_IMPORTED_MODULE_8__.object,\n          prop_types__WEBPACK_IMPORTED_MODULE_8__.func,\n          prop_types__WEBPACK_IMPORTED_MODULE_8__.arrayOf(\n            // prettier-ignore\n            // type-coverage:ignore-next-line\n            prop_types__WEBPACK_IMPORTED_MODULE_8__.any\n          )\n        ])\n      )\n    ])\n  ),\n  rehypePlugins: prop_types__WEBPACK_IMPORTED_MODULE_8__.arrayOf(\n    prop_types__WEBPACK_IMPORTED_MODULE_8__.oneOfType([\n      prop_types__WEBPACK_IMPORTED_MODULE_8__.object,\n      prop_types__WEBPACK_IMPORTED_MODULE_8__.func,\n      prop_types__WEBPACK_IMPORTED_MODULE_8__.arrayOf(\n        prop_types__WEBPACK_IMPORTED_MODULE_8__.oneOfType([\n          prop_types__WEBPACK_IMPORTED_MODULE_8__.bool,\n          prop_types__WEBPACK_IMPORTED_MODULE_8__.string,\n          prop_types__WEBPACK_IMPORTED_MODULE_8__.object,\n          prop_types__WEBPACK_IMPORTED_MODULE_8__.func,\n          prop_types__WEBPACK_IMPORTED_MODULE_8__.arrayOf(\n            // prettier-ignore\n            // type-coverage:ignore-next-line\n            prop_types__WEBPACK_IMPORTED_MODULE_8__.any\n          )\n        ])\n      )\n    ])\n  ),\n  // Transform options:\n  sourcePos: prop_types__WEBPACK_IMPORTED_MODULE_8__.bool,\n  rawSourcePos: prop_types__WEBPACK_IMPORTED_MODULE_8__.bool,\n  skipHtml: prop_types__WEBPACK_IMPORTED_MODULE_8__.bool,\n  includeElementIndex: prop_types__WEBPACK_IMPORTED_MODULE_8__.bool,\n  transformLinkUri: prop_types__WEBPACK_IMPORTED_MODULE_8__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_8__.func, prop_types__WEBPACK_IMPORTED_MODULE_8__.bool]),\n  linkTarget: prop_types__WEBPACK_IMPORTED_MODULE_8__.oneOfType([prop_types__WEBPACK_IMPORTED_MODULE_8__.func, prop_types__WEBPACK_IMPORTED_MODULE_8__.string]),\n  transformImageUri: prop_types__WEBPACK_IMPORTED_MODULE_8__.func,\n  components: prop_types__WEBPACK_IMPORTED_MODULE_8__.object\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/react-markdown.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/rehype-filter.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/rehype-filter.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeFilter)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/.pnpm/unist-util-visit@4.1.2/node_modules/unist-util-visit/lib/index.js\");\n\n\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Element} Element\n *\n * @callback AllowElement\n * @param {Element} element\n * @param {number} index\n * @param {Element|Root} parent\n * @returns {boolean|undefined}\n *\n * @typedef Options\n * @property {Array<string>} [allowedElements]\n * @property {Array<string>} [disallowedElements=[]]\n * @property {AllowElement} [allowElement]\n * @property {boolean} [unwrapDisallowed=false]\n */\n\n/**\n * @type {import('unified').Plugin<[Options], Root>}\n */\nfunction rehypeFilter(options) {\n  if (options.allowedElements && options.disallowedElements) {\n    throw new TypeError(\n      'Only one of `allowedElements` and `disallowedElements` should be defined'\n    )\n  }\n\n  if (\n    options.allowedElements ||\n    options.disallowedElements ||\n    options.allowElement\n  ) {\n    return (tree) => {\n      (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(tree, 'element', (node, index, parent_) => {\n        const parent = /** @type {Element|Root} */ (parent_)\n        /** @type {boolean|undefined} */\n        let remove\n\n        if (options.allowedElements) {\n          remove = !options.allowedElements.includes(node.tagName)\n        } else if (options.disallowedElements) {\n          remove = options.disallowedElements.includes(node.tagName)\n        }\n\n        if (!remove && options.allowElement && typeof index === 'number') {\n          remove = !options.allowElement(node, index, parent)\n        }\n\n        if (remove && typeof index === 'number') {\n          if (options.unwrapDisallowed && node.children) {\n            parent.children.splice(index, 1, ...node.children)\n          } else {\n            parent.children.splice(index, 1)\n          }\n\n          return index\n        }\n\n        return undefined\n      })\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/rehype-filter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/uri-transformer.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/uri-transformer.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uriTransformer: () => (/* binding */ uriTransformer)\n/* harmony export */ });\nconst protocols = ['http', 'https', 'mailto', 'tel']\n\n/**\n * @param {string} uri\n * @returns {string}\n */\nfunction uriTransformer(uri) {\n  const url = (uri || '').trim()\n  const first = url.charAt(0)\n\n  if (first === '#' || first === '/') {\n    return url\n  }\n\n  const colon = url.indexOf(':')\n  if (colon === -1) {\n    return url\n  }\n\n  let index = -1\n\n  while (++index < protocols.length) {\n    const protocol = protocols[index]\n\n    if (\n      colon === protocol.length &&\n      url.slice(0, protocol.length).toLowerCase() === protocol\n    ) {\n      return url\n    }\n  }\n\n  index = url.indexOf('?')\n  if (index !== -1 && colon > index) {\n    return url\n  }\n\n  index = url.indexOf('#')\n  if (index !== -1 && colon > index) {\n    return url\n  }\n\n  // eslint-disable-next-line no-script-url\n  return 'javascript:void(0)'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/uri-transformer.js\n");

/***/ })

};
;