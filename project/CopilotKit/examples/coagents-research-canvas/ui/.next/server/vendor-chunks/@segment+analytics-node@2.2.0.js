"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@segment+analytics-node@2.2.0";
exports.ids = ["vendor-chunks/@segment+analytics-node@2.2.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/analytics-node.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/analytics-node.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* binding */ Analytics)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/bind-all.js\");\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/callback/index.js\");\n/* harmony import */ var _settings__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./settings */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/settings.js\");\n/* harmony import */ var _generated_version__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../generated/version */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/generated/version.js\");\n/* harmony import */ var _plugins_segmentio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../plugins/segmentio */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/index.js\");\n/* harmony import */ var _event_factory__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./event-factory */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/event-factory.js\");\n/* harmony import */ var _dispatch_emit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dispatch-emit */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/dispatch-emit.js\");\n/* harmony import */ var _emitter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./emitter */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/emitter.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./context */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/context.js\");\n/* harmony import */ var _event_queue__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./event-queue */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/event-queue.js\");\n/* harmony import */ var _lib_http_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../lib/http-client */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/http-client.js\");\n\n\n\n\n\n\n\n\n\n\nclass Analytics extends _emitter__WEBPACK_IMPORTED_MODULE_5__.NodeEmitter {\n    _eventFactory;\n    _isClosed = false;\n    _pendingEvents = 0;\n    _closeAndFlushDefaultTimeout;\n    _publisher;\n    _isFlushing = false;\n    _queue;\n    ready;\n    constructor(settings) {\n        super();\n        (0,_settings__WEBPACK_IMPORTED_MODULE_0__.validateSettings)(settings);\n        this._eventFactory = new _event_factory__WEBPACK_IMPORTED_MODULE_3__.NodeEventFactory();\n        this._queue = new _event_queue__WEBPACK_IMPORTED_MODULE_7__.NodeEventQueue();\n        const flushInterval = settings.flushInterval ?? 10000;\n        this._closeAndFlushDefaultTimeout = flushInterval * 1.25; // add arbitrary multiplier in case an event is in a plugin.\n        const { plugin, publisher } = (0,_plugins_segmentio__WEBPACK_IMPORTED_MODULE_2__.createConfiguredNodePlugin)({\n            writeKey: settings.writeKey,\n            host: settings.host,\n            path: settings.path,\n            maxRetries: settings.maxRetries ?? 3,\n            flushAt: settings.flushAt ?? settings.maxEventsInBatch ?? 15,\n            httpRequestTimeout: settings.httpRequestTimeout,\n            disable: settings.disable,\n            flushInterval,\n            httpClient: typeof settings.httpClient === 'function'\n                ? new _lib_http_client__WEBPACK_IMPORTED_MODULE_8__.FetchHTTPClient(settings.httpClient)\n                : settings.httpClient ?? new _lib_http_client__WEBPACK_IMPORTED_MODULE_8__.FetchHTTPClient(),\n            oauthSettings: settings.oauthSettings,\n        }, this);\n        this._publisher = publisher;\n        this.ready = this.register(plugin).then(() => undefined);\n        this.emit('initialize', settings);\n        (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_9__.bindAll)(this);\n    }\n    get VERSION() {\n        return _generated_version__WEBPACK_IMPORTED_MODULE_1__.version;\n    }\n    /**\n     * Call this method to stop collecting new events and flush all existing events.\n     * This method also waits for any event method-specific callbacks to be triggered,\n     * and any of their subsequent promises to be resolved/rejected.\n     */\n    closeAndFlush({ timeout = this._closeAndFlushDefaultTimeout, } = {}) {\n        return this.flush({ timeout, close: true });\n    }\n    /**\n     * Call this method to flush all existing events..\n     * This method also waits for any event method-specific callbacks to be triggered,\n     * and any of their subsequent promises to be resolved/rejected.\n     */\n    async flush({ timeout, close = false, } = {}) {\n        if (this._isFlushing) {\n            // if we're already flushing, then we don't need to do anything\n            console.warn('Overlapping flush calls detected. Please wait for the previous flush to finish before calling .flush again');\n            return;\n        }\n        else {\n            this._isFlushing = true;\n        }\n        if (close) {\n            this._isClosed = true;\n        }\n        this._publisher.flush(this._pendingEvents);\n        const promise = new Promise((resolve) => {\n            if (!this._pendingEvents) {\n                resolve();\n            }\n            else {\n                this.once('drained', () => {\n                    resolve();\n                });\n            }\n        }).finally(() => {\n            this._isFlushing = false;\n        });\n        return timeout ? (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_10__.pTimeout)(promise, timeout).catch(() => undefined) : promise;\n    }\n    _dispatch(segmentEvent, callback) {\n        if (this._isClosed) {\n            this.emit('call_after_close', segmentEvent);\n            return undefined;\n        }\n        this._pendingEvents++;\n        (0,_dispatch_emit__WEBPACK_IMPORTED_MODULE_4__.dispatchAndEmit)(segmentEvent, this._queue, this, callback)\n            .catch((ctx) => ctx)\n            .finally(() => {\n            this._pendingEvents--;\n            if (!this._pendingEvents) {\n                this.emit('drained');\n            }\n        });\n    }\n    /**\n     * Combines two unassociated user identities.\n     * @link https://segment.com/docs/connections/sources/catalog/libraries/server/node/#alias\n     */\n    alias({ userId, previousId, context, timestamp, integrations, messageId, }, callback) {\n        const segmentEvent = this._eventFactory.alias(userId, previousId, {\n            context,\n            integrations,\n            timestamp,\n            messageId,\n        });\n        this._dispatch(segmentEvent, callback);\n    }\n    /**\n     * Associates an identified user with a collective.\n     *  @link https://segment.com/docs/connections/sources/catalog/libraries/server/node/#group\n     */\n    group({ timestamp, groupId, userId, anonymousId, traits = {}, context, integrations, messageId, }, callback) {\n        const segmentEvent = this._eventFactory.group(groupId, traits, {\n            context,\n            anonymousId,\n            userId,\n            timestamp,\n            integrations,\n            messageId,\n        });\n        this._dispatch(segmentEvent, callback);\n    }\n    /**\n     * Includes a unique userId and (maybe anonymousId) and any optional traits you know about them.\n     * @link https://segment.com/docs/connections/sources/catalog/libraries/server/node/#identify\n     */\n    identify({ userId, anonymousId, traits = {}, context, timestamp, integrations, messageId, }, callback) {\n        const segmentEvent = this._eventFactory.identify(userId, traits, {\n            context,\n            anonymousId,\n            userId,\n            timestamp,\n            integrations,\n            messageId,\n        });\n        this._dispatch(segmentEvent, callback);\n    }\n    /**\n     * The page method lets you record page views on your website, along with optional extra information about the page being viewed.\n     * @link https://segment.com/docs/connections/sources/catalog/libraries/server/node/#page\n     */\n    page({ userId, anonymousId, category, name, properties, context, timestamp, integrations, messageId, }, callback) {\n        const segmentEvent = this._eventFactory.page(category ?? null, name ?? null, properties, { context, anonymousId, userId, timestamp, integrations, messageId });\n        this._dispatch(segmentEvent, callback);\n    }\n    /**\n     * Records screen views on your app, along with optional extra information\n     * about the screen viewed by the user.\n     *\n     * TODO: This is not documented on the segment docs ATM (for node).\n     */\n    screen({ userId, anonymousId, category, name, properties, context, timestamp, integrations, messageId, }, callback) {\n        const segmentEvent = this._eventFactory.screen(category ?? null, name ?? null, properties, { context, anonymousId, userId, timestamp, integrations, messageId });\n        this._dispatch(segmentEvent, callback);\n    }\n    /**\n     * Records actions your users perform.\n     * @link https://segment.com/docs/connections/sources/catalog/libraries/server/node/#track\n     */\n    track({ userId, anonymousId, event, properties, context, timestamp, integrations, messageId, }, callback) {\n        const segmentEvent = this._eventFactory.track(event, properties, {\n            context,\n            userId,\n            anonymousId,\n            timestamp,\n            integrations,\n            messageId,\n        });\n        this._dispatch(segmentEvent, callback);\n    }\n    /**\n     * Registers one or more plugins to augment Analytics functionality.\n     * @param plugins\n     */\n    register(...plugins) {\n        return this._queue.criticalTasks.run(async () => {\n            const ctx = _context__WEBPACK_IMPORTED_MODULE_6__.Context.system();\n            const registrations = plugins.map((xt) => this._queue.register(ctx, xt, this));\n            await Promise.all(registrations);\n            this.emit('register', plugins.map((el) => el.name));\n        });\n    }\n    /**\n     * Deregisters one or more plugins based on their names.\n     * @param pluginNames - The names of one or more plugins to deregister.\n     */\n    async deregister(...pluginNames) {\n        const ctx = _context__WEBPACK_IMPORTED_MODULE_6__.Context.system();\n        const deregistrations = pluginNames.map((pl) => {\n            const plugin = this._queue.plugins.find((p) => p.name === pl);\n            if (plugin) {\n                return this._queue.deregister(ctx, plugin, this);\n            }\n            else {\n                ctx.log('warn', `plugin ${pl} not found`);\n            }\n        });\n        await Promise.all(deregistrations);\n        this.emit('deregister', pluginNames);\n    }\n}\n//# sourceMappingURL=analytics-node.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/analytics-node.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/context.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/context.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Context: () => (/* binding */ Context)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/context/index.js\");\n// create a derived class since we may want to add node specific things to Context later\n\n// While this is not a type, it is a definition\nclass Context extends _segment_analytics_core__WEBPACK_IMPORTED_MODULE_0__.CoreContext {\n    static system() {\n        return new this({ type: 'track', event: 'system' });\n    }\n}\n//# sourceMappingURL=context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2FwcC9jb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDc0Q7QUFDdEQ7QUFDTyxzQkFBc0IsZ0VBQVc7QUFDeEM7QUFDQSwwQkFBMEIsZ0NBQWdDO0FBQzFEO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzZWdtZW50K2FuYWx5dGljcy1ub2RlQDIuMi4wL25vZGVfbW9kdWxlcy9Ac2VnbWVudC9hbmFseXRpY3Mtbm9kZS9kaXN0L2VzbS9hcHAvY29udGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBjcmVhdGUgYSBkZXJpdmVkIGNsYXNzIHNpbmNlIHdlIG1heSB3YW50IHRvIGFkZCBub2RlIHNwZWNpZmljIHRoaW5ncyB0byBDb250ZXh0IGxhdGVyXG5pbXBvcnQgeyBDb3JlQ29udGV4dCB9IGZyb20gJ0BzZWdtZW50L2FuYWx5dGljcy1jb3JlJztcbi8vIFdoaWxlIHRoaXMgaXMgbm90IGEgdHlwZSwgaXQgaXMgYSBkZWZpbml0aW9uXG5leHBvcnQgY2xhc3MgQ29udGV4dCBleHRlbmRzIENvcmVDb250ZXh0IHtcbiAgICBzdGF0aWMgc3lzdGVtKCkge1xuICAgICAgICByZXR1cm4gbmV3IHRoaXMoeyB0eXBlOiAndHJhY2snLCBldmVudDogJ3N5c3RlbScgfSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29udGV4dC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/context.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/dispatch-emit.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/dispatch-emit.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dispatchAndEmit: () => (/* binding */ dispatchAndEmit)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/analytics/dispatch.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./context */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/context.js\");\n\n\nconst normalizeDispatchCb = (cb) => (ctx) => {\n    const failedDelivery = ctx.failedDelivery();\n    return failedDelivery ? cb(failedDelivery.reason, ctx) : cb(undefined, ctx);\n};\n/* Dispatch function, but swallow promise rejections and use event emitter instead */\nconst dispatchAndEmit = async (event, queue, emitter, callback) => {\n    try {\n        const context = new _context__WEBPACK_IMPORTED_MODULE_0__.Context(event);\n        const ctx = await (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_1__.dispatch)(context, queue, emitter, {\n            ...(callback ? { callback: normalizeDispatchCb(callback) } : {}),\n        });\n        const failedDelivery = ctx.failedDelivery();\n        if (failedDelivery) {\n            emitter.emit('error', {\n                code: 'delivery_failure',\n                reason: failedDelivery.reason,\n                ctx: ctx,\n            });\n        }\n        else {\n            emitter.emit(event.type, ctx);\n        }\n    }\n    catch (err) {\n        emitter.emit('error', {\n            code: 'unknown',\n            reason: err,\n        });\n    }\n};\n//# sourceMappingURL=dispatch-emit.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/dispatch-emit.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/emitter.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/emitter.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NodeEmitter: () => (/* binding */ NodeEmitter)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @segment/analytics-generic-utils */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-generic-utils@1.2.0/node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js\");\n\nclass NodeEmitter extends _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_0__.Emitter {\n}\n//# sourceMappingURL=emitter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2FwcC9lbWl0dGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJEO0FBQ3BELDBCQUEwQixxRUFBTztBQUN4QztBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2FwcC9lbWl0dGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEVtaXR0ZXIgfSBmcm9tICdAc2VnbWVudC9hbmFseXRpY3MtZ2VuZXJpYy11dGlscyc7XG5leHBvcnQgY2xhc3MgTm9kZUVtaXR0ZXIgZXh0ZW5kcyBFbWl0dGVyIHtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWVtaXR0ZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/emitter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/event-factory.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/event-factory.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NodeEventFactory: () => (/* binding */ NodeEventFactory)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/events/index.js\");\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/assertions.js\");\n/* harmony import */ var _lib_get_message_id__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/get-message-id */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/get-message-id.js\");\n\n\nclass NodeEventFactory extends _segment_analytics_core__WEBPACK_IMPORTED_MODULE_1__.CoreEventFactory {\n    constructor() {\n        super({\n            createMessageId: _lib_get_message_id__WEBPACK_IMPORTED_MODULE_0__.createMessageId,\n            onFinishedEvent: (event) => {\n                (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_2__.assertUserIdentity)(event);\n            },\n        });\n    }\n}\n//# sourceMappingURL=event-factory.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2FwcC9ldmVudC1mYWN0b3J5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0U7QUFDdkI7QUFDakQsK0JBQStCLHFFQUFnQjtBQUN0RDtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0EsZ0JBQWdCLDJFQUFrQjtBQUNsQyxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzZWdtZW50K2FuYWx5dGljcy1ub2RlQDIuMi4wL25vZGVfbW9kdWxlcy9Ac2VnbWVudC9hbmFseXRpY3Mtbm9kZS9kaXN0L2VzbS9hcHAvZXZlbnQtZmFjdG9yeS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBhc3NlcnRVc2VySWRlbnRpdHksIENvcmVFdmVudEZhY3RvcnkgfSBmcm9tICdAc2VnbWVudC9hbmFseXRpY3MtY29yZSc7XG5pbXBvcnQgeyBjcmVhdGVNZXNzYWdlSWQgfSBmcm9tICcuLi9saWIvZ2V0LW1lc3NhZ2UtaWQnO1xuZXhwb3J0IGNsYXNzIE5vZGVFdmVudEZhY3RvcnkgZXh0ZW5kcyBDb3JlRXZlbnRGYWN0b3J5IHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoe1xuICAgICAgICAgICAgY3JlYXRlTWVzc2FnZUlkLFxuICAgICAgICAgICAgb25GaW5pc2hlZEV2ZW50OiAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgICAgICBhc3NlcnRVc2VySWRlbnRpdHkoZXZlbnQpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXZlbnQtZmFjdG9yeS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/event-factory.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/event-queue.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/event-queue.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NodeEventQueue: () => (/* binding */ NodeEventQueue)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/priority-queue/index.js\");\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/queue/event-queue.js\");\n\nclass NodePriorityQueue extends _segment_analytics_core__WEBPACK_IMPORTED_MODULE_0__.PriorityQueue {\n    constructor() {\n        super(1, []);\n    }\n    // do not use an internal \"seen\" map\n    getAttempts(ctx) {\n        return ctx.attempts ?? 0;\n    }\n    updateAttempts(ctx) {\n        ctx.attempts = this.getAttempts(ctx) + 1;\n        return this.getAttempts(ctx);\n    }\n}\nclass NodeEventQueue extends _segment_analytics_core__WEBPACK_IMPORTED_MODULE_1__.CoreEventQueue {\n    constructor() {\n        super(new NodePriorityQueue());\n    }\n}\n//# sourceMappingURL=event-queue.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2FwcC9ldmVudC1xdWV1ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBd0U7QUFDeEUsZ0NBQWdDLGtFQUFhO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLDZCQUE2QixtRUFBYztBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2FwcC9ldmVudC1xdWV1ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDb3JlRXZlbnRRdWV1ZSwgUHJpb3JpdHlRdWV1ZSB9IGZyb20gJ0BzZWdtZW50L2FuYWx5dGljcy1jb3JlJztcbmNsYXNzIE5vZGVQcmlvcml0eVF1ZXVlIGV4dGVuZHMgUHJpb3JpdHlRdWV1ZSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKDEsIFtdKTtcbiAgICB9XG4gICAgLy8gZG8gbm90IHVzZSBhbiBpbnRlcm5hbCBcInNlZW5cIiBtYXBcbiAgICBnZXRBdHRlbXB0cyhjdHgpIHtcbiAgICAgICAgcmV0dXJuIGN0eC5hdHRlbXB0cyA/PyAwO1xuICAgIH1cbiAgICB1cGRhdGVBdHRlbXB0cyhjdHgpIHtcbiAgICAgICAgY3R4LmF0dGVtcHRzID0gdGhpcy5nZXRBdHRlbXB0cyhjdHgpICsgMTtcbiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0QXR0ZW1wdHMoY3R4KTtcbiAgICB9XG59XG5leHBvcnQgY2xhc3MgTm9kZUV2ZW50UXVldWUgZXh0ZW5kcyBDb3JlRXZlbnRRdWV1ZSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKG5ldyBOb2RlUHJpb3JpdHlRdWV1ZSgpKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ldmVudC1xdWV1ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/event-queue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/settings.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/settings.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateSettings: () => (/* binding */ validateSettings)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/errors.js\");\n\nconst validateSettings = (settings) => {\n    if (!settings.writeKey) {\n        throw new _segment_analytics_core__WEBPACK_IMPORTED_MODULE_0__.ValidationError('writeKey', 'writeKey is missing.');\n    }\n};\n//# sourceMappingURL=settings.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2FwcC9zZXR0aW5ncy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwRDtBQUNuRDtBQUNQO0FBQ0Esa0JBQWtCLG9FQUFlO0FBQ2pDO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzZWdtZW50K2FuYWx5dGljcy1ub2RlQDIuMi4wL25vZGVfbW9kdWxlcy9Ac2VnbWVudC9hbmFseXRpY3Mtbm9kZS9kaXN0L2VzbS9hcHAvc2V0dGluZ3MuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVmFsaWRhdGlvbkVycm9yIH0gZnJvbSAnQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUnO1xuZXhwb3J0IGNvbnN0IHZhbGlkYXRlU2V0dGluZ3MgPSAoc2V0dGluZ3MpID0+IHtcbiAgICBpZiAoIXNldHRpbmdzLndyaXRlS2V5KSB7XG4gICAgICAgIHRocm93IG5ldyBWYWxpZGF0aW9uRXJyb3IoJ3dyaXRlS2V5JywgJ3dyaXRlS2V5IGlzIG1pc3NpbmcuJyk7XG4gICAgfVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNldHRpbmdzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/settings.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/generated/version.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/generated/version.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n// This file is generated.\nconst version = '2.2.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2dlbmVyYXRlZC92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1AiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac2VnbWVudCthbmFseXRpY3Mtbm9kZUAyLjIuMC9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vZ2VuZXJhdGVkL3ZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVGhpcyBmaWxlIGlzIGdlbmVyYXRlZC5cbmV4cG9ydCBjb25zdCB2ZXJzaW9uID0gJzIuMi4wJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/generated/version.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/index.common.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/index.common.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* reexport safe */ _app_analytics_node__WEBPACK_IMPORTED_MODULE_0__.Analytics),\n/* harmony export */   Context: () => (/* reexport safe */ _app_context__WEBPACK_IMPORTED_MODULE_1__.Context),\n/* harmony export */   FetchHTTPClient: () => (/* reexport safe */ _lib_http_client__WEBPACK_IMPORTED_MODULE_2__.FetchHTTPClient)\n/* harmony export */ });\n/* harmony import */ var _app_analytics_node__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/analytics-node */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/analytics-node.js\");\n/* harmony import */ var _app_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./app/context */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/app/context.js\");\n/* harmony import */ var _lib_http_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/http-client */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/http-client.js\");\n\n\n\n//# sourceMappingURL=index.common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2luZGV4LmNvbW1vbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBaUQ7QUFDVDtBQUNhO0FBQ3JEIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2luZGV4LmNvbW1vbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBBbmFseXRpY3MgfSBmcm9tICcuL2FwcC9hbmFseXRpY3Mtbm9kZSc7XG5leHBvcnQgeyBDb250ZXh0IH0gZnJvbSAnLi9hcHAvY29udGV4dCc7XG5leHBvcnQgeyBGZXRjaEhUVFBDbGllbnQsIH0gZnJvbSAnLi9saWIvaHR0cC1jbGllbnQnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguY29tbW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/index.common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* reexport safe */ _index_common__WEBPACK_IMPORTED_MODULE_0__.Analytics),\n/* harmony export */   Context: () => (/* reexport safe */ _index_common__WEBPACK_IMPORTED_MODULE_0__.Context),\n/* harmony export */   FetchHTTPClient: () => (/* reexport safe */ _index_common__WEBPACK_IMPORTED_MODULE_0__.FetchHTTPClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _index_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.common */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/index.common.js\");\n\n// export Analytics as both a named export and a default export (for backwards-compat. reasons)\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_index_common__WEBPACK_IMPORTED_MODULE_0__.Analytics);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQStCO0FBQy9CO0FBQzJDO0FBQzNDLGlFQUFlLG9EQUFTLEVBQUM7QUFDekIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac2VnbWVudCthbmFseXRpY3Mtbm9kZUAyLjIuMC9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9pbmRleC5jb21tb24nO1xuLy8gZXhwb3J0IEFuYWx5dGljcyBhcyBib3RoIGEgbmFtZWQgZXhwb3J0IGFuZCBhIGRlZmF1bHQgZXhwb3J0IChmb3IgYmFja3dhcmRzLWNvbXBhdC4gcmVhc29ucylcbmltcG9ydCB7IEFuYWx5dGljcyB9IGZyb20gJy4vaW5kZXguY29tbW9uJztcbmV4cG9ydCBkZWZhdWx0IEFuYWx5dGljcztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/abort.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/abort.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortController: () => (/* binding */ AbortController),\n/* harmony export */   AbortSignal: () => (/* binding */ AbortSignal),\n/* harmony export */   abortSignalAfterTimeout: () => (/* binding */ abortSignalAfterTimeout)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @segment/analytics-generic-utils */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-generic-utils@1.2.0/node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js\");\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./env */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/env.js\");\n/**\n * use non-native event emitter for the benefit of non-node runtimes like CF workers.\n */\n\n\n/**\n * adapted from: https://www.npmjs.com/package/node-abort-controller\n */\nclass AbortSignal {\n    onabort = null;\n    aborted = false;\n    eventEmitter = new _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_1__.Emitter();\n    toString() {\n        return '[object AbortSignal]';\n    }\n    get [Symbol.toStringTag]() {\n        return 'AbortSignal';\n    }\n    removeEventListener(...args) {\n        this.eventEmitter.off(...args);\n    }\n    addEventListener(...args) {\n        this.eventEmitter.on(...args);\n    }\n    dispatchEvent(type) {\n        const event = { type, target: this };\n        const handlerName = `on${type}`;\n        if (typeof this[handlerName] === 'function') {\n            ;\n            this[handlerName](event);\n        }\n        this.eventEmitter.emit(type, event);\n    }\n}\n/**\n * This polyfill is only neccessary to support versions of node < 14.17.\n * Can be removed once node 14 support is dropped.\n */\nclass AbortController {\n    signal = new AbortSignal();\n    abort() {\n        if (this.signal.aborted)\n            return;\n        this.signal.aborted = true;\n        this.signal.dispatchEvent('abort');\n    }\n    toString() {\n        return '[object AbortController]';\n    }\n    get [Symbol.toStringTag]() {\n        return 'AbortController';\n    }\n}\n/**\n * @param timeoutMs - Set a request timeout, after which the request is cancelled.\n */\nconst abortSignalAfterTimeout = (timeoutMs) => {\n    if ((0,_env__WEBPACK_IMPORTED_MODULE_0__.detectRuntime)() === 'cloudflare-worker') {\n        return []; // TODO: this is broken in cloudflare workers, otherwise results in \"A hanging Promise was canceled...\" error.\n    }\n    const ac = new (globalThis.AbortController || AbortController)();\n    const timeoutId = setTimeout(() => {\n        ac.abort();\n    }, timeoutMs);\n    // Allow Node.js processes to exit early if only the timeout is running\n    timeoutId?.unref?.();\n    return [ac.signal, timeoutId];\n};\n//# sourceMappingURL=abort.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/abort.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/create-url.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/create-url.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tryCreateFormattedUrl: () => (/* binding */ tryCreateFormattedUrl)\n/* harmony export */ });\nconst stripTrailingSlash = (str) => str.replace(/\\/$/, '');\n/**\n *\n * @param host e.g. \"http://foo.com\"\n * @param path e.g. \"/bar\"\n * @returns \"e.g.\" \"http://foo.com/bar\"\n */\nconst tryCreateFormattedUrl = (host, path) => {\n    return stripTrailingSlash(new URL(path || '', host).href);\n};\n//# sourceMappingURL=create-url.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2xpYi9jcmVhdGUtdXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2xpYi9jcmVhdGUtdXJsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHN0cmlwVHJhaWxpbmdTbGFzaCA9IChzdHIpID0+IHN0ci5yZXBsYWNlKC9cXC8kLywgJycpO1xuLyoqXG4gKlxuICogQHBhcmFtIGhvc3QgZS5nLiBcImh0dHA6Ly9mb28uY29tXCJcbiAqIEBwYXJhbSBwYXRoIGUuZy4gXCIvYmFyXCJcbiAqIEByZXR1cm5zIFwiZS5nLlwiIFwiaHR0cDovL2Zvby5jb20vYmFyXCJcbiAqL1xuZXhwb3J0IGNvbnN0IHRyeUNyZWF0ZUZvcm1hdHRlZFVybCA9IChob3N0LCBwYXRoKSA9PiB7XG4gICAgcmV0dXJuIHN0cmlwVHJhaWxpbmdTbGFzaChuZXcgVVJMKHBhdGggfHwgJycsIGhvc3QpLmhyZWYpO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNyZWF0ZS11cmwuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/create-url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/env.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/env.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   detectRuntime: () => (/* binding */ detectRuntime)\n/* harmony export */ });\nconst detectRuntime = () => {\n    if (typeof process === 'object' &&\n        process &&\n        typeof process.env === 'object' &&\n        process.env &&\n        typeof process.version === 'string') {\n        return 'node';\n    }\n    if (typeof window === 'object') {\n        return 'browser';\n    }\n    // @ts-ignore\n    if (typeof WebSocketPair !== 'undefined') {\n        return 'cloudflare-worker';\n    }\n    // @ts-ignore\n    if (typeof EdgeRuntime === 'string') {\n        return 'vercel-edge';\n    }\n    if (\n    // @ts-ignore\n    typeof WorkerGlobalScope !== 'undefined' &&\n        // @ts-ignore\n        typeof importScripts === 'function') {\n        return 'web-worker';\n    }\n    return 'unknown';\n};\n//# sourceMappingURL=env.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2xpYi9lbnYuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac2VnbWVudCthbmFseXRpY3Mtbm9kZUAyLjIuMC9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLW5vZGUvZGlzdC9lc20vbGliL2Vudi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgZGV0ZWN0UnVudGltZSA9ICgpID0+IHtcbiAgICBpZiAodHlwZW9mIHByb2Nlc3MgPT09ICdvYmplY3QnICYmXG4gICAgICAgIHByb2Nlc3MgJiZcbiAgICAgICAgdHlwZW9mIHByb2Nlc3MuZW52ID09PSAnb2JqZWN0JyAmJlxuICAgICAgICBwcm9jZXNzLmVudiAmJlxuICAgICAgICB0eXBlb2YgcHJvY2Vzcy52ZXJzaW9uID09PSAnc3RyaW5nJykge1xuICAgICAgICByZXR1cm4gJ25vZGUnO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgcmV0dXJuICdicm93c2VyJztcbiAgICB9XG4gICAgLy8gQHRzLWlnbm9yZVxuICAgIGlmICh0eXBlb2YgV2ViU29ja2V0UGFpciAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgcmV0dXJuICdjbG91ZGZsYXJlLXdvcmtlcic7XG4gICAgfVxuICAgIC8vIEB0cy1pZ25vcmVcbiAgICBpZiAodHlwZW9mIEVkZ2VSdW50aW1lID09PSAnc3RyaW5nJykge1xuICAgICAgICByZXR1cm4gJ3ZlcmNlbC1lZGdlJztcbiAgICB9XG4gICAgaWYgKFxuICAgIC8vIEB0cy1pZ25vcmVcbiAgICB0eXBlb2YgV29ya2VyR2xvYmFsU2NvcGUgIT09ICd1bmRlZmluZWQnICYmXG4gICAgICAgIC8vIEB0cy1pZ25vcmVcbiAgICAgICAgdHlwZW9mIGltcG9ydFNjcmlwdHMgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgcmV0dXJuICd3ZWItd29ya2VyJztcbiAgICB9XG4gICAgcmV0dXJuICd1bmtub3duJztcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1lbnYuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/env.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/fetch.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/fetch.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetch: () => (/* binding */ fetch)\n/* harmony export */ });\nconst fetch = async (...args) => {\n    if (globalThis.fetch) {\n        return globalThis.fetch(...args);\n    }\n    // This guard causes is important, as it causes dead-code elimination to be enabled inside this block.\n    // @ts-ignore\n    else if (typeof EdgeRuntime !== 'string') {\n        return (await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! node-fetch */ \"(rsc)/./node_modules/.pnpm/node-fetch@2.7.0/node_modules/node-fetch/lib/index.mjs\"))).default(...args);\n    }\n    else {\n        throw new Error('Invariant: an edge runtime that does not support fetch should not exist');\n    }\n};\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2xpYi9mZXRjaC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsMkxBQW9CO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzZWdtZW50K2FuYWx5dGljcy1ub2RlQDIuMi4wL25vZGVfbW9kdWxlcy9Ac2VnbWVudC9hbmFseXRpY3Mtbm9kZS9kaXN0L2VzbS9saWIvZmV0Y2guanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGZldGNoID0gYXN5bmMgKC4uLmFyZ3MpID0+IHtcbiAgICBpZiAoZ2xvYmFsVGhpcy5mZXRjaCkge1xuICAgICAgICByZXR1cm4gZ2xvYmFsVGhpcy5mZXRjaCguLi5hcmdzKTtcbiAgICB9XG4gICAgLy8gVGhpcyBndWFyZCBjYXVzZXMgaXMgaW1wb3J0YW50LCBhcyBpdCBjYXVzZXMgZGVhZC1jb2RlIGVsaW1pbmF0aW9uIHRvIGJlIGVuYWJsZWQgaW5zaWRlIHRoaXMgYmxvY2suXG4gICAgLy8gQHRzLWlnbm9yZVxuICAgIGVsc2UgaWYgKHR5cGVvZiBFZGdlUnVudGltZSAhPT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmV0dXJuIChhd2FpdCBpbXBvcnQoJ25vZGUtZmV0Y2gnKSkuZGVmYXVsdCguLi5hcmdzKTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YXJpYW50OiBhbiBlZGdlIHJ1bnRpbWUgdGhhdCBkb2VzIG5vdCBzdXBwb3J0IGZldGNoIHNob3VsZCBub3QgZXhpc3QnKTtcbiAgICB9XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZmV0Y2guanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/fetch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/get-message-id.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/get-message-id.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMessageId: () => (/* binding */ createMessageId)\n/* harmony export */ });\n/* harmony import */ var _uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./uuid */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/uuid.js\");\n\n/**\n * get a unique messageId with a very low chance of collisions\n * using @lukeed/uuid/secure uses the node crypto module, which is the fastest\n * @example \"node-next-1668208232027-743be593-7789-4b74-8078-cbcc8894c586\"\n */\nconst createMessageId = () => {\n    return `node-next-${Date.now()}-${(0,_uuid__WEBPACK_IMPORTED_MODULE_0__.uuid)()}`;\n};\n//# sourceMappingURL=get-message-id.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2xpYi9nZXQtbWVzc2FnZS1pZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4QjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCx3QkFBd0IsV0FBVyxHQUFHLDJDQUFJLEdBQUc7QUFDN0M7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzZWdtZW50K2FuYWx5dGljcy1ub2RlQDIuMi4wL25vZGVfbW9kdWxlcy9Ac2VnbWVudC9hbmFseXRpY3Mtbm9kZS9kaXN0L2VzbS9saWIvZ2V0LW1lc3NhZ2UtaWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXVpZCB9IGZyb20gJy4vdXVpZCc7XG4vKipcbiAqIGdldCBhIHVuaXF1ZSBtZXNzYWdlSWQgd2l0aCBhIHZlcnkgbG93IGNoYW5jZSBvZiBjb2xsaXNpb25zXG4gKiB1c2luZyBAbHVrZWVkL3V1aWQvc2VjdXJlIHVzZXMgdGhlIG5vZGUgY3J5cHRvIG1vZHVsZSwgd2hpY2ggaXMgdGhlIGZhc3Rlc3RcbiAqIEBleGFtcGxlIFwibm9kZS1uZXh0LTE2NjgyMDgyMzIwMjctNzQzYmU1OTMtNzc4OS00Yjc0LTgwNzgtY2JjYzg4OTRjNTg2XCJcbiAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZU1lc3NhZ2VJZCA9ICgpID0+IHtcbiAgICByZXR1cm4gYG5vZGUtbmV4dC0ke0RhdGUubm93KCl9LSR7dXVpZCgpfWA7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Z2V0LW1lc3NhZ2UtaWQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/get-message-id.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/http-client.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/http-client.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FetchHTTPClient: () => (/* binding */ FetchHTTPClient)\n/* harmony export */ });\n/* harmony import */ var _abort__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./abort */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/abort.js\");\n/* harmony import */ var _fetch__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./fetch */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/fetch.js\");\n\n\n/**\n * Default HTTP client implementation using fetch\n */\nclass FetchHTTPClient {\n    _fetch;\n    constructor(fetchFn) {\n        this._fetch = fetchFn ?? _fetch__WEBPACK_IMPORTED_MODULE_1__.fetch;\n    }\n    async makeRequest(options) {\n        const [signal, timeoutId] = (0,_abort__WEBPACK_IMPORTED_MODULE_0__.abortSignalAfterTimeout)(options.httpRequestTimeout);\n        const requestInit = {\n            url: options.url,\n            method: options.method,\n            headers: options.headers,\n            body: options.body,\n            signal: signal,\n        };\n        return this._fetch(options.url, requestInit).finally(() => clearTimeout(timeoutId));\n    }\n}\n//# sourceMappingURL=http-client.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2xpYi9odHRwLWNsaWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0Q7QUFDRjtBQUNoRDtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxpQ0FBaUMseUNBQVk7QUFDN0M7QUFDQTtBQUNBLG9DQUFvQywrREFBdUI7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzZWdtZW50K2FuYWx5dGljcy1ub2RlQDIuMi4wL25vZGVfbW9kdWxlcy9Ac2VnbWVudC9hbmFseXRpY3Mtbm9kZS9kaXN0L2VzbS9saWIvaHR0cC1jbGllbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYWJvcnRTaWduYWxBZnRlclRpbWVvdXQgfSBmcm9tICcuL2Fib3J0JztcbmltcG9ydCB7IGZldGNoIGFzIGRlZmF1bHRGZXRjaCB9IGZyb20gJy4vZmV0Y2gnO1xuLyoqXG4gKiBEZWZhdWx0IEhUVFAgY2xpZW50IGltcGxlbWVudGF0aW9uIHVzaW5nIGZldGNoXG4gKi9cbmV4cG9ydCBjbGFzcyBGZXRjaEhUVFBDbGllbnQge1xuICAgIF9mZXRjaDtcbiAgICBjb25zdHJ1Y3RvcihmZXRjaEZuKSB7XG4gICAgICAgIHRoaXMuX2ZldGNoID0gZmV0Y2hGbiA/PyBkZWZhdWx0RmV0Y2g7XG4gICAgfVxuICAgIGFzeW5jIG1ha2VSZXF1ZXN0KG9wdGlvbnMpIHtcbiAgICAgICAgY29uc3QgW3NpZ25hbCwgdGltZW91dElkXSA9IGFib3J0U2lnbmFsQWZ0ZXJUaW1lb3V0KG9wdGlvbnMuaHR0cFJlcXVlc3RUaW1lb3V0KTtcbiAgICAgICAgY29uc3QgcmVxdWVzdEluaXQgPSB7XG4gICAgICAgICAgICB1cmw6IG9wdGlvbnMudXJsLFxuICAgICAgICAgICAgbWV0aG9kOiBvcHRpb25zLm1ldGhvZCxcbiAgICAgICAgICAgIGhlYWRlcnM6IG9wdGlvbnMuaGVhZGVycyxcbiAgICAgICAgICAgIGJvZHk6IG9wdGlvbnMuYm9keSxcbiAgICAgICAgICAgIHNpZ25hbDogc2lnbmFsLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5fZmV0Y2gob3B0aW9ucy51cmwsIHJlcXVlc3RJbml0KS5maW5hbGx5KCgpID0+IGNsZWFyVGltZW91dCh0aW1lb3V0SWQpKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1odHRwLWNsaWVudC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/http-client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/token-manager.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/token-manager.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenManager: () => (/* binding */ TokenManager)\n/* harmony export */ });\n/* harmony import */ var _uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./uuid */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/uuid.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/.pnpm/jose@5.9.3/node_modules/jose/dist/node/esm/key/import.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/.pnpm/jose@5.9.3/node_modules/jose/dist/node/esm/jwt/sign.js\");\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/priority-queue/backoff.js\");\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/callback/index.js\");\n/* harmony import */ var _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @segment/analytics-generic-utils */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-generic-utils@1.2.0/node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js\");\n\n\n\n\nconst isAccessToken = (thing) => {\n    return Boolean(thing &&\n        typeof thing === 'object' &&\n        'access_token' in thing &&\n        'expires_in' in thing &&\n        typeof thing.access_token === 'string' &&\n        typeof thing.expires_in === 'number');\n};\nconst isValidCustomResponse = (response) => {\n    return typeof response.text === 'function';\n};\nfunction convertHeaders(headers) {\n    const lowercaseHeaders = {};\n    if (!headers)\n        return {};\n    if (isHeaders(headers)) {\n        for (const [name, value] of headers.entries()) {\n            lowercaseHeaders[name.toLowerCase()] = value;\n        }\n        return lowercaseHeaders;\n    }\n    for (const [name, value] of Object.entries(headers)) {\n        lowercaseHeaders[name.toLowerCase()] = value;\n    }\n    return lowercaseHeaders;\n}\nfunction isHeaders(thing) {\n    if (typeof thing === 'object' &&\n        thing !== null &&\n        'entries' in Object(thing) &&\n        typeof Object(thing).entries === 'function') {\n        return true;\n    }\n    return false;\n}\nclass TokenManager {\n    alg = 'RS256';\n    grantType = 'client_credentials';\n    clientAssertionType = 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer';\n    clientId;\n    clientKey;\n    keyId;\n    scope;\n    authServer;\n    httpClient;\n    maxRetries;\n    clockSkewInSeconds = 0;\n    accessToken;\n    tokenEmitter = new _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_1__.Emitter();\n    retryCount;\n    pollerTimer;\n    constructor(props) {\n        this.keyId = props.keyId;\n        this.clientId = props.clientId;\n        this.clientKey = props.clientKey;\n        this.authServer = props.authServer ?? 'https://oauth2.segment.io';\n        this.scope = props.scope ?? 'tracking_api:write';\n        this.httpClient = props.httpClient;\n        this.maxRetries = props.maxRetries;\n        this.tokenEmitter.on('access_token', (event) => {\n            if ('token' in event) {\n                this.accessToken = event.token;\n            }\n        });\n        this.retryCount = 0;\n    }\n    stopPoller() {\n        clearTimeout(this.pollerTimer);\n    }\n    async pollerLoop() {\n        let timeUntilRefreshInMs = 25;\n        let response;\n        try {\n            response = await this.requestAccessToken();\n        }\n        catch (err) {\n            // Error without a status code - likely networking, retry\n            return this.handleTransientError({ error: err });\n        }\n        if (!isValidCustomResponse(response)) {\n            return this.handleInvalidCustomResponse();\n        }\n        const headers = convertHeaders(response.headers);\n        if (headers['date']) {\n            this.updateClockSkew(Date.parse(headers['date']));\n        }\n        // Handle status codes!\n        if (response.status === 200) {\n            try {\n                const body = await response.text();\n                const token = JSON.parse(body);\n                if (!isAccessToken(token)) {\n                    throw new Error('Response did not contain a valid access_token and expires_in');\n                }\n                // Success, we have a token!\n                token.expires_at = Math.round(Date.now() / 1000) + token.expires_in;\n                this.tokenEmitter.emit('access_token', { token });\n                // Reset our failure count\n                this.retryCount = 0;\n                // Refresh the token after half the expiry time passes\n                timeUntilRefreshInMs = (token.expires_in / 2) * 1000;\n                return this.queueNextPoll(timeUntilRefreshInMs);\n            }\n            catch (err) {\n                // Something went really wrong with the body, lets surface an error and try again?\n                return this.handleTransientError({ error: err, forceEmitError: true });\n            }\n        }\n        else if (response.status === 429) {\n            // Rate limited, wait for the reset time\n            return await this.handleRateLimited(response, headers, timeUntilRefreshInMs);\n        }\n        else if ([400, 401, 415].includes(response.status)) {\n            // Unrecoverable errors, stops the poller\n            return this.handleUnrecoverableErrors(response);\n        }\n        else {\n            return this.handleTransientError({\n                error: new Error(`[${response.status}] ${response.statusText}`),\n            });\n        }\n    }\n    handleTransientError({ error, forceEmitError, }) {\n        this.incrementRetries({ error, forceEmitError });\n        const timeUntilRefreshInMs = (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_2__.backoff)({\n            attempt: this.retryCount,\n            minTimeout: 25,\n            maxTimeout: 1000,\n        });\n        this.queueNextPoll(timeUntilRefreshInMs);\n    }\n    handleInvalidCustomResponse() {\n        this.tokenEmitter.emit('access_token', {\n            error: new Error('HTTPClient does not implement response.text method'),\n        });\n    }\n    async handleRateLimited(response, headers, timeUntilRefreshInMs) {\n        this.incrementRetries({\n            error: new Error(`[${response.status}] ${response.statusText}`),\n        });\n        if (headers['x-ratelimit-reset']) {\n            const rateLimitResetTimestamp = parseInt(headers['x-ratelimit-reset'], 10);\n            if (isFinite(rateLimitResetTimestamp)) {\n                timeUntilRefreshInMs =\n                    rateLimitResetTimestamp - Date.now() + this.clockSkewInSeconds * 1000;\n            }\n            else {\n                timeUntilRefreshInMs = 5 * 1000;\n            }\n            // We want subsequent calls to get_token to be able to interrupt our\n            //  Timeout when it's waiting for e.g. a long normal expiration, but\n            //  not when we're waiting for a rate limit reset. Sleep instead.\n            await (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_3__.sleep)(timeUntilRefreshInMs);\n            timeUntilRefreshInMs = 0;\n        }\n        this.queueNextPoll(timeUntilRefreshInMs);\n    }\n    handleUnrecoverableErrors(response) {\n        this.retryCount = 0;\n        this.tokenEmitter.emit('access_token', {\n            error: new Error(`[${response.status}] ${response.statusText}`),\n        });\n        this.stopPoller();\n    }\n    updateClockSkew(dateInMs) {\n        this.clockSkewInSeconds = (Date.now() - dateInMs) / 1000;\n    }\n    incrementRetries({ error, forceEmitError, }) {\n        this.retryCount++;\n        if (forceEmitError || this.retryCount % this.maxRetries === 0) {\n            this.retryCount = 0;\n            this.tokenEmitter.emit('access_token', { error: error });\n        }\n    }\n    queueNextPoll(timeUntilRefreshInMs) {\n        this.pollerTimer = setTimeout(() => this.pollerLoop(), timeUntilRefreshInMs);\n        if (this.pollerTimer.unref) {\n            this.pollerTimer.unref();\n        }\n    }\n    /**\n     * Solely responsible for building the HTTP request and calling the token service.\n     */\n    async requestAccessToken() {\n        // Set issued at time to 5 seconds in the past to account for clock skew\n        const ISSUED_AT_BUFFER_IN_SECONDS = 5;\n        const MAX_EXPIRY_IN_SECONDS = 60;\n        // Final expiry time takes into account the issued at time, so need to subtract IAT buffer\n        const EXPIRY_IN_SECONDS = MAX_EXPIRY_IN_SECONDS - ISSUED_AT_BUFFER_IN_SECONDS;\n        const jti = (0,_uuid__WEBPACK_IMPORTED_MODULE_0__.uuid)();\n        const currentUTCInSeconds = Math.round(Date.now() / 1000) - this.clockSkewInSeconds;\n        const jwtBody = {\n            iss: this.clientId,\n            sub: this.clientId,\n            aud: this.authServer,\n            iat: currentUTCInSeconds - ISSUED_AT_BUFFER_IN_SECONDS,\n            exp: currentUTCInSeconds + EXPIRY_IN_SECONDS,\n            jti,\n        };\n        const key = await (0,jose__WEBPACK_IMPORTED_MODULE_4__.importPKCS8)(this.clientKey, 'RS256');\n        const signedJwt = await new jose__WEBPACK_IMPORTED_MODULE_5__.SignJWT(jwtBody)\n            .setProtectedHeader({ alg: this.alg, kid: this.keyId, typ: 'JWT' })\n            .sign(key);\n        const requestBody = `grant_type=${this.grantType}&client_assertion_type=${this.clientAssertionType}&client_assertion=${signedJwt}&scope=${this.scope}`;\n        const accessTokenEndpoint = `${this.authServer}/token`;\n        const requestOptions = {\n            method: 'POST',\n            url: accessTokenEndpoint,\n            body: requestBody,\n            headers: {\n                'Content-Type': 'application/x-www-form-urlencoded',\n            },\n            httpRequestTimeout: 10000,\n        };\n        return this.httpClient.makeRequest(requestOptions);\n    }\n    async getAccessToken() {\n        // Use the cached token if it is still valid, otherwise wait for a new token.\n        if (this.isValidToken(this.accessToken)) {\n            return this.accessToken;\n        }\n        // stop poller first in order to make sure that it's not sleeping if we need a token immediately\n        // Otherwise it could be hours before the expiration time passes normally\n        this.stopPoller();\n        // startPoller needs to be called somewhere, either lazily when a token is first requested, or at instantiation.\n        // Doing it lazily currently\n        this.pollerLoop().catch(() => { });\n        return new Promise((resolve, reject) => {\n            this.tokenEmitter.once('access_token', (event) => {\n                if ('token' in event) {\n                    resolve(event.token);\n                }\n                else {\n                    reject(event.error);\n                }\n            });\n        });\n    }\n    clearToken() {\n        this.accessToken = undefined;\n    }\n    isValidToken(token) {\n        return (typeof token !== 'undefined' &&\n            token !== null &&\n            token.expires_in < Date.now() / 1000);\n    }\n}\n//# sourceMappingURL=token-manager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/token-manager.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/uuid.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/uuid.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uuid: () => (/* reexport safe */ _lukeed_uuid__WEBPACK_IMPORTED_MODULE_0__.v4)\n/* harmony export */ });\n/* harmony import */ var _lukeed_uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lukeed/uuid */ \"(rsc)/./node_modules/.pnpm/@lukeed+uuid@2.0.1/node_modules/@lukeed/uuid/dist/index.mjs\");\n\n//# sourceMappingURL=uuid.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2xpYi91dWlkLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBDO0FBQzFDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL2xpYi91dWlkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IHY0IGFzIHV1aWQgfSBmcm9tICdAbHVrZWVkL3V1aWQnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXVpZC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/uuid.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/context-batch.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/context-batch.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContextBatch: () => (/* binding */ ContextBatch)\n/* harmony export */ });\n/* harmony import */ var _lib_uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/uuid */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/uuid.js\");\n\nconst MAX_EVENT_SIZE_IN_KB = 32;\nconst MAX_BATCH_SIZE_IN_KB = 480; //  (500 KB is the limit, leaving some padding)\nclass ContextBatch {\n    id = (0,_lib_uuid__WEBPACK_IMPORTED_MODULE_0__.uuid)();\n    items = [];\n    sizeInBytes = 0;\n    maxEventCount;\n    constructor(maxEventCount) {\n        this.maxEventCount = Math.max(1, maxEventCount);\n    }\n    tryAdd(item) {\n        if (this.length === this.maxEventCount)\n            return {\n                success: false,\n                message: `Event limit of ${this.maxEventCount} has been exceeded.`,\n            };\n        const eventSize = this.calculateSize(item.context);\n        if (eventSize > MAX_EVENT_SIZE_IN_KB * 1024) {\n            return {\n                success: false,\n                message: `Event exceeds maximum event size of ${MAX_EVENT_SIZE_IN_KB} KB`,\n            };\n        }\n        if (this.sizeInBytes + eventSize > MAX_BATCH_SIZE_IN_KB * 1024) {\n            return {\n                success: false,\n                message: `Event has caused batch size to exceed ${MAX_BATCH_SIZE_IN_KB} KB`,\n            };\n        }\n        this.items.push(item);\n        this.sizeInBytes += eventSize;\n        return { success: true };\n    }\n    get length() {\n        return this.items.length;\n    }\n    calculateSize(ctx) {\n        return encodeURI(JSON.stringify(ctx.event)).split(/%..|i/).length;\n    }\n    getEvents() {\n        const events = this.items.map(({ context }) => context.event);\n        return events;\n    }\n    getContexts() {\n        return this.items.map((item) => item.context);\n    }\n    resolveEvents() {\n        this.items.forEach(({ resolver, context }) => resolver(context));\n    }\n}\n//# sourceMappingURL=context-batch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/context-batch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/index.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/index.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConfiguredNodePlugin: () => (/* binding */ createConfiguredNodePlugin),\n/* harmony export */   createNodePlugin: () => (/* binding */ createNodePlugin)\n/* harmony export */ });\n/* harmony import */ var _publisher__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./publisher */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/publisher.js\");\n/* harmony import */ var _generated_version__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../generated/version */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/generated/version.js\");\n/* harmony import */ var _lib_env__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/env */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/env.js\");\n\n\n\nfunction normalizeEvent(ctx) {\n    ctx.updateEvent('context.library.name', '@segment/analytics-node');\n    ctx.updateEvent('context.library.version', _generated_version__WEBPACK_IMPORTED_MODULE_1__.version);\n    const runtime = (0,_lib_env__WEBPACK_IMPORTED_MODULE_2__.detectRuntime)();\n    if (runtime === 'node') {\n        // eslint-disable-next-line no-restricted-globals\n        ctx.updateEvent('_metadata.nodeVersion', process.version);\n    }\n    ctx.updateEvent('_metadata.jsRuntime', runtime);\n}\nfunction createNodePlugin(publisher) {\n    function action(ctx) {\n        normalizeEvent(ctx);\n        return publisher.enqueue(ctx);\n    }\n    return {\n        name: 'Segment.io',\n        type: 'destination',\n        version: '1.0.0',\n        isLoaded: () => true,\n        load: () => Promise.resolve(),\n        alias: action,\n        group: action,\n        identify: action,\n        page: action,\n        screen: action,\n        track: action,\n    };\n}\nconst createConfiguredNodePlugin = (props, emitter) => {\n    const publisher = new _publisher__WEBPACK_IMPORTED_MODULE_0__.Publisher(props, emitter);\n    return {\n        publisher: publisher,\n        plugin: createNodePlugin(publisher),\n    };\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLW5vZGVAMi4yLjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1ub2RlL2Rpc3QvZXNtL3BsdWdpbnMvc2VnbWVudGlvL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXdDO0FBQ1U7QUFDSjtBQUM5QztBQUNBO0FBQ0EsK0NBQStDLHVEQUFPO0FBQ3RELG9CQUFvQix1REFBYTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsMEJBQTBCLGlEQUFTO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzZWdtZW50K2FuYWx5dGljcy1ub2RlQDIuMi4wL25vZGVfbW9kdWxlcy9Ac2VnbWVudC9hbmFseXRpY3Mtbm9kZS9kaXN0L2VzbS9wbHVnaW5zL3NlZ21lbnRpby9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQdWJsaXNoZXIgfSBmcm9tICcuL3B1Ymxpc2hlcic7XG5pbXBvcnQgeyB2ZXJzaW9uIH0gZnJvbSAnLi4vLi4vZ2VuZXJhdGVkL3ZlcnNpb24nO1xuaW1wb3J0IHsgZGV0ZWN0UnVudGltZSB9IGZyb20gJy4uLy4uL2xpYi9lbnYnO1xuZnVuY3Rpb24gbm9ybWFsaXplRXZlbnQoY3R4KSB7XG4gICAgY3R4LnVwZGF0ZUV2ZW50KCdjb250ZXh0LmxpYnJhcnkubmFtZScsICdAc2VnbWVudC9hbmFseXRpY3Mtbm9kZScpO1xuICAgIGN0eC51cGRhdGVFdmVudCgnY29udGV4dC5saWJyYXJ5LnZlcnNpb24nLCB2ZXJzaW9uKTtcbiAgICBjb25zdCBydW50aW1lID0gZGV0ZWN0UnVudGltZSgpO1xuICAgIGlmIChydW50aW1lID09PSAnbm9kZScpIHtcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXJlc3RyaWN0ZWQtZ2xvYmFsc1xuICAgICAgICBjdHgudXBkYXRlRXZlbnQoJ19tZXRhZGF0YS5ub2RlVmVyc2lvbicsIHByb2Nlc3MudmVyc2lvbik7XG4gICAgfVxuICAgIGN0eC51cGRhdGVFdmVudCgnX21ldGFkYXRhLmpzUnVudGltZScsIHJ1bnRpbWUpO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZU5vZGVQbHVnaW4ocHVibGlzaGVyKSB7XG4gICAgZnVuY3Rpb24gYWN0aW9uKGN0eCkge1xuICAgICAgICBub3JtYWxpemVFdmVudChjdHgpO1xuICAgICAgICByZXR1cm4gcHVibGlzaGVyLmVucXVldWUoY3R4KTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbmFtZTogJ1NlZ21lbnQuaW8nLFxuICAgICAgICB0eXBlOiAnZGVzdGluYXRpb24nLFxuICAgICAgICB2ZXJzaW9uOiAnMS4wLjAnLFxuICAgICAgICBpc0xvYWRlZDogKCkgPT4gdHJ1ZSxcbiAgICAgICAgbG9hZDogKCkgPT4gUHJvbWlzZS5yZXNvbHZlKCksXG4gICAgICAgIGFsaWFzOiBhY3Rpb24sXG4gICAgICAgIGdyb3VwOiBhY3Rpb24sXG4gICAgICAgIGlkZW50aWZ5OiBhY3Rpb24sXG4gICAgICAgIHBhZ2U6IGFjdGlvbixcbiAgICAgICAgc2NyZWVuOiBhY3Rpb24sXG4gICAgICAgIHRyYWNrOiBhY3Rpb24sXG4gICAgfTtcbn1cbmV4cG9ydCBjb25zdCBjcmVhdGVDb25maWd1cmVkTm9kZVBsdWdpbiA9IChwcm9wcywgZW1pdHRlcikgPT4ge1xuICAgIGNvbnN0IHB1Ymxpc2hlciA9IG5ldyBQdWJsaXNoZXIocHJvcHMsIGVtaXR0ZXIpO1xuICAgIHJldHVybiB7XG4gICAgICAgIHB1Ymxpc2hlcjogcHVibGlzaGVyLFxuICAgICAgICBwbHVnaW46IGNyZWF0ZU5vZGVQbHVnaW4ocHVibGlzaGVyKSxcbiAgICB9O1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/publisher.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/publisher.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Publisher: () => (/* binding */ Publisher)\n/* harmony export */ });\n/* harmony import */ var _segment_analytics_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @segment/analytics-core */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/priority-queue/backoff.js\");\n/* harmony import */ var _lib_create_url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/create-url */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/create-url.js\");\n/* harmony import */ var _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @segment/analytics-generic-utils */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-generic-utils@1.2.0/node_modules/@segment/analytics-generic-utils/dist/esm/create-deferred/create-deferred.js\");\n/* harmony import */ var _context_batch__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context-batch */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/context-batch.js\");\n/* harmony import */ var _lib_token_manager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/token-manager */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/lib/token-manager.js\");\n\n\n\n\n\nfunction sleep(timeoutInMs) {\n    return new Promise((resolve) => setTimeout(resolve, timeoutInMs));\n}\nfunction noop() { }\n/**\n * The Publisher is responsible for batching events and sending them to the Segment API.\n */\nclass Publisher {\n    pendingFlushTimeout;\n    _batch;\n    _flushInterval;\n    _flushAt;\n    _maxRetries;\n    _url;\n    _flushPendingItemsCount;\n    _httpRequestTimeout;\n    _emitter;\n    _disable;\n    _httpClient;\n    _writeKey;\n    _tokenManager;\n    constructor({ host, path, maxRetries, flushAt, flushInterval, writeKey, httpRequestTimeout, httpClient, disable, oauthSettings, }, emitter) {\n        this._emitter = emitter;\n        this._maxRetries = maxRetries;\n        this._flushAt = Math.max(flushAt, 1);\n        this._flushInterval = flushInterval;\n        this._url = (0,_lib_create_url__WEBPACK_IMPORTED_MODULE_0__.tryCreateFormattedUrl)(host ?? 'https://api.segment.io', path ?? '/v1/batch');\n        this._httpRequestTimeout = httpRequestTimeout ?? 10000;\n        this._disable = Boolean(disable);\n        this._httpClient = httpClient;\n        this._writeKey = writeKey;\n        if (oauthSettings) {\n            this._tokenManager = new _lib_token_manager__WEBPACK_IMPORTED_MODULE_2__.TokenManager({\n                ...oauthSettings,\n                httpClient: oauthSettings.httpClient ?? httpClient,\n                maxRetries: oauthSettings.maxRetries ?? maxRetries,\n            });\n        }\n    }\n    createBatch() {\n        this.pendingFlushTimeout && clearTimeout(this.pendingFlushTimeout);\n        const batch = new _context_batch__WEBPACK_IMPORTED_MODULE_1__.ContextBatch(this._flushAt);\n        this._batch = batch;\n        this.pendingFlushTimeout = setTimeout(() => {\n            if (batch === this._batch) {\n                this._batch = undefined;\n            }\n            this.pendingFlushTimeout = undefined;\n            if (batch.length) {\n                this.send(batch).catch(noop);\n            }\n        }, this._flushInterval);\n        return batch;\n    }\n    clearBatch() {\n        this.pendingFlushTimeout && clearTimeout(this.pendingFlushTimeout);\n        this._batch = undefined;\n    }\n    flush(pendingItemsCount) {\n        if (!pendingItemsCount) {\n            // if number of pending items is 0, there will never be anything else entering the batch, since the app is closed.\n            if (this._tokenManager) {\n                this._tokenManager.stopPoller();\n            }\n            return;\n        }\n        this._flushPendingItemsCount = pendingItemsCount;\n        // if batch is empty, there's nothing to flush, and when things come in, enqueue will handle them.\n        if (!this._batch)\n            return;\n        // the number of globally pending items will always be larger or the same as batch size.\n        // Any mismatch is because some globally pending items are in plugins.\n        const isExpectingNoMoreItems = this._batch.length === pendingItemsCount;\n        if (isExpectingNoMoreItems) {\n            this.send(this._batch)\n                .catch(noop)\n                .finally(() => {\n                // stop poller so program can exit ().\n                if (this._tokenManager) {\n                    this._tokenManager.stopPoller();\n                }\n            });\n            this.clearBatch();\n        }\n    }\n    /**\n     * Enqueues the context for future delivery.\n     * @param ctx - Context containing a Segment event.\n     * @returns a promise that resolves with the context after the event has been delivered.\n     */\n    enqueue(ctx) {\n        const batch = this._batch ?? this.createBatch();\n        const { promise: ctxPromise, resolve } = (0,_segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_3__.createDeferred)();\n        const pendingItem = {\n            context: ctx,\n            resolver: resolve,\n        };\n        /*\n          The following logic ensures that a batch is never orphaned,\n          and is always sent before a new batch is created.\n    \n          Add an event to the existing batch.\n            Success: Check if batch is full or no more items are expected to come in (i.e. closing). If so, send batch.\n            Failure: Assume event is too big to fit in current batch - send existing batch.\n              Add an event to the new batch.\n                Success: Check if batch is full and send if it is.\n                Failure: Event exceeds maximum size (it will never fit), fail the event.\n        */\n        const addStatus = batch.tryAdd(pendingItem);\n        if (addStatus.success) {\n            const isExpectingNoMoreItems = batch.length === this._flushPendingItemsCount;\n            const isFull = batch.length === this._flushAt;\n            if (isFull || isExpectingNoMoreItems) {\n                this.send(batch).catch(noop);\n                this.clearBatch();\n            }\n            return ctxPromise;\n        }\n        // If the new item causes the maximimum event size to be exceeded, send the current batch and create a new one.\n        if (batch.length) {\n            this.send(batch).catch(noop);\n            this.clearBatch();\n        }\n        const fallbackBatch = this.createBatch();\n        const fbAddStatus = fallbackBatch.tryAdd(pendingItem);\n        if (fbAddStatus.success) {\n            const isExpectingNoMoreItems = fallbackBatch.length === this._flushPendingItemsCount;\n            if (isExpectingNoMoreItems) {\n                this.send(fallbackBatch).catch(noop);\n                this.clearBatch();\n            }\n            return ctxPromise;\n        }\n        else {\n            // this should only occur if max event size is exceeded\n            ctx.setFailedDelivery({\n                reason: new Error(fbAddStatus.message),\n            });\n            return Promise.resolve(ctx);\n        }\n    }\n    async send(batch) {\n        if (this._flushPendingItemsCount) {\n            this._flushPendingItemsCount -= batch.length;\n        }\n        const events = batch.getEvents();\n        const maxAttempts = this._maxRetries + 1;\n        let currentAttempt = 0;\n        while (currentAttempt < maxAttempts) {\n            currentAttempt++;\n            let requestedRetryTimeout;\n            let failureReason;\n            try {\n                if (this._disable) {\n                    return batch.resolveEvents();\n                }\n                let authString = undefined;\n                if (this._tokenManager) {\n                    const token = await this._tokenManager.getAccessToken();\n                    if (token && token.access_token) {\n                        authString = `Bearer ${token.access_token}`;\n                    }\n                }\n                const headers = {\n                    'Content-Type': 'application/json',\n                    'User-Agent': 'analytics-node-next/latest',\n                    ...(authString ? { Authorization: authString } : {}),\n                };\n                const request = {\n                    url: this._url,\n                    method: 'POST',\n                    headers: headers,\n                    body: JSON.stringify({\n                        batch: events,\n                        writeKey: this._writeKey,\n                        sentAt: new Date(),\n                    }),\n                    httpRequestTimeout: this._httpRequestTimeout,\n                };\n                this._emitter.emit('http_request', {\n                    body: request.body,\n                    method: request.method,\n                    url: request.url,\n                    headers: request.headers,\n                });\n                const response = await this._httpClient.makeRequest(request);\n                if (response.status >= 200 && response.status < 300) {\n                    // Successfully sent events, so exit!\n                    batch.resolveEvents();\n                    return;\n                }\n                else if (this._tokenManager &&\n                    (response.status === 400 ||\n                        response.status === 401 ||\n                        response.status === 403)) {\n                    // Retry with a new OAuth token if we have OAuth data\n                    this._tokenManager.clearToken();\n                    failureReason = new Error(`[${response.status}] ${response.statusText}`);\n                }\n                else if (response.status === 400) {\n                    // https://segment.com/docs/connections/sources/catalog/libraries/server/http-api/#max-request-size\n                    // Request either malformed or size exceeded - don't retry.\n                    resolveFailedBatch(batch, new Error(`[${response.status}] ${response.statusText}`));\n                    return;\n                }\n                else if (response.status === 429) {\n                    // Rate limited, wait for the reset time\n                    if (response.headers && 'x-ratelimit-reset' in response.headers) {\n                        const rateLimitResetTimestamp = parseInt(response.headers['x-ratelimit-reset'], 10);\n                        if (isFinite(rateLimitResetTimestamp)) {\n                            requestedRetryTimeout = rateLimitResetTimestamp - Date.now();\n                        }\n                    }\n                    failureReason = new Error(`[${response.status}] ${response.statusText}`);\n                }\n                else {\n                    // Treat other errors as transient and retry.\n                    failureReason = new Error(`[${response.status}] ${response.statusText}`);\n                }\n            }\n            catch (err) {\n                // Network errors get thrown, retry them.\n                failureReason = err;\n            }\n            // Final attempt failed, update context and resolve events.\n            if (currentAttempt === maxAttempts) {\n                resolveFailedBatch(batch, failureReason);\n                return;\n            }\n            // Retry after attempt-based backoff.\n            await sleep(requestedRetryTimeout\n                ? requestedRetryTimeout\n                : (0,_segment_analytics_core__WEBPACK_IMPORTED_MODULE_4__.backoff)({\n                    attempt: currentAttempt,\n                    minTimeout: 25,\n                    maxTimeout: 1000,\n                }));\n        }\n    }\n}\nfunction resolveFailedBatch(batch, reason) {\n    batch.getContexts().forEach((ctx) => ctx.setFailedDelivery({ reason }));\n    batch.resolveEvents();\n}\n//# sourceMappingURL=publisher.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/plugins/segmentio/publisher.js\n");

/***/ })

};
;