"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@graphql-tools+executor@1.3.1_graphql@16.9.0";
exports.ids = ["vendor-chunks/@graphql-tools+executor@1.3.1_graphql@16.9.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/coerceError.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/coerceError.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.coerceError = void 0;\nfunction coerceError(error) {\n    if (error instanceof Error) {\n        return error;\n    }\n    if (typeof error === 'object' && error != null) {\n        if ('message' in error && typeof error.message === 'string') {\n            let errorOptions;\n            if ('cause' in error) {\n                errorOptions = { cause: error.cause };\n            }\n            const coercedError = new Error(error.message, errorOptions);\n            if ('stack' in error && typeof error.stack === 'string') {\n                coercedError.stack = error.stack;\n            }\n            if ('name' in error && typeof error.name === 'string') {\n                coercedError.name = error.name;\n            }\n            return coercedError;\n        }\n    }\n    return new Error(String(error));\n}\nexports.coerceError = coerceError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/coerceError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/execute.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/execute.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isIncrementalResult = exports.getFieldDef = exports.flattenIncrementalResults = exports.subscribe = exports.defaultFieldResolver = exports.defaultTypeResolver = exports.CRITICAL_ERROR = exports.buildResolveInfo = exports.buildExecutionContext = exports.getFragmentsFromDocument = exports.assertValidExecutionArguments = exports.executeSync = exports.execute = void 0;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/.pnpm/graphql@16.9.0/node_modules/graphql/index.mjs\");\nconst value_or_promise_1 = __webpack_require__(/*! value-or-promise */ \"(rsc)/./node_modules/.pnpm/value-or-promise@1.0.12/node_modules/value-or-promise/build/module/index.js\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+utils@10.5.4_graphql@16.9.0/node_modules/@graphql-tools/utils/cjs/index.js\");\nconst coerceError_js_1 = __webpack_require__(/*! ./coerceError.js */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/coerceError.js\");\nconst flattenAsyncIterable_js_1 = __webpack_require__(/*! ./flattenAsyncIterable.js */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/flattenAsyncIterable.js\");\nconst invariant_js_1 = __webpack_require__(/*! ./invariant.js */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/invariant.js\");\nconst promiseForObject_js_1 = __webpack_require__(/*! ./promiseForObject.js */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/promiseForObject.js\");\nconst values_js_1 = __webpack_require__(/*! ./values.js */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/values.js\");\n/**\n * A memoized collection of relevant subfields with regard to the return\n * type. Memoizing ensures the subfields are not repeatedly calculated, which\n * saves overhead when resolving lists of values.\n */\nconst collectSubfields = (0, utils_1.memoize3)((exeContext, returnType, fieldNodes) => (0, utils_1.collectSubFields)(exeContext.schema, exeContext.fragments, exeContext.variableValues, returnType, fieldNodes));\n/**\n * Implements the \"Executing requests\" section of the GraphQL specification,\n * including `@defer` and `@stream` as proposed in\n * https://github.com/graphql/graphql-spec/pull/742\n *\n * This function returns a Promise of an IncrementalExecutionResults\n * object. This object either consists of a single ExecutionResult, or an\n * object containing an `initialResult` and a stream of `subsequentResults`.\n *\n * If the arguments to this function do not result in a legal execution context,\n * a GraphQLError will be thrown immediately explaining the invalid input.\n */\nfunction execute(args) {\n    // If a valid execution context cannot be created due to incorrect arguments,\n    // a \"Response\" with only errors is returned.\n    const exeContext = buildExecutionContext(args);\n    // Return early errors if execution context failed.\n    if (!('schema' in exeContext)) {\n        return {\n            errors: exeContext.map(e => {\n                Object.defineProperty(e, 'extensions', {\n                    value: {\n                        ...e.extensions,\n                        http: {\n                            ...e.extensions?.['http'],\n                            status: 400,\n                        },\n                    },\n                });\n                return e;\n            }),\n        };\n    }\n    return executeImpl(exeContext);\n}\nexports.execute = execute;\nfunction executeImpl(exeContext) {\n    if (exeContext.signal?.aborted) {\n        throw exeContext.signal.reason;\n    }\n    // Return a Promise that will eventually resolve to the data described by\n    // The \"Response\" section of the GraphQL specification.\n    //\n    // If errors are encountered while executing a GraphQL field, only that\n    // field and its descendants will be omitted, and sibling fields will still\n    // be executed. An execution which encounters errors will still result in a\n    // resolved Promise.\n    //\n    // Errors from sub-fields of a NonNull type may propagate to the top level,\n    // at which point we still log the error and null the parent field, which\n    // in this case is the entire response.\n    const result = new value_or_promise_1.ValueOrPromise(() => executeOperation(exeContext))\n        .then(data => {\n        const initialResult = buildResponse(data, exeContext.errors);\n        if (exeContext.subsequentPayloads.size > 0) {\n            return {\n                initialResult: {\n                    ...initialResult,\n                    hasNext: true,\n                },\n                subsequentResults: yieldSubsequentPayloads(exeContext),\n            };\n        }\n        return initialResult;\n    }, (error) => {\n        if (exeContext.signal?.aborted) {\n            throw exeContext.signal.reason;\n        }\n        if (error.errors) {\n            exeContext.errors.push(...error.errors);\n        }\n        else {\n            exeContext.errors.push(error);\n        }\n        return buildResponse(null, exeContext.errors);\n    })\n        .resolve();\n    return result;\n}\n/**\n * Also implements the \"Executing requests\" section of the GraphQL specification.\n * However, it guarantees to complete synchronously (or throw an error) assuming\n * that all field resolvers are also synchronous.\n */\nfunction executeSync(args) {\n    const result = execute(args);\n    // Assert that the execution was synchronous.\n    if ((0, utils_1.isPromise)(result) || 'initialResult' in result) {\n        throw new Error('GraphQL execution failed to complete synchronously.');\n    }\n    return result;\n}\nexports.executeSync = executeSync;\n/**\n * Given a completed execution context and data, build the `{ errors, data }`\n * response defined by the \"Response\" section of the GraphQL specification.\n */\nfunction buildResponse(data, errors) {\n    return errors.length === 0 ? { data } : { errors, data };\n}\n/**\n * Essential assertions before executing to provide developer feedback for\n * improper use of the GraphQL library.\n *\n * @internal\n */\nfunction assertValidExecutionArguments(schema, document, rawVariableValues) {\n    console.assert(!!document, 'Must provide document.');\n    // If the schema used for execution is invalid, throw an error.\n    (0, graphql_1.assertValidSchema)(schema);\n    // Variables, if provided, must be an object.\n    console.assert(rawVariableValues == null || (0, utils_1.isObjectLike)(rawVariableValues), 'Variables must be provided as an Object where each property is a variable value. Perhaps look to see if an unparsed JSON string was provided.');\n}\nexports.assertValidExecutionArguments = assertValidExecutionArguments;\nexports.getFragmentsFromDocument = (0, utils_1.memoize1)(function getFragmentsFromDocument(document) {\n    const fragments = Object.create(null);\n    for (const definition of document.definitions) {\n        if (definition.kind === graphql_1.Kind.FRAGMENT_DEFINITION) {\n            fragments[definition.name.value] = definition;\n        }\n    }\n    return fragments;\n});\n/**\n * Constructs a ExecutionContext object from the arguments passed to\n * execute, which we will pass throughout the other execution methods.\n *\n * Throws a GraphQLError if a valid execution context cannot be created.\n *\n * TODO: consider no longer exporting this function\n * @internal\n */\nfunction buildExecutionContext(args) {\n    const { schema, document, rootValue, contextValue, variableValues: rawVariableValues, operationName, fieldResolver, typeResolver, subscribeFieldResolver, signal, } = args;\n    // If the schema used for execution is invalid, throw an error.\n    (0, graphql_1.assertValidSchema)(schema);\n    const fragments = (0, exports.getFragmentsFromDocument)(document);\n    let operation;\n    for (const definition of document.definitions) {\n        switch (definition.kind) {\n            case graphql_1.Kind.OPERATION_DEFINITION:\n                if (operationName == null) {\n                    if (operation !== undefined) {\n                        return [\n                            (0, utils_1.createGraphQLError)('Must provide operation name if query contains multiple operations.'),\n                        ];\n                    }\n                    operation = definition;\n                }\n                else if (definition.name?.value === operationName) {\n                    operation = definition;\n                }\n                break;\n            default:\n            // ignore non-executable definitions\n        }\n    }\n    if (operation == null) {\n        if (operationName != null) {\n            return [(0, utils_1.createGraphQLError)(`Unknown operation named \"${operationName}\".`)];\n        }\n        return [(0, utils_1.createGraphQLError)('Must provide an operation.')];\n    }\n    // FIXME: https://github.com/graphql/graphql-js/issues/2203\n    /* c8 ignore next */\n    const variableDefinitions = operation.variableDefinitions ?? [];\n    const coercedVariableValues = (0, values_js_1.getVariableValues)(schema, variableDefinitions, rawVariableValues ?? {}, {\n        maxErrors: 50,\n    });\n    if (coercedVariableValues.errors) {\n        return coercedVariableValues.errors;\n    }\n    return {\n        schema,\n        fragments,\n        rootValue,\n        contextValue,\n        operation,\n        variableValues: coercedVariableValues.coerced,\n        fieldResolver: fieldResolver ?? exports.defaultFieldResolver,\n        typeResolver: typeResolver ?? exports.defaultTypeResolver,\n        subscribeFieldResolver: subscribeFieldResolver ?? exports.defaultFieldResolver,\n        subsequentPayloads: new Set(),\n        errors: [],\n        signal,\n    };\n}\nexports.buildExecutionContext = buildExecutionContext;\nfunction buildPerEventExecutionContext(exeContext, payload) {\n    return {\n        ...exeContext,\n        rootValue: payload,\n        subsequentPayloads: new Set(),\n        errors: [],\n    };\n}\n/**\n * Implements the \"Executing operations\" section of the spec.\n */\nfunction executeOperation(exeContext) {\n    const { operation, schema, fragments, variableValues, rootValue } = exeContext;\n    const rootType = (0, utils_1.getDefinedRootType)(schema, operation.operation, [operation]);\n    if (rootType == null) {\n        (0, utils_1.createGraphQLError)(`Schema is not configured to execute ${operation.operation} operation.`, {\n            nodes: operation,\n        });\n    }\n    const { fields: rootFields, patches } = (0, utils_1.collectFields)(schema, fragments, variableValues, rootType, operation.selectionSet);\n    const path = undefined;\n    let result;\n    if (operation.operation === 'mutation') {\n        result = executeFieldsSerially(exeContext, rootType, rootValue, path, rootFields);\n    }\n    else {\n        result = executeFields(exeContext, rootType, rootValue, path, rootFields);\n    }\n    for (const patch of patches) {\n        const { label, fields: patchFields } = patch;\n        executeDeferredFragment(exeContext, rootType, rootValue, patchFields, label, path);\n    }\n    return result;\n}\n/**\n * Implements the \"Executing selection sets\" section of the spec\n * for fields that must be executed serially.\n */\nfunction executeFieldsSerially(exeContext, parentType, sourceValue, path, fields) {\n    return (0, utils_1.promiseReduce)(fields, (results, [responseName, fieldNodes]) => {\n        const fieldPath = (0, utils_1.addPath)(path, responseName, parentType.name);\n        if (exeContext.signal?.aborted) {\n            throw exeContext.signal.reason;\n        }\n        return new value_or_promise_1.ValueOrPromise(() => executeField(exeContext, parentType, sourceValue, fieldNodes, fieldPath)).then(result => {\n            if (result === undefined) {\n                return results;\n            }\n            results[responseName] = result;\n            return results;\n        });\n    }, Object.create(null)).resolve();\n}\n/**\n * Implements the \"Executing selection sets\" section of the spec\n * for fields that may be executed in parallel.\n */\nfunction executeFields(exeContext, parentType, sourceValue, path, fields, asyncPayloadRecord) {\n    const results = Object.create(null);\n    let containsPromise = false;\n    try {\n        for (const [responseName, fieldNodes] of fields) {\n            if (exeContext.signal?.aborted) {\n                throw exeContext.signal.reason;\n            }\n            const fieldPath = (0, utils_1.addPath)(path, responseName, parentType.name);\n            const result = executeField(exeContext, parentType, sourceValue, fieldNodes, fieldPath, asyncPayloadRecord);\n            if (result !== undefined) {\n                results[responseName] = result;\n                if ((0, utils_1.isPromise)(result)) {\n                    containsPromise = true;\n                }\n            }\n        }\n    }\n    catch (error) {\n        if (containsPromise) {\n            // Ensure that any promises returned by other fields are handled, as they may also reject.\n            return (0, promiseForObject_js_1.promiseForObject)(results, exeContext.signal).finally(() => {\n                throw error;\n            });\n        }\n        throw error;\n    }\n    // If there are no promises, we can just return the object\n    if (!containsPromise) {\n        return results;\n    }\n    // Otherwise, results is a map from field name to the result of resolving that\n    // field, which is possibly a promise. Return a promise that will return this\n    // same map, but with any promises replaced with the values they resolved to.\n    return (0, promiseForObject_js_1.promiseForObject)(results, exeContext.signal);\n}\n/**\n * Implements the \"Executing fields\" section of the spec\n * In particular, this function figures out the value that the field returns by\n * calling its resolve function, then calls completeValue to complete promises,\n * serialize scalars, or execute the sub-selection-set for objects.\n */\nfunction executeField(exeContext, parentType, source, fieldNodes, path, asyncPayloadRecord) {\n    const errors = asyncPayloadRecord?.errors ?? exeContext.errors;\n    const fieldDef = getFieldDef(exeContext.schema, parentType, fieldNodes[0]);\n    if (!fieldDef) {\n        return;\n    }\n    const returnType = fieldDef.type;\n    const resolveFn = fieldDef.resolve ?? exeContext.fieldResolver;\n    const info = buildResolveInfo(exeContext, fieldDef, fieldNodes, parentType, path);\n    // Get the resolve function, regardless of if its result is normal or abrupt (error).\n    try {\n        // Build a JS object of arguments from the field.arguments AST, using the\n        // variables scope to fulfill any variable references.\n        // TODO: find a way to memoize, in case this field is within a List type.\n        const args = (0, utils_1.getArgumentValues)(fieldDef, fieldNodes[0], exeContext.variableValues);\n        // The resolve function's optional third argument is a context value that\n        // is provided to every resolve function within an execution. It is commonly\n        // used to represent an authenticated user, or request-specific caches.\n        const contextValue = exeContext.contextValue;\n        const result = resolveFn(source, args, contextValue, info);\n        let completed;\n        if ((0, utils_1.isPromise)(result)) {\n            completed = result.then(resolved => completeValue(exeContext, returnType, fieldNodes, info, path, resolved, asyncPayloadRecord));\n        }\n        else {\n            completed = completeValue(exeContext, returnType, fieldNodes, info, path, result, asyncPayloadRecord);\n        }\n        if ((0, utils_1.isPromise)(completed)) {\n            // Note: we don't rely on a `catch` method, but we do expect \"thenable\"\n            // to take a second callback for the error case.\n            return completed.then(undefined, rawError => {\n                if (rawError instanceof AggregateError) {\n                    return new AggregateError(rawError.errors.map(rawErrorItem => {\n                        rawErrorItem = (0, coerceError_js_1.coerceError)(rawErrorItem);\n                        const error = (0, graphql_1.locatedError)(rawErrorItem, fieldNodes, (0, utils_1.pathToArray)(path));\n                        const handledError = handleFieldError(error, returnType, errors);\n                        filterSubsequentPayloads(exeContext, path, asyncPayloadRecord);\n                        return handledError;\n                    }));\n                }\n                rawError = (0, coerceError_js_1.coerceError)(rawError);\n                const error = (0, graphql_1.locatedError)(rawError, fieldNodes, (0, utils_1.pathToArray)(path));\n                const handledError = handleFieldError(error, returnType, errors);\n                filterSubsequentPayloads(exeContext, path, asyncPayloadRecord);\n                return handledError;\n            });\n        }\n        return completed;\n    }\n    catch (rawError) {\n        if (rawError instanceof AggregateError) {\n            return new AggregateError(rawError.errors.map(rawErrorItem => {\n                const coercedError = (0, coerceError_js_1.coerceError)(rawErrorItem);\n                const error = (0, graphql_1.locatedError)(coercedError, fieldNodes, (0, utils_1.pathToArray)(path));\n                return handleFieldError(error, returnType, errors);\n            }));\n        }\n        const coercedError = (0, coerceError_js_1.coerceError)(rawError);\n        const error = (0, graphql_1.locatedError)(coercedError, fieldNodes, (0, utils_1.pathToArray)(path));\n        const handledError = handleFieldError(error, returnType, errors);\n        filterSubsequentPayloads(exeContext, path, asyncPayloadRecord);\n        return handledError;\n    }\n}\n/**\n * TODO: consider no longer exporting this function\n * @internal\n */\nfunction buildResolveInfo(exeContext, fieldDef, fieldNodes, parentType, path) {\n    // The resolve function's optional fourth argument is a collection of\n    // information about the current execution state.\n    return {\n        fieldName: fieldDef.name,\n        fieldNodes,\n        returnType: fieldDef.type,\n        parentType,\n        path,\n        schema: exeContext.schema,\n        fragments: exeContext.fragments,\n        rootValue: exeContext.rootValue,\n        operation: exeContext.operation,\n        variableValues: exeContext.variableValues,\n    };\n}\nexports.buildResolveInfo = buildResolveInfo;\nexports.CRITICAL_ERROR = 'CRITICAL_ERROR';\nfunction handleFieldError(error, returnType, errors) {\n    // If the field type is non-nullable, then it is resolved without any\n    // protection from errors, however it still properly locates the error.\n    if ((0, graphql_1.isNonNullType)(returnType)) {\n        throw error;\n    }\n    if (error.extensions?.[exports.CRITICAL_ERROR]) {\n        throw error;\n    }\n    // Otherwise, error protection is applied, logging the error and resolving\n    // a null value for this field if one is encountered.\n    errors.push(error);\n    return null;\n}\n/**\n * Implements the instructions for completeValue as defined in the\n * \"Value Completion\" section of the spec.\n *\n * If the field type is Non-Null, then this recursively completes the value\n * for the inner type. It throws a field error if that completion returns null,\n * as per the \"Nullability\" section of the spec.\n *\n * If the field type is a List, then this recursively completes the value\n * for the inner type on each item in the list.\n *\n * If the field type is a Scalar or Enum, ensures the completed value is a legal\n * value of the type by calling the `serialize` method of GraphQL type\n * definition.\n *\n * If the field is an abstract type, determine the runtime type of the value\n * and then complete based on that type\n *\n * Otherwise, the field type expects a sub-selection set, and will complete the\n * value by executing all sub-selections.\n */\nfunction completeValue(exeContext, returnType, fieldNodes, info, path, result, asyncPayloadRecord) {\n    // If result is an Error, throw a located error.\n    if (result instanceof Error) {\n        throw result;\n    }\n    // If field type is NonNull, complete for inner type, and throw field error\n    // if result is null.\n    if ((0, graphql_1.isNonNullType)(returnType)) {\n        const completed = completeValue(exeContext, returnType.ofType, fieldNodes, info, path, result, asyncPayloadRecord);\n        if (completed === null) {\n            throw new Error(`Cannot return null for non-nullable field ${info.parentType.name}.${info.fieldName}.`);\n        }\n        return completed;\n    }\n    // If result value is null or undefined then return null.\n    if (result == null) {\n        return null;\n    }\n    // If field type is List, complete each item in the list with the inner type\n    if ((0, graphql_1.isListType)(returnType)) {\n        return completeListValue(exeContext, returnType, fieldNodes, info, path, result, asyncPayloadRecord);\n    }\n    // If field type is a leaf type, Scalar or Enum, serialize to a valid value,\n    // returning null if serialization is not possible.\n    if ((0, graphql_1.isLeafType)(returnType)) {\n        return completeLeafValue(returnType, result);\n    }\n    // If field type is an abstract type, Interface or Union, determine the\n    // runtime Object type and complete for that type.\n    if ((0, graphql_1.isAbstractType)(returnType)) {\n        return completeAbstractValue(exeContext, returnType, fieldNodes, info, path, result, asyncPayloadRecord);\n    }\n    // If field type is Object, execute and complete all sub-selections.\n    if ((0, graphql_1.isObjectType)(returnType)) {\n        return completeObjectValue(exeContext, returnType, fieldNodes, info, path, result, asyncPayloadRecord);\n    }\n    /* c8 ignore next 6 */\n    // Not reachable, all possible output types have been considered.\n    console.assert(false, 'Cannot complete value of unexpected output type: ' + (0, utils_1.inspect)(returnType));\n}\n/**\n * Returns an object containing the `@stream` arguments if a field should be\n * streamed based on the experimental flag, stream directive present and\n * not disabled by the \"if\" argument.\n */\nfunction getStreamValues(exeContext, fieldNodes, path) {\n    // do not stream inner lists of multi-dimensional lists\n    if (typeof path.key === 'number') {\n        return;\n    }\n    // validation only allows equivalent streams on multiple fields, so it is\n    // safe to only check the first fieldNode for the stream directive\n    const stream = (0, graphql_1.getDirectiveValues)(utils_1.GraphQLStreamDirective, fieldNodes[0], exeContext.variableValues);\n    if (!stream) {\n        return;\n    }\n    if (stream.if === false) {\n        return;\n    }\n    (0, invariant_js_1.invariant)(typeof stream['initialCount'] === 'number', 'initialCount must be a number');\n    (0, invariant_js_1.invariant)(stream['initialCount'] >= 0, 'initialCount must be a positive integer');\n    return {\n        initialCount: stream['initialCount'],\n        label: typeof stream['label'] === 'string' ? stream['label'] : undefined,\n    };\n}\n/**\n * Complete a async iterator value by completing the result and calling\n * recursively until all the results are completed.\n */\nasync function completeAsyncIteratorValue(exeContext, itemType, fieldNodes, info, path, iterator, asyncPayloadRecord) {\n    exeContext.signal?.addEventListener('abort', () => {\n        iterator.return?.();\n    });\n    const errors = asyncPayloadRecord?.errors ?? exeContext.errors;\n    const stream = getStreamValues(exeContext, fieldNodes, path);\n    let containsPromise = false;\n    const completedResults = [];\n    let index = 0;\n    while (true) {\n        if (stream && typeof stream.initialCount === 'number' && index >= stream.initialCount) {\n            executeStreamIterator(index, iterator, exeContext, fieldNodes, info, itemType, path, stream.label, asyncPayloadRecord);\n            break;\n        }\n        const itemPath = (0, utils_1.addPath)(path, index, undefined);\n        let iteration;\n        try {\n            iteration = await iterator.next();\n            if (iteration.done) {\n                break;\n            }\n        }\n        catch (rawError) {\n            const coercedError = (0, coerceError_js_1.coerceError)(rawError);\n            const error = (0, graphql_1.locatedError)(coercedError, fieldNodes, (0, utils_1.pathToArray)(itemPath));\n            completedResults.push(handleFieldError(error, itemType, errors));\n            break;\n        }\n        if (completeListItemValue(iteration.value, completedResults, errors, exeContext, itemType, fieldNodes, info, itemPath, asyncPayloadRecord)) {\n            containsPromise = true;\n        }\n        index += 1;\n    }\n    return containsPromise ? Promise.all(completedResults) : completedResults;\n}\n/**\n * Complete a list value by completing each item in the list with the\n * inner type\n */\nfunction completeListValue(exeContext, returnType, fieldNodes, info, path, result, asyncPayloadRecord) {\n    const itemType = returnType.ofType;\n    const errors = asyncPayloadRecord?.errors ?? exeContext.errors;\n    if ((0, utils_1.isAsyncIterable)(result)) {\n        const iterator = result[Symbol.asyncIterator]();\n        return completeAsyncIteratorValue(exeContext, itemType, fieldNodes, info, path, iterator, asyncPayloadRecord);\n    }\n    if (!(0, utils_1.isIterableObject)(result)) {\n        throw (0, utils_1.createGraphQLError)(`Expected Iterable, but did not find one for field \"${info.parentType.name}.${info.fieldName}\".`);\n    }\n    const stream = getStreamValues(exeContext, fieldNodes, path);\n    // This is specified as a simple map, however we're optimizing the path\n    // where the list contains no Promises by avoiding creating another Promise.\n    let containsPromise = false;\n    let previousAsyncPayloadRecord = asyncPayloadRecord;\n    const completedResults = [];\n    let index = 0;\n    for (const item of result) {\n        // No need to modify the info object containing the path,\n        // since from here on it is not ever accessed by resolver functions.\n        const itemPath = (0, utils_1.addPath)(path, index, undefined);\n        if (stream && typeof stream.initialCount === 'number' && index >= stream.initialCount) {\n            previousAsyncPayloadRecord = executeStreamField(path, itemPath, item, exeContext, fieldNodes, info, itemType, stream.label, previousAsyncPayloadRecord);\n            index++;\n            continue;\n        }\n        if (completeListItemValue(item, completedResults, errors, exeContext, itemType, fieldNodes, info, itemPath, asyncPayloadRecord)) {\n            containsPromise = true;\n        }\n        index++;\n    }\n    return containsPromise ? Promise.all(completedResults) : completedResults;\n}\n/**\n * Complete a list item value by adding it to the completed results.\n *\n * Returns true if the value is a Promise.\n */\nfunction completeListItemValue(item, completedResults, errors, exeContext, itemType, fieldNodes, info, itemPath, asyncPayloadRecord) {\n    try {\n        let completedItem;\n        if ((0, utils_1.isPromise)(item)) {\n            completedItem = item.then(resolved => completeValue(exeContext, itemType, fieldNodes, info, itemPath, resolved, asyncPayloadRecord));\n        }\n        else {\n            completedItem = completeValue(exeContext, itemType, fieldNodes, info, itemPath, item, asyncPayloadRecord);\n        }\n        if ((0, utils_1.isPromise)(completedItem)) {\n            // Note: we don't rely on a `catch` method, but we do expect \"thenable\"\n            // to take a second callback for the error case.\n            completedResults.push(completedItem.then(undefined, rawError => {\n                rawError = (0, coerceError_js_1.coerceError)(rawError);\n                const error = (0, graphql_1.locatedError)(rawError, fieldNodes, (0, utils_1.pathToArray)(itemPath));\n                const handledError = handleFieldError(error, itemType, errors);\n                filterSubsequentPayloads(exeContext, itemPath, asyncPayloadRecord);\n                return handledError;\n            }));\n            return true;\n        }\n        completedResults.push(completedItem);\n    }\n    catch (rawError) {\n        const coercedError = (0, coerceError_js_1.coerceError)(rawError);\n        const error = (0, graphql_1.locatedError)(coercedError, fieldNodes, (0, utils_1.pathToArray)(itemPath));\n        const handledError = handleFieldError(error, itemType, errors);\n        filterSubsequentPayloads(exeContext, itemPath, asyncPayloadRecord);\n        completedResults.push(handledError);\n    }\n    return false;\n}\n/**\n * Complete a Scalar or Enum by serializing to a valid value, returning\n * null if serialization is not possible.\n */\nfunction completeLeafValue(returnType, result) {\n    let serializedResult;\n    // Note: We transform GraphQLError to Error in order to be consistent with\n    // how non-null checks work later on.\n    // See https://github.com/kamilkisiela/graphql-hive/pull/2299\n    // See https://github.com/n1ru4l/envelop/issues/1808\n    try {\n        serializedResult = returnType.serialize(result);\n    }\n    catch (err) {\n        if (err instanceof graphql_1.GraphQLError) {\n            throw new Error(err.message);\n        }\n        throw err;\n    }\n    if (serializedResult == null) {\n        throw new Error(`Expected \\`${(0, utils_1.inspect)(returnType)}.serialize(${(0, utils_1.inspect)(result)})\\` to ` +\n            `return non-nullable value, returned: ${(0, utils_1.inspect)(serializedResult)}`);\n    }\n    return serializedResult;\n}\n/**\n * Complete a value of an abstract type by determining the runtime object type\n * of that value, then complete the value for that type.\n */\nfunction completeAbstractValue(exeContext, returnType, fieldNodes, info, path, result, asyncPayloadRecord) {\n    const resolveTypeFn = returnType.resolveType ?? exeContext.typeResolver;\n    const contextValue = exeContext.contextValue;\n    const runtimeType = resolveTypeFn(result, contextValue, info, returnType);\n    if ((0, utils_1.isPromise)(runtimeType)) {\n        return runtimeType.then(resolvedRuntimeType => completeObjectValue(exeContext, ensureValidRuntimeType(resolvedRuntimeType, exeContext, returnType, fieldNodes, info, result), fieldNodes, info, path, result, asyncPayloadRecord));\n    }\n    return completeObjectValue(exeContext, ensureValidRuntimeType(runtimeType, exeContext, returnType, fieldNodes, info, result), fieldNodes, info, path, result, asyncPayloadRecord);\n}\nfunction ensureValidRuntimeType(runtimeTypeName, exeContext, returnType, fieldNodes, info, result) {\n    if (runtimeTypeName == null) {\n        throw (0, utils_1.createGraphQLError)(`Abstract type \"${returnType.name}\" must resolve to an Object type at runtime for field \"${info.parentType.name}.${info.fieldName}\". Either the \"${returnType.name}\" type should provide a \"resolveType\" function or each possible type should provide an \"isTypeOf\" function.`, { nodes: fieldNodes });\n    }\n    // releases before 16.0.0 supported returning `GraphQLObjectType` from `resolveType`\n    // TODO: remove in 17.0.0 release\n    if ((0, graphql_1.isObjectType)(runtimeTypeName)) {\n        if (graphql_1.versionInfo.major >= 16) {\n            throw (0, utils_1.createGraphQLError)('Support for returning GraphQLObjectType from resolveType was removed in graphql-js@16.0.0 please return type name instead.');\n        }\n        runtimeTypeName = runtimeTypeName.name;\n    }\n    if (typeof runtimeTypeName !== 'string') {\n        throw (0, utils_1.createGraphQLError)(`Abstract type \"${returnType.name}\" must resolve to an Object type at runtime for field \"${info.parentType.name}.${info.fieldName}\" with ` +\n            `value ${(0, utils_1.inspect)(result)}, received \"${(0, utils_1.inspect)(runtimeTypeName)}\".`);\n    }\n    const runtimeType = exeContext.schema.getType(runtimeTypeName);\n    if (runtimeType == null) {\n        throw (0, utils_1.createGraphQLError)(`Abstract type \"${returnType.name}\" was resolved to a type \"${runtimeTypeName}\" that does not exist inside the schema.`, { nodes: fieldNodes });\n    }\n    if (!(0, graphql_1.isObjectType)(runtimeType)) {\n        throw (0, utils_1.createGraphQLError)(`Abstract type \"${returnType.name}\" was resolved to a non-object type \"${runtimeTypeName}\".`, { nodes: fieldNodes });\n    }\n    if (!exeContext.schema.isSubType(returnType, runtimeType)) {\n        throw (0, utils_1.createGraphQLError)(`Runtime Object type \"${runtimeType.name}\" is not a possible type for \"${returnType.name}\".`, { nodes: fieldNodes });\n    }\n    return runtimeType;\n}\n/**\n * Complete an Object value by executing all sub-selections.\n */\nfunction completeObjectValue(exeContext, returnType, fieldNodes, info, path, result, asyncPayloadRecord) {\n    // If there is an isTypeOf predicate function, call it with the\n    // current result. If isTypeOf returns false, then raise an error rather\n    // than continuing execution.\n    if (returnType.isTypeOf) {\n        const isTypeOf = returnType.isTypeOf(result, exeContext.contextValue, info);\n        if ((0, utils_1.isPromise)(isTypeOf)) {\n            return isTypeOf.then(resolvedIsTypeOf => {\n                if (!resolvedIsTypeOf) {\n                    throw invalidReturnTypeError(returnType, result, fieldNodes);\n                }\n                return collectAndExecuteSubfields(exeContext, returnType, fieldNodes, path, result, asyncPayloadRecord);\n            });\n        }\n        if (!isTypeOf) {\n            throw invalidReturnTypeError(returnType, result, fieldNodes);\n        }\n    }\n    return collectAndExecuteSubfields(exeContext, returnType, fieldNodes, path, result, asyncPayloadRecord);\n}\nfunction invalidReturnTypeError(returnType, result, fieldNodes) {\n    return (0, utils_1.createGraphQLError)(`Expected value of type \"${returnType.name}\" but got: ${(0, utils_1.inspect)(result)}.`, {\n        nodes: fieldNodes,\n    });\n}\nfunction collectAndExecuteSubfields(exeContext, returnType, fieldNodes, path, result, asyncPayloadRecord) {\n    // Collect sub-fields to execute to complete this value.\n    const { fields: subFieldNodes, patches: subPatches } = collectSubfields(exeContext, returnType, fieldNodes);\n    const subFields = executeFields(exeContext, returnType, result, path, subFieldNodes, asyncPayloadRecord);\n    for (const subPatch of subPatches) {\n        const { label, fields: subPatchFieldNodes } = subPatch;\n        executeDeferredFragment(exeContext, returnType, result, subPatchFieldNodes, label, path, asyncPayloadRecord);\n    }\n    return subFields;\n}\n/**\n * If a resolveType function is not given, then a default resolve behavior is\n * used which attempts two strategies:\n *\n * First, See if the provided value has a `__typename` field defined, if so, use\n * that value as name of the resolved type.\n *\n * Otherwise, test each possible type for the abstract type by calling\n * isTypeOf for the object being coerced, returning the first type that matches.\n */\nconst defaultTypeResolver = function (value, contextValue, info, abstractType) {\n    // First, look for `__typename`.\n    if ((0, utils_1.isObjectLike)(value) && typeof value['__typename'] === 'string') {\n        return value['__typename'];\n    }\n    // Otherwise, test each possible type.\n    const possibleTypes = info.schema.getPossibleTypes(abstractType);\n    const promisedIsTypeOfResults = [];\n    for (let i = 0; i < possibleTypes.length; i++) {\n        const type = possibleTypes[i];\n        if (type.isTypeOf) {\n            const isTypeOfResult = type.isTypeOf(value, contextValue, info);\n            if ((0, utils_1.isPromise)(isTypeOfResult)) {\n                promisedIsTypeOfResults[i] = isTypeOfResult;\n            }\n            else if (isTypeOfResult) {\n                return type.name;\n            }\n        }\n    }\n    if (promisedIsTypeOfResults.length) {\n        return Promise.all(promisedIsTypeOfResults).then(isTypeOfResults => {\n            for (let i = 0; i < isTypeOfResults.length; i++) {\n                if (isTypeOfResults[i]) {\n                    return possibleTypes[i].name;\n                }\n            }\n        });\n    }\n};\nexports.defaultTypeResolver = defaultTypeResolver;\n/**\n * If a resolve function is not given, then a default resolve behavior is used\n * which takes the property of the source object of the same name as the field\n * and returns it as the result, or if it's a function, returns the result\n * of calling that function while passing along args and context value.\n */\nconst defaultFieldResolver = function (source, args, contextValue, info) {\n    // ensure source is a value for which property access is acceptable.\n    if ((0, utils_1.isObjectLike)(source) || typeof source === 'function') {\n        const property = source[info.fieldName];\n        if (typeof property === 'function') {\n            return source[info.fieldName](args, contextValue, info);\n        }\n        return property;\n    }\n};\nexports.defaultFieldResolver = defaultFieldResolver;\n/**\n * Implements the \"Subscribe\" algorithm described in the GraphQL specification,\n * including `@defer` and `@stream` as proposed in\n * https://github.com/graphql/graphql-spec/pull/742\n *\n * Returns a Promise which resolves to either an AsyncIterator (if successful)\n * or an ExecutionResult (error). The promise will be rejected if the schema or\n * other arguments to this function are invalid, or if the resolved event stream\n * is not an async iterable.\n *\n * If the client-provided arguments to this function do not result in a\n * compliant subscription, a GraphQL Response (ExecutionResult) with descriptive\n * errors and no data will be returned.\n *\n * If the source stream could not be created due to faulty subscription resolver\n * logic or underlying systems, the promise will resolve to a single\n * ExecutionResult containing `errors` and no `data`.\n *\n * If the operation succeeded, the promise resolves to an AsyncIterator, which\n * yields a stream of result representing the response stream.\n *\n * Each result may be an ExecutionResult with no `hasNext` (if executing the\n * event did not use `@defer` or `@stream`), or an\n * `InitialIncrementalExecutionResult` or `SubsequentIncrementalExecutionResult`\n * (if executing the event used `@defer` or `@stream`). In the case of\n * incremental execution results, each event produces a single\n * `InitialIncrementalExecutionResult` followed by one or more\n * `SubsequentIncrementalExecutionResult`s; all but the last have `hasNext: true`,\n * and the last has `hasNext: false`. There is no interleaving between results\n * generated from the same original event.\n *\n * Accepts an object with named arguments.\n */\nfunction subscribe(args) {\n    // If a valid execution context cannot be created due to incorrect arguments,\n    // a \"Response\" with only errors is returned.\n    const exeContext = buildExecutionContext(args);\n    // Return early errors if execution context failed.\n    if (!('schema' in exeContext)) {\n        return {\n            errors: exeContext.map(e => {\n                Object.defineProperty(e, 'extensions', {\n                    value: {\n                        ...e.extensions,\n                        http: {\n                            ...e.extensions?.['http'],\n                            status: 400,\n                        },\n                    },\n                });\n                return e;\n            }),\n        };\n    }\n    const resultOrStream = createSourceEventStreamImpl(exeContext);\n    if ((0, utils_1.isPromise)(resultOrStream)) {\n        return resultOrStream.then(resolvedResultOrStream => mapSourceToResponse(exeContext, resolvedResultOrStream));\n    }\n    return mapSourceToResponse(exeContext, resultOrStream);\n}\nexports.subscribe = subscribe;\nfunction flattenIncrementalResults(incrementalResults) {\n    const subsequentIterator = incrementalResults.subsequentResults;\n    let initialResultSent = false;\n    let done = false;\n    return {\n        [Symbol.asyncIterator]() {\n            return this;\n        },\n        next() {\n            if (done) {\n                return Promise.resolve({\n                    value: undefined,\n                    done,\n                });\n            }\n            if (initialResultSent) {\n                return subsequentIterator.next();\n            }\n            initialResultSent = true;\n            return Promise.resolve({\n                value: incrementalResults.initialResult,\n                done,\n            });\n        },\n        return() {\n            done = true;\n            return subsequentIterator.return();\n        },\n        throw(error) {\n            done = true;\n            return subsequentIterator.throw(error);\n        },\n    };\n}\nexports.flattenIncrementalResults = flattenIncrementalResults;\nasync function* ensureAsyncIterable(someExecutionResult) {\n    if ('initialResult' in someExecutionResult) {\n        yield* flattenIncrementalResults(someExecutionResult);\n    }\n    else {\n        yield someExecutionResult;\n    }\n}\nfunction mapSourceToResponse(exeContext, resultOrStream) {\n    if (!(0, utils_1.isAsyncIterable)(resultOrStream)) {\n        return resultOrStream;\n    }\n    // For each payload yielded from a subscription, map it over the normal\n    // GraphQL `execute` function, with `payload` as the rootValue.\n    // This implements the \"MapSourceToResponseEvent\" algorithm described in\n    // the GraphQL specification. The `execute` function provides the\n    // \"ExecuteSubscriptionEvent\" algorithm, as it is nearly identical to the\n    // \"ExecuteQuery\" algorithm, for which `execute` is also used.\n    return (0, flattenAsyncIterable_js_1.flattenAsyncIterable)((0, utils_1.mapAsyncIterator)(resultOrStream, async (payload) => ensureAsyncIterable(await executeImpl(buildPerEventExecutionContext(exeContext, payload))), (error) => {\n        if (error instanceof AggregateError) {\n            throw new AggregateError(error.errors.map(e => wrapError(e, exeContext.operation)), error.message);\n        }\n        throw wrapError(error, exeContext.operation);\n    }));\n}\nfunction wrapError(error, operation) {\n    return (0, utils_1.createGraphQLError)(error.message, {\n        originalError: error,\n        nodes: [operation],\n    });\n}\nfunction createSourceEventStreamImpl(exeContext) {\n    try {\n        const eventStream = executeSubscription(exeContext);\n        if ((0, utils_1.isPromise)(eventStream)) {\n            return eventStream.then(undefined, error => ({ errors: [error] }));\n        }\n        return eventStream;\n    }\n    catch (error) {\n        return { errors: [error] };\n    }\n}\nfunction executeSubscription(exeContext) {\n    const { schema, fragments, operation, variableValues, rootValue } = exeContext;\n    const rootType = schema.getSubscriptionType();\n    if (rootType == null) {\n        throw (0, utils_1.createGraphQLError)('Schema is not configured to execute subscription operation.', {\n            nodes: operation,\n        });\n    }\n    const { fields: rootFields } = (0, utils_1.collectFields)(schema, fragments, variableValues, rootType, operation.selectionSet);\n    const [responseName, fieldNodes] = [...rootFields.entries()][0];\n    const fieldName = fieldNodes[0].name.value;\n    const fieldDef = getFieldDef(schema, rootType, fieldNodes[0]);\n    if (!fieldDef) {\n        throw (0, utils_1.createGraphQLError)(`The subscription field \"${fieldName}\" is not defined.`, {\n            nodes: fieldNodes,\n        });\n    }\n    const path = (0, utils_1.addPath)(undefined, responseName, rootType.name);\n    const info = buildResolveInfo(exeContext, fieldDef, fieldNodes, rootType, path);\n    try {\n        // Implements the \"ResolveFieldEventStream\" algorithm from GraphQL specification.\n        // It differs from \"ResolveFieldValue\" due to providing a different `resolveFn`.\n        // Build a JS object of arguments from the field.arguments AST, using the\n        // variables scope to fulfill any variable references.\n        const args = (0, utils_1.getArgumentValues)(fieldDef, fieldNodes[0], variableValues);\n        // The resolve function's optional third argument is a context value that\n        // is provided to every resolve function within an execution. It is commonly\n        // used to represent an authenticated user, or request-specific caches.\n        const contextValue = exeContext.contextValue;\n        // Call the `subscribe()` resolver or the default resolver to produce an\n        // AsyncIterable yielding raw payloads.\n        const resolveFn = fieldDef.subscribe ?? exeContext.subscribeFieldResolver;\n        const result = resolveFn(rootValue, args, contextValue, info);\n        if ((0, utils_1.isPromise)(result)) {\n            return result.then(assertEventStream).then(undefined, error => {\n                throw (0, graphql_1.locatedError)(error, fieldNodes, (0, utils_1.pathToArray)(path));\n            });\n        }\n        return assertEventStream(result, exeContext.signal);\n    }\n    catch (error) {\n        throw (0, graphql_1.locatedError)(error, fieldNodes, (0, utils_1.pathToArray)(path));\n    }\n}\nfunction assertEventStream(result, signal) {\n    if (result instanceof Error) {\n        throw result;\n    }\n    // Assert field returned an event stream, otherwise yield an error.\n    if (!(0, utils_1.isAsyncIterable)(result)) {\n        throw (0, utils_1.createGraphQLError)('Subscription field must return Async Iterable. ' + `Received: ${(0, utils_1.inspect)(result)}.`);\n    }\n    return {\n        [Symbol.asyncIterator]() {\n            const asyncIterator = result[Symbol.asyncIterator]();\n            signal?.addEventListener('abort', () => {\n                asyncIterator.return?.();\n            });\n            return asyncIterator;\n        },\n    };\n}\nfunction executeDeferredFragment(exeContext, parentType, sourceValue, fields, label, path, parentContext) {\n    const asyncPayloadRecord = new DeferredFragmentRecord({\n        label,\n        path,\n        parentContext,\n        exeContext,\n    });\n    let promiseOrData;\n    try {\n        promiseOrData = executeFields(exeContext, parentType, sourceValue, path, fields, asyncPayloadRecord);\n        if ((0, utils_1.isPromise)(promiseOrData)) {\n            promiseOrData = promiseOrData.then(null, e => {\n                asyncPayloadRecord.errors.push(e);\n                return null;\n            });\n        }\n    }\n    catch (e) {\n        asyncPayloadRecord.errors.push(e);\n        promiseOrData = null;\n    }\n    asyncPayloadRecord.addData(promiseOrData);\n}\nfunction executeStreamField(path, itemPath, item, exeContext, fieldNodes, info, itemType, label, parentContext) {\n    const asyncPayloadRecord = new StreamRecord({\n        label,\n        path: itemPath,\n        parentContext,\n        exeContext,\n    });\n    let completedItem;\n    try {\n        try {\n            if ((0, utils_1.isPromise)(item)) {\n                completedItem = item.then(resolved => completeValue(exeContext, itemType, fieldNodes, info, itemPath, resolved, asyncPayloadRecord));\n            }\n            else {\n                completedItem = completeValue(exeContext, itemType, fieldNodes, info, itemPath, item, asyncPayloadRecord);\n            }\n            if ((0, utils_1.isPromise)(completedItem)) {\n                // Note: we don't rely on a `catch` method, but we do expect \"thenable\"\n                // to take a second callback for the error case.\n                completedItem = completedItem.then(undefined, rawError => {\n                    rawError = (0, coerceError_js_1.coerceError)(rawError);\n                    const error = (0, graphql_1.locatedError)(rawError, fieldNodes, (0, utils_1.pathToArray)(itemPath));\n                    const handledError = handleFieldError(error, itemType, asyncPayloadRecord.errors);\n                    filterSubsequentPayloads(exeContext, itemPath, asyncPayloadRecord);\n                    return handledError;\n                });\n            }\n        }\n        catch (rawError) {\n            const coercedError = (0, coerceError_js_1.coerceError)(rawError);\n            const error = (0, graphql_1.locatedError)(coercedError, fieldNodes, (0, utils_1.pathToArray)(itemPath));\n            completedItem = handleFieldError(error, itemType, asyncPayloadRecord.errors);\n            filterSubsequentPayloads(exeContext, itemPath, asyncPayloadRecord);\n        }\n    }\n    catch (error) {\n        asyncPayloadRecord.errors.push(error);\n        filterSubsequentPayloads(exeContext, path, asyncPayloadRecord);\n        asyncPayloadRecord.addItems(null);\n        return asyncPayloadRecord;\n    }\n    let completedItems;\n    if ((0, utils_1.isPromise)(completedItem)) {\n        completedItems = completedItem.then(value => [value], error => {\n            asyncPayloadRecord.errors.push(error);\n            filterSubsequentPayloads(exeContext, path, asyncPayloadRecord);\n            return null;\n        });\n    }\n    else {\n        completedItems = [completedItem];\n    }\n    asyncPayloadRecord.addItems(completedItems);\n    return asyncPayloadRecord;\n}\nasync function executeStreamIteratorItem(iterator, exeContext, fieldNodes, info, itemType, asyncPayloadRecord, itemPath) {\n    let item;\n    try {\n        const { value, done } = await iterator.next();\n        if (done) {\n            asyncPayloadRecord.setIsCompletedIterator();\n            return { done, value: undefined };\n        }\n        item = value;\n    }\n    catch (rawError) {\n        const coercedError = (0, coerceError_js_1.coerceError)(rawError);\n        const error = (0, graphql_1.locatedError)(coercedError, fieldNodes, (0, utils_1.pathToArray)(itemPath));\n        const value = handleFieldError(error, itemType, asyncPayloadRecord.errors);\n        // don't continue if iterator throws\n        return { done: true, value };\n    }\n    let completedItem;\n    try {\n        completedItem = completeValue(exeContext, itemType, fieldNodes, info, itemPath, item, asyncPayloadRecord);\n        if ((0, utils_1.isPromise)(completedItem)) {\n            completedItem = completedItem.then(undefined, rawError => {\n                const error = (0, graphql_1.locatedError)(rawError, fieldNodes, (0, utils_1.pathToArray)(itemPath));\n                const handledError = handleFieldError(error, itemType, asyncPayloadRecord.errors);\n                filterSubsequentPayloads(exeContext, itemPath, asyncPayloadRecord);\n                return handledError;\n            });\n        }\n        return { done: false, value: completedItem };\n    }\n    catch (rawError) {\n        const error = (0, graphql_1.locatedError)(rawError, fieldNodes, (0, utils_1.pathToArray)(itemPath));\n        const value = handleFieldError(error, itemType, asyncPayloadRecord.errors);\n        filterSubsequentPayloads(exeContext, itemPath, asyncPayloadRecord);\n        return { done: false, value };\n    }\n}\nasync function executeStreamIterator(initialIndex, iterator, exeContext, fieldNodes, info, itemType, path, label, parentContext) {\n    let index = initialIndex;\n    let previousAsyncPayloadRecord = parentContext ?? undefined;\n    while (true) {\n        const itemPath = (0, utils_1.addPath)(path, index, undefined);\n        const asyncPayloadRecord = new StreamRecord({\n            label,\n            path: itemPath,\n            parentContext: previousAsyncPayloadRecord,\n            iterator,\n            exeContext,\n        });\n        let iteration;\n        try {\n            iteration = await executeStreamIteratorItem(iterator, exeContext, fieldNodes, info, itemType, asyncPayloadRecord, itemPath);\n        }\n        catch (error) {\n            asyncPayloadRecord.errors.push(error);\n            filterSubsequentPayloads(exeContext, path, asyncPayloadRecord);\n            asyncPayloadRecord.addItems(null);\n            // entire stream has errored and bubbled upwards\n            if (iterator?.return) {\n                iterator.return().catch(() => {\n                    // ignore errors\n                });\n            }\n            return;\n        }\n        const { done, value: completedItem } = iteration;\n        let completedItems;\n        if ((0, utils_1.isPromise)(completedItem)) {\n            completedItems = completedItem.then(value => [value], error => {\n                asyncPayloadRecord.errors.push(error);\n                filterSubsequentPayloads(exeContext, path, asyncPayloadRecord);\n                return null;\n            });\n        }\n        else {\n            completedItems = [completedItem];\n        }\n        asyncPayloadRecord.addItems(completedItems);\n        if (done) {\n            break;\n        }\n        previousAsyncPayloadRecord = asyncPayloadRecord;\n        index++;\n    }\n}\nfunction filterSubsequentPayloads(exeContext, nullPath, currentAsyncRecord) {\n    const nullPathArray = (0, utils_1.pathToArray)(nullPath);\n    exeContext.subsequentPayloads.forEach(asyncRecord => {\n        if (asyncRecord === currentAsyncRecord) {\n            // don't remove payload from where error originates\n            return;\n        }\n        for (let i = 0; i < nullPathArray.length; i++) {\n            if (asyncRecord.path[i] !== nullPathArray[i]) {\n                // asyncRecord points to a path unaffected by this payload\n                return;\n            }\n        }\n        // asyncRecord path points to nulled error field\n        if (isStreamPayload(asyncRecord) && asyncRecord.iterator?.return) {\n            asyncRecord.iterator.return().catch(() => {\n                // ignore error\n            });\n        }\n        exeContext.subsequentPayloads.delete(asyncRecord);\n    });\n}\nfunction getCompletedIncrementalResults(exeContext) {\n    const incrementalResults = [];\n    for (const asyncPayloadRecord of exeContext.subsequentPayloads) {\n        const incrementalResult = {};\n        if (!asyncPayloadRecord.isCompleted) {\n            continue;\n        }\n        exeContext.subsequentPayloads.delete(asyncPayloadRecord);\n        if (isStreamPayload(asyncPayloadRecord)) {\n            const items = asyncPayloadRecord.items;\n            if (asyncPayloadRecord.isCompletedIterator) {\n                // async iterable resolver just finished but there may be pending payloads\n                continue;\n            }\n            incrementalResult.items = items;\n        }\n        else {\n            const data = asyncPayloadRecord.data;\n            incrementalResult.data = data ?? null;\n        }\n        incrementalResult.path = asyncPayloadRecord.path;\n        if (asyncPayloadRecord.label) {\n            incrementalResult.label = asyncPayloadRecord.label;\n        }\n        if (asyncPayloadRecord.errors.length > 0) {\n            incrementalResult.errors = asyncPayloadRecord.errors;\n        }\n        incrementalResults.push(incrementalResult);\n    }\n    return incrementalResults;\n}\nfunction yieldSubsequentPayloads(exeContext) {\n    let isDone = false;\n    const abortPromise = new Promise((_, reject) => {\n        exeContext.signal?.addEventListener('abort', () => {\n            isDone = true;\n            reject(exeContext.signal?.reason);\n        });\n    });\n    async function next() {\n        if (isDone) {\n            return { value: undefined, done: true };\n        }\n        await Promise.race([\n            abortPromise,\n            ...Array.from(exeContext.subsequentPayloads).map(p => p.promise),\n        ]);\n        if (isDone) {\n            // a different call to next has exhausted all payloads\n            return { value: undefined, done: true };\n        }\n        const incremental = getCompletedIncrementalResults(exeContext);\n        const hasNext = exeContext.subsequentPayloads.size > 0;\n        if (!incremental.length && hasNext) {\n            return next();\n        }\n        if (!hasNext) {\n            isDone = true;\n        }\n        return {\n            value: incremental.length ? { incremental, hasNext } : { hasNext },\n            done: false,\n        };\n    }\n    function returnStreamIterators() {\n        const promises = [];\n        exeContext.subsequentPayloads.forEach(asyncPayloadRecord => {\n            if (isStreamPayload(asyncPayloadRecord) && asyncPayloadRecord.iterator?.return) {\n                promises.push(asyncPayloadRecord.iterator.return());\n            }\n        });\n        return Promise.all(promises);\n    }\n    return {\n        [Symbol.asyncIterator]() {\n            return this;\n        },\n        next,\n        async return() {\n            await returnStreamIterators();\n            isDone = true;\n            return { value: undefined, done: true };\n        },\n        async throw(error) {\n            await returnStreamIterators();\n            isDone = true;\n            return Promise.reject(error);\n        },\n    };\n}\nclass DeferredFragmentRecord {\n    type;\n    errors;\n    label;\n    path;\n    promise;\n    data;\n    parentContext;\n    isCompleted;\n    _exeContext;\n    _resolve;\n    constructor(opts) {\n        this.type = 'defer';\n        this.label = opts.label;\n        this.path = (0, utils_1.pathToArray)(opts.path);\n        this.parentContext = opts.parentContext;\n        this.errors = [];\n        this._exeContext = opts.exeContext;\n        this._exeContext.subsequentPayloads.add(this);\n        this.isCompleted = false;\n        this.data = null;\n        this.promise = new Promise(resolve => {\n            this._resolve = MaybePromise => {\n                resolve(MaybePromise);\n            };\n        }).then(data => {\n            this.data = data;\n            this.isCompleted = true;\n        });\n    }\n    addData(data) {\n        const parentData = this.parentContext?.promise;\n        if (parentData) {\n            this._resolve?.(parentData.then(() => data));\n            return;\n        }\n        this._resolve?.(data);\n    }\n}\nclass StreamRecord {\n    type;\n    errors;\n    label;\n    path;\n    items;\n    promise;\n    parentContext;\n    iterator;\n    isCompletedIterator;\n    isCompleted;\n    _exeContext;\n    _resolve;\n    constructor(opts) {\n        this.type = 'stream';\n        this.items = null;\n        this.label = opts.label;\n        this.path = (0, utils_1.pathToArray)(opts.path);\n        this.parentContext = opts.parentContext;\n        this.iterator = opts.iterator;\n        this.errors = [];\n        this._exeContext = opts.exeContext;\n        this._exeContext.subsequentPayloads.add(this);\n        this.isCompleted = false;\n        this.items = null;\n        this.promise = new Promise(resolve => {\n            this._resolve = MaybePromise => {\n                resolve(MaybePromise);\n            };\n        }).then(items => {\n            this.items = items;\n            this.isCompleted = true;\n        });\n    }\n    addItems(items) {\n        const parentData = this.parentContext?.promise;\n        if (parentData) {\n            this._resolve?.(parentData.then(() => items));\n            return;\n        }\n        this._resolve?.(items);\n    }\n    setIsCompletedIterator() {\n        this.isCompletedIterator = true;\n    }\n}\nfunction isStreamPayload(asyncPayload) {\n    return asyncPayload.type === 'stream';\n}\n/**\n * This method looks up the field on the given type definition.\n * It has special casing for the three introspection fields,\n * __schema, __type and __typename. __typename is special because\n * it can always be queried as a field, even in situations where no\n * other fields are allowed, like on a Union. __schema and __type\n * could get automatically added to the query type, but that would\n * require mutating type definitions, which would cause issues.\n *\n * @internal\n */\nfunction getFieldDef(schema, parentType, fieldNode) {\n    const fieldName = fieldNode.name.value;\n    if (fieldName === graphql_1.SchemaMetaFieldDef.name && schema.getQueryType() === parentType) {\n        return graphql_1.SchemaMetaFieldDef;\n    }\n    else if (fieldName === graphql_1.TypeMetaFieldDef.name && schema.getQueryType() === parentType) {\n        return graphql_1.TypeMetaFieldDef;\n    }\n    else if (fieldName === graphql_1.TypeNameMetaFieldDef.name) {\n        return graphql_1.TypeNameMetaFieldDef;\n    }\n    return parentType.getFields()[fieldName];\n}\nexports.getFieldDef = getFieldDef;\nfunction isIncrementalResult(result) {\n    return 'incremental' in result;\n}\nexports.isIncrementalResult = isIncrementalResult;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/execute.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/flattenAsyncIterable.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/flattenAsyncIterable.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.flattenAsyncIterable = void 0;\n/**\n * Given an AsyncIterable of AsyncIterables, flatten all yielded results into a\n * single AsyncIterable.\n */\nfunction flattenAsyncIterable(iterable) {\n    // You might think this whole function could be replaced with\n    //\n    //    async function* flattenAsyncIterable(iterable) {\n    //      for await (const subIterator of iterable) {\n    //        yield* subIterator;\n    //      }\n    //    }\n    //\n    // but calling `.return()` on the iterator it returns won't interrupt the `for await`.\n    const topIterator = iterable[Symbol.asyncIterator]();\n    let currentNestedIterator;\n    let waitForCurrentNestedIterator;\n    let done = false;\n    async function next() {\n        if (done) {\n            return { value: undefined, done: true };\n        }\n        try {\n            if (!currentNestedIterator) {\n                // Somebody else is getting it already.\n                if (waitForCurrentNestedIterator) {\n                    await waitForCurrentNestedIterator;\n                    return await next();\n                }\n                // Nobody else is getting it. We should!\n                let resolve;\n                waitForCurrentNestedIterator = new Promise(r => {\n                    resolve = r;\n                });\n                const topIteratorResult = await topIterator.next();\n                if (topIteratorResult.done) {\n                    // Given that done only ever transitions from false to true,\n                    // require-atomic-updates is being unnecessarily cautious.\n                    done = true;\n                    return await next();\n                }\n                // eslint is making a reasonable point here, but we've explicitly protected\n                // ourself from the race condition by ensuring that only the single call\n                // that assigns to waitForCurrentNestedIterator is allowed to assign to\n                // currentNestedIterator or waitForCurrentNestedIterator.\n                currentNestedIterator = topIteratorResult.value[Symbol.asyncIterator]();\n                waitForCurrentNestedIterator = undefined;\n                resolve();\n                return await next();\n            }\n            const rememberCurrentNestedIterator = currentNestedIterator;\n            const nestedIteratorResult = await currentNestedIterator.next();\n            if (!nestedIteratorResult.done) {\n                return nestedIteratorResult;\n            }\n            // The nested iterator is done. If it's still the current one, make it not\n            // current. (If it's not the current one, somebody else has made us move on.)\n            if (currentNestedIterator === rememberCurrentNestedIterator) {\n                currentNestedIterator = undefined;\n            }\n            return await next();\n        }\n        catch (err) {\n            done = true;\n            throw err;\n        }\n    }\n    return {\n        next,\n        async return() {\n            done = true;\n            await Promise.all([currentNestedIterator?.return?.(), topIterator.return?.()]);\n            return { value: undefined, done: true };\n        },\n        async throw(error) {\n            done = true;\n            await Promise.all([currentNestedIterator?.throw?.(error), topIterator.throw?.(error)]);\n            /* c8 ignore next */\n            throw error;\n        },\n        [Symbol.asyncIterator]() {\n            return this;\n        },\n    };\n}\nexports.flattenAsyncIterable = flattenAsyncIterable;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/flattenAsyncIterable.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/index.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/index.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\ntslib_1.__exportStar(__webpack_require__(/*! ./execute.js */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/execute.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./values.js */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/values.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./normalizedExecutor.js */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/normalizedExecutor.js\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGdyYXBocWwtdG9vbHMrZXhlY3V0b3JAMS4zLjFfZ3JhcGhxbEAxNi45LjAvbm9kZV9tb2R1bGVzL0BncmFwaHFsLXRvb2xzL2V4ZWN1dG9yL2Nqcy9leGVjdXRpb24vaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZ0JBQWdCLG1CQUFPLENBQUMsc0ZBQU87QUFDL0IscUJBQXFCLG1CQUFPLENBQUMsMkpBQWM7QUFDM0MscUJBQXFCLG1CQUFPLENBQUMseUpBQWE7QUFDMUMscUJBQXFCLG1CQUFPLENBQUMsaUxBQXlCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGdyYXBocWwtdG9vbHMrZXhlY3V0b3JAMS4zLjFfZ3JhcGhxbEAxNi45LjAvbm9kZV9tb2R1bGVzL0BncmFwaHFsLXRvb2xzL2V4ZWN1dG9yL2Nqcy9leGVjdXRpb24vaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5jb25zdCB0c2xpYl8xID0gcmVxdWlyZShcInRzbGliXCIpO1xudHNsaWJfMS5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vZXhlY3V0ZS5qc1wiKSwgZXhwb3J0cyk7XG50c2xpYl8xLl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi92YWx1ZXMuanNcIiksIGV4cG9ydHMpO1xudHNsaWJfMS5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vbm9ybWFsaXplZEV4ZWN1dG9yLmpzXCIpLCBleHBvcnRzKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/invariant.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/invariant.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.invariant = void 0;\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(message != null ? message : 'Unexpected invariant triggered.');\n    }\n}\nexports.invariant = invariant;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGdyYXBocWwtdG9vbHMrZXhlY3V0b3JAMS4zLjFfZ3JhcGhxbEAxNi45LjAvbm9kZV9tb2R1bGVzL0BncmFwaHFsLXRvb2xzL2V4ZWN1dG9yL2Nqcy9leGVjdXRpb24vaW52YXJpYW50LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGdyYXBocWwtdG9vbHMrZXhlY3V0b3JAMS4zLjFfZ3JhcGhxbEAxNi45LjAvbm9kZV9tb2R1bGVzL0BncmFwaHFsLXRvb2xzL2V4ZWN1dG9yL2Nqcy9leGVjdXRpb24vaW52YXJpYW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5pbnZhcmlhbnQgPSB2b2lkIDA7XG5mdW5jdGlvbiBpbnZhcmlhbnQoY29uZGl0aW9uLCBtZXNzYWdlKSB7XG4gICAgaWYgKCFjb25kaXRpb24pIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKG1lc3NhZ2UgIT0gbnVsbCA/IG1lc3NhZ2UgOiAnVW5leHBlY3RlZCBpbnZhcmlhbnQgdHJpZ2dlcmVkLicpO1xuICAgIH1cbn1cbmV4cG9ydHMuaW52YXJpYW50ID0gaW52YXJpYW50O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/invariant.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/normalizedExecutor.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/normalizedExecutor.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.normalizedExecutor = void 0;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/.pnpm/graphql@16.9.0/node_modules/graphql/index.mjs\");\nconst value_or_promise_1 = __webpack_require__(/*! value-or-promise */ \"(rsc)/./node_modules/.pnpm/value-or-promise@1.0.12/node_modules/value-or-promise/build/module/index.js\");\nconst execute_js_1 = __webpack_require__(/*! ./execute.js */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/execute.js\");\nfunction normalizedExecutor(args) {\n    const operationAST = (0, graphql_1.getOperationAST)(args.document, args.operationName);\n    if (operationAST == null) {\n        throw new Error('Must provide an operation.');\n    }\n    if (operationAST.operation === 'subscription') {\n        return (0, execute_js_1.subscribe)(args);\n    }\n    return new value_or_promise_1.ValueOrPromise(() => (0, execute_js_1.execute)(args))\n        .then((result) => {\n        if ('initialResult' in result) {\n            return (0, execute_js_1.flattenIncrementalResults)(result);\n        }\n        return result;\n    })\n        .resolve();\n}\nexports.normalizedExecutor = normalizedExecutor;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/normalizedExecutor.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/promiseForObject.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/promiseForObject.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.promiseForObject = void 0;\n/**\n * This function transforms a JS object `Record<string, Promise<T>>` into\n * a `Promise<Record<string, T>>`\n *\n * This is akin to bluebird's `Promise.props`, but implemented only using\n * `Promise.all` so it will work with any implementation of ES6 promises.\n */\nasync function promiseForObject(object, signal) {\n    const resolvedObject = Object.create(null);\n    await new Promise((resolve, reject) => {\n        signal?.addEventListener('abort', () => {\n            reject(signal.reason);\n        });\n        Promise.all(Object.entries(object).map(async ([key, value]) => {\n            resolvedObject[key] = await value;\n        })).then(() => resolve(), reject);\n    });\n    return resolvedObject;\n}\nexports.promiseForObject = promiseForObject;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/promiseForObject.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/values.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/values.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getVariableValues = void 0;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/.pnpm/graphql@16.9.0/node_modules/graphql/index.mjs\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+utils@10.5.4_graphql@16.9.0/node_modules/@graphql-tools/utils/cjs/index.js\");\n/**\n * Prepares an object map of variableValues of the correct type based on the\n * provided variable definitions and arbitrary input. If the input cannot be\n * parsed to match the variable definitions, a GraphQLError will be thrown.\n *\n * Note: The returned value is a plain Object with a prototype, since it is\n * exposed to user code. Care should be taken to not pull values from the\n * Object prototype.\n */\nfunction getVariableValues(schema, varDefNodes, inputs, options) {\n    const errors = [];\n    const maxErrors = options?.maxErrors;\n    try {\n        const coerced = coerceVariableValues(schema, varDefNodes, inputs, error => {\n            if (maxErrors != null && errors.length >= maxErrors) {\n                throw (0, utils_1.createGraphQLError)('Too many errors processing variables, error limit reached. Execution aborted.');\n            }\n            errors.push(error);\n        });\n        if (errors.length === 0) {\n            return { coerced };\n        }\n    }\n    catch (error) {\n        errors.push(error);\n    }\n    // @ts-expect-error - We know that errors is an array of GraphQLError.\n    return { errors };\n}\nexports.getVariableValues = getVariableValues;\nfunction coerceVariableValues(schema, varDefNodes, inputs, onError) {\n    const coercedValues = {};\n    for (const varDefNode of varDefNodes) {\n        const varName = varDefNode.variable.name.value;\n        const varType = (0, graphql_1.typeFromAST)(schema, varDefNode.type);\n        if (!(0, graphql_1.isInputType)(varType)) {\n            // Must use input types for variables. This should be caught during\n            // validation, however is checked again here for safety.\n            const varTypeStr = (0, graphql_1.print)(varDefNode.type);\n            onError((0, utils_1.createGraphQLError)(`Variable \"$${varName}\" expected value of type \"${varTypeStr}\" which cannot be used as an input type.`, { nodes: varDefNode.type }));\n            continue;\n        }\n        if (!(0, utils_1.hasOwnProperty)(inputs, varName)) {\n            if (varDefNode.defaultValue) {\n                coercedValues[varName] = (0, graphql_1.valueFromAST)(varDefNode.defaultValue, varType);\n            }\n            else if ((0, graphql_1.isNonNullType)(varType)) {\n                const varTypeStr = (0, utils_1.inspect)(varType);\n                onError((0, utils_1.createGraphQLError)(`Variable \"$${varName}\" of required type \"${varTypeStr}\" was not provided.`, {\n                    nodes: varDefNode,\n                }));\n            }\n            continue;\n        }\n        const value = inputs[varName];\n        if (value === null && (0, graphql_1.isNonNullType)(varType)) {\n            const varTypeStr = (0, utils_1.inspect)(varType);\n            onError((0, utils_1.createGraphQLError)(`Variable \"$${varName}\" of non-null type \"${varTypeStr}\" must not be null.`, {\n                nodes: varDefNode,\n            }));\n            continue;\n        }\n        coercedValues[varName] = (0, graphql_1.coerceInputValue)(value, varType, (path, invalidValue, error) => {\n            let prefix = `Variable \"$${varName}\" got invalid value ` + (0, utils_1.inspect)(invalidValue);\n            if (path.length > 0) {\n                prefix += ` at \"${varName}${(0, utils_1.printPathArray)(path)}\"`;\n            }\n            onError((0, utils_1.createGraphQLError)(prefix + '; ' + error.message, {\n                nodes: varDefNode,\n                originalError: error,\n            }));\n        });\n    }\n    return coercedValues;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/values.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/index.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/index.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\ntslib_1.__exportStar(__webpack_require__(/*! ./execution/index.js */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/execution/index.js\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGdyYXBocWwtdG9vbHMrZXhlY3V0b3JAMS4zLjFfZ3JhcGhxbEAxNi45LjAvbm9kZV9tb2R1bGVzL0BncmFwaHFsLXRvb2xzL2V4ZWN1dG9yL2Nqcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxnQkFBZ0IsbUJBQU8sQ0FBQyxzRkFBTztBQUMvQixxQkFBcUIsbUJBQU8sQ0FBQyxpS0FBc0IiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AZ3JhcGhxbC10b29scytleGVjdXRvckAxLjMuMV9ncmFwaHFsQDE2LjkuMC9ub2RlX21vZHVsZXMvQGdyYXBocWwtdG9vbHMvZXhlY3V0b3IvY2pzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuY29uc3QgdHNsaWJfMSA9IHJlcXVpcmUoXCJ0c2xpYlwiKTtcbnRzbGliXzEuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL2V4ZWN1dGlvbi9pbmRleC5qc1wiKSwgZXhwb3J0cyk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-tools+executor@1.3.1_graphql@16.9.0/node_modules/@graphql-tools/executor/cjs/index.js\n");

/***/ })

};
;