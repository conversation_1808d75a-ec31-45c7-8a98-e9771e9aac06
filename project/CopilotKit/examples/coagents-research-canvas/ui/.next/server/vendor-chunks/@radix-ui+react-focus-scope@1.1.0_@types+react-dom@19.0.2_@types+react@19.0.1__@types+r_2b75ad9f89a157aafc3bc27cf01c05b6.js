"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-focus-scope@1.1.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+r_2b75ad9f89a157aafc3bc27cf01c05b6";
exports.ids = ["vendor-chunks/@radix-ui+react-focus-scope@1.1.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+r_2b75ad9f89a157aafc3bc27cf01c05b6"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+r_2b75ad9f89a157aafc3bc27cf01c05b6/node_modules/@radix-ui/react-focus-scope/dist/index.mjs":
/*!*************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+r_2b75ad9f89a157aafc3bc27cf01c05b6/node_modules/@radix-ui/react-focus-scope/dist/index.mjs ***!
  \*************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ FocusScope),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.0_@types+react@19.0.1_react@19.0.0/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.0.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+rea_c98e92916030b59f0b851119b2fb60f3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.0_@types+react@19.0.1_react@19.0.0/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ FocusScope,Root auto */ // packages/react/focus-scope/src/FocusScope.tsx\n\n\n\n\n\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { loop = false, trapped = false, onMountAutoFocus: onMountAutoFocusProp, onUnmountAutoFocus: onUnmountAutoFocusProp, ...scopeProps } = props;\n    const [container, setContainer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const onMountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onMountAutoFocusProp);\n    const onUnmountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onUnmountAutoFocusProp);\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"FocusScope.useComposedRefs[composedRefs]\": (node)=>setContainer(node)\n    }[\"FocusScope.useComposedRefs[composedRefs]\"]);\n    const focusScope = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        paused: false,\n        pause () {\n            this.paused = true;\n        },\n        resume () {\n            this.paused = false;\n        }\n    }).current;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"FocusScope.useEffect\": ()=>{\n            if (trapped) {\n                let handleFocusIn2 = {\n                    \"FocusScope.useEffect.handleFocusIn2\": function(event) {\n                        if (focusScope.paused || !container) return;\n                        const target = event.target;\n                        if (container.contains(target)) {\n                            lastFocusedElementRef.current = target;\n                        } else {\n                            focus(lastFocusedElementRef.current, {\n                                select: true\n                            });\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleFocusIn2\"], handleFocusOut2 = {\n                    \"FocusScope.useEffect.handleFocusOut2\": function(event) {\n                        if (focusScope.paused || !container) return;\n                        const relatedTarget = event.relatedTarget;\n                        if (relatedTarget === null) return;\n                        if (!container.contains(relatedTarget)) {\n                            focus(lastFocusedElementRef.current, {\n                                select: true\n                            });\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleFocusOut2\"], handleMutations2 = {\n                    \"FocusScope.useEffect.handleMutations2\": function(mutations) {\n                        const focusedElement = document.activeElement;\n                        if (focusedElement !== document.body) return;\n                        for (const mutation of mutations){\n                            if (mutation.removedNodes.length > 0) focus(container);\n                        }\n                    }\n                }[\"FocusScope.useEffect.handleMutations2\"];\n                var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n                document.addEventListener(\"focusin\", handleFocusIn2);\n                document.addEventListener(\"focusout\", handleFocusOut2);\n                const mutationObserver = new MutationObserver(handleMutations2);\n                if (container) mutationObserver.observe(container, {\n                    childList: true,\n                    subtree: true\n                });\n                return ({\n                    \"FocusScope.useEffect\": ()=>{\n                        document.removeEventListener(\"focusin\", handleFocusIn2);\n                        document.removeEventListener(\"focusout\", handleFocusOut2);\n                        mutationObserver.disconnect();\n                    }\n                })[\"FocusScope.useEffect\"];\n            }\n        }\n    }[\"FocusScope.useEffect\"], [\n        trapped,\n        container,\n        focusScope.paused\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"FocusScope.useEffect\": ()=>{\n            if (container) {\n                focusScopesStack.add(focusScope);\n                const previouslyFocusedElement = document.activeElement;\n                const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n                if (!hasFocusedCandidate) {\n                    const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n                    container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                    container.dispatchEvent(mountEvent);\n                    if (!mountEvent.defaultPrevented) {\n                        focusFirst(removeLinks(getTabbableCandidates(container)), {\n                            select: true\n                        });\n                        if (document.activeElement === previouslyFocusedElement) {\n                            focus(container);\n                        }\n                    }\n                }\n                return ({\n                    \"FocusScope.useEffect\": ()=>{\n                        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                        setTimeout({\n                            \"FocusScope.useEffect\": ()=>{\n                                const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n                                container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                                container.dispatchEvent(unmountEvent);\n                                if (!unmountEvent.defaultPrevented) {\n                                    focus(previouslyFocusedElement ?? document.body, {\n                                        select: true\n                                    });\n                                }\n                                container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                                focusScopesStack.remove(focusScope);\n                            }\n                        }[\"FocusScope.useEffect\"], 0);\n                    }\n                })[\"FocusScope.useEffect\"];\n            }\n        }\n    }[\"FocusScope.useEffect\"], [\n        container,\n        onMountAutoFocus,\n        onUnmountAutoFocus,\n        focusScope\n    ]);\n    const handleKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"FocusScope.useCallback[handleKeyDown]\": (event)=>{\n            if (!loop && !trapped) return;\n            if (focusScope.paused) return;\n            const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n            const focusedElement = document.activeElement;\n            if (isTabKey && focusedElement) {\n                const container2 = event.currentTarget;\n                const [first, last] = getTabbableEdges(container2);\n                const hasTabbableElementsInside = first && last;\n                if (!hasTabbableElementsInside) {\n                    if (focusedElement === container2) event.preventDefault();\n                } else {\n                    if (!event.shiftKey && focusedElement === last) {\n                        event.preventDefault();\n                        if (loop) focus(first, {\n                            select: true\n                        });\n                    } else if (event.shiftKey && focusedElement === first) {\n                        event.preventDefault();\n                        if (loop) focus(last, {\n                            select: true\n                        });\n                    }\n                }\n            }\n        }\n    }[\"FocusScope.useCallback[handleKeyDown]\"], [\n        loop,\n        trapped,\n        focusScope.paused\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        tabIndex: -1,\n        ...scopeProps,\n        ref: composedRefs,\n        onKeyDown: handleKeyDown\n    });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n    const previouslyFocusedElement = document.activeElement;\n    for (const candidate of candidates){\n        focus(candidate, {\n            select\n        });\n        if (document.activeElement !== previouslyFocusedElement) return;\n    }\n}\nfunction getTabbableEdges(container) {\n    const candidates = getTabbableCandidates(container);\n    const first = findVisible(candidates, container);\n    const last = findVisible(candidates.reverse(), container);\n    return [\n        first,\n        last\n    ];\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction findVisible(elements, container) {\n    for (const element of elements){\n        if (!isHidden(element, {\n            upTo: container\n        })) return element;\n    }\n}\nfunction isHidden(node, { upTo }) {\n    if (getComputedStyle(node).visibility === \"hidden\") return true;\n    while(node){\n        if (upTo !== void 0 && node === upTo) return false;\n        if (getComputedStyle(node).display === \"none\") return true;\n        node = node.parentElement;\n    }\n    return false;\n}\nfunction isSelectableInput(element) {\n    return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n    if (element && element.focus) {\n        const previouslyFocusedElement = document.activeElement;\n        element.focus({\n            preventScroll: true\n        });\n        if (element !== previouslyFocusedElement && isSelectableInput(element) && select) element.select();\n    }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n    let stack = [];\n    return {\n        add (focusScope) {\n            const activeFocusScope = stack[0];\n            if (focusScope !== activeFocusScope) {\n                activeFocusScope?.pause();\n            }\n            stack = arrayRemove(stack, focusScope);\n            stack.unshift(focusScope);\n        },\n        remove (focusScope) {\n            stack = arrayRemove(stack, focusScope);\n            stack[0]?.resume();\n        }\n    };\n}\nfunction arrayRemove(array, item) {\n    const updatedArray = [\n        ...array\n    ];\n    const index = updatedArray.indexOf(item);\n    if (index !== -1) {\n        updatedArray.splice(index, 1);\n    }\n    return updatedArray;\n}\nfunction removeLinks(items) {\n    return items.filter((item)=>item.tagName !== \"A\");\n}\nvar Root = FocusScope;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.0_@types+react-dom@19.0.2_@types+react@19.0.1__@types+r_2b75ad9f89a157aafc3bc27cf01c05b6/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\n");

/***/ })

};
;