"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+util-base64@4.0.0";
exports.ids = ["vendor-chunks/@smithy+util-base64@4.0.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/fromBase64.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/fromBase64.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromBase64: () => (/* binding */ fromBase64)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-buffer-from */ \"(rsc)/./node_modules/.pnpm/@smithy+util-buffer-from@4.0.0/node_modules/@smithy/util-buffer-from/dist-es/index.js\");\n\nconst BASE64_REGEX = /^[A-Za-z0-9+/]*={0,2}$/;\nconst fromBase64 = (input) => {\n    if ((input.length * 3) % 4 !== 0) {\n        throw new TypeError(`Incorrect padding on base64 string.`);\n    }\n    if (!BASE64_REGEX.exec(input)) {\n        throw new TypeError(`Invalid base64 string.`);\n    }\n    const buffer = (0,_smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_0__.fromString)(input, \"base64\");\n    return new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWJhc2U2NEA0LjAuMC9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWJhc2U2NC9kaXN0LWVzL2Zyb21CYXNlNjQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0Q7QUFDdEQsdUNBQXVDLElBQUk7QUFDcEM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsb0VBQVU7QUFDN0I7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1iYXNlNjRANC4wLjAvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1iYXNlNjQvZGlzdC1lcy9mcm9tQmFzZTY0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZyb21TdHJpbmcgfSBmcm9tIFwiQHNtaXRoeS91dGlsLWJ1ZmZlci1mcm9tXCI7XG5jb25zdCBCQVNFNjRfUkVHRVggPSAvXltBLVphLXowLTkrL10qPXswLDJ9JC87XG5leHBvcnQgY29uc3QgZnJvbUJhc2U2NCA9IChpbnB1dCkgPT4ge1xuICAgIGlmICgoaW5wdXQubGVuZ3RoICogMykgJSA0ICE9PSAwKSB7XG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYEluY29ycmVjdCBwYWRkaW5nIG9uIGJhc2U2NCBzdHJpbmcuYCk7XG4gICAgfVxuICAgIGlmICghQkFTRTY0X1JFR0VYLmV4ZWMoaW5wdXQpKSB7XG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYEludmFsaWQgYmFzZTY0IHN0cmluZy5gKTtcbiAgICB9XG4gICAgY29uc3QgYnVmZmVyID0gZnJvbVN0cmluZyhpbnB1dCwgXCJiYXNlNjRcIik7XG4gICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGJ1ZmZlci5idWZmZXIsIGJ1ZmZlci5ieXRlT2Zmc2V0LCBidWZmZXIuYnl0ZUxlbmd0aCk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/fromBase64.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromBase64: () => (/* reexport safe */ _fromBase64__WEBPACK_IMPORTED_MODULE_0__.fromBase64),\n/* harmony export */   toBase64: () => (/* reexport safe */ _toBase64__WEBPACK_IMPORTED_MODULE_1__.toBase64)\n/* harmony export */ });\n/* harmony import */ var _fromBase64__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fromBase64 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/fromBase64.js\");\n/* harmony import */ var _toBase64__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./toBase64 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/toBase64.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWJhc2U2NEA0LjAuMC9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWJhc2U2NC9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNkI7QUFDRiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1iYXNlNjRANC4wLjAvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1iYXNlNjQvZGlzdC1lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9mcm9tQmFzZTY0XCI7XG5leHBvcnQgKiBmcm9tIFwiLi90b0Jhc2U2NFwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/toBase64.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/toBase64.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toBase64: () => (/* binding */ toBase64)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-buffer-from */ \"(rsc)/./node_modules/.pnpm/@smithy+util-buffer-from@4.0.0/node_modules/@smithy/util-buffer-from/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n\n\nconst toBase64 = (_input) => {\n    let input;\n    if (typeof _input === \"string\") {\n        input = (0,_smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__.fromUtf8)(_input);\n    }\n    else {\n        input = _input;\n    }\n    if (typeof input !== \"object\" || typeof input.byteOffset !== \"number\" || typeof input.byteLength !== \"number\") {\n        throw new Error(\"@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.\");\n    }\n    return (0,_smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_0__.fromArrayBuffer)(input.buffer, input.byteOffset, input.byteLength).toString(\"base64\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWJhc2U2NEA0LjAuMC9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWJhc2U2NC9kaXN0LWVzL3RvQmFzZTY0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyRDtBQUNkO0FBQ3RDO0FBQ1A7QUFDQTtBQUNBLGdCQUFnQiwyREFBUTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcseUVBQWU7QUFDMUIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtYmFzZTY0QDQuMC4wL25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtYmFzZTY0L2Rpc3QtZXMvdG9CYXNlNjQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZnJvbUFycmF5QnVmZmVyIH0gZnJvbSBcIkBzbWl0aHkvdXRpbC1idWZmZXItZnJvbVwiO1xuaW1wb3J0IHsgZnJvbVV0ZjggfSBmcm9tIFwiQHNtaXRoeS91dGlsLXV0ZjhcIjtcbmV4cG9ydCBjb25zdCB0b0Jhc2U2NCA9IChfaW5wdXQpID0+IHtcbiAgICBsZXQgaW5wdXQ7XG4gICAgaWYgKHR5cGVvZiBfaW5wdXQgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgaW5wdXQgPSBmcm9tVXRmOChfaW5wdXQpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgaW5wdXQgPSBfaW5wdXQ7XG4gICAgfVxuICAgIGlmICh0eXBlb2YgaW5wdXQgIT09IFwib2JqZWN0XCIgfHwgdHlwZW9mIGlucHV0LmJ5dGVPZmZzZXQgIT09IFwibnVtYmVyXCIgfHwgdHlwZW9mIGlucHV0LmJ5dGVMZW5ndGggIT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQHNtaXRoeS91dGlsLWJhc2U2NDogdG9CYXNlNjQgZW5jb2RlciBmdW5jdGlvbiBvbmx5IGFjY2VwdHMgc3RyaW5nIHwgVWludDhBcnJheS5cIik7XG4gICAgfVxuICAgIHJldHVybiBmcm9tQXJyYXlCdWZmZXIoaW5wdXQuYnVmZmVyLCBpbnB1dC5ieXRlT2Zmc2V0LCBpbnB1dC5ieXRlTGVuZ3RoKS50b1N0cmluZyhcImJhc2U2NFwiKTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/toBase64.js\n");

/***/ })

};
;