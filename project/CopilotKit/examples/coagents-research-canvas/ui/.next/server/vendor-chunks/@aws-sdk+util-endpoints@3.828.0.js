"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+util-endpoints@3.828.0";
exports.ids = ["vendor-chunks/@aws-sdk+util-endpoints@3.828.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/aws.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/aws.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   awsEndpointFunctions: () => (/* binding */ awsEndpointFunctions)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/index.js\");\n/* harmony import */ var _lib_aws_isVirtualHostableS3Bucket__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/aws/isVirtualHostableS3Bucket */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/isVirtualHostableS3Bucket.js\");\n/* harmony import */ var _lib_aws_parseArn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/aws/parseArn */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/parseArn.js\");\n/* harmony import */ var _lib_aws_partition__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/aws/partition */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/partition.js\");\n\n\n\n\nconst awsEndpointFunctions = {\n    isVirtualHostableS3Bucket: _lib_aws_isVirtualHostableS3Bucket__WEBPACK_IMPORTED_MODULE_1__.isVirtualHostableS3Bucket,\n    parseArn: _lib_aws_parseArn__WEBPACK_IMPORTED_MODULE_2__.parseArn,\n    partition: _lib_aws_partition__WEBPACK_IMPORTED_MODULE_3__.partition,\n};\n_smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_0__.customEndpointFunctions.aws = awsEndpointFunctions;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy9hd3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBaUU7QUFDZTtBQUNsQztBQUNFO0FBQ3pDO0FBQ1AsK0JBQStCLHlGQUF5QjtBQUN4RCxjQUFjLHVEQUFRO0FBQ3RCLGVBQWUseURBQVM7QUFDeEI7QUFDQSwyRUFBdUIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayt1dGlsLWVuZHBvaW50c0AzLjgyOC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2F3cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjdXN0b21FbmRwb2ludEZ1bmN0aW9ucyB9IGZyb20gXCJAc21pdGh5L3V0aWwtZW5kcG9pbnRzXCI7XG5pbXBvcnQgeyBpc1ZpcnR1YWxIb3N0YWJsZVMzQnVja2V0IH0gZnJvbSBcIi4vbGliL2F3cy9pc1ZpcnR1YWxIb3N0YWJsZVMzQnVja2V0XCI7XG5pbXBvcnQgeyBwYXJzZUFybiB9IGZyb20gXCIuL2xpYi9hd3MvcGFyc2VBcm5cIjtcbmltcG9ydCB7IHBhcnRpdGlvbiB9IGZyb20gXCIuL2xpYi9hd3MvcGFydGl0aW9uXCI7XG5leHBvcnQgY29uc3QgYXdzRW5kcG9pbnRGdW5jdGlvbnMgPSB7XG4gICAgaXNWaXJ0dWFsSG9zdGFibGVTM0J1Y2tldDogaXNWaXJ0dWFsSG9zdGFibGVTM0J1Y2tldCxcbiAgICBwYXJzZUFybjogcGFyc2VBcm4sXG4gICAgcGFydGl0aW9uOiBwYXJ0aXRpb24sXG59O1xuY3VzdG9tRW5kcG9pbnRGdW5jdGlvbnMuYXdzID0gYXdzRW5kcG9pbnRGdW5jdGlvbnM7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/aws.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/index.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/index.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EndpointError: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_4__.EndpointError),\n/* harmony export */   awsEndpointFunctions: () => (/* reexport safe */ _aws__WEBPACK_IMPORTED_MODULE_0__.awsEndpointFunctions),\n/* harmony export */   getUserAgentPrefix: () => (/* reexport safe */ _lib_aws_partition__WEBPACK_IMPORTED_MODULE_1__.getUserAgentPrefix),\n/* harmony export */   isIpAddress: () => (/* reexport safe */ _lib_isIpAddress__WEBPACK_IMPORTED_MODULE_2__.isIpAddress),\n/* harmony export */   partition: () => (/* reexport safe */ _lib_aws_partition__WEBPACK_IMPORTED_MODULE_1__.partition),\n/* harmony export */   resolveEndpoint: () => (/* reexport safe */ _resolveEndpoint__WEBPACK_IMPORTED_MODULE_3__.resolveEndpoint),\n/* harmony export */   setPartitionInfo: () => (/* reexport safe */ _lib_aws_partition__WEBPACK_IMPORTED_MODULE_1__.setPartitionInfo),\n/* harmony export */   useDefaultPartitionInfo: () => (/* reexport safe */ _lib_aws_partition__WEBPACK_IMPORTED_MODULE_1__.useDefaultPartitionInfo)\n/* harmony export */ });\n/* harmony import */ var _aws__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./aws */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/aws.js\");\n/* harmony import */ var _lib_aws_partition__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/aws/partition */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/partition.js\");\n/* harmony import */ var _lib_isIpAddress__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/isIpAddress */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/isIpAddress.js\");\n/* harmony import */ var _resolveEndpoint__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resolveEndpoint */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/resolveEndpoint.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/index.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQXNCO0FBQ2M7QUFDRjtBQUNBO0FBQ1YiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayt1dGlsLWVuZHBvaW50c0AzLjgyOC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2F3c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vbGliL2F3cy9wYXJ0aXRpb25cIjtcbmV4cG9ydCAqIGZyb20gXCIuL2xpYi9pc0lwQWRkcmVzc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vcmVzb2x2ZUVuZHBvaW50XCI7XG5leHBvcnQgKiBmcm9tIFwiLi90eXBlc1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/isVirtualHostableS3Bucket.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/isVirtualHostableS3Bucket.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isVirtualHostableS3Bucket: () => (/* binding */ isVirtualHostableS3Bucket)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/index.js\");\n/* harmony import */ var _isIpAddress__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../isIpAddress */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/isIpAddress.js\");\n\n\nconst isVirtualHostableS3Bucket = (value, allowSubDomains = false) => {\n    if (allowSubDomains) {\n        for (const label of value.split(\".\")) {\n            if (!isVirtualHostableS3Bucket(label)) {\n                return false;\n            }\n        }\n        return true;\n    }\n    if (!(0,_smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_0__.isValidHostLabel)(value)) {\n        return false;\n    }\n    if (value.length < 3 || value.length > 63) {\n        return false;\n    }\n    if (value !== value.toLowerCase()) {\n        return false;\n    }\n    if ((0,_isIpAddress__WEBPACK_IMPORTED_MODULE_1__.isIpAddress)(value)) {\n        return false;\n    }\n    return true;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy9saWIvYXdzL2lzVmlydHVhbEhvc3RhYmxlUzNCdWNrZXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBEO0FBQ2I7QUFDdEM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyx3RUFBZ0I7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEseURBQVc7QUFDbkI7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayt1dGlsLWVuZHBvaW50c0AzLjgyOC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9hd3MvaXNWaXJ0dWFsSG9zdGFibGVTM0J1Y2tldC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc1ZhbGlkSG9zdExhYmVsIH0gZnJvbSBcIkBzbWl0aHkvdXRpbC1lbmRwb2ludHNcIjtcbmltcG9ydCB7IGlzSXBBZGRyZXNzIH0gZnJvbSBcIi4uL2lzSXBBZGRyZXNzXCI7XG5leHBvcnQgY29uc3QgaXNWaXJ0dWFsSG9zdGFibGVTM0J1Y2tldCA9ICh2YWx1ZSwgYWxsb3dTdWJEb21haW5zID0gZmFsc2UpID0+IHtcbiAgICBpZiAoYWxsb3dTdWJEb21haW5zKSB7XG4gICAgICAgIGZvciAoY29uc3QgbGFiZWwgb2YgdmFsdWUuc3BsaXQoXCIuXCIpKSB7XG4gICAgICAgICAgICBpZiAoIWlzVmlydHVhbEhvc3RhYmxlUzNCdWNrZXQobGFiZWwpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICBpZiAoIWlzVmFsaWRIb3N0TGFiZWwodmFsdWUpKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgaWYgKHZhbHVlLmxlbmd0aCA8IDMgfHwgdmFsdWUubGVuZ3RoID4gNjMpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAodmFsdWUgIT09IHZhbHVlLnRvTG93ZXJDYXNlKCkpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAoaXNJcEFkZHJlc3ModmFsdWUpKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/isVirtualHostableS3Bucket.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/parseArn.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/parseArn.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseArn: () => (/* binding */ parseArn)\n/* harmony export */ });\nconst ARN_DELIMITER = \":\";\nconst RESOURCE_DELIMITER = \"/\";\nconst parseArn = (value) => {\n    const segments = value.split(ARN_DELIMITER);\n    if (segments.length < 6)\n        return null;\n    const [arn, partition, service, region, accountId, ...resourcePath] = segments;\n    if (arn !== \"arn\" || partition === \"\" || service === \"\" || resourcePath.join(ARN_DELIMITER) === \"\")\n        return null;\n    const resourceId = resourcePath.map((resource) => resource.split(RESOURCE_DELIMITER)).flat();\n    return {\n        partition,\n        service,\n        region,\n        accountId,\n        resourceId,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy9saWIvYXdzL3BhcnNlQXJuLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayt1dGlsLWVuZHBvaW50c0AzLjgyOC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9hd3MvcGFyc2VBcm4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgQVJOX0RFTElNSVRFUiA9IFwiOlwiO1xuY29uc3QgUkVTT1VSQ0VfREVMSU1JVEVSID0gXCIvXCI7XG5leHBvcnQgY29uc3QgcGFyc2VBcm4gPSAodmFsdWUpID0+IHtcbiAgICBjb25zdCBzZWdtZW50cyA9IHZhbHVlLnNwbGl0KEFSTl9ERUxJTUlURVIpO1xuICAgIGlmIChzZWdtZW50cy5sZW5ndGggPCA2KVxuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICBjb25zdCBbYXJuLCBwYXJ0aXRpb24sIHNlcnZpY2UsIHJlZ2lvbiwgYWNjb3VudElkLCAuLi5yZXNvdXJjZVBhdGhdID0gc2VnbWVudHM7XG4gICAgaWYgKGFybiAhPT0gXCJhcm5cIiB8fCBwYXJ0aXRpb24gPT09IFwiXCIgfHwgc2VydmljZSA9PT0gXCJcIiB8fCByZXNvdXJjZVBhdGguam9pbihBUk5fREVMSU1JVEVSKSA9PT0gXCJcIilcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgY29uc3QgcmVzb3VyY2VJZCA9IHJlc291cmNlUGF0aC5tYXAoKHJlc291cmNlKSA9PiByZXNvdXJjZS5zcGxpdChSRVNPVVJDRV9ERUxJTUlURVIpKS5mbGF0KCk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgcGFydGl0aW9uLFxuICAgICAgICBzZXJ2aWNlLFxuICAgICAgICByZWdpb24sXG4gICAgICAgIGFjY291bnRJZCxcbiAgICAgICAgcmVzb3VyY2VJZCxcbiAgICB9O1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/parseArn.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/partition.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/partition.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUserAgentPrefix: () => (/* binding */ getUserAgentPrefix),\n/* harmony export */   partition: () => (/* binding */ partition),\n/* harmony export */   setPartitionInfo: () => (/* binding */ setPartitionInfo),\n/* harmony export */   useDefaultPartitionInfo: () => (/* binding */ useDefaultPartitionInfo)\n/* harmony export */ });\n/* harmony import */ var _partitions_json__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./partitions.json */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/partitions.json\");\n\nlet selectedPartitionsInfo = _partitions_json__WEBPACK_IMPORTED_MODULE_0__;\nlet selectedUserAgentPrefix = \"\";\nconst partition = (value) => {\n    const { partitions } = selectedPartitionsInfo;\n    for (const partition of partitions) {\n        const { regions, outputs } = partition;\n        for (const [region, regionData] of Object.entries(regions)) {\n            if (region === value) {\n                return {\n                    ...outputs,\n                    ...regionData,\n                };\n            }\n        }\n    }\n    for (const partition of partitions) {\n        const { regionRegex, outputs } = partition;\n        if (new RegExp(regionRegex).test(value)) {\n            return {\n                ...outputs,\n            };\n        }\n    }\n    const DEFAULT_PARTITION = partitions.find((partition) => partition.id === \"aws\");\n    if (!DEFAULT_PARTITION) {\n        throw new Error(\"Provided region was not found in the partition array or regex,\" +\n            \" and default partition with id 'aws' doesn't exist.\");\n    }\n    return {\n        ...DEFAULT_PARTITION.outputs,\n    };\n};\nconst setPartitionInfo = (partitionsInfo, userAgentPrefix = \"\") => {\n    selectedPartitionsInfo = partitionsInfo;\n    selectedUserAgentPrefix = userAgentPrefix;\n};\nconst useDefaultPartitionInfo = () => {\n    setPartitionInfo(_partitions_json__WEBPACK_IMPORTED_MODULE_0__, \"\");\n};\nconst getUserAgentPrefix = () => selectedUserAgentPrefix;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/partition.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/isIpAddress.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/isIpAddress.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isIpAddress: () => (/* reexport safe */ _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_0__.isIpAddress)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/index.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy9saWIvaXNJcEFkZHJlc3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUQiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayt1dGlsLWVuZHBvaW50c0AzLjgyOC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9pc0lwQWRkcmVzcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBpc0lwQWRkcmVzcyB9IGZyb20gXCJAc21pdGh5L3V0aWwtZW5kcG9pbnRzXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/isIpAddress.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/resolveEndpoint.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/resolveEndpoint.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveEndpoint: () => (/* reexport safe */ _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_0__.resolveEndpoint)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/index.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy9yZXNvbHZlRW5kcG9pbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUQiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayt1dGlsLWVuZHBvaW50c0AzLjgyOC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3Jlc29sdmVFbmRwb2ludC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyByZXNvbHZlRW5kcG9pbnQgfSBmcm9tIFwiQHNtaXRoeS91dGlsLWVuZHBvaW50c1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/resolveEndpoint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/EndpointError.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/EndpointError.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EndpointError: () => (/* reexport safe */ _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_0__.EndpointError)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/index.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9FbmRwb2ludEVycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXVEIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9FbmRwb2ludEVycm9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IEVuZHBvaW50RXJyb3IgfSBmcm9tIFwiQHNtaXRoeS91dGlsLWVuZHBvaW50c1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/EndpointError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/EndpointRuleObject.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/EndpointRuleObject.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9FbmRwb2ludFJ1bGVPYmplY3QuanMiLCJtYXBwaW5ncyI6IjtBQUFVIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9FbmRwb2ludFJ1bGVPYmplY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/EndpointRuleObject.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/ErrorRuleObject.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/ErrorRuleObject.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9FcnJvclJ1bGVPYmplY3QuanMiLCJtYXBwaW5ncyI6IjtBQUFVIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9FcnJvclJ1bGVPYmplY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/ErrorRuleObject.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/RuleSetObject.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/RuleSetObject.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9SdWxlU2V0T2JqZWN0LmpzIiwibWFwcGluZ3MiOiI7QUFBVSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3V0aWwtZW5kcG9pbnRzQDMuODI4LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvdHlwZXMvUnVsZVNldE9iamVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge307XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/RuleSetObject.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/TreeRuleObject.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/TreeRuleObject.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9UcmVlUnVsZU9iamVjdC5qcyIsIm1hcHBpbmdzIjoiO0FBQVUiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayt1dGlsLWVuZHBvaW50c0AzLjgyOC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3R5cGVzL1RyZWVSdWxlT2JqZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/TreeRuleObject.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/index.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/index.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EndpointError: () => (/* reexport safe */ _EndpointError__WEBPACK_IMPORTED_MODULE_0__.EndpointError)\n/* harmony export */ });\n/* harmony import */ var _EndpointError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EndpointError */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/EndpointError.js\");\n/* harmony import */ var _EndpointRuleObject__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EndpointRuleObject */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/EndpointRuleObject.js\");\n/* harmony import */ var _ErrorRuleObject__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ErrorRuleObject */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/ErrorRuleObject.js\");\n/* harmony import */ var _RuleSetObject__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RuleSetObject */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/RuleSetObject.js\");\n/* harmony import */ var _TreeRuleObject__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TreeRuleObject */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/TreeRuleObject.js\");\n/* harmony import */ var _shared__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./shared */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/shared.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQWdDO0FBQ0s7QUFDSDtBQUNGO0FBQ0M7QUFDUiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3V0aWwtZW5kcG9pbnRzQDMuODI4LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvdHlwZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vRW5kcG9pbnRFcnJvclwiO1xuZXhwb3J0ICogZnJvbSBcIi4vRW5kcG9pbnRSdWxlT2JqZWN0XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9FcnJvclJ1bGVPYmplY3RcIjtcbmV4cG9ydCAqIGZyb20gXCIuL1J1bGVTZXRPYmplY3RcIjtcbmV4cG9ydCAqIGZyb20gXCIuL1RyZWVSdWxlT2JqZWN0XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zaGFyZWRcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/shared.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/shared.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9zaGFyZWQuanMiLCJtYXBwaW5ncyI6IjtBQUFVIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC1lbmRwb2ludHNAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9zaGFyZWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/types/shared.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/partitions.json":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/lib/aws/partitions.json ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"partitions":[{"id":"aws","outputs":{"dnsSuffix":"amazonaws.com","dualStackDnsSuffix":"api.aws","implicitGlobalRegion":"us-east-1","name":"aws","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^(us|eu|ap|sa|ca|me|af|il|mx)\\\\-\\\\w+\\\\-\\\\d+$","regions":{"af-south-1":{"description":"Africa (Cape Town)"},"ap-east-1":{"description":"Asia Pacific (Hong Kong)"},"ap-east-2":{"description":"Asia Pacific (Taipei)"},"ap-northeast-1":{"description":"Asia Pacific (Tokyo)"},"ap-northeast-2":{"description":"Asia Pacific (Seoul)"},"ap-northeast-3":{"description":"Asia Pacific (Osaka)"},"ap-south-1":{"description":"Asia Pacific (Mumbai)"},"ap-south-2":{"description":"Asia Pacific (Hyderabad)"},"ap-southeast-1":{"description":"Asia Pacific (Singapore)"},"ap-southeast-2":{"description":"Asia Pacific (Sydney)"},"ap-southeast-3":{"description":"Asia Pacific (Jakarta)"},"ap-southeast-4":{"description":"Asia Pacific (Melbourne)"},"ap-southeast-5":{"description":"Asia Pacific (Malaysia)"},"ap-southeast-7":{"description":"Asia Pacific (Thailand)"},"aws-global":{"description":"AWS Standard global region"},"ca-central-1":{"description":"Canada (Central)"},"ca-west-1":{"description":"Canada West (Calgary)"},"eu-central-1":{"description":"Europe (Frankfurt)"},"eu-central-2":{"description":"Europe (Zurich)"},"eu-north-1":{"description":"Europe (Stockholm)"},"eu-south-1":{"description":"Europe (Milan)"},"eu-south-2":{"description":"Europe (Spain)"},"eu-west-1":{"description":"Europe (Ireland)"},"eu-west-2":{"description":"Europe (London)"},"eu-west-3":{"description":"Europe (Paris)"},"il-central-1":{"description":"Israel (Tel Aviv)"},"me-central-1":{"description":"Middle East (UAE)"},"me-south-1":{"description":"Middle East (Bahrain)"},"mx-central-1":{"description":"Mexico (Central)"},"sa-east-1":{"description":"South America (Sao Paulo)"},"us-east-1":{"description":"US East (N. Virginia)"},"us-east-2":{"description":"US East (Ohio)"},"us-west-1":{"description":"US West (N. California)"},"us-west-2":{"description":"US West (Oregon)"}}},{"id":"aws-cn","outputs":{"dnsSuffix":"amazonaws.com.cn","dualStackDnsSuffix":"api.amazonwebservices.com.cn","implicitGlobalRegion":"cn-northwest-1","name":"aws-cn","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^cn\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-cn-global":{"description":"AWS China global region"},"cn-north-1":{"description":"China (Beijing)"},"cn-northwest-1":{"description":"China (Ningxia)"}}},{"id":"aws-us-gov","outputs":{"dnsSuffix":"amazonaws.com","dualStackDnsSuffix":"api.aws","implicitGlobalRegion":"us-gov-west-1","name":"aws-us-gov","supportsDualStack":true,"supportsFIPS":true},"regionRegex":"^us\\\\-gov\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-us-gov-global":{"description":"AWS GovCloud (US) global region"},"us-gov-east-1":{"description":"AWS GovCloud (US-East)"},"us-gov-west-1":{"description":"AWS GovCloud (US-West)"}}},{"id":"aws-iso","outputs":{"dnsSuffix":"c2s.ic.gov","dualStackDnsSuffix":"c2s.ic.gov","implicitGlobalRegion":"us-iso-east-1","name":"aws-iso","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-iso\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-global":{"description":"AWS ISO (US) global region"},"us-iso-east-1":{"description":"US ISO East"},"us-iso-west-1":{"description":"US ISO WEST"}}},{"id":"aws-iso-b","outputs":{"dnsSuffix":"sc2s.sgov.gov","dualStackDnsSuffix":"sc2s.sgov.gov","implicitGlobalRegion":"us-isob-east-1","name":"aws-iso-b","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-isob\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-b-global":{"description":"AWS ISOB (US) global region"},"us-isob-east-1":{"description":"US ISOB East (Ohio)"}}},{"id":"aws-iso-e","outputs":{"dnsSuffix":"cloud.adc-e.uk","dualStackDnsSuffix":"cloud.adc-e.uk","implicitGlobalRegion":"eu-isoe-west-1","name":"aws-iso-e","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^eu\\\\-isoe\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-e-global":{"description":"AWS ISOE (Europe) global region"},"eu-isoe-west-1":{"description":"EU ISOE West"}}},{"id":"aws-iso-f","outputs":{"dnsSuffix":"csp.hci.ic.gov","dualStackDnsSuffix":"csp.hci.ic.gov","implicitGlobalRegion":"us-isof-south-1","name":"aws-iso-f","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^us\\\\-isof\\\\-\\\\w+\\\\-\\\\d+$","regions":{"aws-iso-f-global":{"description":"AWS ISOF global region"},"us-isof-east-1":{"description":"US ISOF EAST"},"us-isof-south-1":{"description":"US ISOF SOUTH"}}},{"id":"aws-eusc","outputs":{"dnsSuffix":"amazonaws.eu","dualStackDnsSuffix":"amazonaws.eu","implicitGlobalRegion":"eusc-de-east-1","name":"aws-eusc","supportsDualStack":false,"supportsFIPS":true},"regionRegex":"^eusc\\\\-(de)\\\\-\\\\w+\\\\-\\\\d+$","regions":{"eusc-de-east-1":{"description":"EU (Germany)"}}}],"version":"1.1"}');

/***/ })

};
;