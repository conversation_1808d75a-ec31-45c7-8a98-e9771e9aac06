"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+credential-provider-imds@4.0.6";
exports.ids = ["vendor-chunks/@smithy+credential-provider-imds@4.0.6"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/Endpoint.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/Endpoint.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Endpoint: () => (/* binding */ Endpoint)\n/* harmony export */ });\nvar Endpoint;\n(function (Endpoint) {\n    Endpoint[\"IPv4\"] = \"http://***************\";\n    Endpoint[\"IPv6\"] = \"http://[fd00:ec2::254]\";\n})(Endpoint || (Endpoint = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/Endpoint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/EndpointConfigOptions.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/EndpointConfigOptions.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONFIG_ENDPOINT_NAME: () => (/* binding */ CONFIG_ENDPOINT_NAME),\n/* harmony export */   ENDPOINT_CONFIG_OPTIONS: () => (/* binding */ ENDPOINT_CONFIG_OPTIONS),\n/* harmony export */   ENV_ENDPOINT_NAME: () => (/* binding */ ENV_ENDPOINT_NAME)\n/* harmony export */ });\nconst ENV_ENDPOINT_NAME = \"AWS_EC2_METADATA_SERVICE_ENDPOINT\";\nconst CONFIG_ENDPOINT_NAME = \"ec2_metadata_service_endpoint\";\nconst ENDPOINT_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => env[ENV_ENDPOINT_NAME],\n    configFileSelector: (profile) => profile[CONFIG_ENDPOINT_NAME],\n    default: undefined,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjcmVkZW50aWFsLXByb3ZpZGVyLWltZHNANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY3JlZGVudGlhbC1wcm92aWRlci1pbWRzL2Rpc3QtZXMvY29uZmlnL0VuZHBvaW50Q29uZmlnT3B0aW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBTztBQUNBO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY3JlZGVudGlhbC1wcm92aWRlci1pbWRzQDQuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NyZWRlbnRpYWwtcHJvdmlkZXItaW1kcy9kaXN0LWVzL2NvbmZpZy9FbmRwb2ludENvbmZpZ09wdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IEVOVl9FTkRQT0lOVF9OQU1FID0gXCJBV1NfRUMyX01FVEFEQVRBX1NFUlZJQ0VfRU5EUE9JTlRcIjtcbmV4cG9ydCBjb25zdCBDT05GSUdfRU5EUE9JTlRfTkFNRSA9IFwiZWMyX21ldGFkYXRhX3NlcnZpY2VfZW5kcG9pbnRcIjtcbmV4cG9ydCBjb25zdCBFTkRQT0lOVF9DT05GSUdfT1BUSU9OUyA9IHtcbiAgICBlbnZpcm9ubWVudFZhcmlhYmxlU2VsZWN0b3I6IChlbnYpID0+IGVudltFTlZfRU5EUE9JTlRfTkFNRV0sXG4gICAgY29uZmlnRmlsZVNlbGVjdG9yOiAocHJvZmlsZSkgPT4gcHJvZmlsZVtDT05GSUdfRU5EUE9JTlRfTkFNRV0sXG4gICAgZGVmYXVsdDogdW5kZWZpbmVkLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/EndpointConfigOptions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/EndpointMode.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/EndpointMode.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EndpointMode: () => (/* binding */ EndpointMode)\n/* harmony export */ });\nvar EndpointMode;\n(function (EndpointMode) {\n    EndpointMode[\"IPv4\"] = \"IPv4\";\n    EndpointMode[\"IPv6\"] = \"IPv6\";\n})(EndpointMode || (EndpointMode = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjcmVkZW50aWFsLXByb3ZpZGVyLWltZHNANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY3JlZGVudGlhbC1wcm92aWRlci1pbWRzL2Rpc3QtZXMvY29uZmlnL0VuZHBvaW50TW9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxDQUFDLG9DQUFvQyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY3JlZGVudGlhbC1wcm92aWRlci1pbWRzQDQuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NyZWRlbnRpYWwtcHJvdmlkZXItaW1kcy9kaXN0LWVzL2NvbmZpZy9FbmRwb2ludE1vZGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBFbmRwb2ludE1vZGU7XG4oZnVuY3Rpb24gKEVuZHBvaW50TW9kZSkge1xuICAgIEVuZHBvaW50TW9kZVtcIklQdjRcIl0gPSBcIklQdjRcIjtcbiAgICBFbmRwb2ludE1vZGVbXCJJUHY2XCJdID0gXCJJUHY2XCI7XG59KShFbmRwb2ludE1vZGUgfHwgKEVuZHBvaW50TW9kZSA9IHt9KSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/EndpointMode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/EndpointModeConfigOptions.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/EndpointModeConfigOptions.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONFIG_ENDPOINT_MODE_NAME: () => (/* binding */ CONFIG_ENDPOINT_MODE_NAME),\n/* harmony export */   ENDPOINT_MODE_CONFIG_OPTIONS: () => (/* binding */ ENDPOINT_MODE_CONFIG_OPTIONS),\n/* harmony export */   ENV_ENDPOINT_MODE_NAME: () => (/* binding */ ENV_ENDPOINT_MODE_NAME)\n/* harmony export */ });\n/* harmony import */ var _EndpointMode__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EndpointMode */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/EndpointMode.js\");\n\nconst ENV_ENDPOINT_MODE_NAME = \"AWS_EC2_METADATA_SERVICE_ENDPOINT_MODE\";\nconst CONFIG_ENDPOINT_MODE_NAME = \"ec2_metadata_service_endpoint_mode\";\nconst ENDPOINT_MODE_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => env[ENV_ENDPOINT_MODE_NAME],\n    configFileSelector: (profile) => profile[CONFIG_ENDPOINT_MODE_NAME],\n    default: _EndpointMode__WEBPACK_IMPORTED_MODULE_0__.EndpointMode.IPv4,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjcmVkZW50aWFsLXByb3ZpZGVyLWltZHNANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY3JlZGVudGlhbC1wcm92aWRlci1pbWRzL2Rpc3QtZXMvY29uZmlnL0VuZHBvaW50TW9kZUNvbmZpZ09wdGlvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE4QztBQUN2QztBQUNBO0FBQ0E7QUFDUDtBQUNBO0FBQ0EsYUFBYSx1REFBWTtBQUN6QiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY3JlZGVudGlhbC1wcm92aWRlci1pbWRzQDQuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NyZWRlbnRpYWwtcHJvdmlkZXItaW1kcy9kaXN0LWVzL2NvbmZpZy9FbmRwb2ludE1vZGVDb25maWdPcHRpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEVuZHBvaW50TW9kZSB9IGZyb20gXCIuL0VuZHBvaW50TW9kZVwiO1xuZXhwb3J0IGNvbnN0IEVOVl9FTkRQT0lOVF9NT0RFX05BTUUgPSBcIkFXU19FQzJfTUVUQURBVEFfU0VSVklDRV9FTkRQT0lOVF9NT0RFXCI7XG5leHBvcnQgY29uc3QgQ09ORklHX0VORFBPSU5UX01PREVfTkFNRSA9IFwiZWMyX21ldGFkYXRhX3NlcnZpY2VfZW5kcG9pbnRfbW9kZVwiO1xuZXhwb3J0IGNvbnN0IEVORFBPSU5UX01PREVfQ09ORklHX09QVElPTlMgPSB7XG4gICAgZW52aXJvbm1lbnRWYXJpYWJsZVNlbGVjdG9yOiAoZW52KSA9PiBlbnZbRU5WX0VORFBPSU5UX01PREVfTkFNRV0sXG4gICAgY29uZmlnRmlsZVNlbGVjdG9yOiAocHJvZmlsZSkgPT4gcHJvZmlsZVtDT05GSUdfRU5EUE9JTlRfTU9ERV9OQU1FXSxcbiAgICBkZWZhdWx0OiBFbmRwb2ludE1vZGUuSVB2NCxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/EndpointModeConfigOptions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/error/InstanceMetadataV1FallbackError.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/error/InstanceMetadataV1FallbackError.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InstanceMetadataV1FallbackError: () => (/* binding */ InstanceMetadataV1FallbackError)\n/* harmony export */ });\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n\nclass InstanceMetadataV1FallbackError extends _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError {\n    constructor(message, tryNextLink = true) {\n        super(message, tryNextLink);\n        this.tryNextLink = tryNextLink;\n        this.name = \"InstanceMetadataV1FallbackError\";\n        Object.setPrototypeOf(this, InstanceMetadataV1FallbackError.prototype);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjcmVkZW50aWFsLXByb3ZpZGVyLWltZHNANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY3JlZGVudGlhbC1wcm92aWRlci1pbWRzL2Rpc3QtZXMvZXJyb3IvSW5zdGFuY2VNZXRhZGF0YVYxRmFsbGJhY2tFcnJvci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRTtBQUM5RCw4Q0FBOEMsK0VBQXdCO0FBQzdFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjcmVkZW50aWFsLXByb3ZpZGVyLWltZHNANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY3JlZGVudGlhbC1wcm92aWRlci1pbWRzL2Rpc3QtZXMvZXJyb3IvSW5zdGFuY2VNZXRhZGF0YVYxRmFsbGJhY2tFcnJvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDcmVkZW50aWFsc1Byb3ZpZGVyRXJyb3IgfSBmcm9tIFwiQHNtaXRoeS9wcm9wZXJ0eS1wcm92aWRlclwiO1xuZXhwb3J0IGNsYXNzIEluc3RhbmNlTWV0YWRhdGFWMUZhbGxiYWNrRXJyb3IgZXh0ZW5kcyBDcmVkZW50aWFsc1Byb3ZpZGVyRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIHRyeU5leHRMaW5rID0gdHJ1ZSkge1xuICAgICAgICBzdXBlcihtZXNzYWdlLCB0cnlOZXh0TGluayk7XG4gICAgICAgIHRoaXMudHJ5TmV4dExpbmsgPSB0cnlOZXh0TGluaztcbiAgICAgICAgdGhpcy5uYW1lID0gXCJJbnN0YW5jZU1ldGFkYXRhVjFGYWxsYmFja0Vycm9yXCI7XG4gICAgICAgIE9iamVjdC5zZXRQcm90b3R5cGVPZih0aGlzLCBJbnN0YW5jZU1ldGFkYXRhVjFGYWxsYmFja0Vycm9yLnByb3RvdHlwZSk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/error/InstanceMetadataV1FallbackError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/fromContainerMetadata.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/fromContainerMetadata.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ENV_CMDS_AUTH_TOKEN: () => (/* binding */ ENV_CMDS_AUTH_TOKEN),\n/* harmony export */   ENV_CMDS_FULL_URI: () => (/* binding */ ENV_CMDS_FULL_URI),\n/* harmony export */   ENV_CMDS_RELATIVE_URI: () => (/* binding */ ENV_CMDS_RELATIVE_URI),\n/* harmony export */   fromContainerMetadata: () => (/* binding */ fromContainerMetadata)\n/* harmony export */ });\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! url */ \"url\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(url__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _remoteProvider_httpRequest__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./remoteProvider/httpRequest */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/httpRequest.js\");\n/* harmony import */ var _remoteProvider_ImdsCredentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./remoteProvider/ImdsCredentials */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/ImdsCredentials.js\");\n/* harmony import */ var _remoteProvider_RemoteProviderInit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./remoteProvider/RemoteProviderInit */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/RemoteProviderInit.js\");\n/* harmony import */ var _remoteProvider_retry__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./remoteProvider/retry */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/retry.js\");\n\n\n\n\n\n\nconst ENV_CMDS_FULL_URI = \"AWS_CONTAINER_CREDENTIALS_FULL_URI\";\nconst ENV_CMDS_RELATIVE_URI = \"AWS_CONTAINER_CREDENTIALS_RELATIVE_URI\";\nconst ENV_CMDS_AUTH_TOKEN = \"AWS_CONTAINER_AUTHORIZATION_TOKEN\";\nconst fromContainerMetadata = (init = {}) => {\n    const { timeout, maxRetries } = (0,_remoteProvider_RemoteProviderInit__WEBPACK_IMPORTED_MODULE_4__.providerConfigFromInit)(init);\n    return () => (0,_remoteProvider_retry__WEBPACK_IMPORTED_MODULE_5__.retry)(async () => {\n        const requestOptions = await getCmdsUri({ logger: init.logger });\n        const credsResponse = JSON.parse(await requestFromEcsImds(timeout, requestOptions));\n        if (!(0,_remoteProvider_ImdsCredentials__WEBPACK_IMPORTED_MODULE_3__.isImdsCredentials)(credsResponse)) {\n            throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(\"Invalid response received from instance metadata service.\", {\n                logger: init.logger,\n            });\n        }\n        return (0,_remoteProvider_ImdsCredentials__WEBPACK_IMPORTED_MODULE_3__.fromImdsCredentials)(credsResponse);\n    }, maxRetries);\n};\nconst requestFromEcsImds = async (timeout, options) => {\n    if (process.env[ENV_CMDS_AUTH_TOKEN]) {\n        options.headers = {\n            ...options.headers,\n            Authorization: process.env[ENV_CMDS_AUTH_TOKEN],\n        };\n    }\n    const buffer = await (0,_remoteProvider_httpRequest__WEBPACK_IMPORTED_MODULE_2__.httpRequest)({\n        ...options,\n        timeout,\n    });\n    return buffer.toString();\n};\nconst CMDS_IP = \"*************\";\nconst GREENGRASS_HOSTS = {\n    localhost: true,\n    \"127.0.0.1\": true,\n};\nconst GREENGRASS_PROTOCOLS = {\n    \"http:\": true,\n    \"https:\": true,\n};\nconst getCmdsUri = async ({ logger }) => {\n    if (process.env[ENV_CMDS_RELATIVE_URI]) {\n        return {\n            hostname: CMDS_IP,\n            path: process.env[ENV_CMDS_RELATIVE_URI],\n        };\n    }\n    if (process.env[ENV_CMDS_FULL_URI]) {\n        const parsed = (0,url__WEBPACK_IMPORTED_MODULE_1__.parse)(process.env[ENV_CMDS_FULL_URI]);\n        if (!parsed.hostname || !(parsed.hostname in GREENGRASS_HOSTS)) {\n            throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`${parsed.hostname} is not a valid container metadata service hostname`, {\n                tryNextLink: false,\n                logger,\n            });\n        }\n        if (!parsed.protocol || !(parsed.protocol in GREENGRASS_PROTOCOLS)) {\n            throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`${parsed.protocol} is not a valid container metadata service protocol`, {\n                tryNextLink: false,\n                logger,\n            });\n        }\n        return {\n            ...parsed,\n            port: parsed.port ? parseInt(parsed.port, 10) : undefined,\n        };\n    }\n    throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(\"The container metadata credential provider cannot be used unless\" +\n        ` the ${ENV_CMDS_RELATIVE_URI} or ${ENV_CMDS_FULL_URI} environment` +\n        \" variable is set\", {\n        tryNextLink: false,\n        logger,\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/fromContainerMetadata.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/fromInstanceMetadata.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/fromInstanceMetadata.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromInstanceMetadata: () => (/* binding */ fromInstanceMetadata)\n/* harmony export */ });\n/* harmony import */ var _smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/node-config-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+node-config-provider@4.1.3/node_modules/@smithy/node-config-provider/dist-es/index.js\");\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var _error_InstanceMetadataV1FallbackError__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./error/InstanceMetadataV1FallbackError */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/error/InstanceMetadataV1FallbackError.js\");\n/* harmony import */ var _remoteProvider_httpRequest__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./remoteProvider/httpRequest */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/httpRequest.js\");\n/* harmony import */ var _remoteProvider_ImdsCredentials__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./remoteProvider/ImdsCredentials */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/ImdsCredentials.js\");\n/* harmony import */ var _remoteProvider_RemoteProviderInit__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./remoteProvider/RemoteProviderInit */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/RemoteProviderInit.js\");\n/* harmony import */ var _remoteProvider_retry__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./remoteProvider/retry */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/retry.js\");\n/* harmony import */ var _utils_getInstanceMetadataEndpoint__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/getInstanceMetadataEndpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/utils/getInstanceMetadataEndpoint.js\");\n/* harmony import */ var _utils_staticStabilityProvider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/staticStabilityProvider */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/utils/staticStabilityProvider.js\");\n\n\n\n\n\n\n\n\n\nconst IMDS_PATH = \"/latest/meta-data/iam/security-credentials/\";\nconst IMDS_TOKEN_PATH = \"/latest/api/token\";\nconst AWS_EC2_METADATA_V1_DISABLED = \"AWS_EC2_METADATA_V1_DISABLED\";\nconst PROFILE_AWS_EC2_METADATA_V1_DISABLED = \"ec2_metadata_v1_disabled\";\nconst X_AWS_EC2_METADATA_TOKEN = \"x-aws-ec2-metadata-token\";\nconst fromInstanceMetadata = (init = {}) => (0,_utils_staticStabilityProvider__WEBPACK_IMPORTED_MODULE_8__.staticStabilityProvider)(getInstanceMetadataProvider(init), { logger: init.logger });\nconst getInstanceMetadataProvider = (init = {}) => {\n    let disableFetchToken = false;\n    const { logger, profile } = init;\n    const { timeout, maxRetries } = (0,_remoteProvider_RemoteProviderInit__WEBPACK_IMPORTED_MODULE_5__.providerConfigFromInit)(init);\n    const getCredentials = async (maxRetries, options) => {\n        const isImdsV1Fallback = disableFetchToken || options.headers?.[X_AWS_EC2_METADATA_TOKEN] == null;\n        if (isImdsV1Fallback) {\n            let fallbackBlockedFromProfile = false;\n            let fallbackBlockedFromProcessEnv = false;\n            const configValue = await (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_0__.loadConfig)({\n                environmentVariableSelector: (env) => {\n                    const envValue = env[AWS_EC2_METADATA_V1_DISABLED];\n                    fallbackBlockedFromProcessEnv = !!envValue && envValue !== \"false\";\n                    if (envValue === undefined) {\n                        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_1__.CredentialsProviderError(`${AWS_EC2_METADATA_V1_DISABLED} not set in env, checking config file next.`, { logger: init.logger });\n                    }\n                    return fallbackBlockedFromProcessEnv;\n                },\n                configFileSelector: (profile) => {\n                    const profileValue = profile[PROFILE_AWS_EC2_METADATA_V1_DISABLED];\n                    fallbackBlockedFromProfile = !!profileValue && profileValue !== \"false\";\n                    return fallbackBlockedFromProfile;\n                },\n                default: false,\n            }, {\n                profile,\n            })();\n            if (init.ec2MetadataV1Disabled || configValue) {\n                const causes = [];\n                if (init.ec2MetadataV1Disabled)\n                    causes.push(\"credential provider initialization (runtime option ec2MetadataV1Disabled)\");\n                if (fallbackBlockedFromProfile)\n                    causes.push(`config file profile (${PROFILE_AWS_EC2_METADATA_V1_DISABLED})`);\n                if (fallbackBlockedFromProcessEnv)\n                    causes.push(`process environment variable (${AWS_EC2_METADATA_V1_DISABLED})`);\n                throw new _error_InstanceMetadataV1FallbackError__WEBPACK_IMPORTED_MODULE_2__.InstanceMetadataV1FallbackError(`AWS EC2 Metadata v1 fallback has been blocked by AWS SDK configuration in the following: [${causes.join(\", \")}].`);\n            }\n        }\n        const imdsProfile = (await (0,_remoteProvider_retry__WEBPACK_IMPORTED_MODULE_6__.retry)(async () => {\n            let profile;\n            try {\n                profile = await getProfile(options);\n            }\n            catch (err) {\n                if (err.statusCode === 401) {\n                    disableFetchToken = false;\n                }\n                throw err;\n            }\n            return profile;\n        }, maxRetries)).trim();\n        return (0,_remoteProvider_retry__WEBPACK_IMPORTED_MODULE_6__.retry)(async () => {\n            let creds;\n            try {\n                creds = await getCredentialsFromProfile(imdsProfile, options, init);\n            }\n            catch (err) {\n                if (err.statusCode === 401) {\n                    disableFetchToken = false;\n                }\n                throw err;\n            }\n            return creds;\n        }, maxRetries);\n    };\n    return async () => {\n        const endpoint = await (0,_utils_getInstanceMetadataEndpoint__WEBPACK_IMPORTED_MODULE_7__.getInstanceMetadataEndpoint)();\n        if (disableFetchToken) {\n            logger?.debug(\"AWS SDK Instance Metadata\", \"using v1 fallback (no token fetch)\");\n            return getCredentials(maxRetries, { ...endpoint, timeout });\n        }\n        else {\n            let token;\n            try {\n                token = (await getMetadataToken({ ...endpoint, timeout })).toString();\n            }\n            catch (error) {\n                if (error?.statusCode === 400) {\n                    throw Object.assign(error, {\n                        message: \"EC2 Metadata token request returned error\",\n                    });\n                }\n                else if (error.message === \"TimeoutError\" || [403, 404, 405].includes(error.statusCode)) {\n                    disableFetchToken = true;\n                }\n                logger?.debug(\"AWS SDK Instance Metadata\", \"using v1 fallback (initial)\");\n                return getCredentials(maxRetries, { ...endpoint, timeout });\n            }\n            return getCredentials(maxRetries, {\n                ...endpoint,\n                headers: {\n                    [X_AWS_EC2_METADATA_TOKEN]: token,\n                },\n                timeout,\n            });\n        }\n    };\n};\nconst getMetadataToken = async (options) => (0,_remoteProvider_httpRequest__WEBPACK_IMPORTED_MODULE_3__.httpRequest)({\n    ...options,\n    path: IMDS_TOKEN_PATH,\n    method: \"PUT\",\n    headers: {\n        \"x-aws-ec2-metadata-token-ttl-seconds\": \"21600\",\n    },\n});\nconst getProfile = async (options) => (await (0,_remoteProvider_httpRequest__WEBPACK_IMPORTED_MODULE_3__.httpRequest)({ ...options, path: IMDS_PATH })).toString();\nconst getCredentialsFromProfile = async (profile, options, init) => {\n    const credentialsResponse = JSON.parse((await (0,_remoteProvider_httpRequest__WEBPACK_IMPORTED_MODULE_3__.httpRequest)({\n        ...options,\n        path: IMDS_PATH + profile,\n    })).toString());\n    if (!(0,_remoteProvider_ImdsCredentials__WEBPACK_IMPORTED_MODULE_4__.isImdsCredentials)(credentialsResponse)) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_1__.CredentialsProviderError(\"Invalid response received from instance metadata service.\", {\n            logger: init.logger,\n        });\n    }\n    return (0,_remoteProvider_ImdsCredentials__WEBPACK_IMPORTED_MODULE_4__.fromImdsCredentials)(credentialsResponse);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/fromInstanceMetadata.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/index.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/index.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_MAX_RETRIES: () => (/* reexport safe */ _remoteProvider_RemoteProviderInit__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_MAX_RETRIES),\n/* harmony export */   DEFAULT_TIMEOUT: () => (/* reexport safe */ _remoteProvider_RemoteProviderInit__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TIMEOUT),\n/* harmony export */   ENV_CMDS_AUTH_TOKEN: () => (/* reexport safe */ _fromContainerMetadata__WEBPACK_IMPORTED_MODULE_0__.ENV_CMDS_AUTH_TOKEN),\n/* harmony export */   ENV_CMDS_FULL_URI: () => (/* reexport safe */ _fromContainerMetadata__WEBPACK_IMPORTED_MODULE_0__.ENV_CMDS_FULL_URI),\n/* harmony export */   ENV_CMDS_RELATIVE_URI: () => (/* reexport safe */ _fromContainerMetadata__WEBPACK_IMPORTED_MODULE_0__.ENV_CMDS_RELATIVE_URI),\n/* harmony export */   Endpoint: () => (/* reexport safe */ _config_Endpoint__WEBPACK_IMPORTED_MODULE_6__.Endpoint),\n/* harmony export */   fromContainerMetadata: () => (/* reexport safe */ _fromContainerMetadata__WEBPACK_IMPORTED_MODULE_0__.fromContainerMetadata),\n/* harmony export */   fromInstanceMetadata: () => (/* reexport safe */ _fromInstanceMetadata__WEBPACK_IMPORTED_MODULE_1__.fromInstanceMetadata),\n/* harmony export */   getInstanceMetadataEndpoint: () => (/* reexport safe */ _utils_getInstanceMetadataEndpoint__WEBPACK_IMPORTED_MODULE_5__.getInstanceMetadataEndpoint),\n/* harmony export */   httpRequest: () => (/* reexport safe */ _remoteProvider_httpRequest__WEBPACK_IMPORTED_MODULE_4__.httpRequest),\n/* harmony export */   providerConfigFromInit: () => (/* reexport safe */ _remoteProvider_RemoteProviderInit__WEBPACK_IMPORTED_MODULE_2__.providerConfigFromInit)\n/* harmony export */ });\n/* harmony import */ var _fromContainerMetadata__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fromContainerMetadata */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/fromContainerMetadata.js\");\n/* harmony import */ var _fromInstanceMetadata__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./fromInstanceMetadata */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/fromInstanceMetadata.js\");\n/* harmony import */ var _remoteProvider_RemoteProviderInit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./remoteProvider/RemoteProviderInit */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/RemoteProviderInit.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/types.js\");\n/* harmony import */ var _remoteProvider_httpRequest__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./remoteProvider/httpRequest */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/httpRequest.js\");\n/* harmony import */ var _utils_getInstanceMetadataEndpoint__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/getInstanceMetadataEndpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/utils/getInstanceMetadataEndpoint.js\");\n/* harmony import */ var _config_Endpoint__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./config/Endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/Endpoint.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjcmVkZW50aWFsLXByb3ZpZGVyLWltZHNANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY3JlZGVudGlhbC1wcm92aWRlci1pbWRzL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdDO0FBQ0Q7QUFDYTtBQUM1QjtBQUNtQztBQUN1QjtBQUNyQyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY3JlZGVudGlhbC1wcm92aWRlci1pbWRzQDQuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NyZWRlbnRpYWwtcHJvdmlkZXItaW1kcy9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2Zyb21Db250YWluZXJNZXRhZGF0YVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vZnJvbUluc3RhbmNlTWV0YWRhdGFcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3JlbW90ZVByb3ZpZGVyL1JlbW90ZVByb3ZpZGVySW5pdFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHlwZXNcIjtcbmV4cG9ydCB7IGh0dHBSZXF1ZXN0IH0gZnJvbSBcIi4vcmVtb3RlUHJvdmlkZXIvaHR0cFJlcXVlc3RcIjtcbmV4cG9ydCB7IGdldEluc3RhbmNlTWV0YWRhdGFFbmRwb2ludCB9IGZyb20gXCIuL3V0aWxzL2dldEluc3RhbmNlTWV0YWRhdGFFbmRwb2ludFwiO1xuZXhwb3J0IHsgRW5kcG9pbnQgfSBmcm9tIFwiLi9jb25maWcvRW5kcG9pbnRcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/ImdsCredentials.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/ImdsCredentials.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromImdsCredentials: () => (/* binding */ fromImdsCredentials),\n/* harmony export */   isImdsCredentials: () => (/* binding */ isImdsCredentials)\n/* harmony export */ });\nconst isImdsCredentials = (arg) => Boolean(arg) &&\n    typeof arg === \"object\" &&\n    typeof arg.AccessKeyId === \"string\" &&\n    typeof arg.SecretAccessKey === \"string\" &&\n    typeof arg.Token === \"string\" &&\n    typeof arg.Expiration === \"string\";\nconst fromImdsCredentials = (creds) => ({\n    accessKeyId: creds.AccessKeyId,\n    secretAccessKey: creds.SecretAccessKey,\n    sessionToken: creds.Token,\n    expiration: new Date(creds.Expiration),\n    ...(creds.AccountId && { accountId: creds.AccountId }),\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjcmVkZW50aWFsLXByb3ZpZGVyLWltZHNANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY3JlZGVudGlhbC1wcm92aWRlci1pbWRzL2Rpc3QtZXMvcmVtb3RlUHJvdmlkZXIvSW1kc0NyZWRlbnRpYWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2Qiw0QkFBNEI7QUFDekQsQ0FBQyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY3JlZGVudGlhbC1wcm92aWRlci1pbWRzQDQuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NyZWRlbnRpYWwtcHJvdmlkZXItaW1kcy9kaXN0LWVzL3JlbW90ZVByb3ZpZGVyL0ltZHNDcmVkZW50aWFscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgaXNJbWRzQ3JlZGVudGlhbHMgPSAoYXJnKSA9PiBCb29sZWFuKGFyZykgJiZcbiAgICB0eXBlb2YgYXJnID09PSBcIm9iamVjdFwiICYmXG4gICAgdHlwZW9mIGFyZy5BY2Nlc3NLZXlJZCA9PT0gXCJzdHJpbmdcIiAmJlxuICAgIHR5cGVvZiBhcmcuU2VjcmV0QWNjZXNzS2V5ID09PSBcInN0cmluZ1wiICYmXG4gICAgdHlwZW9mIGFyZy5Ub2tlbiA9PT0gXCJzdHJpbmdcIiAmJlxuICAgIHR5cGVvZiBhcmcuRXhwaXJhdGlvbiA9PT0gXCJzdHJpbmdcIjtcbmV4cG9ydCBjb25zdCBmcm9tSW1kc0NyZWRlbnRpYWxzID0gKGNyZWRzKSA9PiAoe1xuICAgIGFjY2Vzc0tleUlkOiBjcmVkcy5BY2Nlc3NLZXlJZCxcbiAgICBzZWNyZXRBY2Nlc3NLZXk6IGNyZWRzLlNlY3JldEFjY2Vzc0tleSxcbiAgICBzZXNzaW9uVG9rZW46IGNyZWRzLlRva2VuLFxuICAgIGV4cGlyYXRpb246IG5ldyBEYXRlKGNyZWRzLkV4cGlyYXRpb24pLFxuICAgIC4uLihjcmVkcy5BY2NvdW50SWQgJiYgeyBhY2NvdW50SWQ6IGNyZWRzLkFjY291bnRJZCB9KSxcbn0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/ImdsCredentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/RemoteProviderInit.js":
/*!**************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/RemoteProviderInit.js ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_MAX_RETRIES: () => (/* binding */ DEFAULT_MAX_RETRIES),\n/* harmony export */   DEFAULT_TIMEOUT: () => (/* binding */ DEFAULT_TIMEOUT),\n/* harmony export */   providerConfigFromInit: () => (/* binding */ providerConfigFromInit)\n/* harmony export */ });\nconst DEFAULT_TIMEOUT = 1000;\nconst DEFAULT_MAX_RETRIES = 0;\nconst providerConfigFromInit = ({ maxRetries = DEFAULT_MAX_RETRIES, timeout = DEFAULT_TIMEOUT, }) => ({ maxRetries, timeout });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjcmVkZW50aWFsLXByb3ZpZGVyLWltZHNANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY3JlZGVudGlhbC1wcm92aWRlci1pbWRzL2Rpc3QtZXMvcmVtb3RlUHJvdmlkZXIvUmVtb3RlUHJvdmlkZXJJbml0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFPO0FBQ0E7QUFDQSxrQ0FBa0MsOERBQThELFFBQVEscUJBQXFCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjcmVkZW50aWFsLXByb3ZpZGVyLWltZHNANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY3JlZGVudGlhbC1wcm92aWRlci1pbWRzL2Rpc3QtZXMvcmVtb3RlUHJvdmlkZXIvUmVtb3RlUHJvdmlkZXJJbml0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBERUZBVUxUX1RJTUVPVVQgPSAxMDAwO1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfTUFYX1JFVFJJRVMgPSAwO1xuZXhwb3J0IGNvbnN0IHByb3ZpZGVyQ29uZmlnRnJvbUluaXQgPSAoeyBtYXhSZXRyaWVzID0gREVGQVVMVF9NQVhfUkVUUklFUywgdGltZW91dCA9IERFRkFVTFRfVElNRU9VVCwgfSkgPT4gKHsgbWF4UmV0cmllcywgdGltZW91dCB9KTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/RemoteProviderInit.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/httpRequest.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/httpRequest.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   httpRequest: () => (/* binding */ httpRequest)\n/* harmony export */ });\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var buffer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! buffer */ \"buffer\");\n/* harmony import */ var buffer__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(buffer__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! http */ \"http\");\n/* harmony import */ var http__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(http__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction httpRequest(options) {\n    return new Promise((resolve, reject) => {\n        const req = (0,http__WEBPACK_IMPORTED_MODULE_2__.request)({\n            method: \"GET\",\n            ...options,\n            hostname: options.hostname?.replace(/^\\[(.+)\\]$/, \"$1\"),\n        });\n        req.on(\"error\", (err) => {\n            reject(Object.assign(new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.ProviderError(\"Unable to connect to instance metadata service\"), err));\n            req.destroy();\n        });\n        req.on(\"timeout\", () => {\n            reject(new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.ProviderError(\"TimeoutError from instance metadata service\"));\n            req.destroy();\n        });\n        req.on(\"response\", (res) => {\n            const { statusCode = 400 } = res;\n            if (statusCode < 200 || 300 <= statusCode) {\n                reject(Object.assign(new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.ProviderError(\"Error response received from instance metadata service\"), { statusCode }));\n                req.destroy();\n            }\n            const chunks = [];\n            res.on(\"data\", (chunk) => {\n                chunks.push(chunk);\n            });\n            res.on(\"end\", () => {\n                resolve(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat(chunks));\n                req.destroy();\n            });\n        });\n        req.end();\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/httpRequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/retry.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/retry.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   retry: () => (/* binding */ retry)\n/* harmony export */ });\nconst retry = (toRetry, maxRetries) => {\n    let promise = toRetry();\n    for (let i = 0; i < maxRetries; i++) {\n        promise = promise.catch(toRetry);\n    }\n    return promise;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjcmVkZW50aWFsLXByb3ZpZGVyLWltZHNANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY3JlZGVudGlhbC1wcm92aWRlci1pbWRzL2Rpc3QtZXMvcmVtb3RlUHJvdmlkZXIvcmV0cnkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQSxvQkFBb0IsZ0JBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjcmVkZW50aWFsLXByb3ZpZGVyLWltZHNANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY3JlZGVudGlhbC1wcm92aWRlci1pbWRzL2Rpc3QtZXMvcmVtb3RlUHJvdmlkZXIvcmV0cnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHJldHJ5ID0gKHRvUmV0cnksIG1heFJldHJpZXMpID0+IHtcbiAgICBsZXQgcHJvbWlzZSA9IHRvUmV0cnkoKTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IG1heFJldHJpZXM7IGkrKykge1xuICAgICAgICBwcm9taXNlID0gcHJvbWlzZS5jYXRjaCh0b1JldHJ5KTtcbiAgICB9XG4gICAgcmV0dXJuIHByb21pc2U7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/remoteProvider/retry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/types.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/types.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjcmVkZW50aWFsLXByb3ZpZGVyLWltZHNANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY3JlZGVudGlhbC1wcm92aWRlci1pbWRzL2Rpc3QtZXMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IjtBQUFVIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjcmVkZW50aWFsLXByb3ZpZGVyLWltZHNANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY3JlZGVudGlhbC1wcm92aWRlci1pbWRzL2Rpc3QtZXMvdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/utils/getExtendedInstanceMetadataCredentials.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/utils/getExtendedInstanceMetadataCredentials.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getExtendedInstanceMetadataCredentials: () => (/* binding */ getExtendedInstanceMetadataCredentials)\n/* harmony export */ });\nconst STATIC_STABILITY_REFRESH_INTERVAL_SECONDS = 5 * 60;\nconst STATIC_STABILITY_REFRESH_INTERVAL_JITTER_WINDOW_SECONDS = 5 * 60;\nconst STATIC_STABILITY_DOC_URL = \"https://docs.aws.amazon.com/sdkref/latest/guide/feature-static-credentials.html\";\nconst getExtendedInstanceMetadataCredentials = (credentials, logger) => {\n    const refreshInterval = STATIC_STABILITY_REFRESH_INTERVAL_SECONDS +\n        Math.floor(Math.random() * STATIC_STABILITY_REFRESH_INTERVAL_JITTER_WINDOW_SECONDS);\n    const newExpiration = new Date(Date.now() + refreshInterval * 1000);\n    logger.warn(\"Attempting credential expiration extension due to a credential service availability issue. A refresh of these \" +\n        `credentials will be attempted after ${new Date(newExpiration)}.\\nFor more information, please visit: ` +\n        STATIC_STABILITY_DOC_URL);\n    const originalExpiration = credentials.originalExpiration ?? credentials.expiration;\n    return {\n        ...credentials,\n        ...(originalExpiration ? { originalExpiration } : {}),\n        expiration: newExpiration,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/utils/getExtendedInstanceMetadataCredentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/utils/getInstanceMetadataEndpoint.js":
/*!**************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/utils/getInstanceMetadataEndpoint.js ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getInstanceMetadataEndpoint: () => (/* binding */ getInstanceMetadataEndpoint)\n/* harmony export */ });\n/* harmony import */ var _smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/node-config-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+node-config-provider@4.1.3/node_modules/@smithy/node-config-provider/dist-es/index.js\");\n/* harmony import */ var _smithy_url_parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/url-parser */ \"(rsc)/./node_modules/.pnpm/@smithy+url-parser@4.0.4/node_modules/@smithy/url-parser/dist-es/index.js\");\n/* harmony import */ var _config_Endpoint__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config/Endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/Endpoint.js\");\n/* harmony import */ var _config_EndpointConfigOptions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config/EndpointConfigOptions */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/EndpointConfigOptions.js\");\n/* harmony import */ var _config_EndpointMode__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../config/EndpointMode */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/EndpointMode.js\");\n/* harmony import */ var _config_EndpointModeConfigOptions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../config/EndpointModeConfigOptions */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/config/EndpointModeConfigOptions.js\");\n\n\n\n\n\n\nconst getInstanceMetadataEndpoint = async () => (0,_smithy_url_parser__WEBPACK_IMPORTED_MODULE_1__.parseUrl)((await getFromEndpointConfig()) || (await getFromEndpointModeConfig()));\nconst getFromEndpointConfig = async () => (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_0__.loadConfig)(_config_EndpointConfigOptions__WEBPACK_IMPORTED_MODULE_3__.ENDPOINT_CONFIG_OPTIONS)();\nconst getFromEndpointModeConfig = async () => {\n    const endpointMode = await (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_0__.loadConfig)(_config_EndpointModeConfigOptions__WEBPACK_IMPORTED_MODULE_5__.ENDPOINT_MODE_CONFIG_OPTIONS)();\n    switch (endpointMode) {\n        case _config_EndpointMode__WEBPACK_IMPORTED_MODULE_4__.EndpointMode.IPv4:\n            return _config_Endpoint__WEBPACK_IMPORTED_MODULE_2__.Endpoint.IPv4;\n        case _config_EndpointMode__WEBPACK_IMPORTED_MODULE_4__.EndpointMode.IPv6:\n            return _config_Endpoint__WEBPACK_IMPORTED_MODULE_2__.Endpoint.IPv6;\n        default:\n            throw new Error(`Unsupported endpoint mode: ${endpointMode}.` + ` Select from ${Object.values(_config_EndpointMode__WEBPACK_IMPORTED_MODULE_4__.EndpointMode)}`);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/utils/getInstanceMetadataEndpoint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/utils/staticStabilityProvider.js":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/utils/staticStabilityProvider.js ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   staticStabilityProvider: () => (/* binding */ staticStabilityProvider)\n/* harmony export */ });\n/* harmony import */ var _getExtendedInstanceMetadataCredentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getExtendedInstanceMetadataCredentials */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/utils/getExtendedInstanceMetadataCredentials.js\");\n\nconst staticStabilityProvider = (provider, options = {}) => {\n    const logger = options?.logger || console;\n    let pastCredentials;\n    return async () => {\n        let credentials;\n        try {\n            credentials = await provider();\n            if (credentials.expiration && credentials.expiration.getTime() < Date.now()) {\n                credentials = (0,_getExtendedInstanceMetadataCredentials__WEBPACK_IMPORTED_MODULE_0__.getExtendedInstanceMetadataCredentials)(credentials, logger);\n            }\n        }\n        catch (e) {\n            if (pastCredentials) {\n                logger.warn(\"Credential renew failed: \", e);\n                credentials = (0,_getExtendedInstanceMetadataCredentials__WEBPACK_IMPORTED_MODULE_0__.getExtendedInstanceMetadataCredentials)(pastCredentials, logger);\n            }\n            else {\n                throw e;\n            }\n        }\n        pastCredentials = credentials;\n        return credentials;\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/utils/staticStabilityProvider.js\n");

/***/ })

};
;