/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@whatwg-node+server@0.9.49";
exports.ids = ["vendor-chunks/@whatwg-node+server@0.9.49"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/createServerAdapter.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/createServerAdapter.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createServerAdapter = createServerAdapter;\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* eslint-disable @typescript-eslint/ban-types */\nconst DefaultFetchAPI = tslib_1.__importStar(__webpack_require__(/*! @whatwg-node/fetch */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/node-ponyfill.js\"));\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/utils.js\");\nconst uwebsockets_js_1 = __webpack_require__(/*! ./uwebsockets.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/uwebsockets.js\");\nasync function handleWaitUntils(waitUntilPromises) {\n    await Promise.allSettled(waitUntilPromises);\n}\n// Required for envs like nextjs edge runtime\nfunction isRequestAccessible(serverContext) {\n    try {\n        return !!serverContext?.request;\n    }\n    catch {\n        return false;\n    }\n}\nconst EMPTY_OBJECT = {};\nfunction createServerAdapter(serverAdapterBaseObject, options) {\n    const fetchAPI = {\n        ...DefaultFetchAPI,\n        ...options?.fetchAPI,\n    };\n    const givenHandleRequest = typeof serverAdapterBaseObject === 'function'\n        ? serverAdapterBaseObject\n        : serverAdapterBaseObject.handle;\n    const onRequestHooks = [];\n    const onResponseHooks = [];\n    if (options?.plugins != null) {\n        for (const plugin of options.plugins) {\n            if (plugin.onRequest) {\n                onRequestHooks.push(plugin.onRequest);\n            }\n            if (plugin.onResponse) {\n                onResponseHooks.push(plugin.onResponse);\n            }\n        }\n    }\n    const handleRequest = onRequestHooks.length > 0 || onResponseHooks.length > 0\n        ? function handleRequest(request, serverContext) {\n            let requestHandler = givenHandleRequest;\n            let response;\n            if (onRequestHooks.length === 0) {\n                return handleEarlyResponse();\n            }\n            let url = new Proxy(EMPTY_OBJECT, {\n                get(_target, prop, _receiver) {\n                    url = new fetchAPI.URL(request.url, 'http://localhost');\n                    return Reflect.get(url, prop, url);\n                },\n            });\n            const onRequestHooksIteration$ = (0, utils_js_1.iterateAsyncVoid)(onRequestHooks, (onRequestHook, stopEarly) => onRequestHook({\n                request,\n                setRequest(newRequest) {\n                    request = newRequest;\n                },\n                serverContext,\n                fetchAPI,\n                url,\n                requestHandler,\n                setRequestHandler(newRequestHandler) {\n                    requestHandler = newRequestHandler;\n                },\n                endResponse(newResponse) {\n                    response = newResponse;\n                    if (newResponse) {\n                        stopEarly();\n                    }\n                },\n            }));\n            function handleResponse(response) {\n                if (onResponseHooks.length === 0) {\n                    return response;\n                }\n                const onResponseHookPayload = {\n                    request,\n                    response,\n                    serverContext,\n                    setResponse(newResponse) {\n                        response = newResponse;\n                    },\n                    fetchAPI,\n                };\n                const onResponseHooksIteration$ = (0, utils_js_1.iterateAsyncVoid)(onResponseHooks, onResponseHook => onResponseHook(onResponseHookPayload));\n                if ((0, utils_js_1.isPromise)(onResponseHooksIteration$)) {\n                    return onResponseHooksIteration$.then(() => response);\n                }\n                return response;\n            }\n            function handleEarlyResponse() {\n                if (!response) {\n                    const response$ = requestHandler(request, serverContext);\n                    if ((0, utils_js_1.isPromise)(response$)) {\n                        return response$.then(handleResponse);\n                    }\n                    return handleResponse(response$);\n                }\n                return handleResponse(response);\n            }\n            if ((0, utils_js_1.isPromise)(onRequestHooksIteration$)) {\n                return onRequestHooksIteration$.then(handleEarlyResponse);\n            }\n            return handleEarlyResponse();\n        }\n        : givenHandleRequest;\n    // TODO: Remove this on the next major version\n    function handleNodeRequest(nodeRequest, ...ctx) {\n        const serverContext = ctx.length > 1 ? (0, utils_js_1.completeAssign)(...ctx) : ctx[0] || {};\n        const request = (0, utils_js_1.normalizeNodeRequest)(nodeRequest, fetchAPI.Request);\n        return handleRequest(request, serverContext);\n    }\n    function handleNodeRequestAndResponse(nodeRequest, nodeResponseOrContainer, ...ctx) {\n        const nodeResponse = nodeResponseOrContainer.raw || nodeResponseOrContainer;\n        utils_js_1.nodeRequestResponseMap.set(nodeRequest, nodeResponse);\n        return handleNodeRequest(nodeRequest, ...ctx);\n    }\n    function requestListener(nodeRequest, nodeResponse, ...ctx) {\n        const waitUntilPromises = [];\n        const defaultServerContext = {\n            req: nodeRequest,\n            res: nodeResponse,\n            waitUntil(cb) {\n                waitUntilPromises.push(cb.catch(err => console.error(err)));\n            },\n        };\n        let response$;\n        try {\n            response$ = handleNodeRequestAndResponse(nodeRequest, nodeResponse, defaultServerContext, ...ctx);\n        }\n        catch (err) {\n            response$ = (0, utils_js_1.handleErrorFromRequestHandler)(err, fetchAPI.Response);\n        }\n        if ((0, utils_js_1.isPromise)(response$)) {\n            return response$\n                .catch((e) => (0, utils_js_1.handleErrorFromRequestHandler)(e, fetchAPI.Response))\n                .then(response => (0, utils_js_1.sendNodeResponse)(response, nodeResponse, nodeRequest))\n                .catch(err => {\n                console.error(`Unexpected error while handling request: ${err.message || err}`);\n            });\n        }\n        try {\n            return (0, utils_js_1.sendNodeResponse)(response$, nodeResponse, nodeRequest);\n        }\n        catch (err) {\n            console.error(`Unexpected error while handling request: ${err.message || err}`);\n        }\n    }\n    function handleUWS(res, req, ...ctx) {\n        const waitUntilPromises = [];\n        const defaultServerContext = {\n            res,\n            req,\n            waitUntil(cb) {\n                waitUntilPromises.push(cb.catch(err => console.error(err)));\n            },\n        };\n        const filteredCtxParts = ctx.filter(partCtx => partCtx != null);\n        const serverContext = filteredCtxParts.length > 0\n            ? (0, utils_js_1.completeAssign)(defaultServerContext, ...ctx)\n            : defaultServerContext;\n        const signal = new utils_js_1.ServerAdapterRequestAbortSignal();\n        const originalResEnd = res.end.bind(res);\n        let resEnded = false;\n        res.end = function (data) {\n            resEnded = true;\n            return originalResEnd(data);\n        };\n        const originalOnAborted = res.onAborted.bind(res);\n        originalOnAborted(function () {\n            signal.sendAbort();\n        });\n        res.onAborted = function (cb) {\n            signal.addEventListener('abort', cb);\n        };\n        const request = (0, uwebsockets_js_1.getRequestFromUWSRequest)({\n            req,\n            res,\n            fetchAPI,\n            signal,\n        });\n        let response$;\n        try {\n            response$ = handleRequest(request, serverContext);\n        }\n        catch (err) {\n            response$ = (0, utils_js_1.handleErrorFromRequestHandler)(err, fetchAPI.Response);\n        }\n        if ((0, utils_js_1.isPromise)(response$)) {\n            return response$\n                .catch((e) => (0, utils_js_1.handleErrorFromRequestHandler)(e, fetchAPI.Response))\n                .then(response => {\n                if (!signal.aborted && !resEnded) {\n                    return (0, uwebsockets_js_1.sendResponseToUwsOpts)(res, response, signal);\n                }\n            })\n                .catch(err => {\n                console.error(`Unexpected error while handling request: \\n${err.stack || err.message || err}`);\n            });\n        }\n        try {\n            if (!signal.aborted && !resEnded) {\n                return (0, uwebsockets_js_1.sendResponseToUwsOpts)(res, response$, signal);\n            }\n        }\n        catch (err) {\n            console.error(`Unexpected error while handling request: \\n${err.stack || err.message || err}`);\n        }\n    }\n    function handleEvent(event, ...ctx) {\n        if (!event.respondWith || !event.request) {\n            throw new TypeError(`Expected FetchEvent, got ${event}`);\n        }\n        const filteredCtxParts = ctx.filter(partCtx => partCtx != null);\n        const serverContext = filteredCtxParts.length > 0\n            ? (0, utils_js_1.completeAssign)({}, event, ...filteredCtxParts)\n            : (0, utils_js_1.isolateObject)(event);\n        const response$ = handleRequest(event.request, serverContext);\n        event.respondWith(response$);\n    }\n    function handleRequestWithWaitUntil(request, ...ctx) {\n        const filteredCtxParts = ctx.filter(partCtx => partCtx != null);\n        let waitUntilPromises;\n        const serverContext = filteredCtxParts.length > 1\n            ? (0, utils_js_1.completeAssign)({}, ...filteredCtxParts)\n            : (0, utils_js_1.isolateObject)(filteredCtxParts[0], filteredCtxParts[0] == null || filteredCtxParts[0].waitUntil == null\n                ? (waitUntilPromises = [])\n                : undefined);\n        const response$ = handleRequest(request, serverContext);\n        if (waitUntilPromises?.length) {\n            return handleWaitUntils(waitUntilPromises).then(() => response$);\n        }\n        return response$;\n    }\n    const fetchFn = (input, ...maybeCtx) => {\n        if (typeof input === 'string' || 'href' in input) {\n            const [initOrCtx, ...restOfCtx] = maybeCtx;\n            if ((0, utils_js_1.isRequestInit)(initOrCtx)) {\n                const request = new fetchAPI.Request(input, initOrCtx);\n                const res$ = handleRequestWithWaitUntil(request, ...restOfCtx);\n                return (0, utils_js_1.handleAbortSignalAndPromiseResponse)(res$, initOrCtx?.signal);\n            }\n            const request = new fetchAPI.Request(input);\n            return handleRequestWithWaitUntil(request, ...maybeCtx);\n        }\n        const res$ = handleRequestWithWaitUntil(input, ...maybeCtx);\n        return (0, utils_js_1.handleAbortSignalAndPromiseResponse)(res$, input._signal);\n    };\n    const genericRequestHandler = (input, ...maybeCtx) => {\n        // If it is a Node request\n        const [initOrCtxOrRes, ...restOfCtx] = maybeCtx;\n        if ((0, utils_js_1.isNodeRequest)(input)) {\n            if (!(0, utils_js_1.isServerResponse)(initOrCtxOrRes)) {\n                throw new TypeError(`Expected ServerResponse, got ${initOrCtxOrRes}`);\n            }\n            return requestListener(input, initOrCtxOrRes, ...restOfCtx);\n        }\n        if ((0, uwebsockets_js_1.isUWSResponse)(input)) {\n            return handleUWS(input, initOrCtxOrRes, ...restOfCtx);\n        }\n        if ((0, utils_js_1.isServerResponse)(initOrCtxOrRes)) {\n            throw new TypeError('Got Node response without Node request');\n        }\n        // Is input a container object over Request?\n        if (isRequestAccessible(input)) {\n            // Is it FetchEvent?\n            if ((0, utils_js_1.isFetchEvent)(input)) {\n                return handleEvent(input, ...maybeCtx);\n            }\n            // In this input is also the context\n            return handleRequestWithWaitUntil(input.request, input, ...maybeCtx);\n        }\n        // Or is it Request itself?\n        // Then ctx is present and it is the context\n        return fetchFn(input, ...maybeCtx);\n    };\n    const adapterObj = {\n        handleRequest: handleRequestWithWaitUntil,\n        fetch: fetchFn,\n        handleNodeRequest,\n        handleNodeRequestAndResponse,\n        requestListener,\n        handleEvent,\n        handleUWS,\n        handle: genericRequestHandler,\n    };\n    const serverAdapter = new Proxy(genericRequestHandler, {\n        // It should have all the attributes of the handler function and the server instance\n        has: (_, prop) => {\n            return (prop in adapterObj ||\n                prop in genericRequestHandler ||\n                (serverAdapterBaseObject && prop in serverAdapterBaseObject));\n        },\n        get: (_, prop) => {\n            const adapterProp = adapterObj[prop];\n            if (adapterProp) {\n                if (adapterProp.bind) {\n                    return adapterProp.bind(adapterObj);\n                }\n                return adapterProp;\n            }\n            const handleProp = genericRequestHandler[prop];\n            if (handleProp) {\n                if (handleProp.bind) {\n                    return handleProp.bind(genericRequestHandler);\n                }\n                return handleProp;\n            }\n            if (serverAdapterBaseObject) {\n                const serverAdapterBaseObjectProp = serverAdapterBaseObject[prop];\n                if (serverAdapterBaseObjectProp) {\n                    if (serverAdapterBaseObjectProp.bind) {\n                        return function (...args) {\n                            const returnedVal = serverAdapterBaseObject[prop](...args);\n                            if (returnedVal === serverAdapterBaseObject) {\n                                return serverAdapter;\n                            }\n                            return returnedVal;\n                        };\n                    }\n                    return serverAdapterBaseObjectProp;\n                }\n            }\n        },\n        apply(_, __, args) {\n            return genericRequestHandler(...args);\n        },\n    });\n    return serverAdapter;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/createServerAdapter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/index.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/index.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Response = void 0;\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\ntslib_1.__exportStar(__webpack_require__(/*! ./createServerAdapter.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/createServerAdapter.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./types.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/types.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/utils.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/types.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/types.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/useCors.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/useCors.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/useErrorHandling.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/useErrorHandling.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./plugins/useContentEncoding.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/useContentEncoding.js\"), exports);\ntslib_1.__exportStar(__webpack_require__(/*! ./uwebsockets.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/uwebsockets.js\"), exports);\nvar fetch_1 = __webpack_require__(/*! @whatwg-node/fetch */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/node-ponyfill.js\");\nObject.defineProperty(exports, \"Response\", ({ enumerable: true, get: function () { return fetch_1.Response; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/types.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/types.js ***!
  \*************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/useContentEncoding.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/useContentEncoding.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useContentEncoding = useContentEncoding;\nconst utils_js_1 = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/utils.js\");\nfunction useContentEncoding() {\n    const encodingMap = new WeakMap();\n    return {\n        onRequest({ request, setRequest, fetchAPI, endResponse }) {\n            if (request.body) {\n                const contentEncodingHeader = request.headers.get('content-encoding');\n                if (contentEncodingHeader && contentEncodingHeader !== 'none') {\n                    const contentEncodings = contentEncodingHeader?.split(',');\n                    if (!contentEncodings.every(encoding => (0, utils_js_1.getSupportedEncodings)(fetchAPI).includes(encoding))) {\n                        endResponse(new fetchAPI.Response(`Unsupported 'Content-Encoding': ${contentEncodingHeader}`, {\n                            status: 415,\n                            statusText: 'Unsupported Media Type',\n                        }));\n                        return;\n                    }\n                    let newBody = request.body;\n                    for (const contentEncoding of contentEncodings) {\n                        newBody = newBody.pipeThrough(new fetchAPI.DecompressionStream(contentEncoding));\n                    }\n                    request = new fetchAPI.Request(request.url, {\n                        body: newBody,\n                        cache: request.cache,\n                        credentials: request.credentials,\n                        headers: request.headers,\n                        integrity: request.integrity,\n                        keepalive: request.keepalive,\n                        method: request.method,\n                        mode: request.mode,\n                        redirect: request.redirect,\n                        referrer: request.referrer,\n                        referrerPolicy: request.referrerPolicy,\n                        signal: request.signal,\n                        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n                        // @ts-ignore - not in the TS types yet\n                        duplex: 'half',\n                    });\n                    setRequest(request);\n                }\n            }\n            const acceptEncoding = request.headers.get('accept-encoding');\n            if (acceptEncoding) {\n                encodingMap.set(request, acceptEncoding.split(','));\n            }\n        },\n        onResponse({ request, response, setResponse, fetchAPI, serverContext }) {\n            const waitUntil = serverContext.waitUntil?.bind(serverContext) || (() => { });\n            // Hack for avoiding to create whatwg-node to create a readable stream until it's needed\n            if (response['bodyInit'] || response.body) {\n                const encodings = encodingMap.get(request);\n                if (encodings) {\n                    const supportedEncoding = encodings.find(encoding => (0, utils_js_1.getSupportedEncodings)(fetchAPI).includes(encoding));\n                    if (supportedEncoding) {\n                        const compressionStream = new fetchAPI.CompressionStream(supportedEncoding);\n                        // To calculate final content-length\n                        const contentLength = response.headers.get('content-length');\n                        if (contentLength) {\n                            const bufOfRes = response._buffer;\n                            if (bufOfRes) {\n                                const writer = compressionStream.writable.getWriter();\n                                waitUntil(writer.write(bufOfRes));\n                                waitUntil(writer.close());\n                                const uint8Arrays$ = (0, utils_js_1.isReadable)(compressionStream.readable['readable'])\n                                    ? collectReadableValues(compressionStream.readable['readable'])\n                                    : (0, utils_js_1.isAsyncIterable)(compressionStream.readable)\n                                        ? collectAsyncIterableValues(compressionStream.readable)\n                                        : collectReadableStreamValues(compressionStream.readable);\n                                return uint8Arrays$.then(uint8Arrays => {\n                                    const chunks = uint8Arrays.flatMap(uint8Array => [...uint8Array]);\n                                    const uint8Array = new Uint8Array(chunks);\n                                    const newHeaders = new fetchAPI.Headers(response.headers);\n                                    newHeaders.set('content-encoding', supportedEncoding);\n                                    newHeaders.set('content-length', uint8Array.byteLength.toString());\n                                    const compressedResponse = new fetchAPI.Response(uint8Array, {\n                                        ...response,\n                                        headers: newHeaders,\n                                    });\n                                    utils_js_1.decompressedResponseMap.set(compressedResponse, response);\n                                    setResponse(compressedResponse);\n                                    waitUntil(compressionStream.writable.close());\n                                });\n                            }\n                        }\n                        const newHeaders = new fetchAPI.Headers(response.headers);\n                        newHeaders.set('content-encoding', supportedEncoding);\n                        newHeaders.delete('content-length');\n                        const compressedBody = response.body.pipeThrough(compressionStream);\n                        const compressedResponse = new fetchAPI.Response(compressedBody, {\n                            status: response.status,\n                            statusText: response.statusText,\n                            headers: newHeaders,\n                        });\n                        utils_js_1.decompressedResponseMap.set(compressedResponse, response);\n                        setResponse(compressedResponse);\n                    }\n                }\n            }\n        },\n    };\n}\nfunction collectReadableValues(readable) {\n    const values = [];\n    readable.on('data', value => values.push(value));\n    return new Promise((resolve, reject) => {\n        readable.once('end', () => resolve(values));\n        readable.once('error', reject);\n    });\n}\nasync function collectAsyncIterableValues(asyncIterable) {\n    const values = [];\n    for await (const value of asyncIterable) {\n        values.push(value);\n    }\n    return values;\n}\nasync function collectReadableStreamValues(readableStream) {\n    const reader = readableStream.getReader();\n    const values = [];\n    while (true) {\n        const { done, value } = await reader.read();\n        if (done) {\n            reader.releaseLock();\n            break;\n        }\n        else if (value) {\n            values.push(value);\n        }\n    }\n    return values;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/useContentEncoding.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/useCors.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/useCors.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getCORSHeadersByRequestAndOptions = getCORSHeadersByRequestAndOptions;\nexports.useCORS = useCORS;\nfunction getCORSHeadersByRequestAndOptions(request, corsOptions) {\n    const currentOrigin = request.headers.get('origin');\n    if (corsOptions === false || currentOrigin == null) {\n        return null;\n    }\n    const headers = {};\n    // If defined origins have '*' or undefined by any means, we should allow all origins\n    if (corsOptions.origin == null ||\n        corsOptions.origin.length === 0 ||\n        corsOptions.origin.includes('*')) {\n        headers['Access-Control-Allow-Origin'] = currentOrigin;\n        // Vary by origin because there are multiple origins\n        headers['Vary'] = 'Origin';\n    }\n    else if (typeof corsOptions.origin === 'string') {\n        // If there is one specific origin is specified, use it directly\n        headers['Access-Control-Allow-Origin'] = corsOptions.origin;\n    }\n    else if (Array.isArray(corsOptions.origin)) {\n        // If there is only one origin defined in the array, consider it as a single one\n        if (corsOptions.origin.length === 1) {\n            headers['Access-Control-Allow-Origin'] = corsOptions.origin[0];\n        }\n        else if (corsOptions.origin.includes(currentOrigin)) {\n            // If origin is available in the headers, use it\n            headers['Access-Control-Allow-Origin'] = currentOrigin;\n            // Vary by origin because there are multiple origins\n            headers['Vary'] = 'Origin';\n        }\n        else {\n            // There is no origin found in the headers, so we should return null\n            headers['Access-Control-Allow-Origin'] = 'null';\n        }\n    }\n    if (corsOptions.methods?.length) {\n        headers['Access-Control-Allow-Methods'] = corsOptions.methods.join(', ');\n    }\n    else {\n        const requestMethod = request.headers.get('access-control-request-method');\n        if (requestMethod) {\n            headers['Access-Control-Allow-Methods'] = requestMethod;\n        }\n    }\n    if (corsOptions.allowedHeaders?.length) {\n        headers['Access-Control-Allow-Headers'] = corsOptions.allowedHeaders.join(', ');\n    }\n    else {\n        const requestHeaders = request.headers.get('access-control-request-headers');\n        if (requestHeaders) {\n            headers['Access-Control-Allow-Headers'] = requestHeaders;\n            if (headers['Vary']) {\n                headers['Vary'] += ', Access-Control-Request-Headers';\n            }\n            else {\n                headers['Vary'] = 'Access-Control-Request-Headers';\n            }\n        }\n    }\n    if (corsOptions.credentials != null) {\n        if (corsOptions.credentials === true) {\n            headers['Access-Control-Allow-Credentials'] = 'true';\n        }\n    }\n    else if (headers['Access-Control-Allow-Origin'] !== '*') {\n        headers['Access-Control-Allow-Credentials'] = 'true';\n    }\n    if (corsOptions.exposedHeaders) {\n        headers['Access-Control-Expose-Headers'] = corsOptions.exposedHeaders.join(', ');\n    }\n    if (corsOptions.maxAge) {\n        headers['Access-Control-Max-Age'] = corsOptions.maxAge.toString();\n    }\n    return headers;\n}\nasync function getCORSResponseHeaders(request, corsOptionsFactory, serverContext) {\n    const corsOptions = await corsOptionsFactory(request, serverContext);\n    return getCORSHeadersByRequestAndOptions(request, corsOptions);\n}\nfunction useCORS(options) {\n    let corsOptionsFactory = () => ({});\n    if (options != null) {\n        if (typeof options === 'function') {\n            corsOptionsFactory = options;\n        }\n        else if (typeof options === 'object') {\n            const corsOptions = {\n                ...options,\n            };\n            corsOptionsFactory = () => corsOptions;\n        }\n        else if (options === false) {\n            corsOptionsFactory = () => false;\n        }\n    }\n    return {\n        onRequest({ request, fetchAPI, endResponse }) {\n            if (request.method.toUpperCase() === 'OPTIONS') {\n                const response = new fetchAPI.Response(null, {\n                    status: 204,\n                    // Safari (and potentially other browsers) need content-length 0,\n                    // for 204 or they just hang waiting for a body\n                    // see: https://github.com/expressjs/cors/blob/master/lib/index.js#L176\n                    headers: {\n                        'Content-Length': '0',\n                    },\n                });\n                endResponse(response);\n            }\n        },\n        async onResponse({ request, serverContext, response }) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const headers = await getCORSResponseHeaders(request, corsOptionsFactory, serverContext);\n            if (headers != null) {\n                for (const headerName in headers) {\n                    response.headers.set(headerName, headers[headerName]);\n                }\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/useCors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/useErrorHandling.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/useErrorHandling.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.HTTPError = void 0;\nexports.createDefaultErrorHandler = createDefaultErrorHandler;\nexports.useErrorHandling = useErrorHandling;\nconst fetch_1 = __webpack_require__(/*! @whatwg-node/fetch */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/node-ponyfill.js\");\nconst utils_js_1 = __webpack_require__(/*! ../utils.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/utils.js\");\nfunction createDefaultErrorHandler(ResponseCtor = fetch_1.Response) {\n    return function defaultErrorHandler(e) {\n        if (e.details || e.status || e.headers || e.name === 'HTTPError') {\n            return new ResponseCtor(typeof e.details === 'object' ? JSON.stringify(e.details) : e.message, {\n                status: e.status,\n                headers: e.headers || {},\n            });\n        }\n        console.error(e);\n        return createDefaultErrorResponse(ResponseCtor);\n    };\n}\nfunction createDefaultErrorResponse(ResponseCtor) {\n    if (ResponseCtor.error) {\n        return ResponseCtor.error();\n    }\n    return new ResponseCtor(null, { status: 500 });\n}\nclass HTTPError extends Error {\n    status;\n    message;\n    headers;\n    details;\n    name = 'HTTPError';\n    constructor(status = 500, message, headers = {}, details) {\n        super(message);\n        this.status = status;\n        this.message = message;\n        this.headers = headers;\n        this.details = details;\n        Error.captureStackTrace(this, HTTPError);\n    }\n}\nexports.HTTPError = HTTPError;\nfunction useErrorHandling(onError) {\n    return {\n        onRequest({ requestHandler, setRequestHandler, fetchAPI }) {\n            const errorHandler = onError || createDefaultErrorHandler(fetchAPI.Response);\n            setRequestHandler(function handlerWithErrorHandling(request, serverContext) {\n                try {\n                    const response$ = requestHandler(request, serverContext);\n                    if ((0, utils_js_1.isPromise)(response$)) {\n                        return response$.catch(e => errorHandler(e, request, serverContext) ||\n                            createDefaultErrorResponse(fetchAPI.Response));\n                    }\n                    return response$;\n                }\n                catch (e) {\n                    return (errorHandler(e, request, serverContext) || createDefaultErrorResponse(fetchAPI.Response));\n                }\n            });\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/plugins/useErrorHandling.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/types.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/types.js ***!
  \*****************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/utils.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/utils.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.decompressedResponseMap = exports.nodeRequestResponseMap = exports.ServerAdapterRequestAbortSignal = void 0;\nexports.isAsyncIterable = isAsyncIterable;\nexports.normalizeNodeRequest = normalizeNodeRequest;\nexports.isReadable = isReadable;\nexports.isNodeRequest = isNodeRequest;\nexports.isServerResponse = isServerResponse;\nexports.isReadableStream = isReadableStream;\nexports.isFetchEvent = isFetchEvent;\nexports.sendNodeResponse = sendNodeResponse;\nexports.isRequestInit = isRequestInit;\nexports.completeAssign = completeAssign;\nexports.isPromise = isPromise;\nexports.iterateAsyncVoid = iterateAsyncVoid;\nexports.handleErrorFromRequestHandler = handleErrorFromRequestHandler;\nexports.isolateObject = isolateObject;\nexports.createDeferredPromise = createDeferredPromise;\nexports.handleAbortSignalAndPromiseResponse = handleAbortSignalAndPromiseResponse;\nexports.getSupportedEncodings = getSupportedEncodings;\nexports.handleResponseDecompression = handleResponseDecompression;\nconst fetch_1 = __webpack_require__(/*! @whatwg-node/fetch */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+fetch@0.9.21/node_modules/@whatwg-node/fetch/dist/node-ponyfill.js\");\nfunction isAsyncIterable(body) {\n    return (body != null && typeof body === 'object' && typeof body[Symbol.asyncIterator] === 'function');\n}\nfunction getPort(nodeRequest) {\n    if (nodeRequest.socket?.localPort) {\n        return nodeRequest.socket?.localPort;\n    }\n    const hostInHeader = nodeRequest.headers?.[':authority'] || nodeRequest.headers?.host;\n    const portInHeader = hostInHeader?.split(':')?.[1];\n    if (portInHeader) {\n        return portInHeader;\n    }\n    return 80;\n}\nfunction getHostnameWithPort(nodeRequest) {\n    if (nodeRequest.headers?.[':authority']) {\n        return nodeRequest.headers?.[':authority'];\n    }\n    if (nodeRequest.headers?.host) {\n        return nodeRequest.headers?.host;\n    }\n    const port = getPort(nodeRequest);\n    if (nodeRequest.hostname) {\n        return nodeRequest.hostname + ':' + port;\n    }\n    const localIp = nodeRequest.socket?.localAddress;\n    if (localIp && !localIp?.includes('::') && !localIp?.includes('ffff')) {\n        return `${localIp}:${port}`;\n    }\n    return 'localhost';\n}\nfunction buildFullUrl(nodeRequest) {\n    const hostnameWithPort = getHostnameWithPort(nodeRequest);\n    const protocol = nodeRequest.protocol || (nodeRequest.socket?.encrypted ? 'https' : 'http');\n    const endpoint = nodeRequest.originalUrl || nodeRequest.url || '/graphql';\n    return `${protocol}://${hostnameWithPort}${endpoint}`;\n}\nfunction isRequestBody(body) {\n    const stringTag = body[Symbol.toStringTag];\n    if (typeof body === 'string' ||\n        stringTag === 'Uint8Array' ||\n        stringTag === 'Blob' ||\n        stringTag === 'FormData' ||\n        stringTag === 'URLSearchParams' ||\n        isAsyncIterable(body)) {\n        return true;\n    }\n    return false;\n}\nclass ServerAdapterRequestAbortSignal extends EventTarget {\n    aborted = false;\n    _onabort = null;\n    reason;\n    throwIfAborted() {\n        if (this.aborted) {\n            throw this.reason;\n        }\n    }\n    sendAbort() {\n        this.reason = new DOMException('This operation was aborted', 'AbortError');\n        this.aborted = true;\n        this.dispatchEvent(new Event('abort'));\n    }\n    get onabort() {\n        return this._onabort;\n    }\n    set onabort(value) {\n        this._onabort = value;\n        if (value) {\n            this.addEventListener('abort', value);\n        }\n        else {\n            this.removeEventListener('abort', value);\n        }\n    }\n    any(signals) {\n        return AbortSignal.any([...signals]);\n    }\n}\nexports.ServerAdapterRequestAbortSignal = ServerAdapterRequestAbortSignal;\nlet bunNodeCompatModeWarned = false;\nexports.nodeRequestResponseMap = new WeakMap();\nfunction normalizeNodeRequest(nodeRequest, RequestCtor) {\n    const rawRequest = nodeRequest.raw || nodeRequest.req || nodeRequest;\n    let fullUrl = buildFullUrl(rawRequest);\n    if (nodeRequest.query) {\n        const url = new fetch_1.URL(fullUrl);\n        for (const key in nodeRequest.query) {\n            url.searchParams.set(key, nodeRequest.query[key]);\n        }\n        fullUrl = url.toString();\n    }\n    let signal;\n    const nodeResponse = exports.nodeRequestResponseMap.get(nodeRequest);\n    exports.nodeRequestResponseMap.delete(nodeRequest);\n    let normalizedHeaders = nodeRequest.headers;\n    if (nodeRequest.headers?.[':method']) {\n        normalizedHeaders = {};\n        for (const key in nodeRequest.headers) {\n            if (!key.startsWith(':')) {\n                normalizedHeaders[key] = nodeRequest.headers[key];\n            }\n        }\n    }\n    if (nodeResponse?.once) {\n        let sendAbortSignal;\n        // If ponyfilled\n        if (RequestCtor !== globalThis.Request) {\n            signal = new ServerAdapterRequestAbortSignal();\n            sendAbortSignal = () => signal.sendAbort();\n        }\n        else {\n            const controller = new AbortController();\n            signal = controller.signal;\n            sendAbortSignal = () => controller.abort();\n        }\n        const closeEventListener = () => {\n            if (signal && !signal.aborted) {\n                rawRequest.aborted = true;\n                sendAbortSignal();\n            }\n        };\n        nodeResponse.once('error', closeEventListener);\n        nodeResponse.once('close', closeEventListener);\n        nodeResponse.once('finish', () => {\n            nodeResponse.removeListener('close', closeEventListener);\n        });\n    }\n    if (nodeRequest.method === 'GET' || nodeRequest.method === 'HEAD') {\n        return new RequestCtor(fullUrl, {\n            method: nodeRequest.method,\n            headers: normalizedHeaders,\n            signal,\n        });\n    }\n    /**\n     * Some Node server frameworks like Serverless Express sends a dummy object with body but as a Buffer not string\n     * so we do those checks to see is there something we can use directly as BodyInit\n     * because the presence of body means the request stream is already consumed and,\n     * rawRequest cannot be used as BodyInit/ReadableStream by Fetch API in this case.\n     */\n    const maybeParsedBody = nodeRequest.body;\n    if (maybeParsedBody != null && Object.keys(maybeParsedBody).length > 0) {\n        if (isRequestBody(maybeParsedBody)) {\n            return new RequestCtor(fullUrl, {\n                method: nodeRequest.method,\n                headers: normalizedHeaders,\n                body: maybeParsedBody,\n                signal,\n            });\n        }\n        const request = new RequestCtor(fullUrl, {\n            method: nodeRequest.method,\n            headers: normalizedHeaders,\n            signal,\n        });\n        if (!request.headers.get('content-type')?.includes('json')) {\n            request.headers.set('content-type', 'application/json; charset=utf-8');\n        }\n        return new Proxy(request, {\n            get: (target, prop, receiver) => {\n                switch (prop) {\n                    case 'json':\n                        return async () => maybeParsedBody;\n                    case 'text':\n                        return async () => JSON.stringify(maybeParsedBody);\n                    default:\n                        return Reflect.get(target, prop, receiver);\n                }\n            },\n        });\n    }\n    // Temporary workaround for a bug in Bun Node compat mode\n    if (globalThis.process?.versions?.bun && isReadable(rawRequest)) {\n        if (!bunNodeCompatModeWarned) {\n            bunNodeCompatModeWarned = true;\n            console.warn(`You use Bun Node compatibility mode, which is not recommended!\nIt will affect your performance. Please check our Bun integration recipe, and avoid using 'http' for your server implementation.`);\n        }\n        return new RequestCtor(fullUrl, {\n            method: nodeRequest.method,\n            headers: normalizedHeaders,\n            duplex: 'half',\n            body: new ReadableStream({\n                start(controller) {\n                    rawRequest.on('data', chunk => {\n                        controller.enqueue(chunk);\n                    });\n                    rawRequest.on('error', e => {\n                        controller.error(e);\n                    });\n                    rawRequest.on('end', () => {\n                        controller.close();\n                    });\n                },\n                cancel(e) {\n                    rawRequest.destroy(e);\n                },\n            }),\n            signal,\n        });\n    }\n    // perf: instead of spreading the object, we can just pass it as is and it performs better\n    return new RequestCtor(fullUrl, {\n        method: nodeRequest.method,\n        headers: normalizedHeaders,\n        body: rawRequest,\n        duplex: 'half',\n        signal,\n    });\n}\nfunction isReadable(stream) {\n    return stream.read != null;\n}\nfunction isNodeRequest(request) {\n    return isReadable(request);\n}\nfunction isServerResponse(stream) {\n    // Check all used functions are defined\n    return (stream != null &&\n        stream.setHeader != null &&\n        stream.end != null &&\n        stream.once != null &&\n        stream.write != null);\n}\nfunction isReadableStream(stream) {\n    return stream != null && stream.getReader != null;\n}\nfunction isFetchEvent(event) {\n    return event != null && event.request != null && event.respondWith != null;\n}\nfunction configureSocket(rawRequest) {\n    rawRequest?.socket?.setTimeout?.(0);\n    rawRequest?.socket?.setNoDelay?.(true);\n    rawRequest?.socket?.setKeepAlive?.(true);\n}\nfunction endResponse(serverResponse) {\n    // @ts-expect-error Avoid arguments adaptor trampoline https://v8.dev/blog/adaptor-frame\n    serverResponse.end(null, null, null);\n}\nasync function sendAsyncIterable(serverResponse, asyncIterable) {\n    let closed = false;\n    const closeEventListener = () => {\n        closed = true;\n    };\n    serverResponse.once('error', closeEventListener);\n    serverResponse.once('close', closeEventListener);\n    serverResponse.once('finish', () => {\n        serverResponse.removeListener('close', closeEventListener);\n    });\n    for await (const chunk of asyncIterable) {\n        if (closed) {\n            break;\n        }\n        if (!serverResponse\n            // @ts-expect-error http and http2 writes are actually compatible\n            .write(chunk)) {\n            if (closed) {\n                break;\n            }\n            await new Promise(resolve => serverResponse.once('drain', resolve));\n        }\n    }\n    endResponse(serverResponse);\n}\nfunction sendNodeResponse(fetchResponse, serverResponse, nodeRequest) {\n    if (serverResponse.closed || serverResponse.destroyed || serverResponse.writableEnded) {\n        return;\n    }\n    if (!fetchResponse) {\n        serverResponse.statusCode = 404;\n        serverResponse.end();\n        return;\n    }\n    serverResponse.statusCode = fetchResponse.status;\n    serverResponse.statusMessage = fetchResponse.statusText;\n    let setCookiesSet = false;\n    fetchResponse.headers.forEach((value, key) => {\n        if (key === 'set-cookie') {\n            if (setCookiesSet) {\n                return;\n            }\n            setCookiesSet = true;\n            const setCookies = fetchResponse.headers.getSetCookie?.();\n            if (setCookies) {\n                serverResponse.setHeader('set-cookie', setCookies);\n                return;\n            }\n        }\n        serverResponse.setHeader(key, value);\n    });\n    // Optimizations for node-fetch\n    const bufOfRes = fetchResponse._buffer;\n    if (bufOfRes) {\n        // @ts-expect-error http and http2 writes are actually compatible\n        serverResponse.write(bufOfRes);\n        endResponse(serverResponse);\n        return;\n    }\n    // Other fetch implementations\n    const fetchBody = fetchResponse.body;\n    if (fetchBody == null) {\n        endResponse(serverResponse);\n        return;\n    }\n    if (fetchBody[Symbol.toStringTag] === 'Uint8Array') {\n        serverResponse\n            // @ts-expect-error http and http2 writes are actually compatible\n            .write(fetchBody);\n        endResponse(serverResponse);\n        return;\n    }\n    configureSocket(nodeRequest);\n    if (isReadable(fetchBody)) {\n        serverResponse.once('close', () => {\n            fetchBody.destroy();\n        });\n        fetchBody.pipe(serverResponse);\n        return;\n    }\n    if (isAsyncIterable(fetchBody)) {\n        return sendAsyncIterable(serverResponse, fetchBody);\n    }\n}\nfunction isRequestInit(val) {\n    return (val != null &&\n        typeof val === 'object' &&\n        ('body' in val ||\n            'cache' in val ||\n            'credentials' in val ||\n            'headers' in val ||\n            'integrity' in val ||\n            'keepalive' in val ||\n            'method' in val ||\n            'mode' in val ||\n            'redirect' in val ||\n            'referrer' in val ||\n            'referrerPolicy' in val ||\n            'signal' in val ||\n            'window' in val));\n}\n// from https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign#copying_accessors\nfunction completeAssign(...args) {\n    const [target, ...sources] = args.filter(arg => arg != null && typeof arg === 'object');\n    sources.forEach(source => {\n        // modified Object.keys to Object.getOwnPropertyNames\n        // because Object.keys only returns enumerable properties\n        const descriptors = Object.getOwnPropertyNames(source).reduce((descriptors, key) => {\n            const descriptor = Object.getOwnPropertyDescriptor(source, key);\n            if (descriptor) {\n                descriptors[key] = Object.getOwnPropertyDescriptor(source, key);\n            }\n            return descriptors;\n        }, {});\n        // By default, Object.assign copies enumerable Symbols, too\n        Object.getOwnPropertySymbols(source).forEach(sym => {\n            const descriptor = Object.getOwnPropertyDescriptor(source, sym);\n            if (descriptor?.enumerable) {\n                descriptors[sym] = descriptor;\n            }\n        });\n        Object.defineProperties(target, descriptors);\n    });\n    return target;\n}\nfunction isPromise(val) {\n    return val?.then != null;\n}\nfunction iterateAsyncVoid(iterable, callback) {\n    const iterator = iterable[Symbol.iterator]();\n    let stopEarlyFlag = false;\n    function stopEarlyFn() {\n        stopEarlyFlag = true;\n    }\n    function iterate() {\n        const { done: endOfIterator, value } = iterator.next();\n        if (endOfIterator) {\n            return;\n        }\n        const result$ = callback(value, stopEarlyFn);\n        if (isPromise(result$)) {\n            return result$.then(() => {\n                if (stopEarlyFlag) {\n                    return;\n                }\n                return iterate();\n            });\n        }\n        if (stopEarlyFlag) {\n            return;\n        }\n        return iterate();\n    }\n    return iterate();\n}\nfunction handleErrorFromRequestHandler(error, ResponseCtor) {\n    return new ResponseCtor(error.stack || error.message || error.toString(), {\n        status: error.status || 500,\n    });\n}\nfunction isolateObject(originalCtx, waitUntilPromises) {\n    if (originalCtx == null) {\n        if (waitUntilPromises == null) {\n            return {};\n        }\n        originalCtx = {};\n    }\n    const extraProps = {};\n    const deletedProps = new Set();\n    return new Proxy(originalCtx, {\n        get(originalCtx, prop) {\n            if (waitUntilPromises != null && prop === 'waitUntil') {\n                return function waitUntil(promise) {\n                    waitUntilPromises.push(promise.catch(err => console.error(err)));\n                };\n            }\n            const extraPropVal = extraProps[prop];\n            if (extraPropVal != null) {\n                if (typeof extraPropVal === 'function') {\n                    return extraPropVal.bind(extraProps);\n                }\n                return extraPropVal;\n            }\n            if (deletedProps.has(prop)) {\n                return undefined;\n            }\n            return originalCtx[prop];\n        },\n        set(_originalCtx, prop, value) {\n            extraProps[prop] = value;\n            return true;\n        },\n        has(originalCtx, prop) {\n            if (waitUntilPromises != null && prop === 'waitUntil') {\n                return true;\n            }\n            if (deletedProps.has(prop)) {\n                return false;\n            }\n            if (prop in extraProps) {\n                return true;\n            }\n            return prop in originalCtx;\n        },\n        defineProperty(_originalCtx, prop, descriptor) {\n            return Reflect.defineProperty(extraProps, prop, descriptor);\n        },\n        deleteProperty(_originalCtx, prop) {\n            if (prop in extraProps) {\n                return Reflect.deleteProperty(extraProps, prop);\n            }\n            deletedProps.add(prop);\n            return true;\n        },\n        ownKeys(originalCtx) {\n            const extraKeys = Reflect.ownKeys(extraProps);\n            const originalKeys = Reflect.ownKeys(originalCtx);\n            const deletedKeys = Array.from(deletedProps);\n            const allKeys = new Set(extraKeys.concat(originalKeys.filter(keys => !deletedKeys.includes(keys))));\n            if (waitUntilPromises != null) {\n                allKeys.add('waitUntil');\n            }\n            return Array.from(allKeys);\n        },\n        getOwnPropertyDescriptor(originalCtx, prop) {\n            if (prop in extraProps) {\n                return Reflect.getOwnPropertyDescriptor(extraProps, prop);\n            }\n            if (deletedProps.has(prop)) {\n                return undefined;\n            }\n            return Reflect.getOwnPropertyDescriptor(originalCtx, prop);\n        },\n    });\n}\nfunction createDeferredPromise() {\n    let resolveFn;\n    let rejectFn;\n    const promise = new Promise(function deferredPromiseExecutor(resolve, reject) {\n        resolveFn = resolve;\n        rejectFn = reject;\n    });\n    return {\n        promise,\n        get resolve() {\n            return resolveFn;\n        },\n        get reject() {\n            return rejectFn;\n        },\n    };\n}\nfunction handleAbortSignalAndPromiseResponse(response$, abortSignal) {\n    if (isPromise(response$) && abortSignal) {\n        const deferred$ = createDeferredPromise();\n        abortSignal.addEventListener('abort', function abortSignalFetchErrorHandler() {\n            deferred$.reject(abortSignal.reason);\n        });\n        response$\n            .then(function fetchSuccessHandler(res) {\n            deferred$.resolve(res);\n        })\n            .catch(function fetchErrorHandler(err) {\n            deferred$.reject(err);\n        });\n        return deferred$.promise;\n    }\n    return response$;\n}\nexports.decompressedResponseMap = new WeakMap();\nconst supportedEncodingsByFetchAPI = new WeakMap();\nfunction getSupportedEncodings(fetchAPI) {\n    let supportedEncodings = supportedEncodingsByFetchAPI.get(fetchAPI);\n    if (!supportedEncodings) {\n        const possibleEncodings = ['deflate', 'gzip', 'deflate-raw', 'br'];\n        if (fetchAPI.DecompressionStream['supportedFormats']) {\n            supportedEncodings = fetchAPI.DecompressionStream['supportedFormats'];\n        }\n        else {\n            supportedEncodings = possibleEncodings.filter(encoding => {\n                // deflate-raw is not supported in Node.js >v20\n                if (globalThis.process?.version?.startsWith('v2') &&\n                    fetchAPI.DecompressionStream === globalThis.DecompressionStream &&\n                    encoding === 'deflate-raw') {\n                    return false;\n                }\n                try {\n                    // eslint-disable-next-line no-new\n                    new fetchAPI.DecompressionStream(encoding);\n                    return true;\n                }\n                catch {\n                    return false;\n                }\n            });\n        }\n        supportedEncodingsByFetchAPI.set(fetchAPI, supportedEncodings);\n    }\n    return supportedEncodings;\n}\nfunction handleResponseDecompression(response, fetchAPI) {\n    const contentEncodingHeader = response?.headers.get('content-encoding');\n    if (!contentEncodingHeader || contentEncodingHeader === 'none') {\n        return response;\n    }\n    if (!response?.body) {\n        return response;\n    }\n    let decompressedResponse = exports.decompressedResponseMap.get(response);\n    if (!decompressedResponse || decompressedResponse.bodyUsed) {\n        let decompressedBody = response.body;\n        const contentEncodings = contentEncodingHeader.split(',');\n        if (!contentEncodings.every(encoding => getSupportedEncodings(fetchAPI).includes(encoding))) {\n            return new fetchAPI.Response(`Unsupported 'Content-Encoding': ${contentEncodingHeader}`, {\n                status: 415,\n                statusText: 'Unsupported Media Type',\n            });\n        }\n        for (const contentEncoding of contentEncodings) {\n            decompressedBody = decompressedBody.pipeThrough(new fetchAPI.DecompressionStream(contentEncoding));\n        }\n        decompressedResponse = new fetchAPI.Response(decompressedBody, response);\n        exports.decompressedResponseMap.set(response, decompressedResponse);\n    }\n    return decompressedResponse;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/uwebsockets.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/uwebsockets.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isUWSResponse = isUWSResponse;\nexports.getRequestFromUWSRequest = getRequestFromUWSRequest;\nexports.sendResponseToUwsOpts = sendResponseToUwsOpts;\nfunction isUWSResponse(res) {\n    return !!res.onData;\n}\nfunction getRequestFromUWSRequest({ req, res, fetchAPI, signal }) {\n    let body;\n    const method = req.getMethod();\n    if (method !== 'get' && method !== 'head') {\n        let controller;\n        body = new fetchAPI.ReadableStream({\n            start(c) {\n                controller = c;\n            },\n        });\n        const readable = body.readable;\n        if (readable) {\n            signal.addEventListener('abort', () => {\n                readable.push(null);\n            });\n            res.onData(function (ab, isLast) {\n                const chunk = Buffer.from(ab, 0, ab.byteLength);\n                readable.push(Buffer.from(chunk));\n                if (isLast) {\n                    readable.push(null);\n                }\n            });\n        }\n        else {\n            let closed = false;\n            signal.addEventListener('abort', () => {\n                if (!closed) {\n                    closed = true;\n                    controller.close();\n                }\n            });\n            res.onData(function (ab, isLast) {\n                const chunk = Buffer.from(ab, 0, ab.byteLength);\n                controller.enqueue(Buffer.from(chunk));\n                if (isLast) {\n                    closed = true;\n                    controller.close();\n                }\n            });\n        }\n    }\n    const headers = new fetchAPI.Headers();\n    req.forEach((key, value) => {\n        headers.append(key, value);\n    });\n    let url = `http://localhost${req.getUrl()}`;\n    const query = req.getQuery();\n    if (query) {\n        url += `?${query}`;\n    }\n    return new fetchAPI.Request(url, {\n        method,\n        headers,\n        body: body,\n        signal,\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore - not in the TS types yet\n        duplex: 'half',\n    });\n}\nasync function forwardResponseBodyToUWSResponse(uwsResponse, fetchResponse, signal) {\n    for await (const chunk of fetchResponse.body) {\n        if (signal.aborted) {\n            return;\n        }\n        uwsResponse.cork(() => {\n            uwsResponse.write(chunk);\n        });\n    }\n    uwsResponse.cork(() => {\n        uwsResponse.end();\n    });\n}\nfunction sendResponseToUwsOpts(uwsResponse, fetchResponse, signal) {\n    if (!fetchResponse) {\n        uwsResponse.writeStatus('404 Not Found');\n        uwsResponse.end();\n        return;\n    }\n    const bufferOfRes = fetchResponse._buffer;\n    if (signal.aborted) {\n        return;\n    }\n    uwsResponse.cork(() => {\n        uwsResponse.writeStatus(`${fetchResponse.status} ${fetchResponse.statusText}`);\n        for (const [key, value] of fetchResponse.headers) {\n            // content-length causes an error with Node.js's fetch\n            if (key !== 'content-length') {\n                if (key === 'set-cookie') {\n                    const setCookies = fetchResponse.headers.getSetCookie?.();\n                    if (setCookies) {\n                        for (const setCookie of setCookies) {\n                            uwsResponse.writeHeader(key, setCookie);\n                        }\n                        continue;\n                    }\n                }\n                uwsResponse.writeHeader(key, value);\n            }\n        }\n        if (bufferOfRes) {\n            uwsResponse.end(bufferOfRes);\n        }\n    });\n    if (bufferOfRes) {\n        return;\n    }\n    if (!fetchResponse.body) {\n        uwsResponse.end();\n        return;\n    }\n    return forwardResponseBodyToUWSResponse(uwsResponse, fetchResponse, signal);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+server@0.9.49/node_modules/@whatwg-node/server/cjs/uwebsockets.js\n");

/***/ })

};
;