"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/property-information@7.1.0";
exports.ids = ["vendor-chunks/property-information@7.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/index.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/index.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: () => (/* reexport safe */ _lib_find_js__WEBPACK_IMPORTED_MODULE_7__.find),\n/* harmony export */   hastToReact: () => (/* reexport safe */ _lib_hast_to_react_js__WEBPACK_IMPORTED_MODULE_0__.hastToReact),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   normalize: () => (/* reexport safe */ _lib_normalize_js__WEBPACK_IMPORTED_MODULE_8__.normalize),\n/* harmony export */   svg: () => (/* binding */ svg)\n/* harmony export */ });\n/* harmony import */ var _lib_util_merge_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/util/merge.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/merge.js\");\n/* harmony import */ var _lib_aria_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/aria.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/aria.js\");\n/* harmony import */ var _lib_html_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/html.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/html.js\");\n/* harmony import */ var _lib_svg_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/svg.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/svg.js\");\n/* harmony import */ var _lib_xlink_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/xlink.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/xlink.js\");\n/* harmony import */ var _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/xmlns.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/xmlns.js\");\n/* harmony import */ var _lib_xml_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/xml.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/xml.js\");\n/* harmony import */ var _lib_hast_to_react_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/hast-to-react.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/hast-to-react.js\");\n/* harmony import */ var _lib_find_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/find.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/find.js\");\n/* harmony import */ var _lib_normalize_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/normalize.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/normalize.js\");\n// Note: types exposed from `index.d.ts`.\n\n\n\n\n\n\n\n\n\n\nconst html = (0,_lib_util_merge_js__WEBPACK_IMPORTED_MODULE_1__.merge)([_lib_aria_js__WEBPACK_IMPORTED_MODULE_2__.aria, _lib_html_js__WEBPACK_IMPORTED_MODULE_3__.html, _lib_xlink_js__WEBPACK_IMPORTED_MODULE_4__.xlink, _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_5__.xmlns, _lib_xml_js__WEBPACK_IMPORTED_MODULE_6__.xml], 'html')\n\n\n\n\nconst svg = (0,_lib_util_merge_js__WEBPACK_IMPORTED_MODULE_1__.merge)([_lib_aria_js__WEBPACK_IMPORTED_MODULE_2__.aria, _lib_svg_js__WEBPACK_IMPORTED_MODULE_9__.svg, _lib_xlink_js__WEBPACK_IMPORTED_MODULE_4__.xlink, _lib_xmlns_js__WEBPACK_IMPORTED_MODULE_5__.xmlns, _lib_xml_js__WEBPACK_IMPORTED_MODULE_6__.xml], 'svg')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ3lDO0FBQ1A7QUFDWTtBQUNIO0FBQ1A7QUFDQTtBQUNKOztBQUVrQjs7QUFFM0MsYUFBYSx5REFBSyxFQUFFLDhDQUFJLEVBQUUsOENBQVEsRUFBRSxnREFBSyxFQUFFLGdEQUFLLEVBQUUsNENBQUc7O0FBRTFCO0FBQ1U7O0FBRXJDLFlBQVkseURBQUssRUFBRSw4Q0FBSSxFQUFFLDRDQUFPLEVBQUUsZ0RBQUssRUFBRSxnREFBSyxFQUFFLDRDQUFHIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIE5vdGU6IHR5cGVzIGV4cG9zZWQgZnJvbSBgaW5kZXguZC50c2AuXG5pbXBvcnQge21lcmdlfSBmcm9tICcuL2xpYi91dGlsL21lcmdlLmpzJ1xuaW1wb3J0IHthcmlhfSBmcm9tICcuL2xpYi9hcmlhLmpzJ1xuaW1wb3J0IHtodG1sIGFzIGh0bWxCYXNlfSBmcm9tICcuL2xpYi9odG1sLmpzJ1xuaW1wb3J0IHtzdmcgYXMgc3ZnQmFzZX0gZnJvbSAnLi9saWIvc3ZnLmpzJ1xuaW1wb3J0IHt4bGlua30gZnJvbSAnLi9saWIveGxpbmsuanMnXG5pbXBvcnQge3htbG5zfSBmcm9tICcuL2xpYi94bWxucy5qcydcbmltcG9ydCB7eG1sfSBmcm9tICcuL2xpYi94bWwuanMnXG5cbmV4cG9ydCB7aGFzdFRvUmVhY3R9IGZyb20gJy4vbGliL2hhc3QtdG8tcmVhY3QuanMnXG5cbmV4cG9ydCBjb25zdCBodG1sID0gbWVyZ2UoW2FyaWEsIGh0bWxCYXNlLCB4bGluaywgeG1sbnMsIHhtbF0sICdodG1sJylcblxuZXhwb3J0IHtmaW5kfSBmcm9tICcuL2xpYi9maW5kLmpzJ1xuZXhwb3J0IHtub3JtYWxpemV9IGZyb20gJy4vbGliL25vcm1hbGl6ZS5qcydcblxuZXhwb3J0IGNvbnN0IHN2ZyA9IG1lcmdlKFthcmlhLCBzdmdCYXNlLCB4bGluaywgeG1sbnMsIHhtbF0sICdzdmcnKVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/aria.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/aria.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aria: () => (/* binding */ aria)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/types.js\");\n\n\n\nconst aria = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaChecked: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaColCount: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaColIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaColSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaControls: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaDropEffect: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaFlowTo: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaGrabbed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaHasPopup: null,\n    ariaHidden: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaLevel: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaLive: null,\n    ariaModal: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaMultiLine: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaMultiSelectable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaOrientation: null,\n    ariaOwns: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaPressed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaReadOnly: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaRelevant: null,\n    ariaRequired: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaRoleDescription: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    ariaRowCount: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaRowIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaRowSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaSelected: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    ariaSetSize: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaSort: null,\n    ariaValueMax: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueMin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueNow: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    ariaValueText: null,\n    role: null\n  },\n  transform(_, property) {\n    return property === 'role'\n      ? property\n      : 'aria-' + property.slice(4).toLowerCase()\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/aria.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/find.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/find.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   find: () => (/* binding */ find)\n/* harmony export */ });\n/* harmony import */ var _util_defined_info_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/defined-info.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/defined-info.js\");\n/* harmony import */ var _util_info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/info.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/info.js\");\n/* harmony import */ var _normalize_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./normalize.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/normalize.js\");\n/**\n * @import {Schema} from 'property-information'\n */\n\n\n\n\n\nconst cap = /[A-Z]/g\nconst dash = /-[a-z]/g\nconst valid = /^data[-\\w.:]+$/i\n\n/**\n * Look up info on a property.\n *\n * In most cases the given `schema` contains info on the property.\n * All standard,\n * most legacy,\n * and some non-standard properties are supported.\n * For these cases,\n * the returned `Info` has hints about the value of the property.\n *\n * `name` can also be a valid data attribute or property,\n * in which case an `Info` object with the correctly cased `attribute` and\n * `property` is returned.\n *\n * `name` can be an unknown attribute,\n * in which case an `Info` object with `attribute` and `property` set to the\n * given name is returned.\n * It is not recommended to provide unsupported legacy or recently specced\n * properties.\n *\n *\n * @param {Schema} schema\n *   Schema;\n *   either the `html` or `svg` export.\n * @param {string} value\n *   An attribute-like or property-like name;\n *   it will be passed through `normalize` to hopefully find the correct info.\n * @returns {Info}\n *   Info.\n */\nfunction find(schema, value) {\n  const normal = (0,_normalize_js__WEBPACK_IMPORTED_MODULE_0__.normalize)(value)\n  let property = value\n  let Type = _util_info_js__WEBPACK_IMPORTED_MODULE_1__.Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase)\n      property = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1)\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4)\n\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab)\n\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes\n        }\n\n        value = 'data' + dashes\n      }\n    }\n\n    Type = _util_defined_info_js__WEBPACK_IMPORTED_MODULE_2__.DefinedInfo\n  }\n\n  return new Type(property, value)\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Kebab.\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Camel.\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/find.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/hast-to-react.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/hast-to-react.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hastToReact: () => (/* binding */ hastToReact)\n/* harmony export */ });\n/**\n * Special cases for React (`Record<string, string>`).\n *\n * `hast` is close to `React` but differs in a couple of cases.\n * To get a React property from a hast property,\n * check if it is in `hastToReact`.\n * If it is, use the corresponding value;\n * otherwise, use the hast property.\n *\n * @type {Record<string, string>}\n */\nconst hastToReact = {\n  classId: 'classID',\n  dataType: 'datatype',\n  itemId: 'itemID',\n  strokeDashArray: 'strokeDasharray',\n  strokeDashOffset: 'strokeDashoffset',\n  strokeLineCap: 'strokeLinecap',\n  strokeLineJoin: 'strokeLinejoin',\n  strokeMiterLimit: 'strokeMiterlimit',\n  typeOf: 'typeof',\n  xLinkActuate: 'xlinkActuate',\n  xLinkArcRole: 'xlinkArcrole',\n  xLinkHref: 'xlinkHref',\n  xLinkRole: 'xlinkRole',\n  xLinkShow: 'xlinkShow',\n  xLinkTitle: 'xlinkTitle',\n  xLinkType: 'xlinkType',\n  xmlnsXLink: 'xmlnsXlink'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi9oYXN0LXRvLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL3Byb3BlcnR5LWluZm9ybWF0aW9uQDcuMS4wL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvaGFzdC10by1yZWFjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFNwZWNpYWwgY2FzZXMgZm9yIFJlYWN0IChgUmVjb3JkPHN0cmluZywgc3RyaW5nPmApLlxuICpcbiAqIGBoYXN0YCBpcyBjbG9zZSB0byBgUmVhY3RgIGJ1dCBkaWZmZXJzIGluIGEgY291cGxlIG9mIGNhc2VzLlxuICogVG8gZ2V0IGEgUmVhY3QgcHJvcGVydHkgZnJvbSBhIGhhc3QgcHJvcGVydHksXG4gKiBjaGVjayBpZiBpdCBpcyBpbiBgaGFzdFRvUmVhY3RgLlxuICogSWYgaXQgaXMsIHVzZSB0aGUgY29ycmVzcG9uZGluZyB2YWx1ZTtcbiAqIG90aGVyd2lzZSwgdXNlIHRoZSBoYXN0IHByb3BlcnR5LlxuICpcbiAqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fVxuICovXG5leHBvcnQgY29uc3QgaGFzdFRvUmVhY3QgPSB7XG4gIGNsYXNzSWQ6ICdjbGFzc0lEJyxcbiAgZGF0YVR5cGU6ICdkYXRhdHlwZScsXG4gIGl0ZW1JZDogJ2l0ZW1JRCcsXG4gIHN0cm9rZURhc2hBcnJheTogJ3N0cm9rZURhc2hhcnJheScsXG4gIHN0cm9rZURhc2hPZmZzZXQ6ICdzdHJva2VEYXNob2Zmc2V0JyxcbiAgc3Ryb2tlTGluZUNhcDogJ3N0cm9rZUxpbmVjYXAnLFxuICBzdHJva2VMaW5lSm9pbjogJ3N0cm9rZUxpbmVqb2luJyxcbiAgc3Ryb2tlTWl0ZXJMaW1pdDogJ3N0cm9rZU1pdGVybGltaXQnLFxuICB0eXBlT2Y6ICd0eXBlb2YnLFxuICB4TGlua0FjdHVhdGU6ICd4bGlua0FjdHVhdGUnLFxuICB4TGlua0FyY1JvbGU6ICd4bGlua0FyY3JvbGUnLFxuICB4TGlua0hyZWY6ICd4bGlua0hyZWYnLFxuICB4TGlua1JvbGU6ICd4bGlua1JvbGUnLFxuICB4TGlua1Nob3c6ICd4bGlua1Nob3cnLFxuICB4TGlua1RpdGxlOiAneGxpbmtUaXRsZScsXG4gIHhMaW5rVHlwZTogJ3hsaW5rVHlwZScsXG4gIHhtbG5zWExpbms6ICd4bWxuc1hsaW5rJ1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/hast-to-react.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/html.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/html.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   html: () => (/* binding */ html)\n/* harmony export */ });\n/* harmony import */ var _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/case-insensitive-transform.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/case-insensitive-transform.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/types.js\");\n\n\n\n\nconst html = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    acceptCharset: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    accessKey: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    allowPaymentRequest: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    allowUserMedia: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    alt: null,\n    as: null,\n    async: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    autoCapitalize: null,\n    autoComplete: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    autoFocus: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    autoPlay: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    blocking: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    capture: null,\n    charSet: null,\n    checked: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    cite: null,\n    className: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    cols: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    colSpan: null,\n    content: null,\n    contentEditable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    controls: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    controlsList: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    coords: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number | _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    defer: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    dir: null,\n    dirName: null,\n    disabled: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    download: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.overloadedBoolean,\n    draggable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    encType: null,\n    enterKeyHint: null,\n    fetchPriority: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    formTarget: null,\n    headers: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    height: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    hidden: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.overloadedBoolean,\n    high: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    href: null,\n    hrefLang: null,\n    htmlFor: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    httpEquiv: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: null,\n    inert: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    itemId: null,\n    itemProp: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    itemRef: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    itemScope: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    itemType: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    low: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    manifest: null,\n    max: null,\n    maxLength: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    multiple: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    muted: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    name: null,\n    nonce: null,\n    noModule: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    noValidate: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforeMatch: null,\n    onBeforePrint: null,\n    onBeforeToggle: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextLost: null,\n    onContextMenu: null,\n    onContextRestored: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onScrollEnd: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    optimum: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    pattern: null,\n    ping: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    placeholder: null,\n    playsInline: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    popover: null,\n    popoverTarget: null,\n    popoverTargetAction: null,\n    poster: null,\n    preload: null,\n    readOnly: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    referrerPolicy: null,\n    rel: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    required: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    reversed: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    rows: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    rowSpan: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    sandbox: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    scope: null,\n    scoped: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    seamless: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    selected: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    shadowRootClonable: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    shadowRootDelegatesFocus: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    shadowRootMode: null,\n    shape: null,\n    size: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    sizes: null,\n    slot: null,\n    span: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    spellCheck: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: null,\n    start: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    step: null,\n    style: null,\n    tabIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    useMap: null,\n    value: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish,\n    width: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    wrap: null,\n    writingSuggestions: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // Lists. Use CSS to reduce space between items instead\n    declare: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<img>` and `<object>`\n    leftMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    marginWidth: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    noResize: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<frame>`\n    noHref: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    disableRemotePlayback: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    prefix: null,\n    property: null,\n    results: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    security: null,\n    unselectable: null\n  },\n  space: 'html',\n  transform: _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_2__.caseInsensitiveTransform\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/normalize.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/normalize.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalize: () => (/* binding */ normalize)\n/* harmony export */ });\n/**\n * Get the cleaned case insensitive form of an attribute or property.\n *\n * @param {string} value\n *   An attribute-like or property-like name.\n * @returns {string}\n *   Value that can be used to look up the properly cased property on a\n *   `Schema`.\n */\nfunction normalize(value) {\n  return value.toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi9ub3JtYWxpemUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9wcm9wZXJ0eS1pbmZvcm1hdGlvbkA3LjEuMC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL25vcm1hbGl6ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEdldCB0aGUgY2xlYW5lZCBjYXNlIGluc2Vuc2l0aXZlIGZvcm0gb2YgYW4gYXR0cmlidXRlIG9yIHByb3BlcnR5LlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSB2YWx1ZVxuICogICBBbiBhdHRyaWJ1dGUtbGlrZSBvciBwcm9wZXJ0eS1saWtlIG5hbWUuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBWYWx1ZSB0aGF0IGNhbiBiZSB1c2VkIHRvIGxvb2sgdXAgdGhlIHByb3Blcmx5IGNhc2VkIHByb3BlcnR5IG9uIGFcbiAqICAgYFNjaGVtYWAuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBub3JtYWxpemUodmFsdWUpIHtcbiAgcmV0dXJuIHZhbHVlLnRvTG93ZXJDYXNlKClcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/normalize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/svg.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/svg.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   svg: () => (/* binding */ svg)\n/* harmony export */ });\n/* harmony import */ var _util_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util/case-sensitive-transform.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/case-sensitive-transform.js\");\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/types.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/types.js\");\n\n\n\n\nconst svg = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  attributes: {\n    accentHeight: 'accent-height',\n    alignmentBaseline: 'alignment-baseline',\n    arabicForm: 'arabic-form',\n    baselineShift: 'baseline-shift',\n    capHeight: 'cap-height',\n    className: 'class',\n    clipPath: 'clip-path',\n    clipRule: 'clip-rule',\n    colorInterpolation: 'color-interpolation',\n    colorInterpolationFilters: 'color-interpolation-filters',\n    colorProfile: 'color-profile',\n    colorRendering: 'color-rendering',\n    crossOrigin: 'crossorigin',\n    dataType: 'datatype',\n    dominantBaseline: 'dominant-baseline',\n    enableBackground: 'enable-background',\n    fillOpacity: 'fill-opacity',\n    fillRule: 'fill-rule',\n    floodColor: 'flood-color',\n    floodOpacity: 'flood-opacity',\n    fontFamily: 'font-family',\n    fontSize: 'font-size',\n    fontSizeAdjust: 'font-size-adjust',\n    fontStretch: 'font-stretch',\n    fontStyle: 'font-style',\n    fontVariant: 'font-variant',\n    fontWeight: 'font-weight',\n    glyphName: 'glyph-name',\n    glyphOrientationHorizontal: 'glyph-orientation-horizontal',\n    glyphOrientationVertical: 'glyph-orientation-vertical',\n    hrefLang: 'hreflang',\n    horizAdvX: 'horiz-adv-x',\n    horizOriginX: 'horiz-origin-x',\n    horizOriginY: 'horiz-origin-y',\n    imageRendering: 'image-rendering',\n    letterSpacing: 'letter-spacing',\n    lightingColor: 'lighting-color',\n    markerEnd: 'marker-end',\n    markerMid: 'marker-mid',\n    markerStart: 'marker-start',\n    navDown: 'nav-down',\n    navDownLeft: 'nav-down-left',\n    navDownRight: 'nav-down-right',\n    navLeft: 'nav-left',\n    navNext: 'nav-next',\n    navPrev: 'nav-prev',\n    navRight: 'nav-right',\n    navUp: 'nav-up',\n    navUpLeft: 'nav-up-left',\n    navUpRight: 'nav-up-right',\n    onAbort: 'onabort',\n    onActivate: 'onactivate',\n    onAfterPrint: 'onafterprint',\n    onBeforePrint: 'onbeforeprint',\n    onBegin: 'onbegin',\n    onCancel: 'oncancel',\n    onCanPlay: 'oncanplay',\n    onCanPlayThrough: 'oncanplaythrough',\n    onChange: 'onchange',\n    onClick: 'onclick',\n    onClose: 'onclose',\n    onCopy: 'oncopy',\n    onCueChange: 'oncuechange',\n    onCut: 'oncut',\n    onDblClick: 'ondblclick',\n    onDrag: 'ondrag',\n    onDragEnd: 'ondragend',\n    onDragEnter: 'ondragenter',\n    onDragExit: 'ondragexit',\n    onDragLeave: 'ondragleave',\n    onDragOver: 'ondragover',\n    onDragStart: 'ondragstart',\n    onDrop: 'ondrop',\n    onDurationChange: 'ondurationchange',\n    onEmptied: 'onemptied',\n    onEnd: 'onend',\n    onEnded: 'onended',\n    onError: 'onerror',\n    onFocus: 'onfocus',\n    onFocusIn: 'onfocusin',\n    onFocusOut: 'onfocusout',\n    onHashChange: 'onhashchange',\n    onInput: 'oninput',\n    onInvalid: 'oninvalid',\n    onKeyDown: 'onkeydown',\n    onKeyPress: 'onkeypress',\n    onKeyUp: 'onkeyup',\n    onLoad: 'onload',\n    onLoadedData: 'onloadeddata',\n    onLoadedMetadata: 'onloadedmetadata',\n    onLoadStart: 'onloadstart',\n    onMessage: 'onmessage',\n    onMouseDown: 'onmousedown',\n    onMouseEnter: 'onmouseenter',\n    onMouseLeave: 'onmouseleave',\n    onMouseMove: 'onmousemove',\n    onMouseOut: 'onmouseout',\n    onMouseOver: 'onmouseover',\n    onMouseUp: 'onmouseup',\n    onMouseWheel: 'onmousewheel',\n    onOffline: 'onoffline',\n    onOnline: 'ononline',\n    onPageHide: 'onpagehide',\n    onPageShow: 'onpageshow',\n    onPaste: 'onpaste',\n    onPause: 'onpause',\n    onPlay: 'onplay',\n    onPlaying: 'onplaying',\n    onPopState: 'onpopstate',\n    onProgress: 'onprogress',\n    onRateChange: 'onratechange',\n    onRepeat: 'onrepeat',\n    onReset: 'onreset',\n    onResize: 'onresize',\n    onScroll: 'onscroll',\n    onSeeked: 'onseeked',\n    onSeeking: 'onseeking',\n    onSelect: 'onselect',\n    onShow: 'onshow',\n    onStalled: 'onstalled',\n    onStorage: 'onstorage',\n    onSubmit: 'onsubmit',\n    onSuspend: 'onsuspend',\n    onTimeUpdate: 'ontimeupdate',\n    onToggle: 'ontoggle',\n    onUnload: 'onunload',\n    onVolumeChange: 'onvolumechange',\n    onWaiting: 'onwaiting',\n    onZoom: 'onzoom',\n    overlinePosition: 'overline-position',\n    overlineThickness: 'overline-thickness',\n    paintOrder: 'paint-order',\n    panose1: 'panose-1',\n    pointerEvents: 'pointer-events',\n    referrerPolicy: 'referrerpolicy',\n    renderingIntent: 'rendering-intent',\n    shapeRendering: 'shape-rendering',\n    stopColor: 'stop-color',\n    stopOpacity: 'stop-opacity',\n    strikethroughPosition: 'strikethrough-position',\n    strikethroughThickness: 'strikethrough-thickness',\n    strokeDashArray: 'stroke-dasharray',\n    strokeDashOffset: 'stroke-dashoffset',\n    strokeLineCap: 'stroke-linecap',\n    strokeLineJoin: 'stroke-linejoin',\n    strokeMiterLimit: 'stroke-miterlimit',\n    strokeOpacity: 'stroke-opacity',\n    strokeWidth: 'stroke-width',\n    tabIndex: 'tabindex',\n    textAnchor: 'text-anchor',\n    textDecoration: 'text-decoration',\n    textRendering: 'text-rendering',\n    transformOrigin: 'transform-origin',\n    typeOf: 'typeof',\n    underlinePosition: 'underline-position',\n    underlineThickness: 'underline-thickness',\n    unicodeBidi: 'unicode-bidi',\n    unicodeRange: 'unicode-range',\n    unitsPerEm: 'units-per-em',\n    vAlphabetic: 'v-alphabetic',\n    vHanging: 'v-hanging',\n    vIdeographic: 'v-ideographic',\n    vMathematical: 'v-mathematical',\n    vectorEffect: 'vector-effect',\n    vertAdvY: 'vert-adv-y',\n    vertOriginX: 'vert-origin-x',\n    vertOriginY: 'vert-origin-y',\n    wordSpacing: 'word-spacing',\n    writingMode: 'writing-mode',\n    xHeight: 'x-height',\n    // These were camelcased in Tiny. Now lowercased in SVG 2\n    playbackOrder: 'playbackorder',\n    timelineBegin: 'timelinebegin'\n  },\n  properties: {\n    about: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    accentHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    accumulate: null,\n    additive: null,\n    alignmentBaseline: null,\n    alphabetic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    amplitude: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    arabicForm: null,\n    ascent: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    attributeName: null,\n    attributeType: null,\n    azimuth: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    bandwidth: null,\n    baselineShift: null,\n    baseFrequency: null,\n    baseProfile: null,\n    bbox: null,\n    begin: null,\n    bias: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    by: null,\n    calcMode: null,\n    capHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    className: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    clip: null,\n    clipPath: null,\n    clipPathUnits: null,\n    clipRule: null,\n    color: null,\n    colorInterpolation: null,\n    colorInterpolationFilters: null,\n    colorProfile: null,\n    colorRendering: null,\n    content: null,\n    contentScriptType: null,\n    contentStyleType: null,\n    crossOrigin: null,\n    cursor: null,\n    cx: null,\n    cy: null,\n    d: null,\n    dataType: null,\n    defaultAction: null,\n    descent: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    diffuseConstant: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    direction: null,\n    display: null,\n    dur: null,\n    divisor: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    dominantBaseline: null,\n    download: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.boolean,\n    dx: null,\n    dy: null,\n    edgeMode: null,\n    editable: null,\n    elevation: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    enableBackground: null,\n    end: null,\n    event: null,\n    exponent: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    externalResourcesRequired: null,\n    fill: null,\n    fillOpacity: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    fillRule: null,\n    filter: null,\n    filterRes: null,\n    filterUnits: null,\n    floodColor: null,\n    floodOpacity: null,\n    focusable: null,\n    focusHighlight: null,\n    fontFamily: null,\n    fontSize: null,\n    fontSizeAdjust: null,\n    fontStretch: null,\n    fontStyle: null,\n    fontVariant: null,\n    fontWeight: null,\n    format: null,\n    fr: null,\n    from: null,\n    fx: null,\n    fy: null,\n    g1: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    g2: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    glyphName: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaSeparated,\n    glyphOrientationHorizontal: null,\n    glyphOrientationVertical: null,\n    glyphRef: null,\n    gradientTransform: null,\n    gradientUnits: null,\n    handler: null,\n    hanging: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    hatchContentUnits: null,\n    hatchUnits: null,\n    height: null,\n    href: null,\n    hrefLang: null,\n    horizAdvX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    horizOriginX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    horizOriginY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    id: null,\n    ideographic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    imageRendering: null,\n    initialVisibility: null,\n    in: null,\n    in2: null,\n    intercept: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k1: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k2: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k3: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    k4: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    kernelMatrix: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    kernelUnitLength: null,\n    keyPoints: null, // SEMI_COLON_SEPARATED\n    keySplines: null, // SEMI_COLON_SEPARATED\n    keyTimes: null, // SEMI_COLON_SEPARATED\n    kerning: null,\n    lang: null,\n    lengthAdjust: null,\n    letterSpacing: null,\n    lightingColor: null,\n    limitingConeAngle: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    local: null,\n    markerEnd: null,\n    markerMid: null,\n    markerStart: null,\n    markerHeight: null,\n    markerUnits: null,\n    markerWidth: null,\n    mask: null,\n    maskContentUnits: null,\n    maskUnits: null,\n    mathematical: null,\n    max: null,\n    media: null,\n    mediaCharacterEncoding: null,\n    mediaContentEncodings: null,\n    mediaSize: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    mediaTime: null,\n    method: null,\n    min: null,\n    mode: null,\n    name: null,\n    navDown: null,\n    navDownLeft: null,\n    navDownRight: null,\n    navLeft: null,\n    navNext: null,\n    navPrev: null,\n    navRight: null,\n    navUp: null,\n    navUpLeft: null,\n    navUpRight: null,\n    numOctaves: null,\n    observer: null,\n    offset: null,\n    onAbort: null,\n    onActivate: null,\n    onAfterPrint: null,\n    onBeforePrint: null,\n    onBegin: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnd: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFocusIn: null,\n    onFocusOut: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onMouseWheel: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRepeat: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onShow: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onZoom: null,\n    opacity: null,\n    operator: null,\n    order: null,\n    orient: null,\n    orientation: null,\n    origin: null,\n    overflow: null,\n    overlay: null,\n    overlinePosition: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    overlineThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    paintOrder: null,\n    panose1: null,\n    path: null,\n    pathLength: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    patternContentUnits: null,\n    patternTransform: null,\n    patternUnits: null,\n    phase: null,\n    ping: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.spaceSeparated,\n    pitch: null,\n    playbackOrder: null,\n    pointerEvents: null,\n    points: null,\n    pointsAtX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    pointsAtY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    pointsAtZ: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    preserveAlpha: null,\n    preserveAspectRatio: null,\n    primitiveUnits: null,\n    propagate: null,\n    property: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    r: null,\n    radius: null,\n    referrerPolicy: null,\n    refX: null,\n    refY: null,\n    rel: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    rev: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    renderingIntent: null,\n    repeatCount: null,\n    repeatDur: null,\n    requiredExtensions: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    requiredFeatures: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    requiredFonts: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    requiredFormats: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    resource: null,\n    restart: null,\n    result: null,\n    rotate: null,\n    rx: null,\n    ry: null,\n    scale: null,\n    seed: null,\n    shapeRendering: null,\n    side: null,\n    slope: null,\n    snapshotTime: null,\n    specularConstant: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    specularExponent: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    spreadMethod: null,\n    spacing: null,\n    startOffset: null,\n    stdDeviation: null,\n    stemh: null,\n    stemv: null,\n    stitchTiles: null,\n    stopColor: null,\n    stopOpacity: null,\n    strikethroughPosition: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    strikethroughThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    string: null,\n    stroke: null,\n    strokeDashArray: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    strokeDashOffset: null,\n    strokeLineCap: null,\n    strokeLineJoin: null,\n    strokeMiterLimit: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    strokeOpacity: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    strokeWidth: null,\n    style: null,\n    surfaceScale: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    syncBehavior: null,\n    syncBehaviorDefault: null,\n    syncMaster: null,\n    syncTolerance: null,\n    syncToleranceDefault: null,\n    systemLanguage: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    tabIndex: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    tableValues: null,\n    target: null,\n    targetX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    targetY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    textAnchor: null,\n    textDecoration: null,\n    textRendering: null,\n    textLength: null,\n    timelineBegin: null,\n    title: null,\n    transformBehavior: null,\n    type: null,\n    typeOf: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.commaOrSpaceSeparated,\n    to: null,\n    transform: null,\n    transformOrigin: null,\n    u1: null,\n    u2: null,\n    underlinePosition: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    underlineThickness: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    unicode: null,\n    unicodeBidi: null,\n    unicodeRange: null,\n    unitsPerEm: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    values: null,\n    vAlphabetic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vMathematical: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vectorEffect: null,\n    vHanging: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vIdeographic: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    version: null,\n    vertAdvY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vertOriginX: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    vertOriginY: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    viewBox: null,\n    viewTarget: null,\n    visibility: null,\n    width: null,\n    widths: null,\n    wordSpacing: null,\n    writingMode: null,\n    x: null,\n    x1: null,\n    x2: null,\n    xChannelSelector: null,\n    xHeight: _util_types_js__WEBPACK_IMPORTED_MODULE_1__.number,\n    y: null,\n    y1: null,\n    y2: null,\n    yChannelSelector: null,\n    z: null,\n    zoomAndPan: null\n  },\n  space: 'svg',\n  transform: _util_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_2__.caseSensitiveTransform\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/svg.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/case-insensitive-transform.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/case-insensitive-transform.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseInsensitiveTransform: () => (/* binding */ caseInsensitiveTransform)\n/* harmony export */ });\n/* harmony import */ var _case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./case-sensitive-transform.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/case-sensitive-transform.js\");\n\n\n/**\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} property\n *   Property.\n * @returns {string}\n *   Transformed property.\n */\nfunction caseInsensitiveTransform(attributes, property) {\n  return (0,_case_sensitive_transform_js__WEBPACK_IMPORTED_MODULE_0__.caseSensitiveTransform)(attributes, property.toLowerCase())\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL2Nhc2UtaW5zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9FOztBQUVwRTtBQUNBLFdBQVcsd0JBQXdCO0FBQ25DO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQLFNBQVMsb0ZBQXNCO0FBQy9CIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL2Nhc2UtaW5zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Y2FzZVNlbnNpdGl2ZVRyYW5zZm9ybX0gZnJvbSAnLi9jYXNlLXNlbnNpdGl2ZS10cmFuc2Zvcm0uanMnXG5cbi8qKlxuICogQHBhcmFtIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fSBhdHRyaWJ1dGVzXG4gKiAgIEF0dHJpYnV0ZXMuXG4gKiBAcGFyYW0ge3N0cmluZ30gcHJvcGVydHlcbiAqICAgUHJvcGVydHkuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBUcmFuc2Zvcm1lZCBwcm9wZXJ0eS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNhc2VJbnNlbnNpdGl2ZVRyYW5zZm9ybShhdHRyaWJ1dGVzLCBwcm9wZXJ0eSkge1xuICByZXR1cm4gY2FzZVNlbnNpdGl2ZVRyYW5zZm9ybShhdHRyaWJ1dGVzLCBwcm9wZXJ0eS50b0xvd2VyQ2FzZSgpKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/case-insensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/case-sensitive-transform.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/case-sensitive-transform.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   caseSensitiveTransform: () => (/* binding */ caseSensitiveTransform)\n/* harmony export */ });\n/**\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} attribute\n *   Attribute.\n * @returns {string}\n *   Transformed attribute.\n */\nfunction caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL2Nhc2Utc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxXQUFXLHdCQUF3QjtBQUNuQztBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL2Nhc2Utc2Vuc2l0aXZlLXRyYW5zZm9ybS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBwYXJhbSB7UmVjb3JkPHN0cmluZywgc3RyaW5nPn0gYXR0cmlidXRlc1xuICogICBBdHRyaWJ1dGVzLlxuICogQHBhcmFtIHtzdHJpbmd9IGF0dHJpYnV0ZVxuICogICBBdHRyaWJ1dGUuXG4gKiBAcmV0dXJucyB7c3RyaW5nfVxuICogICBUcmFuc2Zvcm1lZCBhdHRyaWJ1dGUuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjYXNlU2Vuc2l0aXZlVHJhbnNmb3JtKGF0dHJpYnV0ZXMsIGF0dHJpYnV0ZSkge1xuICByZXR1cm4gYXR0cmlidXRlIGluIGF0dHJpYnV0ZXMgPyBhdHRyaWJ1dGVzW2F0dHJpYnV0ZV0gOiBhdHRyaWJ1dGVcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/case-sensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/create.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/create.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create)\n/* harmony export */ });\n/* harmony import */ var _normalize_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../normalize.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/normalize.js\");\n/* harmony import */ var _defined_info_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defined-info.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/defined-info.js\");\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./schema.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/schema.js\");\n/**\n * @import {Info, Space} from 'property-information'\n */\n\n/**\n * @typedef Definition\n *   Definition of a schema.\n * @property {Record<string, string> | undefined} [attributes]\n *   Normalzed names to special attribute case.\n * @property {ReadonlyArray<string> | undefined} [mustUseProperty]\n *   Normalized names that must be set as properties.\n * @property {Record<string, number | null>} properties\n *   Property names to their types.\n * @property {Space | undefined} [space]\n *   Space.\n * @property {Transform} transform\n *   Transform a property name.\n */\n\n/**\n * @callback Transform\n *   Transform.\n * @param {Record<string, string>} attributes\n *   Attributes.\n * @param {string} property\n *   Property.\n * @returns {string}\n *   Attribute.\n */\n\n\n\n\n\n/**\n * @param {Definition} definition\n *   Definition.\n * @returns {Schema}\n *   Schema.\n */\nfunction create(definition) {\n  /** @type {Record<string, Info>} */\n  const properties = {}\n  /** @type {Record<string, string>} */\n  const normals = {}\n\n  for (const [property, value] of Object.entries(definition.properties)) {\n    const info = new _defined_info_js__WEBPACK_IMPORTED_MODULE_0__.DefinedInfo(\n      property,\n      definition.transform(definition.attributes || {}, property),\n      value,\n      definition.space\n    )\n\n    if (\n      definition.mustUseProperty &&\n      definition.mustUseProperty.includes(property)\n    ) {\n      info.mustUseProperty = true\n    }\n\n    properties[property] = info\n\n    normals[(0,_normalize_js__WEBPACK_IMPORTED_MODULE_1__.normalize)(property)] = property\n    normals[(0,_normalize_js__WEBPACK_IMPORTED_MODULE_1__.normalize)(info.attribute)] = property\n  }\n\n  return new _schema_js__WEBPACK_IMPORTED_MODULE_2__.Schema(properties, normals, definition.space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/create.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/defined-info.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/defined-info.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefinedInfo: () => (/* binding */ DefinedInfo)\n/* harmony export */ });\n/* harmony import */ var _info_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/info.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/types.js\");\n/**\n * @import {Space} from 'property-information'\n */\n\n\n\n\nconst checks = /** @type {ReadonlyArray<keyof typeof types>} */ (\n  Object.keys(_types_js__WEBPACK_IMPORTED_MODULE_0__)\n)\n\nclass DefinedInfo extends _info_js__WEBPACK_IMPORTED_MODULE_1__.Info {\n  /**\n   * @constructor\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @param {number | null | undefined} [mask]\n   *   Mask.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1\n\n    super(property, attribute)\n\n    mark(this, 'space', space)\n\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index]\n        mark(this, checks[index], (mask & _types_js__WEBPACK_IMPORTED_MODULE_0__[check]) === _types_js__WEBPACK_IMPORTED_MODULE_0__[check])\n      }\n    }\n  }\n}\n\nDefinedInfo.prototype.defined = true\n\n/**\n * @template {keyof DefinedInfo} Key\n *   Key type.\n * @param {DefinedInfo} values\n *   Info.\n * @param {Key} key\n *   Key.\n * @param {DefinedInfo[Key]} value\n *   Value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction mark(values, key, value) {\n  if (value) {\n    values[key] = value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/defined-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/info.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/info.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Info: () => (/* binding */ Info)\n/* harmony export */ });\n/**\n * @import {Info as InfoType} from 'property-information'\n */\n\n/** @type {InfoType} */\nclass Info {\n  /**\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute) {\n    this.attribute = attribute\n    this.property = property\n  }\n}\n\nInfo.prototype.attribute = ''\nInfo.prototype.booleanish = false\nInfo.prototype.boolean = false\nInfo.prototype.commaOrSpaceSeparated = false\nInfo.prototype.commaSeparated = false\nInfo.prototype.defined = false\nInfo.prototype.mustUseProperty = false\nInfo.prototype.number = false\nInfo.prototype.overloadedBoolean = false\nInfo.prototype.property = ''\nInfo.prototype.spaceSeparated = false\nInfo.prototype.space = undefined\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL2luZm8uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxrQkFBa0I7QUFDOUI7O0FBRUEsV0FBVyxVQUFVO0FBQ2Q7QUFDUDtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL2luZm8uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtJbmZvIGFzIEluZm9UeXBlfSBmcm9tICdwcm9wZXJ0eS1pbmZvcm1hdGlvbidcbiAqL1xuXG4vKiogQHR5cGUge0luZm9UeXBlfSAqL1xuZXhwb3J0IGNsYXNzIEluZm8ge1xuICAvKipcbiAgICogQHBhcmFtIHtzdHJpbmd9IHByb3BlcnR5XG4gICAqICAgUHJvcGVydHkuXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBhdHRyaWJ1dGVcbiAgICogICBBdHRyaWJ1dGUuXG4gICAqIEByZXR1cm5zXG4gICAqICAgSW5mby5cbiAgICovXG4gIGNvbnN0cnVjdG9yKHByb3BlcnR5LCBhdHRyaWJ1dGUpIHtcbiAgICB0aGlzLmF0dHJpYnV0ZSA9IGF0dHJpYnV0ZVxuICAgIHRoaXMucHJvcGVydHkgPSBwcm9wZXJ0eVxuICB9XG59XG5cbkluZm8ucHJvdG90eXBlLmF0dHJpYnV0ZSA9ICcnXG5JbmZvLnByb3RvdHlwZS5ib29sZWFuaXNoID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLmJvb2xlYW4gPSBmYWxzZVxuSW5mby5wcm90b3R5cGUuY29tbWFPclNwYWNlU2VwYXJhdGVkID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLmNvbW1hU2VwYXJhdGVkID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLmRlZmluZWQgPSBmYWxzZVxuSW5mby5wcm90b3R5cGUubXVzdFVzZVByb3BlcnR5ID0gZmFsc2VcbkluZm8ucHJvdG90eXBlLm51bWJlciA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5vdmVybG9hZGVkQm9vbGVhbiA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5wcm9wZXJ0eSA9ICcnXG5JbmZvLnByb3RvdHlwZS5zcGFjZVNlcGFyYXRlZCA9IGZhbHNlXG5JbmZvLnByb3RvdHlwZS5zcGFjZSA9IHVuZGVmaW5lZFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/merge.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/merge.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   merge: () => (/* binding */ merge)\n/* harmony export */ });\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/schema.js\");\n/**\n * @import {Info, Space} from 'property-information'\n */\n\n\n\n/**\n * @param {ReadonlyArray<Schema>} definitions\n *   Definitions.\n * @param {Space | undefined} [space]\n *   Space.\n * @returns {Schema}\n *   Schema.\n */\nfunction merge(definitions, space) {\n  /** @type {Record<string, Info>} */\n  const property = {}\n  /** @type {Record<string, string>} */\n  const normal = {}\n\n  for (const definition of definitions) {\n    Object.assign(property, definition.property)\n    Object.assign(normal, definition.normal)\n  }\n\n  return new _schema_js__WEBPACK_IMPORTED_MODULE_0__.Schema(property, normal, space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL21lcmdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxZQUFZLGFBQWE7QUFDekI7O0FBRWtDOztBQUVsQztBQUNBLFdBQVcsdUJBQXVCO0FBQ2xDO0FBQ0EsV0FBVyxtQkFBbUI7QUFDOUI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1AsYUFBYSxzQkFBc0I7QUFDbkM7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxhQUFhLDhDQUFNO0FBQ25CIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL21lcmdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7SW5mbywgU3BhY2V9IGZyb20gJ3Byb3BlcnR5LWluZm9ybWF0aW9uJ1xuICovXG5cbmltcG9ydCB7U2NoZW1hfSBmcm9tICcuL3NjaGVtYS5qcydcblxuLyoqXG4gKiBAcGFyYW0ge1JlYWRvbmx5QXJyYXk8U2NoZW1hPn0gZGVmaW5pdGlvbnNcbiAqICAgRGVmaW5pdGlvbnMuXG4gKiBAcGFyYW0ge1NwYWNlIHwgdW5kZWZpbmVkfSBbc3BhY2VdXG4gKiAgIFNwYWNlLlxuICogQHJldHVybnMge1NjaGVtYX1cbiAqICAgU2NoZW1hLlxuICovXG5leHBvcnQgZnVuY3Rpb24gbWVyZ2UoZGVmaW5pdGlvbnMsIHNwYWNlKSB7XG4gIC8qKiBAdHlwZSB7UmVjb3JkPHN0cmluZywgSW5mbz59ICovXG4gIGNvbnN0IHByb3BlcnR5ID0ge31cbiAgLyoqIEB0eXBlIHtSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+fSAqL1xuICBjb25zdCBub3JtYWwgPSB7fVxuXG4gIGZvciAoY29uc3QgZGVmaW5pdGlvbiBvZiBkZWZpbml0aW9ucykge1xuICAgIE9iamVjdC5hc3NpZ24ocHJvcGVydHksIGRlZmluaXRpb24ucHJvcGVydHkpXG4gICAgT2JqZWN0LmFzc2lnbihub3JtYWwsIGRlZmluaXRpb24ubm9ybWFsKVxuICB9XG5cbiAgcmV0dXJuIG5ldyBTY2hlbWEocHJvcGVydHksIG5vcm1hbCwgc3BhY2UpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/schema.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/schema.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Schema: () => (/* binding */ Schema)\n/* harmony export */ });\n/**\n * @import {Schema as SchemaType, Space} from 'property-information'\n */\n\n/** @type {SchemaType} */\nclass Schema {\n  /**\n   * @param {SchemaType['property']} property\n   *   Property.\n   * @param {SchemaType['normal']} normal\n   *   Normal.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Schema.\n   */\n  constructor(property, normal, space) {\n    this.normal = normal\n    this.property = property\n\n    if (space) {\n      this.space = space\n    }\n  }\n}\n\nSchema.prototype.normal = {}\nSchema.prototype.property = {}\nSchema.prototype.space = undefined\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL3NjaGVtYS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLDZCQUE2QjtBQUN6Qzs7QUFFQSxXQUFXLFlBQVk7QUFDaEI7QUFDUDtBQUNBLGFBQWEsd0JBQXdCO0FBQ3JDO0FBQ0EsYUFBYSxzQkFBc0I7QUFDbkM7QUFDQSxhQUFhLG1CQUFtQjtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL3Byb3BlcnR5LWluZm9ybWF0aW9uQDcuMS4wL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIvdXRpbC9zY2hlbWEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtTY2hlbWEgYXMgU2NoZW1hVHlwZSwgU3BhY2V9IGZyb20gJ3Byb3BlcnR5LWluZm9ybWF0aW9uJ1xuICovXG5cbi8qKiBAdHlwZSB7U2NoZW1hVHlwZX0gKi9cbmV4cG9ydCBjbGFzcyBTY2hlbWEge1xuICAvKipcbiAgICogQHBhcmFtIHtTY2hlbWFUeXBlWydwcm9wZXJ0eSddfSBwcm9wZXJ0eVxuICAgKiAgIFByb3BlcnR5LlxuICAgKiBAcGFyYW0ge1NjaGVtYVR5cGVbJ25vcm1hbCddfSBub3JtYWxcbiAgICogICBOb3JtYWwuXG4gICAqIEBwYXJhbSB7U3BhY2UgfCB1bmRlZmluZWR9IFtzcGFjZV1cbiAgICogICBTcGFjZS5cbiAgICogQHJldHVybnNcbiAgICogICBTY2hlbWEuXG4gICAqL1xuICBjb25zdHJ1Y3Rvcihwcm9wZXJ0eSwgbm9ybWFsLCBzcGFjZSkge1xuICAgIHRoaXMubm9ybWFsID0gbm9ybWFsXG4gICAgdGhpcy5wcm9wZXJ0eSA9IHByb3BlcnR5XG5cbiAgICBpZiAoc3BhY2UpIHtcbiAgICAgIHRoaXMuc3BhY2UgPSBzcGFjZVxuICAgIH1cbiAgfVxufVxuXG5TY2hlbWEucHJvdG90eXBlLm5vcm1hbCA9IHt9XG5TY2hlbWEucHJvdG90eXBlLnByb3BlcnR5ID0ge31cblNjaGVtYS5wcm90b3R5cGUuc3BhY2UgPSB1bmRlZmluZWRcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/schema.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/types.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/types.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   boolean: () => (/* binding */ boolean),\n/* harmony export */   booleanish: () => (/* binding */ booleanish),\n/* harmony export */   commaOrSpaceSeparated: () => (/* binding */ commaOrSpaceSeparated),\n/* harmony export */   commaSeparated: () => (/* binding */ commaSeparated),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   overloadedBoolean: () => (/* binding */ overloadedBoolean),\n/* harmony export */   spaceSeparated: () => (/* binding */ spaceSeparated)\n/* harmony export */ });\nlet powers = 0\n\nconst boolean = increment()\nconst booleanish = increment()\nconst overloadedBoolean = increment()\nconst number = increment()\nconst spaceSeparated = increment()\nconst commaSeparated = increment()\nconst commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return 2 ** ++powers\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi91dGlsL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTs7QUFFTztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFUDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9wcm9wZXJ0eS1pbmZvcm1hdGlvbkA3LjEuMC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IHBvd2VycyA9IDBcblxuZXhwb3J0IGNvbnN0IGJvb2xlYW4gPSBpbmNyZW1lbnQoKVxuZXhwb3J0IGNvbnN0IGJvb2xlYW5pc2ggPSBpbmNyZW1lbnQoKVxuZXhwb3J0IGNvbnN0IG92ZXJsb2FkZWRCb29sZWFuID0gaW5jcmVtZW50KClcbmV4cG9ydCBjb25zdCBudW1iZXIgPSBpbmNyZW1lbnQoKVxuZXhwb3J0IGNvbnN0IHNwYWNlU2VwYXJhdGVkID0gaW5jcmVtZW50KClcbmV4cG9ydCBjb25zdCBjb21tYVNlcGFyYXRlZCA9IGluY3JlbWVudCgpXG5leHBvcnQgY29uc3QgY29tbWFPclNwYWNlU2VwYXJhdGVkID0gaW5jcmVtZW50KClcblxuZnVuY3Rpb24gaW5jcmVtZW50KCkge1xuICByZXR1cm4gMiAqKiArK3Bvd2Vyc1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/xlink.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/xlink.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xlink: () => (/* binding */ xlink)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/create.js\");\n\n\nconst xlink = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  },\n  space: 'xlink',\n  transform(_, property) {\n    return 'xlink:' + property.slice(5).toLowerCase()\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi94bGluay5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1Qzs7QUFFaEMsY0FBYyx1REFBTTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL3Byb3BlcnR5LWluZm9ybWF0aW9uQDcuMS4wL25vZGVfbW9kdWxlcy9wcm9wZXJ0eS1pbmZvcm1hdGlvbi9saWIveGxpbmsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtjcmVhdGV9IGZyb20gJy4vdXRpbC9jcmVhdGUuanMnXG5cbmV4cG9ydCBjb25zdCB4bGluayA9IGNyZWF0ZSh7XG4gIHByb3BlcnRpZXM6IHtcbiAgICB4TGlua0FjdHVhdGU6IG51bGwsXG4gICAgeExpbmtBcmNSb2xlOiBudWxsLFxuICAgIHhMaW5rSHJlZjogbnVsbCxcbiAgICB4TGlua1JvbGU6IG51bGwsXG4gICAgeExpbmtTaG93OiBudWxsLFxuICAgIHhMaW5rVGl0bGU6IG51bGwsXG4gICAgeExpbmtUeXBlOiBudWxsXG4gIH0sXG4gIHNwYWNlOiAneGxpbmsnLFxuICB0cmFuc2Zvcm0oXywgcHJvcGVydHkpIHtcbiAgICByZXR1cm4gJ3hsaW5rOicgKyBwcm9wZXJ0eS5zbGljZSg1KS50b0xvd2VyQ2FzZSgpXG4gIH1cbn0pXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/xlink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/xml.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/xml.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xml: () => (/* binding */ xml)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/create.js\");\n\n\nconst xml = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  properties: {xmlBase: null, xmlLang: null, xmlSpace: null},\n  space: 'xml',\n  transform(_, property) {\n    return 'xml:' + property.slice(3).toLowerCase()\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi94bWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUM7O0FBRWhDLFlBQVksdURBQU07QUFDekIsZUFBZSw2Q0FBNkM7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi94bWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtjcmVhdGV9IGZyb20gJy4vdXRpbC9jcmVhdGUuanMnXG5cbmV4cG9ydCBjb25zdCB4bWwgPSBjcmVhdGUoe1xuICBwcm9wZXJ0aWVzOiB7eG1sQmFzZTogbnVsbCwgeG1sTGFuZzogbnVsbCwgeG1sU3BhY2U6IG51bGx9LFxuICBzcGFjZTogJ3htbCcsXG4gIHRyYW5zZm9ybShfLCBwcm9wZXJ0eSkge1xuICAgIHJldHVybiAneG1sOicgKyBwcm9wZXJ0eS5zbGljZSgzKS50b0xvd2VyQ2FzZSgpXG4gIH1cbn0pXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/xml.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/xmlns.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/xmlns.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   xmlns: () => (/* binding */ xmlns)\n/* harmony export */ });\n/* harmony import */ var _util_create_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util/create.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/create.js\");\n/* harmony import */ var _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util/case-insensitive-transform.js */ \"(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/util/case-insensitive-transform.js\");\n\n\n\nconst xmlns = (0,_util_create_js__WEBPACK_IMPORTED_MODULE_0__.create)({\n  attributes: {xmlnsxlink: 'xmlns:xlink'},\n  properties: {xmlnsXLink: null, xmlns: null},\n  space: 'xmlns',\n  transform: _util_case_insensitive_transform_js__WEBPACK_IMPORTED_MODULE_1__.caseInsensitiveTransform\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi94bWxucy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBdUM7QUFDc0M7O0FBRXRFLGNBQWMsdURBQU07QUFDM0IsZUFBZSwwQkFBMEI7QUFDekMsZUFBZSw4QkFBOEI7QUFDN0M7QUFDQSxhQUFhLHlGQUF3QjtBQUNyQyxDQUFDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vcHJvcGVydHktaW5mb3JtYXRpb25ANy4xLjAvbm9kZV9tb2R1bGVzL3Byb3BlcnR5LWluZm9ybWF0aW9uL2xpYi94bWxucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2NyZWF0ZX0gZnJvbSAnLi91dGlsL2NyZWF0ZS5qcydcbmltcG9ydCB7Y2FzZUluc2Vuc2l0aXZlVHJhbnNmb3JtfSBmcm9tICcuL3V0aWwvY2FzZS1pbnNlbnNpdGl2ZS10cmFuc2Zvcm0uanMnXG5cbmV4cG9ydCBjb25zdCB4bWxucyA9IGNyZWF0ZSh7XG4gIGF0dHJpYnV0ZXM6IHt4bWxuc3hsaW5rOiAneG1sbnM6eGxpbmsnfSxcbiAgcHJvcGVydGllczoge3htbG5zWExpbms6IG51bGwsIHhtbG5zOiBudWxsfSxcbiAgc3BhY2U6ICd4bWxucycsXG4gIHRyYW5zZm9ybTogY2FzZUluc2Vuc2l0aXZlVHJhbnNmb3JtXG59KVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/property-information@7.1.0/node_modules/property-information/lib/xmlns.js\n");

/***/ })

};
;