"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/wonka@6.3.4";
exports.ids = ["vendor-chunks/wonka@6.3.4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/wonka@6.3.4/node_modules/wonka/dist/wonka.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/wonka@6.3.4/node_modules/wonka/dist/wonka.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buffer: () => (/* binding */ buffer),\n/* harmony export */   combine: () => (/* binding */ combine),\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   concatAll: () => (/* binding */ concatAll),\n/* harmony export */   concatMap: () => (/* binding */ concatMap),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   filter: () => (/* binding */ filter),\n/* harmony export */   flatten: () => (/* binding */ mergeAll),\n/* harmony export */   forEach: () => (/* binding */ forEach),\n/* harmony export */   fromArray: () => (/* binding */ r),\n/* harmony export */   fromAsyncIterable: () => (/* binding */ fromAsyncIterable),\n/* harmony export */   fromCallbag: () => (/* binding */ fromCallbag),\n/* harmony export */   fromDomEvent: () => (/* binding */ fromDomEvent),\n/* harmony export */   fromIterable: () => (/* binding */ fromIterable),\n/* harmony export */   fromObservable: () => (/* binding */ fromObservable),\n/* harmony export */   fromPromise: () => (/* binding */ fromPromise),\n/* harmony export */   fromValue: () => (/* binding */ fromValue),\n/* harmony export */   interval: () => (/* binding */ interval),\n/* harmony export */   lazy: () => (/* binding */ lazy),\n/* harmony export */   make: () => (/* binding */ make),\n/* harmony export */   makeSubject: () => (/* binding */ makeSubject),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   merge: () => (/* binding */ merge),\n/* harmony export */   mergeAll: () => (/* binding */ mergeAll),\n/* harmony export */   mergeMap: () => (/* binding */ mergeMap),\n/* harmony export */   never: () => (/* binding */ never),\n/* harmony export */   onEnd: () => (/* binding */ onEnd),\n/* harmony export */   onPush: () => (/* binding */ onPush),\n/* harmony export */   onStart: () => (/* binding */ onStart),\n/* harmony export */   pipe: () => (/* binding */ pipe),\n/* harmony export */   publish: () => (/* binding */ publish),\n/* harmony export */   sample: () => (/* binding */ sample),\n/* harmony export */   scan: () => (/* binding */ scan),\n/* harmony export */   share: () => (/* binding */ share),\n/* harmony export */   skip: () => (/* binding */ skip),\n/* harmony export */   skipUntil: () => (/* binding */ skipUntil),\n/* harmony export */   skipWhile: () => (/* binding */ skipWhile),\n/* harmony export */   subscribe: () => (/* binding */ subscribe),\n/* harmony export */   switchAll: () => (/* binding */ switchAll),\n/* harmony export */   switchMap: () => (/* binding */ switchMap),\n/* harmony export */   take: () => (/* binding */ take),\n/* harmony export */   takeLast: () => (/* binding */ takeLast),\n/* harmony export */   takeUntil: () => (/* binding */ takeUntil),\n/* harmony export */   takeWhile: () => (/* binding */ takeWhile),\n/* harmony export */   tap: () => (/* binding */ onPush),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   toArray: () => (/* binding */ toArray),\n/* harmony export */   toAsyncIterable: () => (/* binding */ toAsyncIterable),\n/* harmony export */   toCallbag: () => (/* binding */ toCallbag),\n/* harmony export */   toObservable: () => (/* binding */ toObservable),\n/* harmony export */   toPromise: () => (/* binding */ toPromise),\n/* harmony export */   zip: () => (/* binding */ zip)\n/* harmony export */ });\nvar teardownPlaceholder = () => {};\n\nvar e = teardownPlaceholder;\n\nfunction start(e) {\n  return {\n    tag: 0,\n    0: e\n  };\n}\n\nfunction push(e) {\n  return {\n    tag: 1,\n    0: e\n  };\n}\n\nvar asyncIteratorSymbol = () => \"function\" == typeof Symbol && Symbol.asyncIterator || \"@@asyncIterator\";\n\nvar observableSymbol = () => \"function\" == typeof Symbol && Symbol.observable || \"@@observable\";\n\nvar identity = e => e;\n\nfunction buffer(r) {\n  return t => i => {\n    var a = [];\n    var f = e;\n    var n = e;\n    var s = !1;\n    var l = !1;\n    t((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        n(1);\n        if (a.length) {\n          i(push(a));\n        }\n        i(0);\n      } else if (0 === e.tag) {\n        f = e[0];\n        r((e => {\n          if (l) {} else if (0 === e) {\n            l = !0;\n            f(1);\n            if (a.length) {\n              i(push(a));\n            }\n            i(0);\n          } else if (0 === e.tag) {\n            n = e[0];\n          } else if (a.length) {\n            var r = push(a);\n            a = [];\n            i(r);\n          }\n        }));\n      } else {\n        a.push(e[0]);\n        if (!s) {\n          s = !0;\n          f(0);\n          n(0);\n        } else {\n          s = !1;\n        }\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !l) {\n        l = !0;\n        f(1);\n        n(1);\n      } else if (!l && !s) {\n        s = !0;\n        f(0);\n        n(0);\n      }\n    })));\n  };\n}\n\nfunction concatMap(r) {\n  return t => i => {\n    var a = [];\n    var f = e;\n    var n = e;\n    var s = !1;\n    var l = !1;\n    var u = !1;\n    var o = !1;\n    function applyInnerSource(e) {\n      u = !0;\n      e((e => {\n        if (0 === e) {\n          if (u) {\n            u = !1;\n            if (a.length) {\n              applyInnerSource(r(a.shift()));\n            } else if (o) {\n              i(0);\n            } else if (!s) {\n              s = !0;\n              f(0);\n            }\n          }\n        } else if (0 === e.tag) {\n          l = !1;\n          (n = e[0])(0);\n        } else if (u) {\n          i(e);\n          if (l) {\n            l = !1;\n          } else {\n            n(0);\n          }\n        }\n      }));\n    }\n    t((e => {\n      if (o) {} else if (0 === e) {\n        o = !0;\n        if (!u && !a.length) {\n          i(0);\n        }\n      } else if (0 === e.tag) {\n        f = e[0];\n      } else {\n        s = !1;\n        if (u) {\n          a.push(e[0]);\n        } else {\n          applyInnerSource(r(e[0]));\n        }\n      }\n    }));\n    i(start((e => {\n      if (1 === e) {\n        if (!o) {\n          o = !0;\n          f(1);\n        }\n        if (u) {\n          u = !1;\n          n(1);\n        }\n      } else {\n        if (!o && !s) {\n          s = !0;\n          f(0);\n        }\n        if (u && !l) {\n          l = !0;\n          n(0);\n        }\n      }\n    })));\n  };\n}\n\nfunction concatAll(e) {\n  return concatMap(identity)(e);\n}\n\nfunction concat(e) {\n  return concatAll(r(e));\n}\n\nfunction filter(r) {\n  return t => i => {\n    var a = e;\n    t((e => {\n      if (0 === e) {\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        i(e);\n      } else if (!r(e[0])) {\n        a(0);\n      } else {\n        i(e);\n      }\n    }));\n  };\n}\n\nfunction map(e) {\n  return r => t => r((r => {\n    if (0 === r || 0 === r.tag) {\n      t(r);\n    } else {\n      t(push(e(r[0])));\n    }\n  }));\n}\n\nfunction mergeMap(r) {\n  return t => i => {\n    var a = [];\n    var f = e;\n    var n = !1;\n    var s = !1;\n    t((t => {\n      if (s) {} else if (0 === t) {\n        s = !0;\n        if (!a.length) {\n          i(0);\n        }\n      } else if (0 === t.tag) {\n        f = t[0];\n      } else {\n        n = !1;\n        !function applyInnerSource(r) {\n          var t = e;\n          r((e => {\n            if (0 === e) {\n              if (a.length) {\n                var r = a.indexOf(t);\n                if (r > -1) {\n                  (a = a.slice()).splice(r, 1);\n                }\n                if (!a.length) {\n                  if (s) {\n                    i(0);\n                  } else if (!n) {\n                    n = !0;\n                    f(0);\n                  }\n                }\n              }\n            } else if (0 === e.tag) {\n              a.push(t = e[0]);\n              t(0);\n            } else if (a.length) {\n              i(e);\n              t(0);\n            }\n          }));\n        }(r(t[0]));\n        if (!n) {\n          n = !0;\n          f(0);\n        }\n      }\n    }));\n    i(start((e => {\n      if (1 === e) {\n        if (!s) {\n          s = !0;\n          f(1);\n        }\n        for (var r = 0, t = a, i = a.length; r < i; r++) {\n          t[r](1);\n        }\n        a.length = 0;\n      } else {\n        if (!s && !n) {\n          n = !0;\n          f(0);\n        } else {\n          n = !1;\n        }\n        for (var l = 0, u = a, o = a.length; l < o; l++) {\n          u[l](0);\n        }\n      }\n    })));\n  };\n}\n\nfunction mergeAll(e) {\n  return mergeMap(identity)(e);\n}\n\nfunction merge(e) {\n  return mergeAll(r(e));\n}\n\nfunction onEnd(e) {\n  return r => t => {\n    var i = !1;\n    r((r => {\n      if (i) {} else if (0 === r) {\n        i = !0;\n        t(0);\n        e();\n      } else if (0 === r.tag) {\n        var a = r[0];\n        t(start((r => {\n          if (1 === r) {\n            i = !0;\n            a(1);\n            e();\n          } else {\n            a(r);\n          }\n        })));\n      } else {\n        t(r);\n      }\n    }));\n  };\n}\n\nfunction onPush(e) {\n  return r => t => {\n    var i = !1;\n    r((r => {\n      if (i) {} else if (0 === r) {\n        i = !0;\n        t(0);\n      } else if (0 === r.tag) {\n        var a = r[0];\n        t(start((e => {\n          if (1 === e) {\n            i = !0;\n          }\n          a(e);\n        })));\n      } else {\n        e(r[0]);\n        t(r);\n      }\n    }));\n  };\n}\n\nfunction onStart(e) {\n  return r => t => r((r => {\n    if (0 === r) {\n      t(0);\n    } else if (0 === r.tag) {\n      t(r);\n      e();\n    } else {\n      t(r);\n    }\n  }));\n}\n\nfunction sample(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n;\n    var s = !1;\n    var l = !1;\n    t((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        f(1);\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n      } else {\n        n = e[0];\n        if (!s) {\n          s = !0;\n          f(0);\n          a(0);\n        } else {\n          s = !1;\n        }\n      }\n    }));\n    r((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        a(1);\n        i(0);\n      } else if (0 === e.tag) {\n        f = e[0];\n      } else if (void 0 !== n) {\n        var r = push(n);\n        n = void 0;\n        i(r);\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !l) {\n        l = !0;\n        a(1);\n        f(1);\n      } else if (!l && !s) {\n        s = !0;\n        a(0);\n        f(0);\n      }\n    })));\n  };\n}\n\nfunction scan(e, r) {\n  return t => i => {\n    var a = r;\n    t((r => {\n      if (0 === r) {\n        i(0);\n      } else if (0 === r.tag) {\n        i(r);\n      } else {\n        i(push(a = e(a, r[0])));\n      }\n    }));\n  };\n}\n\nfunction share(r) {\n  var t = [];\n  var i = e;\n  var a = !1;\n  return e => {\n    t.push(e);\n    if (1 === t.length) {\n      r((e => {\n        if (0 === e) {\n          for (var r = 0, f = t, n = t.length; r < n; r++) {\n            f[r](0);\n          }\n          t.length = 0;\n        } else if (0 === e.tag) {\n          i = e[0];\n        } else {\n          a = !1;\n          for (var s = 0, l = t, u = t.length; s < u; s++) {\n            l[s](e);\n          }\n        }\n      }));\n    }\n    e(start((r => {\n      if (1 === r) {\n        var f = t.indexOf(e);\n        if (f > -1) {\n          (t = t.slice()).splice(f, 1);\n        }\n        if (!t.length) {\n          i(1);\n        }\n      } else if (!a) {\n        a = !0;\n        i(0);\n      }\n    })));\n  };\n}\n\nfunction skip(r) {\n  return t => i => {\n    var a = e;\n    var f = r;\n    t((e => {\n      if (0 === e) {\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        i(e);\n      } else if (f-- > 0) {\n        a(0);\n      } else {\n        i(e);\n      }\n    }));\n  };\n}\n\nfunction skipUntil(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n = !0;\n    var s = !1;\n    var l = !1;\n    t((e => {\n      if (l) {} else if (0 === e) {\n        l = !0;\n        if (n) {\n          f(1);\n        }\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        r((e => {\n          if (0 === e) {\n            if (n) {\n              l = !0;\n              a(1);\n            }\n          } else if (0 === e.tag) {\n            (f = e[0])(0);\n          } else {\n            n = !1;\n            f(1);\n          }\n        }));\n      } else if (!n) {\n        s = !1;\n        i(e);\n      } else if (!s) {\n        s = !0;\n        a(0);\n        f(0);\n      } else {\n        s = !1;\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !l) {\n        l = !0;\n        a(1);\n        if (n) {\n          f(1);\n        }\n      } else if (!l && !s) {\n        s = !0;\n        if (n) {\n          f(0);\n        }\n        a(0);\n      }\n    })));\n  };\n}\n\nfunction skipWhile(r) {\n  return t => i => {\n    var a = e;\n    var f = !0;\n    t((e => {\n      if (0 === e) {\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        i(e);\n      } else if (f) {\n        if (r(e[0])) {\n          a(0);\n        } else {\n          f = !1;\n          i(e);\n        }\n      } else {\n        i(e);\n      }\n    }));\n  };\n}\n\nfunction switchMap(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n = !1;\n    var s = !1;\n    var l = !1;\n    var u = !1;\n    t((t => {\n      if (u) {} else if (0 === t) {\n        u = !0;\n        if (!l) {\n          i(0);\n        }\n      } else if (0 === t.tag) {\n        a = t[0];\n      } else {\n        if (l) {\n          f(1);\n          f = e;\n        }\n        if (!n) {\n          n = !0;\n          a(0);\n        } else {\n          n = !1;\n        }\n        !function applyInnerSource(e) {\n          l = !0;\n          e((e => {\n            if (!l) {} else if (0 === e) {\n              l = !1;\n              if (u) {\n                i(0);\n              } else if (!n) {\n                n = !0;\n                a(0);\n              }\n            } else if (0 === e.tag) {\n              s = !1;\n              (f = e[0])(0);\n            } else {\n              i(e);\n              if (!s) {\n                f(0);\n              } else {\n                s = !1;\n              }\n            }\n          }));\n        }(r(t[0]));\n      }\n    }));\n    i(start((e => {\n      if (1 === e) {\n        if (!u) {\n          u = !0;\n          a(1);\n        }\n        if (l) {\n          l = !1;\n          f(1);\n        }\n      } else {\n        if (!u && !n) {\n          n = !0;\n          a(0);\n        }\n        if (l && !s) {\n          s = !0;\n          f(0);\n        }\n      }\n    })));\n  };\n}\n\nfunction switchAll(e) {\n  return switchMap(identity)(e);\n}\n\nfunction take(r) {\n  return t => i => {\n    var a = e;\n    var f = !1;\n    var n = 0;\n    t((e => {\n      if (f) {} else if (0 === e) {\n        f = !0;\n        i(0);\n      } else if (0 === e.tag) {\n        if (r <= 0) {\n          f = !0;\n          i(0);\n          e[0](1);\n        } else {\n          a = e[0];\n        }\n      } else if (n++ < r) {\n        i(e);\n        if (!f && n >= r) {\n          f = !0;\n          i(0);\n          a(1);\n        }\n      } else {\n        i(e);\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !f) {\n        f = !0;\n        a(1);\n      } else if (0 === e && !f && n < r) {\n        a(0);\n      }\n    })));\n  };\n}\n\nfunction takeLast(t) {\n  return i => a => {\n    var f = [];\n    var n = e;\n    i((e => {\n      if (0 === e) {\n        r(f)(a);\n      } else if (0 === e.tag) {\n        if (t <= 0) {\n          e[0](1);\n          r(f)(a);\n        } else {\n          (n = e[0])(0);\n        }\n      } else {\n        if (f.length >= t && t) {\n          f.shift();\n        }\n        f.push(e[0]);\n        n(0);\n      }\n    }));\n  };\n}\n\nfunction takeUntil(r) {\n  return t => i => {\n    var a = e;\n    var f = e;\n    var n = !1;\n    t((e => {\n      if (n) {} else if (0 === e) {\n        n = !0;\n        f(1);\n        i(0);\n      } else if (0 === e.tag) {\n        a = e[0];\n        r((e => {\n          if (0 === e) {} else if (0 === e.tag) {\n            (f = e[0])(0);\n          } else {\n            n = !0;\n            f(1);\n            a(1);\n            i(0);\n          }\n        }));\n      } else {\n        i(e);\n      }\n    }));\n    i(start((e => {\n      if (1 === e && !n) {\n        n = !0;\n        a(1);\n        f(1);\n      } else if (!n) {\n        a(0);\n      }\n    })));\n  };\n}\n\nfunction takeWhile(r, t) {\n  return i => a => {\n    var f = e;\n    var n = !1;\n    i((e => {\n      if (n) {} else if (0 === e) {\n        n = !0;\n        a(0);\n      } else if (0 === e.tag) {\n        f = e[0];\n        a(e);\n      } else if (!r(e[0])) {\n        n = !0;\n        if (t) {\n          a(e);\n        }\n        a(0);\n        f(1);\n      } else {\n        a(e);\n      }\n    }));\n  };\n}\n\nfunction debounce(e) {\n  return r => t => {\n    var i;\n    var a = !1;\n    var f = !1;\n    r((r => {\n      if (f) {} else if (0 === r) {\n        f = !0;\n        if (i) {\n          a = !0;\n        } else {\n          t(0);\n        }\n      } else if (0 === r.tag) {\n        var n = r[0];\n        t(start((e => {\n          if (1 === e && !f) {\n            f = !0;\n            a = !1;\n            if (i) {\n              clearTimeout(i);\n            }\n            n(1);\n          } else if (!f) {\n            n(0);\n          }\n        })));\n      } else {\n        if (i) {\n          clearTimeout(i);\n        }\n        i = setTimeout((() => {\n          i = void 0;\n          t(r);\n          if (a) {\n            t(0);\n          }\n        }), e(r[0]));\n      }\n    }));\n  };\n}\n\nfunction delay(e) {\n  return r => t => {\n    var i = 0;\n    r((r => {\n      if (0 !== r && 0 === r.tag) {\n        t(r);\n      } else {\n        i++;\n        setTimeout((() => {\n          if (i) {\n            i--;\n            t(r);\n          }\n        }), e);\n      }\n    }));\n  };\n}\n\nfunction throttle(e) {\n  return r => t => {\n    var i = !1;\n    var a;\n    r((r => {\n      if (0 === r) {\n        if (a) {\n          clearTimeout(a);\n        }\n        t(0);\n      } else if (0 === r.tag) {\n        var f = r[0];\n        t(start((e => {\n          if (1 === e) {\n            if (a) {\n              clearTimeout(a);\n            }\n            f(1);\n          } else {\n            f(0);\n          }\n        })));\n      } else if (!i) {\n        i = !0;\n        if (a) {\n          clearTimeout(a);\n        }\n        a = setTimeout((() => {\n          a = void 0;\n          i = !1;\n        }), e(r[0]));\n        t(r);\n      }\n    }));\n  };\n}\n\nfunction lazy(e) {\n  return r => e()(r);\n}\n\nfunction fromAsyncIterable(e) {\n  return r => {\n    var t = e[asyncIteratorSymbol()] && e[asyncIteratorSymbol()]() || e;\n    var i = !1;\n    var a = !1;\n    var f = !1;\n    var n;\n    r(start((async e => {\n      if (1 === e) {\n        i = !0;\n        if (t.return) {\n          t.return();\n        }\n      } else if (a) {\n        f = !0;\n      } else {\n        for (f = a = !0; f && !i; ) {\n          if ((n = await t.next()).done) {\n            i = !0;\n            if (t.return) {\n              await t.return();\n            }\n            r(0);\n          } else {\n            try {\n              f = !1;\n              r(push(n.value));\n            } catch (e) {\n              if (t.throw) {\n                if (i = !!(await t.throw(e)).done) {\n                  r(0);\n                }\n              } else {\n                throw e;\n              }\n            }\n          }\n        }\n        a = !1;\n      }\n    })));\n  };\n}\n\nfunction fromIterable(e) {\n  if (e[Symbol.asyncIterator]) {\n    return fromAsyncIterable(e);\n  }\n  return r => {\n    var t = e[Symbol.iterator]();\n    var i = !1;\n    var a = !1;\n    var f = !1;\n    var n;\n    r(start((e => {\n      if (1 === e) {\n        i = !0;\n        if (t.return) {\n          t.return();\n        }\n      } else if (a) {\n        f = !0;\n      } else {\n        for (f = a = !0; f && !i; ) {\n          if ((n = t.next()).done) {\n            i = !0;\n            if (t.return) {\n              t.return();\n            }\n            r(0);\n          } else {\n            try {\n              f = !1;\n              r(push(n.value));\n            } catch (e) {\n              if (t.throw) {\n                if (i = !!t.throw(e).done) {\n                  r(0);\n                }\n              } else {\n                throw e;\n              }\n            }\n          }\n        }\n        a = !1;\n      }\n    })));\n  };\n}\n\nvar r = fromIterable;\n\nfunction fromValue(e) {\n  return r => {\n    var t = !1;\n    r(start((i => {\n      if (1 === i) {\n        t = !0;\n      } else if (!t) {\n        t = !0;\n        r(push(e));\n        r(0);\n      }\n    })));\n  };\n}\n\nfunction make(e) {\n  return r => {\n    var t = !1;\n    var i = e({\n      next(e) {\n        if (!t) {\n          r(push(e));\n        }\n      },\n      complete() {\n        if (!t) {\n          t = !0;\n          r(0);\n        }\n      }\n    });\n    r(start((e => {\n      if (1 === e && !t) {\n        t = !0;\n        i();\n      }\n    })));\n  };\n}\n\nfunction makeSubject() {\n  var e;\n  var r;\n  return {\n    source: share(make((t => {\n      e = t.next;\n      r = t.complete;\n      return teardownPlaceholder;\n    }))),\n    next(r) {\n      if (e) {\n        e(r);\n      }\n    },\n    complete() {\n      if (r) {\n        r();\n      }\n    }\n  };\n}\n\nvar empty = e => {\n  var r = !1;\n  e(start((t => {\n    if (1 === t) {\n      r = !0;\n    } else if (!r) {\n      r = !0;\n      e(0);\n    }\n  })));\n};\n\nvar never = r => {\n  r(start(e));\n};\n\nfunction interval(e) {\n  return make((r => {\n    var t = 0;\n    var i = setInterval((() => r.next(t++)), e);\n    return () => clearInterval(i);\n  }));\n}\n\nfunction fromDomEvent(e, r) {\n  return make((t => {\n    e.addEventListener(r, t.next);\n    return () => e.removeEventListener(r, t.next);\n  }));\n}\n\nfunction fromPromise(e) {\n  return make((r => {\n    e.then((e => {\n      Promise.resolve(e).then((() => {\n        r.next(e);\n        r.complete();\n      }));\n    }));\n    return teardownPlaceholder;\n  }));\n}\n\nfunction subscribe(r) {\n  return t => {\n    var i = e;\n    var a = !1;\n    t((e => {\n      if (0 === e) {\n        a = !0;\n      } else if (0 === e.tag) {\n        (i = e[0])(0);\n      } else if (!a) {\n        r(e[0]);\n        i(0);\n      }\n    }));\n    return {\n      unsubscribe() {\n        if (!a) {\n          a = !0;\n          i(1);\n        }\n      }\n    };\n  };\n}\n\nfunction forEach(e) {\n  return r => {\n    subscribe(e)(r);\n  };\n}\n\nfunction publish(e) {\n  subscribe((e => {}))(e);\n}\n\nvar t = {\n  done: !0\n};\n\nvar toAsyncIterable = r => {\n  var i = [];\n  var a = !1;\n  var f = !1;\n  var n = !1;\n  var s = e;\n  var l;\n  return {\n    async next() {\n      if (!f) {\n        f = !0;\n        r((e => {\n          if (a) {} else if (0 === e) {\n            if (l) {\n              l = l(t);\n            }\n            a = !0;\n          } else if (0 === e.tag) {\n            n = !0;\n            (s = e[0])(0);\n          } else {\n            n = !1;\n            if (l) {\n              l = l({\n                value: e[0],\n                done: !1\n              });\n            } else {\n              i.push(e[0]);\n            }\n          }\n        }));\n      }\n      if (a && !i.length) {\n        return t;\n      } else if (!a && !n && i.length <= 1) {\n        n = !0;\n        s(0);\n      }\n      return i.length ? {\n        value: i.shift(),\n        done: !1\n      } : new Promise((e => l = e));\n    },\n    async return() {\n      if (!a) {\n        l = s(1);\n      }\n      a = !0;\n      return t;\n    },\n    [asyncIteratorSymbol()]() {\n      return this;\n    }\n  };\n};\n\nfunction toArray(r) {\n  var t = [];\n  var i = e;\n  var a = !1;\n  r((e => {\n    if (0 === e) {\n      a = !0;\n    } else if (0 === e.tag) {\n      (i = e[0])(0);\n    } else {\n      t.push(e[0]);\n      i(0);\n    }\n  }));\n  if (!a) {\n    i(1);\n  }\n  return t;\n}\n\nfunction toPromise(r) {\n  return new Promise((t => {\n    var i = e;\n    var a;\n    r((e => {\n      if (0 === e) {\n        Promise.resolve(a).then(t);\n      } else if (0 === e.tag) {\n        (i = e[0])(0);\n      } else {\n        a = e[0];\n        i(0);\n      }\n    }));\n  }));\n}\n\nfunction zip(r) {\n  var t = Object.keys(r).length;\n  return i => {\n    var a = new Set;\n    var f = Array.isArray(r) ? new Array(t).fill(e) : {};\n    var n = Array.isArray(r) ? new Array(t) : {};\n    var s = !1;\n    var l = !1;\n    var u = !1;\n    var o = 0;\n    var loop = function(v) {\n      r[v]((c => {\n        if (0 === c) {\n          if (o >= t - 1) {\n            u = !0;\n            i(0);\n          } else {\n            o++;\n          }\n        } else if (0 === c.tag) {\n          f[v] = c[0];\n        } else if (!u) {\n          n[v] = c[0];\n          a.add(v);\n          if (!s && a.size < t) {\n            if (!l) {\n              for (var h in r) {\n                if (!a.has(h)) {\n                  (f[h] || e)(0);\n                }\n              }\n            } else {\n              l = !1;\n            }\n          } else {\n            s = !0;\n            l = !1;\n            i(push(Array.isArray(n) ? n.slice() : {\n              ...n\n            }));\n          }\n        }\n      }));\n    };\n    for (var v in r) {\n      loop(v);\n    }\n    i(start((e => {\n      if (u) {} else if (1 === e) {\n        u = !0;\n        for (var r in f) {\n          f[r](1);\n        }\n      } else if (!l) {\n        l = !0;\n        for (var t in f) {\n          f[t](0);\n        }\n      }\n    })));\n  };\n}\n\nfunction combine(...e) {\n  return zip(e);\n}\n\nfunction fromObservable(e) {\n  return r => {\n    var t = (e[observableSymbol()] ? e[observableSymbol()]() : e).subscribe({\n      next(e) {\n        r(push(e));\n      },\n      complete() {\n        r(0);\n      },\n      error(e) {\n        throw e;\n      }\n    });\n    r(start((e => {\n      if (1 === e) {\n        t.unsubscribe();\n      }\n    })));\n  };\n}\n\nfunction toObservable(r) {\n  return {\n    subscribe(t, i, a) {\n      var f = \"object\" == typeof t ? t : {\n        next: t,\n        error: i,\n        complete: a\n      };\n      var n = e;\n      var s = !1;\n      r((e => {\n        if (s) {} else if (0 === e) {\n          s = !0;\n          if (f.complete) {\n            f.complete();\n          }\n        } else if (0 === e.tag) {\n          (n = e[0])(0);\n        } else {\n          f.next(e[0]);\n          n(0);\n        }\n      }));\n      var l = {\n        closed: !1,\n        unsubscribe() {\n          l.closed = !0;\n          s = !0;\n          n(1);\n        }\n      };\n      return l;\n    },\n    [observableSymbol()]() {\n      return this;\n    }\n  };\n}\n\nfunction fromCallbag(e) {\n  return r => {\n    e(0, ((e, t) => {\n      if (0 === e) {\n        r(start((e => {\n          t(e + 1);\n        })));\n      } else if (1 === e) {\n        r(push(t));\n      } else {\n        r(0);\n      }\n    }));\n  };\n}\n\nfunction toCallbag(e) {\n  return (r, t) => {\n    if (0 === r) {\n      e((e => {\n        if (0 === e) {\n          t(2);\n        } else if (0 === e.tag) {\n          t(0, (r => {\n            if (r < 3) {\n              e[0](r - 1);\n            }\n          }));\n        } else {\n          t(1, e[0]);\n        }\n      }));\n    }\n  };\n}\n\nvar pipe = (...e) => {\n  var r = e[0];\n  for (var t = 1, i = e.length; t < i; t++) {\n    r = e[t](r);\n  }\n  return r;\n};\n\n\n//# sourceMappingURL=wonka.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/wonka@6.3.4/node_modules/wonka/dist/wonka.mjs\n");

/***/ })

};
;