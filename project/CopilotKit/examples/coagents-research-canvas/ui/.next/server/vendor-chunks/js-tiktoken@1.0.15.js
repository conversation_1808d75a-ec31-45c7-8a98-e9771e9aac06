"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/js-tiktoken@1.0.15";
exports.ids = ["vendor-chunks/js-tiktoken@1.0.15"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/js-tiktoken@1.0.15/node_modules/js-tiktoken/dist/lite.cjs":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/js-tiktoken@1.0.15/node_modules/js-tiktoken/dist/lite.cjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar base64 = __webpack_require__(/*! base64-js */ \"(rsc)/./node_modules/.pnpm/base64-js@1.5.1/node_modules/base64-js/index.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar base64__default = /*#__PURE__*/_interopDefault(base64);\n\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\n\n// src/core.ts\nfunction bytePairMerge(piece, ranks) {\n  let parts = Array.from(\n    { length: piece.length },\n    (_, i) => ({ start: i, end: i + 1 })\n  );\n  while (parts.length > 1) {\n    let minRank = null;\n    for (let i = 0; i < parts.length - 1; i++) {\n      const slice = piece.slice(parts[i].start, parts[i + 1].end);\n      const rank = ranks.get(slice.join(\",\"));\n      if (rank == null)\n        continue;\n      if (minRank == null || rank < minRank[0]) {\n        minRank = [rank, i];\n      }\n    }\n    if (minRank != null) {\n      const i = minRank[1];\n      parts[i] = { start: parts[i].start, end: parts[i + 1].end };\n      parts.splice(i + 1, 1);\n    } else {\n      break;\n    }\n  }\n  return parts;\n}\nfunction bytePairEncode(piece, ranks) {\n  if (piece.length === 1)\n    return [ranks.get(piece.join(\",\"))];\n  return bytePairMerge(piece, ranks).map((p) => ranks.get(piece.slice(p.start, p.end).join(\",\"))).filter((x) => x != null);\n}\nfunction escapeRegex(str) {\n  return str.replace(/[\\\\^$*+?.()|[\\]{}]/g, \"\\\\$&\");\n}\nvar _Tiktoken = class {\n  /** @internal */\n  specialTokens;\n  /** @internal */\n  inverseSpecialTokens;\n  /** @internal */\n  patStr;\n  /** @internal */\n  textEncoder = new TextEncoder();\n  /** @internal */\n  textDecoder = new TextDecoder(\"utf-8\");\n  /** @internal */\n  rankMap = /* @__PURE__ */ new Map();\n  /** @internal */\n  textMap = /* @__PURE__ */ new Map();\n  constructor(ranks, extendedSpecialTokens) {\n    this.patStr = ranks.pat_str;\n    const uncompressed = ranks.bpe_ranks.split(\"\\n\").filter(Boolean).reduce((memo, x) => {\n      const [_, offsetStr, ...tokens] = x.split(\" \");\n      const offset = Number.parseInt(offsetStr, 10);\n      tokens.forEach((token, i) => memo[token] = offset + i);\n      return memo;\n    }, {});\n    for (const [token, rank] of Object.entries(uncompressed)) {\n      const bytes = base64__default.default.toByteArray(token);\n      this.rankMap.set(bytes.join(\",\"), rank);\n      this.textMap.set(rank, bytes);\n    }\n    this.specialTokens = { ...ranks.special_tokens, ...extendedSpecialTokens };\n    this.inverseSpecialTokens = Object.entries(this.specialTokens).reduce((memo, [text, rank]) => {\n      memo[rank] = this.textEncoder.encode(text);\n      return memo;\n    }, {});\n  }\n  encode(text, allowedSpecial = [], disallowedSpecial = \"all\") {\n    const regexes = new RegExp(this.patStr, \"ug\");\n    const specialRegex = _Tiktoken.specialTokenRegex(\n      Object.keys(this.specialTokens)\n    );\n    const ret = [];\n    const allowedSpecialSet = new Set(\n      allowedSpecial === \"all\" ? Object.keys(this.specialTokens) : allowedSpecial\n    );\n    const disallowedSpecialSet = new Set(\n      disallowedSpecial === \"all\" ? Object.keys(this.specialTokens).filter(\n        (x) => !allowedSpecialSet.has(x)\n      ) : disallowedSpecial\n    );\n    if (disallowedSpecialSet.size > 0) {\n      const disallowedSpecialRegex = _Tiktoken.specialTokenRegex([\n        ...disallowedSpecialSet\n      ]);\n      const specialMatch = text.match(disallowedSpecialRegex);\n      if (specialMatch != null) {\n        throw new Error(\n          `The text contains a special token that is not allowed: ${specialMatch[0]}`\n        );\n      }\n    }\n    let start = 0;\n    while (true) {\n      let nextSpecial = null;\n      let startFind = start;\n      while (true) {\n        specialRegex.lastIndex = startFind;\n        nextSpecial = specialRegex.exec(text);\n        if (nextSpecial == null || allowedSpecialSet.has(nextSpecial[0]))\n          break;\n        startFind = nextSpecial.index + 1;\n      }\n      const end = nextSpecial?.index ?? text.length;\n      for (const match of text.substring(start, end).matchAll(regexes)) {\n        const piece = this.textEncoder.encode(match[0]);\n        const token2 = this.rankMap.get(piece.join(\",\"));\n        if (token2 != null) {\n          ret.push(token2);\n          continue;\n        }\n        ret.push(...bytePairEncode(piece, this.rankMap));\n      }\n      if (nextSpecial == null)\n        break;\n      let token = this.specialTokens[nextSpecial[0]];\n      ret.push(token);\n      start = nextSpecial.index + nextSpecial[0].length;\n    }\n    return ret;\n  }\n  decode(tokens) {\n    const res = [];\n    let length = 0;\n    for (let i2 = 0; i2 < tokens.length; ++i2) {\n      const token = tokens[i2];\n      const bytes = this.textMap.get(token) ?? this.inverseSpecialTokens[token];\n      if (bytes != null) {\n        res.push(bytes);\n        length += bytes.length;\n      }\n    }\n    const mergedArray = new Uint8Array(length);\n    let i = 0;\n    for (const bytes of res) {\n      mergedArray.set(bytes, i);\n      i += bytes.length;\n    }\n    return this.textDecoder.decode(mergedArray);\n  }\n};\nvar Tiktoken = _Tiktoken;\n__publicField(Tiktoken, \"specialTokenRegex\", (tokens) => {\n  return new RegExp(tokens.map((i) => escapeRegex(i)).join(\"|\"), \"g\");\n});\nfunction getEncodingNameForModel(model) {\n  switch (model) {\n    case \"gpt2\": {\n      return \"gpt2\";\n    }\n    case \"code-cushman-001\":\n    case \"code-cushman-002\":\n    case \"code-davinci-001\":\n    case \"code-davinci-002\":\n    case \"cushman-codex\":\n    case \"davinci-codex\":\n    case \"davinci-002\":\n    case \"text-davinci-002\":\n    case \"text-davinci-003\": {\n      return \"p50k_base\";\n    }\n    case \"code-davinci-edit-001\":\n    case \"text-davinci-edit-001\": {\n      return \"p50k_edit\";\n    }\n    case \"ada\":\n    case \"babbage\":\n    case \"babbage-002\":\n    case \"code-search-ada-code-001\":\n    case \"code-search-babbage-code-001\":\n    case \"curie\":\n    case \"davinci\":\n    case \"text-ada-001\":\n    case \"text-babbage-001\":\n    case \"text-curie-001\":\n    case \"text-davinci-001\":\n    case \"text-search-ada-doc-001\":\n    case \"text-search-babbage-doc-001\":\n    case \"text-search-curie-doc-001\":\n    case \"text-search-davinci-doc-001\":\n    case \"text-similarity-ada-001\":\n    case \"text-similarity-babbage-001\":\n    case \"text-similarity-curie-001\":\n    case \"text-similarity-davinci-001\": {\n      return \"r50k_base\";\n    }\n    case \"gpt-3.5-turbo-instruct-0914\":\n    case \"gpt-3.5-turbo-instruct\":\n    case \"gpt-3.5-turbo-16k-0613\":\n    case \"gpt-3.5-turbo-16k\":\n    case \"gpt-3.5-turbo-0613\":\n    case \"gpt-3.5-turbo-0301\":\n    case \"gpt-3.5-turbo\":\n    case \"gpt-4-32k-0613\":\n    case \"gpt-4-32k-0314\":\n    case \"gpt-4-32k\":\n    case \"gpt-4-0613\":\n    case \"gpt-4-0314\":\n    case \"gpt-4\":\n    case \"gpt-3.5-turbo-1106\":\n    case \"gpt-35-turbo\":\n    case \"gpt-4-1106-preview\":\n    case \"gpt-4-vision-preview\":\n    case \"gpt-3.5-turbo-0125\":\n    case \"gpt-4-turbo\":\n    case \"gpt-4-turbo-2024-04-09\":\n    case \"gpt-4-turbo-preview\":\n    case \"gpt-4-0125-preview\":\n    case \"text-embedding-ada-002\":\n    case \"text-embedding-3-small\":\n    case \"text-embedding-3-large\": {\n      return \"cl100k_base\";\n    }\n    case \"gpt-4o\":\n    case \"gpt-4o-2024-05-13\":\n    case \"gpt-4o-2024-08-06\":\n    case \"gpt-4o-mini-2024-07-18\":\n    case \"gpt-4o-mini\":\n    case \"o1-mini\":\n    case \"o1-preview\":\n    case \"o1-preview-2024-09-12\":\n    case \"o1-mini-2024-09-12\":\n    case \"chatgpt-4o-latest\":\n    case \"gpt-4o-realtime\":\n    case \"gpt-4o-realtime-preview-2024-10-01\": {\n      return \"o200k_base\";\n    }\n    default:\n      throw new Error(\"Unknown model\");\n  }\n}\n\nexports.Tiktoken = Tiktoken;\nexports.getEncodingNameForModel = getEncodingNameForModel;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/js-tiktoken@1.0.15/node_modules/js-tiktoken/dist/lite.cjs\n");

/***/ })

};
;