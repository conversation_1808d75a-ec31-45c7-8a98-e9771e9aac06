"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+middleware-stack@4.0.4";
exports.ids = ["vendor-chunks/@smithy+middleware-stack@4.0.4"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-stack@4.0.4/node_modules/@smithy/middleware-stack/dist-es/MiddlewareStack.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-stack@4.0.4/node_modules/@smithy/middleware-stack/dist-es/MiddlewareStack.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   constructStack: () => (/* binding */ constructStack)\n/* harmony export */ });\nconst getAllAliases = (name, aliases) => {\n    const _aliases = [];\n    if (name) {\n        _aliases.push(name);\n    }\n    if (aliases) {\n        for (const alias of aliases) {\n            _aliases.push(alias);\n        }\n    }\n    return _aliases;\n};\nconst getMiddlewareNameWithAliases = (name, aliases) => {\n    return `${name || \"anonymous\"}${aliases && aliases.length > 0 ? ` (a.k.a. ${aliases.join(\",\")})` : \"\"}`;\n};\nconst constructStack = () => {\n    let absoluteEntries = [];\n    let relativeEntries = [];\n    let identifyOnResolve = false;\n    const entriesNameSet = new Set();\n    const sort = (entries) => entries.sort((a, b) => stepWeights[b.step] - stepWeights[a.step] ||\n        priorityWeights[b.priority || \"normal\"] - priorityWeights[a.priority || \"normal\"]);\n    const removeByName = (toRemove) => {\n        let isRemoved = false;\n        const filterCb = (entry) => {\n            const aliases = getAllAliases(entry.name, entry.aliases);\n            if (aliases.includes(toRemove)) {\n                isRemoved = true;\n                for (const alias of aliases) {\n                    entriesNameSet.delete(alias);\n                }\n                return false;\n            }\n            return true;\n        };\n        absoluteEntries = absoluteEntries.filter(filterCb);\n        relativeEntries = relativeEntries.filter(filterCb);\n        return isRemoved;\n    };\n    const removeByReference = (toRemove) => {\n        let isRemoved = false;\n        const filterCb = (entry) => {\n            if (entry.middleware === toRemove) {\n                isRemoved = true;\n                for (const alias of getAllAliases(entry.name, entry.aliases)) {\n                    entriesNameSet.delete(alias);\n                }\n                return false;\n            }\n            return true;\n        };\n        absoluteEntries = absoluteEntries.filter(filterCb);\n        relativeEntries = relativeEntries.filter(filterCb);\n        return isRemoved;\n    };\n    const cloneTo = (toStack) => {\n        absoluteEntries.forEach((entry) => {\n            toStack.add(entry.middleware, { ...entry });\n        });\n        relativeEntries.forEach((entry) => {\n            toStack.addRelativeTo(entry.middleware, { ...entry });\n        });\n        toStack.identifyOnResolve?.(stack.identifyOnResolve());\n        return toStack;\n    };\n    const expandRelativeMiddlewareList = (from) => {\n        const expandedMiddlewareList = [];\n        from.before.forEach((entry) => {\n            if (entry.before.length === 0 && entry.after.length === 0) {\n                expandedMiddlewareList.push(entry);\n            }\n            else {\n                expandedMiddlewareList.push(...expandRelativeMiddlewareList(entry));\n            }\n        });\n        expandedMiddlewareList.push(from);\n        from.after.reverse().forEach((entry) => {\n            if (entry.before.length === 0 && entry.after.length === 0) {\n                expandedMiddlewareList.push(entry);\n            }\n            else {\n                expandedMiddlewareList.push(...expandRelativeMiddlewareList(entry));\n            }\n        });\n        return expandedMiddlewareList;\n    };\n    const getMiddlewareList = (debug = false) => {\n        const normalizedAbsoluteEntries = [];\n        const normalizedRelativeEntries = [];\n        const normalizedEntriesNameMap = {};\n        absoluteEntries.forEach((entry) => {\n            const normalizedEntry = {\n                ...entry,\n                before: [],\n                after: [],\n            };\n            for (const alias of getAllAliases(normalizedEntry.name, normalizedEntry.aliases)) {\n                normalizedEntriesNameMap[alias] = normalizedEntry;\n            }\n            normalizedAbsoluteEntries.push(normalizedEntry);\n        });\n        relativeEntries.forEach((entry) => {\n            const normalizedEntry = {\n                ...entry,\n                before: [],\n                after: [],\n            };\n            for (const alias of getAllAliases(normalizedEntry.name, normalizedEntry.aliases)) {\n                normalizedEntriesNameMap[alias] = normalizedEntry;\n            }\n            normalizedRelativeEntries.push(normalizedEntry);\n        });\n        normalizedRelativeEntries.forEach((entry) => {\n            if (entry.toMiddleware) {\n                const toMiddleware = normalizedEntriesNameMap[entry.toMiddleware];\n                if (toMiddleware === undefined) {\n                    if (debug) {\n                        return;\n                    }\n                    throw new Error(`${entry.toMiddleware} is not found when adding ` +\n                        `${getMiddlewareNameWithAliases(entry.name, entry.aliases)} ` +\n                        `middleware ${entry.relation} ${entry.toMiddleware}`);\n                }\n                if (entry.relation === \"after\") {\n                    toMiddleware.after.push(entry);\n                }\n                if (entry.relation === \"before\") {\n                    toMiddleware.before.push(entry);\n                }\n            }\n        });\n        const mainChain = sort(normalizedAbsoluteEntries)\n            .map(expandRelativeMiddlewareList)\n            .reduce((wholeList, expandedMiddlewareList) => {\n            wholeList.push(...expandedMiddlewareList);\n            return wholeList;\n        }, []);\n        return mainChain;\n    };\n    const stack = {\n        add: (middleware, options = {}) => {\n            const { name, override, aliases: _aliases } = options;\n            const entry = {\n                step: \"initialize\",\n                priority: \"normal\",\n                middleware,\n                ...options,\n            };\n            const aliases = getAllAliases(name, _aliases);\n            if (aliases.length > 0) {\n                if (aliases.some((alias) => entriesNameSet.has(alias))) {\n                    if (!override)\n                        throw new Error(`Duplicate middleware name '${getMiddlewareNameWithAliases(name, _aliases)}'`);\n                    for (const alias of aliases) {\n                        const toOverrideIndex = absoluteEntries.findIndex((entry) => entry.name === alias || entry.aliases?.some((a) => a === alias));\n                        if (toOverrideIndex === -1) {\n                            continue;\n                        }\n                        const toOverride = absoluteEntries[toOverrideIndex];\n                        if (toOverride.step !== entry.step || entry.priority !== toOverride.priority) {\n                            throw new Error(`\"${getMiddlewareNameWithAliases(toOverride.name, toOverride.aliases)}\" middleware with ` +\n                                `${toOverride.priority} priority in ${toOverride.step} step cannot ` +\n                                `be overridden by \"${getMiddlewareNameWithAliases(name, _aliases)}\" middleware with ` +\n                                `${entry.priority} priority in ${entry.step} step.`);\n                        }\n                        absoluteEntries.splice(toOverrideIndex, 1);\n                    }\n                }\n                for (const alias of aliases) {\n                    entriesNameSet.add(alias);\n                }\n            }\n            absoluteEntries.push(entry);\n        },\n        addRelativeTo: (middleware, options) => {\n            const { name, override, aliases: _aliases } = options;\n            const entry = {\n                middleware,\n                ...options,\n            };\n            const aliases = getAllAliases(name, _aliases);\n            if (aliases.length > 0) {\n                if (aliases.some((alias) => entriesNameSet.has(alias))) {\n                    if (!override)\n                        throw new Error(`Duplicate middleware name '${getMiddlewareNameWithAliases(name, _aliases)}'`);\n                    for (const alias of aliases) {\n                        const toOverrideIndex = relativeEntries.findIndex((entry) => entry.name === alias || entry.aliases?.some((a) => a === alias));\n                        if (toOverrideIndex === -1) {\n                            continue;\n                        }\n                        const toOverride = relativeEntries[toOverrideIndex];\n                        if (toOverride.toMiddleware !== entry.toMiddleware || toOverride.relation !== entry.relation) {\n                            throw new Error(`\"${getMiddlewareNameWithAliases(toOverride.name, toOverride.aliases)}\" middleware ` +\n                                `${toOverride.relation} \"${toOverride.toMiddleware}\" middleware cannot be overridden ` +\n                                `by \"${getMiddlewareNameWithAliases(name, _aliases)}\" middleware ${entry.relation} ` +\n                                `\"${entry.toMiddleware}\" middleware.`);\n                        }\n                        relativeEntries.splice(toOverrideIndex, 1);\n                    }\n                }\n                for (const alias of aliases) {\n                    entriesNameSet.add(alias);\n                }\n            }\n            relativeEntries.push(entry);\n        },\n        clone: () => cloneTo(constructStack()),\n        use: (plugin) => {\n            plugin.applyToStack(stack);\n        },\n        remove: (toRemove) => {\n            if (typeof toRemove === \"string\")\n                return removeByName(toRemove);\n            else\n                return removeByReference(toRemove);\n        },\n        removeByTag: (toRemove) => {\n            let isRemoved = false;\n            const filterCb = (entry) => {\n                const { tags, name, aliases: _aliases } = entry;\n                if (tags && tags.includes(toRemove)) {\n                    const aliases = getAllAliases(name, _aliases);\n                    for (const alias of aliases) {\n                        entriesNameSet.delete(alias);\n                    }\n                    isRemoved = true;\n                    return false;\n                }\n                return true;\n            };\n            absoluteEntries = absoluteEntries.filter(filterCb);\n            relativeEntries = relativeEntries.filter(filterCb);\n            return isRemoved;\n        },\n        concat: (from) => {\n            const cloned = cloneTo(constructStack());\n            cloned.use(from);\n            cloned.identifyOnResolve(identifyOnResolve || cloned.identifyOnResolve() || (from.identifyOnResolve?.() ?? false));\n            return cloned;\n        },\n        applyToStack: cloneTo,\n        identify: () => {\n            return getMiddlewareList(true).map((mw) => {\n                const step = mw.step ??\n                    mw.relation +\n                        \" \" +\n                        mw.toMiddleware;\n                return getMiddlewareNameWithAliases(mw.name, mw.aliases) + \" - \" + step;\n            });\n        },\n        identifyOnResolve(toggle) {\n            if (typeof toggle === \"boolean\")\n                identifyOnResolve = toggle;\n            return identifyOnResolve;\n        },\n        resolve: (handler, context) => {\n            for (const middleware of getMiddlewareList()\n                .map((entry) => entry.middleware)\n                .reverse()) {\n                handler = middleware(handler, context);\n            }\n            if (identifyOnResolve) {\n                console.log(stack.identify());\n            }\n            return handler;\n        },\n    };\n    return stack;\n};\nconst stepWeights = {\n    initialize: 5,\n    serialize: 4,\n    build: 3,\n    finalizeRequest: 2,\n    deserialize: 1,\n};\nconst priorityWeights = {\n    high: 3,\n    normal: 2,\n    low: 1,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-stack@4.0.4/node_modules/@smithy/middleware-stack/dist-es/MiddlewareStack.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-stack@4.0.4/node_modules/@smithy/middleware-stack/dist-es/index.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-stack@4.0.4/node_modules/@smithy/middleware-stack/dist-es/index.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   constructStack: () => (/* reexport safe */ _MiddlewareStack__WEBPACK_IMPORTED_MODULE_0__.constructStack)\n/* harmony export */ });\n/* harmony import */ var _MiddlewareStack__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./MiddlewareStack */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-stack@4.0.4/node_modules/@smithy/middleware-stack/dist-es/MiddlewareStack.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLXN0YWNrQDQuMC40L25vZGVfbW9kdWxlcy9Ac21pdGh5L21pZGRsZXdhcmUtc3RhY2svZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrQyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrbWlkZGxld2FyZS1zdGFja0A0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLXN0YWNrL2Rpc3QtZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vTWlkZGxld2FyZVN0YWNrXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-stack@4.0.4/node_modules/@smithy/middleware-stack/dist-es/index.js\n");

/***/ })

};
;