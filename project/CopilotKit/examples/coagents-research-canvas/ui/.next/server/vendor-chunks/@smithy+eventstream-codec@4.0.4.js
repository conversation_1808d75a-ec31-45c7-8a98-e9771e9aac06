"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+eventstream-codec@4.0.4";
exports.ids = ["vendor-chunks/@smithy+eventstream-codec@4.0.4"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/EventStreamCodec.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/EventStreamCodec.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventStreamCodec: () => (/* binding */ EventStreamCodec)\n/* harmony export */ });\n/* harmony import */ var _aws_crypto_crc32__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-crypto/crc32 */ \"(rsc)/./node_modules/.pnpm/@aws-crypto+crc32@5.2.0/node_modules/@aws-crypto/crc32/build/module/index.js\");\n/* harmony import */ var _HeaderMarshaller__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./HeaderMarshaller */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/HeaderMarshaller.js\");\n/* harmony import */ var _splitMessage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./splitMessage */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/splitMessage.js\");\n\n\n\nclass EventStreamCodec {\n    constructor(toUtf8, fromUtf8) {\n        this.headerMarshaller = new _HeaderMarshaller__WEBPACK_IMPORTED_MODULE_1__.HeaderMarshaller(toUtf8, fromUtf8);\n        this.messageBuffer = [];\n        this.isEndOfStream = false;\n    }\n    feed(message) {\n        this.messageBuffer.push(this.decode(message));\n    }\n    endOfStream() {\n        this.isEndOfStream = true;\n    }\n    getMessage() {\n        const message = this.messageBuffer.pop();\n        const isEndOfStream = this.isEndOfStream;\n        return {\n            getMessage() {\n                return message;\n            },\n            isEndOfStream() {\n                return isEndOfStream;\n            },\n        };\n    }\n    getAvailableMessages() {\n        const messages = this.messageBuffer;\n        this.messageBuffer = [];\n        const isEndOfStream = this.isEndOfStream;\n        return {\n            getMessages() {\n                return messages;\n            },\n            isEndOfStream() {\n                return isEndOfStream;\n            },\n        };\n    }\n    encode({ headers: rawHeaders, body }) {\n        const headers = this.headerMarshaller.format(rawHeaders);\n        const length = headers.byteLength + body.byteLength + 16;\n        const out = new Uint8Array(length);\n        const view = new DataView(out.buffer, out.byteOffset, out.byteLength);\n        const checksum = new _aws_crypto_crc32__WEBPACK_IMPORTED_MODULE_0__.Crc32();\n        view.setUint32(0, length, false);\n        view.setUint32(4, headers.byteLength, false);\n        view.setUint32(8, checksum.update(out.subarray(0, 8)).digest(), false);\n        out.set(headers, 12);\n        out.set(body, headers.byteLength + 12);\n        view.setUint32(length - 4, checksum.update(out.subarray(8, length - 4)).digest(), false);\n        return out;\n    }\n    decode(message) {\n        const { headers, body } = (0,_splitMessage__WEBPACK_IMPORTED_MODULE_2__.splitMessage)(message);\n        return { headers: this.headerMarshaller.parse(headers), body };\n    }\n    formatHeaders(rawHeaders) {\n        return this.headerMarshaller.format(rawHeaders);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/EventStreamCodec.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/HeaderMarshaller.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/HeaderMarshaller.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeaderMarshaller: () => (/* binding */ HeaderMarshaller)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-hex-encoding */ \"(rsc)/./node_modules/.pnpm/@smithy+util-hex-encoding@4.0.0/node_modules/@smithy/util-hex-encoding/dist-es/index.js\");\n/* harmony import */ var _Int64__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Int64 */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/Int64.js\");\n\n\nclass HeaderMarshaller {\n    constructor(toUtf8, fromUtf8) {\n        this.toUtf8 = toUtf8;\n        this.fromUtf8 = fromUtf8;\n    }\n    format(headers) {\n        const chunks = [];\n        for (const headerName of Object.keys(headers)) {\n            const bytes = this.fromUtf8(headerName);\n            chunks.push(Uint8Array.from([bytes.byteLength]), bytes, this.formatHeaderValue(headers[headerName]));\n        }\n        const out = new Uint8Array(chunks.reduce((carry, bytes) => carry + bytes.byteLength, 0));\n        let position = 0;\n        for (const chunk of chunks) {\n            out.set(chunk, position);\n            position += chunk.byteLength;\n        }\n        return out;\n    }\n    formatHeaderValue(header) {\n        switch (header.type) {\n            case \"boolean\":\n                return Uint8Array.from([header.value ? 0 : 1]);\n            case \"byte\":\n                return Uint8Array.from([2, header.value]);\n            case \"short\":\n                const shortView = new DataView(new ArrayBuffer(3));\n                shortView.setUint8(0, 3);\n                shortView.setInt16(1, header.value, false);\n                return new Uint8Array(shortView.buffer);\n            case \"integer\":\n                const intView = new DataView(new ArrayBuffer(5));\n                intView.setUint8(0, 4);\n                intView.setInt32(1, header.value, false);\n                return new Uint8Array(intView.buffer);\n            case \"long\":\n                const longBytes = new Uint8Array(9);\n                longBytes[0] = 5;\n                longBytes.set(header.value.bytes, 1);\n                return longBytes;\n            case \"binary\":\n                const binView = new DataView(new ArrayBuffer(3 + header.value.byteLength));\n                binView.setUint8(0, 6);\n                binView.setUint16(1, header.value.byteLength, false);\n                const binBytes = new Uint8Array(binView.buffer);\n                binBytes.set(header.value, 3);\n                return binBytes;\n            case \"string\":\n                const utf8Bytes = this.fromUtf8(header.value);\n                const strView = new DataView(new ArrayBuffer(3 + utf8Bytes.byteLength));\n                strView.setUint8(0, 7);\n                strView.setUint16(1, utf8Bytes.byteLength, false);\n                const strBytes = new Uint8Array(strView.buffer);\n                strBytes.set(utf8Bytes, 3);\n                return strBytes;\n            case \"timestamp\":\n                const tsBytes = new Uint8Array(9);\n                tsBytes[0] = 8;\n                tsBytes.set(_Int64__WEBPACK_IMPORTED_MODULE_1__.Int64.fromNumber(header.value.valueOf()).bytes, 1);\n                return tsBytes;\n            case \"uuid\":\n                if (!UUID_PATTERN.test(header.value)) {\n                    throw new Error(`Invalid UUID received: ${header.value}`);\n                }\n                const uuidBytes = new Uint8Array(17);\n                uuidBytes[0] = 9;\n                uuidBytes.set((0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.fromHex)(header.value.replace(/\\-/g, \"\")), 1);\n                return uuidBytes;\n        }\n    }\n    parse(headers) {\n        const out = {};\n        let position = 0;\n        while (position < headers.byteLength) {\n            const nameLength = headers.getUint8(position++);\n            const name = this.toUtf8(new Uint8Array(headers.buffer, headers.byteOffset + position, nameLength));\n            position += nameLength;\n            switch (headers.getUint8(position++)) {\n                case 0:\n                    out[name] = {\n                        type: BOOLEAN_TAG,\n                        value: true,\n                    };\n                    break;\n                case 1:\n                    out[name] = {\n                        type: BOOLEAN_TAG,\n                        value: false,\n                    };\n                    break;\n                case 2:\n                    out[name] = {\n                        type: BYTE_TAG,\n                        value: headers.getInt8(position++),\n                    };\n                    break;\n                case 3:\n                    out[name] = {\n                        type: SHORT_TAG,\n                        value: headers.getInt16(position, false),\n                    };\n                    position += 2;\n                    break;\n                case 4:\n                    out[name] = {\n                        type: INT_TAG,\n                        value: headers.getInt32(position, false),\n                    };\n                    position += 4;\n                    break;\n                case 5:\n                    out[name] = {\n                        type: LONG_TAG,\n                        value: new _Int64__WEBPACK_IMPORTED_MODULE_1__.Int64(new Uint8Array(headers.buffer, headers.byteOffset + position, 8)),\n                    };\n                    position += 8;\n                    break;\n                case 6:\n                    const binaryLength = headers.getUint16(position, false);\n                    position += 2;\n                    out[name] = {\n                        type: BINARY_TAG,\n                        value: new Uint8Array(headers.buffer, headers.byteOffset + position, binaryLength),\n                    };\n                    position += binaryLength;\n                    break;\n                case 7:\n                    const stringLength = headers.getUint16(position, false);\n                    position += 2;\n                    out[name] = {\n                        type: STRING_TAG,\n                        value: this.toUtf8(new Uint8Array(headers.buffer, headers.byteOffset + position, stringLength)),\n                    };\n                    position += stringLength;\n                    break;\n                case 8:\n                    out[name] = {\n                        type: TIMESTAMP_TAG,\n                        value: new Date(new _Int64__WEBPACK_IMPORTED_MODULE_1__.Int64(new Uint8Array(headers.buffer, headers.byteOffset + position, 8)).valueOf()),\n                    };\n                    position += 8;\n                    break;\n                case 9:\n                    const uuidBytes = new Uint8Array(headers.buffer, headers.byteOffset + position, 16);\n                    position += 16;\n                    out[name] = {\n                        type: UUID_TAG,\n                        value: `${(0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(uuidBytes.subarray(0, 4))}-${(0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(uuidBytes.subarray(4, 6))}-${(0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(uuidBytes.subarray(6, 8))}-${(0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(uuidBytes.subarray(8, 10))}-${(0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(uuidBytes.subarray(10))}`,\n                    };\n                    break;\n                default:\n                    throw new Error(`Unrecognized header type tag`);\n            }\n        }\n        return out;\n    }\n}\nvar HEADER_VALUE_TYPE;\n(function (HEADER_VALUE_TYPE) {\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"boolTrue\"] = 0] = \"boolTrue\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"boolFalse\"] = 1] = \"boolFalse\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"byte\"] = 2] = \"byte\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"short\"] = 3] = \"short\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"integer\"] = 4] = \"integer\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"long\"] = 5] = \"long\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"byteArray\"] = 6] = \"byteArray\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"string\"] = 7] = \"string\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"timestamp\"] = 8] = \"timestamp\";\n    HEADER_VALUE_TYPE[HEADER_VALUE_TYPE[\"uuid\"] = 9] = \"uuid\";\n})(HEADER_VALUE_TYPE || (HEADER_VALUE_TYPE = {}));\nconst BOOLEAN_TAG = \"boolean\";\nconst BYTE_TAG = \"byte\";\nconst SHORT_TAG = \"short\";\nconst INT_TAG = \"integer\";\nconst LONG_TAG = \"long\";\nconst BINARY_TAG = \"binary\";\nconst STRING_TAG = \"string\";\nconst TIMESTAMP_TAG = \"timestamp\";\nconst UUID_TAG = \"uuid\";\nconst UUID_PATTERN = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/HeaderMarshaller.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/Int64.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/Int64.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Int64: () => (/* binding */ Int64)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-hex-encoding */ \"(rsc)/./node_modules/.pnpm/@smithy+util-hex-encoding@4.0.0/node_modules/@smithy/util-hex-encoding/dist-es/index.js\");\n\nclass Int64 {\n    constructor(bytes) {\n        this.bytes = bytes;\n        if (bytes.byteLength !== 8) {\n            throw new Error(\"Int64 buffers must be exactly 8 bytes\");\n        }\n    }\n    static fromNumber(number) {\n        if (number > 9223372036854776000 || number < -9223372036854776000) {\n            throw new Error(`${number} is too large (or, if negative, too small) to represent as an Int64`);\n        }\n        const bytes = new Uint8Array(8);\n        for (let i = 7, remaining = Math.abs(Math.round(number)); i > -1 && remaining > 0; i--, remaining /= 256) {\n            bytes[i] = remaining;\n        }\n        if (number < 0) {\n            negate(bytes);\n        }\n        return new Int64(bytes);\n    }\n    valueOf() {\n        const bytes = this.bytes.slice(0);\n        const negative = bytes[0] & 0b10000000;\n        if (negative) {\n            negate(bytes);\n        }\n        return parseInt((0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_0__.toHex)(bytes), 16) * (negative ? -1 : 1);\n    }\n    toString() {\n        return String(this.valueOf());\n    }\n}\nfunction negate(bytes) {\n    for (let i = 0; i < 8; i++) {\n        bytes[i] ^= 0xff;\n    }\n    for (let i = 7; i > -1; i--) {\n        bytes[i]++;\n        if (bytes[i] !== 0)\n            break;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/Int64.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/Message.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/Message.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1jb2RlY0A0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1jb2RlYy9kaXN0LWVzL01lc3NhZ2UuanMiLCJtYXBwaW5ncyI6IjtBQUFVIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1jb2RlY0A0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1jb2RlYy9kaXN0LWVzL01lc3NhZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/Message.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/MessageDecoderStream.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/MessageDecoderStream.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageDecoderStream: () => (/* binding */ MessageDecoderStream)\n/* harmony export */ });\nclass MessageDecoderStream {\n    constructor(options) {\n        this.options = options;\n    }\n    [Symbol.asyncIterator]() {\n        return this.asyncIterator();\n    }\n    async *asyncIterator() {\n        for await (const bytes of this.options.inputStream) {\n            const decoded = this.options.decoder.decode(bytes);\n            yield decoded;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1jb2RlY0A0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1jb2RlYy9kaXN0LWVzL01lc3NhZ2VEZWNvZGVyU3RyZWFtLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1jb2RlY0A0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1jb2RlYy9kaXN0LWVzL01lc3NhZ2VEZWNvZGVyU3RyZWFtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBNZXNzYWdlRGVjb2RlclN0cmVhbSB7XG4gICAgY29uc3RydWN0b3Iob3B0aW9ucykge1xuICAgICAgICB0aGlzLm9wdGlvbnMgPSBvcHRpb25zO1xuICAgIH1cbiAgICBbU3ltYm9sLmFzeW5jSXRlcmF0b3JdKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5hc3luY0l0ZXJhdG9yKCk7XG4gICAgfVxuICAgIGFzeW5jICphc3luY0l0ZXJhdG9yKCkge1xuICAgICAgICBmb3IgYXdhaXQgKGNvbnN0IGJ5dGVzIG9mIHRoaXMub3B0aW9ucy5pbnB1dFN0cmVhbSkge1xuICAgICAgICAgICAgY29uc3QgZGVjb2RlZCA9IHRoaXMub3B0aW9ucy5kZWNvZGVyLmRlY29kZShieXRlcyk7XG4gICAgICAgICAgICB5aWVsZCBkZWNvZGVkO1xuICAgICAgICB9XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/MessageDecoderStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/MessageEncoderStream.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/MessageEncoderStream.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageEncoderStream: () => (/* binding */ MessageEncoderStream)\n/* harmony export */ });\nclass MessageEncoderStream {\n    constructor(options) {\n        this.options = options;\n    }\n    [Symbol.asyncIterator]() {\n        return this.asyncIterator();\n    }\n    async *asyncIterator() {\n        for await (const msg of this.options.messageStream) {\n            const encoded = this.options.encoder.encode(msg);\n            yield encoded;\n        }\n        if (this.options.includeEndFrame) {\n            yield new Uint8Array(0);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1jb2RlY0A0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1jb2RlYy9kaXN0LWVzL01lc3NhZ2VFbmNvZGVyU3RyZWFtLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1jb2RlY0A0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1jb2RlYy9kaXN0LWVzL01lc3NhZ2VFbmNvZGVyU3RyZWFtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBNZXNzYWdlRW5jb2RlclN0cmVhbSB7XG4gICAgY29uc3RydWN0b3Iob3B0aW9ucykge1xuICAgICAgICB0aGlzLm9wdGlvbnMgPSBvcHRpb25zO1xuICAgIH1cbiAgICBbU3ltYm9sLmFzeW5jSXRlcmF0b3JdKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5hc3luY0l0ZXJhdG9yKCk7XG4gICAgfVxuICAgIGFzeW5jICphc3luY0l0ZXJhdG9yKCkge1xuICAgICAgICBmb3IgYXdhaXQgKGNvbnN0IG1zZyBvZiB0aGlzLm9wdGlvbnMubWVzc2FnZVN0cmVhbSkge1xuICAgICAgICAgICAgY29uc3QgZW5jb2RlZCA9IHRoaXMub3B0aW9ucy5lbmNvZGVyLmVuY29kZShtc2cpO1xuICAgICAgICAgICAgeWllbGQgZW5jb2RlZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5vcHRpb25zLmluY2x1ZGVFbmRGcmFtZSkge1xuICAgICAgICAgICAgeWllbGQgbmV3IFVpbnQ4QXJyYXkoMCk7XG4gICAgICAgIH1cbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/MessageEncoderStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/SmithyMessageDecoderStream.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/SmithyMessageDecoderStream.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmithyMessageDecoderStream: () => (/* binding */ SmithyMessageDecoderStream)\n/* harmony export */ });\nclass SmithyMessageDecoderStream {\n    constructor(options) {\n        this.options = options;\n    }\n    [Symbol.asyncIterator]() {\n        return this.asyncIterator();\n    }\n    async *asyncIterator() {\n        for await (const message of this.options.messageStream) {\n            const deserialized = await this.options.deserializer(message);\n            if (deserialized === undefined)\n                continue;\n            yield deserialized;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1jb2RlY0A0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1jb2RlYy9kaXN0LWVzL1NtaXRoeU1lc3NhZ2VEZWNvZGVyU3RyZWFtLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrZXZlbnRzdHJlYW0tY29kZWNANC4wLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvZXZlbnRzdHJlYW0tY29kZWMvZGlzdC1lcy9TbWl0aHlNZXNzYWdlRGVjb2RlclN0cmVhbS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgU21pdGh5TWVzc2FnZURlY29kZXJTdHJlYW0ge1xuICAgIGNvbnN0cnVjdG9yKG9wdGlvbnMpIHtcbiAgICAgICAgdGhpcy5vcHRpb25zID0gb3B0aW9ucztcbiAgICB9XG4gICAgW1N5bWJvbC5hc3luY0l0ZXJhdG9yXSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuYXN5bmNJdGVyYXRvcigpO1xuICAgIH1cbiAgICBhc3luYyAqYXN5bmNJdGVyYXRvcigpIHtcbiAgICAgICAgZm9yIGF3YWl0IChjb25zdCBtZXNzYWdlIG9mIHRoaXMub3B0aW9ucy5tZXNzYWdlU3RyZWFtKSB7XG4gICAgICAgICAgICBjb25zdCBkZXNlcmlhbGl6ZWQgPSBhd2FpdCB0aGlzLm9wdGlvbnMuZGVzZXJpYWxpemVyKG1lc3NhZ2UpO1xuICAgICAgICAgICAgaWYgKGRlc2VyaWFsaXplZCA9PT0gdW5kZWZpbmVkKVxuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgeWllbGQgZGVzZXJpYWxpemVkO1xuICAgICAgICB9XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/SmithyMessageDecoderStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/SmithyMessageEncoderStream.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/SmithyMessageEncoderStream.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmithyMessageEncoderStream: () => (/* binding */ SmithyMessageEncoderStream)\n/* harmony export */ });\nclass SmithyMessageEncoderStream {\n    constructor(options) {\n        this.options = options;\n    }\n    [Symbol.asyncIterator]() {\n        return this.asyncIterator();\n    }\n    async *asyncIterator() {\n        for await (const chunk of this.options.inputStream) {\n            const payloadBuf = this.options.serializer(chunk);\n            yield payloadBuf;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1jb2RlY0A0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1jb2RlYy9kaXN0LWVzL1NtaXRoeU1lc3NhZ2VFbmNvZGVyU3RyZWFtLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1jb2RlY0A0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1jb2RlYy9kaXN0LWVzL1NtaXRoeU1lc3NhZ2VFbmNvZGVyU3RyZWFtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBTbWl0aHlNZXNzYWdlRW5jb2RlclN0cmVhbSB7XG4gICAgY29uc3RydWN0b3Iob3B0aW9ucykge1xuICAgICAgICB0aGlzLm9wdGlvbnMgPSBvcHRpb25zO1xuICAgIH1cbiAgICBbU3ltYm9sLmFzeW5jSXRlcmF0b3JdKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5hc3luY0l0ZXJhdG9yKCk7XG4gICAgfVxuICAgIGFzeW5jICphc3luY0l0ZXJhdG9yKCkge1xuICAgICAgICBmb3IgYXdhaXQgKGNvbnN0IGNodW5rIG9mIHRoaXMub3B0aW9ucy5pbnB1dFN0cmVhbSkge1xuICAgICAgICAgICAgY29uc3QgcGF5bG9hZEJ1ZiA9IHRoaXMub3B0aW9ucy5zZXJpYWxpemVyKGNodW5rKTtcbiAgICAgICAgICAgIHlpZWxkIHBheWxvYWRCdWY7XG4gICAgICAgIH1cbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/SmithyMessageEncoderStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/index.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventStreamCodec: () => (/* reexport safe */ _EventStreamCodec__WEBPACK_IMPORTED_MODULE_0__.EventStreamCodec),\n/* harmony export */   HeaderMarshaller: () => (/* reexport safe */ _HeaderMarshaller__WEBPACK_IMPORTED_MODULE_1__.HeaderMarshaller),\n/* harmony export */   Int64: () => (/* reexport safe */ _Int64__WEBPACK_IMPORTED_MODULE_2__.Int64),\n/* harmony export */   MessageDecoderStream: () => (/* reexport safe */ _MessageDecoderStream__WEBPACK_IMPORTED_MODULE_4__.MessageDecoderStream),\n/* harmony export */   MessageEncoderStream: () => (/* reexport safe */ _MessageEncoderStream__WEBPACK_IMPORTED_MODULE_5__.MessageEncoderStream),\n/* harmony export */   SmithyMessageDecoderStream: () => (/* reexport safe */ _SmithyMessageDecoderStream__WEBPACK_IMPORTED_MODULE_6__.SmithyMessageDecoderStream),\n/* harmony export */   SmithyMessageEncoderStream: () => (/* reexport safe */ _SmithyMessageEncoderStream__WEBPACK_IMPORTED_MODULE_7__.SmithyMessageEncoderStream)\n/* harmony export */ });\n/* harmony import */ var _EventStreamCodec__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EventStreamCodec */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/EventStreamCodec.js\");\n/* harmony import */ var _HeaderMarshaller__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./HeaderMarshaller */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/HeaderMarshaller.js\");\n/* harmony import */ var _Int64__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Int64 */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/Int64.js\");\n/* harmony import */ var _Message__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Message */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/Message.js\");\n/* harmony import */ var _MessageDecoderStream__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MessageDecoderStream */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/MessageDecoderStream.js\");\n/* harmony import */ var _MessageEncoderStream__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MessageEncoderStream */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/MessageEncoderStream.js\");\n/* harmony import */ var _SmithyMessageDecoderStream__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./SmithyMessageDecoderStream */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/SmithyMessageDecoderStream.js\");\n/* harmony import */ var _SmithyMessageEncoderStream__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./SmithyMessageEncoderStream */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/SmithyMessageEncoderStream.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1jb2RlY0A0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1jb2RlYy9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFtQztBQUNBO0FBQ1g7QUFDRTtBQUNhO0FBQ0E7QUFDTTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1jb2RlY0A0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1jb2RlYy9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL0V2ZW50U3RyZWFtQ29kZWNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL0hlYWRlck1hcnNoYWxsZXJcIjtcbmV4cG9ydCAqIGZyb20gXCIuL0ludDY0XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9NZXNzYWdlXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9NZXNzYWdlRGVjb2RlclN0cmVhbVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vTWVzc2FnZUVuY29kZXJTdHJlYW1cIjtcbmV4cG9ydCAqIGZyb20gXCIuL1NtaXRoeU1lc3NhZ2VEZWNvZGVyU3RyZWFtXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9TbWl0aHlNZXNzYWdlRW5jb2RlclN0cmVhbVwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/splitMessage.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/splitMessage.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   splitMessage: () => (/* binding */ splitMessage)\n/* harmony export */ });\n/* harmony import */ var _aws_crypto_crc32__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-crypto/crc32 */ \"(rsc)/./node_modules/.pnpm/@aws-crypto+crc32@5.2.0/node_modules/@aws-crypto/crc32/build/module/index.js\");\n\nconst PRELUDE_MEMBER_LENGTH = 4;\nconst PRELUDE_LENGTH = PRELUDE_MEMBER_LENGTH * 2;\nconst CHECKSUM_LENGTH = 4;\nconst MINIMUM_MESSAGE_LENGTH = PRELUDE_LENGTH + CHECKSUM_LENGTH * 2;\nfunction splitMessage({ byteLength, byteOffset, buffer }) {\n    if (byteLength < MINIMUM_MESSAGE_LENGTH) {\n        throw new Error(\"Provided message too short to accommodate event stream message overhead\");\n    }\n    const view = new DataView(buffer, byteOffset, byteLength);\n    const messageLength = view.getUint32(0, false);\n    if (byteLength !== messageLength) {\n        throw new Error(\"Reported message length does not match received message length\");\n    }\n    const headerLength = view.getUint32(PRELUDE_MEMBER_LENGTH, false);\n    const expectedPreludeChecksum = view.getUint32(PRELUDE_LENGTH, false);\n    const expectedMessageChecksum = view.getUint32(byteLength - CHECKSUM_LENGTH, false);\n    const checksummer = new _aws_crypto_crc32__WEBPACK_IMPORTED_MODULE_0__.Crc32().update(new Uint8Array(buffer, byteOffset, PRELUDE_LENGTH));\n    if (expectedPreludeChecksum !== checksummer.digest()) {\n        throw new Error(`The prelude checksum specified in the message (${expectedPreludeChecksum}) does not match the calculated CRC32 checksum (${checksummer.digest()})`);\n    }\n    checksummer.update(new Uint8Array(buffer, byteOffset + PRELUDE_LENGTH, byteLength - (PRELUDE_LENGTH + CHECKSUM_LENGTH)));\n    if (expectedMessageChecksum !== checksummer.digest()) {\n        throw new Error(`The message checksum (${checksummer.digest()}) did not match the expected value of ${expectedMessageChecksum}`);\n    }\n    return {\n        headers: new DataView(buffer, byteOffset + PRELUDE_LENGTH + CHECKSUM_LENGTH, headerLength),\n        body: new Uint8Array(buffer, byteOffset + PRELUDE_LENGTH + CHECKSUM_LENGTH + headerLength, messageLength - headerLength - (PRELUDE_LENGTH + CHECKSUM_LENGTH + CHECKSUM_LENGTH)),\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-codec@4.0.4/node_modules/@smithy/eventstream-codec/dist-es/splitMessage.js\n");

/***/ })

};
;