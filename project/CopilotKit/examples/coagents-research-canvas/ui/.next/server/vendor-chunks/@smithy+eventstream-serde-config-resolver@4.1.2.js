"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+eventstream-serde-config-resolver@4.1.2";
exports.ids = ["vendor-chunks/@smithy+eventstream-serde-config-resolver@4.1.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-config-resolver@4.1.2/node_modules/@smithy/eventstream-serde-config-resolver/dist-es/EventStreamSerdeConfig.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-serde-config-resolver@4.1.2/node_modules/@smithy/eventstream-serde-config-resolver/dist-es/EventStreamSerdeConfig.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveEventStreamSerdeConfig: () => (/* binding */ resolveEventStreamSerdeConfig)\n/* harmony export */ });\nconst resolveEventStreamSerdeConfig = (input) => Object.assign(input, {\n    eventStreamMarshaller: input.eventStreamSerdeProvider(input),\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1zZXJkZS1jb25maWctcmVzb2x2ZXJANC4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvZXZlbnRzdHJlYW0tc2VyZGUtY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvRXZlbnRTdHJlYW1TZXJkZUNvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2V2ZW50c3RyZWFtLXNlcmRlLWNvbmZpZy1yZXNvbHZlckA0LjEuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1zZXJkZS1jb25maWctcmVzb2x2ZXIvZGlzdC1lcy9FdmVudFN0cmVhbVNlcmRlQ29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCByZXNvbHZlRXZlbnRTdHJlYW1TZXJkZUNvbmZpZyA9IChpbnB1dCkgPT4gT2JqZWN0LmFzc2lnbihpbnB1dCwge1xuICAgIGV2ZW50U3RyZWFtTWFyc2hhbGxlcjogaW5wdXQuZXZlbnRTdHJlYW1TZXJkZVByb3ZpZGVyKGlucHV0KSxcbn0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-config-resolver@4.1.2/node_modules/@smithy/eventstream-serde-config-resolver/dist-es/EventStreamSerdeConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-config-resolver@4.1.2/node_modules/@smithy/eventstream-serde-config-resolver/dist-es/index.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+eventstream-serde-config-resolver@4.1.2/node_modules/@smithy/eventstream-serde-config-resolver/dist-es/index.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveEventStreamSerdeConfig: () => (/* reexport safe */ _EventStreamSerdeConfig__WEBPACK_IMPORTED_MODULE_0__.resolveEventStreamSerdeConfig)\n/* harmony export */ });\n/* harmony import */ var _EventStreamSerdeConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EventStreamSerdeConfig */ \"(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-config-resolver@4.1.2/node_modules/@smithy/eventstream-serde-config-resolver/dist-es/EventStreamSerdeConfig.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStldmVudHN0cmVhbS1zZXJkZS1jb25maWctcmVzb2x2ZXJANC4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvZXZlbnRzdHJlYW0tc2VyZGUtY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUMiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2V2ZW50c3RyZWFtLXNlcmRlLWNvbmZpZy1yZXNvbHZlckA0LjEuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9ldmVudHN0cmVhbS1zZXJkZS1jb25maWctcmVzb2x2ZXIvZGlzdC1lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9FdmVudFN0cmVhbVNlcmRlQ29uZmlnXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+eventstream-serde-config-resolver@4.1.2/node_modules/@smithy/eventstream-serde-config-resolver/dist-es/index.js\n");

/***/ })

};
;