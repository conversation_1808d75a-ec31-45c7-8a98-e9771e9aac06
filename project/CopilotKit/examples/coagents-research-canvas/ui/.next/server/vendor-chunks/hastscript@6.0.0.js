"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hastscript@6.0.0";
exports.ids = ["vendor-chunks/hastscript@6.0.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/hastscript@6.0.0/node_modules/hastscript/factory.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/hastscript@6.0.0/node_modules/hastscript/factory.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar find = __webpack_require__(/*! property-information/find */ \"(ssr)/./node_modules/.pnpm/property-information@5.6.0/node_modules/property-information/find.js\")\nvar normalize = __webpack_require__(/*! property-information/normalize */ \"(ssr)/./node_modules/.pnpm/property-information@5.6.0/node_modules/property-information/normalize.js\")\nvar parseSelector = __webpack_require__(/*! hast-util-parse-selector */ \"(ssr)/./node_modules/.pnpm/hast-util-parse-selector@2.2.5/node_modules/hast-util-parse-selector/index.js\")\nvar spaces = (__webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/.pnpm/space-separated-tokens@1.1.5/node_modules/space-separated-tokens/index.js\").parse)\nvar commas = (__webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/.pnpm/comma-separated-tokens@1.0.8/node_modules/comma-separated-tokens/index.js\").parse)\n\nmodule.exports = factory\n\nvar own = {}.hasOwnProperty\n\nfunction factory(schema, defaultTagName, caseSensitive) {\n  var adjust = caseSensitive ? createAdjustMap(caseSensitive) : null\n\n  return h\n\n  // Hyperscript compatible DSL for creating virtual hast trees.\n  function h(selector, properties) {\n    var node = parseSelector(selector, defaultTagName)\n    var children = Array.prototype.slice.call(arguments, 2)\n    var name = node.tagName.toLowerCase()\n    var property\n\n    node.tagName = adjust && own.call(adjust, name) ? adjust[name] : name\n\n    if (properties && isChildren(properties, node)) {\n      children.unshift(properties)\n      properties = null\n    }\n\n    if (properties) {\n      for (property in properties) {\n        addProperty(node.properties, property, properties[property])\n      }\n    }\n\n    addChild(node.children, children)\n\n    if (node.tagName === 'template') {\n      node.content = {type: 'root', children: node.children}\n      node.children = []\n    }\n\n    return node\n  }\n\n  function addProperty(properties, key, value) {\n    var info\n    var property\n    var result\n\n    // Ignore nullish and NaN values.\n    if (value === null || value === undefined || value !== value) {\n      return\n    }\n\n    info = find(schema, key)\n    property = info.property\n    result = value\n\n    // Handle list values.\n    if (typeof result === 'string') {\n      if (info.spaceSeparated) {\n        result = spaces(result)\n      } else if (info.commaSeparated) {\n        result = commas(result)\n      } else if (info.commaOrSpaceSeparated) {\n        result = spaces(commas(result).join(' '))\n      }\n    }\n\n    // Accept `object` on style.\n    if (property === 'style' && typeof value !== 'string') {\n      result = style(result)\n    }\n\n    // Class-names (which can be added both on the `selector` and here).\n    if (property === 'className' && properties.className) {\n      result = properties.className.concat(result)\n    }\n\n    properties[property] = parsePrimitives(info, property, result)\n  }\n}\n\nfunction isChildren(value, node) {\n  return (\n    typeof value === 'string' ||\n    'length' in value ||\n    isNode(node.tagName, value)\n  )\n}\n\nfunction isNode(tagName, value) {\n  var type = value.type\n\n  if (tagName === 'input' || !type || typeof type !== 'string') {\n    return false\n  }\n\n  if (typeof value.children === 'object' && 'length' in value.children) {\n    return true\n  }\n\n  type = type.toLowerCase()\n\n  if (tagName === 'button') {\n    return (\n      type !== 'menu' &&\n      type !== 'submit' &&\n      type !== 'reset' &&\n      type !== 'button'\n    )\n  }\n\n  return 'value' in value\n}\n\nfunction addChild(nodes, value) {\n  var index\n  var length\n\n  if (typeof value === 'string' || typeof value === 'number') {\n    nodes.push({type: 'text', value: String(value)})\n    return\n  }\n\n  if (typeof value === 'object' && 'length' in value) {\n    index = -1\n    length = value.length\n\n    while (++index < length) {\n      addChild(nodes, value[index])\n    }\n\n    return\n  }\n\n  if (typeof value !== 'object' || !('type' in value)) {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n\n  nodes.push(value)\n}\n\n// Parse a (list of) primitives.\nfunction parsePrimitives(info, name, value) {\n  var index\n  var length\n  var result\n\n  if (typeof value !== 'object' || !('length' in value)) {\n    return parsePrimitive(info, name, value)\n  }\n\n  length = value.length\n  index = -1\n  result = []\n\n  while (++index < length) {\n    result[index] = parsePrimitive(info, name, value[index])\n  }\n\n  return result\n}\n\n// Parse a single primitives.\nfunction parsePrimitive(info, name, value) {\n  var result = value\n\n  if (info.number || info.positiveNumber) {\n    if (!isNaN(result) && result !== '') {\n      result = Number(result)\n    }\n  } else if (info.boolean || info.overloadedBoolean) {\n    // Accept `boolean` and `string`.\n    if (\n      typeof result === 'string' &&\n      (result === '' || normalize(value) === normalize(name))\n    ) {\n      result = true\n    }\n  }\n\n  return result\n}\n\nfunction style(value) {\n  var result = []\n  var key\n\n  for (key in value) {\n    result.push([key, value[key]].join(': '))\n  }\n\n  return result.join('; ')\n}\n\nfunction createAdjustMap(values) {\n  var length = values.length\n  var index = -1\n  var result = {}\n  var value\n\n  while (++index < length) {\n    value = values[index]\n    result[value.toLowerCase()] = value\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/hastscript@6.0.0/node_modules/hastscript/factory.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/hastscript@6.0.0/node_modules/hastscript/html.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/hastscript@6.0.0/node_modules/hastscript/html.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar schema = __webpack_require__(/*! property-information/html */ \"(ssr)/./node_modules/.pnpm/property-information@5.6.0/node_modules/property-information/html.js\")\nvar factory = __webpack_require__(/*! ./factory */ \"(ssr)/./node_modules/.pnpm/hastscript@6.0.0/node_modules/hastscript/factory.js\")\n\nvar html = factory(schema, 'div')\nhtml.displayName = 'html'\n\nmodule.exports = html\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vaGFzdHNjcmlwdEA2LjAuMC9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9odG1sLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGFBQWEsbUJBQU8sQ0FBQyxrSUFBMkI7QUFDaEQsY0FBYyxtQkFBTyxDQUFDLGlHQUFXOztBQUVqQztBQUNBOztBQUVBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vaGFzdHNjcmlwdEA2LjAuMC9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9odG1sLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgc2NoZW1hID0gcmVxdWlyZSgncHJvcGVydHktaW5mb3JtYXRpb24vaHRtbCcpXG52YXIgZmFjdG9yeSA9IHJlcXVpcmUoJy4vZmFjdG9yeScpXG5cbnZhciBodG1sID0gZmFjdG9yeShzY2hlbWEsICdkaXYnKVxuaHRtbC5kaXNwbGF5TmFtZSA9ICdodG1sJ1xuXG5tb2R1bGUuZXhwb3J0cyA9IGh0bWxcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/hastscript@6.0.0/node_modules/hastscript/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/hastscript@6.0.0/node_modules/hastscript/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/hastscript@6.0.0/node_modules/hastscript/index.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = __webpack_require__(/*! ./html */ \"(ssr)/./node_modules/.pnpm/hastscript@6.0.0/node_modules/hastscript/html.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vaGFzdHNjcmlwdEA2LjAuMC9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixpSUFBa0MiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9oYXN0c2NyaXB0QDYuMC4wL25vZGVfbW9kdWxlcy9oYXN0c2NyaXB0L2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vaHRtbCcpXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/hastscript@6.0.0/node_modules/hastscript/index.js\n");

/***/ })

};
;