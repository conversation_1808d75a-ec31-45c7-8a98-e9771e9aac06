"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@bufbuild+protobuf@2.2.5";
exports.ids = ["vendor-chunks/@bufbuild+protobuf@2.2.5"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   protoInt64: () => (/* binding */ protoInt64)\n/* harmony export */ });\n/* harmony import */ var _wire_varint_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./wire/varint.js */ \"(rsc)/./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/wire/varint.js\");\n// Copyright 2021-2025 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n/**\n * Int64Support for the current environment.\n */\nconst protoInt64 = /*@__PURE__*/ makeInt64Support();\nfunction makeInt64Support() {\n    const dv = new DataView(new ArrayBuffer(8));\n    // note that Safari 14 implements BigInt, but not the DataView methods\n    const ok = typeof BigInt === \"function\" &&\n        typeof dv.getBigInt64 === \"function\" &&\n        typeof dv.getBigUint64 === \"function\" &&\n        typeof dv.setBigInt64 === \"function\" &&\n        typeof dv.setBigUint64 === \"function\" &&\n        (typeof process != \"object\" ||\n            typeof process.env != \"object\" ||\n            process.env.BUF_BIGINT_DISABLE !== \"1\");\n    if (ok) {\n        const MIN = BigInt(\"-9223372036854775808\"), MAX = BigInt(\"9223372036854775807\"), UMIN = BigInt(\"0\"), UMAX = BigInt(\"18446744073709551615\");\n        return {\n            zero: BigInt(0),\n            supported: true,\n            parse(value) {\n                const bi = typeof value == \"bigint\" ? value : BigInt(value);\n                if (bi > MAX || bi < MIN) {\n                    throw new Error(`invalid int64: ${value}`);\n                }\n                return bi;\n            },\n            uParse(value) {\n                const bi = typeof value == \"bigint\" ? value : BigInt(value);\n                if (bi > UMAX || bi < UMIN) {\n                    throw new Error(`invalid uint64: ${value}`);\n                }\n                return bi;\n            },\n            enc(value) {\n                dv.setBigInt64(0, this.parse(value), true);\n                return {\n                    lo: dv.getInt32(0, true),\n                    hi: dv.getInt32(4, true),\n                };\n            },\n            uEnc(value) {\n                dv.setBigInt64(0, this.uParse(value), true);\n                return {\n                    lo: dv.getInt32(0, true),\n                    hi: dv.getInt32(4, true),\n                };\n            },\n            dec(lo, hi) {\n                dv.setInt32(0, lo, true);\n                dv.setInt32(4, hi, true);\n                return dv.getBigInt64(0, true);\n            },\n            uDec(lo, hi) {\n                dv.setInt32(0, lo, true);\n                dv.setInt32(4, hi, true);\n                return dv.getBigUint64(0, true);\n            },\n        };\n    }\n    return {\n        zero: \"0\",\n        supported: false,\n        parse(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertInt64String(value);\n            return value;\n        },\n        uParse(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertUInt64String(value);\n            return value;\n        },\n        enc(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertInt64String(value);\n            return (0,_wire_varint_js__WEBPACK_IMPORTED_MODULE_0__.int64FromString)(value);\n        },\n        uEnc(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertUInt64String(value);\n            return (0,_wire_varint_js__WEBPACK_IMPORTED_MODULE_0__.int64FromString)(value);\n        },\n        dec(lo, hi) {\n            return (0,_wire_varint_js__WEBPACK_IMPORTED_MODULE_0__.int64ToString)(lo, hi);\n        },\n        uDec(lo, hi) {\n            return (0,_wire_varint_js__WEBPACK_IMPORTED_MODULE_0__.uInt64ToString)(lo, hi);\n        },\n    };\n}\nfunction assertInt64String(value) {\n    if (!/^-?[0-9]+$/.test(value)) {\n        throw new Error(\"invalid int64: \" + value);\n    }\n}\nfunction assertUInt64String(value) {\n    if (!/^[0-9]+$/.test(value)) {\n        throw new Error(\"invalid uint64: \" + value);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/wire/binary-encoding.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/wire/binary-encoding.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BinaryReader: () => (/* binding */ BinaryReader),\n/* harmony export */   BinaryWriter: () => (/* binding */ BinaryWriter),\n/* harmony export */   FLOAT32_MAX: () => (/* binding */ FLOAT32_MAX),\n/* harmony export */   FLOAT32_MIN: () => (/* binding */ FLOAT32_MIN),\n/* harmony export */   INT32_MAX: () => (/* binding */ INT32_MAX),\n/* harmony export */   INT32_MIN: () => (/* binding */ INT32_MIN),\n/* harmony export */   UINT32_MAX: () => (/* binding */ UINT32_MAX),\n/* harmony export */   WireType: () => (/* binding */ WireType)\n/* harmony export */ });\n/* harmony import */ var _varint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./varint.js */ \"(rsc)/./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/wire/varint.js\");\n/* harmony import */ var _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../proto-int64.js */ \"(rsc)/./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\");\n/* harmony import */ var _text_encoding_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./text-encoding.js */ \"(rsc)/./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/wire/text-encoding.js\");\n// Copyright 2021-2025 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n/* eslint-disable prefer-const,no-case-declarations,@typescript-eslint/restrict-plus-operands */\n/**\n * Protobuf binary format wire types.\n *\n * A wire type provides just enough information to find the length of the\n * following value.\n *\n * See https://developers.google.com/protocol-buffers/docs/encoding#structure\n */\nvar WireType;\n(function (WireType) {\n    /**\n     * Used for int32, int64, uint32, uint64, sint32, sint64, bool, enum\n     */\n    WireType[WireType[\"Varint\"] = 0] = \"Varint\";\n    /**\n     * Used for fixed64, sfixed64, double.\n     * Always 8 bytes with little-endian byte order.\n     */\n    WireType[WireType[\"Bit64\"] = 1] = \"Bit64\";\n    /**\n     * Used for string, bytes, embedded messages, packed repeated fields\n     *\n     * Only repeated numeric types (types which use the varint, 32-bit,\n     * or 64-bit wire types) can be packed. In proto3, such fields are\n     * packed by default.\n     */\n    WireType[WireType[\"LengthDelimited\"] = 2] = \"LengthDelimited\";\n    /**\n     * Start of a tag-delimited aggregate, such as a proto2 group, or a message\n     * in editions with message_encoding = DELIMITED.\n     */\n    WireType[WireType[\"StartGroup\"] = 3] = \"StartGroup\";\n    /**\n     * End of a tag-delimited aggregate.\n     */\n    WireType[WireType[\"EndGroup\"] = 4] = \"EndGroup\";\n    /**\n     * Used for fixed32, sfixed32, float.\n     * Always 4 bytes with little-endian byte order.\n     */\n    WireType[WireType[\"Bit32\"] = 5] = \"Bit32\";\n})(WireType || (WireType = {}));\n/**\n * Maximum value for a 32-bit floating point value (Protobuf FLOAT).\n */\nconst FLOAT32_MAX = 3.4028234663852886e38;\n/**\n * Minimum value for a 32-bit floating point value (Protobuf FLOAT).\n */\nconst FLOAT32_MIN = -3.4028234663852886e38;\n/**\n * Maximum value for an unsigned 32-bit integer (Protobuf UINT32, FIXED32).\n */\nconst UINT32_MAX = 0xffffffff;\n/**\n * Maximum value for a signed 32-bit integer (Protobuf INT32, SFIXED32, SINT32).\n */\nconst INT32_MAX = 0x7fffffff;\n/**\n * Minimum value for a signed 32-bit integer (Protobuf INT32, SFIXED32, SINT32).\n */\nconst INT32_MIN = -0x80000000;\nclass BinaryWriter {\n    constructor(encodeUtf8 = (0,_text_encoding_js__WEBPACK_IMPORTED_MODULE_0__.getTextEncoding)().encodeUtf8) {\n        this.encodeUtf8 = encodeUtf8;\n        /**\n         * Previous fork states.\n         */\n        this.stack = [];\n        this.chunks = [];\n        this.buf = [];\n    }\n    /**\n     * Return all bytes written and reset this writer.\n     */\n    finish() {\n        if (this.buf.length) {\n            this.chunks.push(new Uint8Array(this.buf)); // flush the buffer\n            this.buf = [];\n        }\n        let len = 0;\n        for (let i = 0; i < this.chunks.length; i++)\n            len += this.chunks[i].length;\n        let bytes = new Uint8Array(len);\n        let offset = 0;\n        for (let i = 0; i < this.chunks.length; i++) {\n            bytes.set(this.chunks[i], offset);\n            offset += this.chunks[i].length;\n        }\n        this.chunks = [];\n        return bytes;\n    }\n    /**\n     * Start a new fork for length-delimited data like a message\n     * or a packed repeated field.\n     *\n     * Must be joined later with `join()`.\n     */\n    fork() {\n        this.stack.push({ chunks: this.chunks, buf: this.buf });\n        this.chunks = [];\n        this.buf = [];\n        return this;\n    }\n    /**\n     * Join the last fork. Write its length and bytes, then\n     * return to the previous state.\n     */\n    join() {\n        // get chunk of fork\n        let chunk = this.finish();\n        // restore previous state\n        let prev = this.stack.pop();\n        if (!prev)\n            throw new Error(\"invalid state, fork stack empty\");\n        this.chunks = prev.chunks;\n        this.buf = prev.buf;\n        // write length of chunk as varint\n        this.uint32(chunk.byteLength);\n        return this.raw(chunk);\n    }\n    /**\n     * Writes a tag (field number and wire type).\n     *\n     * Equivalent to `uint32( (fieldNo << 3 | type) >>> 0 )`.\n     *\n     * Generated code should compute the tag ahead of time and call `uint32()`.\n     */\n    tag(fieldNo, type) {\n        return this.uint32(((fieldNo << 3) | type) >>> 0);\n    }\n    /**\n     * Write a chunk of raw bytes.\n     */\n    raw(chunk) {\n        if (this.buf.length) {\n            this.chunks.push(new Uint8Array(this.buf));\n            this.buf = [];\n        }\n        this.chunks.push(chunk);\n        return this;\n    }\n    /**\n     * Write a `uint32` value, an unsigned 32 bit varint.\n     */\n    uint32(value) {\n        assertUInt32(value);\n        // write value as varint 32, inlined for speed\n        while (value > 0x7f) {\n            this.buf.push((value & 0x7f) | 0x80);\n            value = value >>> 7;\n        }\n        this.buf.push(value);\n        return this;\n    }\n    /**\n     * Write a `int32` value, a signed 32 bit varint.\n     */\n    int32(value) {\n        assertInt32(value);\n        (0,_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint32write)(value, this.buf);\n        return this;\n    }\n    /**\n     * Write a `bool` value, a variant.\n     */\n    bool(value) {\n        this.buf.push(value ? 1 : 0);\n        return this;\n    }\n    /**\n     * Write a `bytes` value, length-delimited arbitrary data.\n     */\n    bytes(value) {\n        this.uint32(value.byteLength); // write length of chunk as varint\n        return this.raw(value);\n    }\n    /**\n     * Write a `string` value, length-delimited data converted to UTF-8 text.\n     */\n    string(value) {\n        let chunk = this.encodeUtf8(value);\n        this.uint32(chunk.byteLength); // write length of chunk as varint\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `float` value, 32-bit floating point number.\n     */\n    float(value) {\n        assertFloat32(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setFloat32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `double` value, a 64-bit floating point number.\n     */\n    double(value) {\n        let chunk = new Uint8Array(8);\n        new DataView(chunk.buffer).setFloat64(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `fixed32` value, an unsigned, fixed-length 32-bit integer.\n     */\n    fixed32(value) {\n        assertUInt32(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setUint32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `sfixed32` value, a signed, fixed-length 32-bit integer.\n     */\n    sfixed32(value) {\n        assertInt32(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setInt32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `sint32` value, a signed, zigzag-encoded 32-bit varint.\n     */\n    sint32(value) {\n        assertInt32(value);\n        // zigzag encode\n        value = ((value << 1) ^ (value >> 31)) >>> 0;\n        (0,_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint32write)(value, this.buf);\n        return this;\n    }\n    /**\n     * Write a `fixed64` value, a signed, fixed-length 64-bit integer.\n     */\n    sfixed64(value) {\n        let chunk = new Uint8Array(8), view = new DataView(chunk.buffer), tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.enc(value);\n        view.setInt32(0, tc.lo, true);\n        view.setInt32(4, tc.hi, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `fixed64` value, an unsigned, fixed-length 64 bit integer.\n     */\n    fixed64(value) {\n        let chunk = new Uint8Array(8), view = new DataView(chunk.buffer), tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uEnc(value);\n        view.setInt32(0, tc.lo, true);\n        view.setInt32(4, tc.hi, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `int64` value, a signed 64-bit varint.\n     */\n    int64(value) {\n        let tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.enc(value);\n        (0,_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64write)(tc.lo, tc.hi, this.buf);\n        return this;\n    }\n    /**\n     * Write a `sint64` value, a signed, zig-zag-encoded 64-bit varint.\n     */\n    sint64(value) {\n        let tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.enc(value), \n        // zigzag encode\n        sign = tc.hi >> 31, lo = (tc.lo << 1) ^ sign, hi = ((tc.hi << 1) | (tc.lo >>> 31)) ^ sign;\n        (0,_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64write)(lo, hi, this.buf);\n        return this;\n    }\n    /**\n     * Write a `uint64` value, an unsigned 64-bit varint.\n     */\n    uint64(value) {\n        let tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uEnc(value);\n        (0,_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64write)(tc.lo, tc.hi, this.buf);\n        return this;\n    }\n}\nclass BinaryReader {\n    constructor(buf, decodeUtf8 = (0,_text_encoding_js__WEBPACK_IMPORTED_MODULE_0__.getTextEncoding)().decodeUtf8) {\n        this.decodeUtf8 = decodeUtf8;\n        this.varint64 = _varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64read; // dirty cast for `this`\n        /**\n         * Read a `uint32` field, an unsigned 32 bit varint.\n         */\n        this.uint32 = _varint_js__WEBPACK_IMPORTED_MODULE_1__.varint32read;\n        this.buf = buf;\n        this.len = buf.length;\n        this.pos = 0;\n        this.view = new DataView(buf.buffer, buf.byteOffset, buf.byteLength);\n    }\n    /**\n     * Reads a tag - field number and wire type.\n     */\n    tag() {\n        let tag = this.uint32(), fieldNo = tag >>> 3, wireType = tag & 7;\n        if (fieldNo <= 0 || wireType < 0 || wireType > 5)\n            throw new Error(\"illegal tag: field no \" + fieldNo + \" wire type \" + wireType);\n        return [fieldNo, wireType];\n    }\n    /**\n     * Skip one element and return the skipped data.\n     *\n     * When skipping StartGroup, provide the tags field number to check for\n     * matching field number in the EndGroup tag.\n     */\n    skip(wireType, fieldNo) {\n        let start = this.pos;\n        switch (wireType) {\n            case WireType.Varint:\n                while (this.buf[this.pos++] & 0x80) {\n                    // ignore\n                }\n                break;\n            // eslint-disable-next-line\n            // @ts-expect-error TS7029: Fallthrough case in switch\n            case WireType.Bit64:\n                this.pos += 4;\n            // eslint-disable-next-line no-fallthrough\n            case WireType.Bit32:\n                this.pos += 4;\n                break;\n            case WireType.LengthDelimited:\n                let len = this.uint32();\n                this.pos += len;\n                break;\n            case WireType.StartGroup:\n                for (;;) {\n                    const [fn, wt] = this.tag();\n                    if (wt === WireType.EndGroup) {\n                        if (fieldNo !== undefined && fn !== fieldNo) {\n                            throw new Error(\"invalid end group tag\");\n                        }\n                        break;\n                    }\n                    this.skip(wt, fn);\n                }\n                break;\n            default:\n                throw new Error(\"cant skip wire type \" + wireType);\n        }\n        this.assertBounds();\n        return this.buf.subarray(start, this.pos);\n    }\n    /**\n     * Throws error if position in byte array is out of range.\n     */\n    assertBounds() {\n        if (this.pos > this.len)\n            throw new RangeError(\"premature EOF\");\n    }\n    /**\n     * Read a `int32` field, a signed 32 bit varint.\n     */\n    int32() {\n        return this.uint32() | 0;\n    }\n    /**\n     * Read a `sint32` field, a signed, zigzag-encoded 32-bit varint.\n     */\n    sint32() {\n        let zze = this.uint32();\n        // decode zigzag\n        return (zze >>> 1) ^ -(zze & 1);\n    }\n    /**\n     * Read a `int64` field, a signed 64-bit varint.\n     */\n    int64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.dec(...this.varint64());\n    }\n    /**\n     * Read a `uint64` field, an unsigned 64-bit varint.\n     */\n    uint64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uDec(...this.varint64());\n    }\n    /**\n     * Read a `sint64` field, a signed, zig-zag-encoded 64-bit varint.\n     */\n    sint64() {\n        let [lo, hi] = this.varint64();\n        // decode zig zag\n        let s = -(lo & 1);\n        lo = ((lo >>> 1) | ((hi & 1) << 31)) ^ s;\n        hi = (hi >>> 1) ^ s;\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.dec(lo, hi);\n    }\n    /**\n     * Read a `bool` field, a variant.\n     */\n    bool() {\n        let [lo, hi] = this.varint64();\n        return lo !== 0 || hi !== 0;\n    }\n    /**\n     * Read a `fixed32` field, an unsigned, fixed-length 32-bit integer.\n     */\n    fixed32() {\n        return this.view.getUint32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `sfixed32` field, a signed, fixed-length 32-bit integer.\n     */\n    sfixed32() {\n        return this.view.getInt32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `fixed64` field, an unsigned, fixed-length 64 bit integer.\n     */\n    fixed64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uDec(this.sfixed32(), this.sfixed32());\n    }\n    /**\n     * Read a `fixed64` field, a signed, fixed-length 64-bit integer.\n     */\n    sfixed64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.dec(this.sfixed32(), this.sfixed32());\n    }\n    /**\n     * Read a `float` field, 32-bit floating point number.\n     */\n    float() {\n        return this.view.getFloat32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `double` field, a 64-bit floating point number.\n     */\n    double() {\n        return this.view.getFloat64((this.pos += 8) - 8, true);\n    }\n    /**\n     * Read a `bytes` field, length-delimited arbitrary data.\n     */\n    bytes() {\n        let len = this.uint32(), start = this.pos;\n        this.pos += len;\n        this.assertBounds();\n        return this.buf.subarray(start, start + len);\n    }\n    /**\n     * Read a `string` field, length-delimited data converted to UTF-8 text.\n     */\n    string() {\n        return this.decodeUtf8(this.bytes());\n    }\n}\n/**\n * Assert a valid signed protobuf 32-bit integer as a number or string.\n */\nfunction assertInt32(arg) {\n    if (typeof arg == \"string\") {\n        arg = Number(arg);\n    }\n    else if (typeof arg != \"number\") {\n        throw new Error(\"invalid int32: \" + typeof arg);\n    }\n    if (!Number.isInteger(arg) ||\n        arg > INT32_MAX ||\n        arg < INT32_MIN)\n        throw new Error(\"invalid int32: \" + arg);\n}\n/**\n * Assert a valid unsigned protobuf 32-bit integer as a number or string.\n */\nfunction assertUInt32(arg) {\n    if (typeof arg == \"string\") {\n        arg = Number(arg);\n    }\n    else if (typeof arg != \"number\") {\n        throw new Error(\"invalid uint32: \" + typeof arg);\n    }\n    if (!Number.isInteger(arg) ||\n        arg > UINT32_MAX ||\n        arg < 0)\n        throw new Error(\"invalid uint32: \" + arg);\n}\n/**\n * Assert a valid protobuf float value as a number or string.\n */\nfunction assertFloat32(arg) {\n    if (typeof arg == \"string\") {\n        const o = arg;\n        arg = Number(arg);\n        if (isNaN(arg) && o !== \"NaN\") {\n            throw new Error(\"invalid float32: \" + o);\n        }\n    }\n    else if (typeof arg != \"number\") {\n        throw new Error(\"invalid float32: \" + typeof arg);\n    }\n    if (Number.isFinite(arg) &&\n        (arg > FLOAT32_MAX || arg < FLOAT32_MIN))\n        throw new Error(\"invalid float32: \" + arg);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/wire/binary-encoding.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/wire/text-encoding.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/wire/text-encoding.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   configureTextEncoding: () => (/* binding */ configureTextEncoding),\n/* harmony export */   getTextEncoding: () => (/* binding */ getTextEncoding)\n/* harmony export */ });\n// Copyright 2021-2025 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nconst symbol = Symbol.for(\"@bufbuild/protobuf/text-encoding\");\n/**\n * Protobuf-ES requires the Text Encoding API to convert UTF-8 from and to\n * binary. This WHATWG API is widely available, but it is not part of the\n * ECMAScript standard. On runtimes where it is not available, use this\n * function to provide your own implementation.\n *\n * Note that the Text Encoding API does not provide a way to validate UTF-8.\n * Our implementation falls back to use encodeURIComponent().\n */\nfunction configureTextEncoding(textEncoding) {\n    globalThis[symbol] = textEncoding;\n}\nfunction getTextEncoding() {\n    if (globalThis[symbol] == undefined) {\n        const te = new globalThis.TextEncoder();\n        const td = new globalThis.TextDecoder();\n        globalThis[symbol] = {\n            encodeUtf8(text) {\n                return te.encode(text);\n            },\n            decodeUtf8(bytes) {\n                return td.decode(bytes);\n            },\n            checkUtf8(text) {\n                try {\n                    encodeURIComponent(text);\n                    return true;\n                }\n                catch (e) {\n                    return false;\n                }\n            },\n        };\n    }\n    return globalThis[symbol];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/wire/text-encoding.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/wire/varint.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/wire/varint.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   int64FromString: () => (/* binding */ int64FromString),\n/* harmony export */   int64ToString: () => (/* binding */ int64ToString),\n/* harmony export */   uInt64ToString: () => (/* binding */ uInt64ToString),\n/* harmony export */   varint32read: () => (/* binding */ varint32read),\n/* harmony export */   varint32write: () => (/* binding */ varint32write),\n/* harmony export */   varint64read: () => (/* binding */ varint64read),\n/* harmony export */   varint64write: () => (/* binding */ varint64write)\n/* harmony export */ });\n// Copyright 2008 Google Inc.  All rights reserved.\n//\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are\n// met:\n//\n// * Redistributions of source code must retain the above copyright\n// notice, this list of conditions and the following disclaimer.\n// * Redistributions in binary form must reproduce the above\n// copyright notice, this list of conditions and the following disclaimer\n// in the documentation and/or other materials provided with the\n// distribution.\n// * Neither the name of Google Inc. nor the names of its\n// contributors may be used to endorse or promote products derived from\n// this software without specific prior written permission.\n//\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n//\n// Code generated by the Protocol Buffer compiler is owned by the owner\n// of the input file used when generating it.  This code is not\n// standalone and requires a support library to be linked with it.  This\n// support library is itself covered by the above license.\n/* eslint-disable prefer-const,@typescript-eslint/restrict-plus-operands */\n/**\n * Read a 64 bit varint as two JS numbers.\n *\n * Returns tuple:\n * [0]: low bits\n * [1]: high bits\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/buffer_decoder.js#L175\n */\nfunction varint64read() {\n    let lowBits = 0;\n    let highBits = 0;\n    for (let shift = 0; shift < 28; shift += 7) {\n        let b = this.buf[this.pos++];\n        lowBits |= (b & 0x7f) << shift;\n        if ((b & 0x80) == 0) {\n            this.assertBounds();\n            return [lowBits, highBits];\n        }\n    }\n    let middleByte = this.buf[this.pos++];\n    // last four bits of the first 32 bit number\n    lowBits |= (middleByte & 0x0f) << 28;\n    // 3 upper bits are part of the next 32 bit number\n    highBits = (middleByte & 0x70) >> 4;\n    if ((middleByte & 0x80) == 0) {\n        this.assertBounds();\n        return [lowBits, highBits];\n    }\n    for (let shift = 3; shift <= 31; shift += 7) {\n        let b = this.buf[this.pos++];\n        highBits |= (b & 0x7f) << shift;\n        if ((b & 0x80) == 0) {\n            this.assertBounds();\n            return [lowBits, highBits];\n        }\n    }\n    throw new Error(\"invalid varint\");\n}\n/**\n * Write a 64 bit varint, given as two JS numbers, to the given bytes array.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/writer.js#L344\n */\nfunction varint64write(lo, hi, bytes) {\n    for (let i = 0; i < 28; i = i + 7) {\n        const shift = lo >>> i;\n        const hasNext = !(shift >>> 7 == 0 && hi == 0);\n        const byte = (hasNext ? shift | 0x80 : shift) & 0xff;\n        bytes.push(byte);\n        if (!hasNext) {\n            return;\n        }\n    }\n    const splitBits = ((lo >>> 28) & 0x0f) | ((hi & 0x07) << 4);\n    const hasMoreBits = !(hi >> 3 == 0);\n    bytes.push((hasMoreBits ? splitBits | 0x80 : splitBits) & 0xff);\n    if (!hasMoreBits) {\n        return;\n    }\n    for (let i = 3; i < 31; i = i + 7) {\n        const shift = hi >>> i;\n        const hasNext = !(shift >>> 7 == 0);\n        const byte = (hasNext ? shift | 0x80 : shift) & 0xff;\n        bytes.push(byte);\n        if (!hasNext) {\n            return;\n        }\n    }\n    bytes.push((hi >>> 31) & 0x01);\n}\n// constants for binary math\nconst TWO_PWR_32_DBL = 0x100000000;\n/**\n * Parse decimal string of 64 bit integer value as two JS numbers.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nfunction int64FromString(dec) {\n    // Check for minus sign.\n    const minus = dec[0] === \"-\";\n    if (minus) {\n        dec = dec.slice(1);\n    }\n    // Work 6 decimal digits at a time, acting like we're converting base 1e6\n    // digits to binary. This is safe to do with floating point math because\n    // Number.isSafeInteger(ALL_32_BITS * 1e6) == true.\n    const base = 1e6;\n    let lowBits = 0;\n    let highBits = 0;\n    function add1e6digit(begin, end) {\n        // Note: Number('') is 0.\n        const digit1e6 = Number(dec.slice(begin, end));\n        highBits *= base;\n        lowBits = lowBits * base + digit1e6;\n        // Carry bits from lowBits to\n        if (lowBits >= TWO_PWR_32_DBL) {\n            highBits = highBits + ((lowBits / TWO_PWR_32_DBL) | 0);\n            lowBits = lowBits % TWO_PWR_32_DBL;\n        }\n    }\n    add1e6digit(-24, -18);\n    add1e6digit(-18, -12);\n    add1e6digit(-12, -6);\n    add1e6digit(-6);\n    return minus ? negate(lowBits, highBits) : newBits(lowBits, highBits);\n}\n/**\n * Losslessly converts a 64-bit signed integer in 32:32 split representation\n * into a decimal string.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nfunction int64ToString(lo, hi) {\n    let bits = newBits(lo, hi);\n    // If we're treating the input as a signed value and the high bit is set, do\n    // a manual two's complement conversion before the decimal conversion.\n    const negative = bits.hi & 0x80000000;\n    if (negative) {\n        bits = negate(bits.lo, bits.hi);\n    }\n    const result = uInt64ToString(bits.lo, bits.hi);\n    return negative ? \"-\" + result : result;\n}\n/**\n * Losslessly converts a 64-bit unsigned integer in 32:32 split representation\n * into a decimal string.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nfunction uInt64ToString(lo, hi) {\n    ({ lo, hi } = toUnsigned(lo, hi));\n    // Skip the expensive conversion if the number is small enough to use the\n    // built-in conversions.\n    // Number.MAX_SAFE_INTEGER = 0x001FFFFF FFFFFFFF, thus any number with\n    // highBits <= 0x1FFFFF can be safely expressed with a double and retain\n    // integer precision.\n    // Proven by: Number.isSafeInteger(0x1FFFFF * 2**32 + 0xFFFFFFFF) == true.\n    if (hi <= 0x1fffff) {\n        return String(TWO_PWR_32_DBL * hi + lo);\n    }\n    // What this code is doing is essentially converting the input number from\n    // base-2 to base-1e7, which allows us to represent the 64-bit range with\n    // only 3 (very large) digits. Those digits are then trivial to convert to\n    // a base-10 string.\n    // The magic numbers used here are -\n    // 2^24 = 16777216 = (1,6777216) in base-1e7.\n    // 2^48 = 281474976710656 = (2,8147497,6710656) in base-1e7.\n    // Split 32:32 representation into 16:24:24 representation so our\n    // intermediate digits don't overflow.\n    const low = lo & 0xffffff;\n    const mid = ((lo >>> 24) | (hi << 8)) & 0xffffff;\n    const high = (hi >> 16) & 0xffff;\n    // Assemble our three base-1e7 digits, ignoring carries. The maximum\n    // value in a digit at this step is representable as a 48-bit integer, which\n    // can be stored in a 64-bit floating point number.\n    let digitA = low + mid * 6777216 + high * 6710656;\n    let digitB = mid + high * 8147497;\n    let digitC = high * 2;\n    // Apply carries from A to B and from B to C.\n    const base = 10000000;\n    if (digitA >= base) {\n        digitB += Math.floor(digitA / base);\n        digitA %= base;\n    }\n    if (digitB >= base) {\n        digitC += Math.floor(digitB / base);\n        digitB %= base;\n    }\n    // If digitC is 0, then we should have returned in the trivial code path\n    // at the top for non-safe integers. Given this, we can assume both digitB\n    // and digitA need leading zeros.\n    return (digitC.toString() +\n        decimalFrom1e7WithLeadingZeros(digitB) +\n        decimalFrom1e7WithLeadingZeros(digitA));\n}\nfunction toUnsigned(lo, hi) {\n    return { lo: lo >>> 0, hi: hi >>> 0 };\n}\nfunction newBits(lo, hi) {\n    return { lo: lo | 0, hi: hi | 0 };\n}\n/**\n * Returns two's compliment negation of input.\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_Operators#Signed_32-bit_integers\n */\nfunction negate(lowBits, highBits) {\n    highBits = ~highBits;\n    if (lowBits) {\n        lowBits = ~lowBits + 1;\n    }\n    else {\n        // If lowBits is 0, then bitwise-not is 0xFFFFFFFF,\n        // adding 1 to that, results in 0x100000000, which leaves\n        // the low bits 0x0 and simply adds one to the high bits.\n        highBits += 1;\n    }\n    return newBits(lowBits, highBits);\n}\n/**\n * Returns decimal representation of digit1e7 with leading zeros.\n */\nconst decimalFrom1e7WithLeadingZeros = (digit1e7) => {\n    const partial = String(digit1e7);\n    return \"0000000\".slice(partial.length) + partial;\n};\n/**\n * Write a 32 bit varint, signed or unsigned. Same as `varint64write(0, value, bytes)`\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/1b18833f4f2a2f681f4e4a25cdf3b0a43115ec26/js/binary/encoder.js#L144\n */\nfunction varint32write(value, bytes) {\n    if (value >= 0) {\n        // write value as varint 32\n        while (value > 0x7f) {\n            bytes.push((value & 0x7f) | 0x80);\n            value = value >>> 7;\n        }\n        bytes.push(value);\n    }\n    else {\n        for (let i = 0; i < 9; i++) {\n            bytes.push((value & 127) | 128);\n            value = value >> 7;\n        }\n        bytes.push(1);\n    }\n}\n/**\n * Read an unsigned 32 bit varint.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/buffer_decoder.js#L220\n */\nfunction varint32read() {\n    let b = this.buf[this.pos++];\n    let result = b & 0x7f;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 7;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 14;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 21;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    // Extract only last 4 bits\n    b = this.buf[this.pos++];\n    result |= (b & 0x0f) << 28;\n    for (let readBytes = 5; (b & 0x80) !== 0 && readBytes < 10; readBytes++)\n        b = this.buf[this.pos++];\n    if ((b & 0x80) != 0)\n        throw new Error(\"invalid varint\");\n    this.assertBounds();\n    // Result can have 32 bits, convert it to unsigned\n    return result >>> 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@bufbuild+protobuf@2.2.5/node_modules/@bufbuild/protobuf/dist/esm/wire/varint.js\n");

/***/ })

};
;