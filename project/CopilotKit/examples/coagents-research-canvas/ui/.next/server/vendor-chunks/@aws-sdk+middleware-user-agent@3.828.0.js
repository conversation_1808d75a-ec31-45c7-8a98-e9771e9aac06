"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+middleware-user-agent@3.828.0";
exports.ids = ["vendor-chunks/@aws-sdk+middleware-user-agent@3.828.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/check-features.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/check-features.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkFeatures: () => (/* binding */ checkFeatures)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setFeature.js\");\n\nconst ACCOUNT_ID_ENDPOINT_REGEX = /\\d{12}\\.ddb/;\nasync function checkFeatures(context, config, args) {\n    const request = args.request;\n    if (request?.headers?.[\"smithy-protocol\"] === \"rpc-v2-cbor\") {\n        (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_0__.setFeature)(context, \"PROTOCOL_RPC_V2_CBOR\", \"M\");\n    }\n    if (typeof config.retryStrategy === \"function\") {\n        const retryStrategy = await config.retryStrategy();\n        if (typeof retryStrategy.acquireInitialRetryToken === \"function\") {\n            if (retryStrategy.constructor?.name?.includes(\"Adaptive\")) {\n                (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_0__.setFeature)(context, \"RETRY_MODE_ADAPTIVE\", \"F\");\n            }\n            else {\n                (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_0__.setFeature)(context, \"RETRY_MODE_STANDARD\", \"E\");\n            }\n        }\n        else {\n            (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_0__.setFeature)(context, \"RETRY_MODE_LEGACY\", \"D\");\n        }\n    }\n    if (typeof config.accountIdEndpointMode === \"function\") {\n        const endpointV2 = context.endpointV2;\n        if (String(endpointV2?.url?.hostname).match(ACCOUNT_ID_ENDPOINT_REGEX)) {\n            (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_0__.setFeature)(context, \"ACCOUNT_ID_ENDPOINT\", \"O\");\n        }\n        switch (await config.accountIdEndpointMode?.()) {\n            case \"disabled\":\n                (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_0__.setFeature)(context, \"ACCOUNT_ID_MODE_DISABLED\", \"Q\");\n                break;\n            case \"preferred\":\n                (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_0__.setFeature)(context, \"ACCOUNT_ID_MODE_PREFERRED\", \"P\");\n                break;\n            case \"required\":\n                (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_0__.setFeature)(context, \"ACCOUNT_ID_MODE_REQUIRED\", \"R\");\n                break;\n        }\n    }\n    const identity = context.__smithy_context?.selectedHttpAuthScheme?.identity;\n    if (identity?.$source) {\n        const credentials = identity;\n        if (credentials.accountId) {\n            (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_0__.setFeature)(context, \"RESOLVED_ACCOUNT_ID\", \"T\");\n        }\n        for (const [key, value] of Object.entries(credentials.$source ?? {})) {\n            (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_0__.setFeature)(context, key, value);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/check-features.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/configurations.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/configurations.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_UA_APP_ID: () => (/* binding */ DEFAULT_UA_APP_ID),\n/* harmony export */   resolveUserAgentConfig: () => (/* binding */ resolveUserAgentConfig)\n/* harmony export */ });\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n\nconst DEFAULT_UA_APP_ID = undefined;\nfunction isValidUserAgentAppId(appId) {\n    if (appId === undefined) {\n        return true;\n    }\n    return typeof appId === \"string\" && appId.length <= 50;\n}\nfunction resolveUserAgentConfig(input) {\n    const normalizedAppIdProvider = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(input.userAgentAppId ?? DEFAULT_UA_APP_ID);\n    const { customUserAgent } = input;\n    return Object.assign(input, {\n        customUserAgent: typeof customUserAgent === \"string\" ? [[customUserAgent]] : customUserAgent,\n        userAgentAppId: async () => {\n            const appId = await normalizedAppIdProvider();\n            if (!isValidUserAgentAppId(appId)) {\n                const logger = input.logger?.constructor?.name === \"NoOpLogger\" || !input.logger ? console : input.logger;\n                if (typeof appId !== \"string\") {\n                    logger?.warn(\"userAgentAppId must be a string or undefined.\");\n                }\n                else if (appId.length > 50) {\n                    logger?.warn(\"The provided userAgentAppId exceeds the maximum length of 50 characters.\");\n                }\n            }\n            return appId;\n        },\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/configurations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/constants.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/constants.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SPACE: () => (/* binding */ SPACE),\n/* harmony export */   UA_ESCAPE_CHAR: () => (/* binding */ UA_ESCAPE_CHAR),\n/* harmony export */   UA_NAME_ESCAPE_REGEX: () => (/* binding */ UA_NAME_ESCAPE_REGEX),\n/* harmony export */   UA_NAME_SEPARATOR: () => (/* binding */ UA_NAME_SEPARATOR),\n/* harmony export */   UA_VALUE_ESCAPE_REGEX: () => (/* binding */ UA_VALUE_ESCAPE_REGEX),\n/* harmony export */   USER_AGENT: () => (/* binding */ USER_AGENT),\n/* harmony export */   X_AMZ_USER_AGENT: () => (/* binding */ X_AMZ_USER_AGENT)\n/* harmony export */ });\nconst USER_AGENT = \"user-agent\";\nconst X_AMZ_USER_AGENT = \"x-amz-user-agent\";\nconst SPACE = \" \";\nconst UA_NAME_SEPARATOR = \"/\";\nconst UA_NAME_ESCAPE_REGEX = /[^\\!\\$\\%\\&\\'\\*\\+\\-\\.\\^\\_\\`\\|\\~\\d\\w]/g;\nconst UA_VALUE_ESCAPE_REGEX = /[^\\!\\$\\%\\&\\'\\*\\+\\-\\.\\^\\_\\`\\|\\~\\d\\w\\#]/g;\nconst UA_ESCAPE_CHAR = \"-\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS11c2VyLWFnZW50QDMuODI4LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtdXNlci1hZ2VudC9kaXN0LWVzL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLXVzZXItYWdlbnRAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS11c2VyLWFnZW50L2Rpc3QtZXMvY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBVU0VSX0FHRU5UID0gXCJ1c2VyLWFnZW50XCI7XG5leHBvcnQgY29uc3QgWF9BTVpfVVNFUl9BR0VOVCA9IFwieC1hbXotdXNlci1hZ2VudFwiO1xuZXhwb3J0IGNvbnN0IFNQQUNFID0gXCIgXCI7XG5leHBvcnQgY29uc3QgVUFfTkFNRV9TRVBBUkFUT1IgPSBcIi9cIjtcbmV4cG9ydCBjb25zdCBVQV9OQU1FX0VTQ0FQRV9SRUdFWCA9IC9bXlxcIVxcJFxcJVxcJlxcJ1xcKlxcK1xcLVxcLlxcXlxcX1xcYFxcfFxcflxcZFxcd10vZztcbmV4cG9ydCBjb25zdCBVQV9WQUxVRV9FU0NBUEVfUkVHRVggPSAvW15cXCFcXCRcXCVcXCZcXCdcXCpcXCtcXC1cXC5cXF5cXF9cXGBcXHxcXH5cXGRcXHdcXCNdL2c7XG5leHBvcnQgY29uc3QgVUFfRVNDQVBFX0NIQVIgPSBcIi1cIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/encode-features.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/encode-features.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodeFeatures: () => (/* binding */ encodeFeatures)\n/* harmony export */ });\nconst BYTE_LIMIT = 1024;\nfunction encodeFeatures(features) {\n    let buffer = \"\";\n    for (const key in features) {\n        const val = features[key];\n        if (buffer.length + val.length + 1 <= BYTE_LIMIT) {\n            if (buffer.length) {\n                buffer += \",\" + val;\n            }\n            else {\n                buffer += val;\n            }\n            continue;\n        }\n        break;\n    }\n    return buffer;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS11c2VyLWFnZW50QDMuODI4LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtdXNlci1hZ2VudC9kaXN0LWVzL2VuY29kZS1mZWF0dXJlcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS11c2VyLWFnZW50QDMuODI4LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtdXNlci1hZ2VudC9kaXN0LWVzL2VuY29kZS1mZWF0dXJlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBCWVRFX0xJTUlUID0gMTAyNDtcbmV4cG9ydCBmdW5jdGlvbiBlbmNvZGVGZWF0dXJlcyhmZWF0dXJlcykge1xuICAgIGxldCBidWZmZXIgPSBcIlwiO1xuICAgIGZvciAoY29uc3Qga2V5IGluIGZlYXR1cmVzKSB7XG4gICAgICAgIGNvbnN0IHZhbCA9IGZlYXR1cmVzW2tleV07XG4gICAgICAgIGlmIChidWZmZXIubGVuZ3RoICsgdmFsLmxlbmd0aCArIDEgPD0gQllURV9MSU1JVCkge1xuICAgICAgICAgICAgaWYgKGJ1ZmZlci5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICBidWZmZXIgKz0gXCIsXCIgKyB2YWw7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBidWZmZXIgKz0gdmFsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgYnJlYWs7XG4gICAgfVxuICAgIHJldHVybiBidWZmZXI7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/encode-features.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/index.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/index.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_UA_APP_ID: () => (/* reexport safe */ _configurations__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_UA_APP_ID),\n/* harmony export */   getUserAgentMiddlewareOptions: () => (/* reexport safe */ _user_agent_middleware__WEBPACK_IMPORTED_MODULE_1__.getUserAgentMiddlewareOptions),\n/* harmony export */   getUserAgentPlugin: () => (/* reexport safe */ _user_agent_middleware__WEBPACK_IMPORTED_MODULE_1__.getUserAgentPlugin),\n/* harmony export */   resolveUserAgentConfig: () => (/* reexport safe */ _configurations__WEBPACK_IMPORTED_MODULE_0__.resolveUserAgentConfig),\n/* harmony export */   userAgentMiddleware: () => (/* reexport safe */ _user_agent_middleware__WEBPACK_IMPORTED_MODULE_1__.userAgentMiddleware)\n/* harmony export */ });\n/* harmony import */ var _configurations__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./configurations */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/configurations.js\");\n/* harmony import */ var _user_agent_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./user-agent-middleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/user-agent-middleware.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS11c2VyLWFnZW50QDMuODI4LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtdXNlci1hZ2VudC9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBaUM7QUFDTyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK21pZGRsZXdhcmUtdXNlci1hZ2VudEAzLjgyOC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLXVzZXItYWdlbnQvZGlzdC1lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9jb25maWd1cmF0aW9uc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdXNlci1hZ2VudC1taWRkbGV3YXJlXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/user-agent-middleware.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/user-agent-middleware.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUserAgentMiddlewareOptions: () => (/* binding */ getUserAgentMiddlewareOptions),\n/* harmony export */   getUserAgentPlugin: () => (/* binding */ getUserAgentPlugin),\n/* harmony export */   userAgentMiddleware: () => (/* binding */ userAgentMiddleware)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/index.js\");\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _check_features__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./check-features */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/check-features.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/constants.js\");\n/* harmony import */ var _encode_features__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./encode-features */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/encode-features.js\");\n\n\n\n\n\nconst userAgentMiddleware = (options) => (next, context) => async (args) => {\n    const { request } = args;\n    if (!_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__.HttpRequest.isInstance(request)) {\n        return next(args);\n    }\n    const { headers } = request;\n    const userAgent = context?.userAgent?.map(escapeUserAgent) || [];\n    const defaultUserAgent = (await options.defaultUserAgentProvider()).map(escapeUserAgent);\n    await (0,_check_features__WEBPACK_IMPORTED_MODULE_2__.checkFeatures)(context, options, args);\n    const awsContext = context;\n    defaultUserAgent.push(`m/${(0,_encode_features__WEBPACK_IMPORTED_MODULE_4__.encodeFeatures)(Object.assign({}, context.__smithy_context?.features, awsContext.__aws_sdk_context?.features))}`);\n    const customUserAgent = options?.customUserAgent?.map(escapeUserAgent) || [];\n    const appId = await options.userAgentAppId();\n    if (appId) {\n        defaultUserAgent.push(escapeUserAgent([`app/${appId}`]));\n    }\n    const prefix = (0,_aws_sdk_util_endpoints__WEBPACK_IMPORTED_MODULE_0__.getUserAgentPrefix)();\n    const sdkUserAgentValue = (prefix ? [prefix] : [])\n        .concat([...defaultUserAgent, ...userAgent, ...customUserAgent])\n        .join(_constants__WEBPACK_IMPORTED_MODULE_3__.SPACE);\n    const normalUAValue = [\n        ...defaultUserAgent.filter((section) => section.startsWith(\"aws-sdk-\")),\n        ...customUserAgent,\n    ].join(_constants__WEBPACK_IMPORTED_MODULE_3__.SPACE);\n    if (options.runtime !== \"browser\") {\n        if (normalUAValue) {\n            headers[_constants__WEBPACK_IMPORTED_MODULE_3__.X_AMZ_USER_AGENT] = headers[_constants__WEBPACK_IMPORTED_MODULE_3__.X_AMZ_USER_AGENT]\n                ? `${headers[_constants__WEBPACK_IMPORTED_MODULE_3__.USER_AGENT]} ${normalUAValue}`\n                : normalUAValue;\n        }\n        headers[_constants__WEBPACK_IMPORTED_MODULE_3__.USER_AGENT] = sdkUserAgentValue;\n    }\n    else {\n        headers[_constants__WEBPACK_IMPORTED_MODULE_3__.X_AMZ_USER_AGENT] = sdkUserAgentValue;\n    }\n    return next({\n        ...args,\n        request,\n    });\n};\nconst escapeUserAgent = (userAgentPair) => {\n    const name = userAgentPair[0]\n        .split(_constants__WEBPACK_IMPORTED_MODULE_3__.UA_NAME_SEPARATOR)\n        .map((part) => part.replace(_constants__WEBPACK_IMPORTED_MODULE_3__.UA_NAME_ESCAPE_REGEX, _constants__WEBPACK_IMPORTED_MODULE_3__.UA_ESCAPE_CHAR))\n        .join(_constants__WEBPACK_IMPORTED_MODULE_3__.UA_NAME_SEPARATOR);\n    const version = userAgentPair[1]?.replace(_constants__WEBPACK_IMPORTED_MODULE_3__.UA_VALUE_ESCAPE_REGEX, _constants__WEBPACK_IMPORTED_MODULE_3__.UA_ESCAPE_CHAR);\n    const prefixSeparatorIndex = name.indexOf(_constants__WEBPACK_IMPORTED_MODULE_3__.UA_NAME_SEPARATOR);\n    const prefix = name.substring(0, prefixSeparatorIndex);\n    let uaName = name.substring(prefixSeparatorIndex + 1);\n    if (prefix === \"api\") {\n        uaName = uaName.toLowerCase();\n    }\n    return [prefix, uaName, version]\n        .filter((item) => item && item.length > 0)\n        .reduce((acc, item, index) => {\n        switch (index) {\n            case 0:\n                return item;\n            case 1:\n                return `${acc}/${item}`;\n            default:\n                return `${acc}#${item}`;\n        }\n    }, \"\");\n};\nconst getUserAgentMiddlewareOptions = {\n    name: \"getUserAgentMiddleware\",\n    step: \"build\",\n    priority: \"low\",\n    tags: [\"SET_USER_AGENT\", \"USER_AGENT\"],\n    override: true,\n};\nconst getUserAgentPlugin = (config) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(userAgentMiddleware(config), getUserAgentMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/user-agent-middleware.js\n");

/***/ })

};
;