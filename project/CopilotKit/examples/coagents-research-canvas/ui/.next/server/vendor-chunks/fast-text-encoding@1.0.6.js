/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-text-encoding@1.0.6";
exports.ids = ["vendor-chunks/fast-text-encoding@1.0.6"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/fast-text-encoding@1.0.6/node_modules/fast-text-encoding/text.min.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/fast-text-encoding@1.0.6/node_modules/fast-text-encoding/text.min.js ***!
  \*************************************************************************************************/
/***/ (function() {

eval("(function(scope) {'use strict';\nfunction B(r,e){var f;return r instanceof Buffer?f=r:f=Buffer.from(r.buffer,r.byteOffset,r.byteLength),f.toString(e)}var w=function(r){return Buffer.from(r)};function h(r){for(var e=0,f=Math.min(256*256,r.length+1),n=new Uint16Array(f),i=[],o=0;;){var t=e<r.length;if(!t||o>=f-1){var s=n.subarray(0,o),m=s;if(i.push(String.fromCharCode.apply(null,m)),!t)return i.join(\"\");r=r.subarray(e),e=0,o=0}var a=r[e++];if((a&128)===0)n[o++]=a;else if((a&224)===192){var d=r[e++]&63;n[o++]=(a&31)<<6|d}else if((a&240)===224){var d=r[e++]&63,l=r[e++]&63;n[o++]=(a&31)<<12|d<<6|l}else if((a&248)===240){var d=r[e++]&63,l=r[e++]&63,R=r[e++]&63,c=(a&7)<<18|d<<12|l<<6|R;c>65535&&(c-=65536,n[o++]=c>>>10&1023|55296,c=56320|c&1023),n[o++]=c}}}function F(r){for(var e=0,f=r.length,n=0,i=Math.max(32,f+(f>>>1)+7),o=new Uint8Array(i>>>3<<3);e<f;){var t=r.charCodeAt(e++);if(t>=55296&&t<=56319){if(e<f){var s=r.charCodeAt(e);(s&64512)===56320&&(++e,t=((t&1023)<<10)+(s&1023)+65536)}if(t>=55296&&t<=56319)continue}if(n+4>o.length){i+=8,i*=1+e/r.length*2,i=i>>>3<<3;var m=new Uint8Array(i);m.set(o),o=m}if((t&4294967168)===0){o[n++]=t;continue}else if((t&4294965248)===0)o[n++]=t>>>6&31|192;else if((t&4294901760)===0)o[n++]=t>>>12&15|224,o[n++]=t>>>6&63|128;else if((t&4292870144)===0)o[n++]=t>>>18&7|240,o[n++]=t>>>12&63|128,o[n++]=t>>>6&63|128;else continue;o[n++]=t&63|128}return o.slice?o.slice(0,n):o.subarray(0,n)}var u=\"Failed to \",p=function(r,e,f){if(r)throw new Error(\"\".concat(u).concat(e,\": the '\").concat(f,\"' option is unsupported.\"))};var x=typeof Buffer==\"function\"&&Buffer.from;var A=x?w:F;function v(){this.encoding=\"utf-8\"}v.prototype.encode=function(r,e){return p(e&&e.stream,\"encode\",\"stream\"),A(r)};function U(r){var e;try{var f=new Blob([r],{type:\"text/plain;charset=UTF-8\"});e=URL.createObjectURL(f);var n=new XMLHttpRequest;return n.open(\"GET\",e,!1),n.send(),n.responseText}finally{e&&URL.revokeObjectURL(e)}}var O=!x&&typeof Blob==\"function\"&&typeof URL==\"function\"&&typeof URL.createObjectURL==\"function\",S=[\"utf-8\",\"utf8\",\"unicode-1-1-utf-8\"],T=h;x?T=B:O&&(T=function(r){try{return U(r)}catch(e){return h(r)}});var y=\"construct 'TextDecoder'\",E=\"\".concat(u,\" \").concat(y,\": the \");function g(r,e){p(e&&e.fatal,y,\"fatal\"),r=r||\"utf-8\";var f;if(x?f=Buffer.isEncoding(r):f=S.indexOf(r.toLowerCase())!==-1,!f)throw new RangeError(\"\".concat(E,\" encoding label provided ('\").concat(r,\"') is invalid.\"));this.encoding=r,this.fatal=!1,this.ignoreBOM=!1}g.prototype.decode=function(r,e){p(e&&e.stream,\"decode\",\"stream\");var f;return r instanceof Uint8Array?f=r:r.buffer instanceof ArrayBuffer?f=new Uint8Array(r.buffer):f=new Uint8Array(r),T(f,this.encoding)};scope.TextEncoder=scope.TextEncoder||v;scope.TextDecoder=scope.TextDecoder||g;\n}(typeof window !== 'undefined' ? window : (typeof global !== 'undefined' ? global : this)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/fast-text-encoding@1.0.6/node_modules/fast-text-encoding/text.min.js\n");

/***/ })

};
;