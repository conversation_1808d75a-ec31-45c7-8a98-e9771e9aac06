/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/process@0.11.10";
exports.ids = ["vendor-chunks/process@0.11.10"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/process@0.11.10/node_modules/process/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/process@0.11.10/node_modules/process/index.js ***!
  \**************************************************************************/
/***/ ((module) => {

eval("// for now just expose the builtin process global from node.js\nmodule.exports = global.process;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vcHJvY2Vzc0AwLjExLjEwL25vZGVfbW9kdWxlcy9wcm9jZXNzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9wcm9jZXNzQDAuMTEuMTAvbm9kZV9tb2R1bGVzL3Byb2Nlc3MvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZm9yIG5vdyBqdXN0IGV4cG9zZSB0aGUgYnVpbHRpbiBwcm9jZXNzIGdsb2JhbCBmcm9tIG5vZGUuanNcbm1vZHVsZS5leHBvcnRzID0gZ2xvYmFsLnByb2Nlc3M7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/process@0.11.10/node_modules/process/index.js\n");

/***/ })

};
;