"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+credential-provider-web-identity@3.830.0";
exports.ids = ["vendor-chunks/@aws-sdk+credential-provider-web-identity@3.830.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/fromTokenFile.js":
/*!**************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/fromTokenFile.js ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromTokenFile: () => (/* binding */ fromTokenFile)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @aws-sdk/core/client */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\");\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fromWebToken__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./fromWebToken */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/fromWebToken.js\");\n\n\n\n\nconst ENV_TOKEN_FILE = \"AWS_WEB_IDENTITY_TOKEN_FILE\";\nconst ENV_ROLE_ARN = \"AWS_ROLE_ARN\";\nconst ENV_ROLE_SESSION_NAME = \"AWS_ROLE_SESSION_NAME\";\nconst fromTokenFile = (init = {}) => async () => {\n    init.logger?.debug(\"@aws-sdk/credential-provider-web-identity - fromTokenFile\");\n    const webIdentityTokenFile = init?.webIdentityTokenFile ?? process.env[ENV_TOKEN_FILE];\n    const roleArn = init?.roleArn ?? process.env[ENV_ROLE_ARN];\n    const roleSessionName = init?.roleSessionName ?? process.env[ENV_ROLE_SESSION_NAME];\n    if (!webIdentityTokenFile || !roleArn) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(\"Web identity configuration not specified\", {\n            logger: init.logger,\n        });\n    }\n    const credentials = await (0,_fromWebToken__WEBPACK_IMPORTED_MODULE_2__.fromWebToken)({\n        ...init,\n        webIdentityToken: (0,fs__WEBPACK_IMPORTED_MODULE_1__.readFileSync)(webIdentityTokenFile, { encoding: \"ascii\" }),\n        roleArn,\n        roleSessionName,\n    })();\n    if (webIdentityTokenFile === process.env[ENV_TOKEN_FILE]) {\n        (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_3__.setCredentialFeature)(credentials, \"CREDENTIALS_ENV_VARS_STS_WEB_ID_TOKEN\", \"h\");\n    }\n    return credentials;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/fromTokenFile.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/fromWebToken.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/fromWebToken.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromWebToken: () => (/* binding */ fromWebToken)\n/* harmony export */ });\nconst fromWebToken = (init) => async (awsIdentityProperties) => {\n    init.logger?.debug(\"@aws-sdk/credential-provider-web-identity - fromWebToken\");\n    const { roleArn, roleSessionName, webIdentityToken, providerId, policyArns, policy, durationSeconds } = init;\n    let { roleAssumerWithWebIdentity } = init;\n    if (!roleAssumerWithWebIdentity) {\n        const { getDefaultRoleAssumerWithWebIdentity } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@aws-sdk+nested-clients@3.830.0\"), __webpack_require__.e(\"vendor-chunks/@aws-sdk+core@3.826.0\"), __webpack_require__.e(\"vendor-chunks/fast-xml-parser@4.4.1\"), __webpack_require__.e(\"vendor-chunks/strnum@1.1.2\")]).then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/nested-clients/sts */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/index.js\"));\n        roleAssumerWithWebIdentity = getDefaultRoleAssumerWithWebIdentity({\n            ...init.clientConfig,\n            credentialProviderLogger: init.logger,\n            parentClientConfig: {\n                ...awsIdentityProperties?.callerClientConfig,\n                ...init.parentClientConfig,\n            },\n        }, init.clientPlugins);\n    }\n    return roleAssumerWithWebIdentity({\n        RoleArn: roleArn,\n        RoleSessionName: roleSessionName ?? `aws-sdk-js-session-${Date.now()}`,\n        WebIdentityToken: webIdentityToken,\n        ProviderId: providerId,\n        PolicyArns: policyArns,\n        Policy: policy,\n        DurationSeconds: durationSeconds,\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/fromWebToken.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/index.js":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/index.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromTokenFile: () => (/* reexport safe */ _fromTokenFile__WEBPACK_IMPORTED_MODULE_0__.fromTokenFile),\n/* harmony export */   fromWebToken: () => (/* reexport safe */ _fromWebToken__WEBPACK_IMPORTED_MODULE_1__.fromWebToken)\n/* harmony export */ });\n/* harmony import */ var _fromTokenFile__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fromTokenFile */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/fromTokenFile.js\");\n/* harmony import */ var _fromWebToken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./fromWebToken */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/fromWebToken.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci13ZWItaWRlbnRpdHlAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci13ZWItaWRlbnRpdHkvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdDO0FBQ0QiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytjcmVkZW50aWFsLXByb3ZpZGVyLXdlYi1pZGVudGl0eUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jcmVkZW50aWFsLXByb3ZpZGVyLXdlYi1pZGVudGl0eS9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2Zyb21Ub2tlbkZpbGVcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2Zyb21XZWJUb2tlblwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/index.js\n");

/***/ })

};
;