"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0";
exports.ids = ["vendor-chunks/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-4KTMZMM2.mjs":
/*!************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-4KTMZMM2.mjs ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   graphql: () => (/* binding */ graphql)\n/* harmony export */ });\n/* harmony import */ var _chunk_WM3ARNBD_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-WM3ARNBD.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-WM3ARNBD.mjs\");\n\n\n// src/graphql/@generated/gql.ts\nvar documents = {\n  \"\\n  mutation generateCopilotResponse($data: GenerateCopilotResponseInput!, $properties: JSONObject) {\\n    generateCopilotResponse(data: $data, properties: $properties) {\\n      threadId\\n      runId\\n      extensions {\\n        openaiAssistantAPI {\\n          runId\\n          threadId\\n        }\\n      }\\n      ... on CopilotResponse @defer {\\n        status {\\n          ... on BaseResponseStatus {\\n            code\\n          }\\n          ... on FailedResponseStatus {\\n            reason\\n            details\\n          }\\n        }\\n      }\\n      messages @stream {\\n        __typename\\n        ... on BaseMessageOutput {\\n          id\\n          createdAt\\n        }\\n        ... on BaseMessageOutput @defer {\\n          status {\\n            ... on SuccessMessageStatus {\\n              code\\n            }\\n            ... on FailedMessageStatus {\\n              code\\n              reason\\n            }\\n            ... on PendingMessageStatus {\\n              code\\n            }\\n          }\\n        }\\n        ... on TextMessageOutput {\\n          content @stream\\n          role\\n          parentMessageId\\n        }\\n        ... on ImageMessageOutput {\\n          format\\n          bytes\\n          role\\n          parentMessageId\\n        }\\n        ... on ActionExecutionMessageOutput {\\n          name\\n          arguments @stream\\n          parentMessageId\\n        }\\n        ... on ResultMessageOutput {\\n          result\\n          actionExecutionId\\n          actionName\\n        }\\n        ... on AgentStateMessageOutput {\\n          threadId\\n          state\\n          running\\n          agentName\\n          nodeName\\n          runId\\n          active\\n          role\\n        }\\n      }\\n      metaEvents @stream {\\n        ... on LangGraphInterruptEvent {\\n          type\\n          name\\n          value\\n        }\\n\\n        ... on CopilotKitLangGraphInterruptEvent {\\n          type\\n          name\\n          data {\\n            messages {\\n              __typename\\n              ... on BaseMessageOutput {\\n                id\\n                createdAt\\n              }\\n              ... on BaseMessageOutput @defer {\\n                status {\\n                  ... on SuccessMessageStatus {\\n                    code\\n                  }\\n                  ... on FailedMessageStatus {\\n                    code\\n                    reason\\n                  }\\n                  ... on PendingMessageStatus {\\n                    code\\n                  }\\n                }\\n              }\\n              ... on TextMessageOutput {\\n                content\\n                role\\n                parentMessageId\\n              }\\n              ... on ActionExecutionMessageOutput {\\n                name\\n                arguments\\n                parentMessageId\\n              }\\n              ... on ResultMessageOutput {\\n                result\\n                actionExecutionId\\n                actionName\\n              }\\n            }\\n            value\\n          }\\n        }\\n      }\\n    }\\n  }\\n\": _chunk_WM3ARNBD_mjs__WEBPACK_IMPORTED_MODULE_0__.GenerateCopilotResponseDocument,\n  \"\\n  query availableAgents {\\n    availableAgents {\\n      agents {\\n        name\\n        id\\n        description\\n      }\\n    }\\n  }\\n\": _chunk_WM3ARNBD_mjs__WEBPACK_IMPORTED_MODULE_0__.AvailableAgentsDocument,\n  \"\\n  query loadAgentState($data: LoadAgentStateInput!) {\\n    loadAgentState(data: $data) {\\n      threadId\\n      threadExists\\n      state\\n      messages\\n    }\\n  }\\n\": _chunk_WM3ARNBD_mjs__WEBPACK_IMPORTED_MODULE_0__.LoadAgentStateDocument\n};\nfunction graphql(source) {\n  return documents[source] ?? {};\n}\n\n\n//# sourceMappingURL=chunk-4KTMZMM2.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-4KTMZMM2.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-EI72UE2B.mjs":
/*!************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-EI72UE2B.mjs ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CopilotRuntimeClient: () => (/* binding */ CopilotRuntimeClient)\n/* harmony export */ });\n/* harmony import */ var _chunk_HEODM5TW_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-HEODM5TW.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-HEODM5TW.mjs\");\n/* harmony import */ var _chunk_X2UAP3QY_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-X2UAP3QY.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-X2UAP3QY.mjs\");\n/* harmony import */ var _urql_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @urql/core */ \"(ssr)/./node_modules/.pnpm/@urql+core@5.0.6_graphql@16.9.0/node_modules/@urql/core/dist/urql-core.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs\");\n\n\n\n// src/client/CopilotRuntimeClient.ts\n\n\n// package.json\nvar version = \"1.9.2-next.7\";\n\n// src/client/CopilotRuntimeClient.ts\n\nvar createFetchFn = (signal, handleGQLWarning) => async (...args) => {\n  var _a, _b;\n  const publicApiKey = (_b = (_a = args[1]) == null ? void 0 : _a.headers) == null ? void 0 : _b[\"x-copilotcloud-public-api-key\"];\n  try {\n    const result = await fetch(args[0], { ...args[1] ?? {}, signal });\n    const mismatch = publicApiKey ? null : await (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_0__.getPossibleVersionMismatch)({\n      runtimeVersion: result.headers.get(\"X-CopilotKit-Runtime-Version\"),\n      runtimeClientGqlVersion: version\n    });\n    if (result.status !== 200) {\n      if (result.status >= 400 && result.status <= 500) {\n        if (mismatch) {\n          throw new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_0__.CopilotKitVersionMismatchError(mismatch);\n        }\n        throw new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_0__.ResolvedCopilotKitError({ status: result.status });\n      }\n    }\n    if (mismatch && handleGQLWarning) {\n      handleGQLWarning(mismatch.message);\n    }\n    return result;\n  } catch (error) {\n    if (error.message.includes(\"BodyStreamBuffer was aborted\") || error.message.includes(\"signal is aborted without reason\")) {\n      throw error;\n    }\n    if (error instanceof _copilotkit_shared__WEBPACK_IMPORTED_MODULE_0__.CopilotKitError) {\n      throw error;\n    }\n    throw new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_0__.CopilotKitLowLevelError({ error, url: args[0] });\n  }\n};\nvar CopilotRuntimeClient = class {\n  constructor(options) {\n    const headers = {};\n    this.handleGQLErrors = options.handleGQLErrors;\n    this.handleGQLWarning = options.handleGQLWarning;\n    if (options.headers) {\n      Object.assign(headers, options.headers);\n    }\n    if (options.publicApiKey) {\n      headers[\"x-copilotcloud-public-api-key\"] = options.publicApiKey;\n    }\n    this.client = new _urql_core__WEBPACK_IMPORTED_MODULE_1__.Client({\n      url: options.url,\n      exchanges: [_urql_core__WEBPACK_IMPORTED_MODULE_1__.cacheExchange, _urql_core__WEBPACK_IMPORTED_MODULE_1__.fetchExchange],\n      fetchOptions: {\n        headers: {\n          ...headers,\n          \"X-CopilotKit-Runtime-Client-GQL-Version\": version\n        },\n        ...options.credentials ? { credentials: options.credentials } : {}\n      }\n    });\n  }\n  generateCopilotResponse({\n    data,\n    properties,\n    signal\n  }) {\n    const fetchFn = createFetchFn(signal, this.handleGQLWarning);\n    const result = this.client.mutation(_chunk_HEODM5TW_mjs__WEBPACK_IMPORTED_MODULE_2__.generateCopilotResponseMutation, { data, properties }, { fetch: fetchFn });\n    result.subscribe(({ error }) => {\n      var _a, _b;\n      if (error && this.handleGQLErrors) {\n        console.log(\"\\u{1F41B} generateCopilotResponse: GraphQL error detected\", error);\n        console.log(\"\\u{1F41B} generateCopilotResponse: GraphQL errors array\", error.graphQLErrors);\n        console.log(\n          \"\\u{1F41B} generateCopilotResponse: Error extensions\",\n          (_b = (_a = error.graphQLErrors) == null ? void 0 : _a[0]) == null ? void 0 : _b.extensions\n        );\n        this.handleGQLErrors(error);\n      }\n    });\n    return result;\n  }\n  asStream(source) {\n    const handleGQLErrors = this.handleGQLErrors;\n    return new ReadableStream({\n      start(controller) {\n        source.subscribe(({ data, hasNext, error }) => {\n          var _a;\n          if (error) {\n            if (error.message.includes(\"BodyStreamBuffer was aborted\") || error.message.includes(\"signal is aborted without reason\")) {\n              if (!hasNext)\n                controller.close();\n              console.warn(\"Abort error suppressed\");\n              return;\n            }\n            if ((_a = error.extensions) == null ? void 0 : _a.visibility) {\n              const syntheticError = {\n                ...error,\n                graphQLErrors: [\n                  {\n                    message: error.message,\n                    extensions: error.extensions\n                  }\n                ]\n              };\n              if (handleGQLErrors) {\n                handleGQLErrors(syntheticError);\n              }\n              return;\n            }\n            controller.error(error);\n            if (handleGQLErrors) {\n              handleGQLErrors(error);\n            }\n          } else {\n            controller.enqueue(data);\n            if (!hasNext) {\n              controller.close();\n            }\n          }\n        });\n      }\n    });\n  }\n  availableAgents() {\n    const fetchFn = createFetchFn();\n    return this.client.query(_chunk_X2UAP3QY_mjs__WEBPACK_IMPORTED_MODULE_3__.getAvailableAgentsQuery, {}, { fetch: fetchFn });\n  }\n  loadAgentState(data) {\n    const fetchFn = createFetchFn();\n    return this.client.query(\n      _chunk_X2UAP3QY_mjs__WEBPACK_IMPORTED_MODULE_3__.loadAgentStateQuery,\n      { data },\n      { fetch: fetchFn }\n    );\n  }\n  static removeGraphQLTypename(data) {\n    if (Array.isArray(data)) {\n      data.forEach((item) => CopilotRuntimeClient.removeGraphQLTypename(item));\n    } else if (typeof data === \"object\" && data !== null) {\n      delete data.__typename;\n      Object.keys(data).forEach((key) => {\n        if (typeof data[key] === \"object\" && data[key] !== null) {\n          CopilotRuntimeClient.removeGraphQLTypename(data[key]);\n        }\n      });\n    }\n    return data;\n  }\n};\n\n\n//# sourceMappingURL=chunk-EI72UE2B.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-EI72UE2B.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-HEODM5TW.mjs":
/*!************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-HEODM5TW.mjs ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateCopilotResponseMutation: () => (/* binding */ generateCopilotResponseMutation)\n/* harmony export */ });\n/* harmony import */ var _chunk_4KTMZMM2_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-4KTMZMM2.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-4KTMZMM2.mjs\");\n\n\n// src/graphql/definitions/mutations.ts\nvar generateCopilotResponseMutation = (0,_chunk_4KTMZMM2_mjs__WEBPACK_IMPORTED_MODULE_0__.graphql)(\n  /** GraphQL **/\n  `\n  mutation generateCopilotResponse($data: GenerateCopilotResponseInput!, $properties: JSONObject) {\n    generateCopilotResponse(data: $data, properties: $properties) {\n      threadId\n      runId\n      extensions {\n        openaiAssistantAPI {\n          runId\n          threadId\n        }\n      }\n      ... on CopilotResponse @defer {\n        status {\n          ... on BaseResponseStatus {\n            code\n          }\n          ... on FailedResponseStatus {\n            reason\n            details\n          }\n        }\n      }\n      messages @stream {\n        __typename\n        ... on BaseMessageOutput {\n          id\n          createdAt\n        }\n        ... on BaseMessageOutput @defer {\n          status {\n            ... on SuccessMessageStatus {\n              code\n            }\n            ... on FailedMessageStatus {\n              code\n              reason\n            }\n            ... on PendingMessageStatus {\n              code\n            }\n          }\n        }\n        ... on TextMessageOutput {\n          content @stream\n          role\n          parentMessageId\n        }\n        ... on ImageMessageOutput {\n          format\n          bytes\n          role\n          parentMessageId\n        }\n        ... on ActionExecutionMessageOutput {\n          name\n          arguments @stream\n          parentMessageId\n        }\n        ... on ResultMessageOutput {\n          result\n          actionExecutionId\n          actionName\n        }\n        ... on AgentStateMessageOutput {\n          threadId\n          state\n          running\n          agentName\n          nodeName\n          runId\n          active\n          role\n        }\n      }\n      metaEvents @stream {\n        ... on LangGraphInterruptEvent {\n          type\n          name\n          value\n        }\n\n        ... on CopilotKitLangGraphInterruptEvent {\n          type\n          name\n          data {\n            messages {\n              __typename\n              ... on BaseMessageOutput {\n                id\n                createdAt\n              }\n              ... on BaseMessageOutput @defer {\n                status {\n                  ... on SuccessMessageStatus {\n                    code\n                  }\n                  ... on FailedMessageStatus {\n                    code\n                    reason\n                  }\n                  ... on PendingMessageStatus {\n                    code\n                  }\n                }\n              }\n              ... on TextMessageOutput {\n                content\n                role\n                parentMessageId\n              }\n              ... on ActionExecutionMessageOutput {\n                name\n                arguments\n                parentMessageId\n              }\n              ... on ResultMessageOutput {\n                result\n                actionExecutionId\n                actionName\n              }\n            }\n            value\n          }\n        }\n      }\n    }\n  }\n`\n);\n\n\n//# sourceMappingURL=chunk-HEODM5TW.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-HEODM5TW.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-P2AUSQOK.mjs":
/*!************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-P2AUSQOK.mjs ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertGqlOutputToMessages: () => (/* binding */ convertGqlOutputToMessages),\n/* harmony export */   convertMessagesToGqlInput: () => (/* binding */ convertMessagesToGqlInput),\n/* harmony export */   filterAdjacentAgentStateMessages: () => (/* binding */ filterAdjacentAgentStateMessages),\n/* harmony export */   filterAgentStateMessages: () => (/* binding */ filterAgentStateMessages),\n/* harmony export */   loadMessagesFromJsonRepresentation: () => (/* binding */ loadMessagesFromJsonRepresentation)\n/* harmony export */ });\n/* harmony import */ var _chunk_ROUIRR4B_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-ROUIRR4B.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-ROUIRR4B.mjs\");\n/* harmony import */ var untruncate_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! untruncate-json */ \"(ssr)/./node_modules/.pnpm/untruncate-json@0.0.1/node_modules/untruncate-json/dist/esm/index.js\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs\");\n\n\n// src/client/conversion.ts\n\n\nfunction filterAgentStateMessages(messages) {\n  return messages.filter((message) => !message.isAgentStateMessage());\n}\nfunction convertMessagesToGqlInput(messages) {\n  return messages.map((message) => {\n    if (message.isTextMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        textMessage: {\n          content: message.content,\n          role: message.role,\n          parentMessageId: message.parentMessageId\n        }\n      };\n    } else if (message.isActionExecutionMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        actionExecutionMessage: {\n          name: message.name,\n          arguments: JSON.stringify(message.arguments),\n          parentMessageId: message.parentMessageId\n        }\n      };\n    } else if (message.isResultMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        resultMessage: {\n          result: message.result,\n          actionExecutionId: message.actionExecutionId,\n          actionName: message.actionName\n        }\n      };\n    } else if (message.isAgentStateMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        agentStateMessage: {\n          threadId: message.threadId,\n          role: message.role,\n          agentName: message.agentName,\n          nodeName: message.nodeName,\n          runId: message.runId,\n          active: message.active,\n          running: message.running,\n          state: JSON.stringify(message.state)\n        }\n      };\n    } else if (message.isImageMessage()) {\n      return {\n        id: message.id,\n        createdAt: message.createdAt,\n        imageMessage: {\n          format: message.format,\n          bytes: message.bytes,\n          role: message.role,\n          parentMessageId: message.parentMessageId\n        }\n      };\n    } else {\n      throw new Error(\"Unknown message type\");\n    }\n  });\n}\nfunction filterAdjacentAgentStateMessages(messages) {\n  const filteredMessages = [];\n  messages.forEach((message, i) => {\n    if (message.__typename !== \"AgentStateMessageOutput\") {\n      filteredMessages.push(message);\n    } else {\n      const prevAgentStateMessageIndex = filteredMessages.findIndex(\n        // TODO: also check runId\n        (m) => m.__typename === \"AgentStateMessageOutput\" && m.agentName === message.agentName\n      );\n      if (prevAgentStateMessageIndex === -1) {\n        filteredMessages.push(message);\n      } else {\n        filteredMessages[prevAgentStateMessageIndex] = message;\n      }\n    }\n  });\n  return filteredMessages;\n}\nfunction convertGqlOutputToMessages(messages) {\n  return messages.map((message) => {\n    if (message.__typename === \"TextMessageOutput\") {\n      return new _chunk_ROUIRR4B_mjs__WEBPACK_IMPORTED_MODULE_0__.TextMessage({\n        id: message.id,\n        role: message.role,\n        content: message.content.join(\"\"),\n        parentMessageId: message.parentMessageId,\n        createdAt: /* @__PURE__ */ new Date(),\n        status: message.status || { code: \"Pending\" /* Pending */ }\n      });\n    } else if (message.__typename === \"ActionExecutionMessageOutput\") {\n      return new _chunk_ROUIRR4B_mjs__WEBPACK_IMPORTED_MODULE_0__.ActionExecutionMessage({\n        id: message.id,\n        name: message.name,\n        arguments: getPartialArguments(message.arguments),\n        parentMessageId: message.parentMessageId,\n        createdAt: /* @__PURE__ */ new Date(),\n        status: message.status || { code: \"Pending\" /* Pending */ }\n      });\n    } else if (message.__typename === \"ResultMessageOutput\") {\n      return new _chunk_ROUIRR4B_mjs__WEBPACK_IMPORTED_MODULE_0__.ResultMessage({\n        id: message.id,\n        result: message.result,\n        actionExecutionId: message.actionExecutionId,\n        actionName: message.actionName,\n        createdAt: /* @__PURE__ */ new Date(),\n        status: message.status || { code: \"Pending\" /* Pending */ }\n      });\n    } else if (message.__typename === \"AgentStateMessageOutput\") {\n      return new _chunk_ROUIRR4B_mjs__WEBPACK_IMPORTED_MODULE_0__.AgentStateMessage({\n        id: message.id,\n        threadId: message.threadId,\n        role: message.role,\n        agentName: message.agentName,\n        nodeName: message.nodeName,\n        runId: message.runId,\n        active: message.active,\n        running: message.running,\n        state: (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.parseJson)(message.state, {}),\n        createdAt: /* @__PURE__ */ new Date()\n      });\n    } else if (message.__typename === \"ImageMessageOutput\") {\n      return new _chunk_ROUIRR4B_mjs__WEBPACK_IMPORTED_MODULE_0__.ImageMessage({\n        id: message.id,\n        format: message.format,\n        bytes: message.bytes,\n        role: message.role,\n        parentMessageId: message.parentMessageId,\n        createdAt: /* @__PURE__ */ new Date(),\n        status: message.status || { code: \"Pending\" /* Pending */ }\n      });\n    }\n    throw new Error(\"Unknown message type\");\n  });\n}\nfunction loadMessagesFromJsonRepresentation(json) {\n  const result = [];\n  for (const item of json) {\n    if (\"content\" in item) {\n      result.push(\n        new _chunk_ROUIRR4B_mjs__WEBPACK_IMPORTED_MODULE_0__.TextMessage({\n          id: item.id,\n          role: item.role,\n          content: item.content,\n          parentMessageId: item.parentMessageId,\n          createdAt: item.createdAt || /* @__PURE__ */ new Date(),\n          status: item.status || { code: \"Success\" /* Success */ }\n        })\n      );\n    } else if (\"arguments\" in item) {\n      result.push(\n        new _chunk_ROUIRR4B_mjs__WEBPACK_IMPORTED_MODULE_0__.ActionExecutionMessage({\n          id: item.id,\n          name: item.name,\n          arguments: item.arguments,\n          parentMessageId: item.parentMessageId,\n          createdAt: item.createdAt || /* @__PURE__ */ new Date(),\n          status: item.status || { code: \"Success\" /* Success */ }\n        })\n      );\n    } else if (\"result\" in item) {\n      result.push(\n        new _chunk_ROUIRR4B_mjs__WEBPACK_IMPORTED_MODULE_0__.ResultMessage({\n          id: item.id,\n          result: item.result,\n          actionExecutionId: item.actionExecutionId,\n          actionName: item.actionName,\n          createdAt: item.createdAt || /* @__PURE__ */ new Date(),\n          status: item.status || { code: \"Success\" /* Success */ }\n        })\n      );\n    } else if (\"state\" in item) {\n      result.push(\n        new _chunk_ROUIRR4B_mjs__WEBPACK_IMPORTED_MODULE_0__.AgentStateMessage({\n          id: item.id,\n          threadId: item.threadId,\n          role: item.role,\n          agentName: item.agentName,\n          nodeName: item.nodeName,\n          runId: item.runId,\n          active: item.active,\n          running: item.running,\n          state: item.state,\n          createdAt: item.createdAt || /* @__PURE__ */ new Date()\n        })\n      );\n    } else if (\"format\" in item && \"bytes\" in item) {\n      result.push(\n        new _chunk_ROUIRR4B_mjs__WEBPACK_IMPORTED_MODULE_0__.ImageMessage({\n          id: item.id,\n          format: item.format,\n          bytes: item.bytes,\n          role: item.role,\n          parentMessageId: item.parentMessageId,\n          createdAt: item.createdAt || /* @__PURE__ */ new Date(),\n          status: item.status || { code: \"Success\" /* Success */ }\n        })\n      );\n    }\n  }\n  return result;\n}\nfunction getPartialArguments(args) {\n  try {\n    if (!args.length)\n      return {};\n    return JSON.parse((0,untruncate_json__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(args.join(\"\")));\n  } catch (e) {\n    return {};\n  }\n}\n\n\n//# sourceMappingURL=chunk-P2AUSQOK.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-P2AUSQOK.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-ROUIRR4B.mjs":
/*!************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-ROUIRR4B.mjs ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActionExecutionMessage: () => (/* binding */ ActionExecutionMessage),\n/* harmony export */   AgentStateMessage: () => (/* binding */ AgentStateMessage),\n/* harmony export */   ImageMessage: () => (/* binding */ ImageMessage),\n/* harmony export */   Message: () => (/* binding */ Message),\n/* harmony export */   ResultMessage: () => (/* binding */ ResultMessage),\n/* harmony export */   Role: () => (/* binding */ Role),\n/* harmony export */   TextMessage: () => (/* binding */ TextMessage),\n/* harmony export */   langGraphInterruptEvent: () => (/* binding */ langGraphInterruptEvent)\n/* harmony export */ });\n/* harmony import */ var _chunk_WM3ARNBD_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-WM3ARNBD.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-WM3ARNBD.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-VNNKZIFB.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs\");\n\n\n// src/client/types.ts\n\n\nvar Message = class {\n  constructor(props) {\n    props.id ?? (props.id = (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_0__.randomId)());\n    props.status ?? (props.status = { code: \"Success\" /* Success */ });\n    props.createdAt ?? (props.createdAt = /* @__PURE__ */ new Date());\n    Object.assign(this, props);\n  }\n  isTextMessage() {\n    return this.type === \"TextMessage\";\n  }\n  isActionExecutionMessage() {\n    return this.type === \"ActionExecutionMessage\";\n  }\n  isResultMessage() {\n    return this.type === \"ResultMessage\";\n  }\n  isAgentStateMessage() {\n    return this.type === \"AgentStateMessage\";\n  }\n  isImageMessage() {\n    return this.type === \"ImageMessage\";\n  }\n};\nvar Role = _chunk_WM3ARNBD_mjs__WEBPACK_IMPORTED_MODULE_1__.MessageRole;\nvar TextMessage = class extends Message {\n  constructor(props) {\n    super(props);\n    this.type = \"TextMessage\";\n  }\n};\nvar ActionExecutionMessage = class extends Message {\n  constructor(props) {\n    super(props);\n    this.type = \"ActionExecutionMessage\";\n  }\n};\nvar ResultMessage = class extends Message {\n  constructor(props) {\n    super(props);\n    this.type = \"ResultMessage\";\n  }\n  static decodeResult(result) {\n    return (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__.parseJson)(result, result);\n  }\n  static encodeResult(result) {\n    if (result === void 0) {\n      return \"\";\n    } else if (typeof result === \"string\") {\n      return result;\n    } else {\n      return JSON.stringify(result);\n    }\n  }\n};\nvar AgentStateMessage = class extends Message {\n  constructor(props) {\n    super(props);\n    this.type = \"AgentStateMessage\";\n  }\n};\nvar ImageMessage = class extends Message {\n  constructor(props) {\n    super(props);\n    this.type = \"ImageMessage\";\n  }\n};\nfunction langGraphInterruptEvent(eventProps) {\n  return { ...eventProps, name: \"LangGraphInterruptEvent\" /* LangGraphInterruptEvent */, type: \"MetaEvent\" };\n}\n\n\n//# sourceMappingURL=chunk-ROUIRR4B.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-ROUIRR4B.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-WM3ARNBD.mjs":
/*!************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-WM3ARNBD.mjs ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActionInputAvailability: () => (/* binding */ ActionInputAvailability),\n/* harmony export */   AvailableAgentsDocument: () => (/* binding */ AvailableAgentsDocument),\n/* harmony export */   CopilotRequestType: () => (/* binding */ CopilotRequestType),\n/* harmony export */   FailedResponseStatusReason: () => (/* binding */ FailedResponseStatusReason),\n/* harmony export */   GenerateCopilotResponseDocument: () => (/* binding */ GenerateCopilotResponseDocument),\n/* harmony export */   LoadAgentStateDocument: () => (/* binding */ LoadAgentStateDocument),\n/* harmony export */   MessageRole: () => (/* binding */ MessageRole),\n/* harmony export */   MessageStatusCode: () => (/* binding */ MessageStatusCode),\n/* harmony export */   MetaEventName: () => (/* binding */ MetaEventName),\n/* harmony export */   ResponseStatusCode: () => (/* binding */ ResponseStatusCode)\n/* harmony export */ });\n// src/graphql/@generated/graphql.ts\nvar ActionInputAvailability = /* @__PURE__ */ ((ActionInputAvailability2) => {\n  ActionInputAvailability2[\"Disabled\"] = \"disabled\";\n  ActionInputAvailability2[\"Enabled\"] = \"enabled\";\n  ActionInputAvailability2[\"Remote\"] = \"remote\";\n  return ActionInputAvailability2;\n})(ActionInputAvailability || {});\nvar CopilotRequestType = /* @__PURE__ */ ((CopilotRequestType2) => {\n  CopilotRequestType2[\"Chat\"] = \"Chat\";\n  CopilotRequestType2[\"Suggestion\"] = \"Suggestion\";\n  CopilotRequestType2[\"Task\"] = \"Task\";\n  CopilotRequestType2[\"TextareaCompletion\"] = \"TextareaCompletion\";\n  CopilotRequestType2[\"TextareaPopover\"] = \"TextareaPopover\";\n  return CopilotRequestType2;\n})(CopilotRequestType || {});\nvar FailedResponseStatusReason = /* @__PURE__ */ ((FailedResponseStatusReason2) => {\n  FailedResponseStatusReason2[\"GuardrailsValidationFailed\"] = \"GUARDRAILS_VALIDATION_FAILED\";\n  FailedResponseStatusReason2[\"MessageStreamInterrupted\"] = \"MESSAGE_STREAM_INTERRUPTED\";\n  FailedResponseStatusReason2[\"UnknownError\"] = \"UNKNOWN_ERROR\";\n  return FailedResponseStatusReason2;\n})(FailedResponseStatusReason || {});\nvar MessageRole = /* @__PURE__ */ ((MessageRole2) => {\n  MessageRole2[\"Assistant\"] = \"assistant\";\n  MessageRole2[\"Developer\"] = \"developer\";\n  MessageRole2[\"System\"] = \"system\";\n  MessageRole2[\"Tool\"] = \"tool\";\n  MessageRole2[\"User\"] = \"user\";\n  return MessageRole2;\n})(MessageRole || {});\nvar MessageStatusCode = /* @__PURE__ */ ((MessageStatusCode2) => {\n  MessageStatusCode2[\"Failed\"] = \"Failed\";\n  MessageStatusCode2[\"Pending\"] = \"Pending\";\n  MessageStatusCode2[\"Success\"] = \"Success\";\n  return MessageStatusCode2;\n})(MessageStatusCode || {});\nvar MetaEventName = /* @__PURE__ */ ((MetaEventName2) => {\n  MetaEventName2[\"CopilotKitLangGraphInterruptEvent\"] = \"CopilotKitLangGraphInterruptEvent\";\n  MetaEventName2[\"LangGraphInterruptEvent\"] = \"LangGraphInterruptEvent\";\n  return MetaEventName2;\n})(MetaEventName || {});\nvar ResponseStatusCode = /* @__PURE__ */ ((ResponseStatusCode2) => {\n  ResponseStatusCode2[\"Failed\"] = \"Failed\";\n  ResponseStatusCode2[\"Pending\"] = \"Pending\";\n  ResponseStatusCode2[\"Success\"] = \"Success\";\n  return ResponseStatusCode2;\n})(ResponseStatusCode || {});\nvar GenerateCopilotResponseDocument = { \"kind\": \"Document\", \"definitions\": [{ \"kind\": \"OperationDefinition\", \"operation\": \"mutation\", \"name\": { \"kind\": \"Name\", \"value\": \"generateCopilotResponse\" }, \"variableDefinitions\": [{ \"kind\": \"VariableDefinition\", \"variable\": { \"kind\": \"Variable\", \"name\": { \"kind\": \"Name\", \"value\": \"data\" } }, \"type\": { \"kind\": \"NonNullType\", \"type\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"GenerateCopilotResponseInput\" } } } }, { \"kind\": \"VariableDefinition\", \"variable\": { \"kind\": \"Variable\", \"name\": { \"kind\": \"Name\", \"value\": \"properties\" } }, \"type\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"JSONObject\" } } }], \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"generateCopilotResponse\" }, \"arguments\": [{ \"kind\": \"Argument\", \"name\": { \"kind\": \"Name\", \"value\": \"data\" }, \"value\": { \"kind\": \"Variable\", \"name\": { \"kind\": \"Name\", \"value\": \"data\" } } }, { \"kind\": \"Argument\", \"name\": { \"kind\": \"Name\", \"value\": \"properties\" }, \"value\": { \"kind\": \"Variable\", \"name\": { \"kind\": \"Name\", \"value\": \"properties\" } } }], \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"threadId\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"runId\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"extensions\" }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"openaiAssistantAPI\" }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"runId\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"threadId\" } }] } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"CopilotResponse\" } }, \"directives\": [{ \"kind\": \"Directive\", \"name\": { \"kind\": \"Name\", \"value\": \"defer\" } }], \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"status\" }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"BaseResponseStatus\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"code\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"FailedResponseStatus\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"reason\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"details\" } }] } }] } }] } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"messages\" }, \"directives\": [{ \"kind\": \"Directive\", \"name\": { \"kind\": \"Name\", \"value\": \"stream\" } }], \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"__typename\" } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"BaseMessageOutput\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"id\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"createdAt\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"BaseMessageOutput\" } }, \"directives\": [{ \"kind\": \"Directive\", \"name\": { \"kind\": \"Name\", \"value\": \"defer\" } }], \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"status\" }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"SuccessMessageStatus\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"code\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"FailedMessageStatus\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"code\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"reason\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"PendingMessageStatus\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"code\" } }] } }] } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"TextMessageOutput\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"content\" }, \"directives\": [{ \"kind\": \"Directive\", \"name\": { \"kind\": \"Name\", \"value\": \"stream\" } }] }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"role\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"parentMessageId\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"ImageMessageOutput\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"format\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"bytes\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"role\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"parentMessageId\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"ActionExecutionMessageOutput\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"name\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"arguments\" }, \"directives\": [{ \"kind\": \"Directive\", \"name\": { \"kind\": \"Name\", \"value\": \"stream\" } }] }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"parentMessageId\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"ResultMessageOutput\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"result\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"actionExecutionId\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"actionName\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"AgentStateMessageOutput\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"threadId\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"state\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"running\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"agentName\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"nodeName\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"runId\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"active\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"role\" } }] } }] } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"metaEvents\" }, \"directives\": [{ \"kind\": \"Directive\", \"name\": { \"kind\": \"Name\", \"value\": \"stream\" } }], \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"LangGraphInterruptEvent\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"type\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"name\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"value\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"CopilotKitLangGraphInterruptEvent\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"type\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"name\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"data\" }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"messages\" }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"__typename\" } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"BaseMessageOutput\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"id\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"createdAt\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"BaseMessageOutput\" } }, \"directives\": [{ \"kind\": \"Directive\", \"name\": { \"kind\": \"Name\", \"value\": \"defer\" } }], \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"status\" }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"SuccessMessageStatus\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"code\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"FailedMessageStatus\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"code\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"reason\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"PendingMessageStatus\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"code\" } }] } }] } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"TextMessageOutput\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"content\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"role\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"parentMessageId\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"ActionExecutionMessageOutput\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"name\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"arguments\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"parentMessageId\" } }] } }, { \"kind\": \"InlineFragment\", \"typeCondition\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"ResultMessageOutput\" } }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"result\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"actionExecutionId\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"actionName\" } }] } }] } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"value\" } }] } }] } }] } }] } }] } }] };\nvar AvailableAgentsDocument = { \"kind\": \"Document\", \"definitions\": [{ \"kind\": \"OperationDefinition\", \"operation\": \"query\", \"name\": { \"kind\": \"Name\", \"value\": \"availableAgents\" }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"availableAgents\" }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"agents\" }, \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"name\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"id\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"description\" } }] } }] } }] } }] };\nvar LoadAgentStateDocument = { \"kind\": \"Document\", \"definitions\": [{ \"kind\": \"OperationDefinition\", \"operation\": \"query\", \"name\": { \"kind\": \"Name\", \"value\": \"loadAgentState\" }, \"variableDefinitions\": [{ \"kind\": \"VariableDefinition\", \"variable\": { \"kind\": \"Variable\", \"name\": { \"kind\": \"Name\", \"value\": \"data\" } }, \"type\": { \"kind\": \"NonNullType\", \"type\": { \"kind\": \"NamedType\", \"name\": { \"kind\": \"Name\", \"value\": \"LoadAgentStateInput\" } } } }], \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"loadAgentState\" }, \"arguments\": [{ \"kind\": \"Argument\", \"name\": { \"kind\": \"Name\", \"value\": \"data\" }, \"value\": { \"kind\": \"Variable\", \"name\": { \"kind\": \"Name\", \"value\": \"data\" } } }], \"selectionSet\": { \"kind\": \"SelectionSet\", \"selections\": [{ \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"threadId\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"threadExists\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"state\" } }, { \"kind\": \"Field\", \"name\": { \"kind\": \"Name\", \"value\": \"messages\" } }] } }] } }] };\n\n\n//# sourceMappingURL=chunk-WM3ARNBD.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-WM3ARNBD.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-X2UAP3QY.mjs":
/*!************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-X2UAP3QY.mjs ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAvailableAgentsQuery: () => (/* binding */ getAvailableAgentsQuery),\n/* harmony export */   loadAgentStateQuery: () => (/* binding */ loadAgentStateQuery)\n/* harmony export */ });\n/* harmony import */ var _chunk_4KTMZMM2_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-4KTMZMM2.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-4KTMZMM2.mjs\");\n\n\n// src/graphql/definitions/queries.ts\nvar getAvailableAgentsQuery = (0,_chunk_4KTMZMM2_mjs__WEBPACK_IMPORTED_MODULE_0__.graphql)(\n  /** GraphQL **/\n  `\n  query availableAgents {\n    availableAgents {\n      agents {\n        name\n        id\n        description\n      }\n    }\n  }\n`\n);\nvar loadAgentStateQuery = (0,_chunk_4KTMZMM2_mjs__WEBPACK_IMPORTED_MODULE_0__.graphql)(\n  /** GraphQL **/\n  `\n  query loadAgentState($data: LoadAgentStateInput!) {\n    loadAgentState(data: $data) {\n      threadId\n      threadExists\n      state\n      messages\n    }\n  }\n`\n);\n\n\n//# sourceMappingURL=chunk-X2UAP3QY.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcnVudGltZS1jbGllbnQtZ3FsQDEuOS4yLW5leHQuN19ncmFwaHFsQDE2LjkuMF9yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0Bjb3BpbG90a2l0L3J1bnRpbWUtY2xpZW50LWdxbC9kaXN0L2NodW5rLVgyVUFQM1FZLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFFOEI7O0FBRTlCO0FBQ0EsOEJBQThCLDREQUFPO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDREQUFPO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFLRTtBQUNGIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcnVudGltZS1jbGllbnQtZ3FsQDEuOS4yLW5leHQuN19ncmFwaHFsQDE2LjkuMF9yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0Bjb3BpbG90a2l0L3J1bnRpbWUtY2xpZW50LWdxbC9kaXN0L2NodW5rLVgyVUFQM1FZLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBncmFwaHFsXG59IGZyb20gXCIuL2NodW5rLTRLVE1aTU0yLm1qc1wiO1xuXG4vLyBzcmMvZ3JhcGhxbC9kZWZpbml0aW9ucy9xdWVyaWVzLnRzXG52YXIgZ2V0QXZhaWxhYmxlQWdlbnRzUXVlcnkgPSBncmFwaHFsKFxuICAvKiogR3JhcGhRTCAqKi9cbiAgYFxuICBxdWVyeSBhdmFpbGFibGVBZ2VudHMge1xuICAgIGF2YWlsYWJsZUFnZW50cyB7XG4gICAgICBhZ2VudHMge1xuICAgICAgICBuYW1lXG4gICAgICAgIGlkXG4gICAgICAgIGRlc2NyaXB0aW9uXG4gICAgICB9XG4gICAgfVxuICB9XG5gXG4pO1xudmFyIGxvYWRBZ2VudFN0YXRlUXVlcnkgPSBncmFwaHFsKFxuICAvKiogR3JhcGhRTCAqKi9cbiAgYFxuICBxdWVyeSBsb2FkQWdlbnRTdGF0ZSgkZGF0YTogTG9hZEFnZW50U3RhdGVJbnB1dCEpIHtcbiAgICBsb2FkQWdlbnRTdGF0ZShkYXRhOiAkZGF0YSkge1xuICAgICAgdGhyZWFkSWRcbiAgICAgIHRocmVhZEV4aXN0c1xuICAgICAgc3RhdGVcbiAgICAgIG1lc3NhZ2VzXG4gICAgfVxuICB9XG5gXG4pO1xuXG5leHBvcnQge1xuICBnZXRBdmFpbGFibGVBZ2VudHNRdWVyeSxcbiAgbG9hZEFnZW50U3RhdGVRdWVyeVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNodW5rLVgyVUFQM1FZLm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-X2UAP3QY.mjs\n");

/***/ })

};
;