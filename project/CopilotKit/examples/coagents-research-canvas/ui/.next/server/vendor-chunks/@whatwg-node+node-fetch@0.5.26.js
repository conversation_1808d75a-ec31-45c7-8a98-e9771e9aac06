"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@whatwg-node+node-fetch@0.5.26";
exports.ids = ["vendor-chunks/@whatwg-node+node-fetch@0.5.26"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Blob.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Blob.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillBlob = void 0;\nexports.hasBufferMethod = hasBufferMethod;\nexports.hasArrayBufferMethod = hasArrayBufferMethod;\nexports.hasBytesMethod = hasBytesMethod;\nexports.hasTextMethod = hasTextMethod;\nexports.hasSizeProperty = hasSizeProperty;\nexports.hasStreamMethod = hasStreamMethod;\nexports.hasBlobSignature = hasBlobSignature;\nexports.isArrayBuffer = isArrayBuffer;\n/* eslint-disable @typescript-eslint/no-unsafe-declaration-merging */\nconst ReadableStream_js_1 = __webpack_require__(/*! ./ReadableStream.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/ReadableStream.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/utils.js\");\nfunction getBlobPartAsBuffer(blobPart) {\n    if (typeof blobPart === 'string') {\n        return Buffer.from(blobPart);\n    }\n    else if (Buffer.isBuffer(blobPart)) {\n        return blobPart;\n    }\n    else if ((0, utils_js_1.isArrayBufferView)(blobPart)) {\n        return Buffer.from(blobPart.buffer, blobPart.byteOffset, blobPart.byteLength);\n    }\n    else {\n        return Buffer.from(blobPart);\n    }\n}\nfunction hasBufferMethod(obj) {\n    return obj != null && obj.buffer != null && typeof obj.buffer === 'function';\n}\nfunction hasArrayBufferMethod(obj) {\n    return obj != null && obj.arrayBuffer != null && typeof obj.arrayBuffer === 'function';\n}\nfunction hasBytesMethod(obj) {\n    return obj != null && obj.bytes != null && typeof obj.bytes === 'function';\n}\nfunction hasTextMethod(obj) {\n    return obj != null && obj.text != null && typeof obj.text === 'function';\n}\nfunction hasSizeProperty(obj) {\n    return obj != null && typeof obj.size === 'number';\n}\nfunction hasStreamMethod(obj) {\n    return obj != null && obj.stream != null && typeof obj.stream === 'function';\n}\nfunction hasBlobSignature(obj) {\n    return obj != null && obj[Symbol.toStringTag] === 'Blob';\n}\nfunction isArrayBuffer(obj) {\n    return obj != null && obj.byteLength != null && obj.slice != null;\n}\n// Will be removed after v14 reaches EOL\n// Needed because v14 doesn't have .stream() implemented\nclass PonyfillBlob {\n    blobParts;\n    type;\n    encoding;\n    _size = null;\n    constructor(blobParts = [], options) {\n        this.blobParts = blobParts;\n        this.type = options?.type || 'application/octet-stream';\n        this.encoding = options?.encoding || 'utf8';\n        this._size = options?.size || null;\n        if (blobParts.length === 1 && hasBlobSignature(blobParts[0])) {\n            return blobParts[0];\n        }\n    }\n    _buffer = null;\n    buffer() {\n        if (this._buffer) {\n            return (0, utils_js_1.fakePromise)(this._buffer);\n        }\n        if (this.blobParts.length === 1) {\n            const blobPart = this.blobParts[0];\n            if (hasBufferMethod(blobPart)) {\n                return blobPart.buffer().then(buf => {\n                    this._buffer = buf;\n                    return this._buffer;\n                });\n            }\n            if (hasBytesMethod(blobPart)) {\n                return blobPart.bytes().then(bytes => {\n                    this._buffer = Buffer.from(bytes);\n                    return this._buffer;\n                });\n            }\n            if (hasArrayBufferMethod(blobPart)) {\n                return blobPart.arrayBuffer().then(arrayBuf => {\n                    this._buffer = Buffer.from(arrayBuf, undefined, blobPart.size);\n                    return this._buffer;\n                });\n            }\n            this._buffer = getBlobPartAsBuffer(blobPart);\n            return (0, utils_js_1.fakePromise)(this._buffer);\n        }\n        const jobs = [];\n        const bufferChunks = this.blobParts.map((blobPart, i) => {\n            if (hasBufferMethod(blobPart)) {\n                jobs.push(blobPart.buffer().then(buf => {\n                    bufferChunks[i] = buf;\n                }));\n                return undefined;\n            }\n            else if (hasArrayBufferMethod(blobPart)) {\n                jobs.push(blobPart.arrayBuffer().then(arrayBuf => {\n                    bufferChunks[i] = Buffer.from(arrayBuf, undefined, blobPart.size);\n                }));\n                return undefined;\n            }\n            else if (hasBytesMethod(blobPart)) {\n                jobs.push(blobPart.bytes().then(bytes => {\n                    bufferChunks[i] = Buffer.from(bytes);\n                }));\n                return undefined;\n            }\n            else {\n                return getBlobPartAsBuffer(blobPart);\n            }\n        });\n        if (jobs.length > 0) {\n            return Promise.all(jobs).then(() => Buffer.concat(bufferChunks, this._size || undefined));\n        }\n        return (0, utils_js_1.fakePromise)(Buffer.concat(bufferChunks, this._size || undefined));\n    }\n    arrayBuffer() {\n        if (this._buffer) {\n            return (0, utils_js_1.fakePromise)(this._buffer);\n        }\n        if (this.blobParts.length === 1) {\n            if (isArrayBuffer(this.blobParts[0])) {\n                return (0, utils_js_1.fakePromise)(this.blobParts[0]);\n            }\n            if (hasArrayBufferMethod(this.blobParts[0])) {\n                return this.blobParts[0].arrayBuffer();\n            }\n        }\n        return this.buffer();\n    }\n    bytes() {\n        if (this._buffer) {\n            return (0, utils_js_1.fakePromise)(this._buffer);\n        }\n        if (this.blobParts.length === 1) {\n            if (Buffer.isBuffer(this.blobParts[0])) {\n                this._buffer = this.blobParts[0];\n                return (0, utils_js_1.fakePromise)(this.blobParts[0]);\n            }\n            if (this.blobParts[0] instanceof Uint8Array) {\n                this._buffer = Buffer.from(this.blobParts[0]);\n                return (0, utils_js_1.fakePromise)(this.blobParts[0]);\n            }\n            if (hasBytesMethod(this.blobParts[0])) {\n                return this.blobParts[0].bytes();\n            }\n            if (hasBufferMethod(this.blobParts[0])) {\n                return this.blobParts[0].buffer();\n            }\n        }\n        return this.buffer();\n    }\n    _text = null;\n    text() {\n        if (this._text) {\n            return (0, utils_js_1.fakePromise)(this._text);\n        }\n        if (this.blobParts.length === 1) {\n            const blobPart = this.blobParts[0];\n            if (typeof blobPart === 'string') {\n                this._text = blobPart;\n                return (0, utils_js_1.fakePromise)(this._text);\n            }\n            if (hasTextMethod(blobPart)) {\n                return blobPart.text().then(text => {\n                    this._text = text;\n                    return this._text;\n                });\n            }\n            const buf = getBlobPartAsBuffer(blobPart);\n            this._text = buf.toString(this.encoding);\n            return (0, utils_js_1.fakePromise)(this._text);\n        }\n        return this.buffer().then(buf => {\n            this._text = buf.toString(this.encoding);\n            return this._text;\n        });\n    }\n    get size() {\n        if (this._size == null) {\n            this._size = 0;\n            for (const blobPart of this.blobParts) {\n                if (typeof blobPart === 'string') {\n                    this._size += Buffer.byteLength(blobPart);\n                }\n                else if (hasSizeProperty(blobPart)) {\n                    this._size += blobPart.size;\n                }\n                else if ((0, utils_js_1.isArrayBufferView)(blobPart)) {\n                    this._size += blobPart.byteLength;\n                }\n            }\n        }\n        return this._size;\n    }\n    stream() {\n        if (this.blobParts.length === 1) {\n            const blobPart = this.blobParts[0];\n            if (hasStreamMethod(blobPart)) {\n                return blobPart.stream();\n            }\n            const buf = getBlobPartAsBuffer(blobPart);\n            return new ReadableStream_js_1.PonyfillReadableStream({\n                start: controller => {\n                    controller.enqueue(buf);\n                    controller.close();\n                },\n            });\n        }\n        if (this._buffer != null) {\n            return new ReadableStream_js_1.PonyfillReadableStream({\n                start: controller => {\n                    controller.enqueue(this._buffer);\n                    controller.close();\n                },\n            });\n        }\n        let blobPartIterator;\n        return new ReadableStream_js_1.PonyfillReadableStream({\n            start: controller => {\n                if (this.blobParts.length === 0) {\n                    controller.close();\n                    return;\n                }\n                blobPartIterator = this.blobParts[Symbol.iterator]();\n            },\n            pull: controller => {\n                const { value: blobPart, done } = blobPartIterator.next();\n                if (done) {\n                    controller.close();\n                    return;\n                }\n                if (blobPart) {\n                    if (hasBufferMethod(blobPart)) {\n                        return blobPart.buffer().then(buf => {\n                            controller.enqueue(buf);\n                        });\n                    }\n                    if (hasBytesMethod(blobPart)) {\n                        return blobPart.bytes().then(bytes => {\n                            const buf = Buffer.from(bytes);\n                            controller.enqueue(buf);\n                        });\n                    }\n                    if (hasArrayBufferMethod(blobPart)) {\n                        return blobPart.arrayBuffer().then(arrayBuffer => {\n                            const buf = Buffer.from(arrayBuffer, undefined, blobPart.size);\n                            controller.enqueue(buf);\n                        });\n                    }\n                    const buf = getBlobPartAsBuffer(blobPart);\n                    controller.enqueue(buf);\n                }\n            },\n        });\n    }\n    slice() {\n        throw new Error('Not implemented');\n    }\n}\nexports.PonyfillBlob = PonyfillBlob;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Blob.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Body.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Body.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillBody = void 0;\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst busboy_1 = tslib_1.__importDefault(__webpack_require__(/*! busboy */ \"(rsc)/./node_modules/.pnpm/busboy@1.6.0/node_modules/busboy/lib/index.js\"));\nconst Blob_js_1 = __webpack_require__(/*! ./Blob.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Blob.js\");\nconst File_js_1 = __webpack_require__(/*! ./File.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/File.js\");\nconst FormData_js_1 = __webpack_require__(/*! ./FormData.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/FormData.js\");\nconst ReadableStream_js_1 = __webpack_require__(/*! ./ReadableStream.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/ReadableStream.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/utils.js\");\nvar BodyInitType;\n(function (BodyInitType) {\n    BodyInitType[\"ReadableStream\"] = \"ReadableStream\";\n    BodyInitType[\"Blob\"] = \"Blob\";\n    BodyInitType[\"FormData\"] = \"FormData\";\n    BodyInitType[\"String\"] = \"String\";\n    BodyInitType[\"Readable\"] = \"Readable\";\n    BodyInitType[\"Buffer\"] = \"Buffer\";\n})(BodyInitType || (BodyInitType = {}));\nclass PonyfillBody {\n    bodyInit;\n    options;\n    bodyUsed = false;\n    contentType = null;\n    contentLength = null;\n    constructor(bodyInit, options = {}) {\n        this.bodyInit = bodyInit;\n        this.options = options;\n        const { bodyFactory, contentType, contentLength, bodyType, buffer } = processBodyInit(bodyInit);\n        this._bodyFactory = bodyFactory;\n        this.contentType = contentType;\n        this.contentLength = contentLength;\n        this.bodyType = bodyType;\n        this._buffer = buffer;\n    }\n    bodyType;\n    _bodyFactory = () => null;\n    _generatedBody = null;\n    _buffer;\n    generateBody() {\n        if (this._generatedBody?.readable?.destroyed && this._buffer) {\n            this._generatedBody.readable = stream_1.Readable.from(this._buffer);\n        }\n        if (this._generatedBody) {\n            return this._generatedBody;\n        }\n        const body = this._bodyFactory();\n        this._generatedBody = body;\n        return body;\n    }\n    handleContentLengthHeader(forceSet = false) {\n        const contentTypeInHeaders = this.headers.get('content-type');\n        if (!contentTypeInHeaders) {\n            if (this.contentType) {\n                this.headers.set('content-type', this.contentType);\n            }\n        }\n        else {\n            this.contentType = contentTypeInHeaders;\n        }\n        const contentLengthInHeaders = this.headers.get('content-length');\n        if (forceSet && this.bodyInit == null && !contentLengthInHeaders) {\n            this.contentLength = 0;\n            this.headers.set('content-length', '0');\n        }\n        if (!contentLengthInHeaders) {\n            if (this.contentLength) {\n                this.headers.set('content-length', this.contentLength.toString());\n            }\n        }\n        else {\n            this.contentLength = parseInt(contentLengthInHeaders, 10);\n        }\n    }\n    get body() {\n        const _body = this.generateBody();\n        if (_body != null) {\n            const ponyfillReadableStream = _body;\n            const readable = _body.readable;\n            return new Proxy(_body.readable, {\n                get(_, prop) {\n                    if (prop in ponyfillReadableStream) {\n                        const ponyfillReadableStreamProp = ponyfillReadableStream[prop];\n                        if (typeof ponyfillReadableStreamProp === 'function') {\n                            return ponyfillReadableStreamProp.bind(ponyfillReadableStream);\n                        }\n                        return ponyfillReadableStreamProp;\n                    }\n                    if (prop in readable) {\n                        const readableProp = readable[prop];\n                        if (typeof readableProp === 'function') {\n                            return readableProp.bind(readable);\n                        }\n                        return readableProp;\n                    }\n                },\n            });\n        }\n        return null;\n    }\n    _chunks = null;\n    _collectChunksFromReadable() {\n        if (this._chunks) {\n            return (0, utils_js_1.fakePromise)(this._chunks);\n        }\n        const _body = this.generateBody();\n        if (!_body) {\n            return (0, utils_js_1.fakePromise)([]);\n        }\n        this._chunks = [];\n        _body.readable.on('data', chunk => {\n            this._chunks.push(chunk);\n        });\n        return new Promise((resolve, reject) => {\n            _body.readable.once('end', () => {\n                resolve(this._chunks);\n            });\n            _body.readable.once('error', e => {\n                reject(e);\n            });\n        });\n    }\n    _blob = null;\n    blob() {\n        if (this._blob) {\n            return (0, utils_js_1.fakePromise)(this._blob);\n        }\n        if (this.bodyType === BodyInitType.Blob) {\n            this._blob = this.bodyInit;\n            return (0, utils_js_1.fakePromise)(this._blob);\n        }\n        if (this._buffer) {\n            this._blob = new Blob_js_1.PonyfillBlob([this._buffer], {\n                type: this.contentType || '',\n                size: this.contentLength,\n            });\n            return (0, utils_js_1.fakePromise)(this._blob);\n        }\n        return this._collectChunksFromReadable().then(chunks => {\n            this._blob = new Blob_js_1.PonyfillBlob(chunks, {\n                type: this.contentType || '',\n                size: this.contentLength,\n            });\n            return this._blob;\n        });\n    }\n    _formData = null;\n    formData(opts) {\n        if (this._formData) {\n            return (0, utils_js_1.fakePromise)(this._formData);\n        }\n        if (this.bodyType === BodyInitType.FormData) {\n            this._formData = this.bodyInit;\n            return (0, utils_js_1.fakePromise)(this._formData);\n        }\n        this._formData = new FormData_js_1.PonyfillFormData();\n        const _body = this.generateBody();\n        if (_body == null) {\n            return (0, utils_js_1.fakePromise)(this._formData);\n        }\n        const formDataLimits = {\n            ...this.options.formDataLimits,\n            ...opts?.formDataLimits,\n        };\n        return new Promise((resolve, reject) => {\n            const bb = (0, busboy_1.default)({\n                headers: {\n                    'content-type': this.contentType || '',\n                },\n                limits: formDataLimits,\n                defParamCharset: 'utf-8',\n            });\n            bb.on('field', (name, value, { nameTruncated, valueTruncated }) => {\n                if (nameTruncated) {\n                    reject(new Error(`Field name size exceeded: ${formDataLimits?.fieldNameSize} bytes`));\n                }\n                if (valueTruncated) {\n                    reject(new Error(`Field value size exceeded: ${formDataLimits?.fieldSize} bytes`));\n                }\n                this._formData.set(name, value);\n            });\n            bb.on('fieldsLimit', () => {\n                reject(new Error(`Fields limit exceeded: ${formDataLimits?.fields}`));\n            });\n            bb.on('file', (name, fileStream, { filename, mimeType }) => {\n                const chunks = [];\n                fileStream.on('limit', () => {\n                    reject(new Error(`File size limit exceeded: ${formDataLimits?.fileSize} bytes`));\n                });\n                fileStream.on('data', chunk => {\n                    chunks.push(chunk);\n                });\n                fileStream.on('close', () => {\n                    if (fileStream.truncated) {\n                        reject(new Error(`File size limit exceeded: ${formDataLimits?.fileSize} bytes`));\n                    }\n                    const file = new File_js_1.PonyfillFile(chunks, filename, { type: mimeType });\n                    this._formData.set(name, file);\n                });\n            });\n            bb.on('filesLimit', () => {\n                reject(new Error(`Files limit exceeded: ${formDataLimits?.files}`));\n            });\n            bb.on('partsLimit', () => {\n                reject(new Error(`Parts limit exceeded: ${formDataLimits?.parts}`));\n            });\n            bb.on('close', () => {\n                resolve(this._formData);\n            });\n            bb.on('error', (err = 'An error occurred while parsing the form data') => {\n                const errMessage = err.message || err.toString();\n                reject(new TypeError(errMessage, err.cause));\n            });\n            _body?.readable.pipe(bb);\n        });\n    }\n    buffer() {\n        if (this._buffer) {\n            return (0, utils_js_1.fakePromise)(this._buffer);\n        }\n        if (this.bodyType === BodyInitType.Blob) {\n            if ((0, Blob_js_1.hasBufferMethod)(this.bodyInit)) {\n                return this.bodyInit.buffer().then(buf => {\n                    this._buffer = buf;\n                    return this._buffer;\n                });\n            }\n            if ((0, Blob_js_1.hasBytesMethod)(this.bodyInit)) {\n                return this.bodyInit.bytes().then(bytes => {\n                    this._buffer = Buffer.from(bytes);\n                    return this._buffer;\n                });\n            }\n            if ((0, Blob_js_1.hasArrayBufferMethod)(this.bodyInit)) {\n                return this.bodyInit.arrayBuffer().then(buf => {\n                    this._buffer = Buffer.from(buf, undefined, buf.byteLength);\n                    return this._buffer;\n                });\n            }\n        }\n        return this._collectChunksFromReadable().then(chunks => {\n            if (chunks.length === 1) {\n                this._buffer = chunks[0];\n                return this._buffer;\n            }\n            this._buffer = Buffer.concat(chunks);\n            return this._buffer;\n        });\n    }\n    bytes() {\n        return this.buffer();\n    }\n    arrayBuffer() {\n        return this.buffer();\n    }\n    _json = null;\n    json() {\n        if (this._json) {\n            return (0, utils_js_1.fakePromise)(this._json);\n        }\n        return this.text().then(text => {\n            try {\n                this._json = JSON.parse(text);\n            }\n            catch (e) {\n                if (e instanceof SyntaxError) {\n                    e.message += `, \"${text}\" is not valid JSON`;\n                }\n                throw e;\n            }\n            return this._json;\n        });\n    }\n    _text = null;\n    text() {\n        if (this._text) {\n            return (0, utils_js_1.fakePromise)(this._text);\n        }\n        if (this.bodyType === BodyInitType.String) {\n            this._text = this.bodyInit;\n            return (0, utils_js_1.fakePromise)(this._text);\n        }\n        return this.buffer().then(buffer => {\n            this._text = buffer.toString('utf-8');\n            return this._text;\n        });\n    }\n}\nexports.PonyfillBody = PonyfillBody;\nfunction processBodyInit(bodyInit) {\n    if (bodyInit == null) {\n        return {\n            bodyFactory: () => null,\n            contentType: null,\n            contentLength: null,\n        };\n    }\n    if (typeof bodyInit === 'string') {\n        const buffer = Buffer.from(bodyInit);\n        const contentLength = buffer.byteLength;\n        return {\n            bodyType: BodyInitType.String,\n            contentType: 'text/plain;charset=UTF-8',\n            contentLength,\n            buffer,\n            bodyFactory() {\n                const readable = stream_1.Readable.from(buffer);\n                return new ReadableStream_js_1.PonyfillReadableStream(readable);\n            },\n        };\n    }\n    if (Buffer.isBuffer(bodyInit)) {\n        return {\n            bodyType: BodyInitType.Buffer,\n            contentType: null,\n            contentLength: bodyInit.length,\n            buffer: bodyInit,\n            bodyFactory() {\n                const readable = stream_1.Readable.from(bodyInit);\n                const body = new ReadableStream_js_1.PonyfillReadableStream(readable);\n                return body;\n            },\n        };\n    }\n    if ((0, utils_js_1.isArrayBufferView)(bodyInit)) {\n        const buffer = Buffer.from(bodyInit.buffer, bodyInit.byteOffset, bodyInit.byteLength);\n        return {\n            bodyType: BodyInitType.Buffer,\n            contentLength: bodyInit.byteLength,\n            contentType: null,\n            buffer,\n            bodyFactory() {\n                const readable = stream_1.Readable.from(buffer);\n                const body = new ReadableStream_js_1.PonyfillReadableStream(readable);\n                return body;\n            },\n        };\n    }\n    if (bodyInit instanceof ReadableStream_js_1.PonyfillReadableStream && bodyInit.readable != null) {\n        return {\n            bodyType: BodyInitType.ReadableStream,\n            bodyFactory: () => bodyInit,\n            contentType: null,\n            contentLength: null,\n        };\n    }\n    if (isBlob(bodyInit)) {\n        return {\n            bodyType: BodyInitType.Blob,\n            contentType: bodyInit.type,\n            contentLength: bodyInit.size,\n            bodyFactory() {\n                return bodyInit.stream();\n            },\n        };\n    }\n    if (bodyInit instanceof ArrayBuffer) {\n        const contentLength = bodyInit.byteLength;\n        const buffer = Buffer.from(bodyInit, undefined, bodyInit.byteLength);\n        return {\n            bodyType: BodyInitType.Buffer,\n            contentType: null,\n            contentLength,\n            buffer,\n            bodyFactory() {\n                const readable = stream_1.Readable.from(buffer);\n                const body = new ReadableStream_js_1.PonyfillReadableStream(readable);\n                return body;\n            },\n        };\n    }\n    if (bodyInit instanceof stream_1.Readable) {\n        return {\n            bodyType: BodyInitType.Readable,\n            contentType: null,\n            contentLength: null,\n            bodyFactory() {\n                const body = new ReadableStream_js_1.PonyfillReadableStream(bodyInit);\n                return body;\n            },\n        };\n    }\n    if (isURLSearchParams(bodyInit)) {\n        const contentType = 'application/x-www-form-urlencoded;charset=UTF-8';\n        return {\n            bodyType: BodyInitType.String,\n            contentType,\n            contentLength: null,\n            bodyFactory() {\n                const body = new ReadableStream_js_1.PonyfillReadableStream(stream_1.Readable.from(bodyInit.toString()));\n                return body;\n            },\n        };\n    }\n    if (isFormData(bodyInit)) {\n        const boundary = Math.random().toString(36).substr(2);\n        const contentType = `multipart/form-data; boundary=${boundary}`;\n        return {\n            bodyType: BodyInitType.FormData,\n            contentType,\n            contentLength: null,\n            bodyFactory() {\n                return (0, FormData_js_1.getStreamFromFormData)(bodyInit, boundary);\n            },\n        };\n    }\n    if (isReadableStream(bodyInit)) {\n        return {\n            contentType: null,\n            contentLength: null,\n            bodyFactory() {\n                return new ReadableStream_js_1.PonyfillReadableStream(bodyInit);\n            },\n        };\n    }\n    if (bodyInit[Symbol.iterator] || bodyInit[Symbol.asyncIterator]) {\n        return {\n            contentType: null,\n            contentLength: null,\n            bodyFactory() {\n                const readable = stream_1.Readable.from(bodyInit);\n                return new ReadableStream_js_1.PonyfillReadableStream(readable);\n            },\n        };\n    }\n    throw new Error('Unknown body type');\n}\nfunction isFormData(value) {\n    return value?.forEach != null;\n}\nfunction isBlob(value) {\n    return value?.stream != null;\n}\nfunction isURLSearchParams(value) {\n    return value?.sort != null;\n}\nfunction isReadableStream(value) {\n    return value?.getReader != null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Body.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/CompressionStream.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/CompressionStream.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillCompressionStream = void 0;\nconst node_zlib_1 = __webpack_require__(/*! node:zlib */ \"node:zlib\");\nconst TransformStream_js_1 = __webpack_require__(/*! ./TransformStream.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/TransformStream.js\");\nclass PonyfillCompressionStream extends TransformStream_js_1.PonyfillTransformStream {\n    static supportedFormats = globalThis.process?.version?.startsWith('v2')\n        ? ['gzip', 'deflate', 'br']\n        : ['gzip', 'deflate', 'deflate-raw', 'br'];\n    constructor(compressionFormat) {\n        switch (compressionFormat) {\n            case 'x-gzip':\n            case 'gzip':\n                super((0, node_zlib_1.createGzip)());\n                break;\n            case 'x-deflate':\n            case 'deflate':\n                super((0, node_zlib_1.createDeflate)());\n                break;\n            case 'deflate-raw':\n                super((0, node_zlib_1.createDeflateRaw)());\n                break;\n            case 'br':\n                super((0, node_zlib_1.createBrotliCompress)());\n                break;\n            default:\n                throw new Error(`Unsupported compression format: ${compressionFormat}`);\n        }\n    }\n}\nexports.PonyfillCompressionStream = PonyfillCompressionStream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/CompressionStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/DecompressionStream.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/DecompressionStream.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillDecompressionStream = void 0;\nconst node_zlib_1 = __webpack_require__(/*! node:zlib */ \"node:zlib\");\nconst TransformStream_js_1 = __webpack_require__(/*! ./TransformStream.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/TransformStream.js\");\nclass PonyfillDecompressionStream extends TransformStream_js_1.PonyfillTransformStream {\n    static supportedFormats = globalThis.process?.version?.startsWith('v2')\n        ? ['gzip', 'deflate', 'br']\n        : ['gzip', 'deflate', 'deflate-raw', 'br'];\n    constructor(compressionFormat) {\n        switch (compressionFormat) {\n            case 'x-gzip':\n            case 'gzip':\n                super((0, node_zlib_1.createGunzip)());\n                break;\n            case 'x-deflate':\n            case 'deflate':\n                super((0, node_zlib_1.createInflate)());\n                break;\n            case 'deflate-raw':\n                super((0, node_zlib_1.createInflateRaw)());\n                break;\n            case 'br':\n                super((0, node_zlib_1.createBrotliDecompress)());\n                break;\n            default:\n                throw new TypeError(`Unsupported compression format: '${compressionFormat}'`);\n        }\n    }\n}\nexports.PonyfillDecompressionStream = PonyfillDecompressionStream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/DecompressionStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/File.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/File.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillFile = void 0;\nconst Blob_js_1 = __webpack_require__(/*! ./Blob.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Blob.js\");\nclass PonyfillFile extends Blob_js_1.PonyfillBlob {\n    name;\n    lastModified;\n    constructor(fileBits, name, options) {\n        super(fileBits, options);\n        this.name = name;\n        this.lastModified = options?.lastModified || Date.now();\n    }\n    webkitRelativePath = '';\n}\nexports.PonyfillFile = PonyfillFile;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHdoYXR3Zy1ub2RlK25vZGUtZmV0Y2hAMC41LjI2L25vZGVfbW9kdWxlcy9Ad2hhdHdnLW5vZGUvbm9kZS1mZXRjaC9janMvRmlsZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxvQkFBb0I7QUFDcEIsa0JBQWtCLG1CQUFPLENBQUMsNkhBQVc7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ad2hhdHdnLW5vZGUrbm9kZS1mZXRjaEAwLjUuMjYvbm9kZV9tb2R1bGVzL0B3aGF0d2ctbm9kZS9ub2RlLWZldGNoL2Nqcy9GaWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5Qb255ZmlsbEZpbGUgPSB2b2lkIDA7XG5jb25zdCBCbG9iX2pzXzEgPSByZXF1aXJlKFwiLi9CbG9iLmpzXCIpO1xuY2xhc3MgUG9ueWZpbGxGaWxlIGV4dGVuZHMgQmxvYl9qc18xLlBvbnlmaWxsQmxvYiB7XG4gICAgbmFtZTtcbiAgICBsYXN0TW9kaWZpZWQ7XG4gICAgY29uc3RydWN0b3IoZmlsZUJpdHMsIG5hbWUsIG9wdGlvbnMpIHtcbiAgICAgICAgc3VwZXIoZmlsZUJpdHMsIG9wdGlvbnMpO1xuICAgICAgICB0aGlzLm5hbWUgPSBuYW1lO1xuICAgICAgICB0aGlzLmxhc3RNb2RpZmllZCA9IG9wdGlvbnM/Lmxhc3RNb2RpZmllZCB8fCBEYXRlLm5vdygpO1xuICAgIH1cbiAgICB3ZWJraXRSZWxhdGl2ZVBhdGggPSAnJztcbn1cbmV4cG9ydHMuUG9ueWZpbGxGaWxlID0gUG9ueWZpbGxGaWxlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/File.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/FormData.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/FormData.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillFormData = void 0;\nexports.getStreamFromFormData = getStreamFromFormData;\nconst ReadableStream_js_1 = __webpack_require__(/*! ./ReadableStream.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/ReadableStream.js\");\nclass PonyfillFormData {\n    map = new Map();\n    append(name, value, fileName) {\n        let values = this.map.get(name);\n        if (!values) {\n            values = [];\n            this.map.set(name, values);\n        }\n        const entry = isBlob(value)\n            ? getNormalizedFile(name, value, fileName)\n            : value;\n        values.push(entry);\n    }\n    delete(name) {\n        this.map.delete(name);\n    }\n    get(name) {\n        const values = this.map.get(name);\n        return values ? values[0] : null;\n    }\n    getAll(name) {\n        return this.map.get(name) || [];\n    }\n    has(name) {\n        return this.map.has(name);\n    }\n    set(name, value, fileName) {\n        const entry = isBlob(value)\n            ? getNormalizedFile(name, value, fileName)\n            : value;\n        this.map.set(name, [entry]);\n    }\n    *[Symbol.iterator]() {\n        for (const [key, values] of this.map) {\n            for (const value of values) {\n                yield [key, value];\n            }\n        }\n    }\n    entries() {\n        return this[Symbol.iterator]();\n    }\n    keys() {\n        return this.map.keys();\n    }\n    *values() {\n        for (const values of this.map.values()) {\n            for (const value of values) {\n                yield value;\n            }\n        }\n    }\n    forEach(callback) {\n        for (const [key, value] of this) {\n            callback(value, key, this);\n        }\n    }\n}\nexports.PonyfillFormData = PonyfillFormData;\nfunction getStreamFromFormData(formData, boundary = '---') {\n    const entries = [];\n    let sentInitialHeader = false;\n    return new ReadableStream_js_1.PonyfillReadableStream({\n        start: controller => {\n            formData.forEach((value, key) => {\n                if (!sentInitialHeader) {\n                    controller.enqueue(Buffer.from(`--${boundary}\\r\\n`));\n                    sentInitialHeader = true;\n                }\n                entries.push([key, value]);\n            });\n            if (!sentInitialHeader) {\n                controller.enqueue(Buffer.from(`--${boundary}--\\r\\n`));\n                controller.close();\n            }\n        },\n        pull: async (controller) => {\n            const entry = entries.shift();\n            if (entry) {\n                const [key, value] = entry;\n                if (typeof value === 'string') {\n                    controller.enqueue(Buffer.from(`Content-Disposition: form-data; name=\"${key}\"\\r\\n\\r\\n`));\n                    controller.enqueue(Buffer.from(value));\n                }\n                else {\n                    let filenamePart = '';\n                    if (value.name) {\n                        filenamePart = `; filename=\"${value.name}\"`;\n                    }\n                    controller.enqueue(Buffer.from(`Content-Disposition: form-data; name=\"${key}\"${filenamePart}\\r\\n`));\n                    controller.enqueue(Buffer.from(`Content-Type: ${value.type || 'application/octet-stream'}\\r\\n\\r\\n`));\n                    const entryStream = value.stream();\n                    for await (const chunk of entryStream) {\n                        controller.enqueue(chunk);\n                    }\n                }\n                if (entries.length === 0) {\n                    controller.enqueue(Buffer.from(`\\r\\n--${boundary}--\\r\\n`));\n                    controller.close();\n                }\n                else {\n                    controller.enqueue(Buffer.from(`\\r\\n--${boundary}\\r\\n`));\n                }\n            }\n            else {\n                controller.enqueue(Buffer.from(`\\r\\n--${boundary}--\\r\\n`));\n                controller.close();\n            }\n        },\n    });\n}\nfunction getNormalizedFile(name, blob, fileName) {\n    Object.defineProperty(blob, 'name', {\n        configurable: true,\n        enumerable: true,\n        value: fileName || blob.name || name,\n    });\n    return blob;\n}\nfunction isBlob(value) {\n    return value?.arrayBuffer != null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHdoYXR3Zy1ub2RlK25vZGUtZmV0Y2hAMC41LjI2L25vZGVfbW9kdWxlcy9Ad2hhdHdnLW5vZGUvbm9kZS1mZXRjaC9janMvRm9ybURhdGEuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsd0JBQXdCO0FBQ3hCLDZCQUE2QjtBQUM3Qiw0QkFBNEIsbUJBQU8sQ0FBQyxpSkFBcUI7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3REFBd0QsU0FBUztBQUNqRTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxvREFBb0QsU0FBUztBQUM3RDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvRkFBb0YsUUFBUSxJQUFJO0FBQ2hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQ0FBMEMsWUFBWSxXQUFXO0FBQ2pFO0FBQ0Esb0ZBQW9GLFFBQVEsSUFBSSxHQUFHLGFBQWE7QUFDaEgsb0VBQW9FLHlDQUF5QztBQUM3RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0REFBNEQsU0FBUztBQUNyRTtBQUNBO0FBQ0E7QUFDQSw0REFBNEQsU0FBUztBQUNyRTtBQUNBO0FBQ0E7QUFDQSx3REFBd0QsU0FBUztBQUNqRTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0B3aGF0d2ctbm9kZStub2RlLWZldGNoQDAuNS4yNi9ub2RlX21vZHVsZXMvQHdoYXR3Zy1ub2RlL25vZGUtZmV0Y2gvY2pzL0Zvcm1EYXRhLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5Qb255ZmlsbEZvcm1EYXRhID0gdm9pZCAwO1xuZXhwb3J0cy5nZXRTdHJlYW1Gcm9tRm9ybURhdGEgPSBnZXRTdHJlYW1Gcm9tRm9ybURhdGE7XG5jb25zdCBSZWFkYWJsZVN0cmVhbV9qc18xID0gcmVxdWlyZShcIi4vUmVhZGFibGVTdHJlYW0uanNcIik7XG5jbGFzcyBQb255ZmlsbEZvcm1EYXRhIHtcbiAgICBtYXAgPSBuZXcgTWFwKCk7XG4gICAgYXBwZW5kKG5hbWUsIHZhbHVlLCBmaWxlTmFtZSkge1xuICAgICAgICBsZXQgdmFsdWVzID0gdGhpcy5tYXAuZ2V0KG5hbWUpO1xuICAgICAgICBpZiAoIXZhbHVlcykge1xuICAgICAgICAgICAgdmFsdWVzID0gW107XG4gICAgICAgICAgICB0aGlzLm1hcC5zZXQobmFtZSwgdmFsdWVzKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBlbnRyeSA9IGlzQmxvYih2YWx1ZSlcbiAgICAgICAgICAgID8gZ2V0Tm9ybWFsaXplZEZpbGUobmFtZSwgdmFsdWUsIGZpbGVOYW1lKVxuICAgICAgICAgICAgOiB2YWx1ZTtcbiAgICAgICAgdmFsdWVzLnB1c2goZW50cnkpO1xuICAgIH1cbiAgICBkZWxldGUobmFtZSkge1xuICAgICAgICB0aGlzLm1hcC5kZWxldGUobmFtZSk7XG4gICAgfVxuICAgIGdldChuYW1lKSB7XG4gICAgICAgIGNvbnN0IHZhbHVlcyA9IHRoaXMubWFwLmdldChuYW1lKTtcbiAgICAgICAgcmV0dXJuIHZhbHVlcyA/IHZhbHVlc1swXSA6IG51bGw7XG4gICAgfVxuICAgIGdldEFsbChuYW1lKSB7XG4gICAgICAgIHJldHVybiB0aGlzLm1hcC5nZXQobmFtZSkgfHwgW107XG4gICAgfVxuICAgIGhhcyhuYW1lKSB7XG4gICAgICAgIHJldHVybiB0aGlzLm1hcC5oYXMobmFtZSk7XG4gICAgfVxuICAgIHNldChuYW1lLCB2YWx1ZSwgZmlsZU5hbWUpIHtcbiAgICAgICAgY29uc3QgZW50cnkgPSBpc0Jsb2IodmFsdWUpXG4gICAgICAgICAgICA/IGdldE5vcm1hbGl6ZWRGaWxlKG5hbWUsIHZhbHVlLCBmaWxlTmFtZSlcbiAgICAgICAgICAgIDogdmFsdWU7XG4gICAgICAgIHRoaXMubWFwLnNldChuYW1lLCBbZW50cnldKTtcbiAgICB9XG4gICAgKltTeW1ib2wuaXRlcmF0b3JdKCkge1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlc10gb2YgdGhpcy5tYXApIHtcbiAgICAgICAgICAgIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICAgICAgICAgICAgeWllbGQgW2tleSwgdmFsdWVdO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGVudHJpZXMoKSB7XG4gICAgICAgIHJldHVybiB0aGlzW1N5bWJvbC5pdGVyYXRvcl0oKTtcbiAgICB9XG4gICAga2V5cygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubWFwLmtleXMoKTtcbiAgICB9XG4gICAgKnZhbHVlcygpIHtcbiAgICAgICAgZm9yIChjb25zdCB2YWx1ZXMgb2YgdGhpcy5tYXAudmFsdWVzKCkpIHtcbiAgICAgICAgICAgIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICAgICAgICAgICAgeWllbGQgdmFsdWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgZm9yRWFjaChjYWxsYmFjaykge1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiB0aGlzKSB7XG4gICAgICAgICAgICBjYWxsYmFjayh2YWx1ZSwga2V5LCB0aGlzKTtcbiAgICAgICAgfVxuICAgIH1cbn1cbmV4cG9ydHMuUG9ueWZpbGxGb3JtRGF0YSA9IFBvbnlmaWxsRm9ybURhdGE7XG5mdW5jdGlvbiBnZXRTdHJlYW1Gcm9tRm9ybURhdGEoZm9ybURhdGEsIGJvdW5kYXJ5ID0gJy0tLScpIHtcbiAgICBjb25zdCBlbnRyaWVzID0gW107XG4gICAgbGV0IHNlbnRJbml0aWFsSGVhZGVyID0gZmFsc2U7XG4gICAgcmV0dXJuIG5ldyBSZWFkYWJsZVN0cmVhbV9qc18xLlBvbnlmaWxsUmVhZGFibGVTdHJlYW0oe1xuICAgICAgICBzdGFydDogY29udHJvbGxlciA9PiB7XG4gICAgICAgICAgICBmb3JtRGF0YS5mb3JFYWNoKCh2YWx1ZSwga2V5KSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKCFzZW50SW5pdGlhbEhlYWRlcikge1xuICAgICAgICAgICAgICAgICAgICBjb250cm9sbGVyLmVucXVldWUoQnVmZmVyLmZyb20oYC0tJHtib3VuZGFyeX1cXHJcXG5gKSk7XG4gICAgICAgICAgICAgICAgICAgIHNlbnRJbml0aWFsSGVhZGVyID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZW50cmllcy5wdXNoKFtrZXksIHZhbHVlXSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmICghc2VudEluaXRpYWxIZWFkZXIpIHtcbiAgICAgICAgICAgICAgICBjb250cm9sbGVyLmVucXVldWUoQnVmZmVyLmZyb20oYC0tJHtib3VuZGFyeX0tLVxcclxcbmApKTtcbiAgICAgICAgICAgICAgICBjb250cm9sbGVyLmNsb3NlKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIHB1bGw6IGFzeW5jIChjb250cm9sbGVyKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBlbnRyeSA9IGVudHJpZXMuc2hpZnQoKTtcbiAgICAgICAgICAgIGlmIChlbnRyeSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IFtrZXksIHZhbHVlXSA9IGVudHJ5O1xuICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZShCdWZmZXIuZnJvbShgQ29udGVudC1EaXNwb3NpdGlvbjogZm9ybS1kYXRhOyBuYW1lPVwiJHtrZXl9XCJcXHJcXG5cXHJcXG5gKSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZShCdWZmZXIuZnJvbSh2YWx1ZSkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgbGV0IGZpbGVuYW1lUGFydCA9ICcnO1xuICAgICAgICAgICAgICAgICAgICBpZiAodmFsdWUubmFtZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgZmlsZW5hbWVQYXJ0ID0gYDsgZmlsZW5hbWU9XCIke3ZhbHVlLm5hbWV9XCJgO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZShCdWZmZXIuZnJvbShgQ29udGVudC1EaXNwb3NpdGlvbjogZm9ybS1kYXRhOyBuYW1lPVwiJHtrZXl9XCIke2ZpbGVuYW1lUGFydH1cXHJcXG5gKSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZShCdWZmZXIuZnJvbShgQ29udGVudC1UeXBlOiAke3ZhbHVlLnR5cGUgfHwgJ2FwcGxpY2F0aW9uL29jdGV0LXN0cmVhbSd9XFxyXFxuXFxyXFxuYCkpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBlbnRyeVN0cmVhbSA9IHZhbHVlLnN0cmVhbSgpO1xuICAgICAgICAgICAgICAgICAgICBmb3IgYXdhaXQgKGNvbnN0IGNodW5rIG9mIGVudHJ5U3RyZWFtKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb250cm9sbGVyLmVucXVldWUoY2h1bmspO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChlbnRyaWVzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICAgICAgICAgICAgICBjb250cm9sbGVyLmVucXVldWUoQnVmZmVyLmZyb20oYFxcclxcbi0tJHtib3VuZGFyeX0tLVxcclxcbmApKTtcbiAgICAgICAgICAgICAgICAgICAgY29udHJvbGxlci5jbG9zZSgpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgY29udHJvbGxlci5lbnF1ZXVlKEJ1ZmZlci5mcm9tKGBcXHJcXG4tLSR7Ym91bmRhcnl9XFxyXFxuYCkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZShCdWZmZXIuZnJvbShgXFxyXFxuLS0ke2JvdW5kYXJ5fS0tXFxyXFxuYCkpO1xuICAgICAgICAgICAgICAgIGNvbnRyb2xsZXIuY2xvc2UoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICB9KTtcbn1cbmZ1bmN0aW9uIGdldE5vcm1hbGl6ZWRGaWxlKG5hbWUsIGJsb2IsIGZpbGVOYW1lKSB7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGJsb2IsICduYW1lJywge1xuICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgIHZhbHVlOiBmaWxlTmFtZSB8fCBibG9iLm5hbWUgfHwgbmFtZSxcbiAgICB9KTtcbiAgICByZXR1cm4gYmxvYjtcbn1cbmZ1bmN0aW9uIGlzQmxvYih2YWx1ZSkge1xuICAgIHJldHVybiB2YWx1ZT8uYXJyYXlCdWZmZXIgIT0gbnVsbDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/FormData.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Headers.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Headers.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillHeaders = void 0;\nexports.isHeadersLike = isHeadersLike;\nconst util_1 = __webpack_require__(/*! util */ \"util\");\nfunction isHeadersLike(headers) {\n    return headers?.get && headers?.forEach;\n}\nclass PonyfillHeaders {\n    headersInit;\n    _map;\n    objectNormalizedKeysOfHeadersInit = [];\n    objectOriginalKeysOfHeadersInit = [];\n    _setCookies = [];\n    constructor(headersInit) {\n        this.headersInit = headersInit;\n    }\n    // perf: we don't need to build `this.map` for Requests, as we can access the headers directly\n    _get(key) {\n        const normalized = key.toLowerCase();\n        if (normalized === 'set-cookie') {\n            return this._setCookies.join(', ');\n        }\n        // If the map is built, reuse it\n        if (this._map) {\n            return this._map.get(normalized) || null;\n        }\n        // If the map is not built, try to get the value from the this.headersInit\n        if (this.headersInit == null) {\n            return null;\n        }\n        if (Array.isArray(this.headersInit)) {\n            return this.headersInit.find(header => header[0].toLowerCase() === normalized)?.[1] || null;\n        }\n        else if (isHeadersLike(this.headersInit)) {\n            return this.headersInit.get(normalized);\n        }\n        else {\n            const initValue = this.headersInit[key] || this.headersInit[normalized];\n            if (initValue != null) {\n                return initValue;\n            }\n            if (!this.objectNormalizedKeysOfHeadersInit.length) {\n                Object.keys(this.headersInit).forEach(k => {\n                    this.objectOriginalKeysOfHeadersInit.push(k);\n                    this.objectNormalizedKeysOfHeadersInit.push(k.toLowerCase());\n                });\n            }\n            const index = this.objectNormalizedKeysOfHeadersInit.indexOf(normalized);\n            if (index === -1) {\n                return null;\n            }\n            const originalKey = this.objectOriginalKeysOfHeadersInit[index];\n            return this.headersInit[originalKey];\n        }\n    }\n    // perf: Build the map of headers lazily, only when we need to access all headers or write to it.\n    // I could do a getter here, but I'm too lazy to type `getter`.\n    getMap() {\n        if (!this._map) {\n            if (this.headersInit != null) {\n                if (Array.isArray(this.headersInit)) {\n                    this._map = new Map();\n                    this.headersInit.forEach(([key, value]) => {\n                        const normalizedKey = key.toLowerCase();\n                        if (normalizedKey === 'set-cookie') {\n                            this._setCookies.push(value);\n                            return;\n                        }\n                        this._map.set(normalizedKey, value);\n                    });\n                }\n                else if (isHeadersLike(this.headersInit)) {\n                    this._map = new Map();\n                    this.headersInit.forEach((value, key) => {\n                        if (key === 'set-cookie') {\n                            this._setCookies.push(value);\n                            return;\n                        }\n                        this._map.set(key, value);\n                    });\n                }\n                else {\n                    this._map = new Map();\n                    for (const initKey in this.headersInit) {\n                        const initValue = this.headersInit[initKey];\n                        if (initValue != null) {\n                            const normalizedKey = initKey.toLowerCase();\n                            if (normalizedKey === 'set-cookie') {\n                                this._setCookies.push(initValue);\n                                continue;\n                            }\n                            this._map.set(normalizedKey, initValue);\n                        }\n                    }\n                }\n            }\n            else {\n                this._map = new Map();\n            }\n        }\n        return this._map;\n    }\n    append(name, value) {\n        const key = name.toLowerCase();\n        if (key === 'set-cookie') {\n            this._setCookies.push(value);\n            return;\n        }\n        const existingValue = this.getMap().get(key);\n        const finalValue = existingValue ? `${existingValue}, ${value}` : value;\n        this.getMap().set(key, finalValue);\n    }\n    get(name) {\n        const value = this._get(name);\n        if (value == null) {\n            return null;\n        }\n        return value;\n    }\n    has(name) {\n        if (name === 'set-cookie') {\n            return this._setCookies.length > 0;\n        }\n        return !!this._get(name); // we might need to check if header exists and not just check if it's not nullable\n    }\n    set(name, value) {\n        const key = name.toLowerCase();\n        if (key === 'set-cookie') {\n            this._setCookies = [value];\n            return;\n        }\n        this.getMap().set(key, value);\n    }\n    delete(name) {\n        const key = name.toLowerCase();\n        if (key === 'set-cookie') {\n            this._setCookies = [];\n            return;\n        }\n        this.getMap().delete(key);\n    }\n    forEach(callback) {\n        this._setCookies.forEach(setCookie => {\n            callback(setCookie, 'set-cookie', this);\n        });\n        if (!this._map) {\n            if (this.headersInit) {\n                if (Array.isArray(this.headersInit)) {\n                    this.headersInit.forEach(([key, value]) => {\n                        callback(value, key, this);\n                    });\n                    return;\n                }\n                if (isHeadersLike(this.headersInit)) {\n                    this.headersInit.forEach(callback);\n                    return;\n                }\n                Object.entries(this.headersInit).forEach(([key, value]) => {\n                    if (value != null) {\n                        callback(value, key, this);\n                    }\n                });\n            }\n            return;\n        }\n        this.getMap().forEach((value, key) => {\n            callback(value, key, this);\n        });\n    }\n    *keys() {\n        if (this._setCookies.length) {\n            yield 'set-cookie';\n        }\n        if (!this._map) {\n            if (this.headersInit) {\n                if (Array.isArray(this.headersInit)) {\n                    yield* this.headersInit.map(([key]) => key)[Symbol.iterator]();\n                    return;\n                }\n                if (isHeadersLike(this.headersInit)) {\n                    yield* this.headersInit.keys();\n                    return;\n                }\n                yield* Object.keys(this.headersInit)[Symbol.iterator]();\n                return;\n            }\n        }\n        yield* this.getMap().keys();\n    }\n    *values() {\n        yield* this._setCookies;\n        if (!this._map) {\n            if (this.headersInit) {\n                if (Array.isArray(this.headersInit)) {\n                    yield* this.headersInit.map(([, value]) => value)[Symbol.iterator]();\n                    return;\n                }\n                if (isHeadersLike(this.headersInit)) {\n                    yield* this.headersInit.values();\n                    return;\n                }\n                yield* Object.values(this.headersInit)[Symbol.iterator]();\n                return;\n            }\n        }\n        yield* this.getMap().values();\n    }\n    *entries() {\n        yield* this._setCookies.map(cookie => ['set-cookie', cookie]);\n        if (!this._map) {\n            if (this.headersInit) {\n                if (Array.isArray(this.headersInit)) {\n                    yield* this.headersInit;\n                    return;\n                }\n                if (isHeadersLike(this.headersInit)) {\n                    yield* this.headersInit.entries();\n                    return;\n                }\n                yield* Object.entries(this.headersInit);\n                return;\n            }\n        }\n        yield* this.getMap().entries();\n    }\n    getSetCookie() {\n        return this._setCookies;\n    }\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n    [Symbol.for('nodejs.util.inspect.custom')]() {\n        const record = {};\n        this.forEach((value, key) => {\n            if (key === 'set-cookie') {\n                record['set-cookie'] = this._setCookies;\n            }\n            else {\n                record[key] = value.includes(',') ? value.split(',').map(el => el.trim()) : value;\n            }\n        });\n        return `Headers ${(0, util_1.inspect)(record)}`;\n    }\n}\nexports.PonyfillHeaders = PonyfillHeaders;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Headers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/ReadableStream.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/ReadableStream.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillReadableStream = void 0;\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/utils.js\");\nfunction createController(desiredSize, readable) {\n    let chunks = [];\n    let _closed = false;\n    let flushed = false;\n    return {\n        desiredSize,\n        enqueue(chunk) {\n            const buf = typeof chunk === 'string' ? Buffer.from(chunk) : chunk;\n            if (!flushed) {\n                chunks.push(buf);\n            }\n            else {\n                readable.push(buf);\n            }\n        },\n        close() {\n            if (chunks.length > 0) {\n                this._flush();\n            }\n            readable.push(null);\n            _closed = true;\n        },\n        error(error) {\n            if (chunks.length > 0) {\n                this._flush();\n            }\n            readable.destroy(error);\n        },\n        get _closed() {\n            return _closed;\n        },\n        _flush() {\n            flushed = true;\n            if (chunks.length > 0) {\n                const concatenated = chunks.length > 1 ? Buffer.concat(chunks) : chunks[0];\n                readable.push(concatenated);\n                chunks = [];\n            }\n        },\n    };\n}\nfunction isNodeReadable(obj) {\n    return obj?.read != null;\n}\nfunction isReadableStream(obj) {\n    return obj?.getReader != null;\n}\nclass PonyfillReadableStream {\n    readable;\n    constructor(underlyingSource) {\n        if (underlyingSource instanceof PonyfillReadableStream && underlyingSource.readable != null) {\n            this.readable = underlyingSource.readable;\n        }\n        else if (isNodeReadable(underlyingSource)) {\n            this.readable = underlyingSource;\n        }\n        else if (isReadableStream(underlyingSource)) {\n            this.readable = stream_1.Readable.fromWeb(underlyingSource);\n        }\n        else {\n            let started = false;\n            let ongoing = false;\n            this.readable = new stream_1.Readable({\n                read(desiredSize) {\n                    if (ongoing) {\n                        return;\n                    }\n                    ongoing = true;\n                    return Promise.resolve().then(async () => {\n                        if (!started) {\n                            const controller = createController(desiredSize, this);\n                            started = true;\n                            await underlyingSource?.start?.(controller);\n                            controller._flush();\n                            if (controller._closed) {\n                                return;\n                            }\n                        }\n                        const controller = createController(desiredSize, this);\n                        await underlyingSource?.pull?.(controller);\n                        controller._flush();\n                        ongoing = false;\n                    });\n                },\n                destroy(err, callback) {\n                    if (underlyingSource?.cancel) {\n                        try {\n                            const res$ = underlyingSource.cancel(err);\n                            if (res$?.then) {\n                                return res$.then(() => {\n                                    callback(null);\n                                }, err => {\n                                    callback(err);\n                                });\n                            }\n                        }\n                        catch (err) {\n                            callback(err);\n                            return;\n                        }\n                    }\n                    callback(null);\n                },\n            });\n        }\n    }\n    cancel(reason) {\n        this.readable.destroy(reason);\n        return new Promise(resolve => this.readable.once('end', resolve));\n    }\n    locked = false;\n    getReader(_options) {\n        const iterator = this.readable[Symbol.asyncIterator]();\n        this.locked = true;\n        return {\n            read() {\n                return iterator.next();\n            },\n            releaseLock: () => {\n                if (iterator.return) {\n                    const retResult$ = iterator.return();\n                    if (retResult$.then) {\n                        retResult$.then(() => {\n                            this.locked = false;\n                        });\n                        return;\n                    }\n                }\n                this.locked = false;\n            },\n            cancel: reason => {\n                if (iterator.return) {\n                    const retResult$ = iterator.return(reason);\n                    if (retResult$.then) {\n                        return retResult$.then(() => {\n                            this.locked = false;\n                        });\n                    }\n                }\n                this.locked = false;\n                return (0, utils_js_1.fakePromise)(undefined);\n            },\n            closed: new Promise((resolve, reject) => {\n                this.readable.once('end', resolve);\n                this.readable.once('error', reject);\n            }),\n        };\n    }\n    [Symbol.asyncIterator]() {\n        return this.readable[Symbol.asyncIterator]();\n    }\n    tee() {\n        throw new Error('Not implemented');\n    }\n    pipeTo(destination) {\n        if (isPonyfillWritableStream(destination)) {\n            return new Promise((resolve, reject) => {\n                this.readable.pipe(destination.writable);\n                destination.writable.once('finish', resolve);\n                destination.writable.once('error', reject);\n            });\n        }\n        else {\n            const writer = destination.getWriter();\n            return Promise.resolve().then(async () => {\n                try {\n                    for await (const chunk of this) {\n                        await writer.write(chunk);\n                    }\n                    await writer.close();\n                }\n                catch (err) {\n                    await writer.abort(err);\n                }\n            });\n        }\n    }\n    pipeThrough({ writable, readable, }) {\n        this.pipeTo(writable);\n        return readable;\n    }\n    static [Symbol.hasInstance](instance) {\n        return isReadableStream(instance);\n    }\n}\nexports.PonyfillReadableStream = PonyfillReadableStream;\nfunction isPonyfillWritableStream(obj) {\n    return obj?.writable != null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/ReadableStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Request.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Request.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillRequest = void 0;\nconst http_1 = __webpack_require__(/*! http */ \"http\");\nconst https_1 = __webpack_require__(/*! https */ \"https\");\nconst Body_js_1 = __webpack_require__(/*! ./Body.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Body.js\");\nconst Headers_js_1 = __webpack_require__(/*! ./Headers.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Headers.js\");\nfunction isRequest(input) {\n    return input[Symbol.toStringTag] === 'Request';\n}\nfunction isURL(obj) {\n    return obj?.href != null;\n}\nclass PonyfillRequest extends Body_js_1.PonyfillBody {\n    constructor(input, options) {\n        let url;\n        let bodyInit = null;\n        let requestInit;\n        if (typeof input === 'string') {\n            url = input;\n        }\n        else if (isURL(input)) {\n            url = input.toString();\n        }\n        else if (isRequest(input)) {\n            url = input.url;\n            bodyInit = input.body;\n            requestInit = input;\n        }\n        if (options != null) {\n            bodyInit = options.body || null;\n            requestInit = options;\n        }\n        super(bodyInit, options);\n        this.cache = requestInit?.cache || 'default';\n        this.credentials = requestInit?.credentials || 'same-origin';\n        this.headers =\n            requestInit?.headers && (0, Headers_js_1.isHeadersLike)(requestInit.headers)\n                ? requestInit.headers\n                : new Headers_js_1.PonyfillHeaders(requestInit?.headers);\n        this.integrity = requestInit?.integrity || '';\n        this.keepalive = requestInit?.keepalive != null ? requestInit?.keepalive : false;\n        this.method = requestInit?.method?.toUpperCase() || 'GET';\n        this.mode = requestInit?.mode || 'cors';\n        this.redirect = requestInit?.redirect || 'follow';\n        this.referrer = requestInit?.referrer || 'about:client';\n        this.referrerPolicy = requestInit?.referrerPolicy || 'no-referrer';\n        this._signal = requestInit?.signal;\n        this.headersSerializer = requestInit?.headersSerializer;\n        this.duplex = requestInit?.duplex || 'half';\n        this.url = url || '';\n        this.destination = 'document';\n        this.priority = 'auto';\n        if (this.method !== 'GET' && this.method !== 'HEAD') {\n            this.handleContentLengthHeader(true);\n        }\n        if (requestInit?.agent != null) {\n            if (requestInit.agent === false) {\n                this.agent = false;\n            }\n            else if (this.url.startsWith('http:/') && requestInit.agent instanceof http_1.Agent) {\n                this.agent = requestInit.agent;\n            }\n            else if (this.url.startsWith('https:/') && requestInit.agent instanceof https_1.Agent) {\n                this.agent = requestInit.agent;\n            }\n        }\n    }\n    headersSerializer;\n    cache;\n    credentials;\n    destination;\n    headers;\n    integrity;\n    keepalive;\n    method;\n    mode;\n    priority;\n    redirect;\n    referrer;\n    referrerPolicy;\n    url;\n    duplex;\n    agent;\n    _signal;\n    get signal() {\n        // Create a new signal only if needed\n        // Because the creation of signal is expensive\n        if (!this._signal) {\n            this._signal = new AbortController().signal;\n        }\n        return this._signal;\n    }\n    clone() {\n        return this;\n    }\n    [Symbol.toStringTag] = 'Request';\n}\nexports.PonyfillRequest = PonyfillRequest;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Response.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Response.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillResponse = void 0;\nconst http_1 = __webpack_require__(/*! http */ \"http\");\nconst Body_js_1 = __webpack_require__(/*! ./Body.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Body.js\");\nconst Headers_js_1 = __webpack_require__(/*! ./Headers.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Headers.js\");\nconst JSON_CONTENT_TYPE = 'application/json; charset=utf-8';\nclass PonyfillResponse extends Body_js_1.PonyfillBody {\n    headers;\n    constructor(body, init) {\n        super(body || null, init);\n        this.headers =\n            init?.headers && (0, Headers_js_1.isHeadersLike)(init.headers)\n                ? init.headers\n                : new Headers_js_1.PonyfillHeaders(init?.headers);\n        this.status = init?.status || 200;\n        this.statusText = init?.statusText || http_1.STATUS_CODES[this.status] || 'OK';\n        this.url = init?.url || '';\n        this.redirected = init?.redirected || false;\n        this.type = init?.type || 'default';\n        this.handleContentLengthHeader();\n    }\n    get ok() {\n        return this.status >= 200 && this.status < 300;\n    }\n    status;\n    statusText;\n    url;\n    redirected;\n    type;\n    clone() {\n        return this;\n    }\n    static error() {\n        return new PonyfillResponse(null, {\n            status: 500,\n            statusText: 'Internal Server Error',\n        });\n    }\n    static redirect(url, status = 302) {\n        if (status < 300 || status > 399) {\n            throw new RangeError('Invalid status code');\n        }\n        return new PonyfillResponse(null, {\n            headers: {\n                location: url,\n            },\n            status,\n        });\n    }\n    static json(data, init = {}) {\n        init.headers =\n            init?.headers && (0, Headers_js_1.isHeadersLike)(init.headers)\n                ? init.headers\n                : new Headers_js_1.PonyfillHeaders(init?.headers);\n        if (!init.headers.has('content-type')) {\n            init.headers.set('content-type', JSON_CONTENT_TYPE);\n        }\n        return new PonyfillResponse(JSON.stringify(data), init);\n    }\n}\nexports.PonyfillResponse = PonyfillResponse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Response.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/TextEncoderDecoder.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/TextEncoderDecoder.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillTextDecoder = exports.PonyfillTextEncoder = void 0;\nexports.PonyfillBtoa = PonyfillBtoa;\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/utils.js\");\nclass PonyfillTextEncoder {\n    encoding;\n    constructor(encoding = 'utf-8') {\n        this.encoding = encoding;\n    }\n    encode(input) {\n        return Buffer.from(input, this.encoding);\n    }\n    encodeInto(source, destination) {\n        const buffer = this.encode(source);\n        const copied = buffer.copy(destination);\n        return {\n            read: copied,\n            written: copied,\n        };\n    }\n}\nexports.PonyfillTextEncoder = PonyfillTextEncoder;\nclass PonyfillTextDecoder {\n    encoding;\n    fatal = false;\n    ignoreBOM = false;\n    constructor(encoding = 'utf-8', options) {\n        this.encoding = encoding;\n        if (options) {\n            this.fatal = options.fatal || false;\n            this.ignoreBOM = options.ignoreBOM || false;\n        }\n    }\n    decode(input) {\n        if (Buffer.isBuffer(input)) {\n            return input.toString(this.encoding);\n        }\n        if ((0, utils_js_1.isArrayBufferView)(input)) {\n            return Buffer.from(input.buffer, input.byteOffset, input.byteLength).toString(this.encoding);\n        }\n        return Buffer.from(input).toString(this.encoding);\n    }\n}\nexports.PonyfillTextDecoder = PonyfillTextDecoder;\nfunction PonyfillBtoa(input) {\n    return Buffer.from(input, 'binary').toString('base64');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHdoYXR3Zy1ub2RlK25vZGUtZmV0Y2hAMC41LjI2L25vZGVfbW9kdWxlcy9Ad2hhdHdnLW5vZGUvbm9kZS1mZXRjaC9janMvVGV4dEVuY29kZXJEZWNvZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDJCQUEyQixHQUFHLDJCQUEyQjtBQUN6RCxvQkFBb0I7QUFDcEIsbUJBQW1CLG1CQUFPLENBQUMsK0hBQVk7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHdoYXR3Zy1ub2RlK25vZGUtZmV0Y2hAMC41LjI2L25vZGVfbW9kdWxlcy9Ad2hhdHdnLW5vZGUvbm9kZS1mZXRjaC9janMvVGV4dEVuY29kZXJEZWNvZGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5Qb255ZmlsbFRleHREZWNvZGVyID0gZXhwb3J0cy5Qb255ZmlsbFRleHRFbmNvZGVyID0gdm9pZCAwO1xuZXhwb3J0cy5Qb255ZmlsbEJ0b2EgPSBQb255ZmlsbEJ0b2E7XG5jb25zdCB1dGlsc19qc18xID0gcmVxdWlyZShcIi4vdXRpbHMuanNcIik7XG5jbGFzcyBQb255ZmlsbFRleHRFbmNvZGVyIHtcbiAgICBlbmNvZGluZztcbiAgICBjb25zdHJ1Y3RvcihlbmNvZGluZyA9ICd1dGYtOCcpIHtcbiAgICAgICAgdGhpcy5lbmNvZGluZyA9IGVuY29kaW5nO1xuICAgIH1cbiAgICBlbmNvZGUoaW5wdXQpIHtcbiAgICAgICAgcmV0dXJuIEJ1ZmZlci5mcm9tKGlucHV0LCB0aGlzLmVuY29kaW5nKTtcbiAgICB9XG4gICAgZW5jb2RlSW50byhzb3VyY2UsIGRlc3RpbmF0aW9uKSB7XG4gICAgICAgIGNvbnN0IGJ1ZmZlciA9IHRoaXMuZW5jb2RlKHNvdXJjZSk7XG4gICAgICAgIGNvbnN0IGNvcGllZCA9IGJ1ZmZlci5jb3B5KGRlc3RpbmF0aW9uKTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHJlYWQ6IGNvcGllZCxcbiAgICAgICAgICAgIHdyaXR0ZW46IGNvcGllZCxcbiAgICAgICAgfTtcbiAgICB9XG59XG5leHBvcnRzLlBvbnlmaWxsVGV4dEVuY29kZXIgPSBQb255ZmlsbFRleHRFbmNvZGVyO1xuY2xhc3MgUG9ueWZpbGxUZXh0RGVjb2RlciB7XG4gICAgZW5jb2Rpbmc7XG4gICAgZmF0YWwgPSBmYWxzZTtcbiAgICBpZ25vcmVCT00gPSBmYWxzZTtcbiAgICBjb25zdHJ1Y3RvcihlbmNvZGluZyA9ICd1dGYtOCcsIG9wdGlvbnMpIHtcbiAgICAgICAgdGhpcy5lbmNvZGluZyA9IGVuY29kaW5nO1xuICAgICAgICBpZiAob3B0aW9ucykge1xuICAgICAgICAgICAgdGhpcy5mYXRhbCA9IG9wdGlvbnMuZmF0YWwgfHwgZmFsc2U7XG4gICAgICAgICAgICB0aGlzLmlnbm9yZUJPTSA9IG9wdGlvbnMuaWdub3JlQk9NIHx8IGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuICAgIGRlY29kZShpbnB1dCkge1xuICAgICAgICBpZiAoQnVmZmVyLmlzQnVmZmVyKGlucHV0KSkge1xuICAgICAgICAgICAgcmV0dXJuIGlucHV0LnRvU3RyaW5nKHRoaXMuZW5jb2RpbmcpO1xuICAgICAgICB9XG4gICAgICAgIGlmICgoMCwgdXRpbHNfanNfMS5pc0FycmF5QnVmZmVyVmlldykoaW5wdXQpKSB7XG4gICAgICAgICAgICByZXR1cm4gQnVmZmVyLmZyb20oaW5wdXQuYnVmZmVyLCBpbnB1dC5ieXRlT2Zmc2V0LCBpbnB1dC5ieXRlTGVuZ3RoKS50b1N0cmluZyh0aGlzLmVuY29kaW5nKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gQnVmZmVyLmZyb20oaW5wdXQpLnRvU3RyaW5nKHRoaXMuZW5jb2RpbmcpO1xuICAgIH1cbn1cbmV4cG9ydHMuUG9ueWZpbGxUZXh0RGVjb2RlciA9IFBvbnlmaWxsVGV4dERlY29kZXI7XG5mdW5jdGlvbiBQb255ZmlsbEJ0b2EoaW5wdXQpIHtcbiAgICByZXR1cm4gQnVmZmVyLmZyb20oaW5wdXQsICdiaW5hcnknKS50b1N0cmluZygnYmFzZTY0Jyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/TextEncoderDecoder.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/TransformStream.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/TransformStream.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillTransformStream = void 0;\nconst node_stream_1 = __webpack_require__(/*! node:stream */ \"node:stream\");\nconst ReadableStream_js_1 = __webpack_require__(/*! ./ReadableStream.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/ReadableStream.js\");\nconst WritableStream_js_1 = __webpack_require__(/*! ./WritableStream.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/WritableStream.js\");\nclass PonyfillTransformStream {\n    transform;\n    writable;\n    readable;\n    constructor(transformer) {\n        if (transformer instanceof node_stream_1.Transform) {\n            this.transform = transformer;\n        }\n        else if (transformer) {\n            const controller = {\n                enqueue(chunk) {\n                    transform.push(chunk);\n                },\n                error(reason) {\n                    transform.destroy(reason);\n                },\n                terminate() {\n                    transform.end();\n                },\n                get desiredSize() {\n                    return transform.writableLength;\n                },\n            };\n            const transform = new node_stream_1.Transform({\n                read() { },\n                write(chunk, _encoding, callback) {\n                    try {\n                        const result = transformer.transform?.(chunk, controller);\n                        if (result instanceof Promise) {\n                            result.then(() => {\n                                callback();\n                            }, err => {\n                                callback(err);\n                            });\n                        }\n                        else {\n                            callback();\n                        }\n                    }\n                    catch (err) {\n                        callback(err);\n                    }\n                },\n                final(callback) {\n                    try {\n                        const result = transformer.flush?.(controller);\n                        if (result instanceof Promise) {\n                            result.then(() => {\n                                callback();\n                            }, err => {\n                                callback(err);\n                            });\n                        }\n                        else {\n                            callback();\n                        }\n                    }\n                    catch (err) {\n                        callback(err);\n                    }\n                },\n            });\n            this.transform = transform;\n        }\n        else {\n            this.transform = new node_stream_1.Transform();\n        }\n        this.writable = new WritableStream_js_1.PonyfillWritableStream(this.transform);\n        this.readable = new ReadableStream_js_1.PonyfillReadableStream(this.transform);\n    }\n}\nexports.PonyfillTransformStream = PonyfillTransformStream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/TransformStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/URL.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/URL.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillURL = void 0;\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\nconst buffer_1 = __webpack_require__(/*! buffer */ \"buffer\");\nconst crypto_1 = __webpack_require__(/*! crypto */ \"crypto\");\nconst fast_querystring_1 = tslib_1.__importDefault(__webpack_require__(/*! fast-querystring */ \"(rsc)/./node_modules/.pnpm/fast-querystring@1.1.2/node_modules/fast-querystring/lib/index.js\"));\nconst fast_url_parser_1 = tslib_1.__importDefault(__webpack_require__(/*! @kamilkisiela/fast-url-parser */ \"(rsc)/./node_modules/.pnpm/@kamilkisiela+fast-url-parser@1.1.4/node_modules/@kamilkisiela/fast-url-parser/src/urlparser.js\"));\nconst URLSearchParams_js_1 = __webpack_require__(/*! ./URLSearchParams.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/URLSearchParams.js\");\nfast_url_parser_1.default.queryString = fast_querystring_1.default;\nclass PonyfillURL extends fast_url_parser_1.default {\n    constructor(url, base) {\n        super();\n        if (url.startsWith('data:')) {\n            this.protocol = 'data:';\n            this.pathname = url.slice('data:'.length);\n            return;\n        }\n        this.parse(url, false);\n        if (base) {\n            const baseParsed = typeof base === 'string' ? new PonyfillURL(base) : base;\n            this.protocol = this.protocol || baseParsed.protocol;\n            this.host = this.host || baseParsed.host;\n            this.pathname = this.pathname || baseParsed.pathname;\n        }\n    }\n    get origin() {\n        return `${this.protocol}//${this.host}`;\n    }\n    _searchParams;\n    get searchParams() {\n        if (!this._searchParams) {\n            this._searchParams = new URLSearchParams_js_1.PonyfillURLSearchParams(this.query);\n        }\n        return this._searchParams;\n    }\n    get username() {\n        return this.auth?.split(':')[0] || '';\n    }\n    set username(value) {\n        this.auth = `${value}:${this.password}`;\n    }\n    get password() {\n        return this.auth?.split(':')[1] || '';\n    }\n    set password(value) {\n        this.auth = `${this.username}:${value}`;\n    }\n    toString() {\n        return this.format();\n    }\n    toJSON() {\n        return this.toString();\n    }\n    static blobRegistry = new Map();\n    static createObjectURL(blob) {\n        const blobUrl = `blob:whatwgnode:${(0, crypto_1.randomUUID)()}`;\n        this.blobRegistry.set(blobUrl, blob);\n        return blobUrl;\n    }\n    static resolveObjectURL(url) {\n        if (!this.blobRegistry.has(url)) {\n            URL.revokeObjectURL(url);\n        }\n        else {\n            this.blobRegistry.delete(url);\n        }\n    }\n    static getBlobFromURL(url) {\n        return (this.blobRegistry.get(url) || (0, buffer_1.resolveObjectURL)(url));\n    }\n}\nexports.PonyfillURL = PonyfillURL;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHdoYXR3Zy1ub2RlK25vZGUtZmV0Y2hAMC41LjI2L25vZGVfbW9kdWxlcy9Ad2hhdHdnLW5vZGUvbm9kZS1mZXRjaC9janMvVVJMLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG1CQUFtQjtBQUNuQixnQkFBZ0IsbUJBQU8sQ0FBQyxzRkFBTztBQUMvQixpQkFBaUIsbUJBQU8sQ0FBQyxzQkFBUTtBQUNqQyxpQkFBaUIsbUJBQU8sQ0FBQyxzQkFBUTtBQUNqQyxtREFBbUQsbUJBQU8sQ0FBQyxzSEFBa0I7QUFDN0Usa0RBQWtELG1CQUFPLENBQUMsaUtBQStCO0FBQ3pGLDZCQUE2QixtQkFBTyxDQUFDLG1KQUFzQjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsY0FBYyxJQUFJLFVBQVU7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLE1BQU0sR0FBRyxjQUFjO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsY0FBYyxHQUFHLE1BQU07QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLDJCQUEyQjtBQUN0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ad2hhdHdnLW5vZGUrbm9kZS1mZXRjaEAwLjUuMjYvbm9kZV9tb2R1bGVzL0B3aGF0d2ctbm9kZS9ub2RlLWZldGNoL2Nqcy9VUkwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlBvbnlmaWxsVVJMID0gdm9pZCAwO1xuY29uc3QgdHNsaWJfMSA9IHJlcXVpcmUoXCJ0c2xpYlwiKTtcbmNvbnN0IGJ1ZmZlcl8xID0gcmVxdWlyZShcImJ1ZmZlclwiKTtcbmNvbnN0IGNyeXB0b18xID0gcmVxdWlyZShcImNyeXB0b1wiKTtcbmNvbnN0IGZhc3RfcXVlcnlzdHJpbmdfMSA9IHRzbGliXzEuX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCJmYXN0LXF1ZXJ5c3RyaW5nXCIpKTtcbmNvbnN0IGZhc3RfdXJsX3BhcnNlcl8xID0gdHNsaWJfMS5fX2ltcG9ydERlZmF1bHQocmVxdWlyZShcIkBrYW1pbGtpc2llbGEvZmFzdC11cmwtcGFyc2VyXCIpKTtcbmNvbnN0IFVSTFNlYXJjaFBhcmFtc19qc18xID0gcmVxdWlyZShcIi4vVVJMU2VhcmNoUGFyYW1zLmpzXCIpO1xuZmFzdF91cmxfcGFyc2VyXzEuZGVmYXVsdC5xdWVyeVN0cmluZyA9IGZhc3RfcXVlcnlzdHJpbmdfMS5kZWZhdWx0O1xuY2xhc3MgUG9ueWZpbGxVUkwgZXh0ZW5kcyBmYXN0X3VybF9wYXJzZXJfMS5kZWZhdWx0IHtcbiAgICBjb25zdHJ1Y3Rvcih1cmwsIGJhc2UpIHtcbiAgICAgICAgc3VwZXIoKTtcbiAgICAgICAgaWYgKHVybC5zdGFydHNXaXRoKCdkYXRhOicpKSB7XG4gICAgICAgICAgICB0aGlzLnByb3RvY29sID0gJ2RhdGE6JztcbiAgICAgICAgICAgIHRoaXMucGF0aG5hbWUgPSB1cmwuc2xpY2UoJ2RhdGE6Jy5sZW5ndGgpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMucGFyc2UodXJsLCBmYWxzZSk7XG4gICAgICAgIGlmIChiYXNlKSB7XG4gICAgICAgICAgICBjb25zdCBiYXNlUGFyc2VkID0gdHlwZW9mIGJhc2UgPT09ICdzdHJpbmcnID8gbmV3IFBvbnlmaWxsVVJMKGJhc2UpIDogYmFzZTtcbiAgICAgICAgICAgIHRoaXMucHJvdG9jb2wgPSB0aGlzLnByb3RvY29sIHx8IGJhc2VQYXJzZWQucHJvdG9jb2w7XG4gICAgICAgICAgICB0aGlzLmhvc3QgPSB0aGlzLmhvc3QgfHwgYmFzZVBhcnNlZC5ob3N0O1xuICAgICAgICAgICAgdGhpcy5wYXRobmFtZSA9IHRoaXMucGF0aG5hbWUgfHwgYmFzZVBhcnNlZC5wYXRobmFtZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBnZXQgb3JpZ2luKCkge1xuICAgICAgICByZXR1cm4gYCR7dGhpcy5wcm90b2NvbH0vLyR7dGhpcy5ob3N0fWA7XG4gICAgfVxuICAgIF9zZWFyY2hQYXJhbXM7XG4gICAgZ2V0IHNlYXJjaFBhcmFtcygpIHtcbiAgICAgICAgaWYgKCF0aGlzLl9zZWFyY2hQYXJhbXMpIHtcbiAgICAgICAgICAgIHRoaXMuX3NlYXJjaFBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXNfanNfMS5Qb255ZmlsbFVSTFNlYXJjaFBhcmFtcyh0aGlzLnF1ZXJ5KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5fc2VhcmNoUGFyYW1zO1xuICAgIH1cbiAgICBnZXQgdXNlcm5hbWUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmF1dGg/LnNwbGl0KCc6JylbMF0gfHwgJyc7XG4gICAgfVxuICAgIHNldCB1c2VybmFtZSh2YWx1ZSkge1xuICAgICAgICB0aGlzLmF1dGggPSBgJHt2YWx1ZX06JHt0aGlzLnBhc3N3b3JkfWA7XG4gICAgfVxuICAgIGdldCBwYXNzd29yZCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuYXV0aD8uc3BsaXQoJzonKVsxXSB8fCAnJztcbiAgICB9XG4gICAgc2V0IHBhc3N3b3JkKHZhbHVlKSB7XG4gICAgICAgIHRoaXMuYXV0aCA9IGAke3RoaXMudXNlcm5hbWV9OiR7dmFsdWV9YDtcbiAgICB9XG4gICAgdG9TdHJpbmcoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmZvcm1hdCgpO1xuICAgIH1cbiAgICB0b0pTT04oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnRvU3RyaW5nKCk7XG4gICAgfVxuICAgIHN0YXRpYyBibG9iUmVnaXN0cnkgPSBuZXcgTWFwKCk7XG4gICAgc3RhdGljIGNyZWF0ZU9iamVjdFVSTChibG9iKSB7XG4gICAgICAgIGNvbnN0IGJsb2JVcmwgPSBgYmxvYjp3aGF0d2dub2RlOiR7KDAsIGNyeXB0b18xLnJhbmRvbVVVSUQpKCl9YDtcbiAgICAgICAgdGhpcy5ibG9iUmVnaXN0cnkuc2V0KGJsb2JVcmwsIGJsb2IpO1xuICAgICAgICByZXR1cm4gYmxvYlVybDtcbiAgICB9XG4gICAgc3RhdGljIHJlc29sdmVPYmplY3RVUkwodXJsKSB7XG4gICAgICAgIGlmICghdGhpcy5ibG9iUmVnaXN0cnkuaGFzKHVybCkpIHtcbiAgICAgICAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwodXJsKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuYmxvYlJlZ2lzdHJ5LmRlbGV0ZSh1cmwpO1xuICAgICAgICB9XG4gICAgfVxuICAgIHN0YXRpYyBnZXRCbG9iRnJvbVVSTCh1cmwpIHtcbiAgICAgICAgcmV0dXJuICh0aGlzLmJsb2JSZWdpc3RyeS5nZXQodXJsKSB8fCAoMCwgYnVmZmVyXzEucmVzb2x2ZU9iamVjdFVSTCkodXJsKSk7XG4gICAgfVxufVxuZXhwb3J0cy5Qb255ZmlsbFVSTCA9IFBvbnlmaWxsVVJMO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/URL.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/URLSearchParams.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/URLSearchParams.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillURLSearchParams = void 0;\nconst tslib_1 = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\nconst fast_querystring_1 = tslib_1.__importDefault(__webpack_require__(/*! fast-querystring */ \"(rsc)/./node_modules/.pnpm/fast-querystring@1.1.2/node_modules/fast-querystring/lib/index.js\"));\nfunction isURLSearchParams(value) {\n    return value?.entries != null;\n}\nclass PonyfillURLSearchParams {\n    params;\n    constructor(init) {\n        if (init) {\n            if (typeof init === 'string') {\n                this.params = fast_querystring_1.default.parse(init);\n            }\n            else if (Array.isArray(init)) {\n                this.params = {};\n                for (const [key, value] of init) {\n                    this.params[key] = value;\n                }\n            }\n            else if (isURLSearchParams(init)) {\n                this.params = {};\n                for (const [key, value] of init.entries()) {\n                    this.params[key] = value;\n                }\n            }\n            else {\n                this.params = init;\n            }\n        }\n        else {\n            this.params = {};\n        }\n    }\n    append(name, value) {\n        const existingValue = this.params[name];\n        const finalValue = existingValue ? `${existingValue},${value}` : value;\n        this.params[name] = finalValue;\n    }\n    delete(name) {\n        delete this.params[name];\n    }\n    get(name) {\n        const value = this.params[name];\n        if (Array.isArray(value)) {\n            return value[0] || null;\n        }\n        return value || null;\n    }\n    getAll(name) {\n        const value = this.params[name];\n        if (!Array.isArray(value)) {\n            return value ? [value] : [];\n        }\n        return value;\n    }\n    has(name) {\n        return name in this.params;\n    }\n    set(name, value) {\n        this.params[name] = value;\n    }\n    sort() {\n        const sortedKeys = Object.keys(this.params).sort();\n        const sortedParams = {};\n        for (const key of sortedKeys) {\n            sortedParams[key] = this.params[key];\n        }\n        this.params = sortedParams;\n    }\n    toString() {\n        return fast_querystring_1.default.stringify(this.params);\n    }\n    *keys() {\n        for (const key in this.params) {\n            yield key;\n        }\n    }\n    *entries() {\n        for (const key of this.keys()) {\n            const value = this.params[key];\n            if (Array.isArray(value)) {\n                for (const item of value) {\n                    yield [key, item];\n                }\n            }\n            else {\n                yield [key, value];\n            }\n        }\n    }\n    *values() {\n        for (const [, value] of this) {\n            yield value;\n        }\n    }\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n    forEach(callback) {\n        for (const [key, value] of this) {\n            callback(value, key, this);\n        }\n    }\n    get size() {\n        return Object.keys(this.params).length;\n    }\n}\nexports.PonyfillURLSearchParams = PonyfillURLSearchParams;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/URLSearchParams.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/WritableStream.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/WritableStream.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PonyfillWritableStream = void 0;\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/utils.js\");\nclass PonyfillWritableStream {\n    writable;\n    constructor(underlyingSink) {\n        if (underlyingSink instanceof stream_1.Writable) {\n            this.writable = underlyingSink;\n        }\n        else if (underlyingSink) {\n            const writable = new stream_1.Writable({\n                write(chunk, _encoding, callback) {\n                    try {\n                        const result = underlyingSink.write?.(chunk, controller);\n                        if (result instanceof Promise) {\n                            result.then(() => {\n                                callback();\n                            }, err => {\n                                callback(err);\n                            });\n                        }\n                        else {\n                            callback();\n                        }\n                    }\n                    catch (err) {\n                        callback(err);\n                    }\n                },\n                final(callback) {\n                    const result = underlyingSink.close?.();\n                    if (result instanceof Promise) {\n                        result.then(() => {\n                            callback();\n                        }, err => {\n                            callback(err);\n                        });\n                    }\n                    else {\n                        callback();\n                    }\n                },\n            });\n            this.writable = writable;\n            let onabort;\n            let reason;\n            const controller = {\n                signal: {\n                    any(signals) {\n                        return AbortSignal.any([...signals]);\n                    },\n                    get reason() {\n                        return reason;\n                    },\n                    get aborted() {\n                        return writable.destroyed;\n                    },\n                    addEventListener: (_event, eventListener) => {\n                        writable.once('error', eventListener);\n                        writable.once('close', eventListener);\n                    },\n                    removeEventListener: (_event, eventListener) => {\n                        writable.off('error', eventListener);\n                        writable.off('close', eventListener);\n                    },\n                    dispatchEvent: (_event) => {\n                        return false;\n                    },\n                    get onabort() {\n                        return onabort;\n                    },\n                    set onabort(value) {\n                        if (onabort) {\n                            this.removeEventListener('abort', onabort);\n                        }\n                        onabort = value;\n                        if (onabort) {\n                            this.addEventListener('abort', onabort);\n                        }\n                    },\n                    throwIfAborted() {\n                        if (writable.destroyed) {\n                            throw reason;\n                        }\n                    },\n                },\n                error: e => {\n                    this.writable.destroy(e);\n                },\n            };\n            this.writable.once('error', err => {\n                reason = err;\n            });\n        }\n        else {\n            this.writable = new stream_1.Writable();\n        }\n    }\n    getWriter() {\n        const writable = this.writable;\n        return {\n            closed: new Promise(resolve => {\n                writable.once('close', () => {\n                    resolve(undefined);\n                });\n            }),\n            get desiredSize() {\n                return writable.writableLength;\n            },\n            ready: new Promise(resolve => {\n                writable.once('drain', () => {\n                    resolve(undefined);\n                });\n            }),\n            releaseLock() {\n                // no-op\n            },\n            write(chunk) {\n                if (chunk == null) {\n                    return (0, utils_js_1.fakePromise)(undefined);\n                }\n                return new Promise((resolve, reject) => {\n                    writable.write(chunk, (err) => {\n                        if (err) {\n                            reject(err);\n                        }\n                        else {\n                            resolve();\n                        }\n                    });\n                });\n            },\n            close() {\n                if (!writable.errored && writable.closed) {\n                    return (0, utils_js_1.fakePromise)(undefined);\n                }\n                return new Promise((resolve, reject) => {\n                    if (writable.errored) {\n                        reject(writable.errored);\n                    }\n                    else {\n                        writable.end((err) => {\n                            if (err) {\n                                reject(err);\n                            }\n                            else {\n                                resolve();\n                            }\n                        });\n                    }\n                });\n            },\n            abort(reason) {\n                return new Promise(resolve => {\n                    writable.destroy(reason);\n                    writable.once('close', resolve);\n                });\n            },\n        };\n    }\n    close() {\n        if (!this.writable.errored && this.writable.closed) {\n            return (0, utils_js_1.fakePromise)(undefined);\n        }\n        return new Promise((resolve, reject) => {\n            if (this.writable.errored) {\n                reject(this.writable.errored);\n            }\n            else {\n                this.writable.end((err) => {\n                    if (err) {\n                        reject(err);\n                    }\n                    else {\n                        resolve();\n                    }\n                });\n            }\n        });\n    }\n    abort(reason) {\n        return new Promise(resolve => {\n            this.writable.destroy(reason);\n            this.writable.once('close', resolve);\n        });\n    }\n    locked = false;\n}\nexports.PonyfillWritableStream = PonyfillWritableStream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/WritableStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/fetch.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/fetch.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.fetchPonyfill = fetchPonyfill;\nconst fs_1 = __webpack_require__(/*! fs */ \"fs\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst fetchCurl_js_1 = __webpack_require__(/*! ./fetchCurl.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/fetchCurl.js\");\nconst fetchNodeHttp_js_1 = __webpack_require__(/*! ./fetchNodeHttp.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/fetchNodeHttp.js\");\nconst Request_js_1 = __webpack_require__(/*! ./Request.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Request.js\");\nconst Response_js_1 = __webpack_require__(/*! ./Response.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Response.js\");\nconst URL_js_1 = __webpack_require__(/*! ./URL.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/URL.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/utils.js\");\nconst BASE64_SUFFIX = ';base64';\nfunction getResponseForFile(url) {\n    const path = (0, url_1.fileURLToPath)(url);\n    const readable = (0, fs_1.createReadStream)(path);\n    return new Response_js_1.PonyfillResponse(readable);\n}\nfunction getResponseForDataUri(url) {\n    const [mimeType = 'text/plain', ...datas] = url.substring(5).split(',');\n    const data = decodeURIComponent(datas.join(','));\n    if (mimeType.endsWith(BASE64_SUFFIX)) {\n        const buffer = Buffer.from(data, 'base64url');\n        const realMimeType = mimeType.slice(0, -BASE64_SUFFIX.length);\n        return new Response_js_1.PonyfillResponse(buffer, {\n            status: 200,\n            statusText: 'OK',\n            headers: {\n                'content-type': realMimeType,\n            },\n        });\n    }\n    return new Response_js_1.PonyfillResponse(data, {\n        status: 200,\n        statusText: 'OK',\n        headers: {\n            'content-type': mimeType,\n        },\n    });\n}\nfunction getResponseForBlob(url) {\n    const blob = URL_js_1.PonyfillURL.getBlobFromURL(url);\n    if (!blob) {\n        throw new TypeError('Invalid Blob URL');\n    }\n    return new Response_js_1.PonyfillResponse(blob, {\n        status: 200,\n        headers: {\n            'content-type': blob.type,\n            'content-length': blob.size.toString(),\n        },\n    });\n}\nfunction isURL(obj) {\n    return obj != null && obj.href != null;\n}\nfunction fetchPonyfill(info, init) {\n    if (typeof info === 'string' || isURL(info)) {\n        const ponyfillRequest = new Request_js_1.PonyfillRequest(info, init);\n        return fetchPonyfill(ponyfillRequest);\n    }\n    const fetchRequest = info;\n    if (fetchRequest.url.startsWith('data:')) {\n        const response = getResponseForDataUri(fetchRequest.url);\n        return (0, utils_js_1.fakePromise)(response);\n    }\n    if (fetchRequest.url.startsWith('file:')) {\n        const response = getResponseForFile(fetchRequest.url);\n        return (0, utils_js_1.fakePromise)(response);\n    }\n    if (fetchRequest.url.startsWith('blob:')) {\n        const response = getResponseForBlob(fetchRequest.url);\n        return (0, utils_js_1.fakePromise)(response);\n    }\n    if (globalThis.libcurl && !fetchRequest.agent) {\n        return (0, fetchCurl_js_1.fetchCurl)(fetchRequest);\n    }\n    return (0, fetchNodeHttp_js_1.fetchNodeHttp)(fetchRequest);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/fetch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/fetchCurl.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/fetchCurl.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.fetchCurl = fetchCurl;\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst tls_1 = __webpack_require__(/*! tls */ \"tls\");\nconst Response_js_1 = __webpack_require__(/*! ./Response.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Response.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/utils.js\");\nfunction fetchCurl(fetchRequest) {\n    const { Curl, CurlFeature, CurlPause, CurlProgressFunc } = globalThis['libcurl'];\n    const curlHandle = new Curl();\n    curlHandle.enable(CurlFeature.NoDataParsing);\n    curlHandle.setOpt('URL', fetchRequest.url);\n    if (process.env.NODE_TLS_REJECT_UNAUTHORIZED === '0') {\n        curlHandle.setOpt('SSL_VERIFYPEER', false);\n    }\n    if (process.env.NODE_EXTRA_CA_CERTS) {\n        curlHandle.setOpt('CAINFO', process.env.NODE_EXTRA_CA_CERTS);\n    }\n    else {\n        curlHandle.setOpt('CAINFO_BLOB', tls_1.rootCertificates.join('\\n'));\n    }\n    curlHandle.enable(CurlFeature.StreamResponse);\n    curlHandle.setStreamProgressCallback(function () {\n        return fetchRequest['_signal']?.aborted\n            ? process.env.DEBUG\n                ? CurlProgressFunc.Continue\n                : 1\n            : 0;\n    });\n    if (fetchRequest['bodyType'] === 'String') {\n        curlHandle.setOpt('POSTFIELDS', fetchRequest['bodyInit']);\n    }\n    else {\n        const nodeReadable = (fetchRequest.body != null\n            ? (0, utils_js_1.isNodeReadable)(fetchRequest.body)\n                ? fetchRequest.body\n                : stream_1.Readable.from(fetchRequest.body)\n            : null);\n        if (nodeReadable) {\n            curlHandle.setOpt('UPLOAD', true);\n            curlHandle.setUploadStream(nodeReadable);\n        }\n    }\n    if (process.env.DEBUG) {\n        curlHandle.setOpt('VERBOSE', true);\n    }\n    curlHandle.setOpt('TRANSFER_ENCODING', false);\n    curlHandle.setOpt('HTTP_TRANSFER_DECODING', true);\n    curlHandle.setOpt('FOLLOWLOCATION', fetchRequest.redirect === 'follow');\n    curlHandle.setOpt('MAXREDIRS', 20);\n    curlHandle.setOpt('ACCEPT_ENCODING', '');\n    curlHandle.setOpt('CUSTOMREQUEST', fetchRequest.method);\n    const headersSerializer = fetchRequest.headersSerializer || utils_js_1.defaultHeadersSerializer;\n    let size;\n    const curlHeaders = headersSerializer(fetchRequest.headers, value => {\n        size = Number(value);\n    });\n    if (size != null) {\n        curlHandle.setOpt('INFILESIZE', size);\n    }\n    curlHandle.setOpt('HTTPHEADER', curlHeaders);\n    curlHandle.enable(CurlFeature.NoHeaderParsing);\n    const deferredPromise = (0, utils_js_1.createDeferredPromise)();\n    let streamResolved;\n    if (fetchRequest['_signal']) {\n        fetchRequest['_signal'].onabort = () => {\n            if (curlHandle.isOpen) {\n                try {\n                    curlHandle.pause(CurlPause.Recv);\n                }\n                catch (e) {\n                    deferredPromise.reject(e);\n                }\n            }\n        };\n    }\n    curlHandle.once('end', function endListener() {\n        try {\n            curlHandle.close();\n        }\n        catch (e) {\n            deferredPromise.reject(e);\n        }\n    });\n    curlHandle.once('error', function errorListener(error) {\n        if (streamResolved && !streamResolved.closed && !streamResolved.destroyed) {\n            streamResolved.destroy(error);\n        }\n        else {\n            if (error.message === 'Operation was aborted by an application callback') {\n                error.message = 'The operation was aborted.';\n            }\n            deferredPromise.reject(error);\n        }\n        try {\n            curlHandle.close();\n        }\n        catch (e) {\n            deferredPromise.reject(e);\n        }\n    });\n    curlHandle.once('stream', function streamListener(stream, status, headersBuf) {\n        const outputStream = new stream_1.PassThrough();\n        stream_1.promises\n            .pipeline(stream, outputStream, {\n            end: true,\n            signal: fetchRequest['_signal'] ?? undefined,\n        })\n            .then(() => {\n            if (!stream.destroyed) {\n                stream.resume();\n            }\n        })\n            .catch(deferredPromise.reject);\n        const headersFlat = headersBuf\n            .toString('utf8')\n            .split(/\\r?\\n|\\r/g)\n            .filter(headerFilter => {\n            if (headerFilter && !headerFilter.startsWith('HTTP/')) {\n                if (fetchRequest.redirect === 'error' &&\n                    (headerFilter.includes('location') || headerFilter.includes('Location'))) {\n                    if (!stream.destroyed) {\n                        stream.resume();\n                    }\n                    outputStream.destroy();\n                    deferredPromise.reject(new Error('redirect is not allowed'));\n                }\n                return true;\n            }\n            return false;\n        });\n        const headersInit = headersFlat.map(headerFlat => headerFlat.split(/:\\s(.+)/).slice(0, 2));\n        const ponyfillResponse = new Response_js_1.PonyfillResponse(outputStream, {\n            status,\n            headers: headersInit,\n            url: curlHandle.getInfo(Curl.info.REDIRECT_URL)?.toString() || fetchRequest.url,\n            redirected: Number(curlHandle.getInfo(Curl.info.REDIRECT_COUNT)) > 0,\n        });\n        deferredPromise.resolve(ponyfillResponse);\n        streamResolved = outputStream;\n    });\n    let count = 0;\n    try {\n        count = Curl.getCount();\n    }\n    catch { }\n    if (count > 0) {\n        setImmediate(() => {\n            curlHandle.perform();\n        });\n    }\n    else {\n        curlHandle.perform();\n    }\n    return deferredPromise.promise;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/fetchCurl.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/fetchNodeHttp.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/fetchNodeHttp.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.fetchNodeHttp = fetchNodeHttp;\nconst http_1 = __webpack_require__(/*! http */ \"http\");\nconst https_1 = __webpack_require__(/*! https */ \"https\");\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst zlib_1 = __webpack_require__(/*! zlib */ \"zlib\");\nconst Request_js_1 = __webpack_require__(/*! ./Request.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Request.js\");\nconst Response_js_1 = __webpack_require__(/*! ./Response.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Response.js\");\nconst URL_js_1 = __webpack_require__(/*! ./URL.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/URL.js\");\nconst utils_js_1 = __webpack_require__(/*! ./utils.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/utils.js\");\nfunction getRequestFnForProtocol(url) {\n    if (url.startsWith('http:')) {\n        return http_1.request;\n    }\n    else if (url.startsWith('https:')) {\n        return https_1.request;\n    }\n    throw new Error(`Unsupported protocol: ${url.split(':')[0] || url}`);\n}\nfunction fetchNodeHttp(fetchRequest) {\n    return new Promise((resolve, reject) => {\n        try {\n            const requestFn = getRequestFnForProtocol(fetchRequest.url);\n            const nodeReadable = (fetchRequest.body != null\n                ? (0, utils_js_1.isNodeReadable)(fetchRequest.body)\n                    ? fetchRequest.body\n                    : stream_1.Readable.from(fetchRequest.body)\n                : null);\n            const headersSerializer = fetchRequest.headersSerializer || utils_js_1.getHeadersObj;\n            const nodeHeaders = headersSerializer(fetchRequest.headers);\n            if (nodeHeaders['accept-encoding'] == null) {\n                nodeHeaders['accept-encoding'] = 'gzip, deflate, br';\n            }\n            const nodeRequest = requestFn(fetchRequest.url, {\n                method: fetchRequest.method,\n                headers: nodeHeaders,\n                signal: fetchRequest['_signal'] ?? undefined,\n                agent: fetchRequest.agent,\n            });\n            nodeRequest.once('response', nodeResponse => {\n                let outputStream;\n                const contentEncoding = nodeResponse.headers['content-encoding'];\n                switch (contentEncoding) {\n                    case 'x-gzip':\n                    case 'gzip':\n                        outputStream = (0, zlib_1.createGunzip)();\n                        break;\n                    case 'x-deflate':\n                    case 'deflate':\n                        outputStream = (0, zlib_1.createInflate)();\n                        break;\n                    case 'x-deflate-raw':\n                    case 'deflate-raw':\n                        outputStream = (0, zlib_1.createInflateRaw)();\n                        break;\n                    case 'br':\n                        outputStream = (0, zlib_1.createBrotliDecompress)();\n                        break;\n                    default:\n                        outputStream = new stream_1.PassThrough();\n                }\n                if (nodeResponse.headers.location) {\n                    if (fetchRequest.redirect === 'error') {\n                        const redirectError = new Error('Redirects are not allowed');\n                        reject(redirectError);\n                        nodeResponse.resume();\n                        return;\n                    }\n                    if (fetchRequest.redirect === 'follow') {\n                        const redirectedUrl = new URL_js_1.PonyfillURL(nodeResponse.headers.location, fetchRequest.url);\n                        const redirectResponse$ = fetchNodeHttp(new Request_js_1.PonyfillRequest(redirectedUrl, fetchRequest));\n                        resolve(redirectResponse$.then(redirectResponse => {\n                            redirectResponse.redirected = true;\n                            return redirectResponse;\n                        }));\n                        nodeResponse.resume();\n                        return;\n                    }\n                }\n                stream_1.promises\n                    .pipeline(nodeResponse, outputStream, {\n                    signal: fetchRequest['_signal'] ?? undefined,\n                    end: true,\n                })\n                    .then(() => {\n                    if (!nodeResponse.destroyed) {\n                        nodeResponse.resume();\n                    }\n                })\n                    .catch(reject);\n                const ponyfillResponse = new Response_js_1.PonyfillResponse(outputStream, {\n                    status: nodeResponse.statusCode,\n                    statusText: nodeResponse.statusMessage,\n                    headers: nodeResponse.headers,\n                    url: fetchRequest.url,\n                });\n                resolve(ponyfillResponse);\n            });\n            nodeRequest.once('error', reject);\n            if (nodeReadable) {\n                nodeReadable.pipe(nodeRequest);\n            }\n            else {\n                nodeRequest.end();\n            }\n        }\n        catch (e) {\n            reject(e);\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/fetchNodeHttp.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/index.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/index.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DecompressionStream = exports.CompressionStream = exports.TransformStream = exports.WritableStream = exports.URLSearchParams = exports.URL = exports.btoa = exports.TextDecoder = exports.TextEncoder = exports.Blob = exports.FormData = exports.File = exports.ReadableStream = exports.Response = exports.Request = exports.Body = exports.Headers = exports.fetch = void 0;\nvar fetch_js_1 = __webpack_require__(/*! ./fetch.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/fetch.js\");\nObject.defineProperty(exports, \"fetch\", ({ enumerable: true, get: function () { return fetch_js_1.fetchPonyfill; } }));\nvar Headers_js_1 = __webpack_require__(/*! ./Headers.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Headers.js\");\nObject.defineProperty(exports, \"Headers\", ({ enumerable: true, get: function () { return Headers_js_1.PonyfillHeaders; } }));\nvar Body_js_1 = __webpack_require__(/*! ./Body.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Body.js\");\nObject.defineProperty(exports, \"Body\", ({ enumerable: true, get: function () { return Body_js_1.PonyfillBody; } }));\nvar Request_js_1 = __webpack_require__(/*! ./Request.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Request.js\");\nObject.defineProperty(exports, \"Request\", ({ enumerable: true, get: function () { return Request_js_1.PonyfillRequest; } }));\nvar Response_js_1 = __webpack_require__(/*! ./Response.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Response.js\");\nObject.defineProperty(exports, \"Response\", ({ enumerable: true, get: function () { return Response_js_1.PonyfillResponse; } }));\nvar ReadableStream_js_1 = __webpack_require__(/*! ./ReadableStream.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/ReadableStream.js\");\nObject.defineProperty(exports, \"ReadableStream\", ({ enumerable: true, get: function () { return ReadableStream_js_1.PonyfillReadableStream; } }));\nvar File_js_1 = __webpack_require__(/*! ./File.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/File.js\");\nObject.defineProperty(exports, \"File\", ({ enumerable: true, get: function () { return File_js_1.PonyfillFile; } }));\nvar FormData_js_1 = __webpack_require__(/*! ./FormData.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/FormData.js\");\nObject.defineProperty(exports, \"FormData\", ({ enumerable: true, get: function () { return FormData_js_1.PonyfillFormData; } }));\nvar Blob_js_1 = __webpack_require__(/*! ./Blob.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/Blob.js\");\nObject.defineProperty(exports, \"Blob\", ({ enumerable: true, get: function () { return Blob_js_1.PonyfillBlob; } }));\nvar TextEncoderDecoder_js_1 = __webpack_require__(/*! ./TextEncoderDecoder.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/TextEncoderDecoder.js\");\nObject.defineProperty(exports, \"TextEncoder\", ({ enumerable: true, get: function () { return TextEncoderDecoder_js_1.PonyfillTextEncoder; } }));\nObject.defineProperty(exports, \"TextDecoder\", ({ enumerable: true, get: function () { return TextEncoderDecoder_js_1.PonyfillTextDecoder; } }));\nObject.defineProperty(exports, \"btoa\", ({ enumerable: true, get: function () { return TextEncoderDecoder_js_1.PonyfillBtoa; } }));\nvar URL_js_1 = __webpack_require__(/*! ./URL.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/URL.js\");\nObject.defineProperty(exports, \"URL\", ({ enumerable: true, get: function () { return URL_js_1.PonyfillURL; } }));\nvar URLSearchParams_js_1 = __webpack_require__(/*! ./URLSearchParams.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/URLSearchParams.js\");\nObject.defineProperty(exports, \"URLSearchParams\", ({ enumerable: true, get: function () { return URLSearchParams_js_1.PonyfillURLSearchParams; } }));\nvar WritableStream_js_1 = __webpack_require__(/*! ./WritableStream.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/WritableStream.js\");\nObject.defineProperty(exports, \"WritableStream\", ({ enumerable: true, get: function () { return WritableStream_js_1.PonyfillWritableStream; } }));\nvar TransformStream_js_1 = __webpack_require__(/*! ./TransformStream.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/TransformStream.js\");\nObject.defineProperty(exports, \"TransformStream\", ({ enumerable: true, get: function () { return TransformStream_js_1.PonyfillTransformStream; } }));\nvar CompressionStream_js_1 = __webpack_require__(/*! ./CompressionStream.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/CompressionStream.js\");\nObject.defineProperty(exports, \"CompressionStream\", ({ enumerable: true, get: function () { return CompressionStream_js_1.PonyfillCompressionStream; } }));\nvar DecompressionStream_js_1 = __webpack_require__(/*! ./DecompressionStream.js */ \"(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/DecompressionStream.js\");\nObject.defineProperty(exports, \"DecompressionStream\", ({ enumerable: true, get: function () { return DecompressionStream_js_1.PonyfillDecompressionStream; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/utils.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/utils.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getHeadersObj = getHeadersObj;\nexports.defaultHeadersSerializer = defaultHeadersSerializer;\nexports.fakePromise = fakePromise;\nexports.isArrayBufferView = isArrayBufferView;\nexports.isNodeReadable = isNodeReadable;\nexports.createDeferredPromise = createDeferredPromise;\nfunction isHeadersInstance(obj) {\n    return obj?.forEach != null;\n}\nfunction getHeadersObj(headers) {\n    if (headers == null || !isHeadersInstance(headers)) {\n        return headers;\n    }\n    const obj = {};\n    headers.forEach((value, key) => {\n        obj[key] = value;\n    });\n    return obj;\n}\nfunction defaultHeadersSerializer(headers, onContentLength) {\n    const headerArray = [];\n    headers.forEach((value, key) => {\n        if (onContentLength && key === 'content-length') {\n            onContentLength(value);\n        }\n        headerArray.push(`${key}: ${value}`);\n    });\n    return headerArray;\n}\nfunction isPromise(val) {\n    return val?.then != null;\n}\nfunction fakePromise(value) {\n    if (isPromise(value)) {\n        return value;\n    }\n    // Write a fake promise to avoid the promise constructor\n    // being called with `new Promise` in the browser.\n    return {\n        then(resolve) {\n            if (resolve) {\n                const callbackResult = resolve(value);\n                if (isPromise(callbackResult)) {\n                    return callbackResult;\n                }\n                return fakePromise(callbackResult);\n            }\n            return this;\n        },\n        catch() {\n            return this;\n        },\n        finally(cb) {\n            if (cb) {\n                const callbackResult = cb();\n                if (isPromise(callbackResult)) {\n                    return callbackResult.then(() => value);\n                }\n                return fakePromise(value);\n            }\n            return this;\n        },\n        [Symbol.toStringTag]: 'Promise',\n    };\n}\nfunction isArrayBufferView(obj) {\n    return obj != null && obj.buffer != null && obj.byteLength != null && obj.byteOffset != null;\n}\nfunction isNodeReadable(obj) {\n    return obj != null && obj.pipe != null;\n}\nfunction createDeferredPromise() {\n    let resolveFn;\n    let rejectFn;\n    const promise = new Promise(function deferredPromiseExecutor(resolve, reject) {\n        resolveFn = resolve;\n        rejectFn = reject;\n    });\n    return {\n        promise,\n        get resolve() {\n            return resolveFn;\n        },\n        get reject() {\n            return rejectFn;\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+node-fetch@0.5.26/node_modules/@whatwg-node/node-fetch/cjs/utils.js\n");

/***/ })

};
;