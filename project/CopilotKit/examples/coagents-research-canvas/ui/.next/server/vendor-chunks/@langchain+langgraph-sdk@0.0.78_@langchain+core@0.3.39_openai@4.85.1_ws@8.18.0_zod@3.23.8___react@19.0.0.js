"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0";
exports.ids = ["vendor-chunks/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/client.js":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/client.js ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssistantsClient: () => (/* binding */ AssistantsClient),\n/* harmony export */   Client: () => (/* binding */ Client),\n/* harmony export */   CronsClient: () => (/* binding */ CronsClient),\n/* harmony export */   RunsClient: () => (/* binding */ RunsClient),\n/* harmony export */   StoreClient: () => (/* binding */ StoreClient),\n/* harmony export */   ThreadsClient: () => (/* binding */ ThreadsClient),\n/* harmony export */   getApiKey: () => (/* binding */ getApiKey)\n/* harmony export */ });\n/* harmony import */ var _utils_async_caller_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/async_caller.js */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.js\");\n/* harmony import */ var _utils_env_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/env.js */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/env.js\");\n/* harmony import */ var _utils_signals_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/signals.js */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/signals.js\");\n/* harmony import */ var _utils_sse_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/sse.js */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/sse.js\");\n/* harmony import */ var _utils_stream_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/stream.js */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/stream.js\");\n\n\n\n\n\n/**\n * Get the API key from the environment.\n * Precedence:\n *   1. explicit argument\n *   2. LANGGRAPH_API_KEY\n *   3. LANGSMITH_API_KEY\n *   4. LANGCHAIN_API_KEY\n *\n * @param apiKey - Optional API key provided as an argument\n * @returns The API key if found, otherwise undefined\n */\nfunction getApiKey(apiKey) {\n    if (apiKey) {\n        return apiKey;\n    }\n    const prefixes = [\"LANGGRAPH\", \"LANGSMITH\", \"LANGCHAIN\"];\n    for (const prefix of prefixes) {\n        const envKey = (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_1__.getEnvironmentVariable)(`${prefix}_API_KEY`);\n        if (envKey) {\n            // Remove surrounding quotes\n            return envKey.trim().replace(/^[\"']|[\"']$/g, \"\");\n        }\n    }\n    return undefined;\n}\nconst REGEX_RUN_METADATA = /(\\/threads\\/(?<thread_id>.+))?\\/runs\\/(?<run_id>.+)/;\nfunction getRunMetadataFromResponse(response) {\n    const contentLocation = response.headers.get(\"Content-Location\");\n    if (!contentLocation)\n        return undefined;\n    const match = REGEX_RUN_METADATA.exec(contentLocation);\n    if (!match?.groups?.run_id)\n        return undefined;\n    return {\n        run_id: match.groups.run_id,\n        thread_id: match.groups.thread_id || undefined,\n    };\n}\nclass BaseClient {\n    constructor(config) {\n        Object.defineProperty(this, \"asyncCaller\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"timeoutMs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"apiUrl\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"defaultHeaders\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        const callerOptions = {\n            maxRetries: 4,\n            maxConcurrency: 4,\n            ...config?.callerOptions,\n        };\n        let defaultApiUrl = \"http://localhost:8123\";\n        if (!config?.apiUrl &&\n            typeof globalThis === \"object\" &&\n            globalThis != null) {\n            const fetchSmb = Symbol.for(\"langgraph_api:fetch\");\n            const urlSmb = Symbol.for(\"langgraph_api:url\");\n            const global = globalThis;\n            if (global[fetchSmb])\n                callerOptions.fetch ??= global[fetchSmb];\n            if (global[urlSmb])\n                defaultApiUrl = global[urlSmb];\n        }\n        this.asyncCaller = new _utils_async_caller_js__WEBPACK_IMPORTED_MODULE_0__.AsyncCaller(callerOptions);\n        this.timeoutMs = config?.timeoutMs;\n        // default limit being capped by Chrome\n        // https://github.com/nodejs/undici/issues/1373\n        // Regex to remove trailing slash, if present\n        this.apiUrl = config?.apiUrl?.replace(/\\/$/, \"\") || defaultApiUrl;\n        this.defaultHeaders = config?.defaultHeaders || {};\n        const apiKey = getApiKey(config?.apiKey);\n        if (apiKey) {\n            this.defaultHeaders[\"X-Api-Key\"] = apiKey;\n        }\n    }\n    prepareFetchOptions(path, options) {\n        const mutatedOptions = {\n            ...options,\n            headers: { ...this.defaultHeaders, ...options?.headers },\n        };\n        if (mutatedOptions.json) {\n            mutatedOptions.body = JSON.stringify(mutatedOptions.json);\n            mutatedOptions.headers = {\n                ...mutatedOptions.headers,\n                \"Content-Type\": \"application/json\",\n            };\n            delete mutatedOptions.json;\n        }\n        if (mutatedOptions.withResponse) {\n            delete mutatedOptions.withResponse;\n        }\n        let timeoutSignal = null;\n        if (typeof options?.timeoutMs !== \"undefined\") {\n            if (options.timeoutMs != null) {\n                timeoutSignal = AbortSignal.timeout(options.timeoutMs);\n            }\n        }\n        else if (this.timeoutMs != null) {\n            timeoutSignal = AbortSignal.timeout(this.timeoutMs);\n        }\n        mutatedOptions.signal = (0,_utils_signals_js__WEBPACK_IMPORTED_MODULE_2__.mergeSignals)(timeoutSignal, mutatedOptions.signal);\n        const targetUrl = new URL(`${this.apiUrl}${path}`);\n        if (mutatedOptions.params) {\n            for (const [key, value] of Object.entries(mutatedOptions.params)) {\n                if (value == null)\n                    continue;\n                let strValue = typeof value === \"string\" || typeof value === \"number\"\n                    ? value.toString()\n                    : JSON.stringify(value);\n                targetUrl.searchParams.append(key, strValue);\n            }\n            delete mutatedOptions.params;\n        }\n        return [targetUrl, mutatedOptions];\n    }\n    async fetch(path, options) {\n        const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(path, options));\n        const body = (() => {\n            if (response.status === 202 || response.status === 204) {\n                return undefined;\n            }\n            return response.json();\n        })();\n        if (options?.withResponse) {\n            return [await body, response];\n        }\n        return body;\n    }\n}\nclass CronsClient extends BaseClient {\n    /**\n     *\n     * @param threadId The ID of the thread.\n     * @param assistantId Assistant ID to use for this cron job.\n     * @param payload Payload for creating a cron job.\n     * @returns The created background run.\n     */\n    async createForThread(threadId, assistantId, payload) {\n        const json = {\n            schedule: payload?.schedule,\n            input: payload?.input,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            webhook: payload?.webhook,\n            multitask_strategy: payload?.multitaskStrategy,\n            if_not_exists: payload?.ifNotExists,\n            checkpoint_during: payload?.checkpointDuring,\n        };\n        return this.fetch(`/threads/${threadId}/runs/crons`, {\n            method: \"POST\",\n            json,\n        });\n    }\n    /**\n     *\n     * @param assistantId Assistant ID to use for this cron job.\n     * @param payload Payload for creating a cron job.\n     * @returns\n     */\n    async create(assistantId, payload) {\n        const json = {\n            schedule: payload?.schedule,\n            input: payload?.input,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            webhook: payload?.webhook,\n            multitask_strategy: payload?.multitaskStrategy,\n            if_not_exists: payload?.ifNotExists,\n            checkpoint_during: payload?.checkpointDuring,\n        };\n        return this.fetch(`/runs/crons`, {\n            method: \"POST\",\n            json,\n        });\n    }\n    /**\n     *\n     * @param cronId Cron ID of Cron job to delete.\n     */\n    async delete(cronId) {\n        await this.fetch(`/runs/crons/${cronId}`, {\n            method: \"DELETE\",\n        });\n    }\n    /**\n     *\n     * @param query Query options.\n     * @returns List of crons.\n     */\n    async search(query) {\n        return this.fetch(\"/runs/crons/search\", {\n            method: \"POST\",\n            json: {\n                assistant_id: query?.assistantId ?? undefined,\n                thread_id: query?.threadId ?? undefined,\n                limit: query?.limit ?? 10,\n                offset: query?.offset ?? 0,\n            },\n        });\n    }\n}\nclass AssistantsClient extends BaseClient {\n    /**\n     * Get an assistant by ID.\n     *\n     * @param assistantId The ID of the assistant.\n     * @returns Assistant\n     */\n    async get(assistantId) {\n        return this.fetch(`/assistants/${assistantId}`);\n    }\n    /**\n     * Get the JSON representation of the graph assigned to a runnable\n     * @param assistantId The ID of the assistant.\n     * @param options.xray Whether to include subgraphs in the serialized graph representation. If an integer value is provided, only subgraphs with a depth less than or equal to the value will be included.\n     * @returns Serialized graph\n     */\n    async getGraph(assistantId, options) {\n        return this.fetch(`/assistants/${assistantId}/graph`, {\n            params: { xray: options?.xray },\n        });\n    }\n    /**\n     * Get the state and config schema of the graph assigned to a runnable\n     * @param assistantId The ID of the assistant.\n     * @returns Graph schema\n     */\n    async getSchemas(assistantId) {\n        return this.fetch(`/assistants/${assistantId}/schemas`);\n    }\n    /**\n     * Get the schemas of an assistant by ID.\n     *\n     * @param assistantId The ID of the assistant to get the schema of.\n     * @param options Additional options for getting subgraphs, such as namespace or recursion extraction.\n     * @returns The subgraphs of the assistant.\n     */\n    async getSubgraphs(assistantId, options) {\n        if (options?.namespace) {\n            return this.fetch(`/assistants/${assistantId}/subgraphs/${options.namespace}`, { params: { recurse: options?.recurse } });\n        }\n        return this.fetch(`/assistants/${assistantId}/subgraphs`, {\n            params: { recurse: options?.recurse },\n        });\n    }\n    /**\n     * Create a new assistant.\n     * @param payload Payload for creating an assistant.\n     * @returns The created assistant.\n     */\n    async create(payload) {\n        return this.fetch(\"/assistants\", {\n            method: \"POST\",\n            json: {\n                graph_id: payload.graphId,\n                config: payload.config,\n                metadata: payload.metadata,\n                assistant_id: payload.assistantId,\n                if_exists: payload.ifExists,\n                name: payload.name,\n                description: payload.description,\n            },\n        });\n    }\n    /**\n     * Update an assistant.\n     * @param assistantId ID of the assistant.\n     * @param payload Payload for updating the assistant.\n     * @returns The updated assistant.\n     */\n    async update(assistantId, payload) {\n        return this.fetch(`/assistants/${assistantId}`, {\n            method: \"PATCH\",\n            json: {\n                graph_id: payload.graphId,\n                config: payload.config,\n                metadata: payload.metadata,\n                name: payload.name,\n                description: payload.description,\n            },\n        });\n    }\n    /**\n     * Delete an assistant.\n     *\n     * @param assistantId ID of the assistant.\n     */\n    async delete(assistantId) {\n        return this.fetch(`/assistants/${assistantId}`, {\n            method: \"DELETE\",\n        });\n    }\n    /**\n     * List assistants.\n     * @param query Query options.\n     * @returns List of assistants.\n     */\n    async search(query) {\n        return this.fetch(\"/assistants/search\", {\n            method: \"POST\",\n            json: {\n                graph_id: query?.graphId ?? undefined,\n                metadata: query?.metadata ?? undefined,\n                limit: query?.limit ?? 10,\n                offset: query?.offset ?? 0,\n                sort_by: query?.sortBy ?? undefined,\n                sort_order: query?.sortOrder ?? undefined,\n            },\n        });\n    }\n    /**\n     * List all versions of an assistant.\n     *\n     * @param assistantId ID of the assistant.\n     * @returns List of assistant versions.\n     */\n    async getVersions(assistantId, payload) {\n        return this.fetch(`/assistants/${assistantId}/versions`, {\n            method: \"POST\",\n            json: {\n                metadata: payload?.metadata ?? undefined,\n                limit: payload?.limit ?? 10,\n                offset: payload?.offset ?? 0,\n            },\n        });\n    }\n    /**\n     * Change the version of an assistant.\n     *\n     * @param assistantId ID of the assistant.\n     * @param version The version to change to.\n     * @returns The updated assistant.\n     */\n    async setLatest(assistantId, version) {\n        return this.fetch(`/assistants/${assistantId}/latest`, {\n            method: \"POST\",\n            json: { version },\n        });\n    }\n}\nclass ThreadsClient extends BaseClient {\n    /**\n     * Get a thread by ID.\n     *\n     * @param threadId ID of the thread.\n     * @returns The thread.\n     */\n    async get(threadId) {\n        return this.fetch(`/threads/${threadId}`);\n    }\n    /**\n     * Create a new thread.\n     *\n     * @param payload Payload for creating a thread.\n     * @returns The created thread.\n     */\n    async create(payload) {\n        return this.fetch(`/threads`, {\n            method: \"POST\",\n            json: {\n                metadata: {\n                    ...payload?.metadata,\n                    graph_id: payload?.graphId,\n                },\n                thread_id: payload?.threadId,\n                if_exists: payload?.ifExists,\n                supersteps: payload?.supersteps?.map((s) => ({\n                    updates: s.updates.map((u) => ({\n                        values: u.values,\n                        command: u.command,\n                        as_node: u.asNode,\n                    })),\n                })),\n            },\n        });\n    }\n    /**\n     * Copy an existing thread\n     * @param threadId ID of the thread to be copied\n     * @returns Newly copied thread\n     */\n    async copy(threadId) {\n        return this.fetch(`/threads/${threadId}/copy`, {\n            method: \"POST\",\n        });\n    }\n    /**\n     * Update a thread.\n     *\n     * @param threadId ID of the thread.\n     * @param payload Payload for updating the thread.\n     * @returns The updated thread.\n     */\n    async update(threadId, payload) {\n        return this.fetch(`/threads/${threadId}`, {\n            method: \"PATCH\",\n            json: { metadata: payload?.metadata },\n        });\n    }\n    /**\n     * Delete a thread.\n     *\n     * @param threadId ID of the thread.\n     */\n    async delete(threadId) {\n        return this.fetch(`/threads/${threadId}`, {\n            method: \"DELETE\",\n        });\n    }\n    /**\n     * List threads\n     *\n     * @param query Query options\n     * @returns List of threads\n     */\n    async search(query) {\n        return this.fetch(\"/threads/search\", {\n            method: \"POST\",\n            json: {\n                metadata: query?.metadata ?? undefined,\n                limit: query?.limit ?? 10,\n                offset: query?.offset ?? 0,\n                status: query?.status,\n                sort_by: query?.sortBy,\n                sort_order: query?.sortOrder,\n            },\n        });\n    }\n    /**\n     * Get state for a thread.\n     *\n     * @param threadId ID of the thread.\n     * @returns Thread state.\n     */\n    async getState(threadId, checkpoint, options) {\n        if (checkpoint != null) {\n            if (typeof checkpoint !== \"string\") {\n                return this.fetch(`/threads/${threadId}/state/checkpoint`, {\n                    method: \"POST\",\n                    json: { checkpoint, subgraphs: options?.subgraphs },\n                });\n            }\n            // deprecated\n            return this.fetch(`/threads/${threadId}/state/${checkpoint}`, { params: { subgraphs: options?.subgraphs } });\n        }\n        return this.fetch(`/threads/${threadId}/state`, {\n            params: { subgraphs: options?.subgraphs },\n        });\n    }\n    /**\n     * Add state to a thread.\n     *\n     * @param threadId The ID of the thread.\n     * @returns\n     */\n    async updateState(threadId, options) {\n        return this.fetch(`/threads/${threadId}/state`, {\n            method: \"POST\",\n            json: {\n                values: options.values,\n                checkpoint_id: options.checkpointId,\n                checkpoint: options.checkpoint,\n                as_node: options?.asNode,\n            },\n        });\n    }\n    /**\n     * Patch the metadata of a thread.\n     *\n     * @param threadIdOrConfig Thread ID or config to patch the state of.\n     * @param metadata Metadata to patch the state with.\n     */\n    async patchState(threadIdOrConfig, metadata) {\n        let threadId;\n        if (typeof threadIdOrConfig !== \"string\") {\n            if (typeof threadIdOrConfig.configurable?.thread_id !== \"string\") {\n                throw new Error(\"Thread ID is required when updating state with a config.\");\n            }\n            threadId = threadIdOrConfig.configurable.thread_id;\n        }\n        else {\n            threadId = threadIdOrConfig;\n        }\n        return this.fetch(`/threads/${threadId}/state`, {\n            method: \"PATCH\",\n            json: { metadata: metadata },\n        });\n    }\n    /**\n     * Get all past states for a thread.\n     *\n     * @param threadId ID of the thread.\n     * @param options Additional options.\n     * @returns List of thread states.\n     */\n    async getHistory(threadId, options) {\n        return this.fetch(`/threads/${threadId}/history`, {\n            method: \"POST\",\n            json: {\n                limit: options?.limit ?? 10,\n                before: options?.before,\n                metadata: options?.metadata,\n                checkpoint: options?.checkpoint,\n            },\n        });\n    }\n}\nclass RunsClient extends BaseClient {\n    /**\n     * Create a run and stream the results.\n     *\n     * @param threadId The ID of the thread.\n     * @param assistantId Assistant ID to use for this run.\n     * @param payload Payload for creating a run.\n     */\n    async *stream(threadId, assistantId, payload) {\n        const json = {\n            input: payload?.input,\n            command: payload?.command,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            stream_mode: payload?.streamMode,\n            stream_subgraphs: payload?.streamSubgraphs,\n            stream_resumable: payload?.streamResumable,\n            feedback_keys: payload?.feedbackKeys,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            checkpoint: payload?.checkpoint,\n            checkpoint_id: payload?.checkpointId,\n            webhook: payload?.webhook,\n            multitask_strategy: payload?.multitaskStrategy,\n            on_completion: payload?.onCompletion,\n            on_disconnect: payload?.onDisconnect,\n            after_seconds: payload?.afterSeconds,\n            if_not_exists: payload?.ifNotExists,\n            checkpoint_during: payload?.checkpointDuring,\n        };\n        const endpoint = threadId == null ? `/runs/stream` : `/threads/${threadId}/runs/stream`;\n        const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(endpoint, {\n            method: \"POST\",\n            json,\n            timeoutMs: null,\n            signal: payload?.signal,\n        }));\n        const runMetadata = getRunMetadataFromResponse(response);\n        if (runMetadata)\n            payload?.onRunCreated?.(runMetadata);\n        const stream = (response.body || new ReadableStream({ start: (ctrl) => ctrl.close() }))\n            .pipeThrough(new _utils_sse_js__WEBPACK_IMPORTED_MODULE_3__.BytesLineDecoder())\n            .pipeThrough(new _utils_sse_js__WEBPACK_IMPORTED_MODULE_3__.SSEDecoder());\n        yield* _utils_stream_js__WEBPACK_IMPORTED_MODULE_4__.IterableReadableStream.fromReadableStream(stream);\n    }\n    /**\n     * Create a run.\n     *\n     * @param threadId The ID of the thread.\n     * @param assistantId Assistant ID to use for this run.\n     * @param payload Payload for creating a run.\n     * @returns The created run.\n     */\n    async create(threadId, assistantId, payload) {\n        const json = {\n            input: payload?.input,\n            command: payload?.command,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            stream_mode: payload?.streamMode,\n            stream_subgraphs: payload?.streamSubgraphs,\n            stream_resumable: payload?.streamResumable,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            webhook: payload?.webhook,\n            checkpoint: payload?.checkpoint,\n            checkpoint_id: payload?.checkpointId,\n            multitask_strategy: payload?.multitaskStrategy,\n            after_seconds: payload?.afterSeconds,\n            if_not_exists: payload?.ifNotExists,\n            checkpoint_during: payload?.checkpointDuring,\n        };\n        const [run, response] = await this.fetch(`/threads/${threadId}/runs`, {\n            method: \"POST\",\n            json,\n            signal: payload?.signal,\n            withResponse: true,\n        });\n        const runMetadata = getRunMetadataFromResponse(response);\n        if (runMetadata)\n            payload?.onRunCreated?.(runMetadata);\n        return run;\n    }\n    /**\n     * Create a batch of stateless background runs.\n     *\n     * @param payloads An array of payloads for creating runs.\n     * @returns An array of created runs.\n     */\n    async createBatch(payloads) {\n        const filteredPayloads = payloads\n            .map((payload) => ({ ...payload, assistant_id: payload.assistantId }))\n            .map((payload) => {\n            return Object.fromEntries(Object.entries(payload).filter(([_, v]) => v !== undefined));\n        });\n        return this.fetch(\"/runs/batch\", {\n            method: \"POST\",\n            json: filteredPayloads,\n        });\n    }\n    /**\n     * Create a run and wait for it to complete.\n     *\n     * @param threadId The ID of the thread.\n     * @param assistantId Assistant ID to use for this run.\n     * @param payload Payload for creating a run.\n     * @returns The last values chunk of the thread.\n     */\n    async wait(threadId, assistantId, payload) {\n        const json = {\n            input: payload?.input,\n            command: payload?.command,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            checkpoint: payload?.checkpoint,\n            checkpoint_id: payload?.checkpointId,\n            webhook: payload?.webhook,\n            multitask_strategy: payload?.multitaskStrategy,\n            on_completion: payload?.onCompletion,\n            on_disconnect: payload?.onDisconnect,\n            after_seconds: payload?.afterSeconds,\n            if_not_exists: payload?.ifNotExists,\n            checkpoint_during: payload?.checkpointDuring,\n        };\n        const endpoint = threadId == null ? `/runs/wait` : `/threads/${threadId}/runs/wait`;\n        const [run, response] = await this.fetch(endpoint, {\n            method: \"POST\",\n            json,\n            timeoutMs: null,\n            signal: payload?.signal,\n            withResponse: true,\n        });\n        const runMetadata = getRunMetadataFromResponse(response);\n        if (runMetadata)\n            payload?.onRunCreated?.(runMetadata);\n        const raiseError = payload?.raiseError !== undefined ? payload.raiseError : true;\n        if (raiseError &&\n            \"__error__\" in run &&\n            typeof run.__error__ === \"object\" &&\n            run.__error__ &&\n            \"error\" in run.__error__ &&\n            \"message\" in run.__error__) {\n            throw new Error(`${run.__error__?.error}: ${run.__error__?.message}`);\n        }\n        return run;\n    }\n    /**\n     * List all runs for a thread.\n     *\n     * @param threadId The ID of the thread.\n     * @param options Filtering and pagination options.\n     * @returns List of runs.\n     */\n    async list(threadId, options) {\n        return this.fetch(`/threads/${threadId}/runs`, {\n            params: {\n                limit: options?.limit ?? 10,\n                offset: options?.offset ?? 0,\n                status: options?.status ?? undefined,\n            },\n        });\n    }\n    /**\n     * Get a run by ID.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @returns The run.\n     */\n    async get(threadId, runId) {\n        return this.fetch(`/threads/${threadId}/runs/${runId}`);\n    }\n    /**\n     * Cancel a run.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @param wait Whether to block when canceling\n     * @param action Action to take when cancelling the run. Possible values are `interrupt` or `rollback`. Default is `interrupt`.\n     * @returns\n     */\n    async cancel(threadId, runId, wait = false, action = \"interrupt\") {\n        return this.fetch(`/threads/${threadId}/runs/${runId}/cancel`, {\n            method: \"POST\",\n            params: {\n                wait: wait ? \"1\" : \"0\",\n                action: action,\n            },\n        });\n    }\n    /**\n     * Block until a run is done.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @returns\n     */\n    async join(threadId, runId, options) {\n        return this.fetch(`/threads/${threadId}/runs/${runId}/join`, {\n            timeoutMs: null,\n            signal: options?.signal,\n        });\n    }\n    /**\n     * Stream output from a run in real-time, until the run is done.\n     *\n     * @param threadId The ID of the thread. Can be set to `null` | `undefined` for stateless runs.\n     * @param runId The ID of the run.\n     * @param options Additional options for controlling the stream behavior:\n     *   - signal: An AbortSignal that can be used to cancel the stream request\n     *   - lastEventId: The ID of the last event received. Can be used to reconnect to a stream without losing events.\n     *   - cancelOnDisconnect: When true, automatically cancels the run if the client disconnects from the stream\n     *   - streamMode: Controls what types of events to receive from the stream (can be a single mode or array of modes)\n     *        Must be a subset of the stream modes passed when creating the run. Background runs default to having the union of all\n     *        stream modes enabled.\n     * @returns An async generator yielding stream parts.\n     */\n    async *joinStream(threadId, runId, options) {\n        const opts = typeof options === \"object\" &&\n            options != null &&\n            options instanceof AbortSignal\n            ? { signal: options }\n            : options;\n        const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(threadId != null\n            ? `/threads/${threadId}/runs/${runId}/stream`\n            : `/runs/${runId}/stream`, {\n            method: \"GET\",\n            timeoutMs: null,\n            signal: opts?.signal,\n            headers: opts?.lastEventId\n                ? { \"Last-Event-ID\": opts.lastEventId }\n                : undefined,\n            params: {\n                cancel_on_disconnect: opts?.cancelOnDisconnect ? \"1\" : \"0\",\n                stream_mode: opts?.streamMode,\n            },\n        }));\n        const stream = (response.body || new ReadableStream({ start: (ctrl) => ctrl.close() }))\n            .pipeThrough(new _utils_sse_js__WEBPACK_IMPORTED_MODULE_3__.BytesLineDecoder())\n            .pipeThrough(new _utils_sse_js__WEBPACK_IMPORTED_MODULE_3__.SSEDecoder());\n        yield* _utils_stream_js__WEBPACK_IMPORTED_MODULE_4__.IterableReadableStream.fromReadableStream(stream);\n    }\n    /**\n     * Delete a run.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @returns\n     */\n    async delete(threadId, runId) {\n        return this.fetch(`/threads/${threadId}/runs/${runId}`, {\n            method: \"DELETE\",\n        });\n    }\n}\nclass StoreClient extends BaseClient {\n    /**\n     * Store or update an item.\n     *\n     * @param namespace A list of strings representing the namespace path.\n     * @param key The unique identifier for the item within the namespace.\n     * @param value A dictionary containing the item's data.\n     * @param options.index Controls search indexing - null (use defaults), false (disable), or list of field paths to index.\n     * @param options.ttl Optional time-to-live in minutes for the item, or null for no expiration.\n     * @returns Promise<void>\n     *\n     * @example\n     * ```typescript\n     * await client.store.putItem(\n     *   [\"documents\", \"user123\"],\n     *   \"item456\",\n     *   { title: \"My Document\", content: \"Hello World\" },\n     *   { ttl: 60 } // expires in 60 minutes\n     * );\n     * ```\n     */\n    async putItem(namespace, key, value, options) {\n        namespace.forEach((label) => {\n            if (label.includes(\".\")) {\n                throw new Error(`Invalid namespace label '${label}'. Namespace labels cannot contain periods ('.')`);\n            }\n        });\n        const payload = {\n            namespace,\n            key,\n            value,\n            index: options?.index,\n            ttl: options?.ttl,\n        };\n        return this.fetch(\"/store/items\", {\n            method: \"PUT\",\n            json: payload,\n        });\n    }\n    /**\n     * Retrieve a single item.\n     *\n     * @param namespace A list of strings representing the namespace path.\n     * @param key The unique identifier for the item.\n     * @param options.refreshTtl Whether to refresh the TTL on this read operation. If null, uses the store's default behavior.\n     * @returns Promise<Item>\n     *\n     * @example\n     * ```typescript\n     * const item = await client.store.getItem(\n     *   [\"documents\", \"user123\"],\n     *   \"item456\",\n     *   { refreshTtl: true }\n     * );\n     * console.log(item);\n     * // {\n     * //   namespace: [\"documents\", \"user123\"],\n     * //   key: \"item456\",\n     * //   value: { title: \"My Document\", content: \"Hello World\" },\n     * //   createdAt: \"2024-07-30T12:00:00Z\",\n     * //   updatedAt: \"2024-07-30T12:00:00Z\"\n     * // }\n     * ```\n     */\n    async getItem(namespace, key, options) {\n        namespace.forEach((label) => {\n            if (label.includes(\".\")) {\n                throw new Error(`Invalid namespace label '${label}'. Namespace labels cannot contain periods ('.')`);\n            }\n        });\n        const params = {\n            namespace: namespace.join(\".\"),\n            key,\n        };\n        if (options?.refreshTtl !== undefined) {\n            params.refresh_ttl = options.refreshTtl;\n        }\n        const response = await this.fetch(\"/store/items\", {\n            params,\n        });\n        return response\n            ? {\n                ...response,\n                createdAt: response.created_at,\n                updatedAt: response.updated_at,\n            }\n            : null;\n    }\n    /**\n     * Delete an item.\n     *\n     * @param namespace A list of strings representing the namespace path.\n     * @param key The unique identifier for the item.\n     * @returns Promise<void>\n     */\n    async deleteItem(namespace, key) {\n        namespace.forEach((label) => {\n            if (label.includes(\".\")) {\n                throw new Error(`Invalid namespace label '${label}'. Namespace labels cannot contain periods ('.')`);\n            }\n        });\n        return this.fetch(\"/store/items\", {\n            method: \"DELETE\",\n            json: { namespace, key },\n        });\n    }\n    /**\n     * Search for items within a namespace prefix.\n     *\n     * @param namespacePrefix List of strings representing the namespace prefix.\n     * @param options.filter Optional dictionary of key-value pairs to filter results.\n     * @param options.limit Maximum number of items to return (default is 10).\n     * @param options.offset Number of items to skip before returning results (default is 0).\n     * @param options.query Optional search query.\n     * @param options.refreshTtl Whether to refresh the TTL on items returned by this search. If null, uses the store's default behavior.\n     * @returns Promise<SearchItemsResponse>\n     *\n     * @example\n     * ```typescript\n     * const results = await client.store.searchItems(\n     *   [\"documents\"],\n     *   {\n     *     filter: { author: \"John Doe\" },\n     *     limit: 5,\n     *     refreshTtl: true\n     *   }\n     * );\n     * console.log(results);\n     * // {\n     * //   items: [\n     * //     {\n     * //       namespace: [\"documents\", \"user123\"],\n     * //       key: \"item789\",\n     * //       value: { title: \"Another Document\", author: \"John Doe\" },\n     * //       createdAt: \"2024-07-30T12:00:00Z\",\n     * //       updatedAt: \"2024-07-30T12:00:00Z\"\n     * //     },\n     * //     // ... additional items ...\n     * //   ]\n     * // }\n     * ```\n     */\n    async searchItems(namespacePrefix, options) {\n        const payload = {\n            namespace_prefix: namespacePrefix,\n            filter: options?.filter,\n            limit: options?.limit ?? 10,\n            offset: options?.offset ?? 0,\n            query: options?.query,\n            refresh_ttl: options?.refreshTtl,\n        };\n        const response = await this.fetch(\"/store/items/search\", {\n            method: \"POST\",\n            json: payload,\n        });\n        return {\n            items: response.items.map((item) => ({\n                ...item,\n                createdAt: item.created_at,\n                updatedAt: item.updated_at,\n            })),\n        };\n    }\n    /**\n     * List namespaces with optional match conditions.\n     *\n     * @param options.prefix Optional list of strings representing the prefix to filter namespaces.\n     * @param options.suffix Optional list of strings representing the suffix to filter namespaces.\n     * @param options.maxDepth Optional integer specifying the maximum depth of namespaces to return.\n     * @param options.limit Maximum number of namespaces to return (default is 100).\n     * @param options.offset Number of namespaces to skip before returning results (default is 0).\n     * @returns Promise<ListNamespaceResponse>\n     */\n    async listNamespaces(options) {\n        const payload = {\n            prefix: options?.prefix,\n            suffix: options?.suffix,\n            max_depth: options?.maxDepth,\n            limit: options?.limit ?? 100,\n            offset: options?.offset ?? 0,\n        };\n        return this.fetch(\"/store/namespaces\", {\n            method: \"POST\",\n            json: payload,\n        });\n    }\n}\nclass UiClient extends BaseClient {\n    static getOrCached(key, fn) {\n        if (UiClient.promiseCache[key] != null) {\n            return UiClient.promiseCache[key];\n        }\n        const promise = fn();\n        UiClient.promiseCache[key] = promise;\n        return promise;\n    }\n    async getComponent(assistantId, agentName) {\n        return UiClient[\"getOrCached\"](`${this.apiUrl}-${assistantId}-${agentName}`, async () => {\n            const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(`/ui/${assistantId}`, {\n                headers: {\n                    Accept: \"text/html\",\n                    \"Content-Type\": \"application/json\",\n                },\n                method: \"POST\",\n                json: { name: agentName },\n            }));\n            return response.text();\n        });\n    }\n}\nObject.defineProperty(UiClient, \"promiseCache\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: {}\n});\nclass Client {\n    constructor(config) {\n        /**\n         * The client for interacting with assistants.\n         */\n        Object.defineProperty(this, \"assistants\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with threads.\n         */\n        Object.defineProperty(this, \"threads\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with runs.\n         */\n        Object.defineProperty(this, \"runs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with cron runs.\n         */\n        Object.defineProperty(this, \"crons\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with the KV store.\n         */\n        Object.defineProperty(this, \"store\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with the UI.\n         * @internal Used by LoadExternalComponent and the API might change in the future.\n         */\n        Object.defineProperty(this, \"~ui\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.assistants = new AssistantsClient(config);\n        this.threads = new ThreadsClient(config);\n        this.runs = new RunsClient(config);\n        this.crons = new CronsClient(config);\n        this.store = new StoreClient(config);\n        this[\"~ui\"] = new UiClient(config);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/index.js":
/*!*****************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/index.js ***!
  \*****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* reexport safe */ _client_js__WEBPACK_IMPORTED_MODULE_0__.Client),\n/* harmony export */   overrideFetchImplementation: () => (/* reexport safe */ _singletons_fetch_js__WEBPACK_IMPORTED_MODULE_1__.overrideFetchImplementation)\n/* harmony export */ });\n/* harmony import */ var _client_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client.js */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/client.js\");\n/* harmony import */ var _singletons_fetch_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./singletons/fetch.js */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuMC43OF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vbGFuZ2dyYXBoLXNkay9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBcUM7QUFDK0IiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AbGFuZ2NoYWluK2xhbmdncmFwaC1zZGtAMC4wLjc4X0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvQGxhbmdjaGFpbi9sYW5nZ3JhcGgtc2RrL2Rpc3QvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgQ2xpZW50IH0gZnJvbSBcIi4vY2xpZW50LmpzXCI7XG5leHBvcnQgeyBvdmVycmlkZUZldGNoSW1wbGVtZW50YXRpb24gfSBmcm9tIFwiLi9zaW5nbGV0b25zL2ZldGNoLmpzXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.js":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.js ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _getFetchImplementation: () => (/* binding */ _getFetchImplementation),\n/* harmony export */   overrideFetchImplementation: () => (/* binding */ overrideFetchImplementation)\n/* harmony export */ });\n// Wrap the default fetch call due to issues with illegal invocations\n// in some environments:\n// https://stackoverflow.com/questions/69876859/why-does-bind-fix-failed-to-execute-fetch-on-window-illegal-invocation-err\n// @ts-expect-error Broad typing to support a range of fetch implementations\nconst DEFAULT_FETCH_IMPLEMENTATION = (...args) => fetch(...args);\nconst LANGSMITH_FETCH_IMPLEMENTATION_KEY = Symbol.for(\"lg:fetch_implementation\");\n/**\n * Overrides the fetch implementation used for LangSmith calls.\n * You should use this if you need to use an implementation of fetch\n * other than the default global (e.g. for dealing with proxies).\n * @param fetch The new fetch function to use.\n */\nconst overrideFetchImplementation = (fetch) => {\n    globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] = fetch;\n};\n/**\n * @internal\n */\nconst _getFetchImplementation = () => {\n    return (globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] ??\n        DEFAULT_FETCH_IMPLEMENTATION);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.js":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.js ***!
  \******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsyncCaller: () => (/* binding */ AsyncCaller)\n/* harmony export */ });\n/* harmony import */ var p_retry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! p-retry */ \"(rsc)/./node_modules/.pnpm/p-retry@4.6.2/node_modules/p-retry/index.js\");\n/* harmony import */ var p_queue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! p-queue */ \"(rsc)/./node_modules/.pnpm/p-queue@6.6.2/node_modules/p-queue/dist/index.js\");\n/* harmony import */ var _singletons_fetch_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../singletons/fetch.js */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.js\");\n\n\n\nconst STATUS_NO_RETRY = [\n    400, // Bad Request\n    401, // Unauthorized\n    402, // Payment required\n    403, // Forbidden\n    404, // Not Found\n    405, // Method Not Allowed\n    406, // Not Acceptable\n    407, // Proxy Authentication Required\n    408, // Request Timeout\n    409, // Conflict\n    422, // Unprocessable Entity\n];\n/**\n * Do not rely on globalThis.Response, rather just\n * do duck typing\n */\nfunction isResponse(x) {\n    if (x == null || typeof x !== \"object\")\n        return false;\n    return \"status\" in x && \"statusText\" in x && \"text\" in x;\n}\n/**\n * Utility error to properly handle failed requests\n */\nclass HTTPError extends Error {\n    constructor(status, message, response) {\n        super(`HTTP ${status}: ${message}`);\n        Object.defineProperty(this, \"status\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"text\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"response\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.status = status;\n        this.text = message;\n        this.response = response;\n    }\n    static async fromResponse(response, options) {\n        try {\n            return new HTTPError(response.status, await response.text(), options?.includeResponse ? response : undefined);\n        }\n        catch {\n            return new HTTPError(response.status, response.statusText, options?.includeResponse ? response : undefined);\n        }\n    }\n}\n/**\n * A class that can be used to make async calls with concurrency and retry logic.\n *\n * This is useful for making calls to any kind of \"expensive\" external resource,\n * be it because it's rate-limited, subject to network issues, etc.\n *\n * Concurrent calls are limited by the `maxConcurrency` parameter, which defaults\n * to `Infinity`. This means that by default, all calls will be made in parallel.\n *\n * Retries are limited by the `maxRetries` parameter, which defaults to 5. This\n * means that by default, each call will be retried up to 5 times, with an\n * exponential backoff between each attempt.\n */\nclass AsyncCaller {\n    constructor(params) {\n        Object.defineProperty(this, \"maxConcurrency\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"maxRetries\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"queue\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"onFailedResponseHook\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"customFetch\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.maxConcurrency = params.maxConcurrency ?? Infinity;\n        this.maxRetries = params.maxRetries ?? 4;\n        if ( true) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.queue = new p_queue__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n                concurrency: this.maxConcurrency,\n            });\n        }\n        else {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.queue = new p_queue__WEBPACK_IMPORTED_MODULE_1__({ concurrency: this.maxConcurrency });\n        }\n        this.onFailedResponseHook = params?.onFailedResponseHook;\n        this.customFetch = params.fetch;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    call(callable, ...args) {\n        const onFailedResponseHook = this.onFailedResponseHook;\n        return this.queue.add(() => p_retry__WEBPACK_IMPORTED_MODULE_0__(() => callable(...args).catch(async (error) => {\n            // eslint-disable-next-line no-instanceof/no-instanceof\n            if (error instanceof Error) {\n                throw error;\n            }\n            else if (isResponse(error)) {\n                throw await HTTPError.fromResponse(error, {\n                    includeResponse: !!onFailedResponseHook,\n                });\n            }\n            else {\n                throw new Error(error);\n            }\n        }), {\n            async onFailedAttempt(error) {\n                if (error.message.startsWith(\"Cancel\") ||\n                    error.message.startsWith(\"TimeoutError\") ||\n                    error.message.startsWith(\"AbortError\")) {\n                    throw error;\n                }\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                if (error?.code === \"ECONNABORTED\") {\n                    throw error;\n                }\n                if (error instanceof HTTPError) {\n                    if (STATUS_NO_RETRY.includes(error.status)) {\n                        throw error;\n                    }\n                    if (onFailedResponseHook && error.response) {\n                        await onFailedResponseHook(error.response);\n                    }\n                }\n            },\n            // If needed we can change some of the defaults here,\n            // but they're quite sensible.\n            retries: this.maxRetries,\n            randomize: true,\n        }), { throwOnTimeout: true });\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    callWithOptions(options, callable, ...args) {\n        // Note this doesn't cancel the underlying request,\n        // when available prefer to use the signal option of the underlying call\n        if (options.signal) {\n            return Promise.race([\n                this.call(callable, ...args),\n                new Promise((_, reject) => {\n                    options.signal?.addEventListener(\"abort\", () => {\n                        reject(new Error(\"AbortError\"));\n                    });\n                }),\n            ]);\n        }\n        return this.call(callable, ...args);\n    }\n    fetch(...args) {\n        const fetchFn = this.customFetch ?? (0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_2__._getFetchImplementation)();\n        return this.call(() => fetchFn(...args).then((res) => (res.ok ? res : Promise.reject(res))));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/env.js":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/env.js ***!
  \*********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEnvironmentVariable: () => (/* binding */ getEnvironmentVariable)\n/* harmony export */ });\nfunction getEnvironmentVariable(name) {\n    // Certain setups (Deno, frontend) will throw an error if you try to access environment variables\n    try {\n        return typeof process !== \"undefined\"\n            ? // eslint-disable-next-line no-process-env\n                process.env?.[name]\n            : undefined;\n    }\n    catch (e) {\n        return undefined;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuMC43OF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vbGFuZ2dyYXBoLXNkay9kaXN0L3V0aWxzL2Vudi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuMC43OF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vbGFuZ2dyYXBoLXNkay9kaXN0L3V0aWxzL2Vudi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZ2V0RW52aXJvbm1lbnRWYXJpYWJsZShuYW1lKSB7XG4gICAgLy8gQ2VydGFpbiBzZXR1cHMgKERlbm8sIGZyb250ZW5kKSB3aWxsIHRocm93IGFuIGVycm9yIGlmIHlvdSB0cnkgdG8gYWNjZXNzIGVudmlyb25tZW50IHZhcmlhYmxlc1xuICAgIHRyeSB7XG4gICAgICAgIHJldHVybiB0eXBlb2YgcHJvY2VzcyAhPT0gXCJ1bmRlZmluZWRcIlxuICAgICAgICAgICAgPyAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tcHJvY2Vzcy1lbnZcbiAgICAgICAgICAgICAgICBwcm9jZXNzLmVudj8uW25hbWVdXG4gICAgICAgICAgICA6IHVuZGVmaW5lZDtcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/env.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/signals.js":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/signals.js ***!
  \*************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mergeSignals: () => (/* binding */ mergeSignals)\n/* harmony export */ });\nfunction mergeSignals(...signals) {\n    const nonZeroSignals = signals.filter((signal) => signal != null);\n    if (nonZeroSignals.length === 0)\n        return undefined;\n    if (nonZeroSignals.length === 1)\n        return nonZeroSignals[0];\n    const controller = new AbortController();\n    for (const signal of signals) {\n        if (signal?.aborted) {\n            controller.abort(signal.reason);\n            return controller.signal;\n        }\n        signal?.addEventListener(\"abort\", () => controller.abort(signal.reason), {\n            once: true,\n        });\n    }\n    return controller.signal;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuMC43OF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vbGFuZ2dyYXBoLXNkay9kaXN0L3V0aWxzL3NpZ25hbHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuMC43OF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vbGFuZ2dyYXBoLXNkay9kaXN0L3V0aWxzL3NpZ25hbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIG1lcmdlU2lnbmFscyguLi5zaWduYWxzKSB7XG4gICAgY29uc3Qgbm9uWmVyb1NpZ25hbHMgPSBzaWduYWxzLmZpbHRlcigoc2lnbmFsKSA9PiBzaWduYWwgIT0gbnVsbCk7XG4gICAgaWYgKG5vblplcm9TaWduYWxzLmxlbmd0aCA9PT0gMClcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICBpZiAobm9uWmVyb1NpZ25hbHMubGVuZ3RoID09PSAxKVxuICAgICAgICByZXR1cm4gbm9uWmVyb1NpZ25hbHNbMF07XG4gICAgY29uc3QgY29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAgICBmb3IgKGNvbnN0IHNpZ25hbCBvZiBzaWduYWxzKSB7XG4gICAgICAgIGlmIChzaWduYWw/LmFib3J0ZWQpIHtcbiAgICAgICAgICAgIGNvbnRyb2xsZXIuYWJvcnQoc2lnbmFsLnJlYXNvbik7XG4gICAgICAgICAgICByZXR1cm4gY29udHJvbGxlci5zaWduYWw7XG4gICAgICAgIH1cbiAgICAgICAgc2lnbmFsPy5hZGRFdmVudExpc3RlbmVyKFwiYWJvcnRcIiwgKCkgPT4gY29udHJvbGxlci5hYm9ydChzaWduYWwucmVhc29uKSwge1xuICAgICAgICAgICAgb25jZTogdHJ1ZSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHJldHVybiBjb250cm9sbGVyLnNpZ25hbDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/signals.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/sse.js":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/sse.js ***!
  \*********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BytesLineDecoder: () => (/* binding */ BytesLineDecoder),\n/* harmony export */   SSEDecoder: () => (/* binding */ SSEDecoder)\n/* harmony export */ });\nconst CR = \"\\r\".charCodeAt(0);\nconst LF = \"\\n\".charCodeAt(0);\nconst NULL = \"\\0\".charCodeAt(0);\nconst COLON = \":\".charCodeAt(0);\nconst SPACE = \" \".charCodeAt(0);\nconst TRAILING_NEWLINE = [CR, LF];\nclass BytesLineDecoder extends TransformStream {\n    constructor() {\n        let buffer = [];\n        let trailingCr = false;\n        super({\n            start() {\n                buffer = [];\n                trailingCr = false;\n            },\n            transform(chunk, controller) {\n                // See https://docs.python.org/3/glossary.html#term-universal-newlines\n                let text = chunk;\n                // Handle trailing CR from previous chunk\n                if (trailingCr) {\n                    text = joinArrays([[CR], text]);\n                    trailingCr = false;\n                }\n                // Check for trailing CR in current chunk\n                if (text.length > 0 && text.at(-1) === CR) {\n                    trailingCr = true;\n                    text = text.subarray(0, -1);\n                }\n                if (!text.length)\n                    return;\n                const trailingNewline = TRAILING_NEWLINE.includes(text.at(-1));\n                const lastIdx = text.length - 1;\n                const { lines } = text.reduce((acc, cur, idx) => {\n                    if (acc.from > idx)\n                        return acc;\n                    if (cur === CR || cur === LF) {\n                        acc.lines.push(text.subarray(acc.from, idx));\n                        if (cur === CR && text[idx + 1] === LF) {\n                            acc.from = idx + 2;\n                        }\n                        else {\n                            acc.from = idx + 1;\n                        }\n                    }\n                    if (idx === lastIdx && acc.from <= lastIdx) {\n                        acc.lines.push(text.subarray(acc.from));\n                    }\n                    return acc;\n                }, { lines: [], from: 0 });\n                if (lines.length === 1 && !trailingNewline) {\n                    buffer.push(lines[0]);\n                    return;\n                }\n                if (buffer.length) {\n                    // Include existing buffer in first line\n                    buffer.push(lines[0]);\n                    lines[0] = joinArrays(buffer);\n                    buffer = [];\n                }\n                if (!trailingNewline) {\n                    // If the last segment is not newline terminated,\n                    // buffer it for the next chunk\n                    if (lines.length)\n                        buffer = [lines.pop()];\n                }\n                // Enqueue complete lines\n                for (const line of lines) {\n                    controller.enqueue(line);\n                }\n            },\n            flush(controller) {\n                if (buffer.length) {\n                    controller.enqueue(joinArrays(buffer));\n                }\n            },\n        });\n    }\n}\nclass SSEDecoder extends TransformStream {\n    constructor() {\n        let event = \"\";\n        let data = [];\n        let lastEventId = \"\";\n        let retry = null;\n        const decoder = new TextDecoder();\n        super({\n            transform(chunk, controller) {\n                // Handle empty line case\n                if (!chunk.length) {\n                    if (!event && !data.length && !lastEventId && retry == null)\n                        return;\n                    const sse = {\n                        id: lastEventId || undefined,\n                        event,\n                        data: data.length ? decodeArraysToJson(decoder, data) : null,\n                    };\n                    // NOTE: as per the SSE spec, do not reset lastEventId\n                    event = \"\";\n                    data = [];\n                    retry = null;\n                    controller.enqueue(sse);\n                    return;\n                }\n                // Ignore comments\n                if (chunk[0] === COLON)\n                    return;\n                const sepIdx = chunk.indexOf(COLON);\n                if (sepIdx === -1)\n                    return;\n                const fieldName = decoder.decode(chunk.subarray(0, sepIdx));\n                let value = chunk.subarray(sepIdx + 1);\n                if (value[0] === SPACE)\n                    value = value.subarray(1);\n                if (fieldName === \"event\") {\n                    event = decoder.decode(value);\n                }\n                else if (fieldName === \"data\") {\n                    data.push(value);\n                }\n                else if (fieldName === \"id\") {\n                    if (value.indexOf(NULL) === -1)\n                        lastEventId = decoder.decode(value);\n                }\n                else if (fieldName === \"retry\") {\n                    const retryNum = Number.parseInt(decoder.decode(value));\n                    if (!Number.isNaN(retryNum))\n                        retry = retryNum;\n                }\n            },\n            flush(controller) {\n                if (event) {\n                    controller.enqueue({\n                        id: lastEventId || undefined,\n                        event,\n                        data: data.length ? decodeArraysToJson(decoder, data) : null,\n                    });\n                }\n            },\n        });\n    }\n}\nfunction joinArrays(data) {\n    const totalLength = data.reduce((acc, curr) => acc + curr.length, 0);\n    let merged = new Uint8Array(totalLength);\n    let offset = 0;\n    for (const c of data) {\n        merged.set(c, offset);\n        offset += c.length;\n    }\n    return merged;\n}\nfunction decodeArraysToJson(decoder, data) {\n    return JSON.parse(decoder.decode(joinArrays(data)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/sse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/stream.js":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/stream.js ***!
  \************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IterableReadableStream: () => (/* binding */ IterableReadableStream)\n/* harmony export */ });\n/*\n * Support async iterator syntax for ReadableStreams in all environments.\n * Source: https://github.com/MattiasBuelens/web-streams-polyfill/pull/122#issuecomment-1627354490\n */\nclass IterableReadableStream extends ReadableStream {\n    constructor() {\n        super(...arguments);\n        Object.defineProperty(this, \"reader\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n    }\n    ensureReader() {\n        if (!this.reader) {\n            this.reader = this.getReader();\n        }\n    }\n    async next() {\n        this.ensureReader();\n        try {\n            const result = await this.reader.read();\n            if (result.done) {\n                this.reader.releaseLock(); // release lock when stream becomes closed\n                return {\n                    done: true,\n                    value: undefined,\n                };\n            }\n            else {\n                return {\n                    done: false,\n                    value: result.value,\n                };\n            }\n        }\n        catch (e) {\n            this.reader.releaseLock(); // release lock when stream becomes errored\n            throw e;\n        }\n    }\n    async return() {\n        this.ensureReader();\n        // If wrapped in a Node stream, cancel is already called.\n        if (this.locked) {\n            const cancelPromise = this.reader.cancel(); // cancel first, but don't await yet\n            this.reader.releaseLock(); // release lock first\n            await cancelPromise; // now await it\n        }\n        return { done: true, value: undefined };\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    async throw(e) {\n        this.ensureReader();\n        if (this.locked) {\n            const cancelPromise = this.reader.cancel(); // cancel first, but don't await yet\n            this.reader.releaseLock(); // release lock first\n            await cancelPromise; // now await it\n        }\n        throw e;\n    }\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore Not present in Node 18 types, required in latest Node 22\n    async [Symbol.asyncDispose]() {\n        await this.return();\n    }\n    [Symbol.asyncIterator]() {\n        return this;\n    }\n    static fromReadableStream(stream) {\n        // From https://developer.mozilla.org/en-US/docs/Web/API/Streams_API/Using_readable_streams#reading_the_stream\n        const reader = stream.getReader();\n        return new IterableReadableStream({\n            start(controller) {\n                return pump();\n                function pump() {\n                    return reader.read().then(({ done, value }) => {\n                        // When no more data needs to be consumed, close the stream\n                        if (done) {\n                            controller.close();\n                            return;\n                        }\n                        // Enqueue the next data chunk into our target stream\n                        controller.enqueue(value);\n                        return pump();\n                    });\n                }\n            },\n            cancel() {\n                reader.releaseLock();\n            },\n        });\n    }\n    static fromAsyncGenerator(generator) {\n        return new IterableReadableStream({\n            async pull(controller) {\n                const { value, done } = await generator.next();\n                // When no more data needs to be consumed, close the stream\n                if (done) {\n                    controller.close();\n                }\n                // Fix: `else if (value)` will hang the streaming when nullish value (e.g. empty string) is pulled\n                controller.enqueue(value);\n            },\n            async cancel(reason) {\n                await generator.return(reason);\n            },\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/stream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/index.js":
/*!************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/index.js ***!
  \************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Client: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.Client),
/* harmony export */   overrideFetchImplementation: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.overrideFetchImplementation)
/* harmony export */ });
/* harmony import */ var _dist_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dist/index.js */ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.78_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/index.js");


/***/ })

};
;