"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pino-std-serializers@7.0.0";
exports.ids = ["vendor-chunks/pino-std-serializers@7.0.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/index.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/index.js ***!
  \**************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst errSerializer = __webpack_require__(/*! ./lib/err */ \"(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err.js\")\nconst errWithCauseSerializer = __webpack_require__(/*! ./lib/err-with-cause */ \"(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-with-cause.js\")\nconst reqSerializers = __webpack_require__(/*! ./lib/req */ \"(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/req.js\")\nconst resSerializers = __webpack_require__(/*! ./lib/res */ \"(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/res.js\")\n\nmodule.exports = {\n  err: errSerializer,\n  errWithCause: errWithCauseSerializer,\n  mapHttpRequest: reqSerializers.mapHttpRequest,\n  mapHttpResponse: resSerializers.mapHttpResponse,\n  req: reqSerializers.reqSerializer,\n  res: resSerializers.resSerializer,\n\n  wrapErrorSerializer: function wrapErrorSerializer (customSerializer) {\n    if (customSerializer === errSerializer) return customSerializer\n    return function wrapErrSerializer (err) {\n      return customSerializer(errSerializer(err))\n    }\n  },\n\n  wrapRequestSerializer: function wrapRequestSerializer (customSerializer) {\n    if (customSerializer === reqSerializers.reqSerializer) return customSerializer\n    return function wrappedReqSerializer (req) {\n      return customSerializer(reqSerializers.reqSerializer(req))\n    }\n  },\n\n  wrapResponseSerializer: function wrapResponseSerializer (customSerializer) {\n    if (customSerializer === resSerializers.resSerializer) return customSerializer\n    return function wrappedResSerializer (res) {\n      return customSerializer(resSerializers.resSerializer(res))\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-helpers.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-helpers.js ***!
  \************************************************************************************************************/
/***/ ((module) => {

eval("\n\n// **************************************************************\n// * Code initially copied/adapted from \"pony-cause\" npm module *\n// * Please upstream improvements there                         *\n// **************************************************************\n\nconst isErrorLike = (err) => {\n  return err && typeof err.message === 'string'\n}\n\n/**\n * @param {Error|{ cause?: unknown|(()=>err)}} err\n * @returns {Error|Object|undefined}\n */\nconst getErrorCause = (err) => {\n  if (!err) return\n\n  /** @type {unknown} */\n  // @ts-ignore\n  const cause = err.cause\n\n  // VError / NError style causes\n  if (typeof cause === 'function') {\n    // @ts-ignore\n    const causeResult = err.cause()\n\n    return isErrorLike(causeResult)\n      ? causeResult\n      : undefined\n  } else {\n    return isErrorLike(cause)\n      ? cause\n      : undefined\n  }\n}\n\n/**\n * Internal method that keeps a track of which error we have already added, to avoid circular recursion\n *\n * @private\n * @param {Error} err\n * @param {Set<Error>} seen\n * @returns {string}\n */\nconst _stackWithCauses = (err, seen) => {\n  if (!isErrorLike(err)) return ''\n\n  const stack = err.stack || ''\n\n  // Ensure we don't go circular or crazily deep\n  if (seen.has(err)) {\n    return stack + '\\ncauses have become circular...'\n  }\n\n  const cause = getErrorCause(err)\n\n  if (cause) {\n    seen.add(err)\n    return (stack + '\\ncaused by: ' + _stackWithCauses(cause, seen))\n  } else {\n    return stack\n  }\n}\n\n/**\n * @param {Error} err\n * @returns {string}\n */\nconst stackWithCauses = (err) => _stackWithCauses(err, new Set())\n\n/**\n * Internal method that keeps a track of which error we have already added, to avoid circular recursion\n *\n * @private\n * @param {Error} err\n * @param {Set<Error>} seen\n * @param {boolean} [skip]\n * @returns {string}\n */\nconst _messageWithCauses = (err, seen, skip) => {\n  if (!isErrorLike(err)) return ''\n\n  const message = skip ? '' : (err.message || '')\n\n  // Ensure we don't go circular or crazily deep\n  if (seen.has(err)) {\n    return message + ': ...'\n  }\n\n  const cause = getErrorCause(err)\n\n  if (cause) {\n    seen.add(err)\n\n    // @ts-ignore\n    const skipIfVErrorStyleCause = typeof err.cause === 'function'\n\n    return (message +\n      (skipIfVErrorStyleCause ? '' : ': ') +\n      _messageWithCauses(cause, seen, skipIfVErrorStyleCause))\n  } else {\n    return message\n  }\n}\n\n/**\n * @param {Error} err\n * @returns {string}\n */\nconst messageWithCauses = (err) => _messageWithCauses(err, new Set())\n\nmodule.exports = {\n  isErrorLike,\n  getErrorCause,\n  stackWithCauses,\n  messageWithCauses\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-proto.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-proto.js ***!
  \**********************************************************************************************************/
/***/ ((module) => {

eval("\n\nconst seen = Symbol('circular-ref-tag')\nconst rawSymbol = Symbol('pino-raw-err-ref')\n\nconst pinoErrProto = Object.create({}, {\n  type: {\n    enumerable: true,\n    writable: true,\n    value: undefined\n  },\n  message: {\n    enumerable: true,\n    writable: true,\n    value: undefined\n  },\n  stack: {\n    enumerable: true,\n    writable: true,\n    value: undefined\n  },\n  aggregateErrors: {\n    enumerable: true,\n    writable: true,\n    value: undefined\n  },\n  raw: {\n    enumerable: false,\n    get: function () {\n      return this[rawSymbol]\n    },\n    set: function (val) {\n      this[rawSymbol] = val\n    }\n  }\n})\nObject.defineProperty(pinoErrProto, rawSymbol, {\n  writable: true,\n  value: {}\n})\n\nmodule.exports = {\n  pinoErrProto,\n  pinoErrorSymbols: {\n    seen,\n    rawSymbol\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vcGluby1zdGQtc2VyaWFsaXplcnNANy4wLjAvbm9kZV9tb2R1bGVzL3Bpbm8tc3RkLXNlcmlhbGl6ZXJzL2xpYi9lcnItcHJvdG8uanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTs7QUFFQSxxQ0FBcUM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL3Bpbm8tc3RkLXNlcmlhbGl6ZXJzQDcuMC4wL25vZGVfbW9kdWxlcy9waW5vLXN0ZC1zZXJpYWxpemVycy9saWIvZXJyLXByb3RvLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBzZWVuID0gU3ltYm9sKCdjaXJjdWxhci1yZWYtdGFnJylcbmNvbnN0IHJhd1N5bWJvbCA9IFN5bWJvbCgncGluby1yYXctZXJyLXJlZicpXG5cbmNvbnN0IHBpbm9FcnJQcm90byA9IE9iamVjdC5jcmVhdGUoe30sIHtcbiAgdHlwZToge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgd3JpdGFibGU6IHRydWUsXG4gICAgdmFsdWU6IHVuZGVmaW5lZFxuICB9LFxuICBtZXNzYWdlOiB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICB2YWx1ZTogdW5kZWZpbmVkXG4gIH0sXG4gIHN0YWNrOiB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICB2YWx1ZTogdW5kZWZpbmVkXG4gIH0sXG4gIGFnZ3JlZ2F0ZUVycm9yczoge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgd3JpdGFibGU6IHRydWUsXG4gICAgdmFsdWU6IHVuZGVmaW5lZFxuICB9LFxuICByYXc6IHtcbiAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICBnZXQ6IGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiB0aGlzW3Jhd1N5bWJvbF1cbiAgICB9LFxuICAgIHNldDogZnVuY3Rpb24gKHZhbCkge1xuICAgICAgdGhpc1tyYXdTeW1ib2xdID0gdmFsXG4gICAgfVxuICB9XG59KVxuT2JqZWN0LmRlZmluZVByb3BlcnR5KHBpbm9FcnJQcm90bywgcmF3U3ltYm9sLCB7XG4gIHdyaXRhYmxlOiB0cnVlLFxuICB2YWx1ZToge31cbn0pXG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBwaW5vRXJyUHJvdG8sXG4gIHBpbm9FcnJvclN5bWJvbHM6IHtcbiAgICBzZWVuLFxuICAgIHJhd1N5bWJvbFxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-proto.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-with-cause.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-with-cause.js ***!
  \***************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = errWithCauseSerializer\n\nconst { isErrorLike } = __webpack_require__(/*! ./err-helpers */ \"(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-helpers.js\")\nconst { pinoErrProto, pinoErrorSymbols } = __webpack_require__(/*! ./err-proto */ \"(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-proto.js\")\nconst { seen } = pinoErrorSymbols\n\nconst { toString } = Object.prototype\n\nfunction errWithCauseSerializer (err) {\n  if (!isErrorLike(err)) {\n    return err\n  }\n\n  err[seen] = undefined // tag to prevent re-looking at this\n  const _err = Object.create(pinoErrProto)\n  _err.type = toString.call(err.constructor) === '[object Function]'\n    ? err.constructor.name\n    : err.name\n  _err.message = err.message\n  _err.stack = err.stack\n\n  if (Array.isArray(err.errors)) {\n    _err.aggregateErrors = err.errors.map(err => errWithCauseSerializer(err))\n  }\n\n  if (isErrorLike(err.cause) && !Object.prototype.hasOwnProperty.call(err.cause, seen)) {\n    _err.cause = errWithCauseSerializer(err.cause)\n  }\n\n  for (const key in err) {\n    if (_err[key] === undefined) {\n      const val = err[key]\n      if (isErrorLike(val)) {\n        if (!Object.prototype.hasOwnProperty.call(val, seen)) {\n          _err[key] = errWithCauseSerializer(val)\n        }\n      } else {\n        _err[key] = val\n      }\n    }\n  }\n\n  delete err[seen] // clean up tag in case err is serialized again later\n  _err.raw = err\n  return _err\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-with-cause.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err.js ***!
  \****************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = errSerializer\n\nconst { messageWithCauses, stackWithCauses, isErrorLike } = __webpack_require__(/*! ./err-helpers */ \"(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-helpers.js\")\nconst { pinoErrProto, pinoErrorSymbols } = __webpack_require__(/*! ./err-proto */ \"(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err-proto.js\")\nconst { seen } = pinoErrorSymbols\n\nconst { toString } = Object.prototype\n\nfunction errSerializer (err) {\n  if (!isErrorLike(err)) {\n    return err\n  }\n\n  err[seen] = undefined // tag to prevent re-looking at this\n  const _err = Object.create(pinoErrProto)\n  _err.type = toString.call(err.constructor) === '[object Function]'\n    ? err.constructor.name\n    : err.name\n  _err.message = messageWithCauses(err)\n  _err.stack = stackWithCauses(err)\n\n  if (Array.isArray(err.errors)) {\n    _err.aggregateErrors = err.errors.map(err => errSerializer(err))\n  }\n\n  for (const key in err) {\n    if (_err[key] === undefined) {\n      const val = err[key]\n      if (isErrorLike(val)) {\n        // We append cause messages and stacks to _err, therefore skipping causes here\n        if (key !== 'cause' && !Object.prototype.hasOwnProperty.call(val, seen)) {\n          _err[key] = errSerializer(val)\n        }\n      } else {\n        _err[key] = val\n      }\n    }\n  }\n\n  delete err[seen] // clean up tag in case err is serialized again later\n  _err.raw = err\n  return _err\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/err.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/req.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/req.js ***!
  \****************************************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n  mapHttpRequest,\n  reqSerializer\n}\n\nconst rawSymbol = Symbol('pino-raw-req-ref')\nconst pinoReqProto = Object.create({}, {\n  id: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  method: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  url: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  query: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  params: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  headers: {\n    enumerable: true,\n    writable: true,\n    value: {}\n  },\n  remoteAddress: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  remotePort: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  raw: {\n    enumerable: false,\n    get: function () {\n      return this[rawSymbol]\n    },\n    set: function (val) {\n      this[rawSymbol] = val\n    }\n  }\n})\nObject.defineProperty(pinoReqProto, rawSymbol, {\n  writable: true,\n  value: {}\n})\n\nfunction reqSerializer (req) {\n  // req.info is for hapi compat.\n  const connection = req.info || req.socket\n  const _req = Object.create(pinoReqProto)\n  _req.id = (typeof req.id === 'function' ? req.id() : (req.id || (req.info ? req.info.id : undefined)))\n  _req.method = req.method\n  // req.originalUrl is for expressjs compat.\n  if (req.originalUrl) {\n    _req.url = req.originalUrl\n  } else {\n    const path = req.path\n    // path for safe hapi compat.\n    _req.url = typeof path === 'string' ? path : (req.url ? req.url.path || req.url : undefined)\n  }\n\n  if (req.query) {\n    _req.query = req.query\n  }\n\n  if (req.params) {\n    _req.params = req.params\n  }\n\n  _req.headers = req.headers\n  _req.remoteAddress = connection && connection.remoteAddress\n  _req.remotePort = connection && connection.remotePort\n  // req.raw is  for hapi compat/equivalence\n  _req.raw = req.raw || req\n  return _req\n}\n\nfunction mapHttpRequest (req) {\n  return {\n    req: reqSerializer(req)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/req.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/res.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/res.js ***!
  \****************************************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n  mapHttpResponse,\n  resSerializer\n}\n\nconst rawSymbol = Symbol('pino-raw-res-ref')\nconst pinoResProto = Object.create({}, {\n  statusCode: {\n    enumerable: true,\n    writable: true,\n    value: 0\n  },\n  headers: {\n    enumerable: true,\n    writable: true,\n    value: ''\n  },\n  raw: {\n    enumerable: false,\n    get: function () {\n      return this[rawSymbol]\n    },\n    set: function (val) {\n      this[rawSymbol] = val\n    }\n  }\n})\nObject.defineProperty(pinoResProto, rawSymbol, {\n  writable: true,\n  value: {}\n})\n\nfunction resSerializer (res) {\n  const _res = Object.create(pinoResProto)\n  _res.statusCode = res.headersSent ? res.statusCode : null\n  _res.headers = res.getHeaders ? res.getHeaders() : res._headers\n  _res.raw = res\n  return _res\n}\n\nfunction mapHttpResponse (res) {\n  return {\n    res: resSerializer(res)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/pino-std-serializers@7.0.0/node_modules/pino-std-serializers/lib/res.js\n");

/***/ })

};
;