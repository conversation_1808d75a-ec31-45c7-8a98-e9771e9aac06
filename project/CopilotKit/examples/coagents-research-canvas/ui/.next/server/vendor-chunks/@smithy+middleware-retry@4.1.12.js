"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+middleware-retry@4.1.12";
exports.ids = ["vendor-chunks/@smithy+middleware-retry@4.1.12"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/AdaptiveRetryStrategy.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/AdaptiveRetryStrategy.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdaptiveRetryStrategy: () => (/* binding */ AdaptiveRetryStrategy)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_retry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js\");\n/* harmony import */ var _StandardRetryStrategy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./StandardRetryStrategy */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/StandardRetryStrategy.js\");\n\n\nclass AdaptiveRetryStrategy extends _StandardRetryStrategy__WEBPACK_IMPORTED_MODULE_1__.StandardRetryStrategy {\n    constructor(maxAttemptsProvider, options) {\n        const { rateLimiter, ...superOptions } = options ?? {};\n        super(maxAttemptsProvider, superOptions);\n        this.rateLimiter = rateLimiter ?? new _smithy_util_retry__WEBPACK_IMPORTED_MODULE_0__.DefaultRateLimiter();\n        this.mode = _smithy_util_retry__WEBPACK_IMPORTED_MODULE_0__.RETRY_MODES.ADAPTIVE;\n    }\n    async retry(next, args) {\n        return super.retry(next, args, {\n            beforeRequest: async () => {\n                return this.rateLimiter.getSendToken();\n            },\n            afterRequest: (response) => {\n                this.rateLimiter.updateClientSendingRate(response);\n            },\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLXJldHJ5QDQuMS4xMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLXJldHJ5L2Rpc3QtZXMvQWRhcHRpdmVSZXRyeVN0cmF0ZWd5LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRTtBQUNMO0FBQ3pELG9DQUFvQyx5RUFBcUI7QUFDaEU7QUFDQSxnQkFBZ0IsK0JBQStCO0FBQy9DO0FBQ0EsOENBQThDLGtFQUFrQjtBQUNoRSxvQkFBb0IsMkRBQVc7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLXJldHJ5QDQuMS4xMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLXJldHJ5L2Rpc3QtZXMvQWRhcHRpdmVSZXRyeVN0cmF0ZWd5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IERlZmF1bHRSYXRlTGltaXRlciwgUkVUUllfTU9ERVMgfSBmcm9tIFwiQHNtaXRoeS91dGlsLXJldHJ5XCI7XG5pbXBvcnQgeyBTdGFuZGFyZFJldHJ5U3RyYXRlZ3kgfSBmcm9tIFwiLi9TdGFuZGFyZFJldHJ5U3RyYXRlZ3lcIjtcbmV4cG9ydCBjbGFzcyBBZGFwdGl2ZVJldHJ5U3RyYXRlZ3kgZXh0ZW5kcyBTdGFuZGFyZFJldHJ5U3RyYXRlZ3kge1xuICAgIGNvbnN0cnVjdG9yKG1heEF0dGVtcHRzUHJvdmlkZXIsIG9wdGlvbnMpIHtcbiAgICAgICAgY29uc3QgeyByYXRlTGltaXRlciwgLi4uc3VwZXJPcHRpb25zIH0gPSBvcHRpb25zID8/IHt9O1xuICAgICAgICBzdXBlcihtYXhBdHRlbXB0c1Byb3ZpZGVyLCBzdXBlck9wdGlvbnMpO1xuICAgICAgICB0aGlzLnJhdGVMaW1pdGVyID0gcmF0ZUxpbWl0ZXIgPz8gbmV3IERlZmF1bHRSYXRlTGltaXRlcigpO1xuICAgICAgICB0aGlzLm1vZGUgPSBSRVRSWV9NT0RFUy5BREFQVElWRTtcbiAgICB9XG4gICAgYXN5bmMgcmV0cnkobmV4dCwgYXJncykge1xuICAgICAgICByZXR1cm4gc3VwZXIucmV0cnkobmV4dCwgYXJncywge1xuICAgICAgICAgICAgYmVmb3JlUmVxdWVzdDogYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLnJhdGVMaW1pdGVyLmdldFNlbmRUb2tlbigpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGFmdGVyUmVxdWVzdDogKHJlc3BvbnNlKSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy5yYXRlTGltaXRlci51cGRhdGVDbGllbnRTZW5kaW5nUmF0ZShyZXNwb25zZSk7XG4gICAgICAgICAgICB9LFxuICAgICAgICB9KTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/AdaptiveRetryStrategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/StandardRetryStrategy.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/StandardRetryStrategy.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StandardRetryStrategy: () => (/* binding */ StandardRetryStrategy)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_service_error_classification__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/service-error-classification */ \"(rsc)/./node_modules/.pnpm/@smithy+service-error-classification@4.0.5/node_modules/@smithy/service-error-classification/dist-es/index.js\");\n/* harmony import */ var _smithy_util_retry__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/util-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var _defaultRetryQuota__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./defaultRetryQuota */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/defaultRetryQuota.js\");\n/* harmony import */ var _delayDecider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./delayDecider */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/delayDecider.js\");\n/* harmony import */ var _retryDecider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./retryDecider */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/retryDecider.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/util.js\");\n\n\n\n\n\n\n\n\nclass StandardRetryStrategy {\n    constructor(maxAttemptsProvider, options) {\n        this.maxAttemptsProvider = maxAttemptsProvider;\n        this.mode = _smithy_util_retry__WEBPACK_IMPORTED_MODULE_2__.RETRY_MODES.STANDARD;\n        this.retryDecider = options?.retryDecider ?? _retryDecider__WEBPACK_IMPORTED_MODULE_5__.defaultRetryDecider;\n        this.delayDecider = options?.delayDecider ?? _delayDecider__WEBPACK_IMPORTED_MODULE_4__.defaultDelayDecider;\n        this.retryQuota = options?.retryQuota ?? (0,_defaultRetryQuota__WEBPACK_IMPORTED_MODULE_3__.getDefaultRetryQuota)(_smithy_util_retry__WEBPACK_IMPORTED_MODULE_2__.INITIAL_RETRY_TOKENS);\n    }\n    shouldRetry(error, attempts, maxAttempts) {\n        return attempts < maxAttempts && this.retryDecider(error) && this.retryQuota.hasRetryTokens(error);\n    }\n    async getMaxAttempts() {\n        let maxAttempts;\n        try {\n            maxAttempts = await this.maxAttemptsProvider();\n        }\n        catch (error) {\n            maxAttempts = _smithy_util_retry__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_MAX_ATTEMPTS;\n        }\n        return maxAttempts;\n    }\n    async retry(next, args, options) {\n        let retryTokenAmount;\n        let attempts = 0;\n        let totalDelay = 0;\n        const maxAttempts = await this.getMaxAttempts();\n        const { request } = args;\n        if (_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.isInstance(request)) {\n            request.headers[_smithy_util_retry__WEBPACK_IMPORTED_MODULE_2__.INVOCATION_ID_HEADER] = (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n        }\n        while (true) {\n            try {\n                if (_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.isInstance(request)) {\n                    request.headers[_smithy_util_retry__WEBPACK_IMPORTED_MODULE_2__.REQUEST_HEADER] = `attempt=${attempts + 1}; max=${maxAttempts}`;\n                }\n                if (options?.beforeRequest) {\n                    await options.beforeRequest();\n                }\n                const { response, output } = await next(args);\n                if (options?.afterRequest) {\n                    options.afterRequest(response);\n                }\n                this.retryQuota.releaseRetryTokens(retryTokenAmount);\n                output.$metadata.attempts = attempts + 1;\n                output.$metadata.totalRetryDelay = totalDelay;\n                return { response, output };\n            }\n            catch (e) {\n                const err = (0,_util__WEBPACK_IMPORTED_MODULE_6__.asSdkError)(e);\n                attempts++;\n                if (this.shouldRetry(err, attempts, maxAttempts)) {\n                    retryTokenAmount = this.retryQuota.retrieveRetryTokens(err);\n                    const delayFromDecider = this.delayDecider((0,_smithy_service_error_classification__WEBPACK_IMPORTED_MODULE_1__.isThrottlingError)(err) ? _smithy_util_retry__WEBPACK_IMPORTED_MODULE_2__.THROTTLING_RETRY_DELAY_BASE : _smithy_util_retry__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_RETRY_DELAY_BASE, attempts);\n                    const delayFromResponse = getDelayFromRetryAfterHeader(err.$response);\n                    const delay = Math.max(delayFromResponse || 0, delayFromDecider);\n                    totalDelay += delay;\n                    await new Promise((resolve) => setTimeout(resolve, delay));\n                    continue;\n                }\n                if (!err.$metadata) {\n                    err.$metadata = {};\n                }\n                err.$metadata.attempts = attempts;\n                err.$metadata.totalRetryDelay = totalDelay;\n                throw err;\n            }\n        }\n    }\n}\nconst getDelayFromRetryAfterHeader = (response) => {\n    if (!_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpResponse.isInstance(response))\n        return;\n    const retryAfterHeaderName = Object.keys(response.headers).find((key) => key.toLowerCase() === \"retry-after\");\n    if (!retryAfterHeaderName)\n        return;\n    const retryAfter = response.headers[retryAfterHeaderName];\n    const retryAfterSeconds = Number(retryAfter);\n    if (!Number.isNaN(retryAfterSeconds))\n        return retryAfterSeconds * 1000;\n    const retryAfterDate = new Date(retryAfter);\n    return retryAfterDate.getTime() - Date.now();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/StandardRetryStrategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/configurations.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/configurations.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONFIG_MAX_ATTEMPTS: () => (/* binding */ CONFIG_MAX_ATTEMPTS),\n/* harmony export */   CONFIG_RETRY_MODE: () => (/* binding */ CONFIG_RETRY_MODE),\n/* harmony export */   ENV_MAX_ATTEMPTS: () => (/* binding */ ENV_MAX_ATTEMPTS),\n/* harmony export */   ENV_RETRY_MODE: () => (/* binding */ ENV_RETRY_MODE),\n/* harmony export */   NODE_MAX_ATTEMPT_CONFIG_OPTIONS: () => (/* binding */ NODE_MAX_ATTEMPT_CONFIG_OPTIONS),\n/* harmony export */   NODE_RETRY_MODE_CONFIG_OPTIONS: () => (/* binding */ NODE_RETRY_MODE_CONFIG_OPTIONS),\n/* harmony export */   resolveRetryConfig: () => (/* binding */ resolveRetryConfig)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n/* harmony import */ var _smithy_util_retry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js\");\n\n\nconst ENV_MAX_ATTEMPTS = \"AWS_MAX_ATTEMPTS\";\nconst CONFIG_MAX_ATTEMPTS = \"max_attempts\";\nconst NODE_MAX_ATTEMPT_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => {\n        const value = env[ENV_MAX_ATTEMPTS];\n        if (!value)\n            return undefined;\n        const maxAttempt = parseInt(value);\n        if (Number.isNaN(maxAttempt)) {\n            throw new Error(`Environment variable ${ENV_MAX_ATTEMPTS} mast be a number, got \"${value}\"`);\n        }\n        return maxAttempt;\n    },\n    configFileSelector: (profile) => {\n        const value = profile[CONFIG_MAX_ATTEMPTS];\n        if (!value)\n            return undefined;\n        const maxAttempt = parseInt(value);\n        if (Number.isNaN(maxAttempt)) {\n            throw new Error(`Shared config file entry ${CONFIG_MAX_ATTEMPTS} mast be a number, got \"${value}\"`);\n        }\n        return maxAttempt;\n    },\n    default: _smithy_util_retry__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_MAX_ATTEMPTS,\n};\nconst resolveRetryConfig = (input) => {\n    const { retryStrategy, retryMode: _retryMode, maxAttempts: _maxAttempts } = input;\n    const maxAttempts = (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(_maxAttempts ?? _smithy_util_retry__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_MAX_ATTEMPTS);\n    return Object.assign(input, {\n        maxAttempts,\n        retryStrategy: async () => {\n            if (retryStrategy) {\n                return retryStrategy;\n            }\n            const retryMode = await (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(_retryMode)();\n            if (retryMode === _smithy_util_retry__WEBPACK_IMPORTED_MODULE_1__.RETRY_MODES.ADAPTIVE) {\n                return new _smithy_util_retry__WEBPACK_IMPORTED_MODULE_1__.AdaptiveRetryStrategy(maxAttempts);\n            }\n            return new _smithy_util_retry__WEBPACK_IMPORTED_MODULE_1__.StandardRetryStrategy(maxAttempts);\n        },\n    });\n};\nconst ENV_RETRY_MODE = \"AWS_RETRY_MODE\";\nconst CONFIG_RETRY_MODE = \"retry_mode\";\nconst NODE_RETRY_MODE_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => env[ENV_RETRY_MODE],\n    configFileSelector: (profile) => profile[CONFIG_RETRY_MODE],\n    default: _smithy_util_retry__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_RETRY_MODE,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/configurations.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/defaultRetryQuota.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/defaultRetryQuota.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaultRetryQuota: () => (/* binding */ getDefaultRetryQuota)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_retry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js\");\n\nconst getDefaultRetryQuota = (initialRetryTokens, options) => {\n    const MAX_CAPACITY = initialRetryTokens;\n    const noRetryIncrement = options?.noRetryIncrement ?? _smithy_util_retry__WEBPACK_IMPORTED_MODULE_0__.NO_RETRY_INCREMENT;\n    const retryCost = options?.retryCost ?? _smithy_util_retry__WEBPACK_IMPORTED_MODULE_0__.RETRY_COST;\n    const timeoutRetryCost = options?.timeoutRetryCost ?? _smithy_util_retry__WEBPACK_IMPORTED_MODULE_0__.TIMEOUT_RETRY_COST;\n    let availableCapacity = initialRetryTokens;\n    const getCapacityAmount = (error) => (error.name === \"TimeoutError\" ? timeoutRetryCost : retryCost);\n    const hasRetryTokens = (error) => getCapacityAmount(error) <= availableCapacity;\n    const retrieveRetryTokens = (error) => {\n        if (!hasRetryTokens(error)) {\n            throw new Error(\"No retry token available\");\n        }\n        const capacityAmount = getCapacityAmount(error);\n        availableCapacity -= capacityAmount;\n        return capacityAmount;\n    };\n    const releaseRetryTokens = (capacityReleaseAmount) => {\n        availableCapacity += capacityReleaseAmount ?? noRetryIncrement;\n        availableCapacity = Math.min(availableCapacity, MAX_CAPACITY);\n    };\n    return Object.freeze({\n        hasRetryTokens,\n        retrieveRetryTokens,\n        releaseRetryTokens,\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/defaultRetryQuota.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/delayDecider.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/delayDecider.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultDelayDecider: () => (/* binding */ defaultDelayDecider)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_retry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js\");\n\nconst defaultDelayDecider = (delayBase, attempts) => Math.floor(Math.min(_smithy_util_retry__WEBPACK_IMPORTED_MODULE_0__.MAXIMUM_RETRY_DELAY, Math.random() * 2 ** attempts * delayBase));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLXJldHJ5QDQuMS4xMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLXJldHJ5L2Rpc3QtZXMvZGVsYXlEZWNpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlEO0FBQ2xELHlFQUF5RSxtRUFBbUIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K21pZGRsZXdhcmUtcmV0cnlANC4xLjEyL25vZGVfbW9kdWxlcy9Ac21pdGh5L21pZGRsZXdhcmUtcmV0cnkvZGlzdC1lcy9kZWxheURlY2lkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTUFYSU1VTV9SRVRSWV9ERUxBWSB9IGZyb20gXCJAc21pdGh5L3V0aWwtcmV0cnlcIjtcbmV4cG9ydCBjb25zdCBkZWZhdWx0RGVsYXlEZWNpZGVyID0gKGRlbGF5QmFzZSwgYXR0ZW1wdHMpID0+IE1hdGguZmxvb3IoTWF0aC5taW4oTUFYSU1VTV9SRVRSWV9ERUxBWSwgTWF0aC5yYW5kb20oKSAqIDIgKiogYXR0ZW1wdHMgKiBkZWxheUJhc2UpKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/delayDecider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/index.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/index.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdaptiveRetryStrategy: () => (/* reexport safe */ _AdaptiveRetryStrategy__WEBPACK_IMPORTED_MODULE_0__.AdaptiveRetryStrategy),\n/* harmony export */   CONFIG_MAX_ATTEMPTS: () => (/* reexport safe */ _configurations__WEBPACK_IMPORTED_MODULE_2__.CONFIG_MAX_ATTEMPTS),\n/* harmony export */   CONFIG_RETRY_MODE: () => (/* reexport safe */ _configurations__WEBPACK_IMPORTED_MODULE_2__.CONFIG_RETRY_MODE),\n/* harmony export */   ENV_MAX_ATTEMPTS: () => (/* reexport safe */ _configurations__WEBPACK_IMPORTED_MODULE_2__.ENV_MAX_ATTEMPTS),\n/* harmony export */   ENV_RETRY_MODE: () => (/* reexport safe */ _configurations__WEBPACK_IMPORTED_MODULE_2__.ENV_RETRY_MODE),\n/* harmony export */   NODE_MAX_ATTEMPT_CONFIG_OPTIONS: () => (/* reexport safe */ _configurations__WEBPACK_IMPORTED_MODULE_2__.NODE_MAX_ATTEMPT_CONFIG_OPTIONS),\n/* harmony export */   NODE_RETRY_MODE_CONFIG_OPTIONS: () => (/* reexport safe */ _configurations__WEBPACK_IMPORTED_MODULE_2__.NODE_RETRY_MODE_CONFIG_OPTIONS),\n/* harmony export */   StandardRetryStrategy: () => (/* reexport safe */ _StandardRetryStrategy__WEBPACK_IMPORTED_MODULE_1__.StandardRetryStrategy),\n/* harmony export */   defaultDelayDecider: () => (/* reexport safe */ _delayDecider__WEBPACK_IMPORTED_MODULE_3__.defaultDelayDecider),\n/* harmony export */   defaultRetryDecider: () => (/* reexport safe */ _retryDecider__WEBPACK_IMPORTED_MODULE_5__.defaultRetryDecider),\n/* harmony export */   getOmitRetryHeadersPlugin: () => (/* reexport safe */ _omitRetryHeadersMiddleware__WEBPACK_IMPORTED_MODULE_4__.getOmitRetryHeadersPlugin),\n/* harmony export */   getRetryAfterHint: () => (/* reexport safe */ _retryMiddleware__WEBPACK_IMPORTED_MODULE_6__.getRetryAfterHint),\n/* harmony export */   getRetryPlugin: () => (/* reexport safe */ _retryMiddleware__WEBPACK_IMPORTED_MODULE_6__.getRetryPlugin),\n/* harmony export */   omitRetryHeadersMiddleware: () => (/* reexport safe */ _omitRetryHeadersMiddleware__WEBPACK_IMPORTED_MODULE_4__.omitRetryHeadersMiddleware),\n/* harmony export */   omitRetryHeadersMiddlewareOptions: () => (/* reexport safe */ _omitRetryHeadersMiddleware__WEBPACK_IMPORTED_MODULE_4__.omitRetryHeadersMiddlewareOptions),\n/* harmony export */   resolveRetryConfig: () => (/* reexport safe */ _configurations__WEBPACK_IMPORTED_MODULE_2__.resolveRetryConfig),\n/* harmony export */   retryMiddleware: () => (/* reexport safe */ _retryMiddleware__WEBPACK_IMPORTED_MODULE_6__.retryMiddleware),\n/* harmony export */   retryMiddlewareOptions: () => (/* reexport safe */ _retryMiddleware__WEBPACK_IMPORTED_MODULE_6__.retryMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _AdaptiveRetryStrategy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AdaptiveRetryStrategy */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/AdaptiveRetryStrategy.js\");\n/* harmony import */ var _StandardRetryStrategy__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./StandardRetryStrategy */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/StandardRetryStrategy.js\");\n/* harmony import */ var _configurations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./configurations */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/configurations.js\");\n/* harmony import */ var _delayDecider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./delayDecider */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/delayDecider.js\");\n/* harmony import */ var _omitRetryHeadersMiddleware__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./omitRetryHeadersMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/omitRetryHeadersMiddleware.js\");\n/* harmony import */ var _retryDecider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./retryDecider */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/retryDecider.js\");\n/* harmony import */ var _retryMiddleware__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./retryMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/retryMiddleware.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLXJldHJ5QDQuMS4xMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLXJldHJ5L2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF3QztBQUNBO0FBQ1A7QUFDRjtBQUNjO0FBQ2Q7QUFDRyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrbWlkZGxld2FyZS1yZXRyeUA0LjEuMTIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvbWlkZGxld2FyZS1yZXRyeS9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL0FkYXB0aXZlUmV0cnlTdHJhdGVneVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vU3RhbmRhcmRSZXRyeVN0cmF0ZWd5XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jb25maWd1cmF0aW9uc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vZGVsYXlEZWNpZGVyXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9vbWl0UmV0cnlIZWFkZXJzTWlkZGxld2FyZVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vcmV0cnlEZWNpZGVyXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9yZXRyeU1pZGRsZXdhcmVcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/isStreamingPayload/isStreamingPayload.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/isStreamingPayload/isStreamingPayload.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isStreamingPayload: () => (/* binding */ isStreamingPayload)\n/* harmony export */ });\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_0__);\n\nconst isStreamingPayload = (request) => request?.body instanceof stream__WEBPACK_IMPORTED_MODULE_0__.Readable ||\n    (typeof ReadableStream !== \"undefined\" && request?.body instanceof ReadableStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLXJldHJ5QDQuMS4xMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLXJldHJ5L2Rpc3QtZXMvaXNTdHJlYW1pbmdQYXlsb2FkL2lzU3RyZWFtaW5nUGF5bG9hZC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0M7QUFDM0IsaUVBQWlFLDRDQUFRO0FBQ2hGIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLXJldHJ5QDQuMS4xMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLXJldHJ5L2Rpc3QtZXMvaXNTdHJlYW1pbmdQYXlsb2FkL2lzU3RyZWFtaW5nUGF5bG9hZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBSZWFkYWJsZSB9IGZyb20gXCJzdHJlYW1cIjtcbmV4cG9ydCBjb25zdCBpc1N0cmVhbWluZ1BheWxvYWQgPSAocmVxdWVzdCkgPT4gcmVxdWVzdD8uYm9keSBpbnN0YW5jZW9mIFJlYWRhYmxlIHx8XG4gICAgKHR5cGVvZiBSZWFkYWJsZVN0cmVhbSAhPT0gXCJ1bmRlZmluZWRcIiAmJiByZXF1ZXN0Py5ib2R5IGluc3RhbmNlb2YgUmVhZGFibGVTdHJlYW0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/isStreamingPayload/isStreamingPayload.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/omitRetryHeadersMiddleware.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/omitRetryHeadersMiddleware.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getOmitRetryHeadersPlugin: () => (/* binding */ getOmitRetryHeadersPlugin),\n/* harmony export */   omitRetryHeadersMiddleware: () => (/* binding */ omitRetryHeadersMiddleware),\n/* harmony export */   omitRetryHeadersMiddlewareOptions: () => (/* binding */ omitRetryHeadersMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_util_retry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js\");\n\n\nconst omitRetryHeadersMiddleware = () => (next) => async (args) => {\n    const { request } = args;\n    if (_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.isInstance(request)) {\n        delete request.headers[_smithy_util_retry__WEBPACK_IMPORTED_MODULE_1__.INVOCATION_ID_HEADER];\n        delete request.headers[_smithy_util_retry__WEBPACK_IMPORTED_MODULE_1__.REQUEST_HEADER];\n    }\n    return next(args);\n};\nconst omitRetryHeadersMiddlewareOptions = {\n    name: \"omitRetryHeadersMiddleware\",\n    tags: [\"RETRY\", \"HEADERS\", \"OMIT_RETRY_HEADERS\"],\n    relation: \"before\",\n    toMiddleware: \"awsAuthMiddleware\",\n    override: true,\n};\nconst getOmitRetryHeadersPlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo(omitRetryHeadersMiddleware(), omitRetryHeadersMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/omitRetryHeadersMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/retryDecider.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/retryDecider.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultRetryDecider: () => (/* binding */ defaultRetryDecider)\n/* harmony export */ });\n/* harmony import */ var _smithy_service_error_classification__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/service-error-classification */ \"(rsc)/./node_modules/.pnpm/@smithy+service-error-classification@4.0.5/node_modules/@smithy/service-error-classification/dist-es/index.js\");\n\nconst defaultRetryDecider = (error) => {\n    if (!error) {\n        return false;\n    }\n    return (0,_smithy_service_error_classification__WEBPACK_IMPORTED_MODULE_0__.isRetryableByTrait)(error) || (0,_smithy_service_error_classification__WEBPACK_IMPORTED_MODULE_0__.isClockSkewError)(error) || (0,_smithy_service_error_classification__WEBPACK_IMPORTED_MODULE_0__.isThrottlingError)(error) || (0,_smithy_service_error_classification__WEBPACK_IMPORTED_MODULE_0__.isTransientError)(error);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLXJldHJ5QDQuMS4xMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLXJldHJ5L2Rpc3QtZXMvcmV0cnlEZWNpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtJO0FBQzNIO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsV0FBVyx3RkFBa0IsV0FBVyxzRkFBZ0IsV0FBVyx1RkFBaUIsV0FBVyxzRkFBZ0I7QUFDL0ciLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K21pZGRsZXdhcmUtcmV0cnlANC4xLjEyL25vZGVfbW9kdWxlcy9Ac21pdGh5L21pZGRsZXdhcmUtcmV0cnkvZGlzdC1lcy9yZXRyeURlY2lkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNDbG9ja1NrZXdFcnJvciwgaXNSZXRyeWFibGVCeVRyYWl0LCBpc1Rocm90dGxpbmdFcnJvciwgaXNUcmFuc2llbnRFcnJvciwgfSBmcm9tIFwiQHNtaXRoeS9zZXJ2aWNlLWVycm9yLWNsYXNzaWZpY2F0aW9uXCI7XG5leHBvcnQgY29uc3QgZGVmYXVsdFJldHJ5RGVjaWRlciA9IChlcnJvcikgPT4ge1xuICAgIGlmICghZXJyb3IpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICByZXR1cm4gaXNSZXRyeWFibGVCeVRyYWl0KGVycm9yKSB8fCBpc0Nsb2NrU2tld0Vycm9yKGVycm9yKSB8fCBpc1Rocm90dGxpbmdFcnJvcihlcnJvcikgfHwgaXNUcmFuc2llbnRFcnJvcihlcnJvcik7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/retryDecider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/retryMiddleware.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/retryMiddleware.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRetryAfterHint: () => (/* binding */ getRetryAfterHint),\n/* harmony export */   getRetryPlugin: () => (/* binding */ getRetryPlugin),\n/* harmony export */   retryMiddleware: () => (/* binding */ retryMiddleware),\n/* harmony export */   retryMiddlewareOptions: () => (/* binding */ retryMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_service_error_classification__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/service-error-classification */ \"(rsc)/./node_modules/.pnpm/@smithy+service-error-classification@4.0.5/node_modules/@smithy/service-error-classification/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _smithy_util_retry__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/util-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var _isStreamingPayload_isStreamingPayload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./isStreamingPayload/isStreamingPayload */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/isStreamingPayload/isStreamingPayload.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/util.js\");\n\n\n\n\n\n\n\nconst retryMiddleware = (options) => (next, context) => async (args) => {\n    let retryStrategy = await options.retryStrategy();\n    const maxAttempts = await options.maxAttempts();\n    if (isRetryStrategyV2(retryStrategy)) {\n        retryStrategy = retryStrategy;\n        let retryToken = await retryStrategy.acquireInitialRetryToken(context[\"partition_id\"]);\n        let lastError = new Error();\n        let attempts = 0;\n        let totalRetryDelay = 0;\n        const { request } = args;\n        const isRequest = _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.isInstance(request);\n        if (isRequest) {\n            request.headers[_smithy_util_retry__WEBPACK_IMPORTED_MODULE_3__.INVOCATION_ID_HEADER] = (0,uuid__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n        }\n        while (true) {\n            try {\n                if (isRequest) {\n                    request.headers[_smithy_util_retry__WEBPACK_IMPORTED_MODULE_3__.REQUEST_HEADER] = `attempt=${attempts + 1}; max=${maxAttempts}`;\n                }\n                const { response, output } = await next(args);\n                retryStrategy.recordSuccess(retryToken);\n                output.$metadata.attempts = attempts + 1;\n                output.$metadata.totalRetryDelay = totalRetryDelay;\n                return { response, output };\n            }\n            catch (e) {\n                const retryErrorInfo = getRetryErrorInfo(e);\n                lastError = (0,_util__WEBPACK_IMPORTED_MODULE_5__.asSdkError)(e);\n                if (isRequest && (0,_isStreamingPayload_isStreamingPayload__WEBPACK_IMPORTED_MODULE_4__.isStreamingPayload)(request)) {\n                    (context.logger instanceof _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.NoOpLogger ? console : context.logger)?.warn(\"An error was encountered in a non-retryable streaming request.\");\n                    throw lastError;\n                }\n                try {\n                    retryToken = await retryStrategy.refreshRetryTokenForRetry(retryToken, retryErrorInfo);\n                }\n                catch (refreshError) {\n                    if (!lastError.$metadata) {\n                        lastError.$metadata = {};\n                    }\n                    lastError.$metadata.attempts = attempts + 1;\n                    lastError.$metadata.totalRetryDelay = totalRetryDelay;\n                    throw lastError;\n                }\n                attempts = retryToken.getRetryCount();\n                const delay = retryToken.getRetryDelay();\n                totalRetryDelay += delay;\n                await new Promise((resolve) => setTimeout(resolve, delay));\n            }\n        }\n    }\n    else {\n        retryStrategy = retryStrategy;\n        if (retryStrategy?.mode)\n            context.userAgent = [...(context.userAgent || []), [\"cfg/retry-mode\", retryStrategy.mode]];\n        return retryStrategy.retry(next, args);\n    }\n};\nconst isRetryStrategyV2 = (retryStrategy) => typeof retryStrategy.acquireInitialRetryToken !== \"undefined\" &&\n    typeof retryStrategy.refreshRetryTokenForRetry !== \"undefined\" &&\n    typeof retryStrategy.recordSuccess !== \"undefined\";\nconst getRetryErrorInfo = (error) => {\n    const errorInfo = {\n        error,\n        errorType: getRetryErrorType(error),\n    };\n    const retryAfterHint = getRetryAfterHint(error.$response);\n    if (retryAfterHint) {\n        errorInfo.retryAfterHint = retryAfterHint;\n    }\n    return errorInfo;\n};\nconst getRetryErrorType = (error) => {\n    if ((0,_smithy_service_error_classification__WEBPACK_IMPORTED_MODULE_1__.isThrottlingError)(error))\n        return \"THROTTLING\";\n    if ((0,_smithy_service_error_classification__WEBPACK_IMPORTED_MODULE_1__.isTransientError)(error))\n        return \"TRANSIENT\";\n    if ((0,_smithy_service_error_classification__WEBPACK_IMPORTED_MODULE_1__.isServerError)(error))\n        return \"SERVER_ERROR\";\n    return \"CLIENT_ERROR\";\n};\nconst retryMiddlewareOptions = {\n    name: \"retryMiddleware\",\n    tags: [\"RETRY\"],\n    step: \"finalizeRequest\",\n    priority: \"high\",\n    override: true,\n};\nconst getRetryPlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(retryMiddleware(options), retryMiddlewareOptions);\n    },\n});\nconst getRetryAfterHint = (response) => {\n    if (!_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpResponse.isInstance(response))\n        return;\n    const retryAfterHeaderName = Object.keys(response.headers).find((key) => key.toLowerCase() === \"retry-after\");\n    if (!retryAfterHeaderName)\n        return;\n    const retryAfter = response.headers[retryAfterHeaderName];\n    const retryAfterSeconds = Number(retryAfter);\n    if (!Number.isNaN(retryAfterSeconds))\n        return new Date(retryAfterSeconds * 1000);\n    const retryAfterDate = new Date(retryAfter);\n    return retryAfterDate;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/retryMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/util.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/util.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asSdkError: () => (/* binding */ asSdkError)\n/* harmony export */ });\nconst asSdkError = (error) => {\n    if (error instanceof Error)\n        return error;\n    if (error instanceof Object)\n        return Object.assign(new Error(), error);\n    if (typeof error === \"string\")\n        return new Error(error);\n    return new Error(`AWS SDK error wrapper for ${error}`);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLXJldHJ5QDQuMS4xMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLXJldHJ5L2Rpc3QtZXMvdXRpbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrREFBa0QsTUFBTTtBQUN4RCIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrbWlkZGxld2FyZS1yZXRyeUA0LjEuMTIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvbWlkZGxld2FyZS1yZXRyeS9kaXN0LWVzL3V0aWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGFzU2RrRXJyb3IgPSAoZXJyb3IpID0+IHtcbiAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcilcbiAgICAgICAgcmV0dXJuIGVycm9yO1xuICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIE9iamVjdClcbiAgICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24obmV3IEVycm9yKCksIGVycm9yKTtcbiAgICBpZiAodHlwZW9mIGVycm9yID09PSBcInN0cmluZ1wiKVxuICAgICAgICByZXR1cm4gbmV3IEVycm9yKGVycm9yKTtcbiAgICByZXR1cm4gbmV3IEVycm9yKGBBV1MgU0RLIGVycm9yIHdyYXBwZXIgZm9yICR7ZXJyb3J9YCk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/util.js\n");

/***/ })

};
;