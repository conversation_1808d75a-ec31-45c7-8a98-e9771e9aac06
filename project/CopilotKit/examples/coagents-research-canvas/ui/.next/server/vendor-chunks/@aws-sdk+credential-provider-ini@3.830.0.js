"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+credential-provider-ini@3.830.0";
exports.ids = ["vendor-chunks/@aws-sdk+credential-provider-ini@3.830.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/fromIni.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/fromIni.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromIni: () => (/* binding */ fromIni)\n/* harmony export */ });\n/* harmony import */ var _smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/shared-ini-file-loader */ \"(rsc)/./node_modules/.pnpm/@smithy+shared-ini-file-loader@4.0.4/node_modules/@smithy/shared-ini-file-loader/dist-es/index.js\");\n/* harmony import */ var _resolveProfileData__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./resolveProfileData */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveProfileData.js\");\n\n\nconst fromIni = (_init = {}) => async ({ callerClientConfig } = {}) => {\n    const init = {\n        ..._init,\n        parentClientConfig: {\n            ...callerClientConfig,\n            ..._init.parentClientConfig,\n        },\n    };\n    init.logger?.debug(\"@aws-sdk/credential-provider-ini - fromIni\");\n    const profiles = await (0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_0__.parseKnownFiles)(init);\n    return (0,_resolveProfileData__WEBPACK_IMPORTED_MODULE_1__.resolveProfileData)((0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_0__.getProfileName)({\n        profile: _init.profile ?? callerClientConfig?.profile,\n    }), profiles, init);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1pbmlAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1pbmkvZGlzdC1lcy9mcm9tSW5pLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpRjtBQUN2QjtBQUNuRCwyQkFBMkIsY0FBYyxxQkFBcUIsSUFBSTtBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSwyQkFBMkIsK0VBQWU7QUFDMUMsV0FBVyx1RUFBa0IsQ0FBQyw4RUFBYztBQUM1QztBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK2NyZWRlbnRpYWwtcHJvdmlkZXItaW5pQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItaW5pL2Rpc3QtZXMvZnJvbUluaS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRQcm9maWxlTmFtZSwgcGFyc2VLbm93bkZpbGVzIH0gZnJvbSBcIkBzbWl0aHkvc2hhcmVkLWluaS1maWxlLWxvYWRlclwiO1xuaW1wb3J0IHsgcmVzb2x2ZVByb2ZpbGVEYXRhIH0gZnJvbSBcIi4vcmVzb2x2ZVByb2ZpbGVEYXRhXCI7XG5leHBvcnQgY29uc3QgZnJvbUluaSA9IChfaW5pdCA9IHt9KSA9PiBhc3luYyAoeyBjYWxsZXJDbGllbnRDb25maWcgfSA9IHt9KSA9PiB7XG4gICAgY29uc3QgaW5pdCA9IHtcbiAgICAgICAgLi4uX2luaXQsXG4gICAgICAgIHBhcmVudENsaWVudENvbmZpZzoge1xuICAgICAgICAgICAgLi4uY2FsbGVyQ2xpZW50Q29uZmlnLFxuICAgICAgICAgICAgLi4uX2luaXQucGFyZW50Q2xpZW50Q29uZmlnLFxuICAgICAgICB9LFxuICAgIH07XG4gICAgaW5pdC5sb2dnZXI/LmRlYnVnKFwiQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1pbmkgLSBmcm9tSW5pXCIpO1xuICAgIGNvbnN0IHByb2ZpbGVzID0gYXdhaXQgcGFyc2VLbm93bkZpbGVzKGluaXQpO1xuICAgIHJldHVybiByZXNvbHZlUHJvZmlsZURhdGEoZ2V0UHJvZmlsZU5hbWUoe1xuICAgICAgICBwcm9maWxlOiBfaW5pdC5wcm9maWxlID8/IGNhbGxlckNsaWVudENvbmZpZz8ucHJvZmlsZSxcbiAgICB9KSwgcHJvZmlsZXMsIGluaXQpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/fromIni.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/index.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/index.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromIni: () => (/* reexport safe */ _fromIni__WEBPACK_IMPORTED_MODULE_0__.fromIni)\n/* harmony export */ });\n/* harmony import */ var _fromIni__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fromIni */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/fromIni.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1pbmlAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1pbmkvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK2NyZWRlbnRpYWwtcHJvdmlkZXItaW5pQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItaW5pL2Rpc3QtZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vZnJvbUluaVwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveAssumeRoleCredentials.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveAssumeRoleCredentials.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isAssumeRoleProfile: () => (/* binding */ isAssumeRoleProfile),\n/* harmony export */   resolveAssumeRoleCredentials: () => (/* binding */ resolveAssumeRoleCredentials)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @aws-sdk/core/client */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\");\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var _smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/shared-ini-file-loader */ \"(rsc)/./node_modules/.pnpm/@smithy+shared-ini-file-loader@4.0.4/node_modules/@smithy/shared-ini-file-loader/dist-es/index.js\");\n/* harmony import */ var _resolveCredentialSource__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resolveCredentialSource */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveCredentialSource.js\");\n/* harmony import */ var _resolveProfileData__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resolveProfileData */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveProfileData.js\");\n\n\n\n\n\nconst isAssumeRoleProfile = (arg, { profile = \"default\", logger } = {}) => {\n    return (Boolean(arg) &&\n        typeof arg === \"object\" &&\n        typeof arg.role_arn === \"string\" &&\n        [\"undefined\", \"string\"].indexOf(typeof arg.role_session_name) > -1 &&\n        [\"undefined\", \"string\"].indexOf(typeof arg.external_id) > -1 &&\n        [\"undefined\", \"string\"].indexOf(typeof arg.mfa_serial) > -1 &&\n        (isAssumeRoleWithSourceProfile(arg, { profile, logger }) || isCredentialSourceProfile(arg, { profile, logger })));\n};\nconst isAssumeRoleWithSourceProfile = (arg, { profile, logger }) => {\n    const withSourceProfile = typeof arg.source_profile === \"string\" && typeof arg.credential_source === \"undefined\";\n    if (withSourceProfile) {\n        logger?.debug?.(`    ${profile} isAssumeRoleWithSourceProfile source_profile=${arg.source_profile}`);\n    }\n    return withSourceProfile;\n};\nconst isCredentialSourceProfile = (arg, { profile, logger }) => {\n    const withProviderProfile = typeof arg.credential_source === \"string\" && typeof arg.source_profile === \"undefined\";\n    if (withProviderProfile) {\n        logger?.debug?.(`    ${profile} isCredentialSourceProfile credential_source=${arg.credential_source}`);\n    }\n    return withProviderProfile;\n};\nconst resolveAssumeRoleCredentials = async (profileName, profiles, options, visitedProfiles = {}) => {\n    options.logger?.debug(\"@aws-sdk/credential-provider-ini - resolveAssumeRoleCredentials (STS)\");\n    const profileData = profiles[profileName];\n    const { source_profile, region } = profileData;\n    if (!options.roleAssumer) {\n        const { getDefaultRoleAssumer } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@aws-sdk+nested-clients@3.830.0\"), __webpack_require__.e(\"vendor-chunks/@aws-sdk+core@3.826.0\"), __webpack_require__.e(\"vendor-chunks/fast-xml-parser@4.4.1\"), __webpack_require__.e(\"vendor-chunks/strnum@1.1.2\")]).then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/nested-clients/sts */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/index.js\"));\n        options.roleAssumer = getDefaultRoleAssumer({\n            ...options.clientConfig,\n            credentialProviderLogger: options.logger,\n            parentClientConfig: {\n                ...options?.parentClientConfig,\n                region: region ?? options?.parentClientConfig?.region,\n            },\n        }, options.clientPlugins);\n    }\n    if (source_profile && source_profile in visitedProfiles) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`Detected a cycle attempting to resolve credentials for profile` +\n            ` ${(0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_1__.getProfileName)(options)}. Profiles visited: ` +\n            Object.keys(visitedProfiles).join(\", \"), { logger: options.logger });\n    }\n    options.logger?.debug(`@aws-sdk/credential-provider-ini - finding credential resolver using ${source_profile ? `source_profile=[${source_profile}]` : `profile=[${profileName}]`}`);\n    const sourceCredsProvider = source_profile\n        ? (0,_resolveProfileData__WEBPACK_IMPORTED_MODULE_3__.resolveProfileData)(source_profile, profiles, options, {\n            ...visitedProfiles,\n            [source_profile]: true,\n        }, isCredentialSourceWithoutRoleArn(profiles[source_profile] ?? {}))\n        : (await (0,_resolveCredentialSource__WEBPACK_IMPORTED_MODULE_2__.resolveCredentialSource)(profileData.credential_source, profileName, options.logger)(options))();\n    if (isCredentialSourceWithoutRoleArn(profileData)) {\n        return sourceCredsProvider.then((creds) => (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_4__.setCredentialFeature)(creds, \"CREDENTIALS_PROFILE_SOURCE_PROFILE\", \"o\"));\n    }\n    else {\n        const params = {\n            RoleArn: profileData.role_arn,\n            RoleSessionName: profileData.role_session_name || `aws-sdk-js-${Date.now()}`,\n            ExternalId: profileData.external_id,\n            DurationSeconds: parseInt(profileData.duration_seconds || \"3600\", 10),\n        };\n        const { mfa_serial } = profileData;\n        if (mfa_serial) {\n            if (!options.mfaCodeProvider) {\n                throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`Profile ${profileName} requires multi-factor authentication, but no MFA code callback was provided.`, { logger: options.logger, tryNextLink: false });\n            }\n            params.SerialNumber = mfa_serial;\n            params.TokenCode = await options.mfaCodeProvider(mfa_serial);\n        }\n        const sourceCreds = await sourceCredsProvider;\n        return options.roleAssumer(sourceCreds, params).then((creds) => (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_4__.setCredentialFeature)(creds, \"CREDENTIALS_PROFILE_SOURCE_PROFILE\", \"o\"));\n    }\n};\nconst isCredentialSourceWithoutRoleArn = (section) => {\n    return !section.role_arn && !!section.credential_source;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveAssumeRoleCredentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveCredentialSource.js":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveCredentialSource.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveCredentialSource: () => (/* binding */ resolveCredentialSource)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/core/client */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\");\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n\n\nconst resolveCredentialSource = (credentialSource, profileName, logger) => {\n    const sourceProvidersMap = {\n        EcsContainer: async (options) => {\n            const { fromHttp } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@aws-sdk+credential-provider-http@3.826.0\").then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/credential-provider-http */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/index.js\"));\n            const { fromContainerMetadata } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@smithy+credential-provider-imds@4.0.6\").then(__webpack_require__.bind(__webpack_require__, /*! @smithy/credential-provider-imds */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/index.js\"));\n            logger?.debug(\"@aws-sdk/credential-provider-ini - credential_source is EcsContainer\");\n            return async () => (0,_smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.chain)(fromHttp(options ?? {}), fromContainerMetadata(options))().then(setNamedProvider);\n        },\n        Ec2InstanceMetadata: async (options) => {\n            logger?.debug(\"@aws-sdk/credential-provider-ini - credential_source is Ec2InstanceMetadata\");\n            const { fromInstanceMetadata } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@smithy+credential-provider-imds@4.0.6\").then(__webpack_require__.bind(__webpack_require__, /*! @smithy/credential-provider-imds */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/index.js\"));\n            return async () => fromInstanceMetadata(options)().then(setNamedProvider);\n        },\n        Environment: async (options) => {\n            logger?.debug(\"@aws-sdk/credential-provider-ini - credential_source is Environment\");\n            const { fromEnv } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/credential-provider-env */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-env@3.826.0/node_modules/@aws-sdk/credential-provider-env/dist-es/index.js\"));\n            return async () => fromEnv(options)().then(setNamedProvider);\n        },\n    };\n    if (credentialSource in sourceProvidersMap) {\n        return sourceProvidersMap[credentialSource];\n    }\n    else {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`Unsupported credential source in profile ${profileName}. Got ${credentialSource}, ` +\n            `expected EcsContainer or Ec2InstanceMetadata or Environment.`, { logger });\n    }\n};\nconst setNamedProvider = (creds) => (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_1__.setCredentialFeature)(creds, \"CREDENTIALS_PROFILE_NAMED_PROVIDER\", \"p\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveCredentialSource.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveProcessCredentials.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveProcessCredentials.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isProcessProfile: () => (/* binding */ isProcessProfile),\n/* harmony export */   resolveProcessCredentials: () => (/* binding */ resolveProcessCredentials)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/core/client */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\");\n\nconst isProcessProfile = (arg) => Boolean(arg) && typeof arg === \"object\" && typeof arg.credential_process === \"string\";\nconst resolveProcessCredentials = async (options, profile) => __webpack_require__.e(/*! import() */ \"vendor-chunks/@aws-sdk+credential-provider-process@3.826.0\").then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/credential-provider-process */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/index.js\")).then(({ fromProcess }) => fromProcess({\n    ...options,\n    profile,\n})().then((creds) => (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_0__.setCredentialFeature)(creds, \"CREDENTIALS_PROFILE_PROCESS\", \"v\")));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1pbmlAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1pbmkvZGlzdC1lcy9yZXNvbHZlUHJvY2Vzc0NyZWRlbnRpYWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0RDtBQUNyRDtBQUNBLDhEQUE4RCxpVkFBOEMsU0FBUyxhQUFhO0FBQ3pJO0FBQ0E7QUFDQSxDQUFDLG9CQUFvQiwwRUFBb0IiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytjcmVkZW50aWFsLXByb3ZpZGVyLWluaUAzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jcmVkZW50aWFsLXByb3ZpZGVyLWluaS9kaXN0LWVzL3Jlc29sdmVQcm9jZXNzQ3JlZGVudGlhbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc2V0Q3JlZGVudGlhbEZlYXR1cmUgfSBmcm9tIFwiQGF3cy1zZGsvY29yZS9jbGllbnRcIjtcbmV4cG9ydCBjb25zdCBpc1Byb2Nlc3NQcm9maWxlID0gKGFyZykgPT4gQm9vbGVhbihhcmcpICYmIHR5cGVvZiBhcmcgPT09IFwib2JqZWN0XCIgJiYgdHlwZW9mIGFyZy5jcmVkZW50aWFsX3Byb2Nlc3MgPT09IFwic3RyaW5nXCI7XG5leHBvcnQgY29uc3QgcmVzb2x2ZVByb2Nlc3NDcmVkZW50aWFscyA9IGFzeW5jIChvcHRpb25zLCBwcm9maWxlKSA9PiBpbXBvcnQoXCJAYXdzLXNkay9jcmVkZW50aWFsLXByb3ZpZGVyLXByb2Nlc3NcIikudGhlbigoeyBmcm9tUHJvY2VzcyB9KSA9PiBmcm9tUHJvY2Vzcyh7XG4gICAgLi4ub3B0aW9ucyxcbiAgICBwcm9maWxlLFxufSkoKS50aGVuKChjcmVkcykgPT4gc2V0Q3JlZGVudGlhbEZlYXR1cmUoY3JlZHMsIFwiQ1JFREVOVElBTFNfUFJPRklMRV9QUk9DRVNTXCIsIFwidlwiKSkpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveProcessCredentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveProfileData.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveProfileData.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveProfileData: () => (/* binding */ resolveProfileData)\n/* harmony export */ });\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var _resolveAssumeRoleCredentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./resolveAssumeRoleCredentials */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveAssumeRoleCredentials.js\");\n/* harmony import */ var _resolveProcessCredentials__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resolveProcessCredentials */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveProcessCredentials.js\");\n/* harmony import */ var _resolveSsoCredentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resolveSsoCredentials */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveSsoCredentials.js\");\n/* harmony import */ var _resolveStaticCredentials__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./resolveStaticCredentials */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveStaticCredentials.js\");\n/* harmony import */ var _resolveWebIdentityCredentials__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./resolveWebIdentityCredentials */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveWebIdentityCredentials.js\");\n\n\n\n\n\n\nconst resolveProfileData = async (profileName, profiles, options, visitedProfiles = {}, isAssumeRoleRecursiveCall = false) => {\n    const data = profiles[profileName];\n    if (Object.keys(visitedProfiles).length > 0 && (0,_resolveStaticCredentials__WEBPACK_IMPORTED_MODULE_4__.isStaticCredsProfile)(data)) {\n        return (0,_resolveStaticCredentials__WEBPACK_IMPORTED_MODULE_4__.resolveStaticCredentials)(data, options);\n    }\n    if (isAssumeRoleRecursiveCall || (0,_resolveAssumeRoleCredentials__WEBPACK_IMPORTED_MODULE_1__.isAssumeRoleProfile)(data, { profile: profileName, logger: options.logger })) {\n        return (0,_resolveAssumeRoleCredentials__WEBPACK_IMPORTED_MODULE_1__.resolveAssumeRoleCredentials)(profileName, profiles, options, visitedProfiles);\n    }\n    if ((0,_resolveStaticCredentials__WEBPACK_IMPORTED_MODULE_4__.isStaticCredsProfile)(data)) {\n        return (0,_resolveStaticCredentials__WEBPACK_IMPORTED_MODULE_4__.resolveStaticCredentials)(data, options);\n    }\n    if ((0,_resolveWebIdentityCredentials__WEBPACK_IMPORTED_MODULE_5__.isWebIdentityProfile)(data)) {\n        return (0,_resolveWebIdentityCredentials__WEBPACK_IMPORTED_MODULE_5__.resolveWebIdentityCredentials)(data, options);\n    }\n    if ((0,_resolveProcessCredentials__WEBPACK_IMPORTED_MODULE_2__.isProcessProfile)(data)) {\n        return (0,_resolveProcessCredentials__WEBPACK_IMPORTED_MODULE_2__.resolveProcessCredentials)(options, profileName);\n    }\n    if ((0,_resolveSsoCredentials__WEBPACK_IMPORTED_MODULE_3__.isSsoProfile)(data)) {\n        return await (0,_resolveSsoCredentials__WEBPACK_IMPORTED_MODULE_3__.resolveSsoCredentials)(profileName, data, options);\n    }\n    throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`Could not resolve credentials using profile: [${profileName}] in configuration/credentials file(s).`, { logger: options.logger });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveProfileData.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveSsoCredentials.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveSsoCredentials.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSsoProfile: () => (/* binding */ isSsoProfile),\n/* harmony export */   resolveSsoCredentials: () => (/* binding */ resolveSsoCredentials)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/core/client */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\");\n\nconst resolveSsoCredentials = async (profile, profileData, options = {}) => {\n    const { fromSSO } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@aws-sdk+credential-provider-sso@3.830.0\"), __webpack_require__.e(\"vendor-chunks/@aws-sdk+token-providers@3.830.0\")]).then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/credential-provider-sso */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/index.js\"));\n    return fromSSO({\n        profile,\n        logger: options.logger,\n        parentClientConfig: options.parentClientConfig,\n        clientConfig: options.clientConfig,\n    })().then((creds) => {\n        if (profileData.sso_session) {\n            return (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_0__.setCredentialFeature)(creds, \"CREDENTIALS_PROFILE_SSO\", \"r\");\n        }\n        else {\n            return (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_0__.setCredentialFeature)(creds, \"CREDENTIALS_PROFILE_SSO_LEGACY\", \"t\");\n        }\n    });\n};\nconst isSsoProfile = (arg) => arg &&\n    (typeof arg.sso_start_url === \"string\" ||\n        typeof arg.sso_account_id === \"string\" ||\n        typeof arg.sso_session === \"string\" ||\n        typeof arg.sso_region === \"string\" ||\n        typeof arg.sso_role_name === \"string\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveSsoCredentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveStaticCredentials.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveStaticCredentials.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isStaticCredsProfile: () => (/* binding */ isStaticCredsProfile),\n/* harmony export */   resolveStaticCredentials: () => (/* binding */ resolveStaticCredentials)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/core/client */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\");\n\nconst isStaticCredsProfile = (arg) => Boolean(arg) &&\n    typeof arg === \"object\" &&\n    typeof arg.aws_access_key_id === \"string\" &&\n    typeof arg.aws_secret_access_key === \"string\" &&\n    [\"undefined\", \"string\"].indexOf(typeof arg.aws_session_token) > -1 &&\n    [\"undefined\", \"string\"].indexOf(typeof arg.aws_account_id) > -1;\nconst resolveStaticCredentials = async (profile, options) => {\n    options?.logger?.debug(\"@aws-sdk/credential-provider-ini - resolveStaticCredentials\");\n    const credentials = {\n        accessKeyId: profile.aws_access_key_id,\n        secretAccessKey: profile.aws_secret_access_key,\n        sessionToken: profile.aws_session_token,\n        ...(profile.aws_credential_scope && { credentialScope: profile.aws_credential_scope }),\n        ...(profile.aws_account_id && { accountId: profile.aws_account_id }),\n    };\n    return (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_0__.setCredentialFeature)(credentials, \"CREDENTIALS_PROFILE\", \"n\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveStaticCredentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveWebIdentityCredentials.js":
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveWebIdentityCredentials.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isWebIdentityProfile: () => (/* binding */ isWebIdentityProfile),\n/* harmony export */   resolveWebIdentityCredentials: () => (/* binding */ resolveWebIdentityCredentials)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/core/client */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\");\n\nconst isWebIdentityProfile = (arg) => Boolean(arg) &&\n    typeof arg === \"object\" &&\n    typeof arg.web_identity_token_file === \"string\" &&\n    typeof arg.role_arn === \"string\" &&\n    [\"undefined\", \"string\"].indexOf(typeof arg.role_session_name) > -1;\nconst resolveWebIdentityCredentials = async (profile, options) => __webpack_require__.e(/*! import() */ \"vendor-chunks/@aws-sdk+credential-provider-web-identity@3.830.0\").then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/credential-provider-web-identity */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/index.js\")).then(({ fromTokenFile }) => fromTokenFile({\n    webIdentityTokenFile: profile.web_identity_token_file,\n    roleArn: profile.role_arn,\n    roleSessionName: profile.role_session_name,\n    roleAssumerWithWebIdentity: options.roleAssumerWithWebIdentity,\n    logger: options.logger,\n    parentClientConfig: options.parentClientConfig,\n})().then((creds) => (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_0__.setCredentialFeature)(creds, \"CREDENTIALS_PROFILE_STS_WEB_ID_TOKEN\", \"q\")));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/resolveWebIdentityCredentials.js\n");

/***/ })

};
;