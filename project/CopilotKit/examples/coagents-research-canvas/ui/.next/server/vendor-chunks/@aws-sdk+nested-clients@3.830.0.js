"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+nested-clients@3.830.0";
exports.ids = ["vendor-chunks/@aws-sdk+nested-clients@3.830.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/SSOOIDC.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/SSOOIDC.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SSOOIDC: () => (/* binding */ SSOOIDC)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _commands_CreateTokenCommand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./commands/CreateTokenCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/commands/CreateTokenCommand.js\");\n/* harmony import */ var _SSOOIDCClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SSOOIDCClient */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/SSOOIDCClient.js\");\n\n\n\nconst commands = {\n    CreateTokenCommand: _commands_CreateTokenCommand__WEBPACK_IMPORTED_MODULE_1__.CreateTokenCommand,\n};\nclass SSOOIDC extends _SSOOIDCClient__WEBPACK_IMPORTED_MODULE_2__.SSOOIDCClient {\n}\n(0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.createAggregatedClient)(commands, SSOOIDC);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3Nzby1vaWRjL1NTT09JREMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUErRDtBQUNJO0FBQ25CO0FBQ2hEO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ08sc0JBQXNCLHlEQUFhO0FBQzFDO0FBQ0EsNkVBQXNCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3Nzby1vaWRjL1NTT09JREMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQWdncmVnYXRlZENsaWVudCB9IGZyb20gXCJAc21pdGh5L3NtaXRoeS1jbGllbnRcIjtcbmltcG9ydCB7IENyZWF0ZVRva2VuQ29tbWFuZCB9IGZyb20gXCIuL2NvbW1hbmRzL0NyZWF0ZVRva2VuQ29tbWFuZFwiO1xuaW1wb3J0IHsgU1NPT0lEQ0NsaWVudCB9IGZyb20gXCIuL1NTT09JRENDbGllbnRcIjtcbmNvbnN0IGNvbW1hbmRzID0ge1xuICAgIENyZWF0ZVRva2VuQ29tbWFuZCxcbn07XG5leHBvcnQgY2xhc3MgU1NPT0lEQyBleHRlbmRzIFNTT09JRENDbGllbnQge1xufVxuY3JlYXRlQWdncmVnYXRlZENsaWVudChjb21tYW5kcywgU1NPT0lEQyk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/SSOOIDC.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/SSOOIDCClient.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/SSOOIDCClient.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SSOOIDCClient: () => (/* binding */ SSOOIDCClient),\n/* harmony export */   __Client: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_9__.Client)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_middleware_host_header__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/middleware-host-header */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-host-header@3.821.0/node_modules/@aws-sdk/middleware-host-header/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_middleware_logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/middleware-logger */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-logger@3.821.0/node_modules/@aws-sdk/middleware-logger/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_middleware_recursion_detection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/middleware-recursion-detection */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-recursion-detection@3.821.0/node_modules/@aws-sdk/middleware-recursion-detection/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @aws-sdk/middleware-user-agent */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/index.js\");\n/* harmony import */ var _smithy_config_resolver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @smithy/config-resolver */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_content_length__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @smithy/middleware-content-length */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-content-length@4.0.4/node_modules/@smithy/middleware-content-length/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @smithy/middleware-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./auth/httpAuthSchemeProvider */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/auth/httpAuthSchemeProvider.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/endpoint/EndpointParameters.js\");\n/* harmony import */ var _runtimeConfig__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./runtimeConfig */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/runtimeConfig.js\");\n/* harmony import */ var _runtimeExtensions__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./runtimeExtensions */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/runtimeExtensions.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nclass SSOOIDCClient extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_9__.Client {\n    config;\n    constructor(...[configuration]) {\n        const _config_0 = (0,_runtimeConfig__WEBPACK_IMPORTED_MODULE_12__.getRuntimeConfig)(configuration || {});\n        super(_config_0);\n        this.initConfig = _config_0;\n        const _config_1 = (0,_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_11__.resolveClientEndpointParameters)(_config_0);\n        const _config_2 = (0,_aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_3__.resolveUserAgentConfig)(_config_1);\n        const _config_3 = (0,_smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_8__.resolveRetryConfig)(_config_2);\n        const _config_4 = (0,_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_4__.resolveRegionConfig)(_config_3);\n        const _config_5 = (0,_aws_sdk_middleware_host_header__WEBPACK_IMPORTED_MODULE_0__.resolveHostHeaderConfig)(_config_4);\n        const _config_6 = (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_7__.resolveEndpointConfig)(_config_5);\n        const _config_7 = (0,_auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_10__.resolveHttpAuthSchemeConfig)(_config_6);\n        const _config_8 = (0,_runtimeExtensions__WEBPACK_IMPORTED_MODULE_13__.resolveRuntimeExtensions)(_config_7, configuration?.extensions || []);\n        this.config = _config_8;\n        this.middlewareStack.use((0,_aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_3__.getUserAgentPlugin)(this.config));\n        this.middlewareStack.use((0,_smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_8__.getRetryPlugin)(this.config));\n        this.middlewareStack.use((0,_smithy_middleware_content_length__WEBPACK_IMPORTED_MODULE_6__.getContentLengthPlugin)(this.config));\n        this.middlewareStack.use((0,_aws_sdk_middleware_host_header__WEBPACK_IMPORTED_MODULE_0__.getHostHeaderPlugin)(this.config));\n        this.middlewareStack.use((0,_aws_sdk_middleware_logger__WEBPACK_IMPORTED_MODULE_1__.getLoggerPlugin)(this.config));\n        this.middlewareStack.use((0,_aws_sdk_middleware_recursion_detection__WEBPACK_IMPORTED_MODULE_2__.getRecursionDetectionPlugin)(this.config));\n        this.middlewareStack.use((0,_smithy_core__WEBPACK_IMPORTED_MODULE_5__.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config, {\n            httpAuthSchemeParametersProvider: _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_10__.defaultSSOOIDCHttpAuthSchemeParametersProvider,\n            identityProviderConfigProvider: async (config) => new _smithy_core__WEBPACK_IMPORTED_MODULE_5__.DefaultIdentityProviderConfig({\n                \"aws.auth#sigv4\": config.credentials,\n            }),\n        }));\n        this.middlewareStack.use((0,_smithy_core__WEBPACK_IMPORTED_MODULE_5__.getHttpSigningPlugin)(this.config));\n    }\n    destroy() {\n        super.destroy();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/SSOOIDCClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/auth/httpAuthExtensionConfiguration.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/auth/httpAuthExtensionConfiguration.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHttpAuthExtensionConfiguration: () => (/* binding */ getHttpAuthExtensionConfiguration),\n/* harmony export */   resolveHttpAuthRuntimeConfig: () => (/* binding */ resolveHttpAuthRuntimeConfig)\n/* harmony export */ });\nconst getHttpAuthExtensionConfiguration = (runtimeConfig) => {\n    const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;\n    let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;\n    let _credentials = runtimeConfig.credentials;\n    return {\n        setHttpAuthScheme(httpAuthScheme) {\n            const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);\n            if (index === -1) {\n                _httpAuthSchemes.push(httpAuthScheme);\n            }\n            else {\n                _httpAuthSchemes.splice(index, 1, httpAuthScheme);\n            }\n        },\n        httpAuthSchemes() {\n            return _httpAuthSchemes;\n        },\n        setHttpAuthSchemeProvider(httpAuthSchemeProvider) {\n            _httpAuthSchemeProvider = httpAuthSchemeProvider;\n        },\n        httpAuthSchemeProvider() {\n            return _httpAuthSchemeProvider;\n        },\n        setCredentials(credentials) {\n            _credentials = credentials;\n        },\n        credentials() {\n            return _credentials;\n        },\n    };\n};\nconst resolveHttpAuthRuntimeConfig = (config) => {\n    return {\n        httpAuthSchemes: config.httpAuthSchemes(),\n        httpAuthSchemeProvider: config.httpAuthSchemeProvider(),\n        credentials: config.credentials(),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/auth/httpAuthExtensionConfiguration.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/auth/httpAuthSchemeProvider.js":
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/auth/httpAuthSchemeProvider.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultSSOOIDCHttpAuthSchemeParametersProvider: () => (/* binding */ defaultSSOOIDCHttpAuthSchemeParametersProvider),\n/* harmony export */   defaultSSOOIDCHttpAuthSchemeProvider: () => (/* binding */ defaultSSOOIDCHttpAuthSchemeProvider),\n/* harmony export */   resolveHttpAuthSchemeConfig: () => (/* binding */ resolveHttpAuthSchemeConfig)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/resolveAwsSdkSigV4Config.js\");\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n\n\nconst defaultSSOOIDCHttpAuthSchemeParametersProvider = async (config, context, input) => {\n    return {\n        operation: (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.getSmithyContext)(context).operation,\n        region: (await (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(config.region)()) ||\n            (() => {\n                throw new Error(\"expected `region` to be configured for `aws.auth#sigv4`\");\n            })(),\n    };\n};\nfunction createAwsAuthSigv4HttpAuthOption(authParameters) {\n    return {\n        schemeId: \"aws.auth#sigv4\",\n        signingProperties: {\n            name: \"sso-oauth\",\n            region: authParameters.region,\n        },\n        propertiesExtractor: (config, context) => ({\n            signingProperties: {\n                config,\n                context,\n            },\n        }),\n    };\n}\nfunction createSmithyApiNoAuthHttpAuthOption(authParameters) {\n    return {\n        schemeId: \"smithy.api#noAuth\",\n    };\n}\nconst defaultSSOOIDCHttpAuthSchemeProvider = (authParameters) => {\n    const options = [];\n    switch (authParameters.operation) {\n        case \"CreateToken\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        default: {\n            options.push(createAwsAuthSigv4HttpAuthOption(authParameters));\n        }\n    }\n    return options;\n};\nconst resolveHttpAuthSchemeConfig = (config) => {\n    const config_0 = (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_1__.resolveAwsSdkSigV4Config)(config);\n    return Object.assign(config_0, {\n        authSchemePreference: (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(config.authSchemePreference ?? []),\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/auth/httpAuthSchemeProvider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/commands/CreateTokenCommand.js":
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/commands/CreateTokenCommand.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command),\n/* harmony export */   CreateTokenCommand: () => (/* binding */ CreateTokenCommand)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/endpoint/EndpointParameters.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/models_0.js\");\n/* harmony import */ var _protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../protocols/Aws_restJson1 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/protocols/Aws_restJson1.js\");\n\n\n\n\n\n\n\nclass CreateTokenCommand extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command\n    .classBuilder()\n    .ep(_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__.commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        (0,_smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin)(config, this.serialize, this.deserialize),\n        (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSSSOOIDCService\", \"CreateToken\", {})\n    .n(\"SSOOIDCClient\", \"CreateTokenCommand\")\n    .f(_models_models_0__WEBPACK_IMPORTED_MODULE_4__.CreateTokenRequestFilterSensitiveLog, _models_models_0__WEBPACK_IMPORTED_MODULE_4__.CreateTokenResponseFilterSensitiveLog)\n    .ser(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.se_CreateTokenCommand)\n    .de(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.de_CreateTokenCommand)\n    .build() {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/commands/CreateTokenCommand.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/commands/index.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/commands/index.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _CreateTokenCommand__WEBPACK_IMPORTED_MODULE_0__.$Command),\n/* harmony export */   CreateTokenCommand: () => (/* reexport safe */ _CreateTokenCommand__WEBPACK_IMPORTED_MODULE_0__.CreateTokenCommand)\n/* harmony export */ });\n/* harmony import */ var _CreateTokenCommand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CreateTokenCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/commands/CreateTokenCommand.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3Nzby1vaWRjL2NvbW1hbmRzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxQyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK25lc3RlZC1jbGllbnRzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL25lc3RlZC1jbGllbnRzL2Rpc3QtZXMvc3VibW9kdWxlcy9zc28tb2lkYy9jb21tYW5kcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9DcmVhdGVUb2tlbkNvbW1hbmRcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/commands/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/endpoint/EndpointParameters.js":
/*!************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/endpoint/EndpointParameters.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   commonParams: () => (/* binding */ commonParams),\n/* harmony export */   resolveClientEndpointParameters: () => (/* binding */ resolveClientEndpointParameters)\n/* harmony export */ });\nconst resolveClientEndpointParameters = (options) => {\n    return Object.assign(options, {\n        useDualstackEndpoint: options.useDualstackEndpoint ?? false,\n        useFipsEndpoint: options.useFipsEndpoint ?? false,\n        defaultSigningName: \"sso-oauth\",\n    });\n};\nconst commonParams = {\n    UseFIPS: { type: \"builtInParams\", name: \"useFipsEndpoint\" },\n    Endpoint: { type: \"builtInParams\", name: \"endpoint\" },\n    Region: { type: \"builtInParams\", name: \"region\" },\n    UseDualStack: { type: \"builtInParams\", name: \"useDualstackEndpoint\" },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3Nzby1vaWRjL2VuZHBvaW50L0VuZHBvaW50UGFyYW1ldGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDTztBQUNQLGVBQWUsZ0RBQWdEO0FBQy9ELGdCQUFnQix5Q0FBeUM7QUFDekQsY0FBYyx1Q0FBdUM7QUFDckQsb0JBQW9CLHFEQUFxRDtBQUN6RSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK25lc3RlZC1jbGllbnRzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL25lc3RlZC1jbGllbnRzL2Rpc3QtZXMvc3VibW9kdWxlcy9zc28tb2lkYy9lbmRwb2ludC9FbmRwb2ludFBhcmFtZXRlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHJlc29sdmVDbGllbnRFbmRwb2ludFBhcmFtZXRlcnMgPSAob3B0aW9ucykgPT4ge1xuICAgIHJldHVybiBPYmplY3QuYXNzaWduKG9wdGlvbnMsIHtcbiAgICAgICAgdXNlRHVhbHN0YWNrRW5kcG9pbnQ6IG9wdGlvbnMudXNlRHVhbHN0YWNrRW5kcG9pbnQgPz8gZmFsc2UsXG4gICAgICAgIHVzZUZpcHNFbmRwb2ludDogb3B0aW9ucy51c2VGaXBzRW5kcG9pbnQgPz8gZmFsc2UsXG4gICAgICAgIGRlZmF1bHRTaWduaW5nTmFtZTogXCJzc28tb2F1dGhcIixcbiAgICB9KTtcbn07XG5leHBvcnQgY29uc3QgY29tbW9uUGFyYW1zID0ge1xuICAgIFVzZUZJUFM6IHsgdHlwZTogXCJidWlsdEluUGFyYW1zXCIsIG5hbWU6IFwidXNlRmlwc0VuZHBvaW50XCIgfSxcbiAgICBFbmRwb2ludDogeyB0eXBlOiBcImJ1aWx0SW5QYXJhbXNcIiwgbmFtZTogXCJlbmRwb2ludFwiIH0sXG4gICAgUmVnaW9uOiB7IHR5cGU6IFwiYnVpbHRJblBhcmFtc1wiLCBuYW1lOiBcInJlZ2lvblwiIH0sXG4gICAgVXNlRHVhbFN0YWNrOiB7IHR5cGU6IFwiYnVpbHRJblBhcmFtc1wiLCBuYW1lOiBcInVzZUR1YWxzdGFja0VuZHBvaW50XCIgfSxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/endpoint/EndpointParameters.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/endpoint/endpointResolver.js":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/endpoint/endpointResolver.js ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultEndpointResolver: () => (/* binding */ defaultEndpointResolver)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/index.js\");\n/* harmony import */ var _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/index.js\");\n/* harmony import */ var _ruleset__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ruleset */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/endpoint/ruleset.js\");\n\n\n\nconst cache = new _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__.EndpointCache({\n    size: 50,\n    params: [\"Endpoint\", \"Region\", \"UseDualStack\", \"UseFIPS\"],\n});\nconst defaultEndpointResolver = (endpointParams, context = {}) => {\n    return cache.get(endpointParams, () => (0,_smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__.resolveEndpoint)(_ruleset__WEBPACK_IMPORTED_MODULE_2__.ruleSet, {\n        endpointParams: endpointParams,\n        logger: context.logger,\n    }));\n};\n_smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__.customEndpointFunctions.aws = _aws_sdk_util_endpoints__WEBPACK_IMPORTED_MODULE_0__.awsEndpointFunctions;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3Nzby1vaWRjL2VuZHBvaW50L2VuZHBvaW50UmVzb2x2ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUErRDtBQUNrQztBQUM3RDtBQUNwQyxrQkFBa0IsaUVBQWE7QUFDL0I7QUFDQTtBQUNBLENBQUM7QUFDTSw2REFBNkQ7QUFDcEUsMkNBQTJDLHVFQUFlLENBQUMsNkNBQU87QUFDbEU7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLDJFQUF1QixPQUFPLHlFQUFvQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK25lc3RlZC1jbGllbnRzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL25lc3RlZC1jbGllbnRzL2Rpc3QtZXMvc3VibW9kdWxlcy9zc28tb2lkYy9lbmRwb2ludC9lbmRwb2ludFJlc29sdmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGF3c0VuZHBvaW50RnVuY3Rpb25zIH0gZnJvbSBcIkBhd3Mtc2RrL3V0aWwtZW5kcG9pbnRzXCI7XG5pbXBvcnQgeyBjdXN0b21FbmRwb2ludEZ1bmN0aW9ucywgRW5kcG9pbnRDYWNoZSwgcmVzb2x2ZUVuZHBvaW50IH0gZnJvbSBcIkBzbWl0aHkvdXRpbC1lbmRwb2ludHNcIjtcbmltcG9ydCB7IHJ1bGVTZXQgfSBmcm9tIFwiLi9ydWxlc2V0XCI7XG5jb25zdCBjYWNoZSA9IG5ldyBFbmRwb2ludENhY2hlKHtcbiAgICBzaXplOiA1MCxcbiAgICBwYXJhbXM6IFtcIkVuZHBvaW50XCIsIFwiUmVnaW9uXCIsIFwiVXNlRHVhbFN0YWNrXCIsIFwiVXNlRklQU1wiXSxcbn0pO1xuZXhwb3J0IGNvbnN0IGRlZmF1bHRFbmRwb2ludFJlc29sdmVyID0gKGVuZHBvaW50UGFyYW1zLCBjb250ZXh0ID0ge30pID0+IHtcbiAgICByZXR1cm4gY2FjaGUuZ2V0KGVuZHBvaW50UGFyYW1zLCAoKSA9PiByZXNvbHZlRW5kcG9pbnQocnVsZVNldCwge1xuICAgICAgICBlbmRwb2ludFBhcmFtczogZW5kcG9pbnRQYXJhbXMsXG4gICAgICAgIGxvZ2dlcjogY29udGV4dC5sb2dnZXIsXG4gICAgfSkpO1xufTtcbmN1c3RvbUVuZHBvaW50RnVuY3Rpb25zLmF3cyA9IGF3c0VuZHBvaW50RnVuY3Rpb25zO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/endpoint/endpointResolver.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/endpoint/ruleset.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/endpoint/ruleset.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ruleSet: () => (/* binding */ ruleSet)\n/* harmony export */ });\nconst u = \"required\", v = \"fn\", w = \"argv\", x = \"ref\";\nconst a = true, b = \"isSet\", c = \"booleanEquals\", d = \"error\", e = \"endpoint\", f = \"tree\", g = \"PartitionResult\", h = \"getAttr\", i = { [u]: false, \"type\": \"String\" }, j = { [u]: true, \"default\": false, \"type\": \"Boolean\" }, k = { [x]: \"Endpoint\" }, l = { [v]: c, [w]: [{ [x]: \"UseFIPS\" }, true] }, m = { [v]: c, [w]: [{ [x]: \"UseDualStack\" }, true] }, n = {}, o = { [v]: h, [w]: [{ [x]: g }, \"supportsFIPS\"] }, p = { [x]: g }, q = { [v]: c, [w]: [true, { [v]: h, [w]: [p, \"supportsDualStack\"] }] }, r = [l], s = [m], t = [{ [x]: \"Region\" }];\nconst _data = { version: \"1.0\", parameters: { Region: i, UseDualStack: j, UseFIPS: j, Endpoint: i }, rules: [{ conditions: [{ [v]: b, [w]: [k] }], rules: [{ conditions: r, error: \"Invalid Configuration: FIPS and custom endpoint are not supported\", type: d }, { conditions: s, error: \"Invalid Configuration: Dualstack and custom endpoint are not supported\", type: d }, { endpoint: { url: k, properties: n, headers: n }, type: e }], type: f }, { conditions: [{ [v]: b, [w]: t }], rules: [{ conditions: [{ [v]: \"aws.partition\", [w]: t, assign: g }], rules: [{ conditions: [l, m], rules: [{ conditions: [{ [v]: c, [w]: [a, o] }, q], rules: [{ endpoint: { url: \"https://oidc-fips.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: n, headers: n }, type: e }], type: f }, { error: \"FIPS and DualStack are enabled, but this partition does not support one or both\", type: d }], type: f }, { conditions: r, rules: [{ conditions: [{ [v]: c, [w]: [o, a] }], rules: [{ conditions: [{ [v]: \"stringEquals\", [w]: [{ [v]: h, [w]: [p, \"name\"] }, \"aws-us-gov\"] }], endpoint: { url: \"https://oidc.{Region}.amazonaws.com\", properties: n, headers: n }, type: e }, { endpoint: { url: \"https://oidc-fips.{Region}.{PartitionResult#dnsSuffix}\", properties: n, headers: n }, type: e }], type: f }, { error: \"FIPS is enabled but this partition does not support FIPS\", type: d }], type: f }, { conditions: s, rules: [{ conditions: [q], rules: [{ endpoint: { url: \"https://oidc.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: n, headers: n }, type: e }], type: f }, { error: \"DualStack is enabled but this partition does not support DualStack\", type: d }], type: f }, { endpoint: { url: \"https://oidc.{Region}.{PartitionResult#dnsSuffix}\", properties: n, headers: n }, type: e }], type: f }], type: f }, { error: \"Invalid Configuration: Missing Region\", type: d }] };\nconst ruleSet = _data;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/endpoint/ruleset.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/index.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/index.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.$Command),\n/* harmony export */   AccessDeniedException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.AccessDeniedException),\n/* harmony export */   AuthorizationPendingException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.AuthorizationPendingException),\n/* harmony export */   CreateTokenCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.CreateTokenCommand),\n/* harmony export */   CreateTokenRequestFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.CreateTokenRequestFilterSensitiveLog),\n/* harmony export */   CreateTokenResponseFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.CreateTokenResponseFilterSensitiveLog),\n/* harmony export */   ExpiredTokenException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.ExpiredTokenException),\n/* harmony export */   InternalServerException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.InternalServerException),\n/* harmony export */   InvalidClientException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.InvalidClientException),\n/* harmony export */   InvalidGrantException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.InvalidGrantException),\n/* harmony export */   InvalidRequestException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.InvalidRequestException),\n/* harmony export */   InvalidScopeException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.InvalidScopeException),\n/* harmony export */   SSOOIDC: () => (/* reexport safe */ _SSOOIDC__WEBPACK_IMPORTED_MODULE_1__.SSOOIDC),\n/* harmony export */   SSOOIDCClient: () => (/* reexport safe */ _SSOOIDCClient__WEBPACK_IMPORTED_MODULE_0__.SSOOIDCClient),\n/* harmony export */   SSOOIDCServiceException: () => (/* reexport safe */ _models_SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_4__.SSOOIDCServiceException),\n/* harmony export */   SlowDownException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.SlowDownException),\n/* harmony export */   UnauthorizedClientException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.UnauthorizedClientException),\n/* harmony export */   UnsupportedGrantTypeException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.UnsupportedGrantTypeException),\n/* harmony export */   __Client: () => (/* reexport safe */ _SSOOIDCClient__WEBPACK_IMPORTED_MODULE_0__.__Client)\n/* harmony export */ });\n/* harmony import */ var _SSOOIDCClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SSOOIDCClient */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/SSOOIDCClient.js\");\n/* harmony import */ var _SSOOIDC__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SSOOIDC */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/SSOOIDC.js\");\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./commands */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/commands/index.js\");\n/* harmony import */ var _models__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./models */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/index.js\");\n/* harmony import */ var _models_SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./models/SSOOIDCServiceException */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/SSOOIDCServiceException.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3Nzby1vaWRjL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFnQztBQUNOO0FBQ0M7QUFDRjtBQUNrRCIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK25lc3RlZC1jbGllbnRzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL25lc3RlZC1jbGllbnRzL2Rpc3QtZXMvc3VibW9kdWxlcy9zc28tb2lkYy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9TU09PSURDQ2xpZW50XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9TU09PSURDXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jb21tYW5kc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vbW9kZWxzXCI7XG5leHBvcnQgeyBTU09PSURDU2VydmljZUV4Y2VwdGlvbiB9IGZyb20gXCIuL21vZGVscy9TU09PSURDU2VydmljZUV4Y2VwdGlvblwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/SSOOIDCServiceException.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/SSOOIDCServiceException.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SSOOIDCServiceException: () => (/* binding */ SSOOIDCServiceException),\n/* harmony export */   __ServiceException: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.ServiceException)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n\n\nclass SSOOIDCServiceException extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.ServiceException {\n    constructor(options) {\n        super(options);\n        Object.setPrototypeOf(this, SSOOIDCServiceException.prototype);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3Nzby1vaWRjL21vZGVscy9TU09PSURDU2VydmljZUV4Y2VwdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0Y7QUFDbEQ7QUFDdkIsc0NBQXNDLG1FQUFrQjtBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3Nzby1vaWRjL21vZGVscy9TU09PSURDU2VydmljZUV4Y2VwdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTZXJ2aWNlRXhjZXB0aW9uIGFzIF9fU2VydmljZUV4Y2VwdGlvbiwgfSBmcm9tIFwiQHNtaXRoeS9zbWl0aHktY2xpZW50XCI7XG5leHBvcnQgeyBfX1NlcnZpY2VFeGNlcHRpb24gfTtcbmV4cG9ydCBjbGFzcyBTU09PSURDU2VydmljZUV4Y2VwdGlvbiBleHRlbmRzIF9fU2VydmljZUV4Y2VwdGlvbiB7XG4gICAgY29uc3RydWN0b3Iob3B0aW9ucykge1xuICAgICAgICBzdXBlcihvcHRpb25zKTtcbiAgICAgICAgT2JqZWN0LnNldFByb3RvdHlwZU9mKHRoaXMsIFNTT09JRENTZXJ2aWNlRXhjZXB0aW9uLnByb3RvdHlwZSk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/SSOOIDCServiceException.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/index.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/index.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessDeniedException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.AccessDeniedException),\n/* harmony export */   AuthorizationPendingException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.AuthorizationPendingException),\n/* harmony export */   CreateTokenRequestFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.CreateTokenRequestFilterSensitiveLog),\n/* harmony export */   CreateTokenResponseFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.CreateTokenResponseFilterSensitiveLog),\n/* harmony export */   ExpiredTokenException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ExpiredTokenException),\n/* harmony export */   InternalServerException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InternalServerException),\n/* harmony export */   InvalidClientException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvalidClientException),\n/* harmony export */   InvalidGrantException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvalidGrantException),\n/* harmony export */   InvalidRequestException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvalidRequestException),\n/* harmony export */   InvalidScopeException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvalidScopeException),\n/* harmony export */   SlowDownException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.SlowDownException),\n/* harmony export */   UnauthorizedClientException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.UnauthorizedClientException),\n/* harmony export */   UnsupportedGrantTypeException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.UnsupportedGrantTypeException)\n/* harmony export */ });\n/* harmony import */ var _models_0__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/models_0.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3Nzby1vaWRjL21vZGVscy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUEyQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK25lc3RlZC1jbGllbnRzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL25lc3RlZC1jbGllbnRzL2Rpc3QtZXMvc3VibW9kdWxlcy9zc28tb2lkYy9tb2RlbHMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vbW9kZWxzXzBcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/models_0.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/models_0.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessDeniedException: () => (/* binding */ AccessDeniedException),\n/* harmony export */   AuthorizationPendingException: () => (/* binding */ AuthorizationPendingException),\n/* harmony export */   CreateTokenRequestFilterSensitiveLog: () => (/* binding */ CreateTokenRequestFilterSensitiveLog),\n/* harmony export */   CreateTokenResponseFilterSensitiveLog: () => (/* binding */ CreateTokenResponseFilterSensitiveLog),\n/* harmony export */   ExpiredTokenException: () => (/* binding */ ExpiredTokenException),\n/* harmony export */   InternalServerException: () => (/* binding */ InternalServerException),\n/* harmony export */   InvalidClientException: () => (/* binding */ InvalidClientException),\n/* harmony export */   InvalidGrantException: () => (/* binding */ InvalidGrantException),\n/* harmony export */   InvalidRequestException: () => (/* binding */ InvalidRequestException),\n/* harmony export */   InvalidScopeException: () => (/* binding */ InvalidScopeException),\n/* harmony export */   SlowDownException: () => (/* binding */ SlowDownException),\n/* harmony export */   UnauthorizedClientException: () => (/* binding */ UnauthorizedClientException),\n/* harmony export */   UnsupportedGrantTypeException: () => (/* binding */ UnsupportedGrantTypeException)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SSOOIDCServiceException */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/SSOOIDCServiceException.js\");\n\n\nclass AccessDeniedException extends _SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOOIDCServiceException {\n    name = \"AccessDeniedException\";\n    $fault = \"client\";\n    error;\n    error_description;\n    constructor(opts) {\n        super({\n            name: \"AccessDeniedException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, AccessDeniedException.prototype);\n        this.error = opts.error;\n        this.error_description = opts.error_description;\n    }\n}\nclass AuthorizationPendingException extends _SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOOIDCServiceException {\n    name = \"AuthorizationPendingException\";\n    $fault = \"client\";\n    error;\n    error_description;\n    constructor(opts) {\n        super({\n            name: \"AuthorizationPendingException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, AuthorizationPendingException.prototype);\n        this.error = opts.error;\n        this.error_description = opts.error_description;\n    }\n}\nconst CreateTokenRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.clientSecret && { clientSecret: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n    ...(obj.refreshToken && { refreshToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n    ...(obj.codeVerifier && { codeVerifier: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst CreateTokenResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.accessToken && { accessToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n    ...(obj.refreshToken && { refreshToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n    ...(obj.idToken && { idToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nclass ExpiredTokenException extends _SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOOIDCServiceException {\n    name = \"ExpiredTokenException\";\n    $fault = \"client\";\n    error;\n    error_description;\n    constructor(opts) {\n        super({\n            name: \"ExpiredTokenException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ExpiredTokenException.prototype);\n        this.error = opts.error;\n        this.error_description = opts.error_description;\n    }\n}\nclass InternalServerException extends _SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOOIDCServiceException {\n    name = \"InternalServerException\";\n    $fault = \"server\";\n    error;\n    error_description;\n    constructor(opts) {\n        super({\n            name: \"InternalServerException\",\n            $fault: \"server\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, InternalServerException.prototype);\n        this.error = opts.error;\n        this.error_description = opts.error_description;\n    }\n}\nclass InvalidClientException extends _SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOOIDCServiceException {\n    name = \"InvalidClientException\";\n    $fault = \"client\";\n    error;\n    error_description;\n    constructor(opts) {\n        super({\n            name: \"InvalidClientException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, InvalidClientException.prototype);\n        this.error = opts.error;\n        this.error_description = opts.error_description;\n    }\n}\nclass InvalidGrantException extends _SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOOIDCServiceException {\n    name = \"InvalidGrantException\";\n    $fault = \"client\";\n    error;\n    error_description;\n    constructor(opts) {\n        super({\n            name: \"InvalidGrantException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, InvalidGrantException.prototype);\n        this.error = opts.error;\n        this.error_description = opts.error_description;\n    }\n}\nclass InvalidRequestException extends _SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOOIDCServiceException {\n    name = \"InvalidRequestException\";\n    $fault = \"client\";\n    error;\n    error_description;\n    constructor(opts) {\n        super({\n            name: \"InvalidRequestException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, InvalidRequestException.prototype);\n        this.error = opts.error;\n        this.error_description = opts.error_description;\n    }\n}\nclass InvalidScopeException extends _SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOOIDCServiceException {\n    name = \"InvalidScopeException\";\n    $fault = \"client\";\n    error;\n    error_description;\n    constructor(opts) {\n        super({\n            name: \"InvalidScopeException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, InvalidScopeException.prototype);\n        this.error = opts.error;\n        this.error_description = opts.error_description;\n    }\n}\nclass SlowDownException extends _SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOOIDCServiceException {\n    name = \"SlowDownException\";\n    $fault = \"client\";\n    error;\n    error_description;\n    constructor(opts) {\n        super({\n            name: \"SlowDownException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, SlowDownException.prototype);\n        this.error = opts.error;\n        this.error_description = opts.error_description;\n    }\n}\nclass UnauthorizedClientException extends _SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOOIDCServiceException {\n    name = \"UnauthorizedClientException\";\n    $fault = \"client\";\n    error;\n    error_description;\n    constructor(opts) {\n        super({\n            name: \"UnauthorizedClientException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, UnauthorizedClientException.prototype);\n        this.error = opts.error;\n        this.error_description = opts.error_description;\n    }\n}\nclass UnsupportedGrantTypeException extends _SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOOIDCServiceException {\n    name = \"UnsupportedGrantTypeException\";\n    $fault = \"client\";\n    error;\n    error_description;\n    constructor(opts) {\n        super({\n            name: \"UnsupportedGrantTypeException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, UnsupportedGrantTypeException.prototype);\n        this.error = opts.error;\n        this.error_description = opts.error_description;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/models_0.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/protocols/Aws_restJson1.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/protocols/Aws_restJson1.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   de_CreateTokenCommand: () => (/* binding */ de_CreateTokenCommand),\n/* harmony export */   se_CreateTokenCommand: () => (/* binding */ se_CreateTokenCommand)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/json/parseJsonBody.js\");\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/models_0.js\");\n/* harmony import */ var _models_SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../models/SSOOIDCServiceException */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/models/SSOOIDCServiceException.js\");\n\n\n\n\n\nconst se_CreateTokenCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = {\n        \"content-type\": \"application/json\",\n    };\n    b.bp(\"/token\");\n    let body;\n    body = JSON.stringify((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(input, {\n        clientId: [],\n        clientSecret: [],\n        code: [],\n        codeVerifier: [],\n        deviceCode: [],\n        grantType: [],\n        redirectUri: [],\n        refreshToken: [],\n        scope: (_) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json)(_),\n    }));\n    b.m(\"POST\").h(headers).b(body);\n    return b.build();\n};\nconst de_CreateTokenCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectObject)(await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.parseJsonBody)(output.body, context)), \"body\");\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        accessToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        expiresIn: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectInt32,\n        idToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        refreshToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        tokenType: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nconst de_CommandError = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.parseJsonErrorBody)(output.body, context),\n    };\n    const errorCode = (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.loadRestJsonErrorCode)(output, parsedOutput.body);\n    switch (errorCode) {\n        case \"AccessDeniedException\":\n        case \"com.amazonaws.ssooidc#AccessDeniedException\":\n            throw await de_AccessDeniedExceptionRes(parsedOutput, context);\n        case \"AuthorizationPendingException\":\n        case \"com.amazonaws.ssooidc#AuthorizationPendingException\":\n            throw await de_AuthorizationPendingExceptionRes(parsedOutput, context);\n        case \"ExpiredTokenException\":\n        case \"com.amazonaws.ssooidc#ExpiredTokenException\":\n            throw await de_ExpiredTokenExceptionRes(parsedOutput, context);\n        case \"InternalServerException\":\n        case \"com.amazonaws.ssooidc#InternalServerException\":\n            throw await de_InternalServerExceptionRes(parsedOutput, context);\n        case \"InvalidClientException\":\n        case \"com.amazonaws.ssooidc#InvalidClientException\":\n            throw await de_InvalidClientExceptionRes(parsedOutput, context);\n        case \"InvalidGrantException\":\n        case \"com.amazonaws.ssooidc#InvalidGrantException\":\n            throw await de_InvalidGrantExceptionRes(parsedOutput, context);\n        case \"InvalidRequestException\":\n        case \"com.amazonaws.ssooidc#InvalidRequestException\":\n            throw await de_InvalidRequestExceptionRes(parsedOutput, context);\n        case \"InvalidScopeException\":\n        case \"com.amazonaws.ssooidc#InvalidScopeException\":\n            throw await de_InvalidScopeExceptionRes(parsedOutput, context);\n        case \"SlowDownException\":\n        case \"com.amazonaws.ssooidc#SlowDownException\":\n            throw await de_SlowDownExceptionRes(parsedOutput, context);\n        case \"UnauthorizedClientException\":\n        case \"com.amazonaws.ssooidc#UnauthorizedClientException\":\n            throw await de_UnauthorizedClientExceptionRes(parsedOutput, context);\n        case \"UnsupportedGrantTypeException\":\n        case \"com.amazonaws.ssooidc#UnsupportedGrantTypeException\":\n            throw await de_UnsupportedGrantTypeExceptionRes(parsedOutput, context);\n        default:\n            const parsedBody = parsedOutput.body;\n            return throwDefaultError({\n                output,\n                parsedBody,\n                errorCode,\n            });\n    }\n};\nconst throwDefaultError = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.withBaseException)(_models_SSOOIDCServiceException__WEBPACK_IMPORTED_MODULE_3__.SSOOIDCServiceException);\nconst de_AccessDeniedExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        error: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        error_description: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.AccessDeniedException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_AuthorizationPendingExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        error: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        error_description: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.AuthorizationPendingException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_ExpiredTokenExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        error: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        error_description: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.ExpiredTokenException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_InternalServerExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        error: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        error_description: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.InternalServerException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_InvalidClientExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        error: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        error_description: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.InvalidClientException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_InvalidGrantExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        error: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        error_description: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.InvalidGrantException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_InvalidRequestExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        error: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        error_description: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.InvalidRequestException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_InvalidScopeExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        error: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        error_description: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.InvalidScopeException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_SlowDownExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        error: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        error_description: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.SlowDownException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_UnauthorizedClientExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        error: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        error_description: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.UnauthorizedClientException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_UnsupportedGrantTypeExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        error: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        error_description: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.UnsupportedGrantTypeException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst deserializeMetadata = (output) => ({\n    httpStatusCode: output.statusCode,\n    requestId: output.headers[\"x-amzn-requestid\"] ?? output.headers[\"x-amzn-request-id\"] ?? output.headers[\"x-amz-request-id\"],\n    extendedRequestId: output.headers[\"x-amz-id-2\"],\n    cfId: output.headers[\"x-amz-cf-id\"],\n});\nconst collectBodyString = (streamBody, context) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.collectBody)(streamBody, context).then((body) => context.utf8Encoder(body));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/protocols/Aws_restJson1.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/runtimeConfig.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/runtimeConfig.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRuntimeConfig: () => (/* binding */ getRuntimeConfig)\n/* harmony export */ });\n/* harmony import */ var _package_json__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../package.json */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/package.json\");\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/emitWarningIfUnsupportedVersion.js\");\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/NODE_AUTH_SCHEME_PREFERENCE_OPTIONS.js\");\n/* harmony import */ var _aws_sdk_util_user_agent_node__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/util-user-agent-node */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/index.js\");\n/* harmony import */ var _smithy_config_resolver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/config-resolver */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_hash_node__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/hash-node */ \"(rsc)/./node_modules/.pnpm/@smithy+hash-node@4.0.4/node_modules/@smithy/hash-node/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @smithy/middleware-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/index.js\");\n/* harmony import */ var _smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @smithy/node-config-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+node-config-provider@4.1.3/node_modules/@smithy/node-config-provider/dist-es/index.js\");\n/* harmony import */ var _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @smithy/node-http-handler */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/index.js\");\n/* harmony import */ var _smithy_util_body_length_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @smithy/util-body-length-node */ \"(rsc)/./node_modules/.pnpm/@smithy+util-body-length-node@4.0.0/node_modules/@smithy/util-body-length-node/dist-es/index.js\");\n/* harmony import */ var _smithy_util_retry__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @smithy/util-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js\");\n/* harmony import */ var _runtimeConfig_shared__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./runtimeConfig.shared */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/runtimeConfig.shared.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _smithy_util_defaults_mode_node__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @smithy/util-defaults-mode-node */ \"(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst getRuntimeConfig = (config) => {\n    (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_10__.emitWarningIfUnsupportedVersion)(process.version);\n    const defaultsMode = (0,_smithy_util_defaults_mode_node__WEBPACK_IMPORTED_MODULE_11__.resolveDefaultsModeConfig)(config);\n    const defaultConfigProvider = () => defaultsMode().then(_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_10__.loadConfigsForDefaultMode);\n    const clientSharedValues = (0,_runtimeConfig_shared__WEBPACK_IMPORTED_MODULE_9__.getRuntimeConfig)(config);\n    (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_12__.emitWarningIfUnsupportedVersion)(process.version);\n    const loaderConfig = {\n        profile: config?.profile,\n        logger: clientSharedValues.logger,\n    };\n    return {\n        ...clientSharedValues,\n        ...config,\n        runtime: \"node\",\n        defaultsMode,\n        authSchemePreference: config?.authSchemePreference ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_5__.loadConfig)(_aws_sdk_core__WEBPACK_IMPORTED_MODULE_13__.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS, loaderConfig),\n        bodyLengthChecker: config?.bodyLengthChecker ?? _smithy_util_body_length_node__WEBPACK_IMPORTED_MODULE_7__.calculateBodyLength,\n        defaultUserAgentProvider: config?.defaultUserAgentProvider ??\n            (0,_aws_sdk_util_user_agent_node__WEBPACK_IMPORTED_MODULE_1__.createDefaultUserAgentProvider)({ serviceId: clientSharedValues.serviceId, clientVersion: _package_json__WEBPACK_IMPORTED_MODULE_0__.version }),\n        maxAttempts: config?.maxAttempts ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_5__.loadConfig)(_smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_4__.NODE_MAX_ATTEMPT_CONFIG_OPTIONS, config),\n        region: config?.region ??\n            (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_5__.loadConfig)(_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_2__.NODE_REGION_CONFIG_OPTIONS, { ..._smithy_config_resolver__WEBPACK_IMPORTED_MODULE_2__.NODE_REGION_CONFIG_FILE_OPTIONS, ...loaderConfig }),\n        requestHandler: _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_6__.NodeHttpHandler.create(config?.requestHandler ?? defaultConfigProvider),\n        retryMode: config?.retryMode ??\n            (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_5__.loadConfig)({\n                ..._smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_4__.NODE_RETRY_MODE_CONFIG_OPTIONS,\n                default: async () => (await defaultConfigProvider()).retryMode || _smithy_util_retry__WEBPACK_IMPORTED_MODULE_8__.DEFAULT_RETRY_MODE,\n            }, config),\n        sha256: config?.sha256 ?? _smithy_hash_node__WEBPACK_IMPORTED_MODULE_3__.Hash.bind(null, \"sha256\"),\n        streamCollector: config?.streamCollector ?? _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_6__.streamCollector,\n        useDualstackEndpoint: config?.useDualstackEndpoint ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_5__.loadConfig)(_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_2__.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS, loaderConfig),\n        useFipsEndpoint: config?.useFipsEndpoint ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_5__.loadConfig)(_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_2__.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS, loaderConfig),\n        userAgentAppId: config?.userAgentAppId ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_5__.loadConfig)(_aws_sdk_util_user_agent_node__WEBPACK_IMPORTED_MODULE_1__.NODE_APP_ID_CONFIG_OPTIONS, loaderConfig),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3Nzby1vaWRjL3J1bnRpbWVDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWdEO0FBQ3dFO0FBQ2I7QUFDK0U7QUFDako7QUFDa0U7QUFDL0I7QUFDbUI7QUFDM0I7QUFDWjtBQUM0QjtBQUNsQjtBQUNVO0FBQ0o7QUFDakU7QUFDUCxJQUFJLHVGQUErQjtBQUNuQyx5QkFBeUIsMkZBQXlCO0FBQ2xELDREQUE0RCw2RUFBeUI7QUFDckYsK0JBQStCLHVFQUFzQjtBQUNyRCxJQUFJLCtFQUFlO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhEQUE4RCx3RUFBYyxDQUFDLCtFQUFtQztBQUNoSCx3REFBd0QsOEVBQW1CO0FBQzNFO0FBQ0EsWUFBWSw2RkFBOEIsR0FBRyx3REFBd0Qsa0RBQW1CLEVBQUU7QUFDMUgsNENBQTRDLHdFQUFjLENBQUMscUZBQStCO0FBQzFGO0FBQ0EsWUFBWSx3RUFBYyxDQUFDLCtFQUEwQixJQUFJLEdBQUcsb0ZBQStCLG1CQUFtQjtBQUM5Ryx3QkFBd0Isc0VBQWM7QUFDdEM7QUFDQSxZQUFZLHdFQUFjO0FBQzFCLG1CQUFtQixvRkFBOEI7QUFDakQsa0ZBQWtGLGtFQUFrQjtBQUNwRyxhQUFhO0FBQ2Isa0NBQWtDLG1EQUFJO0FBQ3RDLG9EQUFvRCxzRUFBZTtBQUNuRSw4REFBOEQsd0VBQWMsQ0FBQywrRkFBMEM7QUFDdkgsb0RBQW9ELHdFQUFjLENBQUMsMEZBQXFDO0FBQ3hHLGtEQUFrRCx3RUFBYyxDQUFDLHFGQUEwQjtBQUMzRjtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3Nzby1vaWRjL3J1bnRpbWVDb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHBhY2thZ2VJbmZvIGZyb20gXCIuLi8uLi8uLi9wYWNrYWdlLmpzb25cIjtcbmltcG9ydCB7IE5PREVfQVVUSF9TQ0hFTUVfUFJFRkVSRU5DRV9PUFRJT05TLCBlbWl0V2FybmluZ0lmVW5zdXBwb3J0ZWRWZXJzaW9uIGFzIGF3c0NoZWNrVmVyc2lvbiB9IGZyb20gXCJAYXdzLXNkay9jb3JlXCI7XG5pbXBvcnQgeyBOT0RFX0FQUF9JRF9DT05GSUdfT1BUSU9OUywgY3JlYXRlRGVmYXVsdFVzZXJBZ2VudFByb3ZpZGVyIH0gZnJvbSBcIkBhd3Mtc2RrL3V0aWwtdXNlci1hZ2VudC1ub2RlXCI7XG5pbXBvcnQgeyBOT0RFX1JFR0lPTl9DT05GSUdfRklMRV9PUFRJT05TLCBOT0RFX1JFR0lPTl9DT05GSUdfT1BUSU9OUywgTk9ERV9VU0VfRFVBTFNUQUNLX0VORFBPSU5UX0NPTkZJR19PUFRJT05TLCBOT0RFX1VTRV9GSVBTX0VORFBPSU5UX0NPTkZJR19PUFRJT05TLCB9IGZyb20gXCJAc21pdGh5L2NvbmZpZy1yZXNvbHZlclwiO1xuaW1wb3J0IHsgSGFzaCB9IGZyb20gXCJAc21pdGh5L2hhc2gtbm9kZVwiO1xuaW1wb3J0IHsgTk9ERV9NQVhfQVRURU1QVF9DT05GSUdfT1BUSU9OUywgTk9ERV9SRVRSWV9NT0RFX0NPTkZJR19PUFRJT05TIH0gZnJvbSBcIkBzbWl0aHkvbWlkZGxld2FyZS1yZXRyeVwiO1xuaW1wb3J0IHsgbG9hZENvbmZpZyBhcyBsb2FkTm9kZUNvbmZpZyB9IGZyb20gXCJAc21pdGh5L25vZGUtY29uZmlnLXByb3ZpZGVyXCI7XG5pbXBvcnQgeyBOb2RlSHR0cEhhbmRsZXIgYXMgUmVxdWVzdEhhbmRsZXIsIHN0cmVhbUNvbGxlY3RvciB9IGZyb20gXCJAc21pdGh5L25vZGUtaHR0cC1oYW5kbGVyXCI7XG5pbXBvcnQgeyBjYWxjdWxhdGVCb2R5TGVuZ3RoIH0gZnJvbSBcIkBzbWl0aHkvdXRpbC1ib2R5LWxlbmd0aC1ub2RlXCI7XG5pbXBvcnQgeyBERUZBVUxUX1JFVFJZX01PREUgfSBmcm9tIFwiQHNtaXRoeS91dGlsLXJldHJ5XCI7XG5pbXBvcnQgeyBnZXRSdW50aW1lQ29uZmlnIGFzIGdldFNoYXJlZFJ1bnRpbWVDb25maWcgfSBmcm9tIFwiLi9ydW50aW1lQ29uZmlnLnNoYXJlZFwiO1xuaW1wb3J0IHsgbG9hZENvbmZpZ3NGb3JEZWZhdWx0TW9kZSB9IGZyb20gXCJAc21pdGh5L3NtaXRoeS1jbGllbnRcIjtcbmltcG9ydCB7IHJlc29sdmVEZWZhdWx0c01vZGVDb25maWcgfSBmcm9tIFwiQHNtaXRoeS91dGlsLWRlZmF1bHRzLW1vZGUtbm9kZVwiO1xuaW1wb3J0IHsgZW1pdFdhcm5pbmdJZlVuc3VwcG9ydGVkVmVyc2lvbiB9IGZyb20gXCJAc21pdGh5L3NtaXRoeS1jbGllbnRcIjtcbmV4cG9ydCBjb25zdCBnZXRSdW50aW1lQ29uZmlnID0gKGNvbmZpZykgPT4ge1xuICAgIGVtaXRXYXJuaW5nSWZVbnN1cHBvcnRlZFZlcnNpb24ocHJvY2Vzcy52ZXJzaW9uKTtcbiAgICBjb25zdCBkZWZhdWx0c01vZGUgPSByZXNvbHZlRGVmYXVsdHNNb2RlQ29uZmlnKGNvbmZpZyk7XG4gICAgY29uc3QgZGVmYXVsdENvbmZpZ1Byb3ZpZGVyID0gKCkgPT4gZGVmYXVsdHNNb2RlKCkudGhlbihsb2FkQ29uZmlnc0ZvckRlZmF1bHRNb2RlKTtcbiAgICBjb25zdCBjbGllbnRTaGFyZWRWYWx1ZXMgPSBnZXRTaGFyZWRSdW50aW1lQ29uZmlnKGNvbmZpZyk7XG4gICAgYXdzQ2hlY2tWZXJzaW9uKHByb2Nlc3MudmVyc2lvbik7XG4gICAgY29uc3QgbG9hZGVyQ29uZmlnID0ge1xuICAgICAgICBwcm9maWxlOiBjb25maWc/LnByb2ZpbGUsXG4gICAgICAgIGxvZ2dlcjogY2xpZW50U2hhcmVkVmFsdWVzLmxvZ2dlcixcbiAgICB9O1xuICAgIHJldHVybiB7XG4gICAgICAgIC4uLmNsaWVudFNoYXJlZFZhbHVlcyxcbiAgICAgICAgLi4uY29uZmlnLFxuICAgICAgICBydW50aW1lOiBcIm5vZGVcIixcbiAgICAgICAgZGVmYXVsdHNNb2RlLFxuICAgICAgICBhdXRoU2NoZW1lUHJlZmVyZW5jZTogY29uZmlnPy5hdXRoU2NoZW1lUHJlZmVyZW5jZSA/PyBsb2FkTm9kZUNvbmZpZyhOT0RFX0FVVEhfU0NIRU1FX1BSRUZFUkVOQ0VfT1BUSU9OUywgbG9hZGVyQ29uZmlnKSxcbiAgICAgICAgYm9keUxlbmd0aENoZWNrZXI6IGNvbmZpZz8uYm9keUxlbmd0aENoZWNrZXIgPz8gY2FsY3VsYXRlQm9keUxlbmd0aCxcbiAgICAgICAgZGVmYXVsdFVzZXJBZ2VudFByb3ZpZGVyOiBjb25maWc/LmRlZmF1bHRVc2VyQWdlbnRQcm92aWRlciA/P1xuICAgICAgICAgICAgY3JlYXRlRGVmYXVsdFVzZXJBZ2VudFByb3ZpZGVyKHsgc2VydmljZUlkOiBjbGllbnRTaGFyZWRWYWx1ZXMuc2VydmljZUlkLCBjbGllbnRWZXJzaW9uOiBwYWNrYWdlSW5mby52ZXJzaW9uIH0pLFxuICAgICAgICBtYXhBdHRlbXB0czogY29uZmlnPy5tYXhBdHRlbXB0cyA/PyBsb2FkTm9kZUNvbmZpZyhOT0RFX01BWF9BVFRFTVBUX0NPTkZJR19PUFRJT05TLCBjb25maWcpLFxuICAgICAgICByZWdpb246IGNvbmZpZz8ucmVnaW9uID8/XG4gICAgICAgICAgICBsb2FkTm9kZUNvbmZpZyhOT0RFX1JFR0lPTl9DT05GSUdfT1BUSU9OUywgeyAuLi5OT0RFX1JFR0lPTl9DT05GSUdfRklMRV9PUFRJT05TLCAuLi5sb2FkZXJDb25maWcgfSksXG4gICAgICAgIHJlcXVlc3RIYW5kbGVyOiBSZXF1ZXN0SGFuZGxlci5jcmVhdGUoY29uZmlnPy5yZXF1ZXN0SGFuZGxlciA/PyBkZWZhdWx0Q29uZmlnUHJvdmlkZXIpLFxuICAgICAgICByZXRyeU1vZGU6IGNvbmZpZz8ucmV0cnlNb2RlID8/XG4gICAgICAgICAgICBsb2FkTm9kZUNvbmZpZyh7XG4gICAgICAgICAgICAgICAgLi4uTk9ERV9SRVRSWV9NT0RFX0NPTkZJR19PUFRJT05TLFxuICAgICAgICAgICAgICAgIGRlZmF1bHQ6IGFzeW5jICgpID0+IChhd2FpdCBkZWZhdWx0Q29uZmlnUHJvdmlkZXIoKSkucmV0cnlNb2RlIHx8IERFRkFVTFRfUkVUUllfTU9ERSxcbiAgICAgICAgICAgIH0sIGNvbmZpZyksXG4gICAgICAgIHNoYTI1NjogY29uZmlnPy5zaGEyNTYgPz8gSGFzaC5iaW5kKG51bGwsIFwic2hhMjU2XCIpLFxuICAgICAgICBzdHJlYW1Db2xsZWN0b3I6IGNvbmZpZz8uc3RyZWFtQ29sbGVjdG9yID8/IHN0cmVhbUNvbGxlY3RvcixcbiAgICAgICAgdXNlRHVhbHN0YWNrRW5kcG9pbnQ6IGNvbmZpZz8udXNlRHVhbHN0YWNrRW5kcG9pbnQgPz8gbG9hZE5vZGVDb25maWcoTk9ERV9VU0VfRFVBTFNUQUNLX0VORFBPSU5UX0NPTkZJR19PUFRJT05TLCBsb2FkZXJDb25maWcpLFxuICAgICAgICB1c2VGaXBzRW5kcG9pbnQ6IGNvbmZpZz8udXNlRmlwc0VuZHBvaW50ID8/IGxvYWROb2RlQ29uZmlnKE5PREVfVVNFX0ZJUFNfRU5EUE9JTlRfQ09ORklHX09QVElPTlMsIGxvYWRlckNvbmZpZyksXG4gICAgICAgIHVzZXJBZ2VudEFwcElkOiBjb25maWc/LnVzZXJBZ2VudEFwcElkID8/IGxvYWROb2RlQ29uZmlnKE5PREVfQVBQX0lEX0NPTkZJR19PUFRJT05TLCBsb2FkZXJDb25maWcpLFxuICAgIH07XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/runtimeConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/runtimeConfig.shared.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/runtimeConfig.shared.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRuntimeConfig: () => (/* binding */ getRuntimeConfig)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/AwsSdkSigV4Signer.js\");\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _smithy_url_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/url-parser */ \"(rsc)/./node_modules/.pnpm/@smithy+url-parser@4.0.4/node_modules/@smithy/url-parser/dist-es/index.js\");\n/* harmony import */ var _smithy_util_base64__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/util-base64 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n/* harmony import */ var _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./auth/httpAuthSchemeProvider */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/auth/httpAuthSchemeProvider.js\");\n/* harmony import */ var _endpoint_endpointResolver__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./endpoint/endpointResolver */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/endpoint/endpointResolver.js\");\n\n\n\n\n\n\n\n\nconst getRuntimeConfig = (config) => {\n    return {\n        apiVersion: \"2019-06-10\",\n        base64Decoder: config?.base64Decoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_3__.fromBase64,\n        base64Encoder: config?.base64Encoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_3__.toBase64,\n        disableHostPrefix: config?.disableHostPrefix ?? false,\n        endpointProvider: config?.endpointProvider ?? _endpoint_endpointResolver__WEBPACK_IMPORTED_MODULE_6__.defaultEndpointResolver,\n        extensions: config?.extensions ?? [],\n        httpAuthSchemeProvider: config?.httpAuthSchemeProvider ?? _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_5__.defaultSSOOIDCHttpAuthSchemeProvider,\n        httpAuthSchemes: config?.httpAuthSchemes ?? [\n            {\n                schemeId: \"aws.auth#sigv4\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"aws.auth#sigv4\"),\n                signer: new _aws_sdk_core__WEBPACK_IMPORTED_MODULE_7__.AwsSdkSigV4Signer(),\n            },\n            {\n                schemeId: \"smithy.api#noAuth\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"smithy.api#noAuth\") || (async () => ({})),\n                signer: new _smithy_core__WEBPACK_IMPORTED_MODULE_0__.NoAuthSigner(),\n            },\n        ],\n        logger: config?.logger ?? new _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.NoOpLogger(),\n        serviceId: config?.serviceId ?? \"SSO OIDC\",\n        urlParser: config?.urlParser ?? _smithy_url_parser__WEBPACK_IMPORTED_MODULE_2__.parseUrl,\n        utf8Decoder: config?.utf8Decoder ?? _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_4__.fromUtf8,\n        utf8Encoder: config?.utf8Encoder ?? _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_4__.toUtf8,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/runtimeConfig.shared.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/runtimeExtensions.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/runtimeExtensions.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveRuntimeExtensions: () => (/* binding */ resolveRuntimeExtensions)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_region_config_resolver__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/region-config-resolver */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _auth_httpAuthExtensionConfiguration__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./auth/httpAuthExtensionConfiguration */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/auth/httpAuthExtensionConfiguration.js\");\n\n\n\n\nconst resolveRuntimeExtensions = (runtimeConfig, extensions) => {\n    const extensionConfiguration = Object.assign((0,_aws_sdk_region_config_resolver__WEBPACK_IMPORTED_MODULE_0__.getAwsRegionExtensionConfiguration)(runtimeConfig), (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.getDefaultExtensionConfiguration)(runtimeConfig), (0,_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__.getHttpHandlerExtensionConfiguration)(runtimeConfig), (0,_auth_httpAuthExtensionConfiguration__WEBPACK_IMPORTED_MODULE_3__.getHttpAuthExtensionConfiguration)(runtimeConfig));\n    extensions.forEach((extension) => extension.configure(extensionConfiguration));\n    return Object.assign(runtimeConfig, (0,_aws_sdk_region_config_resolver__WEBPACK_IMPORTED_MODULE_0__.resolveAwsRegionExtensionConfiguration)(extensionConfiguration), (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.resolveDefaultRuntimeConfig)(extensionConfiguration), (0,_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__.resolveHttpHandlerRuntimeConfig)(extensionConfiguration), (0,_auth_httpAuthExtensionConfiguration__WEBPACK_IMPORTED_MODULE_3__.resolveHttpAuthRuntimeConfig)(extensionConfiguration));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sso-oidc/runtimeExtensions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/STS.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/STS.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STS: () => (/* binding */ STS)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _commands_AssumeRoleCommand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./commands/AssumeRoleCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleCommand.js\");\n/* harmony import */ var _commands_AssumeRoleWithWebIdentityCommand__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./commands/AssumeRoleWithWebIdentityCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleWithWebIdentityCommand.js\");\n/* harmony import */ var _STSClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./STSClient */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/STSClient.js\");\n\n\n\n\nconst commands = {\n    AssumeRoleCommand: _commands_AssumeRoleCommand__WEBPACK_IMPORTED_MODULE_1__.AssumeRoleCommand,\n    AssumeRoleWithWebIdentityCommand: _commands_AssumeRoleWithWebIdentityCommand__WEBPACK_IMPORTED_MODULE_2__.AssumeRoleWithWebIdentityCommand,\n};\nclass STS extends _STSClient__WEBPACK_IMPORTED_MODULE_3__.STSClient {\n}\n(0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.createAggregatedClient)(commands, STS);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3N0cy9TVFMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBK0Q7QUFDRTtBQUMrQjtBQUN4RDtBQUN4QztBQUNBLHFCQUFxQjtBQUNyQixvQ0FBb0M7QUFDcEM7QUFDTyxrQkFBa0IsaURBQVM7QUFDbEM7QUFDQSw2RUFBc0IiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytuZXN0ZWQtY2xpZW50c0AzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9uZXN0ZWQtY2xpZW50cy9kaXN0LWVzL3N1Ym1vZHVsZXMvc3RzL1NUUy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVBZ2dyZWdhdGVkQ2xpZW50IH0gZnJvbSBcIkBzbWl0aHkvc21pdGh5LWNsaWVudFwiO1xuaW1wb3J0IHsgQXNzdW1lUm9sZUNvbW1hbmQgfSBmcm9tIFwiLi9jb21tYW5kcy9Bc3N1bWVSb2xlQ29tbWFuZFwiO1xuaW1wb3J0IHsgQXNzdW1lUm9sZVdpdGhXZWJJZGVudGl0eUNvbW1hbmQsIH0gZnJvbSBcIi4vY29tbWFuZHMvQXNzdW1lUm9sZVdpdGhXZWJJZGVudGl0eUNvbW1hbmRcIjtcbmltcG9ydCB7IFNUU0NsaWVudCB9IGZyb20gXCIuL1NUU0NsaWVudFwiO1xuY29uc3QgY29tbWFuZHMgPSB7XG4gICAgQXNzdW1lUm9sZUNvbW1hbmQsXG4gICAgQXNzdW1lUm9sZVdpdGhXZWJJZGVudGl0eUNvbW1hbmQsXG59O1xuZXhwb3J0IGNsYXNzIFNUUyBleHRlbmRzIFNUU0NsaWVudCB7XG59XG5jcmVhdGVBZ2dyZWdhdGVkQ2xpZW50KGNvbW1hbmRzLCBTVFMpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/STS.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/STSClient.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/STSClient.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STSClient: () => (/* binding */ STSClient),\n/* harmony export */   __Client: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_9__.Client)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_middleware_host_header__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/middleware-host-header */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-host-header@3.821.0/node_modules/@aws-sdk/middleware-host-header/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_middleware_logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/middleware-logger */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-logger@3.821.0/node_modules/@aws-sdk/middleware-logger/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_middleware_recursion_detection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/middleware-recursion-detection */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-recursion-detection@3.821.0/node_modules/@aws-sdk/middleware-recursion-detection/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @aws-sdk/middleware-user-agent */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/index.js\");\n/* harmony import */ var _smithy_config_resolver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @smithy/config-resolver */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_content_length__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @smithy/middleware-content-length */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-content-length@4.0.4/node_modules/@smithy/middleware-content-length/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @smithy/middleware-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./auth/httpAuthSchemeProvider */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/auth/httpAuthSchemeProvider.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/EndpointParameters.js\");\n/* harmony import */ var _runtimeConfig__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./runtimeConfig */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeConfig.js\");\n/* harmony import */ var _runtimeExtensions__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./runtimeExtensions */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeExtensions.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nclass STSClient extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_9__.Client {\n    config;\n    constructor(...[configuration]) {\n        const _config_0 = (0,_runtimeConfig__WEBPACK_IMPORTED_MODULE_12__.getRuntimeConfig)(configuration || {});\n        super(_config_0);\n        this.initConfig = _config_0;\n        const _config_1 = (0,_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_11__.resolveClientEndpointParameters)(_config_0);\n        const _config_2 = (0,_aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_3__.resolveUserAgentConfig)(_config_1);\n        const _config_3 = (0,_smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_8__.resolveRetryConfig)(_config_2);\n        const _config_4 = (0,_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_4__.resolveRegionConfig)(_config_3);\n        const _config_5 = (0,_aws_sdk_middleware_host_header__WEBPACK_IMPORTED_MODULE_0__.resolveHostHeaderConfig)(_config_4);\n        const _config_6 = (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_7__.resolveEndpointConfig)(_config_5);\n        const _config_7 = (0,_auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_10__.resolveHttpAuthSchemeConfig)(_config_6);\n        const _config_8 = (0,_runtimeExtensions__WEBPACK_IMPORTED_MODULE_13__.resolveRuntimeExtensions)(_config_7, configuration?.extensions || []);\n        this.config = _config_8;\n        this.middlewareStack.use((0,_aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_3__.getUserAgentPlugin)(this.config));\n        this.middlewareStack.use((0,_smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_8__.getRetryPlugin)(this.config));\n        this.middlewareStack.use((0,_smithy_middleware_content_length__WEBPACK_IMPORTED_MODULE_6__.getContentLengthPlugin)(this.config));\n        this.middlewareStack.use((0,_aws_sdk_middleware_host_header__WEBPACK_IMPORTED_MODULE_0__.getHostHeaderPlugin)(this.config));\n        this.middlewareStack.use((0,_aws_sdk_middleware_logger__WEBPACK_IMPORTED_MODULE_1__.getLoggerPlugin)(this.config));\n        this.middlewareStack.use((0,_aws_sdk_middleware_recursion_detection__WEBPACK_IMPORTED_MODULE_2__.getRecursionDetectionPlugin)(this.config));\n        this.middlewareStack.use((0,_smithy_core__WEBPACK_IMPORTED_MODULE_5__.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config, {\n            httpAuthSchemeParametersProvider: _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_10__.defaultSTSHttpAuthSchemeParametersProvider,\n            identityProviderConfigProvider: async (config) => new _smithy_core__WEBPACK_IMPORTED_MODULE_5__.DefaultIdentityProviderConfig({\n                \"aws.auth#sigv4\": config.credentials,\n            }),\n        }));\n        this.middlewareStack.use((0,_smithy_core__WEBPACK_IMPORTED_MODULE_5__.getHttpSigningPlugin)(this.config));\n    }\n    destroy() {\n        super.destroy();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/STSClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/auth/httpAuthExtensionConfiguration.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/auth/httpAuthExtensionConfiguration.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHttpAuthExtensionConfiguration: () => (/* binding */ getHttpAuthExtensionConfiguration),\n/* harmony export */   resolveHttpAuthRuntimeConfig: () => (/* binding */ resolveHttpAuthRuntimeConfig)\n/* harmony export */ });\nconst getHttpAuthExtensionConfiguration = (runtimeConfig) => {\n    const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;\n    let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;\n    let _credentials = runtimeConfig.credentials;\n    return {\n        setHttpAuthScheme(httpAuthScheme) {\n            const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);\n            if (index === -1) {\n                _httpAuthSchemes.push(httpAuthScheme);\n            }\n            else {\n                _httpAuthSchemes.splice(index, 1, httpAuthScheme);\n            }\n        },\n        httpAuthSchemes() {\n            return _httpAuthSchemes;\n        },\n        setHttpAuthSchemeProvider(httpAuthSchemeProvider) {\n            _httpAuthSchemeProvider = httpAuthSchemeProvider;\n        },\n        httpAuthSchemeProvider() {\n            return _httpAuthSchemeProvider;\n        },\n        setCredentials(credentials) {\n            _credentials = credentials;\n        },\n        credentials() {\n            return _credentials;\n        },\n    };\n};\nconst resolveHttpAuthRuntimeConfig = (config) => {\n    return {\n        httpAuthSchemes: config.httpAuthSchemes(),\n        httpAuthSchemeProvider: config.httpAuthSchemeProvider(),\n        credentials: config.credentials(),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/auth/httpAuthExtensionConfiguration.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/auth/httpAuthSchemeProvider.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/auth/httpAuthSchemeProvider.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultSTSHttpAuthSchemeParametersProvider: () => (/* binding */ defaultSTSHttpAuthSchemeParametersProvider),\n/* harmony export */   defaultSTSHttpAuthSchemeProvider: () => (/* binding */ defaultSTSHttpAuthSchemeProvider),\n/* harmony export */   resolveHttpAuthSchemeConfig: () => (/* binding */ resolveHttpAuthSchemeConfig),\n/* harmony export */   resolveStsAuthConfig: () => (/* binding */ resolveStsAuthConfig)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/resolveAwsSdkSigV4Config.js\");\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n/* harmony import */ var _STSClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../STSClient */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/STSClient.js\");\n\n\n\nconst defaultSTSHttpAuthSchemeParametersProvider = async (config, context, input) => {\n    return {\n        operation: (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.getSmithyContext)(context).operation,\n        region: (await (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(config.region)()) ||\n            (() => {\n                throw new Error(\"expected `region` to be configured for `aws.auth#sigv4`\");\n            })(),\n    };\n};\nfunction createAwsAuthSigv4HttpAuthOption(authParameters) {\n    return {\n        schemeId: \"aws.auth#sigv4\",\n        signingProperties: {\n            name: \"sts\",\n            region: authParameters.region,\n        },\n        propertiesExtractor: (config, context) => ({\n            signingProperties: {\n                config,\n                context,\n            },\n        }),\n    };\n}\nfunction createSmithyApiNoAuthHttpAuthOption(authParameters) {\n    return {\n        schemeId: \"smithy.api#noAuth\",\n    };\n}\nconst defaultSTSHttpAuthSchemeProvider = (authParameters) => {\n    const options = [];\n    switch (authParameters.operation) {\n        case \"AssumeRoleWithWebIdentity\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        default: {\n            options.push(createAwsAuthSigv4HttpAuthOption(authParameters));\n        }\n    }\n    return options;\n};\nconst resolveStsAuthConfig = (input) => Object.assign(input, {\n    stsClientCtor: _STSClient__WEBPACK_IMPORTED_MODULE_1__.STSClient,\n});\nconst resolveHttpAuthSchemeConfig = (config) => {\n    const config_0 = resolveStsAuthConfig(config);\n    const config_1 = (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_2__.resolveAwsSdkSigV4Config)(config_0);\n    return Object.assign(config_1, {\n        authSchemePreference: (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(config.authSchemePreference ?? []),\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/auth/httpAuthSchemeProvider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleCommand.js":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleCommand.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command),\n/* harmony export */   AssumeRoleCommand: () => (/* binding */ AssumeRoleCommand)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/EndpointParameters.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/models_0.js\");\n/* harmony import */ var _protocols_Aws_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../protocols/Aws_query */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/protocols/Aws_query.js\");\n\n\n\n\n\n\n\nclass AssumeRoleCommand extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command\n    .classBuilder()\n    .ep(_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__.commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        (0,_smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin)(config, this.serialize, this.deserialize),\n        (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSSecurityTokenServiceV20110615\", \"AssumeRole\", {})\n    .n(\"STSClient\", \"AssumeRoleCommand\")\n    .f(void 0, _models_models_0__WEBPACK_IMPORTED_MODULE_4__.AssumeRoleResponseFilterSensitiveLog)\n    .ser(_protocols_Aws_query__WEBPACK_IMPORTED_MODULE_5__.se_AssumeRoleCommand)\n    .de(_protocols_Aws_query__WEBPACK_IMPORTED_MODULE_5__.de_AssumeRoleCommand)\n    .build() {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleCommand.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleWithWebIdentityCommand.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleWithWebIdentityCommand.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command),\n/* harmony export */   AssumeRoleWithWebIdentityCommand: () => (/* binding */ AssumeRoleWithWebIdentityCommand)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/EndpointParameters.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/models_0.js\");\n/* harmony import */ var _protocols_Aws_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../protocols/Aws_query */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/protocols/Aws_query.js\");\n\n\n\n\n\n\n\nclass AssumeRoleWithWebIdentityCommand extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command\n    .classBuilder()\n    .ep(_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__.commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        (0,_smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin)(config, this.serialize, this.deserialize),\n        (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"AWSSecurityTokenServiceV20110615\", \"AssumeRoleWithWebIdentity\", {})\n    .n(\"STSClient\", \"AssumeRoleWithWebIdentityCommand\")\n    .f(_models_models_0__WEBPACK_IMPORTED_MODULE_4__.AssumeRoleWithWebIdentityRequestFilterSensitiveLog, _models_models_0__WEBPACK_IMPORTED_MODULE_4__.AssumeRoleWithWebIdentityResponseFilterSensitiveLog)\n    .ser(_protocols_Aws_query__WEBPACK_IMPORTED_MODULE_5__.se_AssumeRoleWithWebIdentityCommand)\n    .de(_protocols_Aws_query__WEBPACK_IMPORTED_MODULE_5__.de_AssumeRoleWithWebIdentityCommand)\n    .build() {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleWithWebIdentityCommand.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/index.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/index.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _AssumeRoleCommand__WEBPACK_IMPORTED_MODULE_0__.$Command),\n/* harmony export */   AssumeRoleCommand: () => (/* reexport safe */ _AssumeRoleCommand__WEBPACK_IMPORTED_MODULE_0__.AssumeRoleCommand),\n/* harmony export */   AssumeRoleWithWebIdentityCommand: () => (/* reexport safe */ _AssumeRoleWithWebIdentityCommand__WEBPACK_IMPORTED_MODULE_1__.AssumeRoleWithWebIdentityCommand)\n/* harmony export */ });\n/* harmony import */ var _AssumeRoleCommand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AssumeRoleCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleCommand.js\");\n/* harmony import */ var _AssumeRoleWithWebIdentityCommand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AssumeRoleWithWebIdentityCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleWithWebIdentityCommand.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3N0cy9jb21tYW5kcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFvQztBQUNlIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3N0cy9jb21tYW5kcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9Bc3N1bWVSb2xlQ29tbWFuZFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vQXNzdW1lUm9sZVdpdGhXZWJJZGVudGl0eUNvbW1hbmRcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/defaultRoleAssumers.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/defaultRoleAssumers.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decorateDefaultCredentialProvider: () => (/* binding */ decorateDefaultCredentialProvider),\n/* harmony export */   getDefaultRoleAssumer: () => (/* binding */ getDefaultRoleAssumer),\n/* harmony export */   getDefaultRoleAssumerWithWebIdentity: () => (/* binding */ getDefaultRoleAssumerWithWebIdentity)\n/* harmony export */ });\n/* harmony import */ var _defaultStsRoleAssumers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultStsRoleAssumers */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/defaultStsRoleAssumers.js\");\n/* harmony import */ var _STSClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./STSClient */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/STSClient.js\");\n\n\nconst getCustomizableStsClientCtor = (baseCtor, customizations) => {\n    if (!customizations)\n        return baseCtor;\n    else\n        return class CustomizableSTSClient extends baseCtor {\n            constructor(config) {\n                super(config);\n                for (const customization of customizations) {\n                    this.middlewareStack.use(customization);\n                }\n            }\n        };\n};\nconst getDefaultRoleAssumer = (stsOptions = {}, stsPlugins) => (0,_defaultStsRoleAssumers__WEBPACK_IMPORTED_MODULE_0__.getDefaultRoleAssumer)(stsOptions, getCustomizableStsClientCtor(_STSClient__WEBPACK_IMPORTED_MODULE_1__.STSClient, stsPlugins));\nconst getDefaultRoleAssumerWithWebIdentity = (stsOptions = {}, stsPlugins) => (0,_defaultStsRoleAssumers__WEBPACK_IMPORTED_MODULE_0__.getDefaultRoleAssumerWithWebIdentity)(stsOptions, getCustomizableStsClientCtor(_STSClient__WEBPACK_IMPORTED_MODULE_1__.STSClient, stsPlugins));\nconst decorateDefaultCredentialProvider = (provider) => (input) => provider({\n    roleAssumer: getDefaultRoleAssumer(input),\n    roleAssumerWithWebIdentity: getDefaultRoleAssumerWithWebIdentity(input),\n    ...input,\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/defaultRoleAssumers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/defaultStsRoleAssumers.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/defaultStsRoleAssumers.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decorateDefaultCredentialProvider: () => (/* binding */ decorateDefaultCredentialProvider),\n/* harmony export */   getDefaultRoleAssumer: () => (/* binding */ getDefaultRoleAssumer),\n/* harmony export */   getDefaultRoleAssumerWithWebIdentity: () => (/* binding */ getDefaultRoleAssumerWithWebIdentity)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/core/client */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\");\n/* harmony import */ var _commands_AssumeRoleCommand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./commands/AssumeRoleCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleCommand.js\");\n/* harmony import */ var _commands_AssumeRoleWithWebIdentityCommand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./commands/AssumeRoleWithWebIdentityCommand */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/AssumeRoleWithWebIdentityCommand.js\");\n\n\n\nconst ASSUME_ROLE_DEFAULT_REGION = \"us-east-1\";\nconst getAccountIdFromAssumedRoleUser = (assumedRoleUser) => {\n    if (typeof assumedRoleUser?.Arn === \"string\") {\n        const arnComponents = assumedRoleUser.Arn.split(\":\");\n        if (arnComponents.length > 4 && arnComponents[4] !== \"\") {\n            return arnComponents[4];\n        }\n    }\n    return undefined;\n};\nconst resolveRegion = async (_region, _parentRegion, credentialProviderLogger) => {\n    const region = typeof _region === \"function\" ? await _region() : _region;\n    const parentRegion = typeof _parentRegion === \"function\" ? await _parentRegion() : _parentRegion;\n    credentialProviderLogger?.debug?.(\"@aws-sdk/client-sts::resolveRegion\", \"accepting first of:\", `${region} (provider)`, `${parentRegion} (parent client)`, `${ASSUME_ROLE_DEFAULT_REGION} (STS default)`);\n    return region ?? parentRegion ?? ASSUME_ROLE_DEFAULT_REGION;\n};\nconst getDefaultRoleAssumer = (stsOptions, STSClient) => {\n    let stsClient;\n    let closureSourceCreds;\n    return async (sourceCreds, params) => {\n        closureSourceCreds = sourceCreds;\n        if (!stsClient) {\n            const { logger = stsOptions?.parentClientConfig?.logger, region, requestHandler = stsOptions?.parentClientConfig?.requestHandler, credentialProviderLogger, } = stsOptions;\n            const resolvedRegion = await resolveRegion(region, stsOptions?.parentClientConfig?.region, credentialProviderLogger);\n            const isCompatibleRequestHandler = !isH2(requestHandler);\n            stsClient = new STSClient({\n                profile: stsOptions?.parentClientConfig?.profile,\n                credentialDefaultProvider: () => async () => closureSourceCreds,\n                region: resolvedRegion,\n                requestHandler: isCompatibleRequestHandler ? requestHandler : undefined,\n                logger: logger,\n            });\n        }\n        const { Credentials, AssumedRoleUser } = await stsClient.send(new _commands_AssumeRoleCommand__WEBPACK_IMPORTED_MODULE_0__.AssumeRoleCommand(params));\n        if (!Credentials || !Credentials.AccessKeyId || !Credentials.SecretAccessKey) {\n            throw new Error(`Invalid response from STS.assumeRole call with role ${params.RoleArn}`);\n        }\n        const accountId = getAccountIdFromAssumedRoleUser(AssumedRoleUser);\n        const credentials = {\n            accessKeyId: Credentials.AccessKeyId,\n            secretAccessKey: Credentials.SecretAccessKey,\n            sessionToken: Credentials.SessionToken,\n            expiration: Credentials.Expiration,\n            ...(Credentials.CredentialScope && { credentialScope: Credentials.CredentialScope }),\n            ...(accountId && { accountId }),\n        };\n        (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_2__.setCredentialFeature)(credentials, \"CREDENTIALS_STS_ASSUME_ROLE\", \"i\");\n        return credentials;\n    };\n};\nconst getDefaultRoleAssumerWithWebIdentity = (stsOptions, STSClient) => {\n    let stsClient;\n    return async (params) => {\n        if (!stsClient) {\n            const { logger = stsOptions?.parentClientConfig?.logger, region, requestHandler = stsOptions?.parentClientConfig?.requestHandler, credentialProviderLogger, } = stsOptions;\n            const resolvedRegion = await resolveRegion(region, stsOptions?.parentClientConfig?.region, credentialProviderLogger);\n            const isCompatibleRequestHandler = !isH2(requestHandler);\n            stsClient = new STSClient({\n                profile: stsOptions?.parentClientConfig?.profile,\n                region: resolvedRegion,\n                requestHandler: isCompatibleRequestHandler ? requestHandler : undefined,\n                logger: logger,\n            });\n        }\n        const { Credentials, AssumedRoleUser } = await stsClient.send(new _commands_AssumeRoleWithWebIdentityCommand__WEBPACK_IMPORTED_MODULE_1__.AssumeRoleWithWebIdentityCommand(params));\n        if (!Credentials || !Credentials.AccessKeyId || !Credentials.SecretAccessKey) {\n            throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${params.RoleArn}`);\n        }\n        const accountId = getAccountIdFromAssumedRoleUser(AssumedRoleUser);\n        const credentials = {\n            accessKeyId: Credentials.AccessKeyId,\n            secretAccessKey: Credentials.SecretAccessKey,\n            sessionToken: Credentials.SessionToken,\n            expiration: Credentials.Expiration,\n            ...(Credentials.CredentialScope && { credentialScope: Credentials.CredentialScope }),\n            ...(accountId && { accountId }),\n        };\n        if (accountId) {\n            (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_2__.setCredentialFeature)(credentials, \"RESOLVED_ACCOUNT_ID\", \"T\");\n        }\n        (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_2__.setCredentialFeature)(credentials, \"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID\", \"k\");\n        return credentials;\n    };\n};\nconst decorateDefaultCredentialProvider = (provider) => (input) => provider({\n    roleAssumer: getDefaultRoleAssumer(input, input.stsClientCtor),\n    roleAssumerWithWebIdentity: getDefaultRoleAssumerWithWebIdentity(input, input.stsClientCtor),\n    ...input,\n});\nconst isH2 = (requestHandler) => {\n    return requestHandler?.metadata?.handlerProtocol === \"h2\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/defaultStsRoleAssumers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/EndpointParameters.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/EndpointParameters.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   commonParams: () => (/* binding */ commonParams),\n/* harmony export */   resolveClientEndpointParameters: () => (/* binding */ resolveClientEndpointParameters)\n/* harmony export */ });\nconst resolveClientEndpointParameters = (options) => {\n    return Object.assign(options, {\n        useDualstackEndpoint: options.useDualstackEndpoint ?? false,\n        useFipsEndpoint: options.useFipsEndpoint ?? false,\n        useGlobalEndpoint: options.useGlobalEndpoint ?? false,\n        defaultSigningName: \"sts\",\n    });\n};\nconst commonParams = {\n    UseGlobalEndpoint: { type: \"builtInParams\", name: \"useGlobalEndpoint\" },\n    UseFIPS: { type: \"builtInParams\", name: \"useFipsEndpoint\" },\n    Endpoint: { type: \"builtInParams\", name: \"endpoint\" },\n    Region: { type: \"builtInParams\", name: \"region\" },\n    UseDualStack: { type: \"builtInParams\", name: \"useDualstackEndpoint\" },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3N0cy9lbmRwb2ludC9FbmRwb2ludFBhcmFtZXRlcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDTztBQUNQLHlCQUF5QixrREFBa0Q7QUFDM0UsZUFBZSxnREFBZ0Q7QUFDL0QsZ0JBQWdCLHlDQUF5QztBQUN6RCxjQUFjLHVDQUF1QztBQUNyRCxvQkFBb0IscURBQXFEO0FBQ3pFIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3N0cy9lbmRwb2ludC9FbmRwb2ludFBhcmFtZXRlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHJlc29sdmVDbGllbnRFbmRwb2ludFBhcmFtZXRlcnMgPSAob3B0aW9ucykgPT4ge1xuICAgIHJldHVybiBPYmplY3QuYXNzaWduKG9wdGlvbnMsIHtcbiAgICAgICAgdXNlRHVhbHN0YWNrRW5kcG9pbnQ6IG9wdGlvbnMudXNlRHVhbHN0YWNrRW5kcG9pbnQgPz8gZmFsc2UsXG4gICAgICAgIHVzZUZpcHNFbmRwb2ludDogb3B0aW9ucy51c2VGaXBzRW5kcG9pbnQgPz8gZmFsc2UsXG4gICAgICAgIHVzZUdsb2JhbEVuZHBvaW50OiBvcHRpb25zLnVzZUdsb2JhbEVuZHBvaW50ID8/IGZhbHNlLFxuICAgICAgICBkZWZhdWx0U2lnbmluZ05hbWU6IFwic3RzXCIsXG4gICAgfSk7XG59O1xuZXhwb3J0IGNvbnN0IGNvbW1vblBhcmFtcyA9IHtcbiAgICBVc2VHbG9iYWxFbmRwb2ludDogeyB0eXBlOiBcImJ1aWx0SW5QYXJhbXNcIiwgbmFtZTogXCJ1c2VHbG9iYWxFbmRwb2ludFwiIH0sXG4gICAgVXNlRklQUzogeyB0eXBlOiBcImJ1aWx0SW5QYXJhbXNcIiwgbmFtZTogXCJ1c2VGaXBzRW5kcG9pbnRcIiB9LFxuICAgIEVuZHBvaW50OiB7IHR5cGU6IFwiYnVpbHRJblBhcmFtc1wiLCBuYW1lOiBcImVuZHBvaW50XCIgfSxcbiAgICBSZWdpb246IHsgdHlwZTogXCJidWlsdEluUGFyYW1zXCIsIG5hbWU6IFwicmVnaW9uXCIgfSxcbiAgICBVc2VEdWFsU3RhY2s6IHsgdHlwZTogXCJidWlsdEluUGFyYW1zXCIsIG5hbWU6IFwidXNlRHVhbHN0YWNrRW5kcG9pbnRcIiB9LFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/EndpointParameters.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/endpointResolver.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/endpointResolver.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultEndpointResolver: () => (/* binding */ defaultEndpointResolver)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/index.js\");\n/* harmony import */ var _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/index.js\");\n/* harmony import */ var _ruleset__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ruleset */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/ruleset.js\");\n\n\n\nconst cache = new _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__.EndpointCache({\n    size: 50,\n    params: [\"Endpoint\", \"Region\", \"UseDualStack\", \"UseFIPS\", \"UseGlobalEndpoint\"],\n});\nconst defaultEndpointResolver = (endpointParams, context = {}) => {\n    return cache.get(endpointParams, () => (0,_smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__.resolveEndpoint)(_ruleset__WEBPACK_IMPORTED_MODULE_2__.ruleSet, {\n        endpointParams: endpointParams,\n        logger: context.logger,\n    }));\n};\n_smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__.customEndpointFunctions.aws = _aws_sdk_util_endpoints__WEBPACK_IMPORTED_MODULE_0__.awsEndpointFunctions;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3N0cy9lbmRwb2ludC9lbmRwb2ludFJlc29sdmVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0Q7QUFDa0M7QUFDN0Q7QUFDcEMsa0JBQWtCLGlFQUFhO0FBQy9CO0FBQ0E7QUFDQSxDQUFDO0FBQ00sNkRBQTZEO0FBQ3BFLDJDQUEyQyx1RUFBZSxDQUFDLDZDQUFPO0FBQ2xFO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSwyRUFBdUIsT0FBTyx5RUFBb0IiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytuZXN0ZWQtY2xpZW50c0AzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9uZXN0ZWQtY2xpZW50cy9kaXN0LWVzL3N1Ym1vZHVsZXMvc3RzL2VuZHBvaW50L2VuZHBvaW50UmVzb2x2ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYXdzRW5kcG9pbnRGdW5jdGlvbnMgfSBmcm9tIFwiQGF3cy1zZGsvdXRpbC1lbmRwb2ludHNcIjtcbmltcG9ydCB7IGN1c3RvbUVuZHBvaW50RnVuY3Rpb25zLCBFbmRwb2ludENhY2hlLCByZXNvbHZlRW5kcG9pbnQgfSBmcm9tIFwiQHNtaXRoeS91dGlsLWVuZHBvaW50c1wiO1xuaW1wb3J0IHsgcnVsZVNldCB9IGZyb20gXCIuL3J1bGVzZXRcIjtcbmNvbnN0IGNhY2hlID0gbmV3IEVuZHBvaW50Q2FjaGUoe1xuICAgIHNpemU6IDUwLFxuICAgIHBhcmFtczogW1wiRW5kcG9pbnRcIiwgXCJSZWdpb25cIiwgXCJVc2VEdWFsU3RhY2tcIiwgXCJVc2VGSVBTXCIsIFwiVXNlR2xvYmFsRW5kcG9pbnRcIl0sXG59KTtcbmV4cG9ydCBjb25zdCBkZWZhdWx0RW5kcG9pbnRSZXNvbHZlciA9IChlbmRwb2ludFBhcmFtcywgY29udGV4dCA9IHt9KSA9PiB7XG4gICAgcmV0dXJuIGNhY2hlLmdldChlbmRwb2ludFBhcmFtcywgKCkgPT4gcmVzb2x2ZUVuZHBvaW50KHJ1bGVTZXQsIHtcbiAgICAgICAgZW5kcG9pbnRQYXJhbXM6IGVuZHBvaW50UGFyYW1zLFxuICAgICAgICBsb2dnZXI6IGNvbnRleHQubG9nZ2VyLFxuICAgIH0pKTtcbn07XG5jdXN0b21FbmRwb2ludEZ1bmN0aW9ucy5hd3MgPSBhd3NFbmRwb2ludEZ1bmN0aW9ucztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/endpointResolver.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/ruleset.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/ruleset.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ruleSet: () => (/* binding */ ruleSet)\n/* harmony export */ });\nconst F = \"required\", G = \"type\", H = \"fn\", I = \"argv\", J = \"ref\";\nconst a = false, b = true, c = \"booleanEquals\", d = \"stringEquals\", e = \"sigv4\", f = \"sts\", g = \"us-east-1\", h = \"endpoint\", i = \"https://sts.{Region}.{PartitionResult#dnsSuffix}\", j = \"tree\", k = \"error\", l = \"getAttr\", m = { [F]: false, [G]: \"String\" }, n = { [F]: true, \"default\": false, [G]: \"Boolean\" }, o = { [J]: \"Endpoint\" }, p = { [H]: \"isSet\", [I]: [{ [J]: \"Region\" }] }, q = { [J]: \"Region\" }, r = { [H]: \"aws.partition\", [I]: [q], \"assign\": \"PartitionResult\" }, s = { [J]: \"UseFIPS\" }, t = { [J]: \"UseDualStack\" }, u = { \"url\": \"https://sts.amazonaws.com\", \"properties\": { \"authSchemes\": [{ \"name\": e, \"signingName\": f, \"signingRegion\": g }] }, \"headers\": {} }, v = {}, w = { \"conditions\": [{ [H]: d, [I]: [q, \"aws-global\"] }], [h]: u, [G]: h }, x = { [H]: c, [I]: [s, true] }, y = { [H]: c, [I]: [t, true] }, z = { [H]: l, [I]: [{ [J]: \"PartitionResult\" }, \"supportsFIPS\"] }, A = { [J]: \"PartitionResult\" }, B = { [H]: c, [I]: [true, { [H]: l, [I]: [A, \"supportsDualStack\"] }] }, C = [{ [H]: \"isSet\", [I]: [o] }], D = [x], E = [y];\nconst _data = { version: \"1.0\", parameters: { Region: m, UseDualStack: n, UseFIPS: n, Endpoint: m, UseGlobalEndpoint: n }, rules: [{ conditions: [{ [H]: c, [I]: [{ [J]: \"UseGlobalEndpoint\" }, b] }, { [H]: \"not\", [I]: C }, p, r, { [H]: c, [I]: [s, a] }, { [H]: c, [I]: [t, a] }], rules: [{ conditions: [{ [H]: d, [I]: [q, \"ap-northeast-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"ap-south-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"ap-southeast-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"ap-southeast-2\"] }], endpoint: u, [G]: h }, w, { conditions: [{ [H]: d, [I]: [q, \"ca-central-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"eu-central-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"eu-north-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"eu-west-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"eu-west-2\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"eu-west-3\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"sa-east-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, g] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"us-east-2\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"us-west-1\"] }], endpoint: u, [G]: h }, { conditions: [{ [H]: d, [I]: [q, \"us-west-2\"] }], endpoint: u, [G]: h }, { endpoint: { url: i, properties: { authSchemes: [{ name: e, signingName: f, signingRegion: \"{Region}\" }] }, headers: v }, [G]: h }], [G]: j }, { conditions: C, rules: [{ conditions: D, error: \"Invalid Configuration: FIPS and custom endpoint are not supported\", [G]: k }, { conditions: E, error: \"Invalid Configuration: Dualstack and custom endpoint are not supported\", [G]: k }, { endpoint: { url: o, properties: v, headers: v }, [G]: h }], [G]: j }, { conditions: [p], rules: [{ conditions: [r], rules: [{ conditions: [x, y], rules: [{ conditions: [{ [H]: c, [I]: [b, z] }, B], rules: [{ endpoint: { url: \"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: v, headers: v }, [G]: h }], [G]: j }, { error: \"FIPS and DualStack are enabled, but this partition does not support one or both\", [G]: k }], [G]: j }, { conditions: D, rules: [{ conditions: [{ [H]: c, [I]: [z, b] }], rules: [{ conditions: [{ [H]: d, [I]: [{ [H]: l, [I]: [A, \"name\"] }, \"aws-us-gov\"] }], endpoint: { url: \"https://sts.{Region}.amazonaws.com\", properties: v, headers: v }, [G]: h }, { endpoint: { url: \"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}\", properties: v, headers: v }, [G]: h }], [G]: j }, { error: \"FIPS is enabled but this partition does not support FIPS\", [G]: k }], [G]: j }, { conditions: E, rules: [{ conditions: [B], rules: [{ endpoint: { url: \"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: v, headers: v }, [G]: h }], [G]: j }, { error: \"DualStack is enabled but this partition does not support DualStack\", [G]: k }], [G]: j }, w, { endpoint: { url: i, properties: v, headers: v }, [G]: h }], [G]: j }], [G]: j }, { error: \"Invalid Configuration: Missing Region\", [G]: k }] };\nconst ruleSet = _data;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/ruleset.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/index.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/index.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.$Command),\n/* harmony export */   AssumeRoleCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.AssumeRoleCommand),\n/* harmony export */   AssumeRoleResponseFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.AssumeRoleResponseFilterSensitiveLog),\n/* harmony export */   AssumeRoleWithWebIdentityCommand: () => (/* reexport safe */ _commands__WEBPACK_IMPORTED_MODULE_2__.AssumeRoleWithWebIdentityCommand),\n/* harmony export */   AssumeRoleWithWebIdentityRequestFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.AssumeRoleWithWebIdentityRequestFilterSensitiveLog),\n/* harmony export */   AssumeRoleWithWebIdentityResponseFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.AssumeRoleWithWebIdentityResponseFilterSensitiveLog),\n/* harmony export */   CredentialsFilterSensitiveLog: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.CredentialsFilterSensitiveLog),\n/* harmony export */   ExpiredTokenException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.ExpiredTokenException),\n/* harmony export */   IDPCommunicationErrorException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.IDPCommunicationErrorException),\n/* harmony export */   IDPRejectedClaimException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.IDPRejectedClaimException),\n/* harmony export */   InvalidIdentityTokenException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.InvalidIdentityTokenException),\n/* harmony export */   MalformedPolicyDocumentException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.MalformedPolicyDocumentException),\n/* harmony export */   PackedPolicyTooLargeException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.PackedPolicyTooLargeException),\n/* harmony export */   RegionDisabledException: () => (/* reexport safe */ _models__WEBPACK_IMPORTED_MODULE_3__.RegionDisabledException),\n/* harmony export */   STS: () => (/* reexport safe */ _STS__WEBPACK_IMPORTED_MODULE_1__.STS),\n/* harmony export */   STSClient: () => (/* reexport safe */ _STSClient__WEBPACK_IMPORTED_MODULE_0__.STSClient),\n/* harmony export */   STSServiceException: () => (/* reexport safe */ _models_STSServiceException__WEBPACK_IMPORTED_MODULE_5__.STSServiceException),\n/* harmony export */   __Client: () => (/* reexport safe */ _STSClient__WEBPACK_IMPORTED_MODULE_0__.__Client),\n/* harmony export */   decorateDefaultCredentialProvider: () => (/* reexport safe */ _defaultRoleAssumers__WEBPACK_IMPORTED_MODULE_4__.decorateDefaultCredentialProvider),\n/* harmony export */   getDefaultRoleAssumer: () => (/* reexport safe */ _defaultRoleAssumers__WEBPACK_IMPORTED_MODULE_4__.getDefaultRoleAssumer),\n/* harmony export */   getDefaultRoleAssumerWithWebIdentity: () => (/* reexport safe */ _defaultRoleAssumers__WEBPACK_IMPORTED_MODULE_4__.getDefaultRoleAssumerWithWebIdentity)\n/* harmony export */ });\n/* harmony import */ var _STSClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./STSClient */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/STSClient.js\");\n/* harmony import */ var _STS__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./STS */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/STS.js\");\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./commands */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/commands/index.js\");\n/* harmony import */ var _models__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./models */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/index.js\");\n/* harmony import */ var _defaultRoleAssumers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./defaultRoleAssumers */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/defaultRoleAssumers.js\");\n/* harmony import */ var _models_STSServiceException__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./models/STSServiceException */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/STSServiceException.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3N0cy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNEI7QUFDTjtBQUNLO0FBQ0Y7QUFDYTtBQUM2QiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK25lc3RlZC1jbGllbnRzQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL25lc3RlZC1jbGllbnRzL2Rpc3QtZXMvc3VibW9kdWxlcy9zdHMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vU1RTQ2xpZW50XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9TVFNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2NvbW1hbmRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9tb2RlbHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2RlZmF1bHRSb2xlQXNzdW1lcnNcIjtcbmV4cG9ydCB7IFNUU1NlcnZpY2VFeGNlcHRpb24gfSBmcm9tIFwiLi9tb2RlbHMvU1RTU2VydmljZUV4Y2VwdGlvblwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/STSServiceException.js":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/STSServiceException.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STSServiceException: () => (/* binding */ STSServiceException),\n/* harmony export */   __ServiceException: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.ServiceException)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n\n\nclass STSServiceException extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.ServiceException {\n    constructor(options) {\n        super(options);\n        Object.setPrototypeOf(this, STSServiceException.prototype);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3N0cy9tb2RlbHMvU1RTU2VydmljZUV4Y2VwdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0Y7QUFDbEQ7QUFDdkIsa0NBQWtDLG1FQUFrQjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3N0cy9tb2RlbHMvU1RTU2VydmljZUV4Y2VwdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTZXJ2aWNlRXhjZXB0aW9uIGFzIF9fU2VydmljZUV4Y2VwdGlvbiwgfSBmcm9tIFwiQHNtaXRoeS9zbWl0aHktY2xpZW50XCI7XG5leHBvcnQgeyBfX1NlcnZpY2VFeGNlcHRpb24gfTtcbmV4cG9ydCBjbGFzcyBTVFNTZXJ2aWNlRXhjZXB0aW9uIGV4dGVuZHMgX19TZXJ2aWNlRXhjZXB0aW9uIHtcbiAgICBjb25zdHJ1Y3RvcihvcHRpb25zKSB7XG4gICAgICAgIHN1cGVyKG9wdGlvbnMpO1xuICAgICAgICBPYmplY3Quc2V0UHJvdG90eXBlT2YodGhpcywgU1RTU2VydmljZUV4Y2VwdGlvbi5wcm90b3R5cGUpO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/STSServiceException.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/index.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/index.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssumeRoleResponseFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.AssumeRoleResponseFilterSensitiveLog),\n/* harmony export */   AssumeRoleWithWebIdentityRequestFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.AssumeRoleWithWebIdentityRequestFilterSensitiveLog),\n/* harmony export */   AssumeRoleWithWebIdentityResponseFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.AssumeRoleWithWebIdentityResponseFilterSensitiveLog),\n/* harmony export */   CredentialsFilterSensitiveLog: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.CredentialsFilterSensitiveLog),\n/* harmony export */   ExpiredTokenException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.ExpiredTokenException),\n/* harmony export */   IDPCommunicationErrorException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.IDPCommunicationErrorException),\n/* harmony export */   IDPRejectedClaimException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.IDPRejectedClaimException),\n/* harmony export */   InvalidIdentityTokenException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.InvalidIdentityTokenException),\n/* harmony export */   MalformedPolicyDocumentException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.MalformedPolicyDocumentException),\n/* harmony export */   PackedPolicyTooLargeException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.PackedPolicyTooLargeException),\n/* harmony export */   RegionDisabledException: () => (/* reexport safe */ _models_0__WEBPACK_IMPORTED_MODULE_0__.RegionDisabledException)\n/* harmony export */ });\n/* harmony import */ var _models_0__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/models_0.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3N0cy9tb2RlbHMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQTJCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbmVzdGVkLWNsaWVudHNAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbmVzdGVkLWNsaWVudHMvZGlzdC1lcy9zdWJtb2R1bGVzL3N0cy9tb2RlbHMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vbW9kZWxzXzBcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/models_0.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/models_0.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AssumeRoleResponseFilterSensitiveLog: () => (/* binding */ AssumeRoleResponseFilterSensitiveLog),\n/* harmony export */   AssumeRoleWithWebIdentityRequestFilterSensitiveLog: () => (/* binding */ AssumeRoleWithWebIdentityRequestFilterSensitiveLog),\n/* harmony export */   AssumeRoleWithWebIdentityResponseFilterSensitiveLog: () => (/* binding */ AssumeRoleWithWebIdentityResponseFilterSensitiveLog),\n/* harmony export */   CredentialsFilterSensitiveLog: () => (/* binding */ CredentialsFilterSensitiveLog),\n/* harmony export */   ExpiredTokenException: () => (/* binding */ ExpiredTokenException),\n/* harmony export */   IDPCommunicationErrorException: () => (/* binding */ IDPCommunicationErrorException),\n/* harmony export */   IDPRejectedClaimException: () => (/* binding */ IDPRejectedClaimException),\n/* harmony export */   InvalidIdentityTokenException: () => (/* binding */ InvalidIdentityTokenException),\n/* harmony export */   MalformedPolicyDocumentException: () => (/* binding */ MalformedPolicyDocumentException),\n/* harmony export */   PackedPolicyTooLargeException: () => (/* binding */ PackedPolicyTooLargeException),\n/* harmony export */   RegionDisabledException: () => (/* binding */ RegionDisabledException)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _STSServiceException__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./STSServiceException */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/STSServiceException.js\");\n\n\nconst CredentialsFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.SecretAccessKey && { SecretAccessKey: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst AssumeRoleResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Credentials && { Credentials: CredentialsFilterSensitiveLog(obj.Credentials) }),\n});\nclass ExpiredTokenException extends _STSServiceException__WEBPACK_IMPORTED_MODULE_1__.STSServiceException {\n    name = \"ExpiredTokenException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"ExpiredTokenException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ExpiredTokenException.prototype);\n    }\n}\nclass MalformedPolicyDocumentException extends _STSServiceException__WEBPACK_IMPORTED_MODULE_1__.STSServiceException {\n    name = \"MalformedPolicyDocumentException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"MalformedPolicyDocumentException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, MalformedPolicyDocumentException.prototype);\n    }\n}\nclass PackedPolicyTooLargeException extends _STSServiceException__WEBPACK_IMPORTED_MODULE_1__.STSServiceException {\n    name = \"PackedPolicyTooLargeException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"PackedPolicyTooLargeException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, PackedPolicyTooLargeException.prototype);\n    }\n}\nclass RegionDisabledException extends _STSServiceException__WEBPACK_IMPORTED_MODULE_1__.STSServiceException {\n    name = \"RegionDisabledException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"RegionDisabledException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, RegionDisabledException.prototype);\n    }\n}\nclass IDPRejectedClaimException extends _STSServiceException__WEBPACK_IMPORTED_MODULE_1__.STSServiceException {\n    name = \"IDPRejectedClaimException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"IDPRejectedClaimException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, IDPRejectedClaimException.prototype);\n    }\n}\nclass InvalidIdentityTokenException extends _STSServiceException__WEBPACK_IMPORTED_MODULE_1__.STSServiceException {\n    name = \"InvalidIdentityTokenException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"InvalidIdentityTokenException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, InvalidIdentityTokenException.prototype);\n    }\n}\nconst AssumeRoleWithWebIdentityRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.WebIdentityToken && { WebIdentityToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst AssumeRoleWithWebIdentityResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.Credentials && { Credentials: CredentialsFilterSensitiveLog(obj.Credentials) }),\n});\nclass IDPCommunicationErrorException extends _STSServiceException__WEBPACK_IMPORTED_MODULE_1__.STSServiceException {\n    name = \"IDPCommunicationErrorException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"IDPCommunicationErrorException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, IDPCommunicationErrorException.prototype);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/models_0.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/protocols/Aws_query.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/protocols/Aws_query.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   de_AssumeRoleCommand: () => (/* binding */ de_AssumeRoleCommand),\n/* harmony export */   de_AssumeRoleWithWebIdentityCommand: () => (/* binding */ de_AssumeRoleWithWebIdentityCommand),\n/* harmony export */   se_AssumeRoleCommand: () => (/* binding */ se_AssumeRoleCommand),\n/* harmony export */   se_AssumeRoleWithWebIdentityCommand: () => (/* binding */ se_AssumeRoleWithWebIdentityCommand)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/xml/parseXmlBody.js\");\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/models_0.js\");\n/* harmony import */ var _models_STSServiceException__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../models/STSServiceException */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/models/STSServiceException.js\");\n\n\n\n\n\nconst se_AssumeRoleCommand = async (input, context) => {\n    const headers = SHARED_HEADERS;\n    let body;\n    body = buildFormUrlencodedString({\n        ...se_AssumeRoleRequest(input, context),\n        [_A]: _AR,\n        [_V]: _,\n    });\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nconst se_AssumeRoleWithWebIdentityCommand = async (input, context) => {\n    const headers = SHARED_HEADERS;\n    let body;\n    body = buildFormUrlencodedString({\n        ...se_AssumeRoleWithWebIdentityRequest(input, context),\n        [_A]: _ARWWI,\n        [_V]: _,\n    });\n    return buildHttpRpcRequest(context, headers, \"/\", undefined, body);\n};\nconst de_AssumeRoleCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.parseXmlBody)(output.body, context);\n    let contents = {};\n    contents = de_AssumeRoleResponse(data.AssumeRoleResult, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nconst de_AssumeRoleWithWebIdentityCommand = async (output, context) => {\n    if (output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const data = await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.parseXmlBody)(output.body, context);\n    let contents = {};\n    contents = de_AssumeRoleWithWebIdentityResponse(data.AssumeRoleWithWebIdentityResult, context);\n    const response = {\n        $metadata: deserializeMetadata(output),\n        ...contents,\n    };\n    return response;\n};\nconst de_CommandError = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_4__.parseXmlErrorBody)(output.body, context),\n    };\n    const errorCode = loadQueryErrorCode(output, parsedOutput.body);\n    switch (errorCode) {\n        case \"ExpiredTokenException\":\n        case \"com.amazonaws.sts#ExpiredTokenException\":\n            throw await de_ExpiredTokenExceptionRes(parsedOutput, context);\n        case \"MalformedPolicyDocument\":\n        case \"com.amazonaws.sts#MalformedPolicyDocumentException\":\n            throw await de_MalformedPolicyDocumentExceptionRes(parsedOutput, context);\n        case \"PackedPolicyTooLarge\":\n        case \"com.amazonaws.sts#PackedPolicyTooLargeException\":\n            throw await de_PackedPolicyTooLargeExceptionRes(parsedOutput, context);\n        case \"RegionDisabledException\":\n        case \"com.amazonaws.sts#RegionDisabledException\":\n            throw await de_RegionDisabledExceptionRes(parsedOutput, context);\n        case \"IDPCommunicationError\":\n        case \"com.amazonaws.sts#IDPCommunicationErrorException\":\n            throw await de_IDPCommunicationErrorExceptionRes(parsedOutput, context);\n        case \"IDPRejectedClaim\":\n        case \"com.amazonaws.sts#IDPRejectedClaimException\":\n            throw await de_IDPRejectedClaimExceptionRes(parsedOutput, context);\n        case \"InvalidIdentityToken\":\n        case \"com.amazonaws.sts#InvalidIdentityTokenException\":\n            throw await de_InvalidIdentityTokenExceptionRes(parsedOutput, context);\n        default:\n            const parsedBody = parsedOutput.body;\n            return throwDefaultError({\n                output,\n                parsedBody: parsedBody.Error,\n                errorCode,\n            });\n    }\n};\nconst de_ExpiredTokenExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_ExpiredTokenException(body.Error, context);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.ExpiredTokenException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, body);\n};\nconst de_IDPCommunicationErrorExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_IDPCommunicationErrorException(body.Error, context);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.IDPCommunicationErrorException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, body);\n};\nconst de_IDPRejectedClaimExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_IDPRejectedClaimException(body.Error, context);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.IDPRejectedClaimException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, body);\n};\nconst de_InvalidIdentityTokenExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_InvalidIdentityTokenException(body.Error, context);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.InvalidIdentityTokenException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, body);\n};\nconst de_MalformedPolicyDocumentExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_MalformedPolicyDocumentException(body.Error, context);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.MalformedPolicyDocumentException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, body);\n};\nconst de_PackedPolicyTooLargeExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_PackedPolicyTooLargeException(body.Error, context);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.PackedPolicyTooLargeException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, body);\n};\nconst de_RegionDisabledExceptionRes = async (parsedOutput, context) => {\n    const body = parsedOutput.body;\n    const deserialized = de_RegionDisabledException(body.Error, context);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_2__.RegionDisabledException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...deserialized,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, body);\n};\nconst se_AssumeRoleRequest = (input, context) => {\n    const entries = {};\n    if (input[_RA] != null) {\n        entries[_RA] = input[_RA];\n    }\n    if (input[_RSN] != null) {\n        entries[_RSN] = input[_RSN];\n    }\n    if (input[_PA] != null) {\n        const memberEntries = se_policyDescriptorListType(input[_PA], context);\n        if (input[_PA]?.length === 0) {\n            entries.PolicyArns = [];\n        }\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            const loc = `PolicyArns.${key}`;\n            entries[loc] = value;\n        });\n    }\n    if (input[_P] != null) {\n        entries[_P] = input[_P];\n    }\n    if (input[_DS] != null) {\n        entries[_DS] = input[_DS];\n    }\n    if (input[_T] != null) {\n        const memberEntries = se_tagListType(input[_T], context);\n        if (input[_T]?.length === 0) {\n            entries.Tags = [];\n        }\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            const loc = `Tags.${key}`;\n            entries[loc] = value;\n        });\n    }\n    if (input[_TTK] != null) {\n        const memberEntries = se_tagKeyListType(input[_TTK], context);\n        if (input[_TTK]?.length === 0) {\n            entries.TransitiveTagKeys = [];\n        }\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            const loc = `TransitiveTagKeys.${key}`;\n            entries[loc] = value;\n        });\n    }\n    if (input[_EI] != null) {\n        entries[_EI] = input[_EI];\n    }\n    if (input[_SN] != null) {\n        entries[_SN] = input[_SN];\n    }\n    if (input[_TC] != null) {\n        entries[_TC] = input[_TC];\n    }\n    if (input[_SI] != null) {\n        entries[_SI] = input[_SI];\n    }\n    if (input[_PC] != null) {\n        const memberEntries = se_ProvidedContextsListType(input[_PC], context);\n        if (input[_PC]?.length === 0) {\n            entries.ProvidedContexts = [];\n        }\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            const loc = `ProvidedContexts.${key}`;\n            entries[loc] = value;\n        });\n    }\n    return entries;\n};\nconst se_AssumeRoleWithWebIdentityRequest = (input, context) => {\n    const entries = {};\n    if (input[_RA] != null) {\n        entries[_RA] = input[_RA];\n    }\n    if (input[_RSN] != null) {\n        entries[_RSN] = input[_RSN];\n    }\n    if (input[_WIT] != null) {\n        entries[_WIT] = input[_WIT];\n    }\n    if (input[_PI] != null) {\n        entries[_PI] = input[_PI];\n    }\n    if (input[_PA] != null) {\n        const memberEntries = se_policyDescriptorListType(input[_PA], context);\n        if (input[_PA]?.length === 0) {\n            entries.PolicyArns = [];\n        }\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            const loc = `PolicyArns.${key}`;\n            entries[loc] = value;\n        });\n    }\n    if (input[_P] != null) {\n        entries[_P] = input[_P];\n    }\n    if (input[_DS] != null) {\n        entries[_DS] = input[_DS];\n    }\n    return entries;\n};\nconst se_policyDescriptorListType = (input, context) => {\n    const entries = {};\n    let counter = 1;\n    for (const entry of input) {\n        if (entry === null) {\n            continue;\n        }\n        const memberEntries = se_PolicyDescriptorType(entry, context);\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            entries[`member.${counter}.${key}`] = value;\n        });\n        counter++;\n    }\n    return entries;\n};\nconst se_PolicyDescriptorType = (input, context) => {\n    const entries = {};\n    if (input[_a] != null) {\n        entries[_a] = input[_a];\n    }\n    return entries;\n};\nconst se_ProvidedContext = (input, context) => {\n    const entries = {};\n    if (input[_PAr] != null) {\n        entries[_PAr] = input[_PAr];\n    }\n    if (input[_CA] != null) {\n        entries[_CA] = input[_CA];\n    }\n    return entries;\n};\nconst se_ProvidedContextsListType = (input, context) => {\n    const entries = {};\n    let counter = 1;\n    for (const entry of input) {\n        if (entry === null) {\n            continue;\n        }\n        const memberEntries = se_ProvidedContext(entry, context);\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            entries[`member.${counter}.${key}`] = value;\n        });\n        counter++;\n    }\n    return entries;\n};\nconst se_Tag = (input, context) => {\n    const entries = {};\n    if (input[_K] != null) {\n        entries[_K] = input[_K];\n    }\n    if (input[_Va] != null) {\n        entries[_Va] = input[_Va];\n    }\n    return entries;\n};\nconst se_tagKeyListType = (input, context) => {\n    const entries = {};\n    let counter = 1;\n    for (const entry of input) {\n        if (entry === null) {\n            continue;\n        }\n        entries[`member.${counter}`] = entry;\n        counter++;\n    }\n    return entries;\n};\nconst se_tagListType = (input, context) => {\n    const entries = {};\n    let counter = 1;\n    for (const entry of input) {\n        if (entry === null) {\n            continue;\n        }\n        const memberEntries = se_Tag(entry, context);\n        Object.entries(memberEntries).forEach(([key, value]) => {\n            entries[`member.${counter}.${key}`] = value;\n        });\n        counter++;\n    }\n    return entries;\n};\nconst de_AssumedRoleUser = (output, context) => {\n    const contents = {};\n    if (output[_ARI] != null) {\n        contents[_ARI] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_ARI]);\n    }\n    if (output[_Ar] != null) {\n        contents[_Ar] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_Ar]);\n    }\n    return contents;\n};\nconst de_AssumeRoleResponse = (output, context) => {\n    const contents = {};\n    if (output[_C] != null) {\n        contents[_C] = de_Credentials(output[_C], context);\n    }\n    if (output[_ARU] != null) {\n        contents[_ARU] = de_AssumedRoleUser(output[_ARU], context);\n    }\n    if (output[_PPS] != null) {\n        contents[_PPS] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.strictParseInt32)(output[_PPS]);\n    }\n    if (output[_SI] != null) {\n        contents[_SI] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_SI]);\n    }\n    return contents;\n};\nconst de_AssumeRoleWithWebIdentityResponse = (output, context) => {\n    const contents = {};\n    if (output[_C] != null) {\n        contents[_C] = de_Credentials(output[_C], context);\n    }\n    if (output[_SFWIT] != null) {\n        contents[_SFWIT] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_SFWIT]);\n    }\n    if (output[_ARU] != null) {\n        contents[_ARU] = de_AssumedRoleUser(output[_ARU], context);\n    }\n    if (output[_PPS] != null) {\n        contents[_PPS] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.strictParseInt32)(output[_PPS]);\n    }\n    if (output[_Pr] != null) {\n        contents[_Pr] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_Pr]);\n    }\n    if (output[_Au] != null) {\n        contents[_Au] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_Au]);\n    }\n    if (output[_SI] != null) {\n        contents[_SI] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_SI]);\n    }\n    return contents;\n};\nconst de_Credentials = (output, context) => {\n    const contents = {};\n    if (output[_AKI] != null) {\n        contents[_AKI] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_AKI]);\n    }\n    if (output[_SAK] != null) {\n        contents[_SAK] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_SAK]);\n    }\n    if (output[_ST] != null) {\n        contents[_ST] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_ST]);\n    }\n    if (output[_E] != null) {\n        contents[_E] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.parseRfc3339DateTimeWithOffset)(output[_E]));\n    }\n    return contents;\n};\nconst de_ExpiredTokenException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_m]);\n    }\n    return contents;\n};\nconst de_IDPCommunicationErrorException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_m]);\n    }\n    return contents;\n};\nconst de_IDPRejectedClaimException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_m]);\n    }\n    return contents;\n};\nconst de_InvalidIdentityTokenException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_m]);\n    }\n    return contents;\n};\nconst de_MalformedPolicyDocumentException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_m]);\n    }\n    return contents;\n};\nconst de_PackedPolicyTooLargeException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_m]);\n    }\n    return contents;\n};\nconst de_RegionDisabledException = (output, context) => {\n    const contents = {};\n    if (output[_m] != null) {\n        contents[_m] = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString)(output[_m]);\n    }\n    return contents;\n};\nconst deserializeMetadata = (output) => ({\n    httpStatusCode: output.statusCode,\n    requestId: output.headers[\"x-amzn-requestid\"] ?? output.headers[\"x-amzn-request-id\"] ?? output.headers[\"x-amz-request-id\"],\n    extendedRequestId: output.headers[\"x-amz-id-2\"],\n    cfId: output.headers[\"x-amz-cf-id\"],\n});\nconst collectBodyString = (streamBody, context) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.collectBody)(streamBody, context).then((body) => context.utf8Encoder(body));\nconst throwDefaultError = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.withBaseException)(_models_STSServiceException__WEBPACK_IMPORTED_MODULE_3__.STSServiceException);\nconst buildHttpRpcRequest = async (context, headers, path, resolvedHostname, body) => {\n    const { hostname, protocol = \"https\", port, path: basePath } = await context.endpoint();\n    const contents = {\n        protocol,\n        hostname,\n        port,\n        method: \"POST\",\n        path: basePath.endsWith(\"/\") ? basePath.slice(0, -1) + path : basePath + path,\n        headers,\n    };\n    if (resolvedHostname !== undefined) {\n        contents.hostname = resolvedHostname;\n    }\n    if (body !== undefined) {\n        contents.body = body;\n    }\n    return new _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest(contents);\n};\nconst SHARED_HEADERS = {\n    \"content-type\": \"application/x-www-form-urlencoded\",\n};\nconst _ = \"2011-06-15\";\nconst _A = \"Action\";\nconst _AKI = \"AccessKeyId\";\nconst _AR = \"AssumeRole\";\nconst _ARI = \"AssumedRoleId\";\nconst _ARU = \"AssumedRoleUser\";\nconst _ARWWI = \"AssumeRoleWithWebIdentity\";\nconst _Ar = \"Arn\";\nconst _Au = \"Audience\";\nconst _C = \"Credentials\";\nconst _CA = \"ContextAssertion\";\nconst _DS = \"DurationSeconds\";\nconst _E = \"Expiration\";\nconst _EI = \"ExternalId\";\nconst _K = \"Key\";\nconst _P = \"Policy\";\nconst _PA = \"PolicyArns\";\nconst _PAr = \"ProviderArn\";\nconst _PC = \"ProvidedContexts\";\nconst _PI = \"ProviderId\";\nconst _PPS = \"PackedPolicySize\";\nconst _Pr = \"Provider\";\nconst _RA = \"RoleArn\";\nconst _RSN = \"RoleSessionName\";\nconst _SAK = \"SecretAccessKey\";\nconst _SFWIT = \"SubjectFromWebIdentityToken\";\nconst _SI = \"SourceIdentity\";\nconst _SN = \"SerialNumber\";\nconst _ST = \"SessionToken\";\nconst _T = \"Tags\";\nconst _TC = \"TokenCode\";\nconst _TTK = \"TransitiveTagKeys\";\nconst _V = \"Version\";\nconst _Va = \"Value\";\nconst _WIT = \"WebIdentityToken\";\nconst _a = \"arn\";\nconst _m = \"message\";\nconst buildFormUrlencodedString = (formEntries) => Object.entries(formEntries)\n    .map(([key, value]) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.extendedEncodeURIComponent)(key) + \"=\" + (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.extendedEncodeURIComponent)(value))\n    .join(\"&\");\nconst loadQueryErrorCode = (output, data) => {\n    if (data.Error?.Code !== undefined) {\n        return data.Error.Code;\n    }\n    if (output.statusCode == 404) {\n        return \"NotFound\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/protocols/Aws_query.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeConfig.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeConfig.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRuntimeConfig: () => (/* binding */ getRuntimeConfig)\n/* harmony export */ });\n/* harmony import */ var _package_json__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../package.json */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/package.json\");\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/emitWarningIfUnsupportedVersion.js\");\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/NODE_AUTH_SCHEME_PREFERENCE_OPTIONS.js\");\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/AwsSdkSigV4Signer.js\");\n/* harmony import */ var _aws_sdk_util_user_agent_node__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/util-user-agent-node */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/index.js\");\n/* harmony import */ var _smithy_config_resolver__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/config-resolver */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_hash_node__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @smithy/hash-node */ \"(rsc)/./node_modules/.pnpm/@smithy+hash-node@4.0.4/node_modules/@smithy/hash-node/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @smithy/middleware-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/index.js\");\n/* harmony import */ var _smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @smithy/node-config-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+node-config-provider@4.1.3/node_modules/@smithy/node-config-provider/dist-es/index.js\");\n/* harmony import */ var _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @smithy/node-http-handler */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/index.js\");\n/* harmony import */ var _smithy_util_body_length_node__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @smithy/util-body-length-node */ \"(rsc)/./node_modules/.pnpm/@smithy+util-body-length-node@4.0.0/node_modules/@smithy/util-body-length-node/dist-es/index.js\");\n/* harmony import */ var _smithy_util_retry__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @smithy/util-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js\");\n/* harmony import */ var _runtimeConfig_shared__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./runtimeConfig.shared */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeConfig.shared.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _smithy_util_defaults_mode_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @smithy/util-defaults-mode-node */ \"(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst getRuntimeConfig = (config) => {\n    (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_11__.emitWarningIfUnsupportedVersion)(process.version);\n    const defaultsMode = (0,_smithy_util_defaults_mode_node__WEBPACK_IMPORTED_MODULE_12__.resolveDefaultsModeConfig)(config);\n    const defaultConfigProvider = () => defaultsMode().then(_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_11__.loadConfigsForDefaultMode);\n    const clientSharedValues = (0,_runtimeConfig_shared__WEBPACK_IMPORTED_MODULE_10__.getRuntimeConfig)(config);\n    (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_13__.emitWarningIfUnsupportedVersion)(process.version);\n    const loaderConfig = {\n        profile: config?.profile,\n        logger: clientSharedValues.logger,\n    };\n    return {\n        ...clientSharedValues,\n        ...config,\n        runtime: \"node\",\n        defaultsMode,\n        authSchemePreference: config?.authSchemePreference ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_6__.loadConfig)(_aws_sdk_core__WEBPACK_IMPORTED_MODULE_14__.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS, loaderConfig),\n        bodyLengthChecker: config?.bodyLengthChecker ?? _smithy_util_body_length_node__WEBPACK_IMPORTED_MODULE_8__.calculateBodyLength,\n        defaultUserAgentProvider: config?.defaultUserAgentProvider ??\n            (0,_aws_sdk_util_user_agent_node__WEBPACK_IMPORTED_MODULE_1__.createDefaultUserAgentProvider)({ serviceId: clientSharedValues.serviceId, clientVersion: _package_json__WEBPACK_IMPORTED_MODULE_0__.version }),\n        httpAuthSchemes: config?.httpAuthSchemes ?? [\n            {\n                schemeId: \"aws.auth#sigv4\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"aws.auth#sigv4\") ||\n                    (async (idProps) => await config.credentialDefaultProvider(idProps?.__config || {})()),\n                signer: new _aws_sdk_core__WEBPACK_IMPORTED_MODULE_15__.AwsSdkSigV4Signer(),\n            },\n            {\n                schemeId: \"smithy.api#noAuth\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"smithy.api#noAuth\") || (async () => ({})),\n                signer: new _smithy_core__WEBPACK_IMPORTED_MODULE_3__.NoAuthSigner(),\n            },\n        ],\n        maxAttempts: config?.maxAttempts ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_6__.loadConfig)(_smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_5__.NODE_MAX_ATTEMPT_CONFIG_OPTIONS, config),\n        region: config?.region ??\n            (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_6__.loadConfig)(_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_2__.NODE_REGION_CONFIG_OPTIONS, { ..._smithy_config_resolver__WEBPACK_IMPORTED_MODULE_2__.NODE_REGION_CONFIG_FILE_OPTIONS, ...loaderConfig }),\n        requestHandler: _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_7__.NodeHttpHandler.create(config?.requestHandler ?? defaultConfigProvider),\n        retryMode: config?.retryMode ??\n            (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_6__.loadConfig)({\n                ..._smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_5__.NODE_RETRY_MODE_CONFIG_OPTIONS,\n                default: async () => (await defaultConfigProvider()).retryMode || _smithy_util_retry__WEBPACK_IMPORTED_MODULE_9__.DEFAULT_RETRY_MODE,\n            }, config),\n        sha256: config?.sha256 ?? _smithy_hash_node__WEBPACK_IMPORTED_MODULE_4__.Hash.bind(null, \"sha256\"),\n        streamCollector: config?.streamCollector ?? _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_7__.streamCollector,\n        useDualstackEndpoint: config?.useDualstackEndpoint ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_6__.loadConfig)(_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_2__.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS, loaderConfig),\n        useFipsEndpoint: config?.useFipsEndpoint ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_6__.loadConfig)(_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_2__.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS, loaderConfig),\n        userAgentAppId: config?.userAgentAppId ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_6__.loadConfig)(_aws_sdk_util_user_agent_node__WEBPACK_IMPORTED_MODULE_1__.NODE_APP_ID_CONFIG_OPTIONS, loaderConfig),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeConfig.shared.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeConfig.shared.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRuntimeConfig: () => (/* binding */ getRuntimeConfig)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/AwsSdkSigV4Signer.js\");\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _smithy_url_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/url-parser */ \"(rsc)/./node_modules/.pnpm/@smithy+url-parser@4.0.4/node_modules/@smithy/url-parser/dist-es/index.js\");\n/* harmony import */ var _smithy_util_base64__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/util-base64 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n/* harmony import */ var _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./auth/httpAuthSchemeProvider */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/auth/httpAuthSchemeProvider.js\");\n/* harmony import */ var _endpoint_endpointResolver__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./endpoint/endpointResolver */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/endpoint/endpointResolver.js\");\n\n\n\n\n\n\n\n\nconst getRuntimeConfig = (config) => {\n    return {\n        apiVersion: \"2011-06-15\",\n        base64Decoder: config?.base64Decoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_3__.fromBase64,\n        base64Encoder: config?.base64Encoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_3__.toBase64,\n        disableHostPrefix: config?.disableHostPrefix ?? false,\n        endpointProvider: config?.endpointProvider ?? _endpoint_endpointResolver__WEBPACK_IMPORTED_MODULE_6__.defaultEndpointResolver,\n        extensions: config?.extensions ?? [],\n        httpAuthSchemeProvider: config?.httpAuthSchemeProvider ?? _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_5__.defaultSTSHttpAuthSchemeProvider,\n        httpAuthSchemes: config?.httpAuthSchemes ?? [\n            {\n                schemeId: \"aws.auth#sigv4\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"aws.auth#sigv4\"),\n                signer: new _aws_sdk_core__WEBPACK_IMPORTED_MODULE_7__.AwsSdkSigV4Signer(),\n            },\n            {\n                schemeId: \"smithy.api#noAuth\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"smithy.api#noAuth\") || (async () => ({})),\n                signer: new _smithy_core__WEBPACK_IMPORTED_MODULE_0__.NoAuthSigner(),\n            },\n        ],\n        logger: config?.logger ?? new _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.NoOpLogger(),\n        serviceId: config?.serviceId ?? \"STS\",\n        urlParser: config?.urlParser ?? _smithy_url_parser__WEBPACK_IMPORTED_MODULE_2__.parseUrl,\n        utf8Decoder: config?.utf8Decoder ?? _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_4__.fromUtf8,\n        utf8Encoder: config?.utf8Encoder ?? _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_4__.toUtf8,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeConfig.shared.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeExtensions.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeExtensions.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveRuntimeExtensions: () => (/* binding */ resolveRuntimeExtensions)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_region_config_resolver__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/region-config-resolver */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _auth_httpAuthExtensionConfiguration__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./auth/httpAuthExtensionConfiguration */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/auth/httpAuthExtensionConfiguration.js\");\n\n\n\n\nconst resolveRuntimeExtensions = (runtimeConfig, extensions) => {\n    const extensionConfiguration = Object.assign((0,_aws_sdk_region_config_resolver__WEBPACK_IMPORTED_MODULE_0__.getAwsRegionExtensionConfiguration)(runtimeConfig), (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.getDefaultExtensionConfiguration)(runtimeConfig), (0,_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__.getHttpHandlerExtensionConfiguration)(runtimeConfig), (0,_auth_httpAuthExtensionConfiguration__WEBPACK_IMPORTED_MODULE_3__.getHttpAuthExtensionConfiguration)(runtimeConfig));\n    extensions.forEach((extension) => extension.configure(extensionConfiguration));\n    return Object.assign(runtimeConfig, (0,_aws_sdk_region_config_resolver__WEBPACK_IMPORTED_MODULE_0__.resolveAwsRegionExtensionConfiguration)(extensionConfiguration), (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.resolveDefaultRuntimeConfig)(extensionConfiguration), (0,_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__.resolveHttpHandlerRuntimeConfig)(extensionConfiguration), (0,_auth_httpAuthExtensionConfiguration__WEBPACK_IMPORTED_MODULE_3__.resolveHttpAuthRuntimeConfig)(extensionConfiguration));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/dist-es/submodules/sts/runtimeExtensions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/package.json":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+nested-clients@3.830.0/node_modules/@aws-sdk/nested-clients/package.json ***!
  \**************************************************************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"@aws-sdk/nested-clients","version":"3.830.0","description":"Nested clients for AWS SDK packages.","main":"./dist-cjs/index.js","module":"./dist-es/index.js","types":"./dist-types/index.d.ts","scripts":{"build":"yarn lint && concurrently \'yarn:build:cjs\' \'yarn:build:es\' \'yarn:build:types\'","build:cjs":"node ../../scripts/compilation/inline nested-clients","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4","clean":"rimraf ./dist-* && rimraf *.tsbuildinfo","lint":"node ../../scripts/validation/submodules-linter.js --pkg nested-clients","test":"yarn g:vitest run","test:watch":"yarn g:vitest watch"},"engines":{"node":">=18.0.0"},"author":{"name":"AWS SDK for JavaScript Team","url":"https://aws.amazon.com/javascript/"},"license":"Apache-2.0","dependencies":{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.826.0","@aws-sdk/middleware-host-header":"3.821.0","@aws-sdk/middleware-logger":"3.821.0","@aws-sdk/middleware-recursion-detection":"3.821.0","@aws-sdk/middleware-user-agent":"3.828.0","@aws-sdk/region-config-resolver":"3.821.0","@aws-sdk/types":"3.821.0","@aws-sdk/util-endpoints":"3.828.0","@aws-sdk/util-user-agent-browser":"3.821.0","@aws-sdk/util-user-agent-node":"3.828.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.5.3","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.11","@smithy/middleware-retry":"^4.1.12","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.3","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.19","@smithy/util-defaults-mode-node":"^4.0.19","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.5","@smithy/util-utf8":"^4.0.0","tslib":"^2.6.2"},"devDependencies":{"concurrently":"7.0.0","downlevel-dts":"0.10.1","rimraf":"3.0.2","typescript":"~5.8.3"},"typesVersions":{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},"files":["./sso-oidc.d.ts","./sso-oidc.js","./sts.d.ts","./sts.js","dist-*/**"],"browser":{"./dist-es/submodules/sso-oidc/runtimeConfig":"./dist-es/submodules/sso-oidc/runtimeConfig.browser","./dist-es/submodules/sts/runtimeConfig":"./dist-es/submodules/sts/runtimeConfig.browser"},"react-native":{},"homepage":"https://github.com/aws/aws-sdk-js-v3/tree/main/packages/nested-clients","repository":{"type":"git","url":"https://github.com/aws/aws-sdk-js-v3.git","directory":"packages/nested-clients"},"exports":{"./sso-oidc":{"types":"./dist-types/submodules/sso-oidc/index.d.ts","module":"./dist-es/submodules/sso-oidc/index.js","node":"./dist-cjs/submodules/sso-oidc/index.js","import":"./dist-es/submodules/sso-oidc/index.js","require":"./dist-cjs/submodules/sso-oidc/index.js"},"./sts":{"types":"./dist-types/submodules/sts/index.d.ts","module":"./dist-es/submodules/sts/index.js","node":"./dist-cjs/submodules/sts/index.js","import":"./dist-es/submodules/sts/index.js","require":"./dist-cjs/submodules/sts/index.js"}}}');

/***/ })

};
;