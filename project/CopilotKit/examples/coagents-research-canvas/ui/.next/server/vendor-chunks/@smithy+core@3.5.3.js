"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+core@3.5.3";
exports.ids = ["vendor-chunks/@smithy+core@3.5.3"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/getSmithyContext.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/getSmithyContext.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSmithyContext: () => (/* binding */ getSmithyContext)\n/* harmony export */ });\n/* harmony import */ var _smithy_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/types */ \"(rsc)/./node_modules/.pnpm/@smithy+types@4.3.1/node_modules/@smithy/types/dist-es/index.js\");\n\nconst getSmithyContext = (context) => context[_smithy_types__WEBPACK_IMPORTED_MODULE_0__.SMITHY_CONTEXT_KEY] || (context[_smithy_types__WEBPACK_IMPORTED_MODULE_0__.SMITHY_CONTEXT_KEY] = {});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9nZXRTbWl0aHlDb250ZXh0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1EO0FBQzVDLDhDQUE4Qyw2REFBa0IsY0FBYyw2REFBa0IsTUFBTSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29yZUAzLjUuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb3JlL2Rpc3QtZXMvZ2V0U21pdGh5Q29udGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTTUlUSFlfQ09OVEVYVF9LRVkgfSBmcm9tIFwiQHNtaXRoeS90eXBlc1wiO1xuZXhwb3J0IGNvbnN0IGdldFNtaXRoeUNvbnRleHQgPSAoY29udGV4dCkgPT4gY29udGV4dFtTTUlUSFlfQ09OVEVYVF9LRVldIHx8IChjb250ZXh0W1NNSVRIWV9DT05URVhUX0tFWV0gPSB7fSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/getSmithyContext.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultIdentityProviderConfig: () => (/* reexport safe */ _util_identity_and_auth__WEBPACK_IMPORTED_MODULE_7__.DefaultIdentityProviderConfig),\n/* harmony export */   EXPIRATION_MS: () => (/* reexport safe */ _util_identity_and_auth__WEBPACK_IMPORTED_MODULE_7__.EXPIRATION_MS),\n/* harmony export */   HttpApiKeyAuthSigner: () => (/* reexport safe */ _util_identity_and_auth__WEBPACK_IMPORTED_MODULE_7__.HttpApiKeyAuthSigner),\n/* harmony export */   HttpBearerAuthSigner: () => (/* reexport safe */ _util_identity_and_auth__WEBPACK_IMPORTED_MODULE_7__.HttpBearerAuthSigner),\n/* harmony export */   NoAuthSigner: () => (/* reexport safe */ _util_identity_and_auth__WEBPACK_IMPORTED_MODULE_7__.NoAuthSigner),\n/* harmony export */   createIsIdentityExpiredFunction: () => (/* reexport safe */ _util_identity_and_auth__WEBPACK_IMPORTED_MODULE_7__.createIsIdentityExpiredFunction),\n/* harmony export */   createPaginator: () => (/* reexport safe */ _pagination_createPaginator__WEBPACK_IMPORTED_MODULE_4__.createPaginator),\n/* harmony export */   doesIdentityRequireRefresh: () => (/* reexport safe */ _util_identity_and_auth__WEBPACK_IMPORTED_MODULE_7__.doesIdentityRequireRefresh),\n/* harmony export */   getHttpAuthSchemeEndpointRuleSetPlugin: () => (/* reexport safe */ _middleware_http_auth_scheme__WEBPACK_IMPORTED_MODULE_1__.getHttpAuthSchemeEndpointRuleSetPlugin),\n/* harmony export */   getHttpAuthSchemePlugin: () => (/* reexport safe */ _middleware_http_auth_scheme__WEBPACK_IMPORTED_MODULE_1__.getHttpAuthSchemePlugin),\n/* harmony export */   getHttpSigningPlugin: () => (/* reexport safe */ _middleware_http_signing__WEBPACK_IMPORTED_MODULE_2__.getHttpSigningPlugin),\n/* harmony export */   getSmithyContext: () => (/* reexport safe */ _getSmithyContext__WEBPACK_IMPORTED_MODULE_0__.getSmithyContext),\n/* harmony export */   httpAuthSchemeEndpointRuleSetMiddlewareOptions: () => (/* reexport safe */ _middleware_http_auth_scheme__WEBPACK_IMPORTED_MODULE_1__.httpAuthSchemeEndpointRuleSetMiddlewareOptions),\n/* harmony export */   httpAuthSchemeMiddleware: () => (/* reexport safe */ _middleware_http_auth_scheme__WEBPACK_IMPORTED_MODULE_1__.httpAuthSchemeMiddleware),\n/* harmony export */   httpAuthSchemeMiddlewareOptions: () => (/* reexport safe */ _middleware_http_auth_scheme__WEBPACK_IMPORTED_MODULE_1__.httpAuthSchemeMiddlewareOptions),\n/* harmony export */   httpSigningMiddleware: () => (/* reexport safe */ _middleware_http_signing__WEBPACK_IMPORTED_MODULE_2__.httpSigningMiddleware),\n/* harmony export */   httpSigningMiddlewareOptions: () => (/* reexport safe */ _middleware_http_signing__WEBPACK_IMPORTED_MODULE_2__.httpSigningMiddlewareOptions),\n/* harmony export */   isIdentityExpired: () => (/* reexport safe */ _util_identity_and_auth__WEBPACK_IMPORTED_MODULE_7__.isIdentityExpired),\n/* harmony export */   memoizeIdentityProvider: () => (/* reexport safe */ _util_identity_and_auth__WEBPACK_IMPORTED_MODULE_7__.memoizeIdentityProvider),\n/* harmony export */   normalizeProvider: () => (/* reexport safe */ _normalizeProvider__WEBPACK_IMPORTED_MODULE_3__.normalizeProvider),\n/* harmony export */   requestBuilder: () => (/* reexport safe */ _protocols_requestBuilder__WEBPACK_IMPORTED_MODULE_5__.requestBuilder),\n/* harmony export */   setFeature: () => (/* reexport safe */ _setFeature__WEBPACK_IMPORTED_MODULE_6__.setFeature)\n/* harmony export */ });\n/* harmony import */ var _getSmithyContext__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getSmithyContext */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/getSmithyContext.js\");\n/* harmony import */ var _middleware_http_auth_scheme__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./middleware-http-auth-scheme */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/index.js\");\n/* harmony import */ var _middleware_http_signing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./middleware-http-signing */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-signing/index.js\");\n/* harmony import */ var _normalizeProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./normalizeProvider */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/normalizeProvider.js\");\n/* harmony import */ var _pagination_createPaginator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./pagination/createPaginator */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/pagination/createPaginator.js\");\n/* harmony import */ var _protocols_requestBuilder__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./protocols/requestBuilder */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/protocols/requestBuilder.js\");\n/* harmony import */ var _setFeature__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./setFeature */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/setFeature.js\");\n/* harmony import */ var _util_identity_and_auth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./util-identity-and-auth */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/index.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBbUM7QUFDVztBQUNKO0FBQ047QUFDMkI7QUFDcEI7QUFDZDtBQUNZIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9nZXRTbWl0aHlDb250ZXh0XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9taWRkbGV3YXJlLWh0dHAtYXV0aC1zY2hlbWVcIjtcbmV4cG9ydCAqIGZyb20gXCIuL21pZGRsZXdhcmUtaHR0cC1zaWduaW5nXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9ub3JtYWxpemVQcm92aWRlclwiO1xuZXhwb3J0IHsgY3JlYXRlUGFnaW5hdG9yIH0gZnJvbSBcIi4vcGFnaW5hdGlvbi9jcmVhdGVQYWdpbmF0b3JcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3Byb3RvY29scy9yZXF1ZXN0QnVpbGRlclwiO1xuZXhwb3J0ICogZnJvbSBcIi4vc2V0RmVhdHVyZVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vdXRpbC1pZGVudGl0eS1hbmQtYXV0aFwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/getHttpAuthSchemeEndpointRuleSetPlugin.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/getHttpAuthSchemeEndpointRuleSetPlugin.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHttpAuthSchemeEndpointRuleSetPlugin: () => (/* binding */ getHttpAuthSchemeEndpointRuleSetPlugin),\n/* harmony export */   httpAuthSchemeEndpointRuleSetMiddlewareOptions: () => (/* binding */ httpAuthSchemeEndpointRuleSetMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _httpAuthSchemeMiddleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./httpAuthSchemeMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/httpAuthSchemeMiddleware.js\");\n\nconst httpAuthSchemeEndpointRuleSetMiddlewareOptions = {\n    step: \"serialize\",\n    tags: [\"HTTP_AUTH_SCHEME\"],\n    name: \"httpAuthSchemeMiddleware\",\n    override: true,\n    relation: \"before\",\n    toMiddleware: \"endpointV2Middleware\",\n};\nconst getHttpAuthSchemeEndpointRuleSetPlugin = (config, { httpAuthSchemeParametersProvider, identityProviderConfigProvider, }) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo((0,_httpAuthSchemeMiddleware__WEBPACK_IMPORTED_MODULE_0__.httpAuthSchemeMiddleware)(config, {\n            httpAuthSchemeParametersProvider,\n            identityProviderConfigProvider,\n        }), httpAuthSchemeEndpointRuleSetMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9taWRkbGV3YXJlLWh0dHAtYXV0aC1zY2hlbWUvZ2V0SHR0cEF1dGhTY2hlbWVFbmRwb2ludFJ1bGVTZXRQbHVnaW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNFO0FBQy9EO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTywwREFBMEQsbUVBQW1FO0FBQ3BJO0FBQ0Esa0NBQWtDLG1GQUF3QjtBQUMxRDtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTCxDQUFDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9taWRkbGV3YXJlLWh0dHAtYXV0aC1zY2hlbWUvZ2V0SHR0cEF1dGhTY2hlbWVFbmRwb2ludFJ1bGVTZXRQbHVnaW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaHR0cEF1dGhTY2hlbWVNaWRkbGV3YXJlIH0gZnJvbSBcIi4vaHR0cEF1dGhTY2hlbWVNaWRkbGV3YXJlXCI7XG5leHBvcnQgY29uc3QgaHR0cEF1dGhTY2hlbWVFbmRwb2ludFJ1bGVTZXRNaWRkbGV3YXJlT3B0aW9ucyA9IHtcbiAgICBzdGVwOiBcInNlcmlhbGl6ZVwiLFxuICAgIHRhZ3M6IFtcIkhUVFBfQVVUSF9TQ0hFTUVcIl0sXG4gICAgbmFtZTogXCJodHRwQXV0aFNjaGVtZU1pZGRsZXdhcmVcIixcbiAgICBvdmVycmlkZTogdHJ1ZSxcbiAgICByZWxhdGlvbjogXCJiZWZvcmVcIixcbiAgICB0b01pZGRsZXdhcmU6IFwiZW5kcG9pbnRWMk1pZGRsZXdhcmVcIixcbn07XG5leHBvcnQgY29uc3QgZ2V0SHR0cEF1dGhTY2hlbWVFbmRwb2ludFJ1bGVTZXRQbHVnaW4gPSAoY29uZmlnLCB7IGh0dHBBdXRoU2NoZW1lUGFyYW1ldGVyc1Byb3ZpZGVyLCBpZGVudGl0eVByb3ZpZGVyQ29uZmlnUHJvdmlkZXIsIH0pID0+ICh7XG4gICAgYXBwbHlUb1N0YWNrOiAoY2xpZW50U3RhY2spID0+IHtcbiAgICAgICAgY2xpZW50U3RhY2suYWRkUmVsYXRpdmVUbyhodHRwQXV0aFNjaGVtZU1pZGRsZXdhcmUoY29uZmlnLCB7XG4gICAgICAgICAgICBodHRwQXV0aFNjaGVtZVBhcmFtZXRlcnNQcm92aWRlcixcbiAgICAgICAgICAgIGlkZW50aXR5UHJvdmlkZXJDb25maWdQcm92aWRlcixcbiAgICAgICAgfSksIGh0dHBBdXRoU2NoZW1lRW5kcG9pbnRSdWxlU2V0TWlkZGxld2FyZU9wdGlvbnMpO1xuICAgIH0sXG59KTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/getHttpAuthSchemeEndpointRuleSetPlugin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/getHttpAuthSchemePlugin.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/getHttpAuthSchemePlugin.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHttpAuthSchemePlugin: () => (/* binding */ getHttpAuthSchemePlugin),\n/* harmony export */   httpAuthSchemeMiddlewareOptions: () => (/* binding */ httpAuthSchemeMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _httpAuthSchemeMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./httpAuthSchemeMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/httpAuthSchemeMiddleware.js\");\n\n\nconst httpAuthSchemeMiddlewareOptions = {\n    step: \"serialize\",\n    tags: [\"HTTP_AUTH_SCHEME\"],\n    name: \"httpAuthSchemeMiddleware\",\n    override: true,\n    relation: \"before\",\n    toMiddleware: _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_0__.serializerMiddlewareOption.name,\n};\nconst getHttpAuthSchemePlugin = (config, { httpAuthSchemeParametersProvider, identityProviderConfigProvider, }) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo((0,_httpAuthSchemeMiddleware__WEBPACK_IMPORTED_MODULE_1__.httpAuthSchemeMiddleware)(config, {\n            httpAuthSchemeParametersProvider,\n            identityProviderConfigProvider,\n        }), httpAuthSchemeMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9taWRkbGV3YXJlLWh0dHAtYXV0aC1zY2hlbWUvZ2V0SHR0cEF1dGhTY2hlbWVQbHVnaW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFzRTtBQUNBO0FBQy9EO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixnRkFBMEI7QUFDNUM7QUFDTywyQ0FBMkMsbUVBQW1FO0FBQ3JIO0FBQ0Esa0NBQWtDLG1GQUF3QjtBQUMxRDtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTCxDQUFDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9taWRkbGV3YXJlLWh0dHAtYXV0aC1zY2hlbWUvZ2V0SHR0cEF1dGhTY2hlbWVQbHVnaW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc2VyaWFsaXplck1pZGRsZXdhcmVPcHRpb24gfSBmcm9tIFwiQHNtaXRoeS9taWRkbGV3YXJlLXNlcmRlXCI7XG5pbXBvcnQgeyBodHRwQXV0aFNjaGVtZU1pZGRsZXdhcmUgfSBmcm9tIFwiLi9odHRwQXV0aFNjaGVtZU1pZGRsZXdhcmVcIjtcbmV4cG9ydCBjb25zdCBodHRwQXV0aFNjaGVtZU1pZGRsZXdhcmVPcHRpb25zID0ge1xuICAgIHN0ZXA6IFwic2VyaWFsaXplXCIsXG4gICAgdGFnczogW1wiSFRUUF9BVVRIX1NDSEVNRVwiXSxcbiAgICBuYW1lOiBcImh0dHBBdXRoU2NoZW1lTWlkZGxld2FyZVwiLFxuICAgIG92ZXJyaWRlOiB0cnVlLFxuICAgIHJlbGF0aW9uOiBcImJlZm9yZVwiLFxuICAgIHRvTWlkZGxld2FyZTogc2VyaWFsaXplck1pZGRsZXdhcmVPcHRpb24ubmFtZSxcbn07XG5leHBvcnQgY29uc3QgZ2V0SHR0cEF1dGhTY2hlbWVQbHVnaW4gPSAoY29uZmlnLCB7IGh0dHBBdXRoU2NoZW1lUGFyYW1ldGVyc1Byb3ZpZGVyLCBpZGVudGl0eVByb3ZpZGVyQ29uZmlnUHJvdmlkZXIsIH0pID0+ICh7XG4gICAgYXBwbHlUb1N0YWNrOiAoY2xpZW50U3RhY2spID0+IHtcbiAgICAgICAgY2xpZW50U3RhY2suYWRkUmVsYXRpdmVUbyhodHRwQXV0aFNjaGVtZU1pZGRsZXdhcmUoY29uZmlnLCB7XG4gICAgICAgICAgICBodHRwQXV0aFNjaGVtZVBhcmFtZXRlcnNQcm92aWRlcixcbiAgICAgICAgICAgIGlkZW50aXR5UHJvdmlkZXJDb25maWdQcm92aWRlcixcbiAgICAgICAgfSksIGh0dHBBdXRoU2NoZW1lTWlkZGxld2FyZU9wdGlvbnMpO1xuICAgIH0sXG59KTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/getHttpAuthSchemePlugin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/httpAuthSchemeMiddleware.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/httpAuthSchemeMiddleware.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   httpAuthSchemeMiddleware: () => (/* binding */ httpAuthSchemeMiddleware)\n/* harmony export */ });\n/* harmony import */ var _smithy_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/types */ \"(rsc)/./node_modules/.pnpm/@smithy+types@4.3.1/node_modules/@smithy/types/dist-es/index.js\");\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n/* harmony import */ var _resolveAuthOptions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resolveAuthOptions */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/resolveAuthOptions.js\");\n\n\n\nfunction convertHttpAuthSchemesToMap(httpAuthSchemes) {\n    const map = new Map();\n    for (const scheme of httpAuthSchemes) {\n        map.set(scheme.schemeId, scheme);\n    }\n    return map;\n}\nconst httpAuthSchemeMiddleware = (config, mwOptions) => (next, context) => async (args) => {\n    const options = config.httpAuthSchemeProvider(await mwOptions.httpAuthSchemeParametersProvider(config, context, args.input));\n    const authSchemePreference = config.authSchemePreference ? await config.authSchemePreference() : [];\n    const resolvedOptions = (0,_resolveAuthOptions__WEBPACK_IMPORTED_MODULE_2__.resolveAuthOptions)(options, authSchemePreference);\n    const authSchemes = convertHttpAuthSchemesToMap(config.httpAuthSchemes);\n    const smithyContext = (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_1__.getSmithyContext)(context);\n    const failureReasons = [];\n    for (const option of resolvedOptions) {\n        const scheme = authSchemes.get(option.schemeId);\n        if (!scheme) {\n            failureReasons.push(`HttpAuthScheme \\`${option.schemeId}\\` was not enabled for this service.`);\n            continue;\n        }\n        const identityProvider = scheme.identityProvider(await mwOptions.identityProviderConfigProvider(config));\n        if (!identityProvider) {\n            failureReasons.push(`HttpAuthScheme \\`${option.schemeId}\\` did not have an IdentityProvider configured.`);\n            continue;\n        }\n        const { identityProperties = {}, signingProperties = {} } = option.propertiesExtractor?.(config, context) || {};\n        option.identityProperties = Object.assign(option.identityProperties || {}, identityProperties);\n        option.signingProperties = Object.assign(option.signingProperties || {}, signingProperties);\n        smithyContext.selectedHttpAuthScheme = {\n            httpAuthOption: option,\n            identity: await identityProvider(option.identityProperties),\n            signer: scheme.signer,\n        };\n        break;\n    }\n    if (!smithyContext.selectedHttpAuthScheme) {\n        throw new Error(failureReasons.join(\"\\n\"));\n    }\n    return next(args);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9taWRkbGV3YXJlLWh0dHAtYXV0aC1zY2hlbWUvaHR0cEF1dGhTY2hlbWVNaWRkbGV3YXJlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0Q7QUFDTztBQUNEO0FBQzFEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0EsNEJBQTRCLHVFQUFrQjtBQUM5QztBQUNBLDBCQUEwQix5RUFBZ0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvREFBb0QsZ0JBQWdCO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0RBQW9ELGdCQUFnQjtBQUNwRTtBQUNBO0FBQ0EsZ0JBQWdCLHVCQUF1QiwyQkFBMkI7QUFDbEUsaUZBQWlGO0FBQ2pGLCtFQUErRTtBQUMvRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvcmVAMy41LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29yZS9kaXN0LWVzL21pZGRsZXdhcmUtaHR0cC1hdXRoLXNjaGVtZS9odHRwQXV0aFNjaGVtZU1pZGRsZXdhcmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU01JVEhZX0NPTlRFWFRfS0VZLCB9IGZyb20gXCJAc21pdGh5L3R5cGVzXCI7XG5pbXBvcnQgeyBnZXRTbWl0aHlDb250ZXh0IH0gZnJvbSBcIkBzbWl0aHkvdXRpbC1taWRkbGV3YXJlXCI7XG5pbXBvcnQgeyByZXNvbHZlQXV0aE9wdGlvbnMgfSBmcm9tIFwiLi9yZXNvbHZlQXV0aE9wdGlvbnNcIjtcbmZ1bmN0aW9uIGNvbnZlcnRIdHRwQXV0aFNjaGVtZXNUb01hcChodHRwQXV0aFNjaGVtZXMpIHtcbiAgICBjb25zdCBtYXAgPSBuZXcgTWFwKCk7XG4gICAgZm9yIChjb25zdCBzY2hlbWUgb2YgaHR0cEF1dGhTY2hlbWVzKSB7XG4gICAgICAgIG1hcC5zZXQoc2NoZW1lLnNjaGVtZUlkLCBzY2hlbWUpO1xuICAgIH1cbiAgICByZXR1cm4gbWFwO1xufVxuZXhwb3J0IGNvbnN0IGh0dHBBdXRoU2NoZW1lTWlkZGxld2FyZSA9IChjb25maWcsIG13T3B0aW9ucykgPT4gKG5leHQsIGNvbnRleHQpID0+IGFzeW5jIChhcmdzKSA9PiB7XG4gICAgY29uc3Qgb3B0aW9ucyA9IGNvbmZpZy5odHRwQXV0aFNjaGVtZVByb3ZpZGVyKGF3YWl0IG13T3B0aW9ucy5odHRwQXV0aFNjaGVtZVBhcmFtZXRlcnNQcm92aWRlcihjb25maWcsIGNvbnRleHQsIGFyZ3MuaW5wdXQpKTtcbiAgICBjb25zdCBhdXRoU2NoZW1lUHJlZmVyZW5jZSA9IGNvbmZpZy5hdXRoU2NoZW1lUHJlZmVyZW5jZSA/IGF3YWl0IGNvbmZpZy5hdXRoU2NoZW1lUHJlZmVyZW5jZSgpIDogW107XG4gICAgY29uc3QgcmVzb2x2ZWRPcHRpb25zID0gcmVzb2x2ZUF1dGhPcHRpb25zKG9wdGlvbnMsIGF1dGhTY2hlbWVQcmVmZXJlbmNlKTtcbiAgICBjb25zdCBhdXRoU2NoZW1lcyA9IGNvbnZlcnRIdHRwQXV0aFNjaGVtZXNUb01hcChjb25maWcuaHR0cEF1dGhTY2hlbWVzKTtcbiAgICBjb25zdCBzbWl0aHlDb250ZXh0ID0gZ2V0U21pdGh5Q29udGV4dChjb250ZXh0KTtcbiAgICBjb25zdCBmYWlsdXJlUmVhc29ucyA9IFtdO1xuICAgIGZvciAoY29uc3Qgb3B0aW9uIG9mIHJlc29sdmVkT3B0aW9ucykge1xuICAgICAgICBjb25zdCBzY2hlbWUgPSBhdXRoU2NoZW1lcy5nZXQob3B0aW9uLnNjaGVtZUlkKTtcbiAgICAgICAgaWYgKCFzY2hlbWUpIHtcbiAgICAgICAgICAgIGZhaWx1cmVSZWFzb25zLnB1c2goYEh0dHBBdXRoU2NoZW1lIFxcYCR7b3B0aW9uLnNjaGVtZUlkfVxcYCB3YXMgbm90IGVuYWJsZWQgZm9yIHRoaXMgc2VydmljZS5gKTtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGlkZW50aXR5UHJvdmlkZXIgPSBzY2hlbWUuaWRlbnRpdHlQcm92aWRlcihhd2FpdCBtd09wdGlvbnMuaWRlbnRpdHlQcm92aWRlckNvbmZpZ1Byb3ZpZGVyKGNvbmZpZykpO1xuICAgICAgICBpZiAoIWlkZW50aXR5UHJvdmlkZXIpIHtcbiAgICAgICAgICAgIGZhaWx1cmVSZWFzb25zLnB1c2goYEh0dHBBdXRoU2NoZW1lIFxcYCR7b3B0aW9uLnNjaGVtZUlkfVxcYCBkaWQgbm90IGhhdmUgYW4gSWRlbnRpdHlQcm92aWRlciBjb25maWd1cmVkLmApO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgeyBpZGVudGl0eVByb3BlcnRpZXMgPSB7fSwgc2lnbmluZ1Byb3BlcnRpZXMgPSB7fSB9ID0gb3B0aW9uLnByb3BlcnRpZXNFeHRyYWN0b3I/Lihjb25maWcsIGNvbnRleHQpIHx8IHt9O1xuICAgICAgICBvcHRpb24uaWRlbnRpdHlQcm9wZXJ0aWVzID0gT2JqZWN0LmFzc2lnbihvcHRpb24uaWRlbnRpdHlQcm9wZXJ0aWVzIHx8IHt9LCBpZGVudGl0eVByb3BlcnRpZXMpO1xuICAgICAgICBvcHRpb24uc2lnbmluZ1Byb3BlcnRpZXMgPSBPYmplY3QuYXNzaWduKG9wdGlvbi5zaWduaW5nUHJvcGVydGllcyB8fCB7fSwgc2lnbmluZ1Byb3BlcnRpZXMpO1xuICAgICAgICBzbWl0aHlDb250ZXh0LnNlbGVjdGVkSHR0cEF1dGhTY2hlbWUgPSB7XG4gICAgICAgICAgICBodHRwQXV0aE9wdGlvbjogb3B0aW9uLFxuICAgICAgICAgICAgaWRlbnRpdHk6IGF3YWl0IGlkZW50aXR5UHJvdmlkZXIob3B0aW9uLmlkZW50aXR5UHJvcGVydGllcyksXG4gICAgICAgICAgICBzaWduZXI6IHNjaGVtZS5zaWduZXIsXG4gICAgICAgIH07XG4gICAgICAgIGJyZWFrO1xuICAgIH1cbiAgICBpZiAoIXNtaXRoeUNvbnRleHQuc2VsZWN0ZWRIdHRwQXV0aFNjaGVtZSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZmFpbHVyZVJlYXNvbnMuam9pbihcIlxcblwiKSk7XG4gICAgfVxuICAgIHJldHVybiBuZXh0KGFyZ3MpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/httpAuthSchemeMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/index.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/index.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHttpAuthSchemeEndpointRuleSetPlugin: () => (/* reexport safe */ _getHttpAuthSchemeEndpointRuleSetPlugin__WEBPACK_IMPORTED_MODULE_1__.getHttpAuthSchemeEndpointRuleSetPlugin),\n/* harmony export */   getHttpAuthSchemePlugin: () => (/* reexport safe */ _getHttpAuthSchemePlugin__WEBPACK_IMPORTED_MODULE_2__.getHttpAuthSchemePlugin),\n/* harmony export */   httpAuthSchemeEndpointRuleSetMiddlewareOptions: () => (/* reexport safe */ _getHttpAuthSchemeEndpointRuleSetPlugin__WEBPACK_IMPORTED_MODULE_1__.httpAuthSchemeEndpointRuleSetMiddlewareOptions),\n/* harmony export */   httpAuthSchemeMiddleware: () => (/* reexport safe */ _httpAuthSchemeMiddleware__WEBPACK_IMPORTED_MODULE_0__.httpAuthSchemeMiddleware),\n/* harmony export */   httpAuthSchemeMiddlewareOptions: () => (/* reexport safe */ _getHttpAuthSchemePlugin__WEBPACK_IMPORTED_MODULE_2__.httpAuthSchemeMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _httpAuthSchemeMiddleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./httpAuthSchemeMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/httpAuthSchemeMiddleware.js\");\n/* harmony import */ var _getHttpAuthSchemeEndpointRuleSetPlugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getHttpAuthSchemeEndpointRuleSetPlugin */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/getHttpAuthSchemeEndpointRuleSetPlugin.js\");\n/* harmony import */ var _getHttpAuthSchemePlugin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getHttpAuthSchemePlugin */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/getHttpAuthSchemePlugin.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9taWRkbGV3YXJlLWh0dHAtYXV0aC1zY2hlbWUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBMkM7QUFDYztBQUNmIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9taWRkbGV3YXJlLWh0dHAtYXV0aC1zY2hlbWUvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vaHR0cEF1dGhTY2hlbWVNaWRkbGV3YXJlXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9nZXRIdHRwQXV0aFNjaGVtZUVuZHBvaW50UnVsZVNldFBsdWdpblwiO1xuZXhwb3J0ICogZnJvbSBcIi4vZ2V0SHR0cEF1dGhTY2hlbWVQbHVnaW5cIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/resolveAuthOptions.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/resolveAuthOptions.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveAuthOptions: () => (/* binding */ resolveAuthOptions)\n/* harmony export */ });\nconst resolveAuthOptions = (candidateAuthOptions, authSchemePreference) => {\n    if (!authSchemePreference || authSchemePreference.length === 0) {\n        return candidateAuthOptions;\n    }\n    const preferredAuthOptions = [];\n    for (const preferredSchemeName of authSchemePreference) {\n        for (const candidateAuthOption of candidateAuthOptions) {\n            const candidateAuthSchemeName = candidateAuthOption.schemeId.split(\"#\")[1];\n            if (candidateAuthSchemeName === preferredSchemeName) {\n                preferredAuthOptions.push(candidateAuthOption);\n            }\n        }\n    }\n    for (const candidateAuthOption of candidateAuthOptions) {\n        if (!preferredAuthOptions.find(({ schemeId }) => schemeId === candidateAuthOption.schemeId)) {\n            preferredAuthOptions.push(candidateAuthOption);\n        }\n    }\n    return preferredAuthOptions;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9taWRkbGV3YXJlLWh0dHAtYXV0aC1zY2hlbWUvcmVzb2x2ZUF1dGhPcHRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLFVBQVU7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29yZUAzLjUuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb3JlL2Rpc3QtZXMvbWlkZGxld2FyZS1odHRwLWF1dGgtc2NoZW1lL3Jlc29sdmVBdXRoT3B0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgcmVzb2x2ZUF1dGhPcHRpb25zID0gKGNhbmRpZGF0ZUF1dGhPcHRpb25zLCBhdXRoU2NoZW1lUHJlZmVyZW5jZSkgPT4ge1xuICAgIGlmICghYXV0aFNjaGVtZVByZWZlcmVuY2UgfHwgYXV0aFNjaGVtZVByZWZlcmVuY2UubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHJldHVybiBjYW5kaWRhdGVBdXRoT3B0aW9ucztcbiAgICB9XG4gICAgY29uc3QgcHJlZmVycmVkQXV0aE9wdGlvbnMgPSBbXTtcbiAgICBmb3IgKGNvbnN0IHByZWZlcnJlZFNjaGVtZU5hbWUgb2YgYXV0aFNjaGVtZVByZWZlcmVuY2UpIHtcbiAgICAgICAgZm9yIChjb25zdCBjYW5kaWRhdGVBdXRoT3B0aW9uIG9mIGNhbmRpZGF0ZUF1dGhPcHRpb25zKSB7XG4gICAgICAgICAgICBjb25zdCBjYW5kaWRhdGVBdXRoU2NoZW1lTmFtZSA9IGNhbmRpZGF0ZUF1dGhPcHRpb24uc2NoZW1lSWQuc3BsaXQoXCIjXCIpWzFdO1xuICAgICAgICAgICAgaWYgKGNhbmRpZGF0ZUF1dGhTY2hlbWVOYW1lID09PSBwcmVmZXJyZWRTY2hlbWVOYW1lKSB7XG4gICAgICAgICAgICAgICAgcHJlZmVycmVkQXV0aE9wdGlvbnMucHVzaChjYW5kaWRhdGVBdXRoT3B0aW9uKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBmb3IgKGNvbnN0IGNhbmRpZGF0ZUF1dGhPcHRpb24gb2YgY2FuZGlkYXRlQXV0aE9wdGlvbnMpIHtcbiAgICAgICAgaWYgKCFwcmVmZXJyZWRBdXRoT3B0aW9ucy5maW5kKCh7IHNjaGVtZUlkIH0pID0+IHNjaGVtZUlkID09PSBjYW5kaWRhdGVBdXRoT3B0aW9uLnNjaGVtZUlkKSkge1xuICAgICAgICAgICAgcHJlZmVycmVkQXV0aE9wdGlvbnMucHVzaChjYW5kaWRhdGVBdXRoT3B0aW9uKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcHJlZmVycmVkQXV0aE9wdGlvbnM7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-auth-scheme/resolveAuthOptions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-signing/getHttpSigningMiddleware.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-signing/getHttpSigningMiddleware.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHttpSigningPlugin: () => (/* binding */ getHttpSigningPlugin),\n/* harmony export */   httpSigningMiddlewareOptions: () => (/* binding */ httpSigningMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _httpSigningMiddleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./httpSigningMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-signing/httpSigningMiddleware.js\");\n\nconst httpSigningMiddlewareOptions = {\n    step: \"finalizeRequest\",\n    tags: [\"HTTP_SIGNING\"],\n    name: \"httpSigningMiddleware\",\n    aliases: [\"apiKeyMiddleware\", \"tokenMiddleware\", \"awsAuthMiddleware\"],\n    override: true,\n    relation: \"after\",\n    toMiddleware: \"retryMiddleware\",\n};\nconst getHttpSigningPlugin = (config) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo((0,_httpSigningMiddleware__WEBPACK_IMPORTED_MODULE_0__.httpSigningMiddleware)(config), httpSigningMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9taWRkbGV3YXJlLWh0dHAtc2lnbmluZy9nZXRIdHRwU2lnbmluZ01pZGRsZXdhcmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdFO0FBQ3pEO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSxrQ0FBa0MsNkVBQXFCO0FBQ3ZELEtBQUs7QUFDTCxDQUFDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9taWRkbGV3YXJlLWh0dHAtc2lnbmluZy9nZXRIdHRwU2lnbmluZ01pZGRsZXdhcmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaHR0cFNpZ25pbmdNaWRkbGV3YXJlIH0gZnJvbSBcIi4vaHR0cFNpZ25pbmdNaWRkbGV3YXJlXCI7XG5leHBvcnQgY29uc3QgaHR0cFNpZ25pbmdNaWRkbGV3YXJlT3B0aW9ucyA9IHtcbiAgICBzdGVwOiBcImZpbmFsaXplUmVxdWVzdFwiLFxuICAgIHRhZ3M6IFtcIkhUVFBfU0lHTklOR1wiXSxcbiAgICBuYW1lOiBcImh0dHBTaWduaW5nTWlkZGxld2FyZVwiLFxuICAgIGFsaWFzZXM6IFtcImFwaUtleU1pZGRsZXdhcmVcIiwgXCJ0b2tlbk1pZGRsZXdhcmVcIiwgXCJhd3NBdXRoTWlkZGxld2FyZVwiXSxcbiAgICBvdmVycmlkZTogdHJ1ZSxcbiAgICByZWxhdGlvbjogXCJhZnRlclwiLFxuICAgIHRvTWlkZGxld2FyZTogXCJyZXRyeU1pZGRsZXdhcmVcIixcbn07XG5leHBvcnQgY29uc3QgZ2V0SHR0cFNpZ25pbmdQbHVnaW4gPSAoY29uZmlnKSA9PiAoe1xuICAgIGFwcGx5VG9TdGFjazogKGNsaWVudFN0YWNrKSA9PiB7XG4gICAgICAgIGNsaWVudFN0YWNrLmFkZFJlbGF0aXZlVG8oaHR0cFNpZ25pbmdNaWRkbGV3YXJlKGNvbmZpZyksIGh0dHBTaWduaW5nTWlkZGxld2FyZU9wdGlvbnMpO1xuICAgIH0sXG59KTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-signing/getHttpSigningMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-signing/httpSigningMiddleware.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-signing/httpSigningMiddleware.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   httpSigningMiddleware: () => (/* binding */ httpSigningMiddleware)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/types */ \"(rsc)/./node_modules/.pnpm/@smithy+types@4.3.1/node_modules/@smithy/types/dist-es/index.js\");\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n\n\n\nconst defaultErrorHandler = (signingProperties) => (error) => {\n    throw error;\n};\nconst defaultSuccessHandler = (httpResponse, signingProperties) => { };\nconst httpSigningMiddleware = (config) => (next, context) => async (args) => {\n    if (!_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.isInstance(args.request)) {\n        return next(args);\n    }\n    const smithyContext = (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_2__.getSmithyContext)(context);\n    const scheme = smithyContext.selectedHttpAuthScheme;\n    if (!scheme) {\n        throw new Error(`No HttpAuthScheme was selected: unable to sign request`);\n    }\n    const { httpAuthOption: { signingProperties = {} }, identity, signer, } = scheme;\n    const output = await next({\n        ...args,\n        request: await signer.sign(args.request, identity, signingProperties),\n    }).catch((signer.errorHandler || defaultErrorHandler)(signingProperties));\n    (signer.successHandler || defaultSuccessHandler)(output.response, signingProperties);\n    return output;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-signing/httpSigningMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-signing/index.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-signing/index.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHttpSigningPlugin: () => (/* reexport safe */ _getHttpSigningMiddleware__WEBPACK_IMPORTED_MODULE_1__.getHttpSigningPlugin),\n/* harmony export */   httpSigningMiddleware: () => (/* reexport safe */ _httpSigningMiddleware__WEBPACK_IMPORTED_MODULE_0__.httpSigningMiddleware),\n/* harmony export */   httpSigningMiddlewareOptions: () => (/* reexport safe */ _getHttpSigningMiddleware__WEBPACK_IMPORTED_MODULE_1__.httpSigningMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _httpSigningMiddleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./httpSigningMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-signing/httpSigningMiddleware.js\");\n/* harmony import */ var _getHttpSigningMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getHttpSigningMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-signing/getHttpSigningMiddleware.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9taWRkbGV3YXJlLWh0dHAtc2lnbmluZy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF3QztBQUNHIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9taWRkbGV3YXJlLWh0dHAtc2lnbmluZy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9odHRwU2lnbmluZ01pZGRsZXdhcmVcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2dldEh0dHBTaWduaW5nTWlkZGxld2FyZVwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/middleware-http-signing/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/normalizeProvider.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/normalizeProvider.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalizeProvider: () => (/* binding */ normalizeProvider)\n/* harmony export */ });\nconst normalizeProvider = (input) => {\n    if (typeof input === \"function\")\n        return input;\n    const promisified = Promise.resolve(input);\n    return () => promisified;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9ub3JtYWxpemVQcm92aWRlci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9ub3JtYWxpemVQcm92aWRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3Qgbm9ybWFsaXplUHJvdmlkZXIgPSAoaW5wdXQpID0+IHtcbiAgICBpZiAodHlwZW9mIGlucHV0ID09PSBcImZ1bmN0aW9uXCIpXG4gICAgICAgIHJldHVybiBpbnB1dDtcbiAgICBjb25zdCBwcm9taXNpZmllZCA9IFByb21pc2UucmVzb2x2ZShpbnB1dCk7XG4gICAgcmV0dXJuICgpID0+IHByb21pc2lmaWVkO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/normalizeProvider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/pagination/createPaginator.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/pagination/createPaginator.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createPaginator: () => (/* binding */ createPaginator)\n/* harmony export */ });\nconst makePagedClientRequest = async (CommandCtor, client, input, withCommand = (_) => _, ...args) => {\n    let command = new CommandCtor(input);\n    command = withCommand(command) ?? command;\n    return await client.send(command, ...args);\n};\nfunction createPaginator(ClientCtor, CommandCtor, inputTokenName, outputTokenName, pageSizeTokenName) {\n    return async function* paginateOperation(config, input, ...additionalArguments) {\n        const _input = input;\n        let token = config.startingToken ?? _input[inputTokenName];\n        let hasNext = true;\n        let page;\n        while (hasNext) {\n            _input[inputTokenName] = token;\n            if (pageSizeTokenName) {\n                _input[pageSizeTokenName] = _input[pageSizeTokenName] ?? config.pageSize;\n            }\n            if (config.client instanceof ClientCtor) {\n                page = await makePagedClientRequest(CommandCtor, config.client, input, config.withCommand, ...additionalArguments);\n            }\n            else {\n                throw new Error(`Invalid client, expected instance of ${ClientCtor.name}`);\n            }\n            yield page;\n            const prevToken = token;\n            token = get(page, outputTokenName);\n            hasNext = !!(token && (!config.stopOnSameToken || token !== prevToken));\n        }\n        return undefined;\n    };\n}\nconst get = (fromObject, path) => {\n    let cursor = fromObject;\n    const pathComponents = path.split(\".\");\n    for (const step of pathComponents) {\n        if (!cursor || typeof cursor !== \"object\") {\n            return undefined;\n        }\n        cursor = cursor[step];\n    }\n    return cursor;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/pagination/createPaginator.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/protocols/requestBuilder.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/protocols/requestBuilder.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   requestBuilder: () => (/* reexport safe */ _smithy_core_protocols__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)\n/* harmony export */ });\n/* harmony import */ var _smithy_core_protocols__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core/protocols */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/index.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9wcm90b2NvbHMvcmVxdWVzdEJ1aWxkZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBd0QiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvcmVAMy41LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29yZS9kaXN0LWVzL3Byb3RvY29scy9yZXF1ZXN0QnVpbGRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyByZXF1ZXN0QnVpbGRlciB9IGZyb20gXCJAc21pdGh5L2NvcmUvcHJvdG9jb2xzXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/protocols/requestBuilder.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/setFeature.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/setFeature.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setFeature: () => (/* binding */ setFeature)\n/* harmony export */ });\nfunction setFeature(context, feature, value) {\n    if (!context.__smithy_context) {\n        context.__smithy_context = {\n            features: {},\n        };\n    }\n    else if (!context.__smithy_context.features) {\n        context.__smithy_context.features = {};\n    }\n    context.__smithy_context.features[feature] = value;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zZXRGZWF0dXJlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvcmVAMy41LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29yZS9kaXN0LWVzL3NldEZlYXR1cmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHNldEZlYXR1cmUoY29udGV4dCwgZmVhdHVyZSwgdmFsdWUpIHtcbiAgICBpZiAoIWNvbnRleHQuX19zbWl0aHlfY29udGV4dCkge1xuICAgICAgICBjb250ZXh0Ll9fc21pdGh5X2NvbnRleHQgPSB7XG4gICAgICAgICAgICBmZWF0dXJlczoge30sXG4gICAgICAgIH07XG4gICAgfVxuICAgIGVsc2UgaWYgKCFjb250ZXh0Ll9fc21pdGh5X2NvbnRleHQuZmVhdHVyZXMpIHtcbiAgICAgICAgY29udGV4dC5fX3NtaXRoeV9jb250ZXh0LmZlYXR1cmVzID0ge307XG4gICAgfVxuICAgIGNvbnRleHQuX19zbWl0aHlfY29udGV4dC5mZWF0dXJlc1tmZWF0dXJlXSA9IHZhbHVlO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/setFeature.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/HttpBindingProtocol.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/HttpBindingProtocol.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpBindingProtocol: () => (/* binding */ HttpBindingProtocol)\n/* harmony export */ });\n/* harmony import */ var _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core/schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/index.js\");\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _collect_stream_body__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./collect-stream-body */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/collect-stream-body.js\");\n/* harmony import */ var _extended_encode_uri_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./extended-encode-uri-component */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/extended-encode-uri-component.js\");\n/* harmony import */ var _HttpProtocol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./HttpProtocol */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/HttpProtocol.js\");\n\n\n\n\n\nclass HttpBindingProtocol extends _HttpProtocol__WEBPACK_IMPORTED_MODULE_4__.HttpProtocol {\n    async serializeRequest(operationSchema, input, context) {\n        const serializer = this.serializer;\n        const query = {};\n        const headers = {};\n        const endpoint = await context.endpoint();\n        const ns = _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.of(operationSchema?.input);\n        const schema = ns.getSchema();\n        let hasNonHttpBindingMember = false;\n        let payload;\n        const request = new _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__.HttpRequest({\n            protocol: \"\",\n            hostname: \"\",\n            port: undefined,\n            path: \"\",\n            fragment: undefined,\n            query: query,\n            headers: headers,\n            body: undefined,\n        });\n        if (endpoint) {\n            this.updateServiceEndpoint(request, endpoint);\n            this.setHostPrefix(request, operationSchema, input);\n            const opTraits = _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.translateTraits(operationSchema.traits);\n            if (opTraits.http) {\n                request.method = opTraits.http[0];\n                const [path, search] = opTraits.http[1].split(\"?\");\n                if (request.path == \"/\") {\n                    request.path = path;\n                }\n                else {\n                    request.path += path;\n                }\n                const traitSearchParams = new URLSearchParams(search ?? \"\");\n                Object.assign(query, Object.fromEntries(traitSearchParams));\n            }\n        }\n        const _input = {\n            ...input,\n        };\n        for (const memberName of Object.keys(_input)) {\n            const memberNs = ns.getMemberSchema(memberName);\n            if (memberNs === undefined) {\n                continue;\n            }\n            const memberTraits = memberNs.getMergedTraits();\n            const inputMember = _input[memberName];\n            if (memberTraits.httpPayload) {\n                const isStreaming = memberNs.isStreaming();\n                if (isStreaming) {\n                    const isEventStream = memberNs.isStructSchema();\n                    if (isEventStream) {\n                        throw new Error(\"serialization of event streams is not yet implemented\");\n                    }\n                    else {\n                        payload = inputMember;\n                    }\n                }\n                else {\n                    serializer.write(memberNs, inputMember);\n                    payload = serializer.flush();\n                }\n            }\n            else if (memberTraits.httpLabel) {\n                serializer.write(memberNs, inputMember);\n                const replacement = serializer.flush();\n                if (request.path.includes(`{${memberName}+}`)) {\n                    request.path = request.path.replace(`{${memberName}+}`, replacement.split(\"/\").map(_extended_encode_uri_component__WEBPACK_IMPORTED_MODULE_3__.extendedEncodeURIComponent).join(\"/\"));\n                }\n                else if (request.path.includes(`{${memberName}}`)) {\n                    request.path = request.path.replace(`{${memberName}}`, (0,_extended_encode_uri_component__WEBPACK_IMPORTED_MODULE_3__.extendedEncodeURIComponent)(replacement));\n                }\n                delete _input[memberName];\n            }\n            else if (memberTraits.httpHeader) {\n                serializer.write(memberNs, inputMember);\n                headers[memberTraits.httpHeader.toLowerCase()] = String(serializer.flush());\n                delete _input[memberName];\n            }\n            else if (typeof memberTraits.httpPrefixHeaders === \"string\") {\n                for (const [key, val] of Object.entries(inputMember)) {\n                    const amalgam = memberTraits.httpPrefixHeaders + key;\n                    serializer.write([memberNs.getValueSchema(), { httpHeader: amalgam }], val);\n                    headers[amalgam.toLowerCase()] = serializer.flush();\n                }\n                delete _input[memberName];\n            }\n            else if (memberTraits.httpQuery || memberTraits.httpQueryParams) {\n                this.serializeQuery(memberNs, inputMember, query);\n                delete _input[memberName];\n            }\n            else {\n                hasNonHttpBindingMember = true;\n            }\n        }\n        if (hasNonHttpBindingMember && input) {\n            serializer.write(schema, _input);\n            payload = serializer.flush();\n        }\n        request.headers = headers;\n        request.query = query;\n        request.body = payload;\n        return request;\n    }\n    serializeQuery(ns, data, query) {\n        const serializer = this.serializer;\n        const traits = ns.getMergedTraits();\n        if (traits.httpQueryParams) {\n            for (const [key, val] of Object.entries(data)) {\n                if (!(key in query)) {\n                    this.serializeQuery(_smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.of([\n                        ns.getValueSchema(),\n                        {\n                            ...traits,\n                            httpQuery: key,\n                            httpQueryParams: undefined,\n                        },\n                    ]), val, query);\n                }\n            }\n            return;\n        }\n        if (ns.isListSchema()) {\n            const sparse = !!ns.getMergedTraits().sparse;\n            const buffer = [];\n            for (const item of data) {\n                serializer.write([ns.getValueSchema(), traits], item);\n                const serializable = serializer.flush();\n                if (sparse || serializable !== undefined) {\n                    buffer.push(serializable);\n                }\n            }\n            query[traits.httpQuery] = buffer;\n        }\n        else {\n            serializer.write([ns, traits], data);\n            query[traits.httpQuery] = serializer.flush();\n        }\n    }\n    async deserializeResponse(operationSchema, context, response) {\n        const deserializer = this.deserializer;\n        const ns = _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.of(operationSchema.output);\n        const dataObject = {};\n        if (response.statusCode >= 300) {\n            const bytes = await (0,_collect_stream_body__WEBPACK_IMPORTED_MODULE_2__.collectBody)(response.body, context);\n            if (bytes.byteLength > 0) {\n                Object.assign(dataObject, await deserializer.read(_smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.DOCUMENT, bytes));\n            }\n            await this.handleError(operationSchema, context, response, dataObject, this.deserializeMetadata(response));\n            throw new Error(\"@smithy/core/protocols - HTTP Protocol error handler failed to throw.\");\n        }\n        for (const header in response.headers) {\n            const value = response.headers[header];\n            delete response.headers[header];\n            response.headers[header.toLowerCase()] = value;\n        }\n        const nonHttpBindingMembers = await this.deserializeHttpMessage(ns, context, response, dataObject);\n        if (nonHttpBindingMembers.length) {\n            const bytes = await (0,_collect_stream_body__WEBPACK_IMPORTED_MODULE_2__.collectBody)(response.body, context);\n            if (bytes.byteLength > 0) {\n                const dataFromBody = await deserializer.read(ns, bytes);\n                for (const member of nonHttpBindingMembers) {\n                    dataObject[member] = dataFromBody[member];\n                }\n            }\n        }\n        const output = {\n            $metadata: this.deserializeMetadata(response),\n            ...dataObject,\n        };\n        return output;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/HttpBindingProtocol.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/HttpProtocol.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/HttpProtocol.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpProtocol: () => (/* binding */ HttpProtocol)\n/* harmony export */ });\n/* harmony import */ var _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core/schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/index.js\");\n/* harmony import */ var _smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/core/serde */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/index.js\");\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_util_stream__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/util-stream */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/index.js\");\n/* harmony import */ var _collect_stream_body__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./collect-stream-body */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/collect-stream-body.js\");\n\n\n\n\n\nclass HttpProtocol {\n    constructor(options) {\n        this.options = options;\n    }\n    getRequestType() {\n        return _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_2__.HttpRequest;\n    }\n    getResponseType() {\n        return _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_2__.HttpResponse;\n    }\n    setSerdeContext(serdeContext) {\n        this.serdeContext = serdeContext;\n        this.serializer.setSerdeContext(serdeContext);\n        this.deserializer.setSerdeContext(serdeContext);\n        if (this.getPayloadCodec()) {\n            this.getPayloadCodec().setSerdeContext(serdeContext);\n        }\n    }\n    updateServiceEndpoint(request, endpoint) {\n        if (\"url\" in endpoint) {\n            request.protocol = endpoint.url.protocol;\n            request.hostname = endpoint.url.hostname;\n            request.port = endpoint.url.port ? Number(endpoint.url.port) : undefined;\n            request.path = endpoint.url.pathname;\n            request.fragment = endpoint.url.hash || void 0;\n            request.username = endpoint.url.username || void 0;\n            request.password = endpoint.url.password || void 0;\n            for (const [k, v] of endpoint.url.searchParams.entries()) {\n                if (!request.query) {\n                    request.query = {};\n                }\n                request.query[k] = v;\n            }\n            return request;\n        }\n        else {\n            request.protocol = endpoint.protocol;\n            request.hostname = endpoint.hostname;\n            request.port = endpoint.port ? Number(endpoint.port) : undefined;\n            request.path = endpoint.path;\n            request.query = {\n                ...endpoint.query,\n            };\n            return request;\n        }\n    }\n    setHostPrefix(request, operationSchema, input) {\n        const operationNs = _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.of(operationSchema);\n        const inputNs = _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.of(operationSchema.input);\n        if (operationNs.getMergedTraits().endpoint) {\n            let hostPrefix = operationNs.getMergedTraits().endpoint?.[0];\n            if (typeof hostPrefix === \"string\") {\n                const hostLabelInputs = [...inputNs.structIterator()].filter(([, member]) => member.getMergedTraits().hostLabel);\n                for (const [name] of hostLabelInputs) {\n                    const replacement = input[name];\n                    if (typeof replacement !== \"string\") {\n                        throw new Error(`@smithy/core/schema - ${name} in input must be a string as hostLabel.`);\n                    }\n                    hostPrefix = hostPrefix.replace(`{${name}}`, replacement);\n                }\n                request.hostname = hostPrefix + request.hostname;\n            }\n        }\n    }\n    deserializeMetadata(output) {\n        return {\n            httpStatusCode: output.statusCode,\n            requestId: output.headers[\"x-amzn-requestid\"] ?? output.headers[\"x-amzn-request-id\"] ?? output.headers[\"x-amz-request-id\"],\n            extendedRequestId: output.headers[\"x-amz-id-2\"],\n            cfId: output.headers[\"x-amz-cf-id\"],\n        };\n    }\n    async deserializeHttpMessage(schema, context, response, arg4, arg5) {\n        let dataObject;\n        if (arg4 instanceof Set) {\n            dataObject = arg5;\n        }\n        else {\n            dataObject = arg4;\n        }\n        const deserializer = this.deserializer;\n        const ns = _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.of(schema);\n        const nonHttpBindingMembers = [];\n        for (const [memberName, memberSchema] of ns.structIterator()) {\n            const memberTraits = memberSchema.getMemberTraits();\n            if (memberTraits.httpPayload) {\n                const isStreaming = memberSchema.isStreaming();\n                if (isStreaming) {\n                    const isEventStream = memberSchema.isStructSchema();\n                    if (isEventStream) {\n                        const context = this.serdeContext;\n                        if (!context.eventStreamMarshaller) {\n                            throw new Error(\"@smithy/core - HttpProtocol: eventStreamMarshaller missing in serdeContext.\");\n                        }\n                        const memberSchemas = memberSchema.getMemberSchemas();\n                        dataObject[memberName] = context.eventStreamMarshaller.deserialize(response.body, async (event) => {\n                            const unionMember = Object.keys(event).find((key) => {\n                                return key !== \"__type\";\n                            }) ?? \"\";\n                            if (unionMember in memberSchemas) {\n                                const eventStreamSchema = memberSchemas[unionMember];\n                                return {\n                                    [unionMember]: await deserializer.read(eventStreamSchema, event[unionMember].body),\n                                };\n                            }\n                            else {\n                                return {\n                                    $unknown: event,\n                                };\n                            }\n                        });\n                    }\n                    else {\n                        dataObject[memberName] = (0,_smithy_util_stream__WEBPACK_IMPORTED_MODULE_3__.sdkStreamMixin)(response.body);\n                    }\n                }\n                else if (response.body) {\n                    const bytes = await (0,_collect_stream_body__WEBPACK_IMPORTED_MODULE_4__.collectBody)(response.body, context);\n                    if (bytes.byteLength > 0) {\n                        dataObject[memberName] = await deserializer.read(memberSchema, bytes);\n                    }\n                }\n            }\n            else if (memberTraits.httpHeader) {\n                const key = String(memberTraits.httpHeader).toLowerCase();\n                const value = response.headers[key];\n                if (null != value) {\n                    if (memberSchema.isListSchema()) {\n                        const headerListValueSchema = memberSchema.getValueSchema();\n                        let sections;\n                        if (headerListValueSchema.isTimestampSchema() &&\n                            headerListValueSchema.getSchema() === _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.TIMESTAMP_DEFAULT) {\n                            sections = (0,_smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__.splitEvery)(value, \",\", 2);\n                        }\n                        else {\n                            sections = (0,_smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__.splitHeader)(value);\n                        }\n                        const list = [];\n                        for (const section of sections) {\n                            list.push(await deserializer.read([headerListValueSchema, { httpHeader: key }], section.trim()));\n                        }\n                        dataObject[memberName] = list;\n                    }\n                    else {\n                        dataObject[memberName] = await deserializer.read(memberSchema, value);\n                    }\n                }\n            }\n            else if (memberTraits.httpPrefixHeaders !== undefined) {\n                dataObject[memberName] = {};\n                for (const [header, value] of Object.entries(response.headers)) {\n                    if (header.startsWith(memberTraits.httpPrefixHeaders)) {\n                        dataObject[memberName][header.slice(memberTraits.httpPrefixHeaders.length)] = await deserializer.read([memberSchema.getValueSchema(), { httpHeader: header }], value);\n                    }\n                }\n            }\n            else if (memberTraits.httpResponseCode) {\n                dataObject[memberName] = response.statusCode;\n            }\n            else {\n                nonHttpBindingMembers.push(memberName);\n            }\n        }\n        return nonHttpBindingMembers;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/HttpProtocol.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/RpcProtocol.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/RpcProtocol.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RpcProtocol: () => (/* binding */ RpcProtocol)\n/* harmony export */ });\n/* harmony import */ var _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core/schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/index.js\");\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _collect_stream_body__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./collect-stream-body */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/collect-stream-body.js\");\n/* harmony import */ var _HttpProtocol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./HttpProtocol */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/HttpProtocol.js\");\n\n\n\n\nclass RpcProtocol extends _HttpProtocol__WEBPACK_IMPORTED_MODULE_3__.HttpProtocol {\n    async serializeRequest(operationSchema, input, context) {\n        const serializer = this.serializer;\n        const query = {};\n        const headers = {};\n        const endpoint = await context.endpoint();\n        const ns = _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.of(operationSchema?.input);\n        const schema = ns.getSchema();\n        let payload;\n        const request = new _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__.HttpRequest({\n            protocol: \"\",\n            hostname: \"\",\n            port: undefined,\n            path: \"/\",\n            fragment: undefined,\n            query: query,\n            headers: headers,\n            body: undefined,\n        });\n        if (endpoint) {\n            this.updateServiceEndpoint(request, endpoint);\n            this.setHostPrefix(request, operationSchema, input);\n        }\n        const _input = {\n            ...input,\n        };\n        if (input) {\n            serializer.write(schema, _input);\n            payload = serializer.flush();\n        }\n        request.headers = headers;\n        request.query = query;\n        request.body = payload;\n        request.method = \"POST\";\n        return request;\n    }\n    async deserializeResponse(operationSchema, context, response) {\n        const deserializer = this.deserializer;\n        const ns = _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.of(operationSchema.output);\n        const dataObject = {};\n        if (response.statusCode >= 300) {\n            const bytes = await (0,_collect_stream_body__WEBPACK_IMPORTED_MODULE_2__.collectBody)(response.body, context);\n            if (bytes.byteLength > 0) {\n                Object.assign(dataObject, await deserializer.read(_smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.DOCUMENT, bytes));\n            }\n            await this.handleError(operationSchema, context, response, dataObject, this.deserializeMetadata(response));\n            throw new Error(\"@smithy/core/protocols - RPC Protocol error handler failed to throw.\");\n        }\n        for (const header in response.headers) {\n            const value = response.headers[header];\n            delete response.headers[header];\n            response.headers[header.toLowerCase()] = value;\n        }\n        const bytes = await (0,_collect_stream_body__WEBPACK_IMPORTED_MODULE_2__.collectBody)(response.body, context);\n        if (bytes.byteLength > 0) {\n            Object.assign(dataObject, await deserializer.read(ns, bytes));\n        }\n        const output = {\n            $metadata: this.deserializeMetadata(response),\n            ...dataObject,\n        };\n        return output;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/RpcProtocol.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/collect-stream-body.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/collect-stream-body.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   collectBody: () => (/* binding */ collectBody)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-stream */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/index.js\");\n\nconst collectBody = async (streamBody = new Uint8Array(), context) => {\n    if (streamBody instanceof Uint8Array) {\n        return _smithy_util_stream__WEBPACK_IMPORTED_MODULE_0__.Uint8ArrayBlobAdapter.mutate(streamBody);\n    }\n    if (!streamBody) {\n        return _smithy_util_stream__WEBPACK_IMPORTED_MODULE_0__.Uint8ArrayBlobAdapter.mutate(new Uint8Array());\n    }\n    const fromContext = context.streamCollector(streamBody);\n    return _smithy_util_stream__WEBPACK_IMPORTED_MODULE_0__.Uint8ArrayBlobAdapter.mutate(await fromContext);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3Byb3RvY29scy9jb2xsZWN0LXN0cmVhbS1ib2R5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTREO0FBQ3JEO0FBQ1A7QUFDQSxlQUFlLHNFQUFxQjtBQUNwQztBQUNBO0FBQ0EsZUFBZSxzRUFBcUI7QUFDcEM7QUFDQTtBQUNBLFdBQVcsc0VBQXFCO0FBQ2hDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3Byb3RvY29scy9jb2xsZWN0LXN0cmVhbS1ib2R5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFVpbnQ4QXJyYXlCbG9iQWRhcHRlciB9IGZyb20gXCJAc21pdGh5L3V0aWwtc3RyZWFtXCI7XG5leHBvcnQgY29uc3QgY29sbGVjdEJvZHkgPSBhc3luYyAoc3RyZWFtQm9keSA9IG5ldyBVaW50OEFycmF5KCksIGNvbnRleHQpID0+IHtcbiAgICBpZiAoc3RyZWFtQm9keSBpbnN0YW5jZW9mIFVpbnQ4QXJyYXkpIHtcbiAgICAgICAgcmV0dXJuIFVpbnQ4QXJyYXlCbG9iQWRhcHRlci5tdXRhdGUoc3RyZWFtQm9keSk7XG4gICAgfVxuICAgIGlmICghc3RyZWFtQm9keSkge1xuICAgICAgICByZXR1cm4gVWludDhBcnJheUJsb2JBZGFwdGVyLm11dGF0ZShuZXcgVWludDhBcnJheSgpKTtcbiAgICB9XG4gICAgY29uc3QgZnJvbUNvbnRleHQgPSBjb250ZXh0LnN0cmVhbUNvbGxlY3RvcihzdHJlYW1Cb2R5KTtcbiAgICByZXR1cm4gVWludDhBcnJheUJsb2JBZGFwdGVyLm11dGF0ZShhd2FpdCBmcm9tQ29udGV4dCk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/collect-stream-body.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/extended-encode-uri-component.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/extended-encode-uri-component.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extendedEncodeURIComponent: () => (/* binding */ extendedEncodeURIComponent)\n/* harmony export */ });\nfunction extendedEncodeURIComponent(str) {\n    return encodeURIComponent(str).replace(/[!'()*]/g, function (c) {\n        return \"%\" + c.charCodeAt(0).toString(16).toUpperCase();\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3Byb3RvY29scy9leHRlbmRlZC1lbmNvZGUtdXJpLWNvbXBvbmVudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0EsS0FBSztBQUNMIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3Byb3RvY29scy9leHRlbmRlZC1lbmNvZGUtdXJpLWNvbXBvbmVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gZXh0ZW5kZWRFbmNvZGVVUklDb21wb25lbnQoc3RyKSB7XG4gICAgcmV0dXJuIGVuY29kZVVSSUNvbXBvbmVudChzdHIpLnJlcGxhY2UoL1shJygpKl0vZywgZnVuY3Rpb24gKGMpIHtcbiAgICAgICAgcmV0dXJuIFwiJVwiICsgYy5jaGFyQ29kZUF0KDApLnRvU3RyaW5nKDE2KS50b1VwcGVyQ2FzZSgpO1xuICAgIH0pO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/extended-encode-uri-component.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/index.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/index.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FromStringShapeDeserializer: () => (/* reexport safe */ _serde_FromStringShapeDeserializer__WEBPACK_IMPORTED_MODULE_6__.FromStringShapeDeserializer),\n/* harmony export */   HttpBindingProtocol: () => (/* reexport safe */ _HttpBindingProtocol__WEBPACK_IMPORTED_MODULE_2__.HttpBindingProtocol),\n/* harmony export */   HttpInterceptingShapeDeserializer: () => (/* reexport safe */ _serde_HttpInterceptingShapeDeserializer__WEBPACK_IMPORTED_MODULE_7__.HttpInterceptingShapeDeserializer),\n/* harmony export */   HttpInterceptingShapeSerializer: () => (/* reexport safe */ _serde_HttpInterceptingShapeSerializer__WEBPACK_IMPORTED_MODULE_8__.HttpInterceptingShapeSerializer),\n/* harmony export */   RequestBuilder: () => (/* reexport safe */ _requestBuilder__WEBPACK_IMPORTED_MODULE_4__.RequestBuilder),\n/* harmony export */   RpcProtocol: () => (/* reexport safe */ _RpcProtocol__WEBPACK_IMPORTED_MODULE_3__.RpcProtocol),\n/* harmony export */   ToStringShapeSerializer: () => (/* reexport safe */ _serde_ToStringShapeSerializer__WEBPACK_IMPORTED_MODULE_9__.ToStringShapeSerializer),\n/* harmony export */   collectBody: () => (/* reexport safe */ _collect_stream_body__WEBPACK_IMPORTED_MODULE_0__.collectBody),\n/* harmony export */   determineTimestampFormat: () => (/* reexport safe */ _serde_determineTimestampFormat__WEBPACK_IMPORTED_MODULE_10__.determineTimestampFormat),\n/* harmony export */   extendedEncodeURIComponent: () => (/* reexport safe */ _extended_encode_uri_component__WEBPACK_IMPORTED_MODULE_1__.extendedEncodeURIComponent),\n/* harmony export */   requestBuilder: () => (/* reexport safe */ _requestBuilder__WEBPACK_IMPORTED_MODULE_4__.requestBuilder),\n/* harmony export */   resolvedPath: () => (/* reexport safe */ _resolve_path__WEBPACK_IMPORTED_MODULE_5__.resolvedPath)\n/* harmony export */ });\n/* harmony import */ var _collect_stream_body__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./collect-stream-body */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/collect-stream-body.js\");\n/* harmony import */ var _extended_encode_uri_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./extended-encode-uri-component */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/extended-encode-uri-component.js\");\n/* harmony import */ var _HttpBindingProtocol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HttpBindingProtocol */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/HttpBindingProtocol.js\");\n/* harmony import */ var _RpcProtocol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RpcProtocol */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/RpcProtocol.js\");\n/* harmony import */ var _requestBuilder__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./requestBuilder */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/requestBuilder.js\");\n/* harmony import */ var _resolve_path__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./resolve-path */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/resolve-path.js\");\n/* harmony import */ var _serde_FromStringShapeDeserializer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./serde/FromStringShapeDeserializer */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/FromStringShapeDeserializer.js\");\n/* harmony import */ var _serde_HttpInterceptingShapeDeserializer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./serde/HttpInterceptingShapeDeserializer */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/HttpInterceptingShapeDeserializer.js\");\n/* harmony import */ var _serde_HttpInterceptingShapeSerializer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./serde/HttpInterceptingShapeSerializer */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/HttpInterceptingShapeSerializer.js\");\n/* harmony import */ var _serde_ToStringShapeSerializer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./serde/ToStringShapeSerializer */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/ToStringShapeSerializer.js\");\n/* harmony import */ var _serde_determineTimestampFormat__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./serde/determineTimestampFormat */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/determineTimestampFormat.js\");\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3Byb3RvY29scy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFzQztBQUNVO0FBQ1Y7QUFDUjtBQUNHO0FBQ0Y7QUFDcUI7QUFDTTtBQUNGO0FBQ1I7QUFDQyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29yZUAzLjUuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9wcm90b2NvbHMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vY29sbGVjdC1zdHJlYW0tYm9keVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vZXh0ZW5kZWQtZW5jb2RlLXVyaS1jb21wb25lbnRcIjtcbmV4cG9ydCAqIGZyb20gXCIuL0h0dHBCaW5kaW5nUHJvdG9jb2xcIjtcbmV4cG9ydCAqIGZyb20gXCIuL1JwY1Byb3RvY29sXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9yZXF1ZXN0QnVpbGRlclwiO1xuZXhwb3J0ICogZnJvbSBcIi4vcmVzb2x2ZS1wYXRoXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zZXJkZS9Gcm9tU3RyaW5nU2hhcGVEZXNlcmlhbGl6ZXJcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NlcmRlL0h0dHBJbnRlcmNlcHRpbmdTaGFwZURlc2VyaWFsaXplclwiO1xuZXhwb3J0ICogZnJvbSBcIi4vc2VyZGUvSHR0cEludGVyY2VwdGluZ1NoYXBlU2VyaWFsaXplclwiO1xuZXhwb3J0ICogZnJvbSBcIi4vc2VyZGUvVG9TdHJpbmdTaGFwZVNlcmlhbGl6ZXJcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NlcmRlL2RldGVybWluZVRpbWVzdGFtcEZvcm1hdFwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/requestBuilder.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/requestBuilder.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RequestBuilder: () => (/* binding */ RequestBuilder),\n/* harmony export */   requestBuilder: () => (/* binding */ requestBuilder)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _resolve_path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./resolve-path */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/resolve-path.js\");\n\n\nfunction requestBuilder(input, context) {\n    return new RequestBuilder(input, context);\n}\nclass RequestBuilder {\n    constructor(input, context) {\n        this.input = input;\n        this.context = context;\n        this.query = {};\n        this.method = \"\";\n        this.headers = {};\n        this.path = \"\";\n        this.body = null;\n        this.hostname = \"\";\n        this.resolvePathStack = [];\n    }\n    async build() {\n        const { hostname, protocol = \"https\", port, path: basePath } = await this.context.endpoint();\n        this.path = basePath;\n        for (const resolvePath of this.resolvePathStack) {\n            resolvePath(this.path);\n        }\n        return new _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest({\n            protocol,\n            hostname: this.hostname || hostname,\n            port,\n            method: this.method,\n            path: this.path,\n            query: this.query,\n            body: this.body,\n            headers: this.headers,\n        });\n    }\n    hn(hostname) {\n        this.hostname = hostname;\n        return this;\n    }\n    bp(uriLabel) {\n        this.resolvePathStack.push((basePath) => {\n            this.path = `${basePath?.endsWith(\"/\") ? basePath.slice(0, -1) : basePath || \"\"}` + uriLabel;\n        });\n        return this;\n    }\n    p(memberName, labelValueProvider, uriLabel, isGreedyLabel) {\n        this.resolvePathStack.push((path) => {\n            this.path = (0,_resolve_path__WEBPACK_IMPORTED_MODULE_1__.resolvedPath)(path, this.input, memberName, labelValueProvider, uriLabel, isGreedyLabel);\n        });\n        return this;\n    }\n    h(headers) {\n        this.headers = headers;\n        return this;\n    }\n    q(query) {\n        this.query = query;\n        return this;\n    }\n    b(body) {\n        this.body = body;\n        return this;\n    }\n    m(method) {\n        this.method = method;\n        return this;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/requestBuilder.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/resolve-path.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/resolve-path.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolvedPath: () => (/* binding */ resolvedPath)\n/* harmony export */ });\n/* harmony import */ var _extended_encode_uri_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./extended-encode-uri-component */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/extended-encode-uri-component.js\");\n\nconst resolvedPath = (resolvedPath, input, memberName, labelValueProvider, uriLabel, isGreedyLabel) => {\n    if (input != null && input[memberName] !== undefined) {\n        const labelValue = labelValueProvider();\n        if (labelValue.length <= 0) {\n            throw new Error(\"Empty value provided for input HTTP label: \" + memberName + \".\");\n        }\n        resolvedPath = resolvedPath.replace(uriLabel, isGreedyLabel\n            ? labelValue\n                .split(\"/\")\n                .map((segment) => (0,_extended_encode_uri_component__WEBPACK_IMPORTED_MODULE_0__.extendedEncodeURIComponent)(segment))\n                .join(\"/\")\n            : (0,_extended_encode_uri_component__WEBPACK_IMPORTED_MODULE_0__.extendedEncodeURIComponent)(labelValue));\n    }\n    else {\n        throw new Error(\"No value provided for input HTTP label: \" + memberName + \".\");\n    }\n    return resolvedPath;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3Byb3RvY29scy9yZXNvbHZlLXBhdGguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkU7QUFDdEU7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLDBGQUEwQjtBQUM1RDtBQUNBLGNBQWMsMEZBQTBCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29yZUAzLjUuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9wcm90b2NvbHMvcmVzb2x2ZS1wYXRoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGV4dGVuZGVkRW5jb2RlVVJJQ29tcG9uZW50IH0gZnJvbSBcIi4vZXh0ZW5kZWQtZW5jb2RlLXVyaS1jb21wb25lbnRcIjtcbmV4cG9ydCBjb25zdCByZXNvbHZlZFBhdGggPSAocmVzb2x2ZWRQYXRoLCBpbnB1dCwgbWVtYmVyTmFtZSwgbGFiZWxWYWx1ZVByb3ZpZGVyLCB1cmlMYWJlbCwgaXNHcmVlZHlMYWJlbCkgPT4ge1xuICAgIGlmIChpbnB1dCAhPSBudWxsICYmIGlucHV0W21lbWJlck5hbWVdICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgY29uc3QgbGFiZWxWYWx1ZSA9IGxhYmVsVmFsdWVQcm92aWRlcigpO1xuICAgICAgICBpZiAobGFiZWxWYWx1ZS5sZW5ndGggPD0gMCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRW1wdHkgdmFsdWUgcHJvdmlkZWQgZm9yIGlucHV0IEhUVFAgbGFiZWw6IFwiICsgbWVtYmVyTmFtZSArIFwiLlwiKTtcbiAgICAgICAgfVxuICAgICAgICByZXNvbHZlZFBhdGggPSByZXNvbHZlZFBhdGgucmVwbGFjZSh1cmlMYWJlbCwgaXNHcmVlZHlMYWJlbFxuICAgICAgICAgICAgPyBsYWJlbFZhbHVlXG4gICAgICAgICAgICAgICAgLnNwbGl0KFwiL1wiKVxuICAgICAgICAgICAgICAgIC5tYXAoKHNlZ21lbnQpID0+IGV4dGVuZGVkRW5jb2RlVVJJQ29tcG9uZW50KHNlZ21lbnQpKVxuICAgICAgICAgICAgICAgIC5qb2luKFwiL1wiKVxuICAgICAgICAgICAgOiBleHRlbmRlZEVuY29kZVVSSUNvbXBvbmVudChsYWJlbFZhbHVlKSk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJObyB2YWx1ZSBwcm92aWRlZCBmb3IgaW5wdXQgSFRUUCBsYWJlbDogXCIgKyBtZW1iZXJOYW1lICsgXCIuXCIpO1xuICAgIH1cbiAgICByZXR1cm4gcmVzb2x2ZWRQYXRoO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/resolve-path.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/FromStringShapeDeserializer.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/FromStringShapeDeserializer.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FromStringShapeDeserializer: () => (/* binding */ FromStringShapeDeserializer)\n/* harmony export */ });\n/* harmony import */ var _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core/schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/index.js\");\n/* harmony import */ var _smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/core/serde */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/index.js\");\n/* harmony import */ var _smithy_util_base64__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/util-base64 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n/* harmony import */ var _determineTimestampFormat__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./determineTimestampFormat */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/determineTimestampFormat.js\");\n\n\n\n\n\nclass FromStringShapeDeserializer {\n    constructor(settings) {\n        this.settings = settings;\n    }\n    setSerdeContext(serdeContext) {\n        this.serdeContext = serdeContext;\n    }\n    read(_schema, data) {\n        const ns = _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.of(_schema);\n        if (ns.isListSchema()) {\n            return (0,_smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__.splitHeader)(data).map((item) => this.read(ns.getValueSchema(), item));\n        }\n        if (ns.isBlobSchema()) {\n            return (this.serdeContext?.base64Decoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_2__.fromBase64)(data);\n        }\n        if (ns.isTimestampSchema()) {\n            const format = (0,_determineTimestampFormat__WEBPACK_IMPORTED_MODULE_4__.determineTimestampFormat)(ns, this.settings);\n            switch (format) {\n                case _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.TIMESTAMP_DATE_TIME:\n                    return (0,_smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__.parseRfc3339DateTimeWithOffset)(data);\n                case _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.TIMESTAMP_HTTP_DATE:\n                    return (0,_smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__.parseRfc7231DateTime)(data);\n                case _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.TIMESTAMP_EPOCH_SECONDS:\n                    return (0,_smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__.parseEpochTimestamp)(data);\n                default:\n                    console.warn(\"Missing timestamp format, parsing value with Date constructor:\", data);\n                    return new Date(data);\n            }\n        }\n        if (ns.isStringSchema()) {\n            const mediaType = ns.getMergedTraits().mediaType;\n            let intermediateValue = data;\n            if (mediaType) {\n                if (ns.getMergedTraits().httpHeader) {\n                    intermediateValue = this.base64ToUtf8(intermediateValue);\n                }\n                const isJson = mediaType === \"application/json\" || mediaType.endsWith(\"+json\");\n                if (isJson) {\n                    intermediateValue = _smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__.LazyJsonString.from(intermediateValue);\n                }\n                return intermediateValue;\n            }\n        }\n        switch (true) {\n            case ns.isNumericSchema():\n                return Number(data);\n            case ns.isBigIntegerSchema():\n                return BigInt(data);\n            case ns.isBigDecimalSchema():\n                return new _smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__.NumericValue(data, \"bigDecimal\");\n            case ns.isBooleanSchema():\n                return String(data).toLowerCase() === \"true\";\n        }\n        return data;\n    }\n    base64ToUtf8(base64String) {\n        return (this.serdeContext?.utf8Encoder ?? _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_3__.toUtf8)((this.serdeContext?.base64Decoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_2__.fromBase64)(base64String));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/FromStringShapeDeserializer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/HttpInterceptingShapeDeserializer.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/HttpInterceptingShapeDeserializer.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpInterceptingShapeDeserializer: () => (/* binding */ HttpInterceptingShapeDeserializer)\n/* harmony export */ });\n/* harmony import */ var _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core/schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n/* harmony import */ var _FromStringShapeDeserializer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FromStringShapeDeserializer */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/FromStringShapeDeserializer.js\");\n\n\n\nclass HttpInterceptingShapeDeserializer {\n    constructor(codecDeserializer, codecSettings) {\n        this.codecDeserializer = codecDeserializer;\n        this.stringDeserializer = new _FromStringShapeDeserializer__WEBPACK_IMPORTED_MODULE_2__.FromStringShapeDeserializer(codecSettings);\n    }\n    setSerdeContext(serdeContext) {\n        this.stringDeserializer.setSerdeContext(serdeContext);\n        this.codecDeserializer.setSerdeContext(serdeContext);\n        this.serdeContext = serdeContext;\n    }\n    read(schema, data) {\n        const ns = _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.of(schema);\n        const traits = ns.getMergedTraits();\n        const toString = this.serdeContext?.utf8Encoder ?? _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__.toUtf8;\n        if (traits.httpHeader || traits.httpResponseCode) {\n            return this.stringDeserializer.read(ns, toString(data));\n        }\n        if (traits.httpPayload) {\n            if (ns.isBlobSchema()) {\n                const toBytes = this.serdeContext?.utf8Decoder ?? _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__.fromUtf8;\n                if (typeof data === \"string\") {\n                    return toBytes(data);\n                }\n                return data;\n            }\n            else if (ns.isStringSchema()) {\n                if (\"byteLength\" in data) {\n                    return toString(data);\n                }\n                return data;\n            }\n        }\n        return this.codecDeserializer.read(ns, data);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/HttpInterceptingShapeDeserializer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/HttpInterceptingShapeSerializer.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/HttpInterceptingShapeSerializer.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpInterceptingShapeSerializer: () => (/* binding */ HttpInterceptingShapeSerializer)\n/* harmony export */ });\n/* harmony import */ var _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core/schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/index.js\");\n/* harmony import */ var _ToStringShapeSerializer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ToStringShapeSerializer */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/ToStringShapeSerializer.js\");\n\n\nclass HttpInterceptingShapeSerializer {\n    constructor(codecSerializer, codecSettings, stringSerializer = new _ToStringShapeSerializer__WEBPACK_IMPORTED_MODULE_1__.ToStringShapeSerializer(codecSettings)) {\n        this.codecSerializer = codecSerializer;\n        this.stringSerializer = stringSerializer;\n    }\n    setSerdeContext(serdeContext) {\n        this.codecSerializer.setSerdeContext(serdeContext);\n        this.stringSerializer.setSerdeContext(serdeContext);\n    }\n    write(schema, value) {\n        const ns = _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.of(schema);\n        const traits = ns.getMergedTraits();\n        if (traits.httpHeader || traits.httpLabel || traits.httpQuery) {\n            this.stringSerializer.write(ns, value);\n            this.buffer = this.stringSerializer.flush();\n            return;\n        }\n        return this.codecSerializer.write(ns, value);\n    }\n    flush() {\n        if (this.buffer !== undefined) {\n            const buffer = this.buffer;\n            this.buffer = undefined;\n            return buffer;\n        }\n        return this.codecSerializer.flush();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/HttpInterceptingShapeSerializer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/ToStringShapeSerializer.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/ToStringShapeSerializer.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToStringShapeSerializer: () => (/* binding */ ToStringShapeSerializer)\n/* harmony export */ });\n/* harmony import */ var _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core/schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/index.js\");\n/* harmony import */ var _smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/core/serde */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/index.js\");\n/* harmony import */ var _smithy_util_base64__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/util-base64 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js\");\n/* harmony import */ var _determineTimestampFormat__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./determineTimestampFormat */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/determineTimestampFormat.js\");\n\n\n\n\nclass ToStringShapeSerializer {\n    constructor(settings) {\n        this.settings = settings;\n        this.stringBuffer = \"\";\n        this.serdeContext = undefined;\n    }\n    setSerdeContext(serdeContext) {\n        this.serdeContext = serdeContext;\n    }\n    write(schema, value) {\n        const ns = _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.of(schema);\n        switch (typeof value) {\n            case \"object\":\n                if (value === null) {\n                    this.stringBuffer = \"null\";\n                    return;\n                }\n                if (ns.isTimestampSchema()) {\n                    if (!(value instanceof Date)) {\n                        throw new Error(`@smithy/core/protocols - received non-Date value ${value} when schema expected Date in ${ns.getName(true)}`);\n                    }\n                    const format = (0,_determineTimestampFormat__WEBPACK_IMPORTED_MODULE_3__.determineTimestampFormat)(ns, this.settings);\n                    switch (format) {\n                        case _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.TIMESTAMP_DATE_TIME:\n                            this.stringBuffer = value.toISOString().replace(\".000Z\", \"Z\");\n                            break;\n                        case _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.TIMESTAMP_HTTP_DATE:\n                            this.stringBuffer = (0,_smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__.dateToUtcString)(value);\n                            break;\n                        case _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.TIMESTAMP_EPOCH_SECONDS:\n                            this.stringBuffer = String(value.getTime() / 1000);\n                            break;\n                        default:\n                            console.warn(\"Missing timestamp format, using epoch seconds\", value);\n                            this.stringBuffer = String(value.getTime() / 1000);\n                    }\n                    return;\n                }\n                if (ns.isBlobSchema() && \"byteLength\" in value) {\n                    this.stringBuffer = (this.serdeContext?.base64Encoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_2__.toBase64)(value);\n                    return;\n                }\n                if (ns.isListSchema() && Array.isArray(value)) {\n                    let buffer = \"\";\n                    for (const item of value) {\n                        this.write([ns.getValueSchema(), ns.getMergedTraits()], item);\n                        const headerItem = this.flush();\n                        const serialized = ns.getValueSchema().isTimestampSchema() ? headerItem : (0,_smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__.quoteHeader)(headerItem);\n                        if (buffer !== \"\") {\n                            buffer += \", \";\n                        }\n                        buffer += serialized;\n                    }\n                    this.stringBuffer = buffer;\n                    return;\n                }\n                this.stringBuffer = JSON.stringify(value, null, 2);\n                break;\n            case \"string\":\n                const mediaType = ns.getMergedTraits().mediaType;\n                let intermediateValue = value;\n                if (mediaType) {\n                    const isJson = mediaType === \"application/json\" || mediaType.endsWith(\"+json\");\n                    if (isJson) {\n                        intermediateValue = _smithy_core_serde__WEBPACK_IMPORTED_MODULE_1__.LazyJsonString.from(intermediateValue);\n                    }\n                    if (ns.getMergedTraits().httpHeader) {\n                        this.stringBuffer = (this.serdeContext?.base64Encoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_2__.toBase64)(intermediateValue.toString());\n                        return;\n                    }\n                }\n                this.stringBuffer = value;\n                break;\n            default:\n                this.stringBuffer = String(value);\n        }\n    }\n    flush() {\n        const buffer = this.stringBuffer;\n        this.stringBuffer = \"\";\n        return buffer;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/ToStringShapeSerializer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/determineTimestampFormat.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/determineTimestampFormat.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   determineTimestampFormat: () => (/* binding */ determineTimestampFormat)\n/* harmony export */ });\n/* harmony import */ var _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core/schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/index.js\");\n\nfunction determineTimestampFormat(ns, settings) {\n    if (settings.timestampFormat.useTrait) {\n        if (ns.isTimestampSchema() &&\n            (ns.getSchema() === _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.TIMESTAMP_DATE_TIME ||\n                ns.getSchema() === _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.TIMESTAMP_HTTP_DATE ||\n                ns.getSchema() === _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.TIMESTAMP_EPOCH_SECONDS)) {\n            return ns.getSchema();\n        }\n    }\n    const { httpLabel, httpPrefixHeaders, httpHeader, httpQuery } = ns.getMergedTraits();\n    const bindingFormat = settings.httpBindings\n        ? typeof httpPrefixHeaders === \"string\" || Boolean(httpHeader)\n            ? _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.TIMESTAMP_HTTP_DATE\n            : Boolean(httpQuery) || Boolean(httpLabel)\n                ? _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.SCHEMA.TIMESTAMP_DATE_TIME\n                : undefined\n        : undefined;\n    return bindingFormat ?? settings.timestampFormat.default;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/protocols/serde/determineTimestampFormat.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/TypeRegistry.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/TypeRegistry.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TypeRegistry: () => (/* binding */ TypeRegistry)\n/* harmony export */ });\nclass TypeRegistry {\n    constructor(namespace, schemas = new Map()) {\n        this.namespace = namespace;\n        this.schemas = schemas;\n    }\n    static for(namespace) {\n        if (!TypeRegistry.registries.has(namespace)) {\n            TypeRegistry.registries.set(namespace, new TypeRegistry(namespace));\n        }\n        return TypeRegistry.registries.get(namespace);\n    }\n    register(shapeId, schema) {\n        const qualifiedName = this.normalizeShapeId(shapeId);\n        const registry = TypeRegistry.for(this.getNamespace(shapeId));\n        registry.schemas.set(qualifiedName, schema);\n    }\n    getSchema(shapeId) {\n        const id = this.normalizeShapeId(shapeId);\n        if (!this.schemas.has(id)) {\n            throw new Error(`@smithy/core/schema - schema not found for ${id}`);\n        }\n        return this.schemas.get(id);\n    }\n    getBaseException() {\n        for (const [id, schema] of this.schemas.entries()) {\n            if (id.startsWith(\"smithy.ts.sdk.synthetic.\") && id.endsWith(\"ServiceException\")) {\n                return schema;\n            }\n        }\n        return undefined;\n    }\n    find(predicate) {\n        return [...this.schemas.values()].find(predicate);\n    }\n    destroy() {\n        TypeRegistry.registries.delete(this.namespace);\n        this.schemas.clear();\n    }\n    normalizeShapeId(shapeId) {\n        if (shapeId.includes(\"#\")) {\n            return shapeId;\n        }\n        return this.namespace + \"#\" + shapeId;\n    }\n    getNamespace(shapeId) {\n        return this.normalizeShapeId(shapeId).split(\"#\")[0];\n    }\n}\nTypeRegistry.registries = new Map();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/TypeRegistry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/deref.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/deref.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deref: () => (/* binding */ deref)\n/* harmony export */ });\nconst deref = (schemaRef) => {\n    if (typeof schemaRef === \"function\") {\n        return schemaRef();\n    }\n    return schemaRef;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9kZXJlZi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9kZXJlZi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgZGVyZWYgPSAoc2NoZW1hUmVmKSA9PiB7XG4gICAgaWYgKHR5cGVvZiBzY2hlbWFSZWYgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICByZXR1cm4gc2NoZW1hUmVmKCk7XG4gICAgfVxuICAgIHJldHVybiBzY2hlbWFSZWY7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/deref.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/index.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/index.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorSchema: () => (/* reexport safe */ _schemas_ErrorSchema__WEBPACK_IMPORTED_MODULE_5__.ErrorSchema),\n/* harmony export */   ListSchema: () => (/* reexport safe */ _schemas_ListSchema__WEBPACK_IMPORTED_MODULE_2__.ListSchema),\n/* harmony export */   MapSchema: () => (/* reexport safe */ _schemas_MapSchema__WEBPACK_IMPORTED_MODULE_3__.MapSchema),\n/* harmony export */   NormalizedSchema: () => (/* reexport safe */ _schemas_NormalizedSchema__WEBPACK_IMPORTED_MODULE_6__.NormalizedSchema),\n/* harmony export */   OperationSchema: () => (/* reexport safe */ _schemas_OperationSchema__WEBPACK_IMPORTED_MODULE_4__.OperationSchema),\n/* harmony export */   SCHEMA: () => (/* reexport safe */ _schemas_sentinels__WEBPACK_IMPORTED_MODULE_10__.SCHEMA),\n/* harmony export */   Schema: () => (/* reexport safe */ _schemas_Schema__WEBPACK_IMPORTED_MODULE_7__.Schema),\n/* harmony export */   SimpleSchema: () => (/* reexport safe */ _schemas_SimpleSchema__WEBPACK_IMPORTED_MODULE_8__.SimpleSchema),\n/* harmony export */   StructureSchema: () => (/* reexport safe */ _schemas_StructureSchema__WEBPACK_IMPORTED_MODULE_9__.StructureSchema),\n/* harmony export */   TypeRegistry: () => (/* reexport safe */ _TypeRegistry__WEBPACK_IMPORTED_MODULE_11__.TypeRegistry),\n/* harmony export */   deref: () => (/* reexport safe */ _deref__WEBPACK_IMPORTED_MODULE_0__.deref),\n/* harmony export */   deserializerMiddlewareOption: () => (/* reexport safe */ _middleware_getSchemaSerdePlugin__WEBPACK_IMPORTED_MODULE_1__.deserializerMiddlewareOption),\n/* harmony export */   error: () => (/* reexport safe */ _schemas_ErrorSchema__WEBPACK_IMPORTED_MODULE_5__.error),\n/* harmony export */   getSchemaSerdePlugin: () => (/* reexport safe */ _middleware_getSchemaSerdePlugin__WEBPACK_IMPORTED_MODULE_1__.getSchemaSerdePlugin),\n/* harmony export */   list: () => (/* reexport safe */ _schemas_ListSchema__WEBPACK_IMPORTED_MODULE_2__.list),\n/* harmony export */   map: () => (/* reexport safe */ _schemas_MapSchema__WEBPACK_IMPORTED_MODULE_3__.map),\n/* harmony export */   op: () => (/* reexport safe */ _schemas_OperationSchema__WEBPACK_IMPORTED_MODULE_4__.op),\n/* harmony export */   serializerMiddlewareOption: () => (/* reexport safe */ _middleware_getSchemaSerdePlugin__WEBPACK_IMPORTED_MODULE_1__.serializerMiddlewareOption),\n/* harmony export */   sim: () => (/* reexport safe */ _schemas_SimpleSchema__WEBPACK_IMPORTED_MODULE_8__.sim),\n/* harmony export */   struct: () => (/* reexport safe */ _schemas_StructureSchema__WEBPACK_IMPORTED_MODULE_9__.struct)\n/* harmony export */ });\n/* harmony import */ var _deref__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deref */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/deref.js\");\n/* harmony import */ var _middleware_getSchemaSerdePlugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./middleware/getSchemaSerdePlugin */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/middleware/getSchemaSerdePlugin.js\");\n/* harmony import */ var _schemas_ListSchema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./schemas/ListSchema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/ListSchema.js\");\n/* harmony import */ var _schemas_MapSchema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./schemas/MapSchema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/MapSchema.js\");\n/* harmony import */ var _schemas_OperationSchema__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./schemas/OperationSchema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/OperationSchema.js\");\n/* harmony import */ var _schemas_ErrorSchema__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./schemas/ErrorSchema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/ErrorSchema.js\");\n/* harmony import */ var _schemas_NormalizedSchema__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./schemas/NormalizedSchema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/NormalizedSchema.js\");\n/* harmony import */ var _schemas_Schema__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./schemas/Schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/Schema.js\");\n/* harmony import */ var _schemas_SimpleSchema__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./schemas/SimpleSchema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/SimpleSchema.js\");\n/* harmony import */ var _schemas_StructureSchema__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./schemas/StructureSchema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/StructureSchema.js\");\n/* harmony import */ var _schemas_sentinels__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./schemas/sentinels */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/sentinels.js\");\n/* harmony import */ var _TypeRegistry__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./TypeRegistry */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/TypeRegistry.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF3QjtBQUMwQjtBQUNiO0FBQ0Q7QUFDTTtBQUNKO0FBQ0s7QUFDVjtBQUNNO0FBQ0c7QUFDTjtBQUNMIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9kZXJlZlwiO1xuZXhwb3J0ICogZnJvbSBcIi4vbWlkZGxld2FyZS9nZXRTY2hlbWFTZXJkZVBsdWdpblwiO1xuZXhwb3J0ICogZnJvbSBcIi4vc2NoZW1hcy9MaXN0U2NoZW1hXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zY2hlbWFzL01hcFNjaGVtYVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vc2NoZW1hcy9PcGVyYXRpb25TY2hlbWFcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NjaGVtYXMvRXJyb3JTY2hlbWFcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NjaGVtYXMvTm9ybWFsaXplZFNjaGVtYVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vc2NoZW1hcy9TY2hlbWFcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NjaGVtYXMvU2ltcGxlU2NoZW1hXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zY2hlbWFzL1N0cnVjdHVyZVNjaGVtYVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vc2NoZW1hcy9zZW50aW5lbHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL1R5cGVSZWdpc3RyeVwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/middleware/getSchemaSerdePlugin.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/middleware/getSchemaSerdePlugin.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserializerMiddlewareOption: () => (/* binding */ deserializerMiddlewareOption),\n/* harmony export */   getSchemaSerdePlugin: () => (/* binding */ getSchemaSerdePlugin),\n/* harmony export */   serializerMiddlewareOption: () => (/* binding */ serializerMiddlewareOption)\n/* harmony export */ });\n/* harmony import */ var _schemaDeserializationMiddleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schemaDeserializationMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/middleware/schemaDeserializationMiddleware.js\");\n/* harmony import */ var _schemaSerializationMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schemaSerializationMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/middleware/schemaSerializationMiddleware.js\");\n\n\nconst deserializerMiddlewareOption = {\n    name: \"deserializerMiddleware\",\n    step: \"deserialize\",\n    tags: [\"DESERIALIZER\"],\n    override: true,\n};\nconst serializerMiddlewareOption = {\n    name: \"serializerMiddleware\",\n    step: \"serialize\",\n    tags: [\"SERIALIZER\"],\n    override: true,\n};\nfunction getSchemaSerdePlugin(config) {\n    return {\n        applyToStack: (commandStack) => {\n            commandStack.add((0,_schemaSerializationMiddleware__WEBPACK_IMPORTED_MODULE_1__.schemaSerializationMiddleware)(config), serializerMiddlewareOption);\n            commandStack.add((0,_schemaDeserializationMiddleware__WEBPACK_IMPORTED_MODULE_0__.schemaDeserializationMiddleware)(config), deserializerMiddlewareOption);\n            config.protocol.setSerdeContext(config);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9taWRkbGV3YXJlL2dldFNjaGVtYVNlcmRlUGx1Z2luLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW9GO0FBQ0o7QUFDekU7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0EsNkJBQTZCLDZGQUE2QjtBQUMxRCw2QkFBNkIsaUdBQStCO0FBQzVEO0FBQ0EsU0FBUztBQUNUO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvcmVAMy41LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29yZS9kaXN0LWVzL3N1Ym1vZHVsZXMvc2NoZW1hL21pZGRsZXdhcmUvZ2V0U2NoZW1hU2VyZGVQbHVnaW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc2NoZW1hRGVzZXJpYWxpemF0aW9uTWlkZGxld2FyZSB9IGZyb20gXCIuL3NjaGVtYURlc2VyaWFsaXphdGlvbk1pZGRsZXdhcmVcIjtcbmltcG9ydCB7IHNjaGVtYVNlcmlhbGl6YXRpb25NaWRkbGV3YXJlIH0gZnJvbSBcIi4vc2NoZW1hU2VyaWFsaXphdGlvbk1pZGRsZXdhcmVcIjtcbmV4cG9ydCBjb25zdCBkZXNlcmlhbGl6ZXJNaWRkbGV3YXJlT3B0aW9uID0ge1xuICAgIG5hbWU6IFwiZGVzZXJpYWxpemVyTWlkZGxld2FyZVwiLFxuICAgIHN0ZXA6IFwiZGVzZXJpYWxpemVcIixcbiAgICB0YWdzOiBbXCJERVNFUklBTElaRVJcIl0sXG4gICAgb3ZlcnJpZGU6IHRydWUsXG59O1xuZXhwb3J0IGNvbnN0IHNlcmlhbGl6ZXJNaWRkbGV3YXJlT3B0aW9uID0ge1xuICAgIG5hbWU6IFwic2VyaWFsaXplck1pZGRsZXdhcmVcIixcbiAgICBzdGVwOiBcInNlcmlhbGl6ZVwiLFxuICAgIHRhZ3M6IFtcIlNFUklBTElaRVJcIl0sXG4gICAgb3ZlcnJpZGU6IHRydWUsXG59O1xuZXhwb3J0IGZ1bmN0aW9uIGdldFNjaGVtYVNlcmRlUGx1Z2luKGNvbmZpZykge1xuICAgIHJldHVybiB7XG4gICAgICAgIGFwcGx5VG9TdGFjazogKGNvbW1hbmRTdGFjaykgPT4ge1xuICAgICAgICAgICAgY29tbWFuZFN0YWNrLmFkZChzY2hlbWFTZXJpYWxpemF0aW9uTWlkZGxld2FyZShjb25maWcpLCBzZXJpYWxpemVyTWlkZGxld2FyZU9wdGlvbik7XG4gICAgICAgICAgICBjb21tYW5kU3RhY2suYWRkKHNjaGVtYURlc2VyaWFsaXphdGlvbk1pZGRsZXdhcmUoY29uZmlnKSwgZGVzZXJpYWxpemVyTWlkZGxld2FyZU9wdGlvbik7XG4gICAgICAgICAgICBjb25maWcucHJvdG9jb2wuc2V0U2VyZGVDb250ZXh0KGNvbmZpZyk7XG4gICAgICAgIH0sXG4gICAgfTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/middleware/getSchemaSerdePlugin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/middleware/schemaDeserializationMiddleware.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/middleware/schemaDeserializationMiddleware.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   schemaDeserializationMiddleware: () => (/* binding */ schemaDeserializationMiddleware)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n\n\nconst schemaDeserializationMiddleware = (config) => (next, context) => async (args) => {\n    const { response } = await next(args);\n    const { operationSchema } = (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_1__.getSmithyContext)(context);\n    try {\n        const parsed = await config.protocol.deserializeResponse(operationSchema, {\n            ...config,\n            ...context,\n        }, response);\n        return {\n            response,\n            output: parsed,\n        };\n    }\n    catch (error) {\n        Object.defineProperty(error, \"$response\", {\n            value: response,\n        });\n        if (!(\"$metadata\" in error)) {\n            const hint = `Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.`;\n            try {\n                error.message += \"\\n  \" + hint;\n            }\n            catch (e) {\n                if (!context.logger || context.logger?.constructor?.name === \"NoOpLogger\") {\n                    console.warn(hint);\n                }\n                else {\n                    context.logger?.warn?.(hint);\n                }\n            }\n            if (typeof error.$responseBodyText !== \"undefined\") {\n                if (error.$response) {\n                    error.$response.body = error.$responseBodyText;\n                }\n            }\n            try {\n                if (_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpResponse.isInstance(response)) {\n                    const { headers = {} } = response;\n                    const headerEntries = Object.entries(headers);\n                    error.$metadata = {\n                        httpStatusCode: response.statusCode,\n                        requestId: findHeader(/^x-[\\w-]+-request-?id$/, headerEntries),\n                        extendedRequestId: findHeader(/^x-[\\w-]+-id-2$/, headerEntries),\n                        cfId: findHeader(/^x-[\\w-]+-cf-id$/, headerEntries),\n                    };\n                }\n            }\n            catch (e) {\n            }\n        }\n        throw error;\n    }\n};\nconst findHeader = (pattern, headers) => {\n    return (headers.find(([k]) => {\n        return k.match(pattern);\n    }) || [void 0, void 1])[1];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/middleware/schemaDeserializationMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/middleware/schemaSerializationMiddleware.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/middleware/schemaSerializationMiddleware.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   schemaSerializationMiddleware: () => (/* binding */ schemaSerializationMiddleware)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n\nconst schemaSerializationMiddleware = (config) => (next, context) => async (args) => {\n    const { operationSchema } = (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.getSmithyContext)(context);\n    const endpoint = context.endpointV2?.url && config.urlParser\n        ? async () => config.urlParser(context.endpointV2.url)\n        : config.endpoint;\n    const request = await config.protocol.serializeRequest(operationSchema, args.input, {\n        ...config,\n        ...context,\n        endpoint,\n    });\n    return next({\n        ...args,\n        request,\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9taWRkbGV3YXJlL3NjaGVtYVNlcmlhbGl6YXRpb25NaWRkbGV3YXJlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJEO0FBQ3BEO0FBQ1AsWUFBWSxrQkFBa0IsRUFBRSx5RUFBZ0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9taWRkbGV3YXJlL3NjaGVtYVNlcmlhbGl6YXRpb25NaWRkbGV3YXJlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdldFNtaXRoeUNvbnRleHQgfSBmcm9tIFwiQHNtaXRoeS91dGlsLW1pZGRsZXdhcmVcIjtcbmV4cG9ydCBjb25zdCBzY2hlbWFTZXJpYWxpemF0aW9uTWlkZGxld2FyZSA9IChjb25maWcpID0+IChuZXh0LCBjb250ZXh0KSA9PiBhc3luYyAoYXJncykgPT4ge1xuICAgIGNvbnN0IHsgb3BlcmF0aW9uU2NoZW1hIH0gPSBnZXRTbWl0aHlDb250ZXh0KGNvbnRleHQpO1xuICAgIGNvbnN0IGVuZHBvaW50ID0gY29udGV4dC5lbmRwb2ludFYyPy51cmwgJiYgY29uZmlnLnVybFBhcnNlclxuICAgICAgICA/IGFzeW5jICgpID0+IGNvbmZpZy51cmxQYXJzZXIoY29udGV4dC5lbmRwb2ludFYyLnVybClcbiAgICAgICAgOiBjb25maWcuZW5kcG9pbnQ7XG4gICAgY29uc3QgcmVxdWVzdCA9IGF3YWl0IGNvbmZpZy5wcm90b2NvbC5zZXJpYWxpemVSZXF1ZXN0KG9wZXJhdGlvblNjaGVtYSwgYXJncy5pbnB1dCwge1xuICAgICAgICAuLi5jb25maWcsXG4gICAgICAgIC4uLmNvbnRleHQsXG4gICAgICAgIGVuZHBvaW50LFxuICAgIH0pO1xuICAgIHJldHVybiBuZXh0KHtcbiAgICAgICAgLi4uYXJncyxcbiAgICAgICAgcmVxdWVzdCxcbiAgICB9KTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/middleware/schemaSerializationMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/ErrorSchema.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/ErrorSchema.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorSchema: () => (/* binding */ ErrorSchema),\n/* harmony export */   error: () => (/* binding */ error)\n/* harmony export */ });\n/* harmony import */ var _TypeRegistry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../TypeRegistry */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/TypeRegistry.js\");\n/* harmony import */ var _StructureSchema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./StructureSchema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/StructureSchema.js\");\n\n\nclass ErrorSchema extends _StructureSchema__WEBPACK_IMPORTED_MODULE_1__.StructureSchema {\n    constructor(name, traits, memberNames, memberList, ctor) {\n        super(name, traits, memberNames, memberList);\n        this.name = name;\n        this.traits = traits;\n        this.memberNames = memberNames;\n        this.memberList = memberList;\n        this.ctor = ctor;\n    }\n}\nfunction error(namespace, name, traits = {}, memberNames, memberList, ctor) {\n    const schema = new ErrorSchema(namespace + \"#\" + name, traits, memberNames, memberList, ctor);\n    _TypeRegistry__WEBPACK_IMPORTED_MODULE_0__.TypeRegistry.for(namespace).register(name, schema);\n    return schema;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9zY2hlbWFzL0Vycm9yU2NoZW1hLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBK0M7QUFDSztBQUM3QywwQkFBMEIsNkRBQWU7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08sMkNBQTJDO0FBQ2xEO0FBQ0EsSUFBSSx1REFBWTtBQUNoQjtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9zY2hlbWFzL0Vycm9yU2NoZW1hLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFR5cGVSZWdpc3RyeSB9IGZyb20gXCIuLi9UeXBlUmVnaXN0cnlcIjtcbmltcG9ydCB7IFN0cnVjdHVyZVNjaGVtYSB9IGZyb20gXCIuL1N0cnVjdHVyZVNjaGVtYVwiO1xuZXhwb3J0IGNsYXNzIEVycm9yU2NoZW1hIGV4dGVuZHMgU3RydWN0dXJlU2NoZW1hIHtcbiAgICBjb25zdHJ1Y3RvcihuYW1lLCB0cmFpdHMsIG1lbWJlck5hbWVzLCBtZW1iZXJMaXN0LCBjdG9yKSB7XG4gICAgICAgIHN1cGVyKG5hbWUsIHRyYWl0cywgbWVtYmVyTmFtZXMsIG1lbWJlckxpc3QpO1xuICAgICAgICB0aGlzLm5hbWUgPSBuYW1lO1xuICAgICAgICB0aGlzLnRyYWl0cyA9IHRyYWl0cztcbiAgICAgICAgdGhpcy5tZW1iZXJOYW1lcyA9IG1lbWJlck5hbWVzO1xuICAgICAgICB0aGlzLm1lbWJlckxpc3QgPSBtZW1iZXJMaXN0O1xuICAgICAgICB0aGlzLmN0b3IgPSBjdG9yO1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBlcnJvcihuYW1lc3BhY2UsIG5hbWUsIHRyYWl0cyA9IHt9LCBtZW1iZXJOYW1lcywgbWVtYmVyTGlzdCwgY3Rvcikge1xuICAgIGNvbnN0IHNjaGVtYSA9IG5ldyBFcnJvclNjaGVtYShuYW1lc3BhY2UgKyBcIiNcIiArIG5hbWUsIHRyYWl0cywgbWVtYmVyTmFtZXMsIG1lbWJlckxpc3QsIGN0b3IpO1xuICAgIFR5cGVSZWdpc3RyeS5mb3IobmFtZXNwYWNlKS5yZWdpc3RlcihuYW1lLCBzY2hlbWEpO1xuICAgIHJldHVybiBzY2hlbWE7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/ErrorSchema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/ListSchema.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/ListSchema.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListSchema: () => (/* binding */ ListSchema),\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/* harmony import */ var _TypeRegistry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../TypeRegistry */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/TypeRegistry.js\");\n/* harmony import */ var _Schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/Schema.js\");\n\n\nclass ListSchema extends _Schema__WEBPACK_IMPORTED_MODULE_1__.Schema {\n    constructor(name, traits, valueSchema) {\n        super(name, traits);\n        this.name = name;\n        this.traits = traits;\n        this.valueSchema = valueSchema;\n    }\n}\nfunction list(namespace, name, traits = {}, valueSchema) {\n    const schema = new ListSchema(namespace + \"#\" + name, traits, typeof valueSchema === \"function\" ? valueSchema() : valueSchema);\n    _TypeRegistry__WEBPACK_IMPORTED_MODULE_0__.TypeRegistry.for(namespace).register(name, schema);\n    return schema;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9zY2hlbWFzL0xpc3RTY2hlbWEuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUErQztBQUNiO0FBQzNCLHlCQUF5QiwyQ0FBTTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLDBDQUEwQztBQUNqRDtBQUNBLElBQUksdURBQVk7QUFDaEI7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29yZUAzLjUuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9zY2hlbWEvc2NoZW1hcy9MaXN0U2NoZW1hLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFR5cGVSZWdpc3RyeSB9IGZyb20gXCIuLi9UeXBlUmVnaXN0cnlcIjtcbmltcG9ydCB7IFNjaGVtYSB9IGZyb20gXCIuL1NjaGVtYVwiO1xuZXhwb3J0IGNsYXNzIExpc3RTY2hlbWEgZXh0ZW5kcyBTY2hlbWEge1xuICAgIGNvbnN0cnVjdG9yKG5hbWUsIHRyYWl0cywgdmFsdWVTY2hlbWEpIHtcbiAgICAgICAgc3VwZXIobmFtZSwgdHJhaXRzKTtcbiAgICAgICAgdGhpcy5uYW1lID0gbmFtZTtcbiAgICAgICAgdGhpcy50cmFpdHMgPSB0cmFpdHM7XG4gICAgICAgIHRoaXMudmFsdWVTY2hlbWEgPSB2YWx1ZVNjaGVtYTtcbiAgICB9XG59XG5leHBvcnQgZnVuY3Rpb24gbGlzdChuYW1lc3BhY2UsIG5hbWUsIHRyYWl0cyA9IHt9LCB2YWx1ZVNjaGVtYSkge1xuICAgIGNvbnN0IHNjaGVtYSA9IG5ldyBMaXN0U2NoZW1hKG5hbWVzcGFjZSArIFwiI1wiICsgbmFtZSwgdHJhaXRzLCB0eXBlb2YgdmFsdWVTY2hlbWEgPT09IFwiZnVuY3Rpb25cIiA/IHZhbHVlU2NoZW1hKCkgOiB2YWx1ZVNjaGVtYSk7XG4gICAgVHlwZVJlZ2lzdHJ5LmZvcihuYW1lc3BhY2UpLnJlZ2lzdGVyKG5hbWUsIHNjaGVtYSk7XG4gICAgcmV0dXJuIHNjaGVtYTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/ListSchema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/MapSchema.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/MapSchema.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MapSchema: () => (/* binding */ MapSchema),\n/* harmony export */   map: () => (/* binding */ map)\n/* harmony export */ });\n/* harmony import */ var _TypeRegistry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../TypeRegistry */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/TypeRegistry.js\");\n/* harmony import */ var _Schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/Schema.js\");\n\n\nclass MapSchema extends _Schema__WEBPACK_IMPORTED_MODULE_1__.Schema {\n    constructor(name, traits, keySchema, valueSchema) {\n        super(name, traits);\n        this.name = name;\n        this.traits = traits;\n        this.keySchema = keySchema;\n        this.valueSchema = valueSchema;\n    }\n}\nfunction map(namespace, name, traits = {}, keySchema, valueSchema) {\n    const schema = new MapSchema(namespace + \"#\" + name, traits, keySchema, typeof valueSchema === \"function\" ? valueSchema() : valueSchema);\n    _TypeRegistry__WEBPACK_IMPORTED_MODULE_0__.TypeRegistry.for(namespace).register(name, schema);\n    return schema;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9zY2hlbWFzL01hcFNjaGVtYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStDO0FBQ2I7QUFDM0Isd0JBQXdCLDJDQUFNO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyx5Q0FBeUM7QUFDaEQ7QUFDQSxJQUFJLHVEQUFZO0FBQ2hCO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvcmVAMy41LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29yZS9kaXN0LWVzL3N1Ym1vZHVsZXMvc2NoZW1hL3NjaGVtYXMvTWFwU2NoZW1hLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFR5cGVSZWdpc3RyeSB9IGZyb20gXCIuLi9UeXBlUmVnaXN0cnlcIjtcbmltcG9ydCB7IFNjaGVtYSB9IGZyb20gXCIuL1NjaGVtYVwiO1xuZXhwb3J0IGNsYXNzIE1hcFNjaGVtYSBleHRlbmRzIFNjaGVtYSB7XG4gICAgY29uc3RydWN0b3IobmFtZSwgdHJhaXRzLCBrZXlTY2hlbWEsIHZhbHVlU2NoZW1hKSB7XG4gICAgICAgIHN1cGVyKG5hbWUsIHRyYWl0cyk7XG4gICAgICAgIHRoaXMubmFtZSA9IG5hbWU7XG4gICAgICAgIHRoaXMudHJhaXRzID0gdHJhaXRzO1xuICAgICAgICB0aGlzLmtleVNjaGVtYSA9IGtleVNjaGVtYTtcbiAgICAgICAgdGhpcy52YWx1ZVNjaGVtYSA9IHZhbHVlU2NoZW1hO1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBtYXAobmFtZXNwYWNlLCBuYW1lLCB0cmFpdHMgPSB7fSwga2V5U2NoZW1hLCB2YWx1ZVNjaGVtYSkge1xuICAgIGNvbnN0IHNjaGVtYSA9IG5ldyBNYXBTY2hlbWEobmFtZXNwYWNlICsgXCIjXCIgKyBuYW1lLCB0cmFpdHMsIGtleVNjaGVtYSwgdHlwZW9mIHZhbHVlU2NoZW1hID09PSBcImZ1bmN0aW9uXCIgPyB2YWx1ZVNjaGVtYSgpIDogdmFsdWVTY2hlbWEpO1xuICAgIFR5cGVSZWdpc3RyeS5mb3IobmFtZXNwYWNlKS5yZWdpc3RlcihuYW1lLCBzY2hlbWEpO1xuICAgIHJldHVybiBzY2hlbWE7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/MapSchema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/NormalizedSchema.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/NormalizedSchema.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NormalizedSchema: () => (/* binding */ NormalizedSchema)\n/* harmony export */ });\n/* harmony import */ var _deref__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../deref */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/deref.js\");\n/* harmony import */ var _ListSchema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ListSchema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/ListSchema.js\");\n/* harmony import */ var _MapSchema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MapSchema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/MapSchema.js\");\n/* harmony import */ var _sentinels__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sentinels */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/sentinels.js\");\n/* harmony import */ var _SimpleSchema__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SimpleSchema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/SimpleSchema.js\");\n/* harmony import */ var _StructureSchema__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StructureSchema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/StructureSchema.js\");\n\n\n\n\n\n\nclass NormalizedSchema {\n    constructor(ref, memberName) {\n        this.ref = ref;\n        this.memberName = memberName;\n        const traitStack = [];\n        let _ref = ref;\n        let schema = ref;\n        this._isMemberSchema = false;\n        while (Array.isArray(_ref)) {\n            traitStack.push(_ref[1]);\n            _ref = _ref[0];\n            schema = (0,_deref__WEBPACK_IMPORTED_MODULE_0__.deref)(_ref);\n            this._isMemberSchema = true;\n        }\n        if (traitStack.length > 0) {\n            this.memberTraits = {};\n            for (let i = traitStack.length - 1; i >= 0; --i) {\n                const traitSet = traitStack[i];\n                Object.assign(this.memberTraits, NormalizedSchema.translateTraits(traitSet));\n            }\n        }\n        else {\n            this.memberTraits = 0;\n        }\n        if (schema instanceof NormalizedSchema) {\n            this.name = schema.name;\n            this.traits = schema.traits;\n            this._isMemberSchema = schema._isMemberSchema;\n            this.schema = schema.schema;\n            this.memberTraits = Object.assign({}, schema.getMemberTraits(), this.getMemberTraits());\n            this.normalizedTraits = void 0;\n            this.ref = schema.ref;\n            this.memberName = memberName ?? schema.memberName;\n            return;\n        }\n        this.schema = (0,_deref__WEBPACK_IMPORTED_MODULE_0__.deref)(schema);\n        if (this.schema && typeof this.schema === \"object\") {\n            this.traits = this.schema?.traits ?? {};\n        }\n        else {\n            this.traits = 0;\n        }\n        this.name =\n            (typeof this.schema === \"object\" ? this.schema?.name : void 0) ?? this.memberName ?? this.getSchemaName();\n        if (this._isMemberSchema && !memberName) {\n            throw new Error(`@smithy/core/schema - NormalizedSchema member schema ${this.getName(true)} must initialize with memberName argument.`);\n        }\n    }\n    static of(ref, memberName) {\n        if (ref instanceof NormalizedSchema) {\n            return ref;\n        }\n        return new NormalizedSchema(ref, memberName);\n    }\n    static translateTraits(indicator) {\n        if (typeof indicator === \"object\") {\n            return indicator;\n        }\n        indicator = indicator | 0;\n        const traits = {};\n        if ((indicator & 1) === 1) {\n            traits.httpLabel = 1;\n        }\n        if (((indicator >> 1) & 1) === 1) {\n            traits.idempotent = 1;\n        }\n        if (((indicator >> 2) & 1) === 1) {\n            traits.idempotencyToken = 1;\n        }\n        if (((indicator >> 3) & 1) === 1) {\n            traits.sensitive = 1;\n        }\n        if (((indicator >> 4) & 1) === 1) {\n            traits.httpPayload = 1;\n        }\n        if (((indicator >> 5) & 1) === 1) {\n            traits.httpResponseCode = 1;\n        }\n        if (((indicator >> 6) & 1) === 1) {\n            traits.httpQueryParams = 1;\n        }\n        return traits;\n    }\n    static memberFrom(memberSchema, memberName) {\n        if (memberSchema instanceof NormalizedSchema) {\n            memberSchema.memberName = memberName;\n            memberSchema._isMemberSchema = true;\n            return memberSchema;\n        }\n        return new NormalizedSchema(memberSchema, memberName);\n    }\n    getSchema() {\n        if (this.schema instanceof NormalizedSchema) {\n            return (this.schema = this.schema.getSchema());\n        }\n        if (this.schema instanceof _SimpleSchema__WEBPACK_IMPORTED_MODULE_4__.SimpleSchema) {\n            return (0,_deref__WEBPACK_IMPORTED_MODULE_0__.deref)(this.schema.schemaRef);\n        }\n        return (0,_deref__WEBPACK_IMPORTED_MODULE_0__.deref)(this.schema);\n    }\n    getName(withNamespace = false) {\n        if (!withNamespace) {\n            if (this.name && this.name.includes(\"#\")) {\n                return this.name.split(\"#\")[1];\n            }\n        }\n        return this.name || undefined;\n    }\n    getMemberName() {\n        if (!this.isMemberSchema()) {\n            throw new Error(`@smithy/core/schema - cannot get member name on non-member schema: ${this.getName(true)}`);\n        }\n        return this.memberName;\n    }\n    isMemberSchema() {\n        return this._isMemberSchema;\n    }\n    isUnitSchema() {\n        return this.getSchema() === \"unit\";\n    }\n    isListSchema() {\n        const inner = this.getSchema();\n        if (typeof inner === \"number\") {\n            return inner >= _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.LIST_MODIFIER && inner < _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.MAP_MODIFIER;\n        }\n        return inner instanceof _ListSchema__WEBPACK_IMPORTED_MODULE_1__.ListSchema;\n    }\n    isMapSchema() {\n        const inner = this.getSchema();\n        if (typeof inner === \"number\") {\n            return inner >= _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.MAP_MODIFIER && inner <= 255;\n        }\n        return inner instanceof _MapSchema__WEBPACK_IMPORTED_MODULE_2__.MapSchema;\n    }\n    isDocumentSchema() {\n        return this.getSchema() === _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.DOCUMENT;\n    }\n    isStructSchema() {\n        const inner = this.getSchema();\n        return (inner !== null && typeof inner === \"object\" && \"members\" in inner) || inner instanceof _StructureSchema__WEBPACK_IMPORTED_MODULE_5__.StructureSchema;\n    }\n    isBlobSchema() {\n        return this.getSchema() === _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.BLOB || this.getSchema() === _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.STREAMING_BLOB;\n    }\n    isTimestampSchema() {\n        const schema = this.getSchema();\n        return typeof schema === \"number\" && schema >= _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.TIMESTAMP_DEFAULT && schema <= _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.TIMESTAMP_EPOCH_SECONDS;\n    }\n    isStringSchema() {\n        return this.getSchema() === _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.STRING;\n    }\n    isBooleanSchema() {\n        return this.getSchema() === _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.BOOLEAN;\n    }\n    isNumericSchema() {\n        return this.getSchema() === _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.NUMERIC;\n    }\n    isBigIntegerSchema() {\n        return this.getSchema() === _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.BIG_INTEGER;\n    }\n    isBigDecimalSchema() {\n        return this.getSchema() === _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.BIG_DECIMAL;\n    }\n    isStreaming() {\n        const streaming = !!this.getMergedTraits().streaming;\n        if (streaming) {\n            return true;\n        }\n        return this.getSchema() === _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.STREAMING_BLOB;\n    }\n    getMergedTraits() {\n        if (this.normalizedTraits) {\n            return this.normalizedTraits;\n        }\n        this.normalizedTraits = {\n            ...this.getOwnTraits(),\n            ...this.getMemberTraits(),\n        };\n        return this.normalizedTraits;\n    }\n    getMemberTraits() {\n        return NormalizedSchema.translateTraits(this.memberTraits);\n    }\n    getOwnTraits() {\n        return NormalizedSchema.translateTraits(this.traits);\n    }\n    getKeySchema() {\n        if (this.isDocumentSchema()) {\n            return NormalizedSchema.memberFrom([_sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.DOCUMENT, 0], \"key\");\n        }\n        if (!this.isMapSchema()) {\n            throw new Error(`@smithy/core/schema - cannot get key schema for non-map schema: ${this.getName(true)}`);\n        }\n        const schema = this.getSchema();\n        if (typeof schema === \"number\") {\n            return NormalizedSchema.memberFrom([63 & schema, 0], \"key\");\n        }\n        return NormalizedSchema.memberFrom([schema.keySchema, 0], \"key\");\n    }\n    getValueSchema() {\n        const schema = this.getSchema();\n        if (typeof schema === \"number\") {\n            if (this.isMapSchema()) {\n                return NormalizedSchema.memberFrom([63 & schema, 0], \"value\");\n            }\n            else if (this.isListSchema()) {\n                return NormalizedSchema.memberFrom([63 & schema, 0], \"member\");\n            }\n        }\n        if (schema && typeof schema === \"object\") {\n            if (this.isStructSchema()) {\n                throw new Error(`cannot call getValueSchema() with StructureSchema ${this.getName(true)}`);\n            }\n            const collection = schema;\n            if (\"valueSchema\" in collection) {\n                if (this.isMapSchema()) {\n                    return NormalizedSchema.memberFrom([collection.valueSchema, 0], \"value\");\n                }\n                else if (this.isListSchema()) {\n                    return NormalizedSchema.memberFrom([collection.valueSchema, 0], \"member\");\n                }\n            }\n        }\n        if (this.isDocumentSchema()) {\n            return NormalizedSchema.memberFrom([_sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.DOCUMENT, 0], \"value\");\n        }\n        throw new Error(`@smithy/core/schema - the schema ${this.getName(true)} does not have a value member.`);\n    }\n    getMemberSchema(member) {\n        if (this.isStructSchema()) {\n            const struct = this.getSchema();\n            if (!(member in struct.members)) {\n                throw new Error(`@smithy/core/schema - the schema ${this.getName(true)} does not have a member with name=${member}.`);\n            }\n            return NormalizedSchema.memberFrom(struct.members[member], member);\n        }\n        if (this.isDocumentSchema()) {\n            return NormalizedSchema.memberFrom([_sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.DOCUMENT, 0], member);\n        }\n        throw new Error(`@smithy/core/schema - the schema ${this.getName(true)} does not have members.`);\n    }\n    getMemberSchemas() {\n        const { schema } = this;\n        const struct = schema;\n        if (!struct || typeof struct !== \"object\") {\n            return {};\n        }\n        if (\"members\" in struct) {\n            const buffer = {};\n            for (const member of struct.memberNames) {\n                buffer[member] = this.getMemberSchema(member);\n            }\n            return buffer;\n        }\n        return {};\n    }\n    *structIterator() {\n        if (this.isUnitSchema()) {\n            return;\n        }\n        if (!this.isStructSchema()) {\n            throw new Error(\"@smithy/core/schema - cannot acquire structIterator on non-struct schema.\");\n        }\n        const struct = this.getSchema();\n        for (let i = 0; i < struct.memberNames.length; ++i) {\n            yield [struct.memberNames[i], NormalizedSchema.memberFrom([struct.memberList[i], 0], struct.memberNames[i])];\n        }\n    }\n    getSchemaName() {\n        const schema = this.getSchema();\n        if (typeof schema === \"number\") {\n            const _schema = 63 & schema;\n            const container = 192 & schema;\n            const type = Object.entries(_sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA).find(([, value]) => {\n                return value === _schema;\n            })?.[0] ?? \"Unknown\";\n            switch (container) {\n                case _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.MAP_MODIFIER:\n                    return `${type}Map`;\n                case _sentinels__WEBPACK_IMPORTED_MODULE_3__.SCHEMA.LIST_MODIFIER:\n                    return `${type}List`;\n                case 0:\n                    return type;\n            }\n        }\n        return \"Unknown\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/NormalizedSchema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/OperationSchema.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/OperationSchema.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OperationSchema: () => (/* binding */ OperationSchema),\n/* harmony export */   op: () => (/* binding */ op)\n/* harmony export */ });\n/* harmony import */ var _TypeRegistry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../TypeRegistry */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/TypeRegistry.js\");\n/* harmony import */ var _Schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/Schema.js\");\n\n\nclass OperationSchema extends _Schema__WEBPACK_IMPORTED_MODULE_1__.Schema {\n    constructor(name, traits, input, output) {\n        super(name, traits);\n        this.name = name;\n        this.traits = traits;\n        this.input = input;\n        this.output = output;\n    }\n}\nfunction op(namespace, name, traits = {}, input, output) {\n    const schema = new OperationSchema(namespace + \"#\" + name, traits, input, output);\n    _TypeRegistry__WEBPACK_IMPORTED_MODULE_0__.TypeRegistry.for(namespace).register(name, schema);\n    return schema;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9zY2hlbWFzL09wZXJhdGlvblNjaGVtYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStDO0FBQ2I7QUFDM0IsOEJBQThCLDJDQUFNO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyx3Q0FBd0M7QUFDL0M7QUFDQSxJQUFJLHVEQUFZO0FBQ2hCO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvcmVAMy41LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29yZS9kaXN0LWVzL3N1Ym1vZHVsZXMvc2NoZW1hL3NjaGVtYXMvT3BlcmF0aW9uU2NoZW1hLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFR5cGVSZWdpc3RyeSB9IGZyb20gXCIuLi9UeXBlUmVnaXN0cnlcIjtcbmltcG9ydCB7IFNjaGVtYSB9IGZyb20gXCIuL1NjaGVtYVwiO1xuZXhwb3J0IGNsYXNzIE9wZXJhdGlvblNjaGVtYSBleHRlbmRzIFNjaGVtYSB7XG4gICAgY29uc3RydWN0b3IobmFtZSwgdHJhaXRzLCBpbnB1dCwgb3V0cHV0KSB7XG4gICAgICAgIHN1cGVyKG5hbWUsIHRyYWl0cyk7XG4gICAgICAgIHRoaXMubmFtZSA9IG5hbWU7XG4gICAgICAgIHRoaXMudHJhaXRzID0gdHJhaXRzO1xuICAgICAgICB0aGlzLmlucHV0ID0gaW5wdXQ7XG4gICAgICAgIHRoaXMub3V0cHV0ID0gb3V0cHV0O1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBvcChuYW1lc3BhY2UsIG5hbWUsIHRyYWl0cyA9IHt9LCBpbnB1dCwgb3V0cHV0KSB7XG4gICAgY29uc3Qgc2NoZW1hID0gbmV3IE9wZXJhdGlvblNjaGVtYShuYW1lc3BhY2UgKyBcIiNcIiArIG5hbWUsIHRyYWl0cywgaW5wdXQsIG91dHB1dCk7XG4gICAgVHlwZVJlZ2lzdHJ5LmZvcihuYW1lc3BhY2UpLnJlZ2lzdGVyKG5hbWUsIHNjaGVtYSk7XG4gICAgcmV0dXJuIHNjaGVtYTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/OperationSchema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/Schema.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/Schema.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Schema: () => (/* binding */ Schema)\n/* harmony export */ });\nclass Schema {\n    constructor(name, traits) {\n        this.name = name;\n        this.traits = traits;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9zY2hlbWFzL1NjaGVtYS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9zY2hlbWFzL1NjaGVtYS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgU2NoZW1hIHtcbiAgICBjb25zdHJ1Y3RvcihuYW1lLCB0cmFpdHMpIHtcbiAgICAgICAgdGhpcy5uYW1lID0gbmFtZTtcbiAgICAgICAgdGhpcy50cmFpdHMgPSB0cmFpdHM7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/Schema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/SimpleSchema.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/SimpleSchema.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleSchema: () => (/* binding */ SimpleSchema),\n/* harmony export */   sim: () => (/* binding */ sim)\n/* harmony export */ });\n/* harmony import */ var _TypeRegistry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../TypeRegistry */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/TypeRegistry.js\");\n/* harmony import */ var _Schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/Schema.js\");\n\n\nclass SimpleSchema extends _Schema__WEBPACK_IMPORTED_MODULE_1__.Schema {\n    constructor(name, schemaRef, traits) {\n        super(name, traits);\n        this.name = name;\n        this.schemaRef = schemaRef;\n        this.traits = traits;\n    }\n}\nfunction sim(namespace, name, schemaRef, traits) {\n    const schema = new SimpleSchema(namespace + \"#\" + name, schemaRef, traits);\n    _TypeRegistry__WEBPACK_IMPORTED_MODULE_0__.TypeRegistry.for(namespace).register(name, schema);\n    return schema;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9zY2hlbWFzL1NpbXBsZVNjaGVtYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStDO0FBQ2I7QUFDM0IsMkJBQTJCLDJDQUFNO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLElBQUksdURBQVk7QUFDaEI7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29yZUAzLjUuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9zY2hlbWEvc2NoZW1hcy9TaW1wbGVTY2hlbWEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVHlwZVJlZ2lzdHJ5IH0gZnJvbSBcIi4uL1R5cGVSZWdpc3RyeVwiO1xuaW1wb3J0IHsgU2NoZW1hIH0gZnJvbSBcIi4vU2NoZW1hXCI7XG5leHBvcnQgY2xhc3MgU2ltcGxlU2NoZW1hIGV4dGVuZHMgU2NoZW1hIHtcbiAgICBjb25zdHJ1Y3RvcihuYW1lLCBzY2hlbWFSZWYsIHRyYWl0cykge1xuICAgICAgICBzdXBlcihuYW1lLCB0cmFpdHMpO1xuICAgICAgICB0aGlzLm5hbWUgPSBuYW1lO1xuICAgICAgICB0aGlzLnNjaGVtYVJlZiA9IHNjaGVtYVJlZjtcbiAgICAgICAgdGhpcy50cmFpdHMgPSB0cmFpdHM7XG4gICAgfVxufVxuZXhwb3J0IGZ1bmN0aW9uIHNpbShuYW1lc3BhY2UsIG5hbWUsIHNjaGVtYVJlZiwgdHJhaXRzKSB7XG4gICAgY29uc3Qgc2NoZW1hID0gbmV3IFNpbXBsZVNjaGVtYShuYW1lc3BhY2UgKyBcIiNcIiArIG5hbWUsIHNjaGVtYVJlZiwgdHJhaXRzKTtcbiAgICBUeXBlUmVnaXN0cnkuZm9yKG5hbWVzcGFjZSkucmVnaXN0ZXIobmFtZSwgc2NoZW1hKTtcbiAgICByZXR1cm4gc2NoZW1hO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/SimpleSchema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/StructureSchema.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/StructureSchema.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StructureSchema: () => (/* binding */ StructureSchema),\n/* harmony export */   struct: () => (/* binding */ struct)\n/* harmony export */ });\n/* harmony import */ var _TypeRegistry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../TypeRegistry */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/TypeRegistry.js\");\n/* harmony import */ var _Schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/Schema.js\");\n\n\nclass StructureSchema extends _Schema__WEBPACK_IMPORTED_MODULE_1__.Schema {\n    constructor(name, traits, memberNames, memberList) {\n        super(name, traits);\n        this.name = name;\n        this.traits = traits;\n        this.memberNames = memberNames;\n        this.memberList = memberList;\n        this.members = {};\n        for (let i = 0; i < memberNames.length; ++i) {\n            this.members[memberNames[i]] = Array.isArray(memberList[i])\n                ? memberList[i]\n                : [memberList[i], 0];\n        }\n    }\n}\nfunction struct(namespace, name, traits, memberNames, memberList) {\n    const schema = new StructureSchema(namespace + \"#\" + name, traits, memberNames, memberList);\n    _TypeRegistry__WEBPACK_IMPORTED_MODULE_0__.TypeRegistry.for(namespace).register(name, schema);\n    return schema;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9zY2hlbWFzL1N0cnVjdHVyZVNjaGVtYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStDO0FBQ2I7QUFDM0IsOEJBQThCLDJDQUFNO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHdCQUF3QjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EsSUFBSSx1REFBWTtBQUNoQjtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9zY2hlbWFzL1N0cnVjdHVyZVNjaGVtYS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUeXBlUmVnaXN0cnkgfSBmcm9tIFwiLi4vVHlwZVJlZ2lzdHJ5XCI7XG5pbXBvcnQgeyBTY2hlbWEgfSBmcm9tIFwiLi9TY2hlbWFcIjtcbmV4cG9ydCBjbGFzcyBTdHJ1Y3R1cmVTY2hlbWEgZXh0ZW5kcyBTY2hlbWEge1xuICAgIGNvbnN0cnVjdG9yKG5hbWUsIHRyYWl0cywgbWVtYmVyTmFtZXMsIG1lbWJlckxpc3QpIHtcbiAgICAgICAgc3VwZXIobmFtZSwgdHJhaXRzKTtcbiAgICAgICAgdGhpcy5uYW1lID0gbmFtZTtcbiAgICAgICAgdGhpcy50cmFpdHMgPSB0cmFpdHM7XG4gICAgICAgIHRoaXMubWVtYmVyTmFtZXMgPSBtZW1iZXJOYW1lcztcbiAgICAgICAgdGhpcy5tZW1iZXJMaXN0ID0gbWVtYmVyTGlzdDtcbiAgICAgICAgdGhpcy5tZW1iZXJzID0ge307XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbWVtYmVyTmFtZXMubGVuZ3RoOyArK2kpIHtcbiAgICAgICAgICAgIHRoaXMubWVtYmVyc1ttZW1iZXJOYW1lc1tpXV0gPSBBcnJheS5pc0FycmF5KG1lbWJlckxpc3RbaV0pXG4gICAgICAgICAgICAgICAgPyBtZW1iZXJMaXN0W2ldXG4gICAgICAgICAgICAgICAgOiBbbWVtYmVyTGlzdFtpXSwgMF07XG4gICAgICAgIH1cbiAgICB9XG59XG5leHBvcnQgZnVuY3Rpb24gc3RydWN0KG5hbWVzcGFjZSwgbmFtZSwgdHJhaXRzLCBtZW1iZXJOYW1lcywgbWVtYmVyTGlzdCkge1xuICAgIGNvbnN0IHNjaGVtYSA9IG5ldyBTdHJ1Y3R1cmVTY2hlbWEobmFtZXNwYWNlICsgXCIjXCIgKyBuYW1lLCB0cmFpdHMsIG1lbWJlck5hbWVzLCBtZW1iZXJMaXN0KTtcbiAgICBUeXBlUmVnaXN0cnkuZm9yKG5hbWVzcGFjZSkucmVnaXN0ZXIobmFtZSwgc2NoZW1hKTtcbiAgICByZXR1cm4gc2NoZW1hO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/StructureSchema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/sentinels.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/sentinels.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SCHEMA: () => (/* binding */ SCHEMA)\n/* harmony export */ });\nconst SCHEMA = {\n    BLOB: 21,\n    STREAMING_BLOB: 42,\n    BOOLEAN: 2,\n    STRING: 0,\n    NUMERIC: 1,\n    BIG_INTEGER: 17,\n    BIG_DECIMAL: 19,\n    DOCUMENT: 15,\n    TIMESTAMP_DEFAULT: 4,\n    TIMESTAMP_DATE_TIME: 5,\n    TIMESTAMP_HTTP_DATE: 6,\n    TIMESTAMP_EPOCH_SECONDS: 7,\n    LIST_MODIFIER: 64,\n    MAP_MODIFIER: 128,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NjaGVtYS9zY2hlbWFzL3NlbnRpbmVscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvcmVAMy41LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29yZS9kaXN0LWVzL3N1Ym1vZHVsZXMvc2NoZW1hL3NjaGVtYXMvc2VudGluZWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBTQ0hFTUEgPSB7XG4gICAgQkxPQjogMjEsXG4gICAgU1RSRUFNSU5HX0JMT0I6IDQyLFxuICAgIEJPT0xFQU46IDIsXG4gICAgU1RSSU5HOiAwLFxuICAgIE5VTUVSSUM6IDEsXG4gICAgQklHX0lOVEVHRVI6IDE3LFxuICAgIEJJR19ERUNJTUFMOiAxOSxcbiAgICBET0NVTUVOVDogMTUsXG4gICAgVElNRVNUQU1QX0RFRkFVTFQ6IDQsXG4gICAgVElNRVNUQU1QX0RBVEVfVElNRTogNSxcbiAgICBUSU1FU1RBTVBfSFRUUF9EQVRFOiA2LFxuICAgIFRJTUVTVEFNUF9FUE9DSF9TRUNPTkRTOiA3LFxuICAgIExJU1RfTU9ESUZJRVI6IDY0LFxuICAgIE1BUF9NT0RJRklFUjogMTI4LFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/schemas/sentinels.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/copyDocumentWithTransform.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/copyDocumentWithTransform.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copyDocumentWithTransform: () => (/* binding */ copyDocumentWithTransform)\n/* harmony export */ });\n/* harmony import */ var _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core/schema */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/schema/index.js\");\n\nconst copyDocumentWithTransform = (source, schemaRef, transform = (_) => _) => {\n    const ns = _smithy_core_schema__WEBPACK_IMPORTED_MODULE_0__.NormalizedSchema.of(schemaRef);\n    switch (typeof source) {\n        case \"undefined\":\n        case \"boolean\":\n        case \"number\":\n        case \"string\":\n        case \"bigint\":\n        case \"symbol\":\n            return transform(source, ns);\n        case \"function\":\n        case \"object\":\n            if (source === null) {\n                return transform(null, ns);\n            }\n            if (Array.isArray(source)) {\n                const newArray = new Array(source.length);\n                let i = 0;\n                for (const item of source) {\n                    newArray[i++] = copyDocumentWithTransform(item, ns.getValueSchema(), transform);\n                }\n                return transform(newArray, ns);\n            }\n            if (\"byteLength\" in source) {\n                const newBytes = new Uint8Array(source.byteLength);\n                newBytes.set(source, 0);\n                return transform(newBytes, ns);\n            }\n            if (source instanceof Date) {\n                return transform(source, ns);\n            }\n            const newObject = {};\n            if (ns.isMapSchema()) {\n                for (const key of Object.keys(source)) {\n                    newObject[key] = copyDocumentWithTransform(source[key], ns.getValueSchema(), transform);\n                }\n            }\n            else if (ns.isStructSchema()) {\n                for (const [key, memberSchema] of ns.structIterator()) {\n                    newObject[key] = copyDocumentWithTransform(source[key], memberSchema, transform);\n                }\n            }\n            else if (ns.isDocumentSchema()) {\n                for (const key of Object.keys(source)) {\n                    newObject[key] = copyDocumentWithTransform(source[key], ns.getValueSchema(), transform);\n                }\n            }\n            return transform(newObject, ns);\n        default:\n            return transform(source, ns);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/copyDocumentWithTransform.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/date-utils.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/date-utils.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dateToUtcString: () => (/* binding */ dateToUtcString),\n/* harmony export */   parseEpochTimestamp: () => (/* binding */ parseEpochTimestamp),\n/* harmony export */   parseRfc3339DateTime: () => (/* binding */ parseRfc3339DateTime),\n/* harmony export */   parseRfc3339DateTimeWithOffset: () => (/* binding */ parseRfc3339DateTimeWithOffset),\n/* harmony export */   parseRfc7231DateTime: () => (/* binding */ parseRfc7231DateTime)\n/* harmony export */ });\n/* harmony import */ var _parse_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parse-utils */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/parse-utils.js\");\n\nconst DAYS = [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"];\nconst MONTHS = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"];\nfunction dateToUtcString(date) {\n    const year = date.getUTCFullYear();\n    const month = date.getUTCMonth();\n    const dayOfWeek = date.getUTCDay();\n    const dayOfMonthInt = date.getUTCDate();\n    const hoursInt = date.getUTCHours();\n    const minutesInt = date.getUTCMinutes();\n    const secondsInt = date.getUTCSeconds();\n    const dayOfMonthString = dayOfMonthInt < 10 ? `0${dayOfMonthInt}` : `${dayOfMonthInt}`;\n    const hoursString = hoursInt < 10 ? `0${hoursInt}` : `${hoursInt}`;\n    const minutesString = minutesInt < 10 ? `0${minutesInt}` : `${minutesInt}`;\n    const secondsString = secondsInt < 10 ? `0${secondsInt}` : `${secondsInt}`;\n    return `${DAYS[dayOfWeek]}, ${dayOfMonthString} ${MONTHS[month]} ${year} ${hoursString}:${minutesString}:${secondsString} GMT`;\n}\nconst RFC3339 = new RegExp(/^(\\d{4})-(\\d{2})-(\\d{2})[tT](\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d+))?[zZ]$/);\nconst parseRfc3339DateTime = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value !== \"string\") {\n        throw new TypeError(\"RFC-3339 date-times must be expressed as strings\");\n    }\n    const match = RFC3339.exec(value);\n    if (!match) {\n        throw new TypeError(\"Invalid RFC-3339 date-time value\");\n    }\n    const [_, yearStr, monthStr, dayStr, hours, minutes, seconds, fractionalMilliseconds] = match;\n    const year = (0,_parse_utils__WEBPACK_IMPORTED_MODULE_0__.strictParseShort)(stripLeadingZeroes(yearStr));\n    const month = parseDateValue(monthStr, \"month\", 1, 12);\n    const day = parseDateValue(dayStr, \"day\", 1, 31);\n    return buildDate(year, month, day, { hours, minutes, seconds, fractionalMilliseconds });\n};\nconst RFC3339_WITH_OFFSET = new RegExp(/^(\\d{4})-(\\d{2})-(\\d{2})[tT](\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d+))?(([-+]\\d{2}\\:\\d{2})|[zZ])$/);\nconst parseRfc3339DateTimeWithOffset = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value !== \"string\") {\n        throw new TypeError(\"RFC-3339 date-times must be expressed as strings\");\n    }\n    const match = RFC3339_WITH_OFFSET.exec(value);\n    if (!match) {\n        throw new TypeError(\"Invalid RFC-3339 date-time value\");\n    }\n    const [_, yearStr, monthStr, dayStr, hours, minutes, seconds, fractionalMilliseconds, offsetStr] = match;\n    const year = (0,_parse_utils__WEBPACK_IMPORTED_MODULE_0__.strictParseShort)(stripLeadingZeroes(yearStr));\n    const month = parseDateValue(monthStr, \"month\", 1, 12);\n    const day = parseDateValue(dayStr, \"day\", 1, 31);\n    const date = buildDate(year, month, day, { hours, minutes, seconds, fractionalMilliseconds });\n    if (offsetStr.toUpperCase() != \"Z\") {\n        date.setTime(date.getTime() - parseOffsetToMilliseconds(offsetStr));\n    }\n    return date;\n};\nconst IMF_FIXDATE = new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\\d{2}) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\\d{4}) (\\d{1,2}):(\\d{2}):(\\d{2})(?:\\.(\\d+))? GMT$/);\nconst RFC_850_DATE = new RegExp(/^(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\\d{2})-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\\d{2}) (\\d{1,2}):(\\d{2}):(\\d{2})(?:\\.(\\d+))? GMT$/);\nconst ASC_TIME = new RegExp(/^(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( [1-9]|\\d{2}) (\\d{1,2}):(\\d{2}):(\\d{2})(?:\\.(\\d+))? (\\d{4})$/);\nconst parseRfc7231DateTime = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value !== \"string\") {\n        throw new TypeError(\"RFC-7231 date-times must be expressed as strings\");\n    }\n    let match = IMF_FIXDATE.exec(value);\n    if (match) {\n        const [_, dayStr, monthStr, yearStr, hours, minutes, seconds, fractionalMilliseconds] = match;\n        return buildDate((0,_parse_utils__WEBPACK_IMPORTED_MODULE_0__.strictParseShort)(stripLeadingZeroes(yearStr)), parseMonthByShortName(monthStr), parseDateValue(dayStr, \"day\", 1, 31), { hours, minutes, seconds, fractionalMilliseconds });\n    }\n    match = RFC_850_DATE.exec(value);\n    if (match) {\n        const [_, dayStr, monthStr, yearStr, hours, minutes, seconds, fractionalMilliseconds] = match;\n        return adjustRfc850Year(buildDate(parseTwoDigitYear(yearStr), parseMonthByShortName(monthStr), parseDateValue(dayStr, \"day\", 1, 31), {\n            hours,\n            minutes,\n            seconds,\n            fractionalMilliseconds,\n        }));\n    }\n    match = ASC_TIME.exec(value);\n    if (match) {\n        const [_, monthStr, dayStr, hours, minutes, seconds, fractionalMilliseconds, yearStr] = match;\n        return buildDate((0,_parse_utils__WEBPACK_IMPORTED_MODULE_0__.strictParseShort)(stripLeadingZeroes(yearStr)), parseMonthByShortName(monthStr), parseDateValue(dayStr.trimLeft(), \"day\", 1, 31), { hours, minutes, seconds, fractionalMilliseconds });\n    }\n    throw new TypeError(\"Invalid RFC-7231 date-time value\");\n};\nconst parseEpochTimestamp = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    let valueAsDouble;\n    if (typeof value === \"number\") {\n        valueAsDouble = value;\n    }\n    else if (typeof value === \"string\") {\n        valueAsDouble = (0,_parse_utils__WEBPACK_IMPORTED_MODULE_0__.strictParseDouble)(value);\n    }\n    else if (typeof value === \"object\" && value.tag === 1) {\n        valueAsDouble = value.value;\n    }\n    else {\n        throw new TypeError(\"Epoch timestamps must be expressed as floating point numbers or their string representation\");\n    }\n    if (Number.isNaN(valueAsDouble) || valueAsDouble === Infinity || valueAsDouble === -Infinity) {\n        throw new TypeError(\"Epoch timestamps must be valid, non-Infinite, non-NaN numerics\");\n    }\n    return new Date(Math.round(valueAsDouble * 1000));\n};\nconst buildDate = (year, month, day, time) => {\n    const adjustedMonth = month - 1;\n    validateDayOfMonth(year, adjustedMonth, day);\n    return new Date(Date.UTC(year, adjustedMonth, day, parseDateValue(time.hours, \"hour\", 0, 23), parseDateValue(time.minutes, \"minute\", 0, 59), parseDateValue(time.seconds, \"seconds\", 0, 60), parseMilliseconds(time.fractionalMilliseconds)));\n};\nconst parseTwoDigitYear = (value) => {\n    const thisYear = new Date().getUTCFullYear();\n    const valueInThisCentury = Math.floor(thisYear / 100) * 100 + (0,_parse_utils__WEBPACK_IMPORTED_MODULE_0__.strictParseShort)(stripLeadingZeroes(value));\n    if (valueInThisCentury < thisYear) {\n        return valueInThisCentury + 100;\n    }\n    return valueInThisCentury;\n};\nconst FIFTY_YEARS_IN_MILLIS = 50 * 365 * 24 * 60 * 60 * 1000;\nconst adjustRfc850Year = (input) => {\n    if (input.getTime() - new Date().getTime() > FIFTY_YEARS_IN_MILLIS) {\n        return new Date(Date.UTC(input.getUTCFullYear() - 100, input.getUTCMonth(), input.getUTCDate(), input.getUTCHours(), input.getUTCMinutes(), input.getUTCSeconds(), input.getUTCMilliseconds()));\n    }\n    return input;\n};\nconst parseMonthByShortName = (value) => {\n    const monthIdx = MONTHS.indexOf(value);\n    if (monthIdx < 0) {\n        throw new TypeError(`Invalid month: ${value}`);\n    }\n    return monthIdx + 1;\n};\nconst DAYS_IN_MONTH = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nconst validateDayOfMonth = (year, month, day) => {\n    let maxDays = DAYS_IN_MONTH[month];\n    if (month === 1 && isLeapYear(year)) {\n        maxDays = 29;\n    }\n    if (day > maxDays) {\n        throw new TypeError(`Invalid day for ${MONTHS[month]} in ${year}: ${day}`);\n    }\n};\nconst isLeapYear = (year) => {\n    return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n};\nconst parseDateValue = (value, type, lower, upper) => {\n    const dateVal = (0,_parse_utils__WEBPACK_IMPORTED_MODULE_0__.strictParseByte)(stripLeadingZeroes(value));\n    if (dateVal < lower || dateVal > upper) {\n        throw new TypeError(`${type} must be between ${lower} and ${upper}, inclusive`);\n    }\n    return dateVal;\n};\nconst parseMilliseconds = (value) => {\n    if (value === null || value === undefined) {\n        return 0;\n    }\n    return (0,_parse_utils__WEBPACK_IMPORTED_MODULE_0__.strictParseFloat32)(\"0.\" + value) * 1000;\n};\nconst parseOffsetToMilliseconds = (value) => {\n    const directionStr = value[0];\n    let direction = 1;\n    if (directionStr == \"+\") {\n        direction = 1;\n    }\n    else if (directionStr == \"-\") {\n        direction = -1;\n    }\n    else {\n        throw new TypeError(`Offset direction, ${directionStr}, must be \"+\" or \"-\"`);\n    }\n    const hour = Number(value.substring(1, 3));\n    const minute = Number(value.substring(4, 6));\n    return direction * (hour * 60 + minute) * 60 * 1000;\n};\nconst stripLeadingZeroes = (value) => {\n    let idx = 0;\n    while (idx < value.length - 1 && value.charAt(idx) === \"0\") {\n        idx++;\n    }\n    if (idx === 0) {\n        return value;\n    }\n    return value.slice(idx);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/date-utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/index.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/index.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LazyJsonString: () => (/* reexport safe */ _lazy_json__WEBPACK_IMPORTED_MODULE_2__.LazyJsonString),\n/* harmony export */   NumericValue: () => (/* reexport safe */ _value_NumericValue__WEBPACK_IMPORTED_MODULE_7__.NumericValue),\n/* harmony export */   copyDocumentWithTransform: () => (/* reexport safe */ _copyDocumentWithTransform__WEBPACK_IMPORTED_MODULE_0__.copyDocumentWithTransform),\n/* harmony export */   dateToUtcString: () => (/* reexport safe */ _date_utils__WEBPACK_IMPORTED_MODULE_1__.dateToUtcString),\n/* harmony export */   expectBoolean: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.expectBoolean),\n/* harmony export */   expectByte: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.expectByte),\n/* harmony export */   expectFloat32: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.expectFloat32),\n/* harmony export */   expectInt: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.expectInt),\n/* harmony export */   expectInt32: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.expectInt32),\n/* harmony export */   expectLong: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.expectLong),\n/* harmony export */   expectNonNull: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.expectNonNull),\n/* harmony export */   expectNumber: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.expectNumber),\n/* harmony export */   expectObject: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.expectObject),\n/* harmony export */   expectShort: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.expectShort),\n/* harmony export */   expectString: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.expectString),\n/* harmony export */   expectUnion: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.expectUnion),\n/* harmony export */   handleFloat: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.handleFloat),\n/* harmony export */   limitedParseDouble: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.limitedParseDouble),\n/* harmony export */   limitedParseFloat: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.limitedParseFloat),\n/* harmony export */   limitedParseFloat32: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.limitedParseFloat32),\n/* harmony export */   logger: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.logger),\n/* harmony export */   nv: () => (/* reexport safe */ _value_NumericValue__WEBPACK_IMPORTED_MODULE_7__.nv),\n/* harmony export */   parseBoolean: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.parseBoolean),\n/* harmony export */   parseEpochTimestamp: () => (/* reexport safe */ _date_utils__WEBPACK_IMPORTED_MODULE_1__.parseEpochTimestamp),\n/* harmony export */   parseRfc3339DateTime: () => (/* reexport safe */ _date_utils__WEBPACK_IMPORTED_MODULE_1__.parseRfc3339DateTime),\n/* harmony export */   parseRfc3339DateTimeWithOffset: () => (/* reexport safe */ _date_utils__WEBPACK_IMPORTED_MODULE_1__.parseRfc3339DateTimeWithOffset),\n/* harmony export */   parseRfc7231DateTime: () => (/* reexport safe */ _date_utils__WEBPACK_IMPORTED_MODULE_1__.parseRfc7231DateTime),\n/* harmony export */   quoteHeader: () => (/* reexport safe */ _quote_header__WEBPACK_IMPORTED_MODULE_4__.quoteHeader),\n/* harmony export */   splitEvery: () => (/* reexport safe */ _split_every__WEBPACK_IMPORTED_MODULE_5__.splitEvery),\n/* harmony export */   splitHeader: () => (/* reexport safe */ _split_header__WEBPACK_IMPORTED_MODULE_6__.splitHeader),\n/* harmony export */   strictParseByte: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.strictParseByte),\n/* harmony export */   strictParseDouble: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.strictParseDouble),\n/* harmony export */   strictParseFloat: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.strictParseFloat),\n/* harmony export */   strictParseFloat32: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.strictParseFloat32),\n/* harmony export */   strictParseInt: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.strictParseInt),\n/* harmony export */   strictParseInt32: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.strictParseInt32),\n/* harmony export */   strictParseLong: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.strictParseLong),\n/* harmony export */   strictParseShort: () => (/* reexport safe */ _parse_utils__WEBPACK_IMPORTED_MODULE_3__.strictParseShort)\n/* harmony export */ });\n/* harmony import */ var _copyDocumentWithTransform__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./copyDocumentWithTransform */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/copyDocumentWithTransform.js\");\n/* harmony import */ var _date_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./date-utils */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/date-utils.js\");\n/* harmony import */ var _lazy_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lazy-json */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/lazy-json.js\");\n/* harmony import */ var _parse_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./parse-utils */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/parse-utils.js\");\n/* harmony import */ var _quote_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./quote-header */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/quote-header.js\");\n/* harmony import */ var _split_every__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./split-every */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/split-every.js\");\n/* harmony import */ var _split_header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./split-header */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/split-header.js\");\n/* harmony import */ var _value_NumericValue__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./value/NumericValue */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/value/NumericValue.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NlcmRlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNEM7QUFDZjtBQUNEO0FBQ0U7QUFDQztBQUNEO0FBQ0M7QUFDTSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29yZUAzLjUuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9zZXJkZS9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9jb3B5RG9jdW1lbnRXaXRoVHJhbnNmb3JtXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9kYXRlLXV0aWxzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9sYXp5LWpzb25cIjtcbmV4cG9ydCAqIGZyb20gXCIuL3BhcnNlLXV0aWxzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9xdW90ZS1oZWFkZXJcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NwbGl0LWV2ZXJ5XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zcGxpdC1oZWFkZXJcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3ZhbHVlL051bWVyaWNWYWx1ZVwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/lazy-json.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/lazy-json.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LazyJsonString: () => (/* binding */ LazyJsonString)\n/* harmony export */ });\nconst LazyJsonString = function LazyJsonString(val) {\n    const str = Object.assign(new String(val), {\n        deserializeJSON() {\n            return JSON.parse(String(val));\n        },\n        toString() {\n            return String(val);\n        },\n        toJSON() {\n            return String(val);\n        },\n    });\n    return str;\n};\nLazyJsonString.from = (object) => {\n    if (object && typeof object === \"object\" && (object instanceof LazyJsonString || \"deserializeJSON\" in object)) {\n        return object;\n    }\n    else if (typeof object === \"string\" || Object.getPrototypeOf(object) === String.prototype) {\n        return LazyJsonString(String(object));\n    }\n    return LazyJsonString(JSON.stringify(object));\n};\nLazyJsonString.fromObject = LazyJsonString.from;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NlcmRlL2xhenktanNvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvcmVAMy41LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29yZS9kaXN0LWVzL3N1Ym1vZHVsZXMvc2VyZGUvbGF6eS1qc29uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBMYXp5SnNvblN0cmluZyA9IGZ1bmN0aW9uIExhenlKc29uU3RyaW5nKHZhbCkge1xuICAgIGNvbnN0IHN0ciA9IE9iamVjdC5hc3NpZ24obmV3IFN0cmluZyh2YWwpLCB7XG4gICAgICAgIGRlc2VyaWFsaXplSlNPTigpIHtcbiAgICAgICAgICAgIHJldHVybiBKU09OLnBhcnNlKFN0cmluZyh2YWwpKTtcbiAgICAgICAgfSxcbiAgICAgICAgdG9TdHJpbmcoKSB7XG4gICAgICAgICAgICByZXR1cm4gU3RyaW5nKHZhbCk7XG4gICAgICAgIH0sXG4gICAgICAgIHRvSlNPTigpIHtcbiAgICAgICAgICAgIHJldHVybiBTdHJpbmcodmFsKTtcbiAgICAgICAgfSxcbiAgICB9KTtcbiAgICByZXR1cm4gc3RyO1xufTtcbkxhenlKc29uU3RyaW5nLmZyb20gPSAob2JqZWN0KSA9PiB7XG4gICAgaWYgKG9iamVjdCAmJiB0eXBlb2Ygb2JqZWN0ID09PSBcIm9iamVjdFwiICYmIChvYmplY3QgaW5zdGFuY2VvZiBMYXp5SnNvblN0cmluZyB8fCBcImRlc2VyaWFsaXplSlNPTlwiIGluIG9iamVjdCkpIHtcbiAgICAgICAgcmV0dXJuIG9iamVjdDtcbiAgICB9XG4gICAgZWxzZSBpZiAodHlwZW9mIG9iamVjdCA9PT0gXCJzdHJpbmdcIiB8fCBPYmplY3QuZ2V0UHJvdG90eXBlT2Yob2JqZWN0KSA9PT0gU3RyaW5nLnByb3RvdHlwZSkge1xuICAgICAgICByZXR1cm4gTGF6eUpzb25TdHJpbmcoU3RyaW5nKG9iamVjdCkpO1xuICAgIH1cbiAgICByZXR1cm4gTGF6eUpzb25TdHJpbmcoSlNPTi5zdHJpbmdpZnkob2JqZWN0KSk7XG59O1xuTGF6eUpzb25TdHJpbmcuZnJvbU9iamVjdCA9IExhenlKc29uU3RyaW5nLmZyb207XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/lazy-json.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/parse-utils.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/parse-utils.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   expectBoolean: () => (/* binding */ expectBoolean),\n/* harmony export */   expectByte: () => (/* binding */ expectByte),\n/* harmony export */   expectFloat32: () => (/* binding */ expectFloat32),\n/* harmony export */   expectInt: () => (/* binding */ expectInt),\n/* harmony export */   expectInt32: () => (/* binding */ expectInt32),\n/* harmony export */   expectLong: () => (/* binding */ expectLong),\n/* harmony export */   expectNonNull: () => (/* binding */ expectNonNull),\n/* harmony export */   expectNumber: () => (/* binding */ expectNumber),\n/* harmony export */   expectObject: () => (/* binding */ expectObject),\n/* harmony export */   expectShort: () => (/* binding */ expectShort),\n/* harmony export */   expectString: () => (/* binding */ expectString),\n/* harmony export */   expectUnion: () => (/* binding */ expectUnion),\n/* harmony export */   handleFloat: () => (/* binding */ handleFloat),\n/* harmony export */   limitedParseDouble: () => (/* binding */ limitedParseDouble),\n/* harmony export */   limitedParseFloat: () => (/* binding */ limitedParseFloat),\n/* harmony export */   limitedParseFloat32: () => (/* binding */ limitedParseFloat32),\n/* harmony export */   logger: () => (/* binding */ logger),\n/* harmony export */   parseBoolean: () => (/* binding */ parseBoolean),\n/* harmony export */   strictParseByte: () => (/* binding */ strictParseByte),\n/* harmony export */   strictParseDouble: () => (/* binding */ strictParseDouble),\n/* harmony export */   strictParseFloat: () => (/* binding */ strictParseFloat),\n/* harmony export */   strictParseFloat32: () => (/* binding */ strictParseFloat32),\n/* harmony export */   strictParseInt: () => (/* binding */ strictParseInt),\n/* harmony export */   strictParseInt32: () => (/* binding */ strictParseInt32),\n/* harmony export */   strictParseLong: () => (/* binding */ strictParseLong),\n/* harmony export */   strictParseShort: () => (/* binding */ strictParseShort)\n/* harmony export */ });\nconst parseBoolean = (value) => {\n    switch (value) {\n        case \"true\":\n            return true;\n        case \"false\":\n            return false;\n        default:\n            throw new Error(`Unable to parse boolean value \"${value}\"`);\n    }\n};\nconst expectBoolean = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value === \"number\") {\n        if (value === 0 || value === 1) {\n            logger.warn(stackTraceWarning(`Expected boolean, got ${typeof value}: ${value}`));\n        }\n        if (value === 0) {\n            return false;\n        }\n        if (value === 1) {\n            return true;\n        }\n    }\n    if (typeof value === \"string\") {\n        const lower = value.toLowerCase();\n        if (lower === \"false\" || lower === \"true\") {\n            logger.warn(stackTraceWarning(`Expected boolean, got ${typeof value}: ${value}`));\n        }\n        if (lower === \"false\") {\n            return false;\n        }\n        if (lower === \"true\") {\n            return true;\n        }\n    }\n    if (typeof value === \"boolean\") {\n        return value;\n    }\n    throw new TypeError(`Expected boolean, got ${typeof value}: ${value}`);\n};\nconst expectNumber = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value === \"string\") {\n        const parsed = parseFloat(value);\n        if (!Number.isNaN(parsed)) {\n            if (String(parsed) !== String(value)) {\n                logger.warn(stackTraceWarning(`Expected number but observed string: ${value}`));\n            }\n            return parsed;\n        }\n    }\n    if (typeof value === \"number\") {\n        return value;\n    }\n    throw new TypeError(`Expected number, got ${typeof value}: ${value}`);\n};\nconst MAX_FLOAT = Math.ceil(2 ** 127 * (2 - 2 ** -23));\nconst expectFloat32 = (value) => {\n    const expected = expectNumber(value);\n    if (expected !== undefined && !Number.isNaN(expected) && expected !== Infinity && expected !== -Infinity) {\n        if (Math.abs(expected) > MAX_FLOAT) {\n            throw new TypeError(`Expected 32-bit float, got ${value}`);\n        }\n    }\n    return expected;\n};\nconst expectLong = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (Number.isInteger(value) && !Number.isNaN(value)) {\n        return value;\n    }\n    throw new TypeError(`Expected integer, got ${typeof value}: ${value}`);\n};\nconst expectInt = expectLong;\nconst expectInt32 = (value) => expectSizedInt(value, 32);\nconst expectShort = (value) => expectSizedInt(value, 16);\nconst expectByte = (value) => expectSizedInt(value, 8);\nconst expectSizedInt = (value, size) => {\n    const expected = expectLong(value);\n    if (expected !== undefined && castInt(expected, size) !== expected) {\n        throw new TypeError(`Expected ${size}-bit integer, got ${value}`);\n    }\n    return expected;\n};\nconst castInt = (value, size) => {\n    switch (size) {\n        case 32:\n            return Int32Array.of(value)[0];\n        case 16:\n            return Int16Array.of(value)[0];\n        case 8:\n            return Int8Array.of(value)[0];\n    }\n};\nconst expectNonNull = (value, location) => {\n    if (value === null || value === undefined) {\n        if (location) {\n            throw new TypeError(`Expected a non-null value for ${location}`);\n        }\n        throw new TypeError(\"Expected a non-null value\");\n    }\n    return value;\n};\nconst expectObject = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value === \"object\" && !Array.isArray(value)) {\n        return value;\n    }\n    const receivedType = Array.isArray(value) ? \"array\" : typeof value;\n    throw new TypeError(`Expected object, got ${receivedType}: ${value}`);\n};\nconst expectString = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    if (typeof value === \"string\") {\n        return value;\n    }\n    if ([\"boolean\", \"number\", \"bigint\"].includes(typeof value)) {\n        logger.warn(stackTraceWarning(`Expected string, got ${typeof value}: ${value}`));\n        return String(value);\n    }\n    throw new TypeError(`Expected string, got ${typeof value}: ${value}`);\n};\nconst expectUnion = (value) => {\n    if (value === null || value === undefined) {\n        return undefined;\n    }\n    const asObject = expectObject(value);\n    const setKeys = Object.entries(asObject)\n        .filter(([, v]) => v != null)\n        .map(([k]) => k);\n    if (setKeys.length === 0) {\n        throw new TypeError(`Unions must have exactly one non-null member. None were found.`);\n    }\n    if (setKeys.length > 1) {\n        throw new TypeError(`Unions must have exactly one non-null member. Keys ${setKeys} were not null.`);\n    }\n    return asObject;\n};\nconst strictParseDouble = (value) => {\n    if (typeof value == \"string\") {\n        return expectNumber(parseNumber(value));\n    }\n    return expectNumber(value);\n};\nconst strictParseFloat = strictParseDouble;\nconst strictParseFloat32 = (value) => {\n    if (typeof value == \"string\") {\n        return expectFloat32(parseNumber(value));\n    }\n    return expectFloat32(value);\n};\nconst NUMBER_REGEX = /(-?(?:0|[1-9]\\d*)(?:\\.\\d+)?(?:[eE][+-]?\\d+)?)|(-?Infinity)|(NaN)/g;\nconst parseNumber = (value) => {\n    const matches = value.match(NUMBER_REGEX);\n    if (matches === null || matches[0].length !== value.length) {\n        throw new TypeError(`Expected real number, got implicit NaN`);\n    }\n    return parseFloat(value);\n};\nconst limitedParseDouble = (value) => {\n    if (typeof value == \"string\") {\n        return parseFloatString(value);\n    }\n    return expectNumber(value);\n};\nconst handleFloat = limitedParseDouble;\nconst limitedParseFloat = limitedParseDouble;\nconst limitedParseFloat32 = (value) => {\n    if (typeof value == \"string\") {\n        return parseFloatString(value);\n    }\n    return expectFloat32(value);\n};\nconst parseFloatString = (value) => {\n    switch (value) {\n        case \"NaN\":\n            return NaN;\n        case \"Infinity\":\n            return Infinity;\n        case \"-Infinity\":\n            return -Infinity;\n        default:\n            throw new Error(`Unable to parse float value: ${value}`);\n    }\n};\nconst strictParseLong = (value) => {\n    if (typeof value === \"string\") {\n        return expectLong(parseNumber(value));\n    }\n    return expectLong(value);\n};\nconst strictParseInt = strictParseLong;\nconst strictParseInt32 = (value) => {\n    if (typeof value === \"string\") {\n        return expectInt32(parseNumber(value));\n    }\n    return expectInt32(value);\n};\nconst strictParseShort = (value) => {\n    if (typeof value === \"string\") {\n        return expectShort(parseNumber(value));\n    }\n    return expectShort(value);\n};\nconst strictParseByte = (value) => {\n    if (typeof value === \"string\") {\n        return expectByte(parseNumber(value));\n    }\n    return expectByte(value);\n};\nconst stackTraceWarning = (message) => {\n    return String(new TypeError(message).stack || message)\n        .split(\"\\n\")\n        .slice(0, 5)\n        .filter((s) => !s.includes(\"stackTraceWarning\"))\n        .join(\"\\n\");\n};\nconst logger = {\n    warn: console.warn,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NlcmRlL3BhcnNlLXV0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4REFBOEQsTUFBTTtBQUNwRTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUVBQW1FLGFBQWEsSUFBSSxNQUFNO0FBQzFGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtRUFBbUUsYUFBYSxJQUFJLE1BQU07QUFDMUY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFpRCxhQUFhLElBQUksTUFBTTtBQUN4RTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzRkFBc0YsTUFBTTtBQUM1RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdEQUFnRCxhQUFhLElBQUksTUFBTTtBQUN2RTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSw4REFBOEQsTUFBTTtBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlELGFBQWEsSUFBSSxNQUFNO0FBQ3hFO0FBQ087QUFDQTtBQUNBO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsS0FBSyxvQkFBb0IsTUFBTTtBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLGlFQUFpRSxTQUFTO0FBQzFFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdELGFBQWEsSUFBSSxNQUFNO0FBQ3ZFO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhEQUE4RCxhQUFhLElBQUksTUFBTTtBQUNyRjtBQUNBO0FBQ0EsZ0RBQWdELGFBQWEsSUFBSSxNQUFNO0FBQ3ZFO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0ZBQWtGLFNBQVM7QUFDM0Y7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDQTtBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDREQUE0RCxNQUFNO0FBQ2xFO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NlcmRlL3BhcnNlLXV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBwYXJzZUJvb2xlYW4gPSAodmFsdWUpID0+IHtcbiAgICBzd2l0Y2ggKHZhbHVlKSB7XG4gICAgICAgIGNhc2UgXCJ0cnVlXCI6XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgY2FzZSBcImZhbHNlXCI6XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuYWJsZSB0byBwYXJzZSBib29sZWFuIHZhbHVlIFwiJHt2YWx1ZX1cImApO1xuICAgIH1cbn07XG5leHBvcnQgY29uc3QgZXhwZWN0Qm9vbGVhbiA9ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgaWYgKHZhbHVlID09PSAwIHx8IHZhbHVlID09PSAxKSB7XG4gICAgICAgICAgICBsb2dnZXIud2FybihzdGFja1RyYWNlV2FybmluZyhgRXhwZWN0ZWQgYm9vbGVhbiwgZ290ICR7dHlwZW9mIHZhbHVlfTogJHt2YWx1ZX1gKSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHZhbHVlID09PSAwKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHZhbHVlID09PSAxKSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAodHlwZW9mIHZhbHVlID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIGNvbnN0IGxvd2VyID0gdmFsdWUudG9Mb3dlckNhc2UoKTtcbiAgICAgICAgaWYgKGxvd2VyID09PSBcImZhbHNlXCIgfHwgbG93ZXIgPT09IFwidHJ1ZVwiKSB7XG4gICAgICAgICAgICBsb2dnZXIud2FybihzdGFja1RyYWNlV2FybmluZyhgRXhwZWN0ZWQgYm9vbGVhbiwgZ290ICR7dHlwZW9mIHZhbHVlfTogJHt2YWx1ZX1gKSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGxvd2VyID09PSBcImZhbHNlXCIpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAobG93ZXIgPT09IFwidHJ1ZVwiKSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAodHlwZW9mIHZhbHVlID09PSBcImJvb2xlYW5cIikge1xuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgfVxuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYEV4cGVjdGVkIGJvb2xlYW4sIGdvdCAke3R5cGVvZiB2YWx1ZX06ICR7dmFsdWV9YCk7XG59O1xuZXhwb3J0IGNvbnN0IGV4cGVjdE51bWJlciA9ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgY29uc3QgcGFyc2VkID0gcGFyc2VGbG9hdCh2YWx1ZSk7XG4gICAgICAgIGlmICghTnVtYmVyLmlzTmFOKHBhcnNlZCkpIHtcbiAgICAgICAgICAgIGlmIChTdHJpbmcocGFyc2VkKSAhPT0gU3RyaW5nKHZhbHVlKSkge1xuICAgICAgICAgICAgICAgIGxvZ2dlci53YXJuKHN0YWNrVHJhY2VXYXJuaW5nKGBFeHBlY3RlZCBudW1iZXIgYnV0IG9ic2VydmVkIHN0cmluZzogJHt2YWx1ZX1gKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gcGFyc2VkO1xuICAgICAgICB9XG4gICAgfVxuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKGBFeHBlY3RlZCBudW1iZXIsIGdvdCAke3R5cGVvZiB2YWx1ZX06ICR7dmFsdWV9YCk7XG59O1xuY29uc3QgTUFYX0ZMT0FUID0gTWF0aC5jZWlsKDIgKiogMTI3ICogKDIgLSAyICoqIC0yMykpO1xuZXhwb3J0IGNvbnN0IGV4cGVjdEZsb2F0MzIgPSAodmFsdWUpID0+IHtcbiAgICBjb25zdCBleHBlY3RlZCA9IGV4cGVjdE51bWJlcih2YWx1ZSk7XG4gICAgaWYgKGV4cGVjdGVkICE9PSB1bmRlZmluZWQgJiYgIU51bWJlci5pc05hTihleHBlY3RlZCkgJiYgZXhwZWN0ZWQgIT09IEluZmluaXR5ICYmIGV4cGVjdGVkICE9PSAtSW5maW5pdHkpIHtcbiAgICAgICAgaWYgKE1hdGguYWJzKGV4cGVjdGVkKSA+IE1BWF9GTE9BVCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihgRXhwZWN0ZWQgMzItYml0IGZsb2F0LCBnb3QgJHt2YWx1ZX1gKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gZXhwZWN0ZWQ7XG59O1xuZXhwb3J0IGNvbnN0IGV4cGVjdExvbmcgPSAodmFsdWUpID0+IHtcbiAgICBpZiAodmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIH1cbiAgICBpZiAoTnVtYmVyLmlzSW50ZWdlcih2YWx1ZSkgJiYgIU51bWJlci5pc05hTih2YWx1ZSkpIHtcbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKGBFeHBlY3RlZCBpbnRlZ2VyLCBnb3QgJHt0eXBlb2YgdmFsdWV9OiAke3ZhbHVlfWApO1xufTtcbmV4cG9ydCBjb25zdCBleHBlY3RJbnQgPSBleHBlY3RMb25nO1xuZXhwb3J0IGNvbnN0IGV4cGVjdEludDMyID0gKHZhbHVlKSA9PiBleHBlY3RTaXplZEludCh2YWx1ZSwgMzIpO1xuZXhwb3J0IGNvbnN0IGV4cGVjdFNob3J0ID0gKHZhbHVlKSA9PiBleHBlY3RTaXplZEludCh2YWx1ZSwgMTYpO1xuZXhwb3J0IGNvbnN0IGV4cGVjdEJ5dGUgPSAodmFsdWUpID0+IGV4cGVjdFNpemVkSW50KHZhbHVlLCA4KTtcbmNvbnN0IGV4cGVjdFNpemVkSW50ID0gKHZhbHVlLCBzaXplKSA9PiB7XG4gICAgY29uc3QgZXhwZWN0ZWQgPSBleHBlY3RMb25nKHZhbHVlKTtcbiAgICBpZiAoZXhwZWN0ZWQgIT09IHVuZGVmaW5lZCAmJiBjYXN0SW50KGV4cGVjdGVkLCBzaXplKSAhPT0gZXhwZWN0ZWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihgRXhwZWN0ZWQgJHtzaXplfS1iaXQgaW50ZWdlciwgZ290ICR7dmFsdWV9YCk7XG4gICAgfVxuICAgIHJldHVybiBleHBlY3RlZDtcbn07XG5jb25zdCBjYXN0SW50ID0gKHZhbHVlLCBzaXplKSA9PiB7XG4gICAgc3dpdGNoIChzaXplKSB7XG4gICAgICAgIGNhc2UgMzI6XG4gICAgICAgICAgICByZXR1cm4gSW50MzJBcnJheS5vZih2YWx1ZSlbMF07XG4gICAgICAgIGNhc2UgMTY6XG4gICAgICAgICAgICByZXR1cm4gSW50MTZBcnJheS5vZih2YWx1ZSlbMF07XG4gICAgICAgIGNhc2UgODpcbiAgICAgICAgICAgIHJldHVybiBJbnQ4QXJyYXkub2YodmFsdWUpWzBdO1xuICAgIH1cbn07XG5leHBvcnQgY29uc3QgZXhwZWN0Tm9uTnVsbCA9ICh2YWx1ZSwgbG9jYXRpb24pID0+IHtcbiAgICBpZiAodmFsdWUgPT09IG51bGwgfHwgdmFsdWUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICBpZiAobG9jYXRpb24pIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYEV4cGVjdGVkIGEgbm9uLW51bGwgdmFsdWUgZm9yICR7bG9jYXRpb259YCk7XG4gICAgICAgIH1cbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkV4cGVjdGVkIGEgbm9uLW51bGwgdmFsdWVcIik7XG4gICAgfVxuICAgIHJldHVybiB2YWx1ZTtcbn07XG5leHBvcnQgY29uc3QgZXhwZWN0T2JqZWN0ID0gKHZhbHVlKSA9PiB7XG4gICAgaWYgKHZhbHVlID09PSBudWxsIHx8IHZhbHVlID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gXCJvYmplY3RcIiAmJiAhQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgICBjb25zdCByZWNlaXZlZFR5cGUgPSBBcnJheS5pc0FycmF5KHZhbHVlKSA/IFwiYXJyYXlcIiA6IHR5cGVvZiB2YWx1ZTtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKGBFeHBlY3RlZCBvYmplY3QsIGdvdCAke3JlY2VpdmVkVHlwZX06ICR7dmFsdWV9YCk7XG59O1xuZXhwb3J0IGNvbnN0IGV4cGVjdFN0cmluZyA9ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgICBpZiAoW1wiYm9vbGVhblwiLCBcIm51bWJlclwiLCBcImJpZ2ludFwiXS5pbmNsdWRlcyh0eXBlb2YgdmFsdWUpKSB7XG4gICAgICAgIGxvZ2dlci53YXJuKHN0YWNrVHJhY2VXYXJuaW5nKGBFeHBlY3RlZCBzdHJpbmcsIGdvdCAke3R5cGVvZiB2YWx1ZX06ICR7dmFsdWV9YCkpO1xuICAgICAgICByZXR1cm4gU3RyaW5nKHZhbHVlKTtcbiAgICB9XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcihgRXhwZWN0ZWQgc3RyaW5nLCBnb3QgJHt0eXBlb2YgdmFsdWV9OiAke3ZhbHVlfWApO1xufTtcbmV4cG9ydCBjb25zdCBleHBlY3RVbmlvbiA9ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PT0gbnVsbCB8fCB2YWx1ZSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxuICAgIGNvbnN0IGFzT2JqZWN0ID0gZXhwZWN0T2JqZWN0KHZhbHVlKTtcbiAgICBjb25zdCBzZXRLZXlzID0gT2JqZWN0LmVudHJpZXMoYXNPYmplY3QpXG4gICAgICAgIC5maWx0ZXIoKFssIHZdKSA9PiB2ICE9IG51bGwpXG4gICAgICAgIC5tYXAoKFtrXSkgPT4gayk7XG4gICAgaWYgKHNldEtleXMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYFVuaW9ucyBtdXN0IGhhdmUgZXhhY3RseSBvbmUgbm9uLW51bGwgbWVtYmVyLiBOb25lIHdlcmUgZm91bmQuYCk7XG4gICAgfVxuICAgIGlmIChzZXRLZXlzLmxlbmd0aCA+IDEpIHtcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihgVW5pb25zIG11c3QgaGF2ZSBleGFjdGx5IG9uZSBub24tbnVsbCBtZW1iZXIuIEtleXMgJHtzZXRLZXlzfSB3ZXJlIG5vdCBudWxsLmApO1xuICAgIH1cbiAgICByZXR1cm4gYXNPYmplY3Q7XG59O1xuZXhwb3J0IGNvbnN0IHN0cmljdFBhcnNlRG91YmxlID0gKHZhbHVlKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIHJldHVybiBleHBlY3ROdW1iZXIocGFyc2VOdW1iZXIodmFsdWUpKTtcbiAgICB9XG4gICAgcmV0dXJuIGV4cGVjdE51bWJlcih2YWx1ZSk7XG59O1xuZXhwb3J0IGNvbnN0IHN0cmljdFBhcnNlRmxvYXQgPSBzdHJpY3RQYXJzZURvdWJsZTtcbmV4cG9ydCBjb25zdCBzdHJpY3RQYXJzZUZsb2F0MzIgPSAodmFsdWUpID0+IHtcbiAgICBpZiAodHlwZW9mIHZhbHVlID09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgcmV0dXJuIGV4cGVjdEZsb2F0MzIocGFyc2VOdW1iZXIodmFsdWUpKTtcbiAgICB9XG4gICAgcmV0dXJuIGV4cGVjdEZsb2F0MzIodmFsdWUpO1xufTtcbmNvbnN0IE5VTUJFUl9SRUdFWCA9IC8oLT8oPzowfFsxLTldXFxkKikoPzpcXC5cXGQrKT8oPzpbZUVdWystXT9cXGQrKT8pfCgtP0luZmluaXR5KXwoTmFOKS9nO1xuY29uc3QgcGFyc2VOdW1iZXIgPSAodmFsdWUpID0+IHtcbiAgICBjb25zdCBtYXRjaGVzID0gdmFsdWUubWF0Y2goTlVNQkVSX1JFR0VYKTtcbiAgICBpZiAobWF0Y2hlcyA9PT0gbnVsbCB8fCBtYXRjaGVzWzBdLmxlbmd0aCAhPT0gdmFsdWUubGVuZ3RoKSB7XG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYEV4cGVjdGVkIHJlYWwgbnVtYmVyLCBnb3QgaW1wbGljaXQgTmFOYCk7XG4gICAgfVxuICAgIHJldHVybiBwYXJzZUZsb2F0KHZhbHVlKTtcbn07XG5leHBvcnQgY29uc3QgbGltaXRlZFBhcnNlRG91YmxlID0gKHZhbHVlKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIHJldHVybiBwYXJzZUZsb2F0U3RyaW5nKHZhbHVlKTtcbiAgICB9XG4gICAgcmV0dXJuIGV4cGVjdE51bWJlcih2YWx1ZSk7XG59O1xuZXhwb3J0IGNvbnN0IGhhbmRsZUZsb2F0ID0gbGltaXRlZFBhcnNlRG91YmxlO1xuZXhwb3J0IGNvbnN0IGxpbWl0ZWRQYXJzZUZsb2F0ID0gbGltaXRlZFBhcnNlRG91YmxlO1xuZXhwb3J0IGNvbnN0IGxpbWl0ZWRQYXJzZUZsb2F0MzIgPSAodmFsdWUpID0+IHtcbiAgICBpZiAodHlwZW9mIHZhbHVlID09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgcmV0dXJuIHBhcnNlRmxvYXRTdHJpbmcodmFsdWUpO1xuICAgIH1cbiAgICByZXR1cm4gZXhwZWN0RmxvYXQzMih2YWx1ZSk7XG59O1xuY29uc3QgcGFyc2VGbG9hdFN0cmluZyA9ICh2YWx1ZSkgPT4ge1xuICAgIHN3aXRjaCAodmFsdWUpIHtcbiAgICAgICAgY2FzZSBcIk5hTlwiOlxuICAgICAgICAgICAgcmV0dXJuIE5hTjtcbiAgICAgICAgY2FzZSBcIkluZmluaXR5XCI6XG4gICAgICAgICAgICByZXR1cm4gSW5maW5pdHk7XG4gICAgICAgIGNhc2UgXCItSW5maW5pdHlcIjpcbiAgICAgICAgICAgIHJldHVybiAtSW5maW5pdHk7XG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuYWJsZSB0byBwYXJzZSBmbG9hdCB2YWx1ZTogJHt2YWx1ZX1gKTtcbiAgICB9XG59O1xuZXhwb3J0IGNvbnN0IHN0cmljdFBhcnNlTG9uZyA9ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgcmV0dXJuIGV4cGVjdExvbmcocGFyc2VOdW1iZXIodmFsdWUpKTtcbiAgICB9XG4gICAgcmV0dXJuIGV4cGVjdExvbmcodmFsdWUpO1xufTtcbmV4cG9ydCBjb25zdCBzdHJpY3RQYXJzZUludCA9IHN0cmljdFBhcnNlTG9uZztcbmV4cG9ydCBjb25zdCBzdHJpY3RQYXJzZUludDMyID0gKHZhbHVlKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICByZXR1cm4gZXhwZWN0SW50MzIocGFyc2VOdW1iZXIodmFsdWUpKTtcbiAgICB9XG4gICAgcmV0dXJuIGV4cGVjdEludDMyKHZhbHVlKTtcbn07XG5leHBvcnQgY29uc3Qgc3RyaWN0UGFyc2VTaG9ydCA9ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgcmV0dXJuIGV4cGVjdFNob3J0KHBhcnNlTnVtYmVyKHZhbHVlKSk7XG4gICAgfVxuICAgIHJldHVybiBleHBlY3RTaG9ydCh2YWx1ZSk7XG59O1xuZXhwb3J0IGNvbnN0IHN0cmljdFBhcnNlQnl0ZSA9ICh2YWx1ZSkgPT4ge1xuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgcmV0dXJuIGV4cGVjdEJ5dGUocGFyc2VOdW1iZXIodmFsdWUpKTtcbiAgICB9XG4gICAgcmV0dXJuIGV4cGVjdEJ5dGUodmFsdWUpO1xufTtcbmNvbnN0IHN0YWNrVHJhY2VXYXJuaW5nID0gKG1lc3NhZ2UpID0+IHtcbiAgICByZXR1cm4gU3RyaW5nKG5ldyBUeXBlRXJyb3IobWVzc2FnZSkuc3RhY2sgfHwgbWVzc2FnZSlcbiAgICAgICAgLnNwbGl0KFwiXFxuXCIpXG4gICAgICAgIC5zbGljZSgwLCA1KVxuICAgICAgICAuZmlsdGVyKChzKSA9PiAhcy5pbmNsdWRlcyhcInN0YWNrVHJhY2VXYXJuaW5nXCIpKVxuICAgICAgICAuam9pbihcIlxcblwiKTtcbn07XG5leHBvcnQgY29uc3QgbG9nZ2VyID0ge1xuICAgIHdhcm46IGNvbnNvbGUud2Fybixcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/parse-utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/quote-header.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/quote-header.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   quoteHeader: () => (/* binding */ quoteHeader)\n/* harmony export */ });\nfunction quoteHeader(part) {\n    if (part.includes(\",\") || part.includes('\"')) {\n        part = `\"${part.replace(/\"/g, '\\\\\"')}\"`;\n    }\n    return part;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NlcmRlL3F1b3RlLWhlYWRlci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBLG1CQUFtQiwwQkFBMEI7QUFDN0M7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NlcmRlL3F1b3RlLWhlYWRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gcXVvdGVIZWFkZXIocGFydCkge1xuICAgIGlmIChwYXJ0LmluY2x1ZGVzKFwiLFwiKSB8fCBwYXJ0LmluY2x1ZGVzKCdcIicpKSB7XG4gICAgICAgIHBhcnQgPSBgXCIke3BhcnQucmVwbGFjZSgvXCIvZywgJ1xcXFxcIicpfVwiYDtcbiAgICB9XG4gICAgcmV0dXJuIHBhcnQ7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/quote-header.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/split-every.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/split-every.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   splitEvery: () => (/* binding */ splitEvery)\n/* harmony export */ });\nfunction splitEvery(value, delimiter, numDelimiters) {\n    if (numDelimiters <= 0 || !Number.isInteger(numDelimiters)) {\n        throw new Error(\"Invalid number of delimiters (\" + numDelimiters + \") for splitEvery.\");\n    }\n    const segments = value.split(delimiter);\n    if (numDelimiters === 1) {\n        return segments;\n    }\n    const compoundSegments = [];\n    let currentSegment = \"\";\n    for (let i = 0; i < segments.length; i++) {\n        if (currentSegment === \"\") {\n            currentSegment = segments[i];\n        }\n        else {\n            currentSegment += delimiter + segments[i];\n        }\n        if ((i + 1) % numDelimiters === 0) {\n            compoundSegments.push(currentSegment);\n            currentSegment = \"\";\n        }\n    }\n    if (currentSegment !== \"\") {\n        compoundSegments.push(currentSegment);\n    }\n    return compoundSegments;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NlcmRlL3NwbGl0LWV2ZXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixxQkFBcUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvcmVAMy41LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29yZS9kaXN0LWVzL3N1Ym1vZHVsZXMvc2VyZGUvc3BsaXQtZXZlcnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHNwbGl0RXZlcnkodmFsdWUsIGRlbGltaXRlciwgbnVtRGVsaW1pdGVycykge1xuICAgIGlmIChudW1EZWxpbWl0ZXJzIDw9IDAgfHwgIU51bWJlci5pc0ludGVnZXIobnVtRGVsaW1pdGVycykpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCBudW1iZXIgb2YgZGVsaW1pdGVycyAoXCIgKyBudW1EZWxpbWl0ZXJzICsgXCIpIGZvciBzcGxpdEV2ZXJ5LlwiKTtcbiAgICB9XG4gICAgY29uc3Qgc2VnbWVudHMgPSB2YWx1ZS5zcGxpdChkZWxpbWl0ZXIpO1xuICAgIGlmIChudW1EZWxpbWl0ZXJzID09PSAxKSB7XG4gICAgICAgIHJldHVybiBzZWdtZW50cztcbiAgICB9XG4gICAgY29uc3QgY29tcG91bmRTZWdtZW50cyA9IFtdO1xuICAgIGxldCBjdXJyZW50U2VnbWVudCA9IFwiXCI7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzZWdtZW50cy5sZW5ndGg7IGkrKykge1xuICAgICAgICBpZiAoY3VycmVudFNlZ21lbnQgPT09IFwiXCIpIHtcbiAgICAgICAgICAgIGN1cnJlbnRTZWdtZW50ID0gc2VnbWVudHNbaV07XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBjdXJyZW50U2VnbWVudCArPSBkZWxpbWl0ZXIgKyBzZWdtZW50c1tpXTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoKGkgKyAxKSAlIG51bURlbGltaXRlcnMgPT09IDApIHtcbiAgICAgICAgICAgIGNvbXBvdW5kU2VnbWVudHMucHVzaChjdXJyZW50U2VnbWVudCk7XG4gICAgICAgICAgICBjdXJyZW50U2VnbWVudCA9IFwiXCI7XG4gICAgICAgIH1cbiAgICB9XG4gICAgaWYgKGN1cnJlbnRTZWdtZW50ICE9PSBcIlwiKSB7XG4gICAgICAgIGNvbXBvdW5kU2VnbWVudHMucHVzaChjdXJyZW50U2VnbWVudCk7XG4gICAgfVxuICAgIHJldHVybiBjb21wb3VuZFNlZ21lbnRzO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/split-every.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/split-header.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/split-header.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   splitHeader: () => (/* binding */ splitHeader)\n/* harmony export */ });\nconst splitHeader = (value) => {\n    const z = value.length;\n    const values = [];\n    let withinQuotes = false;\n    let prevChar = undefined;\n    let anchor = 0;\n    for (let i = 0; i < z; ++i) {\n        const char = value[i];\n        switch (char) {\n            case `\"`:\n                if (prevChar !== \"\\\\\") {\n                    withinQuotes = !withinQuotes;\n                }\n                break;\n            case \",\":\n                if (!withinQuotes) {\n                    values.push(value.slice(anchor, i));\n                    anchor = i + 1;\n                }\n                break;\n            default:\n        }\n        prevChar = char;\n    }\n    values.push(value.slice(anchor));\n    return values.map((v) => {\n        v = v.trim();\n        const z = v.length;\n        if (z < 2) {\n            return v;\n        }\n        if (v[0] === `\"` && v[z - 1] === `\"`) {\n            v = v.slice(1, z - 1);\n        }\n        return v.replace(/\\\\\"/g, '\"');\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/split-header.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/value/NumericValue.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/value/NumericValue.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NumericValue: () => (/* binding */ NumericValue),\n/* harmony export */   nv: () => (/* binding */ nv)\n/* harmony export */ });\nclass NumericValue {\n    constructor(string, type) {\n        this.string = string;\n        this.type = type;\n    }\n}\nfunction nv(string) {\n    return new NumericValue(string, \"bigDecimal\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy9zdWJtb2R1bGVzL3NlcmRlL3ZhbHVlL051bWVyaWNWYWx1ZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29yZUAzLjUuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb3JlL2Rpc3QtZXMvc3VibW9kdWxlcy9zZXJkZS92YWx1ZS9OdW1lcmljVmFsdWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIE51bWVyaWNWYWx1ZSB7XG4gICAgY29uc3RydWN0b3Ioc3RyaW5nLCB0eXBlKSB7XG4gICAgICAgIHRoaXMuc3RyaW5nID0gc3RyaW5nO1xuICAgICAgICB0aGlzLnR5cGUgPSB0eXBlO1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBudihzdHJpbmcpIHtcbiAgICByZXR1cm4gbmV3IE51bWVyaWNWYWx1ZShzdHJpbmcsIFwiYmlnRGVjaW1hbFwiKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/submodules/serde/value/NumericValue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/DefaultIdentityProviderConfig.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/DefaultIdentityProviderConfig.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultIdentityProviderConfig: () => (/* binding */ DefaultIdentityProviderConfig)\n/* harmony export */ });\nclass DefaultIdentityProviderConfig {\n    constructor(config) {\n        this.authSchemes = new Map();\n        for (const [key, value] of Object.entries(config)) {\n            if (value !== undefined) {\n                this.authSchemes.set(key, value);\n            }\n        }\n    }\n    getIdentityProvider(schemeId) {\n        return this.authSchemes.get(schemeId);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy91dGlsLWlkZW50aXR5LWFuZC1hdXRoL0RlZmF1bHRJZGVudGl0eVByb3ZpZGVyQ29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29yZUAzLjUuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb3JlL2Rpc3QtZXMvdXRpbC1pZGVudGl0eS1hbmQtYXV0aC9EZWZhdWx0SWRlbnRpdHlQcm92aWRlckNvbmZpZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgRGVmYXVsdElkZW50aXR5UHJvdmlkZXJDb25maWcge1xuICAgIGNvbnN0cnVjdG9yKGNvbmZpZykge1xuICAgICAgICB0aGlzLmF1dGhTY2hlbWVzID0gbmV3IE1hcCgpO1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhjb25maWcpKSB7XG4gICAgICAgICAgICBpZiAodmFsdWUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgIHRoaXMuYXV0aFNjaGVtZXMuc2V0KGtleSwgdmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGdldElkZW50aXR5UHJvdmlkZXIoc2NoZW1lSWQpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuYXV0aFNjaGVtZXMuZ2V0KHNjaGVtZUlkKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/DefaultIdentityProviderConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/httpApiKeyAuth.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/httpApiKeyAuth.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpApiKeyAuthSigner: () => (/* binding */ HttpApiKeyAuthSigner)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/types */ \"(rsc)/./node_modules/.pnpm/@smithy+types@4.3.1/node_modules/@smithy/types/dist-es/index.js\");\n\n\nclass HttpApiKeyAuthSigner {\n    async sign(httpRequest, identity, signingProperties) {\n        if (!signingProperties) {\n            throw new Error(\"request could not be signed with `apiKey` since the `name` and `in` signer properties are missing\");\n        }\n        if (!signingProperties.name) {\n            throw new Error(\"request could not be signed with `apiKey` since the `name` signer property is missing\");\n        }\n        if (!signingProperties.in) {\n            throw new Error(\"request could not be signed with `apiKey` since the `in` signer property is missing\");\n        }\n        if (!identity.apiKey) {\n            throw new Error(\"request could not be signed with `apiKey` since the `apiKey` is not defined\");\n        }\n        const clonedRequest = _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.clone(httpRequest);\n        if (signingProperties.in === _smithy_types__WEBPACK_IMPORTED_MODULE_1__.HttpApiKeyAuthLocation.QUERY) {\n            clonedRequest.query[signingProperties.name] = identity.apiKey;\n        }\n        else if (signingProperties.in === _smithy_types__WEBPACK_IMPORTED_MODULE_1__.HttpApiKeyAuthLocation.HEADER) {\n            clonedRequest.headers[signingProperties.name] = signingProperties.scheme\n                ? `${signingProperties.scheme} ${identity.apiKey}`\n                : identity.apiKey;\n        }\n        else {\n            throw new Error(\"request can only be signed with `apiKey` locations `query` or `header`, \" +\n                \"but found: `\" +\n                signingProperties.in +\n                \"`\");\n        }\n        return clonedRequest;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/httpApiKeyAuth.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/httpBearerAuth.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/httpBearerAuth.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpBearerAuthSigner: () => (/* binding */ HttpBearerAuthSigner)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n\nclass HttpBearerAuthSigner {\n    async sign(httpRequest, identity, signingProperties) {\n        const clonedRequest = _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.clone(httpRequest);\n        if (!identity.token) {\n            throw new Error(\"request could not be signed with `token` since the `token` is not defined\");\n        }\n        clonedRequest.headers[\"Authorization\"] = `Bearer ${identity.token}`;\n        return clonedRequest;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy91dGlsLWlkZW50aXR5LWFuZC1hdXRoL2h0dHBBdXRoU2NoZW1lcy9odHRwQmVhcmVyQXV0aC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFvRDtBQUM3QztBQUNQO0FBQ0EsOEJBQThCLDhEQUFXO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLDJEQUEyRCxlQUFlO0FBQzFFO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29yZUAzLjUuMy9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb3JlL2Rpc3QtZXMvdXRpbC1pZGVudGl0eS1hbmQtYXV0aC9odHRwQXV0aFNjaGVtZXMvaHR0cEJlYXJlckF1dGguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSHR0cFJlcXVlc3QgfSBmcm9tIFwiQHNtaXRoeS9wcm90b2NvbC1odHRwXCI7XG5leHBvcnQgY2xhc3MgSHR0cEJlYXJlckF1dGhTaWduZXIge1xuICAgIGFzeW5jIHNpZ24oaHR0cFJlcXVlc3QsIGlkZW50aXR5LCBzaWduaW5nUHJvcGVydGllcykge1xuICAgICAgICBjb25zdCBjbG9uZWRSZXF1ZXN0ID0gSHR0cFJlcXVlc3QuY2xvbmUoaHR0cFJlcXVlc3QpO1xuICAgICAgICBpZiAoIWlkZW50aXR5LnRva2VuKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJyZXF1ZXN0IGNvdWxkIG5vdCBiZSBzaWduZWQgd2l0aCBgdG9rZW5gIHNpbmNlIHRoZSBgdG9rZW5gIGlzIG5vdCBkZWZpbmVkXCIpO1xuICAgICAgICB9XG4gICAgICAgIGNsb25lZFJlcXVlc3QuaGVhZGVyc1tcIkF1dGhvcml6YXRpb25cIl0gPSBgQmVhcmVyICR7aWRlbnRpdHkudG9rZW59YDtcbiAgICAgICAgcmV0dXJuIGNsb25lZFJlcXVlc3Q7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/httpBearerAuth.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/index.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/index.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpApiKeyAuthSigner: () => (/* reexport safe */ _httpApiKeyAuth__WEBPACK_IMPORTED_MODULE_0__.HttpApiKeyAuthSigner),\n/* harmony export */   HttpBearerAuthSigner: () => (/* reexport safe */ _httpBearerAuth__WEBPACK_IMPORTED_MODULE_1__.HttpBearerAuthSigner),\n/* harmony export */   NoAuthSigner: () => (/* reexport safe */ _noAuth__WEBPACK_IMPORTED_MODULE_2__.NoAuthSigner)\n/* harmony export */ });\n/* harmony import */ var _httpApiKeyAuth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./httpApiKeyAuth */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/httpApiKeyAuth.js\");\n/* harmony import */ var _httpBearerAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./httpBearerAuth */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/httpBearerAuth.js\");\n/* harmony import */ var _noAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./noAuth */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/noAuth.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy91dGlsLWlkZW50aXR5LWFuZC1hdXRoL2h0dHBBdXRoU2NoZW1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBaUM7QUFDQTtBQUNSIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy91dGlsLWlkZW50aXR5LWFuZC1hdXRoL2h0dHBBdXRoU2NoZW1lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9odHRwQXBpS2V5QXV0aFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vaHR0cEJlYXJlckF1dGhcIjtcbmV4cG9ydCAqIGZyb20gXCIuL25vQXV0aFwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/noAuth.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/noAuth.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoAuthSigner: () => (/* binding */ NoAuthSigner)\n/* harmony export */ });\nclass NoAuthSigner {\n    async sign(httpRequest, identity, signingProperties) {\n        return httpRequest;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy91dGlsLWlkZW50aXR5LWFuZC1hdXRoL2h0dHBBdXRoU2NoZW1lcy9ub0F1dGguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvcmVAMy41LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29yZS9kaXN0LWVzL3V0aWwtaWRlbnRpdHktYW5kLWF1dGgvaHR0cEF1dGhTY2hlbWVzL25vQXV0aC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgTm9BdXRoU2lnbmVyIHtcbiAgICBhc3luYyBzaWduKGh0dHBSZXF1ZXN0LCBpZGVudGl0eSwgc2lnbmluZ1Byb3BlcnRpZXMpIHtcbiAgICAgICAgcmV0dXJuIGh0dHBSZXF1ZXN0O1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/noAuth.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultIdentityProviderConfig: () => (/* reexport safe */ _DefaultIdentityProviderConfig__WEBPACK_IMPORTED_MODULE_0__.DefaultIdentityProviderConfig),\n/* harmony export */   EXPIRATION_MS: () => (/* reexport safe */ _memoizeIdentityProvider__WEBPACK_IMPORTED_MODULE_2__.EXPIRATION_MS),\n/* harmony export */   HttpApiKeyAuthSigner: () => (/* reexport safe */ _httpAuthSchemes__WEBPACK_IMPORTED_MODULE_1__.HttpApiKeyAuthSigner),\n/* harmony export */   HttpBearerAuthSigner: () => (/* reexport safe */ _httpAuthSchemes__WEBPACK_IMPORTED_MODULE_1__.HttpBearerAuthSigner),\n/* harmony export */   NoAuthSigner: () => (/* reexport safe */ _httpAuthSchemes__WEBPACK_IMPORTED_MODULE_1__.NoAuthSigner),\n/* harmony export */   createIsIdentityExpiredFunction: () => (/* reexport safe */ _memoizeIdentityProvider__WEBPACK_IMPORTED_MODULE_2__.createIsIdentityExpiredFunction),\n/* harmony export */   doesIdentityRequireRefresh: () => (/* reexport safe */ _memoizeIdentityProvider__WEBPACK_IMPORTED_MODULE_2__.doesIdentityRequireRefresh),\n/* harmony export */   isIdentityExpired: () => (/* reexport safe */ _memoizeIdentityProvider__WEBPACK_IMPORTED_MODULE_2__.isIdentityExpired),\n/* harmony export */   memoizeIdentityProvider: () => (/* reexport safe */ _memoizeIdentityProvider__WEBPACK_IMPORTED_MODULE_2__.memoizeIdentityProvider)\n/* harmony export */ });\n/* harmony import */ var _DefaultIdentityProviderConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./DefaultIdentityProviderConfig */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/DefaultIdentityProviderConfig.js\");\n/* harmony import */ var _httpAuthSchemes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./httpAuthSchemes */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/httpAuthSchemes/index.js\");\n/* harmony import */ var _memoizeIdentityProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./memoizeIdentityProvider */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/memoizeIdentityProvider.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb3JlQDMuNS4zL25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvcmUvZGlzdC1lcy91dGlsLWlkZW50aXR5LWFuZC1hdXRoL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFnRDtBQUNkO0FBQ1EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvcmVAMy41LjMvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29yZS9kaXN0LWVzL3V0aWwtaWRlbnRpdHktYW5kLWF1dGgvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vRGVmYXVsdElkZW50aXR5UHJvdmlkZXJDb25maWdcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2h0dHBBdXRoU2NoZW1lc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vbWVtb2l6ZUlkZW50aXR5UHJvdmlkZXJcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/memoizeIdentityProvider.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/memoizeIdentityProvider.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EXPIRATION_MS: () => (/* binding */ EXPIRATION_MS),\n/* harmony export */   createIsIdentityExpiredFunction: () => (/* binding */ createIsIdentityExpiredFunction),\n/* harmony export */   doesIdentityRequireRefresh: () => (/* binding */ doesIdentityRequireRefresh),\n/* harmony export */   isIdentityExpired: () => (/* binding */ isIdentityExpired),\n/* harmony export */   memoizeIdentityProvider: () => (/* binding */ memoizeIdentityProvider)\n/* harmony export */ });\nconst createIsIdentityExpiredFunction = (expirationMs) => (identity) => doesIdentityRequireRefresh(identity) && identity.expiration.getTime() - Date.now() < expirationMs;\nconst EXPIRATION_MS = 300000;\nconst isIdentityExpired = createIsIdentityExpiredFunction(EXPIRATION_MS);\nconst doesIdentityRequireRefresh = (identity) => identity.expiration !== undefined;\nconst memoizeIdentityProvider = (provider, isExpired, requiresRefresh) => {\n    if (provider === undefined) {\n        return undefined;\n    }\n    const normalizedProvider = typeof provider !== \"function\" ? async () => Promise.resolve(provider) : provider;\n    let resolved;\n    let pending;\n    let hasResult;\n    let isConstant = false;\n    const coalesceProvider = async (options) => {\n        if (!pending) {\n            pending = normalizedProvider(options);\n        }\n        try {\n            resolved = await pending;\n            hasResult = true;\n            isConstant = false;\n        }\n        finally {\n            pending = undefined;\n        }\n        return resolved;\n    };\n    if (isExpired === undefined) {\n        return async (options) => {\n            if (!hasResult || options?.forceRefresh) {\n                resolved = await coalesceProvider(options);\n            }\n            return resolved;\n        };\n    }\n    return async (options) => {\n        if (!hasResult || options?.forceRefresh) {\n            resolved = await coalesceProvider(options);\n        }\n        if (isConstant) {\n            return resolved;\n        }\n        if (!requiresRefresh(resolved)) {\n            isConstant = true;\n            return resolved;\n        }\n        if (isExpired(resolved)) {\n            await coalesceProvider(options);\n            return resolved;\n        }\n        return resolved;\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/util-identity-and-auth/memoizeIdentityProvider.js\n");

/***/ })

};
;