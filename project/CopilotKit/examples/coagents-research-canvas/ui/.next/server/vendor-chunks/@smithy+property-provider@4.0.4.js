"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+property-provider@4.0.4";
exports.ids = ["vendor-chunks/@smithy+property-provider@4.0.4"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/CredentialsProviderError.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/CredentialsProviderError.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CredentialsProviderError: () => (/* binding */ CredentialsProviderError)\n/* harmony export */ });\n/* harmony import */ var _ProviderError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ProviderError */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/ProviderError.js\");\n\nclass CredentialsProviderError extends _ProviderError__WEBPACK_IMPORTED_MODULE_0__.ProviderError {\n    constructor(message, options = true) {\n        super(message, options);\n        this.name = \"CredentialsProviderError\";\n        Object.setPrototypeOf(this, CredentialsProviderError.prototype);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm9wZXJ0eS1wcm92aWRlckA0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9wcm9wZXJ0eS1wcm92aWRlci9kaXN0LWVzL0NyZWRlbnRpYWxzUHJvdmlkZXJFcnJvci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDtBQUN6Qyx1Q0FBdUMseURBQWE7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm9wZXJ0eS1wcm92aWRlckA0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9wcm9wZXJ0eS1wcm92aWRlci9kaXN0LWVzL0NyZWRlbnRpYWxzUHJvdmlkZXJFcnJvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcm92aWRlckVycm9yIH0gZnJvbSBcIi4vUHJvdmlkZXJFcnJvclwiO1xuZXhwb3J0IGNsYXNzIENyZWRlbnRpYWxzUHJvdmlkZXJFcnJvciBleHRlbmRzIFByb3ZpZGVyRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIG9wdGlvbnMgPSB0cnVlKSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UsIG9wdGlvbnMpO1xuICAgICAgICB0aGlzLm5hbWUgPSBcIkNyZWRlbnRpYWxzUHJvdmlkZXJFcnJvclwiO1xuICAgICAgICBPYmplY3Quc2V0UHJvdG90eXBlT2YodGhpcywgQ3JlZGVudGlhbHNQcm92aWRlckVycm9yLnByb3RvdHlwZSk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/CredentialsProviderError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/ProviderError.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/ProviderError.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProviderError: () => (/* binding */ ProviderError)\n/* harmony export */ });\nclass ProviderError extends Error {\n    constructor(message, options = true) {\n        let logger;\n        let tryNextLink = true;\n        if (typeof options === \"boolean\") {\n            logger = undefined;\n            tryNextLink = options;\n        }\n        else if (options != null && typeof options === \"object\") {\n            logger = options.logger;\n            tryNextLink = options.tryNextLink ?? true;\n        }\n        super(message);\n        this.name = \"ProviderError\";\n        this.tryNextLink = tryNextLink;\n        Object.setPrototypeOf(this, ProviderError.prototype);\n        logger?.debug?.(`@smithy/property-provider ${tryNextLink ? \"->\" : \"(!)\"} ${message}`);\n    }\n    static from(error, options = true) {\n        return Object.assign(new this(error.message, options), error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm9wZXJ0eS1wcm92aWRlckA0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9wcm9wZXJ0eS1wcm92aWRlci9kaXN0LWVzL1Byb3ZpZGVyRXJyb3IuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELDRCQUE0QixFQUFFLFFBQVE7QUFDM0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrcHJvcGVydHktcHJvdmlkZXJANC4wLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvcHJvcGVydHktcHJvdmlkZXIvZGlzdC1lcy9Qcm92aWRlckVycm9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBQcm92aWRlckVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIG9wdGlvbnMgPSB0cnVlKSB7XG4gICAgICAgIGxldCBsb2dnZXI7XG4gICAgICAgIGxldCB0cnlOZXh0TGluayA9IHRydWU7XG4gICAgICAgIGlmICh0eXBlb2Ygb3B0aW9ucyA9PT0gXCJib29sZWFuXCIpIHtcbiAgICAgICAgICAgIGxvZ2dlciA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIHRyeU5leHRMaW5rID0gb3B0aW9ucztcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChvcHRpb25zICE9IG51bGwgJiYgdHlwZW9mIG9wdGlvbnMgPT09IFwib2JqZWN0XCIpIHtcbiAgICAgICAgICAgIGxvZ2dlciA9IG9wdGlvbnMubG9nZ2VyO1xuICAgICAgICAgICAgdHJ5TmV4dExpbmsgPSBvcHRpb25zLnRyeU5leHRMaW5rID8/IHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICAgIHRoaXMubmFtZSA9IFwiUHJvdmlkZXJFcnJvclwiO1xuICAgICAgICB0aGlzLnRyeU5leHRMaW5rID0gdHJ5TmV4dExpbms7XG4gICAgICAgIE9iamVjdC5zZXRQcm90b3R5cGVPZih0aGlzLCBQcm92aWRlckVycm9yLnByb3RvdHlwZSk7XG4gICAgICAgIGxvZ2dlcj8uZGVidWc/LihgQHNtaXRoeS9wcm9wZXJ0eS1wcm92aWRlciAke3RyeU5leHRMaW5rID8gXCItPlwiIDogXCIoISlcIn0gJHttZXNzYWdlfWApO1xuICAgIH1cbiAgICBzdGF0aWMgZnJvbShlcnJvciwgb3B0aW9ucyA9IHRydWUpIHtcbiAgICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24obmV3IHRoaXMoZXJyb3IubWVzc2FnZSwgb3B0aW9ucyksIGVycm9yKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/ProviderError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/TokenProviderError.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/TokenProviderError.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenProviderError: () => (/* binding */ TokenProviderError)\n/* harmony export */ });\n/* harmony import */ var _ProviderError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ProviderError */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/ProviderError.js\");\n\nclass TokenProviderError extends _ProviderError__WEBPACK_IMPORTED_MODULE_0__.ProviderError {\n    constructor(message, options = true) {\n        super(message, options);\n        this.name = \"TokenProviderError\";\n        Object.setPrototypeOf(this, TokenProviderError.prototype);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm9wZXJ0eS1wcm92aWRlckA0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9wcm9wZXJ0eS1wcm92aWRlci9kaXN0LWVzL1Rva2VuUHJvdmlkZXJFcnJvci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDtBQUN6QyxpQ0FBaUMseURBQWE7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm9wZXJ0eS1wcm92aWRlckA0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9wcm9wZXJ0eS1wcm92aWRlci9kaXN0LWVzL1Rva2VuUHJvdmlkZXJFcnJvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcm92aWRlckVycm9yIH0gZnJvbSBcIi4vUHJvdmlkZXJFcnJvclwiO1xuZXhwb3J0IGNsYXNzIFRva2VuUHJvdmlkZXJFcnJvciBleHRlbmRzIFByb3ZpZGVyRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIG9wdGlvbnMgPSB0cnVlKSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UsIG9wdGlvbnMpO1xuICAgICAgICB0aGlzLm5hbWUgPSBcIlRva2VuUHJvdmlkZXJFcnJvclwiO1xuICAgICAgICBPYmplY3Quc2V0UHJvdG90eXBlT2YodGhpcywgVG9rZW5Qcm92aWRlckVycm9yLnByb3RvdHlwZSk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/TokenProviderError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/chain.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/chain.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chain: () => (/* binding */ chain)\n/* harmony export */ });\n/* harmony import */ var _ProviderError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ProviderError */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/ProviderError.js\");\n\nconst chain = (...providers) => async () => {\n    if (providers.length === 0) {\n        throw new _ProviderError__WEBPACK_IMPORTED_MODULE_0__.ProviderError(\"No providers in chain\");\n    }\n    let lastProviderError;\n    for (const provider of providers) {\n        try {\n            const credentials = await provider();\n            return credentials;\n        }\n        catch (err) {\n            lastProviderError = err;\n            if (err?.tryNextLink) {\n                continue;\n            }\n            throw err;\n        }\n    }\n    throw lastProviderError;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm9wZXJ0eS1wcm92aWRlckA0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9wcm9wZXJ0eS1wcm92aWRlci9kaXN0LWVzL2NoYWluLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdEO0FBQ3pDO0FBQ1A7QUFDQSxrQkFBa0IseURBQWE7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrcHJvcGVydHktcHJvdmlkZXJANC4wLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvcHJvcGVydHktcHJvdmlkZXIvZGlzdC1lcy9jaGFpbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcm92aWRlckVycm9yIH0gZnJvbSBcIi4vUHJvdmlkZXJFcnJvclwiO1xuZXhwb3J0IGNvbnN0IGNoYWluID0gKC4uLnByb3ZpZGVycykgPT4gYXN5bmMgKCkgPT4ge1xuICAgIGlmIChwcm92aWRlcnMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHRocm93IG5ldyBQcm92aWRlckVycm9yKFwiTm8gcHJvdmlkZXJzIGluIGNoYWluXCIpO1xuICAgIH1cbiAgICBsZXQgbGFzdFByb3ZpZGVyRXJyb3I7XG4gICAgZm9yIChjb25zdCBwcm92aWRlciBvZiBwcm92aWRlcnMpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IGNyZWRlbnRpYWxzID0gYXdhaXQgcHJvdmlkZXIoKTtcbiAgICAgICAgICAgIHJldHVybiBjcmVkZW50aWFscztcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICBsYXN0UHJvdmlkZXJFcnJvciA9IGVycjtcbiAgICAgICAgICAgIGlmIChlcnI/LnRyeU5leHRMaW5rKSB7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aHJvdyBlcnI7XG4gICAgICAgIH1cbiAgICB9XG4gICAgdGhyb3cgbGFzdFByb3ZpZGVyRXJyb3I7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/chain.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/fromStatic.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/fromStatic.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromStatic: () => (/* binding */ fromStatic)\n/* harmony export */ });\nconst fromStatic = (staticValue) => () => Promise.resolve(staticValue);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm9wZXJ0eS1wcm92aWRlckA0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9wcm9wZXJ0eS1wcm92aWRlci9kaXN0LWVzL2Zyb21TdGF0aWMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm9wZXJ0eS1wcm92aWRlckA0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9wcm9wZXJ0eS1wcm92aWRlci9kaXN0LWVzL2Zyb21TdGF0aWMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGZyb21TdGF0aWMgPSAoc3RhdGljVmFsdWUpID0+ICgpID0+IFByb21pc2UucmVzb2x2ZShzdGF0aWNWYWx1ZSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/fromStatic.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CredentialsProviderError: () => (/* reexport safe */ _CredentialsProviderError__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError),\n/* harmony export */   ProviderError: () => (/* reexport safe */ _ProviderError__WEBPACK_IMPORTED_MODULE_1__.ProviderError),\n/* harmony export */   TokenProviderError: () => (/* reexport safe */ _TokenProviderError__WEBPACK_IMPORTED_MODULE_2__.TokenProviderError),\n/* harmony export */   chain: () => (/* reexport safe */ _chain__WEBPACK_IMPORTED_MODULE_3__.chain),\n/* harmony export */   fromStatic: () => (/* reexport safe */ _fromStatic__WEBPACK_IMPORTED_MODULE_4__.fromStatic),\n/* harmony export */   memoize: () => (/* reexport safe */ _memoize__WEBPACK_IMPORTED_MODULE_5__.memoize)\n/* harmony export */ });\n/* harmony import */ var _CredentialsProviderError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CredentialsProviderError */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/CredentialsProviderError.js\");\n/* harmony import */ var _ProviderError__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ProviderError */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/ProviderError.js\");\n/* harmony import */ var _TokenProviderError__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TokenProviderError */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/TokenProviderError.js\");\n/* harmony import */ var _chain__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chain */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/chain.js\");\n/* harmony import */ var _fromStatic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./fromStatic */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/fromStatic.js\");\n/* harmony import */ var _memoize__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./memoize */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/memoize.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm9wZXJ0eS1wcm92aWRlckA0LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9wcm9wZXJ0eS1wcm92aWRlci9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUEyQztBQUNYO0FBQ0s7QUFDYjtBQUNLO0FBQ0giLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3Byb3BlcnR5LXByb3ZpZGVyQDQuMC40L25vZGVfbW9kdWxlcy9Ac21pdGh5L3Byb3BlcnR5LXByb3ZpZGVyL2Rpc3QtZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vQ3JlZGVudGlhbHNQcm92aWRlckVycm9yXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9Qcm92aWRlckVycm9yXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9Ub2tlblByb3ZpZGVyRXJyb3JcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2NoYWluXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9mcm9tU3RhdGljXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9tZW1vaXplXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/memoize.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/memoize.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoize: () => (/* binding */ memoize)\n/* harmony export */ });\nconst memoize = (provider, isExpired, requiresRefresh) => {\n    let resolved;\n    let pending;\n    let hasResult;\n    let isConstant = false;\n    const coalesceProvider = async () => {\n        if (!pending) {\n            pending = provider();\n        }\n        try {\n            resolved = await pending;\n            hasResult = true;\n            isConstant = false;\n        }\n        finally {\n            pending = undefined;\n        }\n        return resolved;\n    };\n    if (isExpired === undefined) {\n        return async (options) => {\n            if (!hasResult || options?.forceRefresh) {\n                resolved = await coalesceProvider();\n            }\n            return resolved;\n        };\n    }\n    return async (options) => {\n        if (!hasResult || options?.forceRefresh) {\n            resolved = await coalesceProvider();\n        }\n        if (isConstant) {\n            return resolved;\n        }\n        if (requiresRefresh && !requiresRefresh(resolved)) {\n            isConstant = true;\n            return resolved;\n        }\n        if (isExpired(resolved)) {\n            await coalesceProvider();\n            return resolved;\n        }\n        return resolved;\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/memoize.js\n");

/***/ })

};
;