"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cross-inspect@1.0.1";
exports.ids = ["vendor-chunks/cross-inspect@1.0.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/cross-inspect@1.0.1/node_modules/cross-inspect/cjs/index.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/cross-inspect@1.0.1/node_modules/cross-inspect/cjs/index.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.inspect = void 0;\n// Taken from graphql-js\n// https://github.com/graphql/graphql-js/blob/main/src/jsutils/inspect.ts\nconst MAX_RECURSIVE_DEPTH = 3;\n/**\n * Used to print values in error messages.\n */\nfunction inspect(value) {\n    return formatValue(value, []);\n}\nexports.inspect = inspect;\nfunction formatValue(value, seenValues) {\n    switch (typeof value) {\n        case 'string':\n            return JSON.stringify(value);\n        case 'function':\n            return value.name ? `[function ${value.name}]` : '[function]';\n        case 'object':\n            return formatObjectValue(value, seenValues);\n        default:\n            return String(value);\n    }\n}\nfunction formatError(value) {\n    // eslint-disable-next-line no-constant-condition\n    if ((value.name = 'GraphQLError')) {\n        return value.toString();\n    }\n    return `${value.name}: ${value.message};\\n ${value.stack}`;\n}\nfunction formatObjectValue(value, previouslySeenValues) {\n    if (value === null) {\n        return 'null';\n    }\n    if (value instanceof Error) {\n        if (value.name === 'AggregateError') {\n            return (formatError(value) +\n                '\\n' +\n                formatArray(value.errors, previouslySeenValues));\n        }\n        return formatError(value);\n    }\n    if (previouslySeenValues.includes(value)) {\n        return '[Circular]';\n    }\n    const seenValues = [...previouslySeenValues, value];\n    if (isJSONable(value)) {\n        const jsonValue = value.toJSON();\n        // check for infinite recursion\n        if (jsonValue !== value) {\n            return typeof jsonValue === 'string' ? jsonValue : formatValue(jsonValue, seenValues);\n        }\n    }\n    else if (Array.isArray(value)) {\n        return formatArray(value, seenValues);\n    }\n    return formatObject(value, seenValues);\n}\nfunction isJSONable(value) {\n    return typeof value.toJSON === 'function';\n}\nfunction formatObject(object, seenValues) {\n    const entries = Object.entries(object);\n    if (entries.length === 0) {\n        return '{}';\n    }\n    if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n        return '[' + getObjectTag(object) + ']';\n    }\n    const properties = entries.map(([key, value]) => key + ': ' + formatValue(value, seenValues));\n    return '{ ' + properties.join(', ') + ' }';\n}\nfunction formatArray(array, seenValues) {\n    if (array.length === 0) {\n        return '[]';\n    }\n    if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n        return '[Array]';\n    }\n    const len = array.length;\n    const items = [];\n    for (let i = 0; i < len; ++i) {\n        items.push(formatValue(array[i], seenValues));\n    }\n    return '[' + items.join(', ') + ']';\n}\nfunction getObjectTag(object) {\n    const tag = Object.prototype.toString\n        .call(object)\n        .replace(/^\\[object /, '')\n        .replace(/]$/, '');\n    if (tag === 'Object' && typeof object.constructor === 'function') {\n        const name = object.constructor.name;\n        if (typeof name === 'string' && name !== '') {\n            return name;\n        }\n    }\n    return tag;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/cross-inspect@1.0.1/node_modules/cross-inspect/cjs/index.js\n");

/***/ })

};
;