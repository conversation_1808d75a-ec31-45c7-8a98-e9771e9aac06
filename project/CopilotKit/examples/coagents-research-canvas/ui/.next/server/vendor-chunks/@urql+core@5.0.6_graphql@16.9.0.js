"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@urql+core@5.0.6_graphql@16.9.0";
exports.ids = ["vendor-chunks/@urql+core@5.0.6_graphql@16.9.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@urql+core@5.0.6_graphql@16.9.0/node_modules/@urql/core/dist/urql-core-chunk.mjs":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@urql+core@5.0.6_graphql@16.9.0/node_modules/@urql/core/dist/urql-core-chunk.mjs ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   C: () => (/* binding */ CombinedError),\n/* harmony export */   a: () => (/* binding */ makeFetchBody),\n/* harmony export */   b: () => (/* binding */ makeErrorResult),\n/* harmony export */   c: () => (/* binding */ mergeResultPatch),\n/* harmony export */   d: () => (/* binding */ makeFetchURL),\n/* harmony export */   e: () => (/* binding */ makeFetchOptions),\n/* harmony export */   f: () => (/* binding */ makeFetchSource),\n/* harmony export */   g: () => (/* binding */ getOperationType),\n/* harmony export */   h: () => (/* binding */ createRequest),\n/* harmony export */   i: () => (/* binding */ stringifyVariables),\n/* harmony export */   k: () => (/* binding */ keyDocument),\n/* harmony export */   m: () => (/* binding */ makeResult),\n/* harmony export */   s: () => (/* binding */ stringifyDocument)\n/* harmony export */ });\n/* harmony import */ var _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @0no-co/graphql.web */ \"(ssr)/./node_modules/.pnpm/@0no-co+graphql.web@1.0.8_graphql@16.9.0/node_modules/@0no-co/graphql.web/dist/graphql.web.mjs\");\n/* harmony import */ var wonka__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! wonka */ \"(ssr)/./node_modules/.pnpm/wonka@6.3.4/node_modules/wonka/dist/wonka.mjs\");\n\n\n\n\nvar rehydrateGraphQlError = r => {\n  if (r && \"string\" == typeof r.message && (r.extensions || \"GraphQLError\" === r.name)) {\n    return r;\n  } else if (\"object\" == typeof r && \"string\" == typeof r.message) {\n    return new _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__.GraphQLError(r.message, r.nodes, r.source, r.positions, r.path, r, r.extensions || {});\n  } else {\n    return new _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__.GraphQLError(r);\n  }\n};\n\nclass CombinedError extends Error {\n  constructor(e) {\n    var r = (e.graphQLErrors || []).map(rehydrateGraphQlError);\n    var t = ((e, r) => {\n      var t = \"\";\n      if (e) {\n        return `[Network] ${e.message}`;\n      }\n      if (r) {\n        for (var a of r) {\n          if (t) {\n            t += \"\\n\";\n          }\n          t += `[GraphQL] ${a.message}`;\n        }\n      }\n      return t;\n    })(e.networkError, r);\n    super(t);\n    this.name = \"CombinedError\";\n    this.message = t;\n    this.graphQLErrors = r;\n    this.networkError = e.networkError;\n    this.response = e.response;\n  }\n  toString() {\n    return this.message;\n  }\n}\n\nvar phash = (e, r) => {\n  var t = 0 | (r || 5381);\n  for (var a = 0, o = 0 | e.length; a < o; a++) {\n    t = (t << 5) + t + e.charCodeAt(a);\n  }\n  return t;\n};\n\nvar i = new Set;\n\nvar f = new WeakMap;\n\nvar stringify = (e, r) => {\n  if (null === e || i.has(e)) {\n    return \"null\";\n  } else if (\"object\" != typeof e) {\n    return JSON.stringify(e) || \"\";\n  } else if (e.toJSON) {\n    return stringify(e.toJSON(), r);\n  } else if (Array.isArray(e)) {\n    var t = \"[\";\n    for (var a of e) {\n      if (t.length > 1) {\n        t += \",\";\n      }\n      t += stringify(a, r) || \"null\";\n    }\n    return t += \"]\";\n  } else if (!r && (l !== NoopConstructor && e instanceof l || c !== NoopConstructor && e instanceof c)) {\n    return \"null\";\n  }\n  var o = Object.keys(e).sort();\n  if (!o.length && e.constructor && Object.getPrototypeOf(e).constructor !== Object.prototype.constructor) {\n    var n = f.get(e) || Math.random().toString(36).slice(2);\n    f.set(e, n);\n    return stringify({\n      __key: n\n    }, r);\n  }\n  i.add(e);\n  var s = \"{\";\n  for (var d of o) {\n    var v = stringify(e[d], r);\n    if (v) {\n      if (s.length > 1) {\n        s += \",\";\n      }\n      s += stringify(d, r) + \":\" + v;\n    }\n  }\n  i.delete(e);\n  return s += \"}\";\n};\n\nvar extract = (e, r, t) => {\n  if (null == t || \"object\" != typeof t || t.toJSON || i.has(t)) {} else if (Array.isArray(t)) {\n    for (var a = 0, o = t.length; a < o; a++) {\n      extract(e, `${r}.${a}`, t[a]);\n    }\n  } else if (t instanceof l || t instanceof c) {\n    e.set(r, t);\n  } else {\n    i.add(t);\n    for (var n of Object.keys(t)) {\n      extract(e, `${r}.${n}`, t[n]);\n    }\n  }\n};\n\nvar stringifyVariables = (e, r) => {\n  i.clear();\n  return stringify(e, r || !1);\n};\n\nclass NoopConstructor {}\n\nvar l = \"undefined\" != typeof File ? File : NoopConstructor;\n\nvar c = \"undefined\" != typeof Blob ? Blob : NoopConstructor;\n\nvar d = /(\"{3}[\\s\\S]*\"{3}|\"(?:\\\\.|[^\"])*\")/g;\n\nvar v = /(?:#[^\\n\\r]+)?(?:[\\r\\n]+|$)/g;\n\nvar replaceOutsideStrings = (e, r) => r % 2 == 0 ? e.replace(v, \"\\n\") : e;\n\nvar sanitizeDocument = e => e.split(d).map(replaceOutsideStrings).join(\"\").trim();\n\nvar p = new Map;\n\nvar u = new Map;\n\nvar stringifyDocument = e => {\n  var t;\n  if (\"string\" == typeof e) {\n    t = sanitizeDocument(e);\n  } else if (e.loc && u.get(e.__key) === e) {\n    t = e.loc.source.body;\n  } else {\n    t = p.get(e) || sanitizeDocument((0,_0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__.print)(e));\n    p.set(e, t);\n  }\n  if (\"string\" != typeof e && !e.loc) {\n    e.loc = {\n      start: 0,\n      end: t.length,\n      source: {\n        body: t,\n        name: \"gql\",\n        locationOffset: {\n          line: 1,\n          column: 1\n        }\n      }\n    };\n  }\n  return t;\n};\n\nvar hashDocument = e => {\n  var r;\n  if (e.documentId) {\n    r = phash(e.documentId);\n  } else {\n    r = phash(stringifyDocument(e));\n    if (e.definitions) {\n      var t = getOperationName(e);\n      if (t) {\n        r = phash(`\\n# ${t}`, r);\n      }\n    }\n  }\n  return r;\n};\n\nvar keyDocument = e => {\n  var r;\n  var a;\n  if (\"string\" == typeof e) {\n    r = hashDocument(e);\n    a = u.get(r) || (0,_0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__.parse)(e, {\n      noLocation: !0\n    });\n  } else {\n    r = e.__key || hashDocument(e);\n    a = u.get(r) || e;\n  }\n  if (!a.loc) {\n    stringifyDocument(a);\n  }\n  a.__key = r;\n  u.set(r, a);\n  return a;\n};\n\nvar createRequest = (e, r, t) => {\n  var a = r || {};\n  var o = keyDocument(e);\n  var n = stringifyVariables(a, !0);\n  var s = o.__key;\n  if (\"{}\" !== n) {\n    s = phash(n, s);\n  }\n  return {\n    key: s,\n    query: o,\n    variables: a,\n    extensions: t\n  };\n};\n\nvar getOperationName = e => {\n  for (var r of e.definitions) {\n    if (r.kind === _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__.Kind.OPERATION_DEFINITION) {\n      return r.name ? r.name.value : void 0;\n    }\n  }\n};\n\nvar getOperationType = e => {\n  for (var r of e.definitions) {\n    if (r.kind === _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_0__.Kind.OPERATION_DEFINITION) {\n      return r.operation;\n    }\n  }\n};\n\nvar makeResult = (e, r, t) => {\n  if (!(\"data\" in r || \"errors\" in r && Array.isArray(r.errors))) {\n    throw new Error(\"No Content\");\n  }\n  var a = \"subscription\" === e.kind;\n  return {\n    operation: e,\n    data: r.data,\n    error: Array.isArray(r.errors) ? new CombinedError({\n      graphQLErrors: r.errors,\n      response: t\n    }) : void 0,\n    extensions: r.extensions ? {\n      ...r.extensions\n    } : void 0,\n    hasNext: null == r.hasNext ? a : r.hasNext,\n    stale: !1\n  };\n};\n\nvar deepMerge = (e, r) => {\n  if (\"object\" == typeof e && null != e) {\n    if (!e.constructor || e.constructor === Object || Array.isArray(e)) {\n      e = Array.isArray(e) ? [ ...e ] : {\n        ...e\n      };\n      for (var t of Object.keys(r)) {\n        e[t] = deepMerge(e[t], r[t]);\n      }\n      return e;\n    }\n  }\n  return r;\n};\n\nvar mergeResultPatch = (e, r, t, a) => {\n  var o = e.error ? e.error.graphQLErrors : [];\n  var n = !!e.extensions || !!(r.payload || r).extensions;\n  var s = {\n    ...e.extensions,\n    ...(r.payload || r).extensions\n  };\n  var i = r.incremental;\n  if (\"path\" in r) {\n    i = [ r ];\n  }\n  var f = {\n    data: e.data\n  };\n  if (i) {\n    var _loop = function(e) {\n      if (Array.isArray(e.errors)) {\n        o.push(...e.errors);\n      }\n      if (e.extensions) {\n        Object.assign(s, e.extensions);\n        n = !0;\n      }\n      var r = \"data\";\n      var t = f;\n      var i = [];\n      if (e.path) {\n        i = e.path;\n      } else if (a) {\n        var l = a.find((r => r.id === e.id));\n        if (e.subPath) {\n          i = [ ...l.path, ...e.subPath ];\n        } else {\n          i = l.path;\n        }\n      }\n      for (var c = 0, d = i.length; c < d; r = i[c++]) {\n        t = t[r] = Array.isArray(t[r]) ? [ ...t[r] ] : {\n          ...t[r]\n        };\n      }\n      if (e.items) {\n        var v = +r >= 0 ? r : 0;\n        for (var p = 0, u = e.items.length; p < u; p++) {\n          t[v + p] = deepMerge(t[v + p], e.items[p]);\n        }\n      } else if (void 0 !== e.data) {\n        t[r] = deepMerge(t[r], e.data);\n      }\n    };\n    for (var l of i) {\n      _loop(l);\n    }\n  } else {\n    f.data = (r.payload || r).data || e.data;\n    o = r.errors || r.payload && r.payload.errors || o;\n  }\n  return {\n    operation: e.operation,\n    data: f.data,\n    error: o.length ? new CombinedError({\n      graphQLErrors: o,\n      response: t\n    }) : void 0,\n    extensions: n ? s : void 0,\n    hasNext: null != r.hasNext ? r.hasNext : e.hasNext,\n    stale: !1\n  };\n};\n\nvar makeErrorResult = (e, r, t) => ({\n  operation: e,\n  data: void 0,\n  error: new CombinedError({\n    networkError: r,\n    response: t\n  }),\n  extensions: void 0,\n  hasNext: !1,\n  stale: !1\n});\n\nfunction makeFetchBody(e) {\n  var r = {\n    query: void 0,\n    documentId: void 0,\n    operationName: getOperationName(e.query),\n    variables: e.variables || void 0,\n    extensions: e.extensions\n  };\n  if (\"documentId\" in e.query && e.query.documentId && (!e.query.definitions || !e.query.definitions.length)) {\n    r.documentId = e.query.documentId;\n  } else if (!e.extensions || !e.extensions.persistedQuery || e.extensions.persistedQuery.miss) {\n    r.query = stringifyDocument(e.query);\n  }\n  return r;\n}\n\nvar makeFetchURL = (e, r) => {\n  var t = \"query\" === e.kind && e.context.preferGetMethod;\n  if (!t || !r) {\n    return e.context.url;\n  }\n  var a = splitOutSearchParams(e.context.url);\n  for (var o in r) {\n    var n = r[o];\n    if (n) {\n      a[1].set(o, \"object\" == typeof n ? stringifyVariables(n) : n);\n    }\n  }\n  var s = a.join(\"?\");\n  if (s.length > 2047 && \"force\" !== t) {\n    e.context.preferGetMethod = !1;\n    return e.context.url;\n  }\n  return s;\n};\n\nvar splitOutSearchParams = e => {\n  var r = e.indexOf(\"?\");\n  return r > -1 ? [ e.slice(0, r), new URLSearchParams(e.slice(r + 1)) ] : [ e, new URLSearchParams ];\n};\n\nvar serializeBody = (e, r) => {\n  if (r && !(\"query\" === e.kind && !!e.context.preferGetMethod)) {\n    var t = stringifyVariables(r);\n    var a = (e => {\n      var r = new Map;\n      if (l !== NoopConstructor || c !== NoopConstructor) {\n        i.clear();\n        extract(r, \"variables\", e);\n      }\n      return r;\n    })(r.variables);\n    if (a.size) {\n      var o = new FormData;\n      o.append(\"operations\", t);\n      o.append(\"map\", stringifyVariables({\n        ...[ ...a.keys() ].map((e => [ e ]))\n      }));\n      var n = 0;\n      for (var s of a.values()) {\n        o.append(\"\" + n++, s);\n      }\n      return o;\n    }\n    return t;\n  }\n};\n\nvar makeFetchOptions = (e, r) => {\n  var t = {\n    accept: \"subscription\" === e.kind ? \"text/event-stream, multipart/mixed\" : \"application/graphql-response+json, application/graphql+json, application/json, text/event-stream, multipart/mixed\"\n  };\n  var a = (\"function\" == typeof e.context.fetchOptions ? e.context.fetchOptions() : e.context.fetchOptions) || {};\n  if (a.headers) {\n    if ((e => \"has\" in e && !Object.keys(e).length)(a.headers)) {\n      a.headers.forEach(((e, r) => {\n        t[r] = e;\n      }));\n    } else if (Array.isArray(a.headers)) {\n      a.headers.forEach(((e, r) => {\n        if (Array.isArray(e)) {\n          if (t[e[0]]) {\n            t[e[0]] = `${t[e[0]]},${e[1]}`;\n          } else {\n            t[e[0]] = e[1];\n          }\n        } else {\n          t[r] = e;\n        }\n      }));\n    } else {\n      for (var o in a.headers) {\n        t[o.toLowerCase()] = a.headers[o];\n      }\n    }\n  }\n  var n = serializeBody(e, r);\n  if (\"string\" == typeof n && !t[\"content-type\"]) {\n    t[\"content-type\"] = \"application/json\";\n  }\n  return {\n    ...a,\n    method: n ? \"POST\" : \"GET\",\n    body: n,\n    headers: t\n  };\n};\n\nvar y = \"undefined\" != typeof TextDecoder ? new TextDecoder : null;\n\nvar h = /boundary=\"?([^=\";]+)\"?/i;\n\nvar m = /data: ?([^\\n]+)/;\n\nvar toString = e => \"Buffer\" === e.constructor.name ? e.toString() : y.decode(e);\n\nasync function* streamBody(e) {\n  if (e.body[Symbol.asyncIterator]) {\n    for await (var r of e.body) {\n      yield toString(r);\n    }\n  } else {\n    var t = e.body.getReader();\n    var a;\n    try {\n      while (!(a = await t.read()).done) {\n        yield toString(a.value);\n      }\n    } finally {\n      t.cancel();\n    }\n  }\n}\n\nasync function* split(e, r) {\n  var t = \"\";\n  var a;\n  for await (var o of e) {\n    t += o;\n    while ((a = t.indexOf(r)) > -1) {\n      yield t.slice(0, a);\n      t = t.slice(a + r.length);\n    }\n  }\n}\n\nasync function* fetchOperation(e, r, t) {\n  var a = !0;\n  var o = null;\n  var n;\n  try {\n    yield await Promise.resolve();\n    var s = (n = await (e.context.fetch || fetch)(r, t)).headers.get(\"Content-Type\") || \"\";\n    var i;\n    if (/multipart\\/mixed/i.test(s)) {\n      i = async function* parseMultipartMixed(e, r) {\n        var t = e.match(h);\n        var a = \"--\" + (t ? t[1] : \"-\");\n        var o = !0;\n        var n;\n        for await (var s of split(streamBody(r), \"\\r\\n\" + a)) {\n          if (o) {\n            o = !1;\n            var i = s.indexOf(a);\n            if (i > -1) {\n              s = s.slice(i + a.length);\n            } else {\n              continue;\n            }\n          }\n          try {\n            yield n = JSON.parse(s.slice(s.indexOf(\"\\r\\n\\r\\n\") + 4));\n          } catch (e) {\n            if (!n) {\n              throw e;\n            }\n          }\n          if (n && !1 === n.hasNext) {\n            break;\n          }\n        }\n        if (n && !1 !== n.hasNext) {\n          yield {\n            hasNext: !1\n          };\n        }\n      }(s, n);\n    } else if (/text\\/event-stream/i.test(s)) {\n      i = async function* parseEventStream(e) {\n        var r;\n        for await (var t of split(streamBody(e), \"\\n\\n\")) {\n          var a = t.match(m);\n          if (a) {\n            var o = a[1];\n            try {\n              yield r = JSON.parse(o);\n            } catch (e) {\n              if (!r) {\n                throw e;\n              }\n            }\n            if (r && !1 === r.hasNext) {\n              break;\n            }\n          }\n        }\n        if (r && !1 !== r.hasNext) {\n          yield {\n            hasNext: !1\n          };\n        }\n      }(n);\n    } else if (!/text\\//i.test(s)) {\n      i = async function* parseJSON(e) {\n        yield JSON.parse(await e.text());\n      }(n);\n    } else {\n      i = async function* parseMaybeJSON(e) {\n        var r = await e.text();\n        try {\n          var t = JSON.parse(r);\n          if (true) {\n            console.warn('Found response with content-type \"text/plain\" but it had a valid \"application/json\" response.');\n          }\n          yield t;\n        } catch (e) {\n          throw new Error(r);\n        }\n      }(n);\n    }\n    var f;\n    for await (var l of i) {\n      if (l.pending && !o) {\n        f = l.pending;\n      } else if (l.pending) {\n        f = [ ...f, ...l.pending ];\n      }\n      o = o ? mergeResultPatch(o, l, n, f) : makeResult(e, l, n);\n      a = !1;\n      yield o;\n      a = !0;\n    }\n    if (!o) {\n      yield o = makeResult(e, {}, n);\n    }\n  } catch (r) {\n    if (!a) {\n      throw r;\n    }\n    yield makeErrorResult(e, n && (n.status < 200 || n.status >= 300) && n.statusText ? new Error(n.statusText) : r, n);\n  }\n}\n\nfunction makeFetchSource(e, r, t) {\n  var a;\n  if (\"undefined\" != typeof AbortController) {\n    t.signal = (a = new AbortController).signal;\n  }\n  return (0,wonka__WEBPACK_IMPORTED_MODULE_1__.onEnd)((() => {\n    if (a) {\n      a.abort();\n    }\n  }))((0,wonka__WEBPACK_IMPORTED_MODULE_1__.filter)((e => !!e))((0,wonka__WEBPACK_IMPORTED_MODULE_1__.fromAsyncIterable)(fetchOperation(e, r, t))));\n}\n\n\n//# sourceMappingURL=urql-core-chunk.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@urql+core@5.0.6_graphql@16.9.0/node_modules/@urql/core/dist/urql-core-chunk.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@urql+core@5.0.6_graphql@16.9.0/node_modules/@urql/core/dist/urql-core.mjs":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@urql+core@5.0.6_graphql@16.9.0/node_modules/@urql/core/dist/urql-core.mjs ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* binding */ C),\n/* harmony export */   CombinedError: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.C),\n/* harmony export */   cacheExchange: () => (/* binding */ cacheExchange),\n/* harmony export */   composeExchanges: () => (/* binding */ composeExchanges),\n/* harmony export */   createClient: () => (/* binding */ Q),\n/* harmony export */   createRequest: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   debugExchange: () => (/* binding */ debugExchange),\n/* harmony export */   errorExchange: () => (/* binding */ mapExchange),\n/* harmony export */   fetchExchange: () => (/* binding */ fetchExchange),\n/* harmony export */   formatDocument: () => (/* binding */ formatDocument),\n/* harmony export */   gql: () => (/* binding */ gql),\n/* harmony export */   makeErrorResult: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   makeOperation: () => (/* binding */ makeOperation),\n/* harmony export */   makeResult: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   mapExchange: () => (/* binding */ mapExchange),\n/* harmony export */   mergeResultPatch: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   ssrExchange: () => (/* binding */ ssrExchange),\n/* harmony export */   stringifyDocument: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   stringifyVariables: () => (/* reexport safe */ _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.i),\n/* harmony export */   subscriptionExchange: () => (/* binding */ subscriptionExchange)\n/* harmony export */ });\n/* harmony import */ var _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @0no-co/graphql.web */ \"(ssr)/./node_modules/.pnpm/@0no-co+graphql.web@1.0.8_graphql@16.9.0/node_modules/@0no-co/graphql.web/dist/graphql.web.mjs\");\n/* harmony import */ var _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./urql-core-chunk.mjs */ \"(ssr)/./node_modules/.pnpm/@urql+core@5.0.6_graphql@16.9.0/node_modules/@urql/core/dist/urql-core-chunk.mjs\");\n/* harmony import */ var wonka__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! wonka */ \"(ssr)/./node_modules/.pnpm/wonka@6.3.4/node_modules/wonka/dist/wonka.mjs\");\n\n\n\n\n\n\n\n\nvar collectTypes = (e, r) => {\n  if (Array.isArray(e)) {\n    for (var t of e) {\n      collectTypes(t, r);\n    }\n  } else if (\"object\" == typeof e && null !== e) {\n    for (var n in e) {\n      if (\"__typename\" === n && \"string\" == typeof e[n]) {\n        r.add(e[n]);\n      } else {\n        collectTypes(e[n], r);\n      }\n    }\n  }\n  return r;\n};\n\nvar formatNode = r => {\n  if (\"definitions\" in r) {\n    var t = [];\n    for (var n of r.definitions) {\n      var a = formatNode(n);\n      t.push(a);\n    }\n    return {\n      ...r,\n      definitions: t\n    };\n  }\n  if (\"directives\" in r && r.directives && r.directives.length) {\n    var o = [];\n    var i = {};\n    for (var s of r.directives) {\n      var c = s.name.value;\n      if (\"_\" !== c[0]) {\n        o.push(s);\n      } else {\n        c = c.slice(1);\n      }\n      i[c] = s;\n    }\n    r = {\n      ...r,\n      directives: o,\n      _directives: i\n    };\n  }\n  if (\"selectionSet\" in r) {\n    var u = [];\n    var p = r.kind === _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__.Kind.OPERATION_DEFINITION;\n    if (r.selectionSet) {\n      for (var d of r.selectionSet.selections || []) {\n        p = p || d.kind === _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__.Kind.FIELD && \"__typename\" === d.name.value && !d.alias;\n        var v = formatNode(d);\n        u.push(v);\n      }\n      if (!p) {\n        u.push({\n          kind: _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__.Kind.FIELD,\n          name: {\n            kind: _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__.Kind.NAME,\n            value: \"__typename\"\n          },\n          _generated: !0\n        });\n      }\n      return {\n        ...r,\n        selectionSet: {\n          ...r.selectionSet,\n          selections: u\n        }\n      };\n    }\n  }\n  return r;\n};\n\nvar I = new Map;\n\nvar formatDocument = e => {\n  var t = (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.k)(e);\n  var n = I.get(t.__key);\n  if (!n) {\n    I.set(t.__key, n = formatNode(t));\n    Object.defineProperty(n, \"__key\", {\n      value: t.__key,\n      enumerable: !1\n    });\n  }\n  return n;\n};\n\nfunction withPromise(e) {\n  var source$ = r => e(r);\n  source$.toPromise = () => (0,wonka__WEBPACK_IMPORTED_MODULE_2__.toPromise)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.take)(1)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => !e.stale && !e.hasNext))(source$)));\n  source$.then = (e, r) => source$.toPromise().then(e, r);\n  source$.subscribe = e => (0,wonka__WEBPACK_IMPORTED_MODULE_2__.subscribe)(e)(source$);\n  return source$;\n}\n\nfunction makeOperation(e, r, t) {\n  return {\n    ...r,\n    kind: e,\n    context: r.context ? {\n      ...r.context,\n      ...t\n    } : t || r.context\n  };\n}\n\nvar addMetadata = (e, r) => makeOperation(e.kind, e, {\n  meta: {\n    ...e.context.meta,\n    ...r\n  }\n});\n\nvar noop = () => {};\n\nfunction gql(n) {\n  var a = new Map;\n  var o = [];\n  var i = [];\n  var s = Array.isArray(n) ? n[0] : n || \"\";\n  for (var c = 1; c < arguments.length; c++) {\n    var u = arguments[c];\n    if (u && u.definitions) {\n      i.push(u);\n    } else {\n      s += u;\n    }\n    s += arguments[0][c];\n  }\n  i.unshift((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.k)(s));\n  for (var p of i) {\n    for (var d of p.definitions) {\n      if (d.kind === _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__.Kind.FRAGMENT_DEFINITION) {\n        var v = d.name.value;\n        var l = (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(d);\n        if (!a.has(v)) {\n          a.set(v, l);\n          o.push(d);\n        } else if ( true && a.get(v) !== l) {\n          console.warn(\"[WARNING: Duplicate Fragment] A fragment with name `\" + v + \"` already exists in this document.\\nWhile fragment names may not be unique across your source, each name must be unique per document.\");\n        }\n      } else {\n        o.push(d);\n      }\n    }\n  }\n  return (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.k)({\n    kind: _0no_co_graphql_web__WEBPACK_IMPORTED_MODULE_1__.Kind.DOCUMENT,\n    definitions: o\n  });\n}\n\nvar shouldSkip = ({kind: e}) => \"mutation\" !== e && \"query\" !== e;\n\nvar mapTypeNames = e => {\n  var r = formatDocument(e.query);\n  if (r !== e.query) {\n    var t = makeOperation(e.kind, e);\n    t.query = r;\n    return t;\n  } else {\n    return e;\n  }\n};\n\nvar cacheExchange = ({forward: e, client: r, dispatchDebug: t}) => {\n  var a = new Map;\n  var o = new Map;\n  var isOperationCached = e => \"query\" === e.kind && \"network-only\" !== e.context.requestPolicy && (\"cache-only\" === e.context.requestPolicy || a.has(e.key));\n  return i => {\n    var s = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.map)((e => {\n      var o = a.get(e.key);\n       true && t({\n        operation: e,\n        ...o ? {\n          type: \"cacheHit\",\n          message: \"The result was successfully retried from the cache\"\n        } : {\n          type: \"cacheMiss\",\n          message: \"The result could not be retrieved from the cache\"\n        },\n        source: \"cacheExchange\"\n      });\n      var i = o || (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.m)(e, {\n        data: null\n      });\n      i = {\n        ...i,\n        operation:  true ? addMetadata(e, {\n          cacheOutcome: o ? \"hit\" : \"miss\"\n        }) : 0\n      };\n      if (\"cache-and-network\" === e.context.requestPolicy) {\n        i.stale = !0;\n        reexecuteOperation(r, e);\n      }\n      return i;\n    }))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => !shouldSkip(e) && isOperationCached(e)))(i));\n    var c = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.tap)((e => {\n      var {operation: n} = e;\n      if (!n) {\n        return;\n      }\n      var i = n.context.additionalTypenames || [];\n      if (\"subscription\" !== e.operation.kind) {\n        i = (e => [ ...collectTypes(e, new Set) ])(e.data).concat(i);\n      }\n      if (\"mutation\" === e.operation.kind || \"subscription\" === e.operation.kind) {\n        var s = new Set;\n         true && t({\n          type: \"cacheInvalidation\",\n          message: `The following typenames have been invalidated: ${i}`,\n          operation: n,\n          data: {\n            typenames: i,\n            response: e\n          },\n          source: \"cacheExchange\"\n        });\n        for (var c = 0; c < i.length; c++) {\n          var u = i[c];\n          var p = o.get(u);\n          if (!p) {\n            o.set(u, p = new Set);\n          }\n          for (var d of p.values()) {\n            s.add(d);\n          }\n          p.clear();\n        }\n        for (var v of s.values()) {\n          if (a.has(v)) {\n            n = a.get(v).operation;\n            a.delete(v);\n            reexecuteOperation(r, n);\n          }\n        }\n      } else if (\"query\" === n.kind && e.data) {\n        a.set(n.key, e);\n        for (var l = 0; l < i.length; l++) {\n          var f = i[l];\n          var h = o.get(f);\n          if (!h) {\n            o.set(f, h = new Set);\n          }\n          h.add(n.key);\n        }\n      }\n    }))(e((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"query\" !== e.kind || \"cache-only\" !== e.context.requestPolicy))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.map)((e =>  true ? addMetadata(e, {\n      cacheOutcome: \"miss\"\n    }) : 0))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ (0,wonka__WEBPACK_IMPORTED_MODULE_2__.map)(mapTypeNames)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => !shouldSkip(e) && !isOperationCached(e)))(i)), (0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => shouldSkip(e)))(i) ])))));\n    return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ s, c ]);\n  };\n};\n\nvar reexecuteOperation = (e, r) => e.reexecuteOperation(makeOperation(r.kind, r, {\n  requestPolicy: \"network-only\"\n}));\n\nvar T = new Set;\n\nvar ssrExchange = (e = {}) => {\n  var r = !!e.staleWhileRevalidate;\n  var t = !!e.includeExtensions;\n  var n = {};\n  var o = [];\n  var invalidate = e => {\n    o.push(e.operation.key);\n    if (1 === o.length) {\n      Promise.resolve().then((() => {\n        var e;\n        while (e = o.shift()) {\n          n[e] = null;\n        }\n      }));\n    }\n  };\n  var ssr = ({client: o, forward: i}) => s => {\n    var c = e && \"boolean\" == typeof e.isClient ? !!e.isClient : !o.suspense;\n    var u = i((0,wonka__WEBPACK_IMPORTED_MODULE_2__.map)(mapTypeNames)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" === e.kind || !n[e.key] || !!n[e.key].hasNext || \"network-only\" === e.context.requestPolicy))(s)));\n    var p = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.map)((e => {\n      var i = ((e, r, t) => ({\n        operation: e,\n        data: r.data ? JSON.parse(r.data) : void 0,\n        extensions: t && r.extensions ? JSON.parse(r.extensions) : void 0,\n        error: r.error ? new _urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.C({\n          networkError: r.error.networkError ? new Error(r.error.networkError) : void 0,\n          graphQLErrors: r.error.graphQLErrors\n        }) : void 0,\n        stale: !1,\n        hasNext: !!r.hasNext\n      }))(e, n[e.key], t);\n      if (r && !T.has(e.key)) {\n        i.stale = !0;\n        T.add(e.key);\n        reexecuteOperation(o, e);\n      }\n      return {\n        ...i,\n        operation:  true ? addMetadata(e, {\n          cacheOutcome: \"hit\"\n        }) : 0\n      };\n    }))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" !== e.kind && !!n[e.key] && \"network-only\" !== e.context.requestPolicy))(s));\n    if (!c) {\n      u = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.tap)((e => {\n        var {operation: r} = e;\n        if (\"mutation\" !== r.kind) {\n          var a = ((e, r) => {\n            var t = {\n              hasNext: e.hasNext\n            };\n            if (void 0 !== e.data) {\n              t.data = JSON.stringify(e.data);\n            }\n            if (r && void 0 !== e.extensions) {\n              t.extensions = JSON.stringify(e.extensions);\n            }\n            if (e.error) {\n              t.error = {\n                graphQLErrors: e.error.graphQLErrors.map((e => {\n                  if (!e.path && !e.extensions) {\n                    return e.message;\n                  }\n                  return {\n                    message: e.message,\n                    path: e.path,\n                    extensions: e.extensions\n                  };\n                }))\n              };\n              if (e.error.networkError) {\n                t.error.networkError = \"\" + e.error.networkError;\n              }\n            }\n            return t;\n          })(e, t);\n          n[r.key] = a;\n        }\n      }))(u);\n    } else {\n      p = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.tap)(invalidate)(p);\n    }\n    return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ u, p ]);\n  };\n  ssr.restoreData = e => {\n    for (var r in e) {\n      if (null !== n[r]) {\n        n[r] = e[r];\n      }\n    }\n  };\n  ssr.extractData = () => {\n    var e = {};\n    for (var r in n) {\n      if (null != n[r]) {\n        e[r] = n[r];\n      }\n    }\n    return e;\n  };\n  if (e && e.initialState) {\n    ssr.restoreData(e.initialState);\n  }\n  return ssr;\n};\n\nvar subscriptionExchange = ({forwardSubscription: e, enableAllOperations: r, isSubscriptionOperation: t}) => ({client: a, forward: i}) => {\n  var u = t || (e => \"subscription\" === e.kind || !!r && (\"query\" === e.kind || \"mutation\" === e.kind));\n  return r => {\n    var t = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.mergeMap)((t => {\n      var {key: i} = t;\n      var u = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" === e.kind && e.key === i))(r);\n      return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.takeUntil)(u)((r => {\n        var t = e((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(r), r);\n        return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.make)((e => {\n          var o = !1;\n          var i;\n          var u;\n          function nextResult(t) {\n            e.next(u = u ? (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.c)(u, t) : (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.m)(r, t));\n          }\n          Promise.resolve().then((() => {\n            if (o) {\n              return;\n            }\n            i = t.subscribe({\n              next: nextResult,\n              error(t) {\n                if (Array.isArray(t)) {\n                  nextResult({\n                    errors: t\n                  });\n                } else {\n                  e.next((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.b)(r, t));\n                }\n                e.complete();\n              },\n              complete() {\n                if (!o) {\n                  o = !0;\n                  if (\"subscription\" === r.kind) {\n                    a.reexecuteOperation(makeOperation(\"teardown\", r, r.context));\n                  }\n                  if (u && u.hasNext) {\n                    nextResult({\n                      hasNext: !1\n                    });\n                  }\n                  e.complete();\n                }\n              }\n            });\n          }));\n          return () => {\n            o = !0;\n            if (i) {\n              i.unsubscribe();\n            }\n          };\n        }));\n      })(t));\n    }))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" !== e.kind && u(e)))(r));\n    var p = i((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" === e.kind || !u(e)))(r));\n    return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ t, p ]);\n  };\n};\n\nvar debugExchange = ({forward: e}) => {\n  if (false) {} else {\n    return r => (0,wonka__WEBPACK_IMPORTED_MODULE_2__.tap)((e => console.log(\"[Exchange debug]: Completed operation: \", e)))(e((0,wonka__WEBPACK_IMPORTED_MODULE_2__.tap)((e => console.log(\"[Exchange debug]: Incoming operation: \", e)))(r)));\n  }\n};\n\nvar fetchExchange = ({forward: e, dispatchDebug: r}) => t => {\n  var n = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.mergeMap)((e => {\n    var n = (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(e);\n    var a = (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.d)(e, n);\n    var i = (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.e)(e, n);\n     true && r({\n      type: \"fetchRequest\",\n      message: \"A fetch request is being executed.\",\n      operation: e,\n      data: {\n        url: a,\n        fetchOptions: i\n      },\n      source: \"fetchExchange\"\n    });\n    var s = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.takeUntil)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((r => \"teardown\" === r.kind && r.key === e.key))(t))((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.f)(e, a, i));\n    if (true) {\n      return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.onPush)((t => {\n        var n = !t.data ? t.error : void 0;\n         true && r({\n          type: n ? \"fetchError\" : \"fetchSuccess\",\n          message: `A ${n ? \"failed\" : \"successful\"} fetch response has been returned.`,\n          operation: e,\n          data: {\n            url: a,\n            fetchOptions: i,\n            value: n || t\n          },\n          source: \"fetchExchange\"\n        });\n      }))(s);\n    }\n    return s;\n  }))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" !== e.kind && (\"subscription\" !== e.kind || !!e.context.fetchSubscriptions)))(t));\n  var a = e((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => \"teardown\" === e.kind || \"subscription\" === e.kind && !e.context.fetchSubscriptions))(t));\n  return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ n, a ]);\n};\n\nvar composeExchanges = e => ({client: r, forward: t, dispatchDebug: n}) => e.reduceRight(((e, t) => {\n  var a = !1;\n  return t({\n    client: r,\n    forward(r) {\n      if (true) {\n        if (a) {\n          throw new Error(\"forward() must only be called once in each Exchange.\");\n        }\n        a = !0;\n      }\n      return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.share)(e((0,wonka__WEBPACK_IMPORTED_MODULE_2__.share)(r)));\n    },\n    dispatchDebug(e) {\n       true && n({\n        timestamp: Date.now(),\n        source: t.name,\n        ...e\n      });\n    }\n  });\n}), t);\n\nvar mapExchange = ({onOperation: e, onResult: r, onError: t}) => ({forward: n}) => a => (0,wonka__WEBPACK_IMPORTED_MODULE_2__.mergeMap)((e => {\n  if (t && e.error) {\n    t(e.error, e.operation);\n  }\n  var n = r && r(e) || e;\n  return \"then\" in n ? (0,wonka__WEBPACK_IMPORTED_MODULE_2__.fromPromise)(n) : (0,wonka__WEBPACK_IMPORTED_MODULE_2__.fromValue)(n);\n}))(n((0,wonka__WEBPACK_IMPORTED_MODULE_2__.mergeMap)((r => {\n  var t = e && e(r) || r;\n  return \"then\" in t ? (0,wonka__WEBPACK_IMPORTED_MODULE_2__.fromPromise)(t) : (0,wonka__WEBPACK_IMPORTED_MODULE_2__.fromValue)(t);\n}))(a)));\n\nvar fallbackExchange = ({dispatchDebug: e}) => r => {\n  if (true) {\n    r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.tap)((r => {\n      if (\"teardown\" !== r.kind && \"production\" !== \"development\") {\n        var t = `No exchange has handled operations of kind \"${r.kind}\". Check whether you've added an exchange responsible for these operations.`;\n         true && e({\n          type: \"fallbackCatch\",\n          message: t,\n          operation: r,\n          source: \"fallbackExchange\"\n        });\n        console.warn(t);\n      }\n    }))(r);\n  }\n  return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((e => !1))(r);\n};\n\nvar C = function Client(e) {\n  if ( true && !e.url) {\n    throw new Error(\"You are creating an urql-client without a url.\");\n  }\n  var r = 0;\n  var t = new Map;\n  var n = new Map;\n  var a = new Set;\n  var o = [];\n  var i = {\n    url: e.url,\n    fetchSubscriptions: e.fetchSubscriptions,\n    fetchOptions: e.fetchOptions,\n    fetch: e.fetch,\n    preferGetMethod: e.preferGetMethod,\n    requestPolicy: e.requestPolicy || \"cache-first\"\n  };\n  var s = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.makeSubject)();\n  function nextOperation(e) {\n    if (\"mutation\" === e.kind || \"teardown\" === e.kind || !a.has(e.key)) {\n      if (\"teardown\" === e.kind) {\n        a.delete(e.key);\n      } else if (\"mutation\" !== e.kind) {\n        a.add(e.key);\n      }\n      s.next(e);\n    }\n  }\n  var c = !1;\n  function dispatchOperation(e) {\n    if (e) {\n      nextOperation(e);\n    }\n    if (!c) {\n      c = !0;\n      while (c && (e = o.shift())) {\n        nextOperation(e);\n      }\n      c = !1;\n    }\n  }\n  var makeResultSource = e => {\n    var r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.takeUntil)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((r => \"teardown\" === r.kind && r.key === e.key))(s.source))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((r => r.operation.kind === e.kind && r.operation.key === e.key && (!r.operation.context._instance || r.operation.context._instance === e.context._instance)))(O));\n    if (\"query\" !== e.kind) {\n      r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.takeWhile)((e => !!e.hasNext), !0)(r);\n    } else {\n      r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.switchMap)((r => {\n        var t = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.fromValue)(r);\n        return r.stale || r.hasNext ? t : (0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ t, (0,wonka__WEBPACK_IMPORTED_MODULE_2__.map)((() => {\n          r.stale = !0;\n          return r;\n        }))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.take)(1)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((r => r.key === e.key))(s.source))) ]);\n      }))(r);\n    }\n    if (\"mutation\" !== e.kind) {\n      r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.onEnd)((() => {\n        a.delete(e.key);\n        t.delete(e.key);\n        n.delete(e.key);\n        c = !1;\n        for (var r = o.length - 1; r >= 0; r--) {\n          if (o[r].key === e.key) {\n            o.splice(r, 1);\n          }\n        }\n        nextOperation(makeOperation(\"teardown\", e, e.context));\n      }))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.onPush)((r => {\n        if (r.stale) {\n          if (!r.hasNext) {\n            a.delete(e.key);\n          } else {\n            for (var n of o) {\n              if (n.key === r.operation.key) {\n                a.delete(n.key);\n                break;\n              }\n            }\n          }\n        } else if (!r.hasNext) {\n          a.delete(e.key);\n        }\n        t.set(e.key, r);\n      }))(r));\n    } else {\n      r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.onStart)((() => {\n        nextOperation(e);\n      }))(r);\n    }\n    return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.share)(r);\n  };\n  var u = this instanceof Client ? this : Object.create(Client.prototype);\n  var p = Object.assign(u, {\n    suspense: !!e.suspense,\n    operations$: s.source,\n    reexecuteOperation(e) {\n      if (\"teardown\" === e.kind) {\n        dispatchOperation(e);\n      } else if (\"mutation\" === e.kind) {\n        o.push(e);\n        Promise.resolve().then(dispatchOperation);\n      } else if (n.has(e.key)) {\n        var r = !1;\n        for (var t = 0; t < o.length; t++) {\n          if (o[t].key === e.key) {\n            o[t] = e;\n            r = !0;\n          }\n        }\n        if (!(r || a.has(e.key) && \"network-only\" !== e.context.requestPolicy)) {\n          o.push(e);\n          Promise.resolve().then(dispatchOperation);\n        } else {\n          a.delete(e.key);\n          Promise.resolve().then(dispatchOperation);\n        }\n      }\n    },\n    createRequestOperation(e, t, n) {\n      if (!n) {\n        n = {};\n      }\n      var a;\n      if ( true && \"teardown\" !== e && (a = (0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.g)(t.query)) !== e) {\n        throw new Error(`Expected operation of type \"${e}\" but found \"${a}\"`);\n      }\n      return makeOperation(e, t, {\n        _instance: \"mutation\" === e ? r = r + 1 | 0 : void 0,\n        ...i,\n        ...n,\n        requestPolicy: n.requestPolicy || i.requestPolicy,\n        suspense: n.suspense || !1 !== n.suspense && p.suspense\n      });\n    },\n    executeRequestOperation(e) {\n      if (\"mutation\" === e.kind) {\n        return withPromise(makeResultSource(e));\n      }\n      return withPromise((0,wonka__WEBPACK_IMPORTED_MODULE_2__.lazy)((() => {\n        var r = n.get(e.key);\n        if (!r) {\n          n.set(e.key, r = makeResultSource(e));\n        }\n        r = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.onStart)((() => {\n          dispatchOperation(e);\n        }))(r);\n        var a = t.get(e.key);\n        if (\"query\" === e.kind && a && (a.stale || a.hasNext)) {\n          return (0,wonka__WEBPACK_IMPORTED_MODULE_2__.switchMap)(wonka__WEBPACK_IMPORTED_MODULE_2__.fromValue)((0,wonka__WEBPACK_IMPORTED_MODULE_2__.merge)([ r, (0,wonka__WEBPACK_IMPORTED_MODULE_2__.filter)((r => r === t.get(e.key)))((0,wonka__WEBPACK_IMPORTED_MODULE_2__.fromValue)(a)) ]));\n        } else {\n          return r;\n        }\n      })));\n    },\n    executeQuery(e, r) {\n      var t = p.createRequestOperation(\"query\", e, r);\n      return p.executeRequestOperation(t);\n    },\n    executeSubscription(e, r) {\n      var t = p.createRequestOperation(\"subscription\", e, r);\n      return p.executeRequestOperation(t);\n    },\n    executeMutation(e, r) {\n      var t = p.createRequestOperation(\"mutation\", e, r);\n      return p.executeRequestOperation(t);\n    },\n    readQuery(e, r, t) {\n      var n = null;\n      (0,wonka__WEBPACK_IMPORTED_MODULE_2__.subscribe)((e => {\n        n = e;\n      }))(p.query(e, r, t)).unsubscribe();\n      return n;\n    },\n    query: (e, r, t) => p.executeQuery((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.h)(e, r), t),\n    subscription: (e, r, t) => p.executeSubscription((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.h)(e, r), t),\n    mutation: (e, r, t) => p.executeMutation((0,_urql_core_chunk_mjs__WEBPACK_IMPORTED_MODULE_0__.h)(e, r), t)\n  });\n  var d = noop;\n  if (true) {\n    var {next: f, source: x} = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.makeSubject)();\n    p.subscribeToDebugTarget = e => (0,wonka__WEBPACK_IMPORTED_MODULE_2__.subscribe)(e)(x);\n    d = f;\n  }\n  var g = composeExchanges(e.exchanges);\n  var O = (0,wonka__WEBPACK_IMPORTED_MODULE_2__.share)(g({\n    client: p,\n    dispatchDebug: d,\n    forward: fallbackExchange({\n      dispatchDebug: d\n    })\n  })(s.source));\n  (0,wonka__WEBPACK_IMPORTED_MODULE_2__.publish)(O);\n  return p;\n};\n\nvar Q = C;\n\n\n//# sourceMappingURL=urql-core.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@urql+core@5.0.6_graphql@16.9.0/node_modules/@urql/core/dist/urql-core.mjs\n");

/***/ })

};
;