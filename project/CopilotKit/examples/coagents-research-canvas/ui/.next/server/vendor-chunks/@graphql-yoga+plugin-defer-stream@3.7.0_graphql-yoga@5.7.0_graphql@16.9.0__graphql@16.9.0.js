"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0";
exports.ids = ["vendor-chunks/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/index.js":
/*!**********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/index.js ***!
  \**********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useDeferStream = useDeferStream;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/.pnpm/graphql@16.9.0/node_modules/graphql/index.mjs\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+utils@10.5.4_graphql@16.9.0/node_modules/@graphql-tools/utils/cjs/index.js\");\nconst defer_stream_directive_label_js_1 = __webpack_require__(/*! ./validations/defer-stream-directive-label.js */ \"(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-label.js\");\nconst defer_stream_directive_on_root_field_js_1 = __webpack_require__(/*! ./validations/defer-stream-directive-on-root-field.js */ \"(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-on-root-field.js\");\nconst overlapping_fields_can_be_merged_js_1 = __webpack_require__(/*! ./validations/overlapping-fields-can-be-merged.js */ \"(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/overlapping-fields-can-be-merged.js\");\nconst stream_directive_on_list_field_js_1 = __webpack_require__(/*! ./validations/stream-directive-on-list-field.js */ \"(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/stream-directive-on-list-field.js\");\nfunction useDeferStream() {\n    return {\n        onSchemaChange: ({ schema, replaceSchema, }) => {\n            const directives = [];\n            const deferInSchema = schema.getDirective('defer');\n            if (deferInSchema == null) {\n                directives.push(utils_1.GraphQLDeferDirective);\n            }\n            const streamInSchema = schema.getDirective('stream');\n            if (streamInSchema == null) {\n                directives.push(utils_1.GraphQLStreamDirective);\n            }\n            if (directives.length) {\n                replaceSchema(new graphql_1.GraphQLSchema({\n                    ...schema.toConfig(),\n                    directives: [...schema.getDirectives(), ...directives],\n                }));\n            }\n        },\n        onValidate: ({ params, addValidationRule, }) => {\n            // Just to make TS happy because rules are always defined by useEngine.\n            params.rules ||= [];\n            params.rules = params.rules.filter(rule => rule.name !== 'OverlappingFieldsCanBeMergedRule');\n            addValidationRule(overlapping_fields_can_be_merged_js_1.OverlappingFieldsCanBeMergedRule);\n            addValidationRule(defer_stream_directive_label_js_1.DeferStreamDirectiveLabelRule);\n            addValidationRule(defer_stream_directive_on_root_field_js_1.DeferStreamDirectiveOnRootFieldRule);\n            addValidationRule(stream_directive_on_list_field_js_1.StreamDirectiveOnListFieldRule);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-label.js":
/*!*********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-label.js ***!
  \*********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DeferStreamDirectiveLabelRule = DeferStreamDirectiveLabelRule;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/.pnpm/graphql@16.9.0/node_modules/graphql/index.mjs\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+utils@10.5.4_graphql@16.9.0/node_modules/@graphql-tools/utils/cjs/index.js\");\n/**\n * Stream directive on list field\n *\n * A GraphQL document is only valid if defer and stream directives' label argument is static and unique.\n */\nfunction DeferStreamDirectiveLabelRule(context) {\n    const knownLabels = Object.create(null);\n    return {\n        Directive(node) {\n            if (node.name.value === utils_1.GraphQLDeferDirective.name ||\n                node.name.value === utils_1.GraphQLStreamDirective.name) {\n                const labelArgument = node.arguments?.find(arg => arg.name.value === 'label');\n                const labelValue = labelArgument?.value;\n                if (!labelValue) {\n                    return;\n                }\n                if (labelValue.kind !== graphql_1.Kind.STRING) {\n                    context.reportError((0, utils_1.createGraphQLError)(`Directive \"${node.name.value}\"'s label argument must be a static string.`, { nodes: node }));\n                }\n                else if (knownLabels[labelValue.value]) {\n                    context.reportError((0, utils_1.createGraphQLError)('Defer/Stream directive label argument must be unique.', {\n                        nodes: [knownLabels[labelValue.value], node],\n                    }));\n                }\n                else {\n                    knownLabels[labelValue.value] = node;\n                }\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-label.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-on-root-field.js":
/*!*****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-on-root-field.js ***!
  \*****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DeferStreamDirectiveOnRootFieldRule = DeferStreamDirectiveOnRootFieldRule;\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+utils@10.5.4_graphql@16.9.0/node_modules/@graphql-tools/utils/cjs/index.js\");\n/**\n * Stream directive on list field\n *\n * A GraphQL document is only valid if defer directives are not used on root mutation or subscription types.\n */\nfunction DeferStreamDirectiveOnRootFieldRule(context) {\n    return {\n        Directive(node) {\n            const mutationType = context.getSchema().getMutationType();\n            const subscriptionType = context.getSchema().getSubscriptionType();\n            const parentType = context.getParentType();\n            if (parentType && node.name.value === utils_1.GraphQLDeferDirective.name) {\n                if (mutationType && parentType === mutationType) {\n                    context.reportError((0, utils_1.createGraphQLError)(`Defer directive cannot be used on root mutation type \"${parentType.name}\".`, { nodes: node }));\n                }\n                if (subscriptionType && parentType === subscriptionType) {\n                    context.reportError((0, utils_1.createGraphQLError)(`Defer directive cannot be used on root subscription type \"${parentType.name}\".`, { nodes: node }));\n                }\n            }\n            if (parentType && node.name.value === utils_1.GraphQLStreamDirective.name) {\n                if (mutationType && parentType === mutationType) {\n                    context.reportError((0, utils_1.createGraphQLError)(`Stream directive cannot be used on root mutation type \"${parentType.name}\".`, { nodes: node }));\n                }\n                if (subscriptionType && parentType === subscriptionType) {\n                    context.reportError((0, utils_1.createGraphQLError)(`Stream directive cannot be used on root subscription type \"${parentType.name}\".`, { nodes: node }));\n                }\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGdyYXBocWwteW9nYStwbHVnaW4tZGVmZXItc3RyZWFtQDMuNy4wX2dyYXBocWwteW9nYUA1LjcuMF9ncmFwaHFsQDE2LjkuMF9fZ3JhcGhxbEAxNi45LjAvbm9kZV9tb2R1bGVzL0BncmFwaHFsLXlvZ2EvcGx1Z2luLWRlZmVyLXN0cmVhbS9janMvdmFsaWRhdGlvbnMvZGVmZXItc3RyZWFtLWRpcmVjdGl2ZS1vbi1yb290LWZpZWxkLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDJDQUEyQztBQUMzQyxnQkFBZ0IsbUJBQU8sQ0FBQyxrSkFBc0I7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpSUFBaUksZ0JBQWdCLE9BQU8sYUFBYTtBQUNySztBQUNBO0FBQ0EscUlBQXFJLGdCQUFnQixPQUFPLGFBQWE7QUFDeks7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrSUFBa0ksZ0JBQWdCLE9BQU8sYUFBYTtBQUN0SztBQUNBO0FBQ0Esc0lBQXNJLGdCQUFnQixPQUFPLGFBQWE7QUFDMUs7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGdyYXBocWwteW9nYStwbHVnaW4tZGVmZXItc3RyZWFtQDMuNy4wX2dyYXBocWwteW9nYUA1LjcuMF9ncmFwaHFsQDE2LjkuMF9fZ3JhcGhxbEAxNi45LjAvbm9kZV9tb2R1bGVzL0BncmFwaHFsLXlvZ2EvcGx1Z2luLWRlZmVyLXN0cmVhbS9janMvdmFsaWRhdGlvbnMvZGVmZXItc3RyZWFtLWRpcmVjdGl2ZS1vbi1yb290LWZpZWxkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5EZWZlclN0cmVhbURpcmVjdGl2ZU9uUm9vdEZpZWxkUnVsZSA9IERlZmVyU3RyZWFtRGlyZWN0aXZlT25Sb290RmllbGRSdWxlO1xuY29uc3QgdXRpbHNfMSA9IHJlcXVpcmUoXCJAZ3JhcGhxbC10b29scy91dGlsc1wiKTtcbi8qKlxuICogU3RyZWFtIGRpcmVjdGl2ZSBvbiBsaXN0IGZpZWxkXG4gKlxuICogQSBHcmFwaFFMIGRvY3VtZW50IGlzIG9ubHkgdmFsaWQgaWYgZGVmZXIgZGlyZWN0aXZlcyBhcmUgbm90IHVzZWQgb24gcm9vdCBtdXRhdGlvbiBvciBzdWJzY3JpcHRpb24gdHlwZXMuXG4gKi9cbmZ1bmN0aW9uIERlZmVyU3RyZWFtRGlyZWN0aXZlT25Sb290RmllbGRSdWxlKGNvbnRleHQpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBEaXJlY3RpdmUobm9kZSkge1xuICAgICAgICAgICAgY29uc3QgbXV0YXRpb25UeXBlID0gY29udGV4dC5nZXRTY2hlbWEoKS5nZXRNdXRhdGlvblR5cGUoKTtcbiAgICAgICAgICAgIGNvbnN0IHN1YnNjcmlwdGlvblR5cGUgPSBjb250ZXh0LmdldFNjaGVtYSgpLmdldFN1YnNjcmlwdGlvblR5cGUoKTtcbiAgICAgICAgICAgIGNvbnN0IHBhcmVudFR5cGUgPSBjb250ZXh0LmdldFBhcmVudFR5cGUoKTtcbiAgICAgICAgICAgIGlmIChwYXJlbnRUeXBlICYmIG5vZGUubmFtZS52YWx1ZSA9PT0gdXRpbHNfMS5HcmFwaFFMRGVmZXJEaXJlY3RpdmUubmFtZSkge1xuICAgICAgICAgICAgICAgIGlmIChtdXRhdGlvblR5cGUgJiYgcGFyZW50VHlwZSA9PT0gbXV0YXRpb25UeXBlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnRleHQucmVwb3J0RXJyb3IoKDAsIHV0aWxzXzEuY3JlYXRlR3JhcGhRTEVycm9yKShgRGVmZXIgZGlyZWN0aXZlIGNhbm5vdCBiZSB1c2VkIG9uIHJvb3QgbXV0YXRpb24gdHlwZSBcIiR7cGFyZW50VHlwZS5uYW1lfVwiLmAsIHsgbm9kZXM6IG5vZGUgfSkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoc3Vic2NyaXB0aW9uVHlwZSAmJiBwYXJlbnRUeXBlID09PSBzdWJzY3JpcHRpb25UeXBlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnRleHQucmVwb3J0RXJyb3IoKDAsIHV0aWxzXzEuY3JlYXRlR3JhcGhRTEVycm9yKShgRGVmZXIgZGlyZWN0aXZlIGNhbm5vdCBiZSB1c2VkIG9uIHJvb3Qgc3Vic2NyaXB0aW9uIHR5cGUgXCIke3BhcmVudFR5cGUubmFtZX1cIi5gLCB7IG5vZGVzOiBub2RlIH0pKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAocGFyZW50VHlwZSAmJiBub2RlLm5hbWUudmFsdWUgPT09IHV0aWxzXzEuR3JhcGhRTFN0cmVhbURpcmVjdGl2ZS5uYW1lKSB7XG4gICAgICAgICAgICAgICAgaWYgKG11dGF0aW9uVHlwZSAmJiBwYXJlbnRUeXBlID09PSBtdXRhdGlvblR5cGUpIHtcbiAgICAgICAgICAgICAgICAgICAgY29udGV4dC5yZXBvcnRFcnJvcigoMCwgdXRpbHNfMS5jcmVhdGVHcmFwaFFMRXJyb3IpKGBTdHJlYW0gZGlyZWN0aXZlIGNhbm5vdCBiZSB1c2VkIG9uIHJvb3QgbXV0YXRpb24gdHlwZSBcIiR7cGFyZW50VHlwZS5uYW1lfVwiLmAsIHsgbm9kZXM6IG5vZGUgfSkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoc3Vic2NyaXB0aW9uVHlwZSAmJiBwYXJlbnRUeXBlID09PSBzdWJzY3JpcHRpb25UeXBlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnRleHQucmVwb3J0RXJyb3IoKDAsIHV0aWxzXzEuY3JlYXRlR3JhcGhRTEVycm9yKShgU3RyZWFtIGRpcmVjdGl2ZSBjYW5ub3QgYmUgdXNlZCBvbiByb290IHN1YnNjcmlwdGlvbiB0eXBlIFwiJHtwYXJlbnRUeXBlLm5hbWV9XCIuYCwgeyBub2Rlczogbm9kZSB9KSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgIH07XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/defer-stream-directive-on-root-field.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/overlapping-fields-can-be-merged.js":
/*!*************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/overlapping-fields-can-be-merged.js ***!
  \*************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.naturalCompare = naturalCompare;\nexports.OverlappingFieldsCanBeMergedRule = OverlappingFieldsCanBeMergedRule;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/.pnpm/graphql@16.9.0/node_modules/graphql/index.mjs\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+utils@10.5.4_graphql@16.9.0/node_modules/@graphql-tools/utils/cjs/index.js\");\n/**\n * Returns a number indicating whether a reference string comes before, or after,\n * or is the same as the given string in natural sort order.\n *\n * See: https://en.wikipedia.org/wiki/Natural_sort_order\n *\n */\nfunction naturalCompare(aStr, bStr) {\n    let aIndex = 0;\n    let bIndex = 0;\n    while (aIndex < aStr.length && bIndex < bStr.length) {\n        let aChar = aStr.charCodeAt(aIndex);\n        let bChar = bStr.charCodeAt(bIndex);\n        if (isDigit(aChar) && isDigit(bChar)) {\n            let aNum = 0;\n            do {\n                ++aIndex;\n                aNum = aNum * 10 + aChar - DIGIT_0;\n                aChar = aStr.charCodeAt(aIndex);\n            } while (isDigit(aChar) && aNum > 0);\n            let bNum = 0;\n            do {\n                ++bIndex;\n                bNum = bNum * 10 + bChar - DIGIT_0;\n                bChar = bStr.charCodeAt(bIndex);\n            } while (isDigit(bChar) && bNum > 0);\n            if (aNum < bNum) {\n                return -1;\n            }\n            if (aNum > bNum) {\n                return 1;\n            }\n        }\n        else {\n            if (aChar < bChar) {\n                return -1;\n            }\n            if (aChar > bChar) {\n                return 1;\n            }\n            ++aIndex;\n            ++bIndex;\n        }\n    }\n    return aStr.length - bStr.length;\n}\nconst DIGIT_0 = 48;\nconst DIGIT_9 = 57;\nfunction isDigit(code) {\n    return !Number.isNaN(code) && DIGIT_0 <= code && code <= DIGIT_9;\n}\nfunction sortValueNode(valueNode) {\n    switch (valueNode.kind) {\n        case graphql_1.Kind.OBJECT:\n            return {\n                ...valueNode,\n                fields: sortFields(valueNode.fields),\n            };\n        case graphql_1.Kind.LIST:\n            return {\n                ...valueNode,\n                values: valueNode.values.map(sortValueNode),\n            };\n        case graphql_1.Kind.INT:\n        case graphql_1.Kind.FLOAT:\n        case graphql_1.Kind.STRING:\n        case graphql_1.Kind.BOOLEAN:\n        case graphql_1.Kind.NULL:\n        case graphql_1.Kind.ENUM:\n        case graphql_1.Kind.VARIABLE:\n            return valueNode;\n    }\n}\nfunction sortFields(fields) {\n    return fields\n        .map(fieldNode => ({\n        ...fieldNode,\n        value: sortValueNode(fieldNode.value),\n    }))\n        .sort((fieldA, fieldB) => naturalCompare(fieldA.name.value, fieldB.name.value));\n}\nfunction reasonMessage(reason) {\n    if (Array.isArray(reason)) {\n        return reason\n            .map(([responseName, subReason]) => `subfields \"${responseName}\" conflict because ` + reasonMessage(subReason))\n            .join(' and ');\n    }\n    return reason;\n}\n/**\n * Overlapping fields can be merged\n *\n * A selection set is only valid if all fields (including spreading any\n * fragments) either correspond to distinct response names or can be merged\n * without ambiguity.\n *\n * See https://spec.graphql.org/draft/#sec-Field-Selection-Merging\n */\nfunction OverlappingFieldsCanBeMergedRule(context) {\n    // A memoization for when two fragments are compared \"between\" each other for\n    // conflicts. Two fragments may be compared many times, so memoizing this can\n    // dramatically improve the performance of this validator.\n    const comparedFragmentPairs = new PairSet();\n    // A cache for the \"field map\" and list of fragment names found in any given\n    // selection set. Selection sets may be asked for this information multiple\n    // times, so this improves the performance of this validator.\n    const cachedFieldsAndFragmentNames = new Map();\n    return {\n        SelectionSet(selectionSet) {\n            const conflicts = findConflictsWithinSelectionSet(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, context.getParentType(), selectionSet);\n            for (const [[responseName, reason], fields1, fields2] of conflicts) {\n                const reasonMsg = reasonMessage(reason);\n                context.reportError((0, utils_1.createGraphQLError)(`Fields \"${responseName}\" conflict because ${reasonMsg}. Use different aliases on the fields to fetch both if this was intentional.`, { nodes: fields1.concat(fields2) }));\n            }\n        },\n    };\n}\n/**\n * Algorithm:\n *\n * Conflicts occur when two fields exist in a query which will produce the same\n * response name, but represent differing values, thus creating a conflict.\n * The algorithm below finds all conflicts via making a series of comparisons\n * between fields. In order to compare as few fields as possible, this makes\n * a series of comparisons \"within\" sets of fields and \"between\" sets of fields.\n *\n * Given any selection set, a collection produces both a set of fields by\n * also including all inline fragments, as well as a list of fragments\n * referenced by fragment spreads.\n *\n * A) Each selection set represented in the document first compares \"within\" its\n * collected set of fields, finding any conflicts between every pair of\n * overlapping fields.\n * Note: This is the *only time* that a the fields \"within\" a set are compared\n * to each other. After this only fields \"between\" sets are compared.\n *\n * B) Also, if any fragment is referenced in a selection set, then a\n * comparison is made \"between\" the original set of fields and the\n * referenced fragment.\n *\n * C) Also, if multiple fragments are referenced, then comparisons\n * are made \"between\" each referenced fragment.\n *\n * D) When comparing \"between\" a set of fields and a referenced fragment, first\n * a comparison is made between each field in the original set of fields and\n * each field in the the referenced set of fields.\n *\n * E) Also, if any fragment is referenced in the referenced selection set,\n * then a comparison is made \"between\" the original set of fields and the\n * referenced fragment (recursively referring to step D).\n *\n * F) When comparing \"between\" two fragments, first a comparison is made between\n * each field in the first referenced set of fields and each field in the the\n * second referenced set of fields.\n *\n * G) Also, any fragments referenced by the first must be compared to the\n * second, and any fragments referenced by the second must be compared to the\n * first (recursively referring to step F).\n *\n * H) When comparing two fields, if both have selection sets, then a comparison\n * is made \"between\" both selection sets, first comparing the set of fields in\n * the first selection set with the set of fields in the second.\n *\n * I) Also, if any fragment is referenced in either selection set, then a\n * comparison is made \"between\" the other set of fields and the\n * referenced fragment.\n *\n * J) Also, if two fragments are referenced in both selection sets, then a\n * comparison is made \"between\" the two fragments.\n *\n */\n// Find all conflicts found \"within\" a selection set, including those found\n// via spreading in fragments. Called when visiting each SelectionSet in the\n// GraphQL Document.\nfunction findConflictsWithinSelectionSet(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, parentType, selectionSet) {\n    const conflicts = [];\n    const [fieldMap, fragmentNames] = getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType, selectionSet);\n    // (A) Find find all conflicts \"within\" the fields of this selection set.\n    // Note: this is the *only place* `collectConflictsWithin` is called.\n    collectConflictsWithin(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, fieldMap);\n    if (fragmentNames.length !== 0) {\n        // (B) Then collect conflicts between these fields and those represented by\n        // each spread fragment name found.\n        for (let i = 0; i < fragmentNames.length; i++) {\n            collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, false, fieldMap, fragmentNames[i]);\n            // (C) Then compare this fragment with all other fragments found in this\n            // selection set to collect conflicts between fragments spread together.\n            // This compares each item in the list of fragment names to every other\n            // item in that same list (except for itself).\n            for (let j = i + 1; j < fragmentNames.length; j++) {\n                collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, false, fragmentNames[i], fragmentNames[j]);\n            }\n        }\n    }\n    return conflicts;\n}\n// Collect all conflicts found between a set of fields and a fragment reference\n// including via spreading in any nested fragments.\nfunction collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap, fragmentName) {\n    const fragment = context.getFragment(fragmentName);\n    if (!fragment) {\n        return;\n    }\n    const [fieldMap2, referencedFragmentNames] = getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment);\n    // Do not compare a fragment's fieldMap to itself.\n    if (fieldMap === fieldMap2) {\n        return;\n    }\n    // (D) First collect any conflicts between the provided collection of fields\n    // and the collection of fields represented by the given fragment.\n    collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap, fieldMap2);\n    // (E) Then collect any conflicts between the provided collection of fields\n    // and any fragment names found in the given fragment.\n    for (const referencedFragmentName of referencedFragmentNames) {\n        // Memoize so two fragments are not compared for conflicts more than once.\n        if (comparedFragmentPairs.has(referencedFragmentName, fragmentName, areMutuallyExclusive)) {\n            continue;\n        }\n        comparedFragmentPairs.add(referencedFragmentName, fragmentName, areMutuallyExclusive);\n        collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap, referencedFragmentName);\n    }\n}\n// Collect all conflicts found between two fragments, including via spreading in\n// any nested fragments.\nfunction collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fragmentName1, fragmentName2) {\n    // No need to compare a fragment to itself.\n    if (fragmentName1 === fragmentName2) {\n        return;\n    }\n    // Memoize so two fragments are not compared for conflicts more than once.\n    if (comparedFragmentPairs.has(fragmentName1, fragmentName2, areMutuallyExclusive)) {\n        return;\n    }\n    comparedFragmentPairs.add(fragmentName1, fragmentName2, areMutuallyExclusive);\n    const fragment1 = context.getFragment(fragmentName1);\n    const fragment2 = context.getFragment(fragmentName2);\n    if (!fragment1 || !fragment2) {\n        return;\n    }\n    const [fieldMap1, referencedFragmentNames1] = getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment1);\n    const [fieldMap2, referencedFragmentNames2] = getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment2);\n    // (F) First, collect all conflicts between these two collections of fields\n    // (not including any nested fragments).\n    collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap1, fieldMap2);\n    // (G) Then collect conflicts between the first fragment and any nested\n    // fragments spread in the second fragment.\n    for (const referencedFragmentName2 of referencedFragmentNames2) {\n        collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fragmentName1, referencedFragmentName2);\n    }\n    // (G) Then collect conflicts between the second fragment and any nested\n    // fragments spread in the first fragment.\n    for (const referencedFragmentName1 of referencedFragmentNames1) {\n        collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, referencedFragmentName1, fragmentName2);\n    }\n}\n// Find all conflicts found between two selection sets, including those found\n// via spreading in fragments. Called when determining if conflicts exist\n// between the sub-fields of two overlapping fields.\nfunction findConflictsBetweenSubSelectionSets(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, parentType1, selectionSet1, parentType2, selectionSet2) {\n    const conflicts = [];\n    const [fieldMap1, fragmentNames1] = getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType1, selectionSet1);\n    const [fieldMap2, fragmentNames2] = getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType2, selectionSet2);\n    // (H) First, collect all conflicts between these two collections of field.\n    collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap1, fieldMap2);\n    // (I) Then collect conflicts between the first collection of fields and\n    // those referenced by each fragment name associated with the second.\n    for (const fragmentName2 of fragmentNames2) {\n        collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap1, fragmentName2);\n    }\n    // (I) Then collect conflicts between the second collection of fields and\n    // those referenced by each fragment name associated with the first.\n    for (const fragmentName1 of fragmentNames1) {\n        collectConflictsBetweenFieldsAndFragment(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fieldMap2, fragmentName1);\n    }\n    // (J) Also collect conflicts between any fragment names by the first and\n    // fragment names by the second. This compares each item in the first set of\n    // names to each item in the second set of names.\n    for (const fragmentName1 of fragmentNames1) {\n        for (const fragmentName2 of fragmentNames2) {\n            collectConflictsBetweenFragments(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, fragmentName1, fragmentName2);\n        }\n    }\n    return conflicts;\n}\n// Collect all Conflicts \"within\" one collection of fields.\nfunction collectConflictsWithin(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, fieldMap) {\n    // A field map is a keyed collection, where each key represents a response\n    // name and the value at that key is a list of all fields which provide that\n    // response name. For every response name, if there are multiple fields, they\n    // must be compared to find a potential conflict.\n    for (const [responseName, fields] of Object.entries(fieldMap)) {\n        // This compares every field in the list to every other field in this list\n        // (except to itself). If the list only has one item, nothing needs to\n        // be compared.\n        if (fields.length > 1) {\n            for (let i = 0; i < fields.length; i++) {\n                for (let j = i + 1; j < fields.length; j++) {\n                    const conflict = findConflict(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, false, // within one collection is never mutually exclusive\n                    responseName, fields[i], fields[j]);\n                    if (conflict) {\n                        conflicts.push(conflict);\n                    }\n                }\n            }\n        }\n    }\n}\n// Collect all Conflicts between two collections of fields. This is similar to,\n// but different from the `collectConflictsWithin` function above. This check\n// assumes that `collectConflictsWithin` has already been called on each\n// provided collection of fields. This is true because this validator traverses\n// each individual selection set.\nfunction collectConflictsBetween(context, conflicts, cachedFieldsAndFragmentNames, comparedFragmentPairs, parentFieldsAreMutuallyExclusive, fieldMap1, fieldMap2) {\n    // A field map is a keyed collection, where each key represents a response\n    // name and the value at that key is a list of all fields which provide that\n    // response name. For any response name which appears in both provided field\n    // maps, each field from the first field map must be compared to every field\n    // in the second field map to find potential conflicts.\n    for (const [responseName, fields1] of Object.entries(fieldMap1)) {\n        const fields2 = fieldMap2[responseName];\n        if (fields2) {\n            for (const field1 of fields1) {\n                for (const field2 of fields2) {\n                    const conflict = findConflict(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, parentFieldsAreMutuallyExclusive, responseName, field1, field2);\n                    if (conflict) {\n                        conflicts.push(conflict);\n                    }\n                }\n            }\n        }\n    }\n}\n// Determines if there is a conflict between two particular fields, including\n// comparing their sub-fields.\nfunction findConflict(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, parentFieldsAreMutuallyExclusive, responseName, field1, field2) {\n    const [parentType1, node1, def1] = field1;\n    const [parentType2, node2, def2] = field2;\n    // If it is known that two fields could not possibly apply at the same\n    // time, due to the parent types, then it is safe to permit them to diverge\n    // in aliased field or arguments used as they will not present any ambiguity\n    // by differing.\n    // It is known that two parent types could never overlap if they are\n    // different Object types. Interface or Union types might overlap - if not\n    // in the current state of the schema, then perhaps in some future version,\n    // thus may not safely diverge.\n    const areMutuallyExclusive = parentFieldsAreMutuallyExclusive ||\n        (parentType1 !== parentType2 && (0, graphql_1.isObjectType)(parentType1) && (0, graphql_1.isObjectType)(parentType2));\n    if (!areMutuallyExclusive) {\n        // Two aliases must refer to the same field.\n        const name1 = node1.name.value;\n        const name2 = node2.name.value;\n        if (name1 !== name2) {\n            return [[responseName, `\"${name1}\" and \"${name2}\" are different fields`], [node1], [node2]];\n        }\n        // Two field calls must have the same arguments.\n        if (stringifyArguments(node1) !== stringifyArguments(node2)) {\n            return [[responseName, 'they have differing arguments'], [node1], [node2]];\n        }\n    }\n    // FIXME https://github.com/graphql/graphql-js/issues/2203\n    const directives1 = /* c8 ignore next */ node1.directives ?? [];\n    const directives2 = /* c8 ignore next */ node2.directives ?? [];\n    if (!sameStreams(directives1, directives2)) {\n        return [[responseName, 'they have differing stream directives'], [node1], [node2]];\n    }\n    // The return type for each field.\n    const type1 = def1?.type;\n    const type2 = def2?.type;\n    if (type1 && type2 && doTypesConflict(type1, type2)) {\n        return [\n            [responseName, `they return conflicting types \"${(0, utils_1.inspect)(type1)}\" and \"${(0, utils_1.inspect)(type2)}\"`],\n            [node1],\n            [node2],\n        ];\n    }\n    // Collect and compare sub-fields. Use the same \"visited fragment names\" list\n    // for both collections so fields in a fragment reference are never\n    // compared to themselves.\n    const selectionSet1 = node1.selectionSet;\n    const selectionSet2 = node2.selectionSet;\n    if (selectionSet1 && selectionSet2) {\n        const conflicts = findConflictsBetweenSubSelectionSets(context, cachedFieldsAndFragmentNames, comparedFragmentPairs, areMutuallyExclusive, (0, graphql_1.getNamedType)(type1), selectionSet1, (0, graphql_1.getNamedType)(type2), selectionSet2);\n        return subfieldConflicts(conflicts, responseName, node1, node2);\n    }\n    return;\n}\nfunction stringifyArguments(fieldNode) {\n    // FIXME https://github.com/graphql/graphql-js/issues/2203\n    const args = /* c8 ignore next */ fieldNode.arguments ?? [];\n    const inputObjectWithArgs = {\n        kind: graphql_1.Kind.OBJECT,\n        fields: args.map(argNode => ({\n            kind: graphql_1.Kind.OBJECT_FIELD,\n            name: argNode.name,\n            value: argNode.value,\n        })),\n    };\n    return (0, graphql_1.print)(sortValueNode(inputObjectWithArgs));\n}\nfunction getStreamDirective(directives) {\n    return directives.find(directive => directive.name.value === 'stream');\n}\nfunction sameStreams(directives1, directives2) {\n    const stream1 = getStreamDirective(directives1);\n    const stream2 = getStreamDirective(directives2);\n    if (!stream1 && !stream2) {\n        // both fields do not have streams\n        return true;\n    }\n    if (stream1 && stream2) {\n        // check if both fields have equivalent streams\n        return stringifyArguments(stream1) === stringifyArguments(stream2);\n    }\n    // fields have a mix of stream and no stream\n    return false;\n}\n// Two types conflict if both types could not apply to a value simultaneously.\n// Composite types are ignored as their individual field types will be compared\n// later recursively. However List and Non-Null types must match.\nfunction doTypesConflict(type1, type2) {\n    if ((0, graphql_1.isListType)(type1)) {\n        return (0, graphql_1.isListType)(type2) ? doTypesConflict(type1.ofType, type2.ofType) : true;\n    }\n    if ((0, graphql_1.isListType)(type2)) {\n        return true;\n    }\n    if ((0, graphql_1.isNonNullType)(type1)) {\n        return (0, graphql_1.isNonNullType)(type2) ? doTypesConflict(type1.ofType, type2.ofType) : true;\n    }\n    if ((0, graphql_1.isNonNullType)(type2)) {\n        return true;\n    }\n    if ((0, graphql_1.isLeafType)(type1) || (0, graphql_1.isLeafType)(type2)) {\n        return type1 !== type2;\n    }\n    return false;\n}\n// Given a selection set, return the collection of fields (a mapping of response\n// name to field nodes and definitions) as well as a list of fragment names\n// referenced via fragment spreads.\nfunction getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, parentType, selectionSet) {\n    const cached = cachedFieldsAndFragmentNames.get(selectionSet);\n    if (cached) {\n        return cached;\n    }\n    const nodeAndDefs = Object.create(null);\n    const fragmentNames = new Set();\n    _collectFieldsAndFragmentNames(context, parentType, selectionSet, nodeAndDefs, fragmentNames);\n    const result = [nodeAndDefs, [...fragmentNames]];\n    cachedFieldsAndFragmentNames.set(selectionSet, result);\n    return result;\n}\n// Given a reference to a fragment, return the represented collection of fields\n// as well as a list of nested fragment names referenced via fragment spreads.\nfunction getReferencedFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragment) {\n    // Short-circuit building a type from the node if possible.\n    const cached = cachedFieldsAndFragmentNames.get(fragment.selectionSet);\n    if (cached) {\n        return cached;\n    }\n    const fragmentType = (0, graphql_1.typeFromAST)(context.getSchema(), fragment.typeCondition);\n    return getFieldsAndFragmentNames(context, cachedFieldsAndFragmentNames, fragmentType, fragment.selectionSet);\n}\nfunction _collectFieldsAndFragmentNames(context, parentType, selectionSet, nodeAndDefs, fragmentNames) {\n    for (const selection of selectionSet.selections) {\n        switch (selection.kind) {\n            case graphql_1.Kind.FIELD: {\n                const fieldName = selection.name.value;\n                let fieldDef;\n                if ((0, graphql_1.isObjectType)(parentType) || (0, graphql_1.isInterfaceType)(parentType)) {\n                    fieldDef = parentType.getFields()[fieldName];\n                }\n                const responseName = selection.alias ? selection.alias.value : fieldName;\n                nodeAndDefs[responseName] ||= [];\n                nodeAndDefs[responseName].push([parentType, selection, fieldDef]);\n                break;\n            }\n            case graphql_1.Kind.FRAGMENT_SPREAD:\n                fragmentNames.add(selection.name.value);\n                break;\n            case graphql_1.Kind.INLINE_FRAGMENT: {\n                const typeCondition = selection.typeCondition;\n                const inlineFragmentType = typeCondition\n                    ? (0, graphql_1.typeFromAST)(context.getSchema(), typeCondition)\n                    : parentType;\n                _collectFieldsAndFragmentNames(context, inlineFragmentType, selection.selectionSet, nodeAndDefs, fragmentNames);\n                break;\n            }\n        }\n    }\n}\n// Given a series of Conflicts which occurred between two sub-fields, generate\n// a single Conflict.\nfunction subfieldConflicts(conflicts, responseName, node1, node2) {\n    if (conflicts.length > 0) {\n        return [\n            [responseName, conflicts.map(([reason]) => reason)],\n            [node1, ...conflicts.map(([, fields1]) => fields1).flat()],\n            [node2, ...conflicts.map(([, , fields2]) => fields2).flat()],\n        ];\n    }\n    return;\n}\n/**\n * A way to keep track of pairs of things when the ordering of the pair does not matter.\n */\nclass PairSet {\n    _data;\n    constructor() {\n        this._data = new Map();\n    }\n    has(a, b, areMutuallyExclusive) {\n        const [key1, key2] = a < b ? [a, b] : [b, a];\n        const result = this._data.get(key1)?.get(key2);\n        if (result === undefined) {\n            return false;\n        }\n        // areMutuallyExclusive being false is a superset of being true, hence if\n        // we want to know if this PairSet \"has\" these two with no exclusivity,\n        // we have to ensure it was added as such.\n        return areMutuallyExclusive ? true : areMutuallyExclusive === result;\n    }\n    add(a, b, areMutuallyExclusive) {\n        const [key1, key2] = a < b ? [a, b] : [b, a];\n        const map = this._data.get(key1);\n        if (map === undefined) {\n            this._data.set(key1, new Map([[key2, areMutuallyExclusive]]));\n        }\n        else {\n            map.set(key2, areMutuallyExclusive);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/overlapping-fields-can-be-merged.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/stream-directive-on-list-field.js":
/*!***********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/stream-directive-on-list-field.js ***!
  \***********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.StreamDirectiveOnListFieldRule = StreamDirectiveOnListFieldRule;\nconst graphql_1 = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/.pnpm/graphql@16.9.0/node_modules/graphql/index.mjs\");\nconst utils_1 = __webpack_require__(/*! @graphql-tools/utils */ \"(rsc)/./node_modules/.pnpm/@graphql-tools+utils@10.5.4_graphql@16.9.0/node_modules/@graphql-tools/utils/cjs/index.js\");\n/**\n * Stream directive on list field\n *\n * A GraphQL document is only valid if stream directives are used on list fields.\n */\nfunction StreamDirectiveOnListFieldRule(context) {\n    return {\n        Directive(node) {\n            const fieldDef = context.getFieldDef();\n            const parentType = context.getParentType();\n            if (fieldDef &&\n                parentType &&\n                node.name.value === utils_1.GraphQLStreamDirective.name &&\n                !((0, graphql_1.isListType)(fieldDef.type) ||\n                    ((0, graphql_1.isWrappingType)(fieldDef.type) && (0, graphql_1.isListType)(fieldDef.type.ofType)))) {\n                context.reportError((0, utils_1.createGraphQLError)(`Stream directive cannot be used on non-list field \"${fieldDef.name}\" on type \"${parentType.name}\".`, { nodes: node }));\n            }\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-yoga+plugin-defer-stream@3.7.0_graphql-yoga@5.7.0_graphql@16.9.0__graphql@16.9.0/node_modules/@graphql-yoga/plugin-defer-stream/cjs/validations/stream-directive-on-list-field.js\n");

/***/ })

};
;