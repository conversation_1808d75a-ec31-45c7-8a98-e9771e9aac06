"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+credential-provider-sso@3.830.0";
exports.ids = ["vendor-chunks/@aws-sdk+credential-provider-sso@3.830.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/fromSSO.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/fromSSO.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromSSO: () => (/* binding */ fromSSO)\n/* harmony export */ });\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var _smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/shared-ini-file-loader */ \"(rsc)/./node_modules/.pnpm/@smithy+shared-ini-file-loader@4.0.4/node_modules/@smithy/shared-ini-file-loader/dist-es/index.js\");\n/* harmony import */ var _isSsoProfile__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isSsoProfile */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/isSsoProfile.js\");\n/* harmony import */ var _resolveSSOCredentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resolveSSOCredentials */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/resolveSSOCredentials.js\");\n/* harmony import */ var _validateSsoProfile__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./validateSsoProfile */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/validateSsoProfile.js\");\n\n\n\n\n\nconst fromSSO = (init = {}) => async ({ callerClientConfig } = {}) => {\n    init.logger?.debug(\"@aws-sdk/credential-provider-sso - fromSSO\");\n    const { ssoStartUrl, ssoAccountId, ssoRegion, ssoRoleName, ssoSession } = init;\n    const { ssoClient } = init;\n    const profileName = (0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_1__.getProfileName)({\n        profile: init.profile ?? callerClientConfig?.profile,\n    });\n    if (!ssoStartUrl && !ssoAccountId && !ssoRegion && !ssoRoleName && !ssoSession) {\n        const profiles = await (0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_1__.parseKnownFiles)(init);\n        const profile = profiles[profileName];\n        if (!profile) {\n            throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`Profile ${profileName} was not found.`, { logger: init.logger });\n        }\n        if (!(0,_isSsoProfile__WEBPACK_IMPORTED_MODULE_2__.isSsoProfile)(profile)) {\n            throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`Profile ${profileName} is not configured with SSO credentials.`, {\n                logger: init.logger,\n            });\n        }\n        if (profile?.sso_session) {\n            const ssoSessions = await (0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_1__.loadSsoSessionData)(init);\n            const session = ssoSessions[profile.sso_session];\n            const conflictMsg = ` configurations in profile ${profileName} and sso-session ${profile.sso_session}`;\n            if (ssoRegion && ssoRegion !== session.sso_region) {\n                throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`Conflicting SSO region` + conflictMsg, {\n                    tryNextLink: false,\n                    logger: init.logger,\n                });\n            }\n            if (ssoStartUrl && ssoStartUrl !== session.sso_start_url) {\n                throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`Conflicting SSO start_url` + conflictMsg, {\n                    tryNextLink: false,\n                    logger: init.logger,\n                });\n            }\n            profile.sso_region = session.sso_region;\n            profile.sso_start_url = session.sso_start_url;\n        }\n        const { sso_start_url, sso_account_id, sso_region, sso_role_name, sso_session } = (0,_validateSsoProfile__WEBPACK_IMPORTED_MODULE_4__.validateSsoProfile)(profile, init.logger);\n        return (0,_resolveSSOCredentials__WEBPACK_IMPORTED_MODULE_3__.resolveSSOCredentials)({\n            ssoStartUrl: sso_start_url,\n            ssoSession: sso_session,\n            ssoAccountId: sso_account_id,\n            ssoRegion: sso_region,\n            ssoRoleName: sso_role_name,\n            ssoClient: ssoClient,\n            clientConfig: init.clientConfig,\n            parentClientConfig: init.parentClientConfig,\n            profile: profileName,\n        });\n    }\n    else if (!ssoStartUrl || !ssoAccountId || !ssoRegion || !ssoRoleName) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(\"Incomplete configuration. The fromSSO() argument hash must include \" +\n            '\"ssoStartUrl\", \"ssoAccountId\", \"ssoRegion\", \"ssoRoleName\"', { tryNextLink: false, logger: init.logger });\n    }\n    else {\n        return (0,_resolveSSOCredentials__WEBPACK_IMPORTED_MODULE_3__.resolveSSOCredentials)({\n            ssoStartUrl,\n            ssoSession,\n            ssoAccountId,\n            ssoRegion,\n            ssoRoleName,\n            ssoClient,\n            clientConfig: init.clientConfig,\n            parentClientConfig: init.parentClientConfig,\n            profile: profileName,\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/fromSSO.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/index.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/index.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromSSO: () => (/* reexport safe */ _fromSSO__WEBPACK_IMPORTED_MODULE_0__.fromSSO),\n/* harmony export */   isSsoProfile: () => (/* reexport safe */ _isSsoProfile__WEBPACK_IMPORTED_MODULE_1__.isSsoProfile),\n/* harmony export */   validateSsoProfile: () => (/* reexport safe */ _validateSsoProfile__WEBPACK_IMPORTED_MODULE_3__.validateSsoProfile)\n/* harmony export */ });\n/* harmony import */ var _fromSSO__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fromSSO */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/fromSSO.js\");\n/* harmony import */ var _isSsoProfile__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./isSsoProfile */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/isSsoProfile.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/types.js\");\n/* harmony import */ var _validateSsoProfile__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./validateSsoProfile */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/validateSsoProfile.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1zc29AMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1zc28vZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTBCO0FBQ0s7QUFDUDtBQUNhIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1zc29AMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1zc28vZGlzdC1lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9mcm9tU1NPXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9pc1Nzb1Byb2ZpbGVcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3R5cGVzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi92YWxpZGF0ZVNzb1Byb2ZpbGVcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/isSsoProfile.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/isSsoProfile.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSsoProfile: () => (/* binding */ isSsoProfile)\n/* harmony export */ });\nconst isSsoProfile = (arg) => arg &&\n    (typeof arg.sso_start_url === \"string\" ||\n        typeof arg.sso_account_id === \"string\" ||\n        typeof arg.sso_session === \"string\" ||\n        typeof arg.sso_region === \"string\" ||\n        typeof arg.sso_role_name === \"string\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1zc29AMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1zc28vZGlzdC1lcy9pc1Nzb1Byb2ZpbGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK2NyZWRlbnRpYWwtcHJvdmlkZXItc3NvQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItc3NvL2Rpc3QtZXMvaXNTc29Qcm9maWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBpc1Nzb1Byb2ZpbGUgPSAoYXJnKSA9PiBhcmcgJiZcbiAgICAodHlwZW9mIGFyZy5zc29fc3RhcnRfdXJsID09PSBcInN0cmluZ1wiIHx8XG4gICAgICAgIHR5cGVvZiBhcmcuc3NvX2FjY291bnRfaWQgPT09IFwic3RyaW5nXCIgfHxcbiAgICAgICAgdHlwZW9mIGFyZy5zc29fc2Vzc2lvbiA9PT0gXCJzdHJpbmdcIiB8fFxuICAgICAgICB0eXBlb2YgYXJnLnNzb19yZWdpb24gPT09IFwic3RyaW5nXCIgfHxcbiAgICAgICAgdHlwZW9mIGFyZy5zc29fcm9sZV9uYW1lID09PSBcInN0cmluZ1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/isSsoProfile.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/loadSso.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/loadSso.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GetRoleCredentialsCommand: () => (/* reexport safe */ _aws_sdk_client_sso__WEBPACK_IMPORTED_MODULE_0__.GetRoleCredentialsCommand),\n/* harmony export */   SSOClient: () => (/* reexport safe */ _aws_sdk_client_sso__WEBPACK_IMPORTED_MODULE_1__.SSOClient)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_client_sso__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/client-sso */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/commands/GetRoleCredentialsCommand.js\");\n/* harmony import */ var _aws_sdk_client_sso__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/client-sso */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/SSOClient.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1zc29AMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1zc28vZGlzdC1lcy9sb2FkU3NvLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMkU7QUFDM0IiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytjcmVkZW50aWFsLXByb3ZpZGVyLXNzb0AzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jcmVkZW50aWFsLXByb3ZpZGVyLXNzby9kaXN0LWVzL2xvYWRTc28uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgR2V0Um9sZUNyZWRlbnRpYWxzQ29tbWFuZCwgU1NPQ2xpZW50IH0gZnJvbSBcIkBhd3Mtc2RrL2NsaWVudC1zc29cIjtcbmV4cG9ydCB7IEdldFJvbGVDcmVkZW50aWFsc0NvbW1hbmQsIFNTT0NsaWVudCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/loadSso.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/resolveSSOCredentials.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/resolveSSOCredentials.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveSSOCredentials: () => (/* binding */ resolveSSOCredentials)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @aws-sdk/core/client */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\");\n/* harmony import */ var _aws_sdk_token_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/token-providers */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+token-providers@3.830.0/node_modules/@aws-sdk/token-providers/dist-es/fromSso.js\");\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var _smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/shared-ini-file-loader */ \"(rsc)/./node_modules/.pnpm/@smithy+shared-ini-file-loader@4.0.4/node_modules/@smithy/shared-ini-file-loader/dist-es/index.js\");\n\n\n\n\nconst SHOULD_FAIL_CREDENTIAL_CHAIN = false;\nconst resolveSSOCredentials = async ({ ssoStartUrl, ssoSession, ssoAccountId, ssoRegion, ssoRoleName, ssoClient, clientConfig, parentClientConfig, profile, logger, }) => {\n    let token;\n    const refreshMessage = `To refresh this SSO session run aws sso login with the corresponding profile.`;\n    if (ssoSession) {\n        try {\n            const _token = await (0,_aws_sdk_token_providers__WEBPACK_IMPORTED_MODULE_2__.fromSso)({ profile })();\n            token = {\n                accessToken: _token.token,\n                expiresAt: new Date(_token.expiration).toISOString(),\n            };\n        }\n        catch (e) {\n            throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(e.message, {\n                tryNextLink: SHOULD_FAIL_CREDENTIAL_CHAIN,\n                logger,\n            });\n        }\n    }\n    else {\n        try {\n            token = await (0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_1__.getSSOTokenFromFile)(ssoStartUrl);\n        }\n        catch (e) {\n            throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`The SSO session associated with this profile is invalid. ${refreshMessage}`, {\n                tryNextLink: SHOULD_FAIL_CREDENTIAL_CHAIN,\n                logger,\n            });\n        }\n    }\n    if (new Date(token.expiresAt).getTime() - Date.now() <= 0) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`The SSO session associated with this profile has expired. ${refreshMessage}`, {\n            tryNextLink: SHOULD_FAIL_CREDENTIAL_CHAIN,\n            logger,\n        });\n    }\n    const { accessToken } = token;\n    const { SSOClient, GetRoleCredentialsCommand } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@aws-sdk+credential-provider-sso@3.830.0\"), __webpack_require__.e(\"vendor-chunks/@aws-sdk+client-sso@3.830.0\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./loadSso */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/loadSso.js\"));\n    const sso = ssoClient ||\n        new SSOClient(Object.assign({}, clientConfig ?? {}, {\n            logger: clientConfig?.logger ?? parentClientConfig?.logger,\n            region: clientConfig?.region ?? ssoRegion,\n        }));\n    let ssoResp;\n    try {\n        ssoResp = await sso.send(new GetRoleCredentialsCommand({\n            accountId: ssoAccountId,\n            roleName: ssoRoleName,\n            accessToken,\n        }));\n    }\n    catch (e) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(e, {\n            tryNextLink: SHOULD_FAIL_CREDENTIAL_CHAIN,\n            logger,\n        });\n    }\n    const { roleCredentials: { accessKeyId, secretAccessKey, sessionToken, expiration, credentialScope, accountId } = {}, } = ssoResp;\n    if (!accessKeyId || !secretAccessKey || !sessionToken || !expiration) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(\"SSO returns an invalid temporary credential.\", {\n            tryNextLink: SHOULD_FAIL_CREDENTIAL_CHAIN,\n            logger,\n        });\n    }\n    const credentials = {\n        accessKeyId,\n        secretAccessKey,\n        sessionToken,\n        expiration: new Date(expiration),\n        ...(credentialScope && { credentialScope }),\n        ...(accountId && { accountId }),\n    };\n    if (ssoSession) {\n        (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_3__.setCredentialFeature)(credentials, \"CREDENTIALS_SSO\", \"s\");\n    }\n    else {\n        (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_3__.setCredentialFeature)(credentials, \"CREDENTIALS_SSO_LEGACY\", \"u\");\n    }\n    return credentials;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/resolveSSOCredentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/types.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/types.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1zc29AMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1zc28vZGlzdC1lcy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiO0FBQVUiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytjcmVkZW50aWFsLXByb3ZpZGVyLXNzb0AzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jcmVkZW50aWFsLXByb3ZpZGVyLXNzby9kaXN0LWVzL3R5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/validateSsoProfile.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/validateSsoProfile.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validateSsoProfile: () => (/* binding */ validateSsoProfile)\n/* harmony export */ });\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n\nconst validateSsoProfile = (profile, logger) => {\n    const { sso_start_url, sso_account_id, sso_region, sso_role_name } = profile;\n    if (!sso_start_url || !sso_account_id || !sso_region || !sso_role_name) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`Profile is configured with invalid SSO credentials. Required parameters \"sso_account_id\", ` +\n            `\"sso_region\", \"sso_role_name\", \"sso_start_url\". Got ${Object.keys(profile).join(\", \")}\\nReference: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html`, { tryNextLink: false, logger });\n    }\n    return profile;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1zc29AMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1zc28vZGlzdC1lcy92YWxpZGF0ZVNzb1Byb2ZpbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUU7QUFDOUQ7QUFDUCxZQUFZLDJEQUEyRDtBQUN2RTtBQUNBLGtCQUFrQiwrRUFBd0I7QUFDMUMsbUVBQW1FLGdDQUFnQyx5RkFBeUYsNEJBQTRCO0FBQ3hOO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK2NyZWRlbnRpYWwtcHJvdmlkZXItc3NvQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItc3NvL2Rpc3QtZXMvdmFsaWRhdGVTc29Qcm9maWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENyZWRlbnRpYWxzUHJvdmlkZXJFcnJvciB9IGZyb20gXCJAc21pdGh5L3Byb3BlcnR5LXByb3ZpZGVyXCI7XG5leHBvcnQgY29uc3QgdmFsaWRhdGVTc29Qcm9maWxlID0gKHByb2ZpbGUsIGxvZ2dlcikgPT4ge1xuICAgIGNvbnN0IHsgc3NvX3N0YXJ0X3VybCwgc3NvX2FjY291bnRfaWQsIHNzb19yZWdpb24sIHNzb19yb2xlX25hbWUgfSA9IHByb2ZpbGU7XG4gICAgaWYgKCFzc29fc3RhcnRfdXJsIHx8ICFzc29fYWNjb3VudF9pZCB8fCAhc3NvX3JlZ2lvbiB8fCAhc3NvX3JvbGVfbmFtZSkge1xuICAgICAgICB0aHJvdyBuZXcgQ3JlZGVudGlhbHNQcm92aWRlckVycm9yKGBQcm9maWxlIGlzIGNvbmZpZ3VyZWQgd2l0aCBpbnZhbGlkIFNTTyBjcmVkZW50aWFscy4gUmVxdWlyZWQgcGFyYW1ldGVycyBcInNzb19hY2NvdW50X2lkXCIsIGAgK1xuICAgICAgICAgICAgYFwic3NvX3JlZ2lvblwiLCBcInNzb19yb2xlX25hbWVcIiwgXCJzc29fc3RhcnRfdXJsXCIuIEdvdCAke09iamVjdC5rZXlzKHByb2ZpbGUpLmpvaW4oXCIsIFwiKX1cXG5SZWZlcmVuY2U6IGh0dHBzOi8vZG9jcy5hd3MuYW1hem9uLmNvbS9jbGkvbGF0ZXN0L3VzZXJndWlkZS9jbGktY29uZmlndXJlLXNzby5odG1sYCwgeyB0cnlOZXh0TGluazogZmFsc2UsIGxvZ2dlciB9KTtcbiAgICB9XG4gICAgcmV0dXJuIHByb2ZpbGU7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/validateSsoProfile.js\n");

/***/ })

};
;