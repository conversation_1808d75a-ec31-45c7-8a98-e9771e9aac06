"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+service-error-classification@4.0.5";
exports.ids = ["vendor-chunks/@smithy+service-error-classification@4.0.5"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+service-error-classification@4.0.5/node_modules/@smithy/service-error-classification/dist-es/constants.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+service-error-classification@4.0.5/node_modules/@smithy/service-error-classification/dist-es/constants.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CLOCK_SKEW_ERROR_CODES: () => (/* binding */ CLOCK_SKEW_ERROR_CODES),\n/* harmony export */   NODEJS_TIMEOUT_ERROR_CODES: () => (/* binding */ NODEJS_TIMEOUT_ERROR_CODES),\n/* harmony export */   THROTTLING_ERROR_CODES: () => (/* binding */ THROTTLING_ERROR_CODES),\n/* harmony export */   TRANSIENT_ERROR_CODES: () => (/* binding */ TRANSIENT_ERROR_CODES),\n/* harmony export */   TRANSIENT_ERROR_STATUS_CODES: () => (/* binding */ TRANSIENT_ERROR_STATUS_CODES)\n/* harmony export */ });\nconst CLOCK_SKEW_ERROR_CODES = [\n    \"AuthFailure\",\n    \"InvalidSignatureException\",\n    \"RequestExpired\",\n    \"RequestInTheFuture\",\n    \"RequestTimeTooSkewed\",\n    \"SignatureDoesNotMatch\",\n];\nconst THROTTLING_ERROR_CODES = [\n    \"BandwidthLimitExceeded\",\n    \"EC2ThrottledException\",\n    \"LimitExceededException\",\n    \"PriorRequestNotComplete\",\n    \"ProvisionedThroughputExceededException\",\n    \"RequestLimitExceeded\",\n    \"RequestThrottled\",\n    \"RequestThrottledException\",\n    \"SlowDown\",\n    \"ThrottledException\",\n    \"Throttling\",\n    \"ThrottlingException\",\n    \"TooManyRequestsException\",\n    \"TransactionInProgressException\",\n];\nconst TRANSIENT_ERROR_CODES = [\"TimeoutError\", \"RequestTimeout\", \"RequestTimeoutException\"];\nconst TRANSIENT_ERROR_STATUS_CODES = [500, 502, 503, 504];\nconst NODEJS_TIMEOUT_ERROR_CODES = [\"ECONNRESET\", \"ECONNREFUSED\", \"EPIPE\", \"ETIMEDOUT\"];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+service-error-classification@4.0.5/node_modules/@smithy/service-error-classification/dist-es/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+service-error-classification@4.0.5/node_modules/@smithy/service-error-classification/dist-es/index.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+service-error-classification@4.0.5/node_modules/@smithy/service-error-classification/dist-es/index.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowserNetworkError: () => (/* binding */ isBrowserNetworkError),\n/* harmony export */   isClockSkewCorrectedError: () => (/* binding */ isClockSkewCorrectedError),\n/* harmony export */   isClockSkewError: () => (/* binding */ isClockSkewError),\n/* harmony export */   isRetryableByTrait: () => (/* binding */ isRetryableByTrait),\n/* harmony export */   isServerError: () => (/* binding */ isServerError),\n/* harmony export */   isThrottlingError: () => (/* binding */ isThrottlingError),\n/* harmony export */   isTransientError: () => (/* binding */ isTransientError)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+service-error-classification@4.0.5/node_modules/@smithy/service-error-classification/dist-es/constants.js\");\n\nconst isRetryableByTrait = (error) => error.$retryable !== undefined;\nconst isClockSkewError = (error) => _constants__WEBPACK_IMPORTED_MODULE_0__.CLOCK_SKEW_ERROR_CODES.includes(error.name);\nconst isClockSkewCorrectedError = (error) => error.$metadata?.clockSkewCorrected;\nconst isBrowserNetworkError = (error) => {\n    const errorMessages = new Set([\n        \"Failed to fetch\",\n        \"NetworkError when attempting to fetch resource\",\n        \"The Internet connection appears to be offline\",\n        \"Load failed\",\n        \"Network request failed\",\n    ]);\n    const isValid = error && error instanceof TypeError;\n    if (!isValid) {\n        return false;\n    }\n    return errorMessages.has(error.message);\n};\nconst isThrottlingError = (error) => error.$metadata?.httpStatusCode === 429 ||\n    _constants__WEBPACK_IMPORTED_MODULE_0__.THROTTLING_ERROR_CODES.includes(error.name) ||\n    error.$retryable?.throttling == true;\nconst isTransientError = (error, depth = 0) => isClockSkewCorrectedError(error) ||\n    _constants__WEBPACK_IMPORTED_MODULE_0__.TRANSIENT_ERROR_CODES.includes(error.name) ||\n    _constants__WEBPACK_IMPORTED_MODULE_0__.NODEJS_TIMEOUT_ERROR_CODES.includes(error?.code || \"\") ||\n    _constants__WEBPACK_IMPORTED_MODULE_0__.TRANSIENT_ERROR_STATUS_CODES.includes(error.$metadata?.httpStatusCode || 0) ||\n    isBrowserNetworkError(error) ||\n    (error.cause !== undefined && depth <= 10 && isTransientError(error.cause, depth + 1));\nconst isServerError = (error) => {\n    if (error.$metadata?.httpStatusCode !== undefined) {\n        const statusCode = error.$metadata.httpStatusCode;\n        if (500 <= statusCode && statusCode <= 599 && !isTransientError(error)) {\n            return true;\n        }\n        return false;\n    }\n    return false;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+service-error-classification@4.0.5/node_modules/@smithy/service-error-classification/dist-es/index.js\n");

/***/ })

};
;