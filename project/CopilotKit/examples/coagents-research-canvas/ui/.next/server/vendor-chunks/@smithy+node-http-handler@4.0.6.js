"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+node-http-handler@4.0.6";
exports.ids = ["vendor-chunks/@smithy+node-http-handler@4.0.6"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/constants.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/constants.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NODEJS_TIMEOUT_ERROR_CODES: () => (/* binding */ NODEJS_TIMEOUT_ERROR_CODES)\n/* harmony export */ });\nconst NODEJS_TIMEOUT_ERROR_CODES = [\"ECONNRESET\", \"EPIPE\", \"ETIMEDOUT\"];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStub2RlLWh0dHAtaGFuZGxlckA0LjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS9ub2RlLWh0dHAtaGFuZGxlci9kaXN0LWVzL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8iLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K25vZGUtaHR0cC1oYW5kbGVyQDQuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L25vZGUtaHR0cC1oYW5kbGVyL2Rpc3QtZXMvY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBOT0RFSlNfVElNRU9VVF9FUlJPUl9DT0RFUyA9IFtcIkVDT05OUkVTRVRcIiwgXCJFUElQRVwiLCBcIkVUSU1FRE9VVFwiXTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/get-transformed-headers.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/get-transformed-headers.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTransformedHeaders: () => (/* binding */ getTransformedHeaders)\n/* harmony export */ });\nconst getTransformedHeaders = (headers) => {\n    const transformedHeaders = {};\n    for (const name of Object.keys(headers)) {\n        const headerValues = headers[name];\n        transformedHeaders[name] = Array.isArray(headerValues) ? headerValues.join(\",\") : headerValues;\n    }\n    return transformedHeaders;\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStub2RlLWh0dHAtaGFuZGxlckA0LjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS9ub2RlLWh0dHAtaGFuZGxlci9kaXN0LWVzL2dldC10cmFuc2Zvcm1lZC1oZWFkZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2lDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStub2RlLWh0dHAtaGFuZGxlckA0LjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS9ub2RlLWh0dHAtaGFuZGxlci9kaXN0LWVzL2dldC10cmFuc2Zvcm1lZC1oZWFkZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGdldFRyYW5zZm9ybWVkSGVhZGVycyA9IChoZWFkZXJzKSA9PiB7XG4gICAgY29uc3QgdHJhbnNmb3JtZWRIZWFkZXJzID0ge307XG4gICAgZm9yIChjb25zdCBuYW1lIG9mIE9iamVjdC5rZXlzKGhlYWRlcnMpKSB7XG4gICAgICAgIGNvbnN0IGhlYWRlclZhbHVlcyA9IGhlYWRlcnNbbmFtZV07XG4gICAgICAgIHRyYW5zZm9ybWVkSGVhZGVyc1tuYW1lXSA9IEFycmF5LmlzQXJyYXkoaGVhZGVyVmFsdWVzKSA/IGhlYWRlclZhbHVlcy5qb2luKFwiLFwiKSA6IGhlYWRlclZhbHVlcztcbiAgICB9XG4gICAgcmV0dXJuIHRyYW5zZm9ybWVkSGVhZGVycztcbn07XG5leHBvcnQgeyBnZXRUcmFuc2Zvcm1lZEhlYWRlcnMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/get-transformed-headers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/index.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_REQUEST_TIMEOUT: () => (/* reexport safe */ _node_http_handler__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_REQUEST_TIMEOUT),\n/* harmony export */   NodeHttp2Handler: () => (/* reexport safe */ _node_http2_handler__WEBPACK_IMPORTED_MODULE_1__.NodeHttp2Handler),\n/* harmony export */   NodeHttpHandler: () => (/* reexport safe */ _node_http_handler__WEBPACK_IMPORTED_MODULE_0__.NodeHttpHandler),\n/* harmony export */   streamCollector: () => (/* reexport safe */ _stream_collector__WEBPACK_IMPORTED_MODULE_2__.streamCollector)\n/* harmony export */ });\n/* harmony import */ var _node_http_handler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node-http-handler */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http-handler.js\");\n/* harmony import */ var _node_http2_handler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node-http2-handler */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http2-handler.js\");\n/* harmony import */ var _stream_collector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stream-collector */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/stream-collector/index.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStub2RlLWh0dHAtaGFuZGxlckA0LjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS9ub2RlLWh0dHAtaGFuZGxlci9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBb0M7QUFDQztBQUNGIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStub2RlLWh0dHAtaGFuZGxlckA0LjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS9ub2RlLWh0dHAtaGFuZGxlci9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL25vZGUtaHR0cC1oYW5kbGVyXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9ub2RlLWh0dHAyLWhhbmRsZXJcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3N0cmVhbS1jb2xsZWN0b3JcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http-handler.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http-handler.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_REQUEST_TIMEOUT: () => (/* binding */ DEFAULT_REQUEST_TIMEOUT),\n/* harmony export */   NodeHttpHandler: () => (/* binding */ NodeHttpHandler)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_querystring_builder__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/querystring-builder */ \"(rsc)/./node_modules/.pnpm/@smithy+querystring-builder@4.0.4/node_modules/@smithy/querystring-builder/dist-es/index.js\");\n/* harmony import */ var http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! http */ \"http\");\n/* harmony import */ var http__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(http__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var https__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! https */ \"https\");\n/* harmony import */ var https__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(https__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/constants.js\");\n/* harmony import */ var _get_transformed_headers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./get-transformed-headers */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/get-transformed-headers.js\");\n/* harmony import */ var _set_connection_timeout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./set-connection-timeout */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/set-connection-timeout.js\");\n/* harmony import */ var _set_socket_keep_alive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./set-socket-keep-alive */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/set-socket-keep-alive.js\");\n/* harmony import */ var _set_socket_timeout__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./set-socket-timeout */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/set-socket-timeout.js\");\n/* harmony import */ var _timing__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./timing */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/timing.js\");\n/* harmony import */ var _write_request_body__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./write-request-body */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/write-request-body.js\");\n\n\n\n\n\n\n\n\n\n\n\nconst DEFAULT_REQUEST_TIMEOUT = 0;\nclass NodeHttpHandler {\n    static create(instanceOrOptions) {\n        if (typeof instanceOrOptions?.handle === \"function\") {\n            return instanceOrOptions;\n        }\n        return new NodeHttpHandler(instanceOrOptions);\n    }\n    static checkSocketUsage(agent, socketWarningTimestamp, logger = console) {\n        const { sockets, requests, maxSockets } = agent;\n        if (typeof maxSockets !== \"number\" || maxSockets === Infinity) {\n            return socketWarningTimestamp;\n        }\n        const interval = 15000;\n        if (Date.now() - interval < socketWarningTimestamp) {\n            return socketWarningTimestamp;\n        }\n        if (sockets && requests) {\n            for (const origin in sockets) {\n                const socketsInUse = sockets[origin]?.length ?? 0;\n                const requestsEnqueued = requests[origin]?.length ?? 0;\n                if (socketsInUse >= maxSockets && requestsEnqueued >= 2 * maxSockets) {\n                    logger?.warn?.(`@smithy/node-http-handler:WARN - socket usage at capacity=${socketsInUse} and ${requestsEnqueued} additional requests are enqueued.\nSee https://docs.aws.amazon.com/sdk-for-javascript/v3/developer-guide/node-configuring-maxsockets.html\nor increase socketAcquisitionWarningTimeout=(millis) in the NodeHttpHandler config.`);\n                    return Date.now();\n                }\n            }\n        }\n        return socketWarningTimestamp;\n    }\n    constructor(options) {\n        this.socketWarningTimestamp = 0;\n        this.metadata = { handlerProtocol: \"http/1.1\" };\n        this.configProvider = new Promise((resolve, reject) => {\n            if (typeof options === \"function\") {\n                options()\n                    .then((_options) => {\n                    resolve(this.resolveDefaultConfig(_options));\n                })\n                    .catch(reject);\n            }\n            else {\n                resolve(this.resolveDefaultConfig(options));\n            }\n        });\n    }\n    resolveDefaultConfig(options) {\n        const { requestTimeout, connectionTimeout, socketTimeout, socketAcquisitionWarningTimeout, httpAgent, httpsAgent } = options || {};\n        const keepAlive = true;\n        const maxSockets = 50;\n        return {\n            connectionTimeout,\n            requestTimeout: requestTimeout ?? socketTimeout,\n            socketAcquisitionWarningTimeout,\n            httpAgent: (() => {\n                if (httpAgent instanceof http__WEBPACK_IMPORTED_MODULE_2__.Agent || typeof httpAgent?.destroy === \"function\") {\n                    return httpAgent;\n                }\n                return new http__WEBPACK_IMPORTED_MODULE_2__.Agent({ keepAlive, maxSockets, ...httpAgent });\n            })(),\n            httpsAgent: (() => {\n                if (httpsAgent instanceof https__WEBPACK_IMPORTED_MODULE_3__.Agent || typeof httpsAgent?.destroy === \"function\") {\n                    return httpsAgent;\n                }\n                return new https__WEBPACK_IMPORTED_MODULE_3__.Agent({ keepAlive, maxSockets, ...httpsAgent });\n            })(),\n            logger: console,\n        };\n    }\n    destroy() {\n        this.config?.httpAgent?.destroy();\n        this.config?.httpsAgent?.destroy();\n    }\n    async handle(request, { abortSignal } = {}) {\n        if (!this.config) {\n            this.config = await this.configProvider;\n        }\n        return new Promise((_resolve, _reject) => {\n            let writeRequestBodyPromise = undefined;\n            const timeouts = [];\n            const resolve = async (arg) => {\n                await writeRequestBodyPromise;\n                timeouts.forEach(_timing__WEBPACK_IMPORTED_MODULE_9__.timing.clearTimeout);\n                _resolve(arg);\n            };\n            const reject = async (arg) => {\n                await writeRequestBodyPromise;\n                timeouts.forEach(_timing__WEBPACK_IMPORTED_MODULE_9__.timing.clearTimeout);\n                _reject(arg);\n            };\n            if (!this.config) {\n                throw new Error(\"Node HTTP request handler config is not resolved\");\n            }\n            if (abortSignal?.aborted) {\n                const abortError = new Error(\"Request aborted\");\n                abortError.name = \"AbortError\";\n                reject(abortError);\n                return;\n            }\n            const isSSL = request.protocol === \"https:\";\n            const agent = isSSL ? this.config.httpsAgent : this.config.httpAgent;\n            timeouts.push(_timing__WEBPACK_IMPORTED_MODULE_9__.timing.setTimeout(() => {\n                this.socketWarningTimestamp = NodeHttpHandler.checkSocketUsage(agent, this.socketWarningTimestamp, this.config.logger);\n            }, this.config.socketAcquisitionWarningTimeout ??\n                (this.config.requestTimeout ?? 2000) + (this.config.connectionTimeout ?? 1000)));\n            const queryString = (0,_smithy_querystring_builder__WEBPACK_IMPORTED_MODULE_1__.buildQueryString)(request.query || {});\n            let auth = undefined;\n            if (request.username != null || request.password != null) {\n                const username = request.username ?? \"\";\n                const password = request.password ?? \"\";\n                auth = `${username}:${password}`;\n            }\n            let path = request.path;\n            if (queryString) {\n                path += `?${queryString}`;\n            }\n            if (request.fragment) {\n                path += `#${request.fragment}`;\n            }\n            let hostname = request.hostname ?? \"\";\n            if (hostname[0] === \"[\" && hostname.endsWith(\"]\")) {\n                hostname = request.hostname.slice(1, -1);\n            }\n            else {\n                hostname = request.hostname;\n            }\n            const nodeHttpsOptions = {\n                headers: request.headers,\n                host: hostname,\n                method: request.method,\n                path,\n                port: request.port,\n                agent,\n                auth,\n            };\n            const requestFunc = isSSL ? https__WEBPACK_IMPORTED_MODULE_3__.request : http__WEBPACK_IMPORTED_MODULE_2__.request;\n            const req = requestFunc(nodeHttpsOptions, (res) => {\n                const httpResponse = new _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpResponse({\n                    statusCode: res.statusCode || -1,\n                    reason: res.statusMessage,\n                    headers: (0,_get_transformed_headers__WEBPACK_IMPORTED_MODULE_5__.getTransformedHeaders)(res.headers),\n                    body: res,\n                });\n                resolve({ response: httpResponse });\n            });\n            req.on(\"error\", (err) => {\n                if (_constants__WEBPACK_IMPORTED_MODULE_4__.NODEJS_TIMEOUT_ERROR_CODES.includes(err.code)) {\n                    reject(Object.assign(err, { name: \"TimeoutError\" }));\n                }\n                else {\n                    reject(err);\n                }\n            });\n            if (abortSignal) {\n                const onAbort = () => {\n                    req.destroy();\n                    const abortError = new Error(\"Request aborted\");\n                    abortError.name = \"AbortError\";\n                    reject(abortError);\n                };\n                if (typeof abortSignal.addEventListener === \"function\") {\n                    const signal = abortSignal;\n                    signal.addEventListener(\"abort\", onAbort, { once: true });\n                    req.once(\"close\", () => signal.removeEventListener(\"abort\", onAbort));\n                }\n                else {\n                    abortSignal.onabort = onAbort;\n                }\n            }\n            timeouts.push((0,_set_connection_timeout__WEBPACK_IMPORTED_MODULE_6__.setConnectionTimeout)(req, reject, this.config.connectionTimeout));\n            timeouts.push((0,_set_socket_timeout__WEBPACK_IMPORTED_MODULE_8__.setSocketTimeout)(req, reject, this.config.requestTimeout));\n            const httpAgent = nodeHttpsOptions.agent;\n            if (typeof httpAgent === \"object\" && \"keepAlive\" in httpAgent) {\n                timeouts.push((0,_set_socket_keep_alive__WEBPACK_IMPORTED_MODULE_7__.setSocketKeepAlive)(req, {\n                    keepAlive: httpAgent.keepAlive,\n                    keepAliveMsecs: httpAgent.keepAliveMsecs,\n                }));\n            }\n            writeRequestBodyPromise = (0,_write_request_body__WEBPACK_IMPORTED_MODULE_10__.writeRequestBody)(req, request, this.config.requestTimeout).catch((e) => {\n                timeouts.forEach(_timing__WEBPACK_IMPORTED_MODULE_9__.timing.clearTimeout);\n                return _reject(e);\n            });\n        });\n    }\n    updateHttpClientConfig(key, value) {\n        this.config = undefined;\n        this.configProvider = this.configProvider.then((config) => {\n            return {\n                ...config,\n                [key]: value,\n            };\n        });\n    }\n    httpHandlerConfigs() {\n        return this.config ?? {};\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http-handler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http2-connection-manager.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http2-connection-manager.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NodeHttp2ConnectionManager: () => (/* binding */ NodeHttp2ConnectionManager)\n/* harmony export */ });\n/* harmony import */ var http2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! http2 */ \"http2\");\n/* harmony import */ var http2__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(http2__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _node_http2_connection_pool__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node-http2-connection-pool */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http2-connection-pool.js\");\n\n\nclass NodeHttp2ConnectionManager {\n    constructor(config) {\n        this.sessionCache = new Map();\n        this.config = config;\n        if (this.config.maxConcurrency && this.config.maxConcurrency <= 0) {\n            throw new RangeError(\"maxConcurrency must be greater than zero.\");\n        }\n    }\n    lease(requestContext, connectionConfiguration) {\n        const url = this.getUrlString(requestContext);\n        const existingPool = this.sessionCache.get(url);\n        if (existingPool) {\n            const existingSession = existingPool.poll();\n            if (existingSession && !this.config.disableConcurrency) {\n                return existingSession;\n            }\n        }\n        const session = http2__WEBPACK_IMPORTED_MODULE_0___default().connect(url);\n        if (this.config.maxConcurrency) {\n            session.settings({ maxConcurrentStreams: this.config.maxConcurrency }, (err) => {\n                if (err) {\n                    throw new Error(\"Fail to set maxConcurrentStreams to \" +\n                        this.config.maxConcurrency +\n                        \"when creating new session for \" +\n                        requestContext.destination.toString());\n                }\n            });\n        }\n        session.unref();\n        const destroySessionCb = () => {\n            session.destroy();\n            this.deleteSession(url, session);\n        };\n        session.on(\"goaway\", destroySessionCb);\n        session.on(\"error\", destroySessionCb);\n        session.on(\"frameError\", destroySessionCb);\n        session.on(\"close\", () => this.deleteSession(url, session));\n        if (connectionConfiguration.requestTimeout) {\n            session.setTimeout(connectionConfiguration.requestTimeout, destroySessionCb);\n        }\n        const connectionPool = this.sessionCache.get(url) || new _node_http2_connection_pool__WEBPACK_IMPORTED_MODULE_1__.NodeHttp2ConnectionPool();\n        connectionPool.offerLast(session);\n        this.sessionCache.set(url, connectionPool);\n        return session;\n    }\n    deleteSession(authority, session) {\n        const existingConnectionPool = this.sessionCache.get(authority);\n        if (!existingConnectionPool) {\n            return;\n        }\n        if (!existingConnectionPool.contains(session)) {\n            return;\n        }\n        existingConnectionPool.remove(session);\n        this.sessionCache.set(authority, existingConnectionPool);\n    }\n    release(requestContext, session) {\n        const cacheKey = this.getUrlString(requestContext);\n        this.sessionCache.get(cacheKey)?.offerLast(session);\n    }\n    destroy() {\n        for (const [key, connectionPool] of this.sessionCache) {\n            for (const session of connectionPool) {\n                if (!session.destroyed) {\n                    session.destroy();\n                }\n                connectionPool.remove(session);\n            }\n            this.sessionCache.delete(key);\n        }\n    }\n    setMaxConcurrentStreams(maxConcurrentStreams) {\n        if (maxConcurrentStreams && maxConcurrentStreams <= 0) {\n            throw new RangeError(\"maxConcurrentStreams must be greater than zero.\");\n        }\n        this.config.maxConcurrency = maxConcurrentStreams;\n    }\n    setDisableConcurrentStreams(disableConcurrentStreams) {\n        this.config.disableConcurrency = disableConcurrentStreams;\n    }\n    getUrlString(request) {\n        return request.destination.toString();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http2-connection-manager.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http2-connection-pool.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http2-connection-pool.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NodeHttp2ConnectionPool: () => (/* binding */ NodeHttp2ConnectionPool)\n/* harmony export */ });\nclass NodeHttp2ConnectionPool {\n    constructor(sessions) {\n        this.sessions = [];\n        this.sessions = sessions ?? [];\n    }\n    poll() {\n        if (this.sessions.length > 0) {\n            return this.sessions.shift();\n        }\n    }\n    offerLast(session) {\n        this.sessions.push(session);\n    }\n    contains(session) {\n        return this.sessions.includes(session);\n    }\n    remove(session) {\n        this.sessions = this.sessions.filter((s) => s !== session);\n    }\n    [Symbol.iterator]() {\n        return this.sessions[Symbol.iterator]();\n    }\n    destroy(connection) {\n        for (const session of this.sessions) {\n            if (session === connection) {\n                if (!session.destroyed) {\n                    session.destroy();\n                }\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStub2RlLWh0dHAtaGFuZGxlckA0LjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS9ub2RlLWh0dHAtaGFuZGxlci9kaXN0LWVzL25vZGUtaHR0cDItY29ubmVjdGlvbi1wb29sLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStub2RlLWh0dHAtaGFuZGxlckA0LjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS9ub2RlLWh0dHAtaGFuZGxlci9kaXN0LWVzL25vZGUtaHR0cDItY29ubmVjdGlvbi1wb29sLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBOb2RlSHR0cDJDb25uZWN0aW9uUG9vbCB7XG4gICAgY29uc3RydWN0b3Ioc2Vzc2lvbnMpIHtcbiAgICAgICAgdGhpcy5zZXNzaW9ucyA9IFtdO1xuICAgICAgICB0aGlzLnNlc3Npb25zID0gc2Vzc2lvbnMgPz8gW107XG4gICAgfVxuICAgIHBvbGwoKSB7XG4gICAgICAgIGlmICh0aGlzLnNlc3Npb25zLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLnNlc3Npb25zLnNoaWZ0KCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgb2ZmZXJMYXN0KHNlc3Npb24pIHtcbiAgICAgICAgdGhpcy5zZXNzaW9ucy5wdXNoKHNlc3Npb24pO1xuICAgIH1cbiAgICBjb250YWlucyhzZXNzaW9uKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnNlc3Npb25zLmluY2x1ZGVzKHNlc3Npb24pO1xuICAgIH1cbiAgICByZW1vdmUoc2Vzc2lvbikge1xuICAgICAgICB0aGlzLnNlc3Npb25zID0gdGhpcy5zZXNzaW9ucy5maWx0ZXIoKHMpID0+IHMgIT09IHNlc3Npb24pO1xuICAgIH1cbiAgICBbU3ltYm9sLml0ZXJhdG9yXSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc2Vzc2lvbnNbU3ltYm9sLml0ZXJhdG9yXSgpO1xuICAgIH1cbiAgICBkZXN0cm95KGNvbm5lY3Rpb24pIHtcbiAgICAgICAgZm9yIChjb25zdCBzZXNzaW9uIG9mIHRoaXMuc2Vzc2lvbnMpIHtcbiAgICAgICAgICAgIGlmIChzZXNzaW9uID09PSBjb25uZWN0aW9uKSB7XG4gICAgICAgICAgICAgICAgaWYgKCFzZXNzaW9uLmRlc3Ryb3llZCkge1xuICAgICAgICAgICAgICAgICAgICBzZXNzaW9uLmRlc3Ryb3koKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http2-connection-pool.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http2-handler.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http2-handler.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NodeHttp2Handler: () => (/* binding */ NodeHttp2Handler)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_querystring_builder__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/querystring-builder */ \"(rsc)/./node_modules/.pnpm/@smithy+querystring-builder@4.0.4/node_modules/@smithy/querystring-builder/dist-es/index.js\");\n/* harmony import */ var http2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! http2 */ \"http2\");\n/* harmony import */ var http2__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(http2__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _get_transformed_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./get-transformed-headers */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/get-transformed-headers.js\");\n/* harmony import */ var _node_http2_connection_manager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node-http2-connection-manager */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http2-connection-manager.js\");\n/* harmony import */ var _write_request_body__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./write-request-body */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/write-request-body.js\");\n\n\n\n\n\n\nclass NodeHttp2Handler {\n    static create(instanceOrOptions) {\n        if (typeof instanceOrOptions?.handle === \"function\") {\n            return instanceOrOptions;\n        }\n        return new NodeHttp2Handler(instanceOrOptions);\n    }\n    constructor(options) {\n        this.metadata = { handlerProtocol: \"h2\" };\n        this.connectionManager = new _node_http2_connection_manager__WEBPACK_IMPORTED_MODULE_4__.NodeHttp2ConnectionManager({});\n        this.configProvider = new Promise((resolve, reject) => {\n            if (typeof options === \"function\") {\n                options()\n                    .then((opts) => {\n                    resolve(opts || {});\n                })\n                    .catch(reject);\n            }\n            else {\n                resolve(options || {});\n            }\n        });\n    }\n    destroy() {\n        this.connectionManager.destroy();\n    }\n    async handle(request, { abortSignal } = {}) {\n        if (!this.config) {\n            this.config = await this.configProvider;\n            this.connectionManager.setDisableConcurrentStreams(this.config.disableConcurrentStreams || false);\n            if (this.config.maxConcurrentStreams) {\n                this.connectionManager.setMaxConcurrentStreams(this.config.maxConcurrentStreams);\n            }\n        }\n        const { requestTimeout, disableConcurrentStreams } = this.config;\n        return new Promise((_resolve, _reject) => {\n            let fulfilled = false;\n            let writeRequestBodyPromise = undefined;\n            const resolve = async (arg) => {\n                await writeRequestBodyPromise;\n                _resolve(arg);\n            };\n            const reject = async (arg) => {\n                await writeRequestBodyPromise;\n                _reject(arg);\n            };\n            if (abortSignal?.aborted) {\n                fulfilled = true;\n                const abortError = new Error(\"Request aborted\");\n                abortError.name = \"AbortError\";\n                reject(abortError);\n                return;\n            }\n            const { hostname, method, port, protocol, query } = request;\n            let auth = \"\";\n            if (request.username != null || request.password != null) {\n                const username = request.username ?? \"\";\n                const password = request.password ?? \"\";\n                auth = `${username}:${password}@`;\n            }\n            const authority = `${protocol}//${auth}${hostname}${port ? `:${port}` : \"\"}`;\n            const requestContext = { destination: new URL(authority) };\n            const session = this.connectionManager.lease(requestContext, {\n                requestTimeout: this.config?.sessionTimeout,\n                disableConcurrentStreams: disableConcurrentStreams || false,\n            });\n            const rejectWithDestroy = (err) => {\n                if (disableConcurrentStreams) {\n                    this.destroySession(session);\n                }\n                fulfilled = true;\n                reject(err);\n            };\n            const queryString = (0,_smithy_querystring_builder__WEBPACK_IMPORTED_MODULE_1__.buildQueryString)(query || {});\n            let path = request.path;\n            if (queryString) {\n                path += `?${queryString}`;\n            }\n            if (request.fragment) {\n                path += `#${request.fragment}`;\n            }\n            const req = session.request({\n                ...request.headers,\n                [http2__WEBPACK_IMPORTED_MODULE_2__.constants.HTTP2_HEADER_PATH]: path,\n                [http2__WEBPACK_IMPORTED_MODULE_2__.constants.HTTP2_HEADER_METHOD]: method,\n            });\n            session.ref();\n            req.on(\"response\", (headers) => {\n                const httpResponse = new _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpResponse({\n                    statusCode: headers[\":status\"] || -1,\n                    headers: (0,_get_transformed_headers__WEBPACK_IMPORTED_MODULE_3__.getTransformedHeaders)(headers),\n                    body: req,\n                });\n                fulfilled = true;\n                resolve({ response: httpResponse });\n                if (disableConcurrentStreams) {\n                    session.close();\n                    this.connectionManager.deleteSession(authority, session);\n                }\n            });\n            if (requestTimeout) {\n                req.setTimeout(requestTimeout, () => {\n                    req.close();\n                    const timeoutError = new Error(`Stream timed out because of no activity for ${requestTimeout} ms`);\n                    timeoutError.name = \"TimeoutError\";\n                    rejectWithDestroy(timeoutError);\n                });\n            }\n            if (abortSignal) {\n                const onAbort = () => {\n                    req.close();\n                    const abortError = new Error(\"Request aborted\");\n                    abortError.name = \"AbortError\";\n                    rejectWithDestroy(abortError);\n                };\n                if (typeof abortSignal.addEventListener === \"function\") {\n                    const signal = abortSignal;\n                    signal.addEventListener(\"abort\", onAbort, { once: true });\n                    req.once(\"close\", () => signal.removeEventListener(\"abort\", onAbort));\n                }\n                else {\n                    abortSignal.onabort = onAbort;\n                }\n            }\n            req.on(\"frameError\", (type, code, id) => {\n                rejectWithDestroy(new Error(`Frame type id ${type} in stream id ${id} has failed with code ${code}.`));\n            });\n            req.on(\"error\", rejectWithDestroy);\n            req.on(\"aborted\", () => {\n                rejectWithDestroy(new Error(`HTTP/2 stream is abnormally aborted in mid-communication with result code ${req.rstCode}.`));\n            });\n            req.on(\"close\", () => {\n                session.unref();\n                if (disableConcurrentStreams) {\n                    session.destroy();\n                }\n                if (!fulfilled) {\n                    rejectWithDestroy(new Error(\"Unexpected error: http2 request did not get a response\"));\n                }\n            });\n            writeRequestBodyPromise = (0,_write_request_body__WEBPACK_IMPORTED_MODULE_5__.writeRequestBody)(req, request, requestTimeout);\n        });\n    }\n    updateHttpClientConfig(key, value) {\n        this.config = undefined;\n        this.configProvider = this.configProvider.then((config) => {\n            return {\n                ...config,\n                [key]: value,\n            };\n        });\n    }\n    httpHandlerConfigs() {\n        return this.config ?? {};\n    }\n    destroySession(session) {\n        if (!session.destroyed) {\n            session.destroy();\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http2-handler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/set-connection-timeout.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/set-connection-timeout.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setConnectionTimeout: () => (/* binding */ setConnectionTimeout)\n/* harmony export */ });\n/* harmony import */ var _timing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./timing */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/timing.js\");\n\nconst DEFER_EVENT_LISTENER_TIME = 1000;\nconst setConnectionTimeout = (request, reject, timeoutInMs = 0) => {\n    if (!timeoutInMs) {\n        return -1;\n    }\n    const registerTimeout = (offset) => {\n        const timeoutId = _timing__WEBPACK_IMPORTED_MODULE_0__.timing.setTimeout(() => {\n            request.destroy();\n            reject(Object.assign(new Error(`Socket timed out without establishing a connection within ${timeoutInMs} ms`), {\n                name: \"TimeoutError\",\n            }));\n        }, timeoutInMs - offset);\n        const doWithSocket = (socket) => {\n            if (socket?.connecting) {\n                socket.on(\"connect\", () => {\n                    _timing__WEBPACK_IMPORTED_MODULE_0__.timing.clearTimeout(timeoutId);\n                });\n            }\n            else {\n                _timing__WEBPACK_IMPORTED_MODULE_0__.timing.clearTimeout(timeoutId);\n            }\n        };\n        if (request.socket) {\n            doWithSocket(request.socket);\n        }\n        else {\n            request.on(\"socket\", doWithSocket);\n        }\n    };\n    if (timeoutInMs < 2000) {\n        registerTimeout(0);\n        return 0;\n    }\n    return _timing__WEBPACK_IMPORTED_MODULE_0__.timing.setTimeout(registerTimeout.bind(null, DEFER_EVENT_LISTENER_TIME), DEFER_EVENT_LISTENER_TIME);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/set-connection-timeout.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/set-socket-keep-alive.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/set-socket-keep-alive.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setSocketKeepAlive: () => (/* binding */ setSocketKeepAlive)\n/* harmony export */ });\n/* harmony import */ var _timing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./timing */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/timing.js\");\n\nconst DEFER_EVENT_LISTENER_TIME = 3000;\nconst setSocketKeepAlive = (request, { keepAlive, keepAliveMsecs }, deferTimeMs = DEFER_EVENT_LISTENER_TIME) => {\n    if (keepAlive !== true) {\n        return -1;\n    }\n    const registerListener = () => {\n        if (request.socket) {\n            request.socket.setKeepAlive(keepAlive, keepAliveMsecs || 0);\n        }\n        else {\n            request.on(\"socket\", (socket) => {\n                socket.setKeepAlive(keepAlive, keepAliveMsecs || 0);\n            });\n        }\n    };\n    if (deferTimeMs === 0) {\n        registerListener();\n        return 0;\n    }\n    return _timing__WEBPACK_IMPORTED_MODULE_0__.timing.setTimeout(registerListener, deferTimeMs);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStub2RlLWh0dHAtaGFuZGxlckA0LjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS9ub2RlLWh0dHAtaGFuZGxlci9kaXN0LWVzL3NldC1zb2NrZXQta2VlcC1hbGl2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrQztBQUNsQztBQUNPLHVDQUF1QywyQkFBMkI7QUFDekU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVywyQ0FBTTtBQUNqQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrbm9kZS1odHRwLWhhbmRsZXJANC4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvbm9kZS1odHRwLWhhbmRsZXIvZGlzdC1lcy9zZXQtc29ja2V0LWtlZXAtYWxpdmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdGltaW5nIH0gZnJvbSBcIi4vdGltaW5nXCI7XG5jb25zdCBERUZFUl9FVkVOVF9MSVNURU5FUl9USU1FID0gMzAwMDtcbmV4cG9ydCBjb25zdCBzZXRTb2NrZXRLZWVwQWxpdmUgPSAocmVxdWVzdCwgeyBrZWVwQWxpdmUsIGtlZXBBbGl2ZU1zZWNzIH0sIGRlZmVyVGltZU1zID0gREVGRVJfRVZFTlRfTElTVEVORVJfVElNRSkgPT4ge1xuICAgIGlmIChrZWVwQWxpdmUgIT09IHRydWUpIHtcbiAgICAgICAgcmV0dXJuIC0xO1xuICAgIH1cbiAgICBjb25zdCByZWdpc3Rlckxpc3RlbmVyID0gKCkgPT4ge1xuICAgICAgICBpZiAocmVxdWVzdC5zb2NrZXQpIHtcbiAgICAgICAgICAgIHJlcXVlc3Quc29ja2V0LnNldEtlZXBBbGl2ZShrZWVwQWxpdmUsIGtlZXBBbGl2ZU1zZWNzIHx8IDApO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmVxdWVzdC5vbihcInNvY2tldFwiLCAoc29ja2V0KSA9PiB7XG4gICAgICAgICAgICAgICAgc29ja2V0LnNldEtlZXBBbGl2ZShrZWVwQWxpdmUsIGtlZXBBbGl2ZU1zZWNzIHx8IDApO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGlmIChkZWZlclRpbWVNcyA9PT0gMCkge1xuICAgICAgICByZWdpc3Rlckxpc3RlbmVyKCk7XG4gICAgICAgIHJldHVybiAwO1xuICAgIH1cbiAgICByZXR1cm4gdGltaW5nLnNldFRpbWVvdXQocmVnaXN0ZXJMaXN0ZW5lciwgZGVmZXJUaW1lTXMpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/set-socket-keep-alive.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/set-socket-timeout.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/set-socket-timeout.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setSocketTimeout: () => (/* binding */ setSocketTimeout)\n/* harmony export */ });\n/* harmony import */ var _node_http_handler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node-http-handler */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/node-http-handler.js\");\n/* harmony import */ var _timing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./timing */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/timing.js\");\n\n\nconst DEFER_EVENT_LISTENER_TIME = 3000;\nconst setSocketTimeout = (request, reject, timeoutInMs = _node_http_handler__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_REQUEST_TIMEOUT) => {\n    const registerTimeout = (offset) => {\n        const timeout = timeoutInMs - offset;\n        const onTimeout = () => {\n            request.destroy();\n            reject(Object.assign(new Error(`Connection timed out after ${timeoutInMs} ms`), { name: \"TimeoutError\" }));\n        };\n        if (request.socket) {\n            request.socket.setTimeout(timeout, onTimeout);\n            request.on(\"close\", () => request.socket?.removeListener(\"timeout\", onTimeout));\n        }\n        else {\n            request.setTimeout(timeout, onTimeout);\n        }\n    };\n    if (0 < timeoutInMs && timeoutInMs < 6000) {\n        registerTimeout(0);\n        return 0;\n    }\n    return _timing__WEBPACK_IMPORTED_MODULE_1__.timing.setTimeout(registerTimeout.bind(null, timeoutInMs === 0 ? 0 : DEFER_EVENT_LISTENER_TIME), DEFER_EVENT_LISTENER_TIME);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/set-socket-timeout.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/stream-collector/collector.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/stream-collector/collector.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collector: () => (/* binding */ Collector)\n/* harmony export */ });\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_0__);\n\nclass Collector extends stream__WEBPACK_IMPORTED_MODULE_0__.Writable {\n    constructor() {\n        super(...arguments);\n        this.bufferedBytes = [];\n    }\n    _write(chunk, encoding, callback) {\n        this.bufferedBytes.push(chunk);\n        callback();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStub2RlLWh0dHAtaGFuZGxlckA0LjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS9ub2RlLWh0dHAtaGFuZGxlci9kaXN0LWVzL3N0cmVhbS1jb2xsZWN0b3IvY29sbGVjdG9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFrQztBQUMzQix3QkFBd0IsNENBQVE7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStub2RlLWh0dHAtaGFuZGxlckA0LjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS9ub2RlLWh0dHAtaGFuZGxlci9kaXN0LWVzL3N0cmVhbS1jb2xsZWN0b3IvY29sbGVjdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFdyaXRhYmxlIH0gZnJvbSBcInN0cmVhbVwiO1xuZXhwb3J0IGNsYXNzIENvbGxlY3RvciBleHRlbmRzIFdyaXRhYmxlIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcbiAgICAgICAgdGhpcy5idWZmZXJlZEJ5dGVzID0gW107XG4gICAgfVxuICAgIF93cml0ZShjaHVuaywgZW5jb2RpbmcsIGNhbGxiYWNrKSB7XG4gICAgICAgIHRoaXMuYnVmZmVyZWRCeXRlcy5wdXNoKGNodW5rKTtcbiAgICAgICAgY2FsbGJhY2soKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/stream-collector/collector.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/stream-collector/index.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/stream-collector/index.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   streamCollector: () => (/* binding */ streamCollector)\n/* harmony export */ });\n/* harmony import */ var _collector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./collector */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/stream-collector/collector.js\");\n\nconst streamCollector = (stream) => {\n    if (isReadableStreamInstance(stream)) {\n        return collectReadableStream(stream);\n    }\n    return new Promise((resolve, reject) => {\n        const collector = new _collector__WEBPACK_IMPORTED_MODULE_0__.Collector();\n        stream.pipe(collector);\n        stream.on(\"error\", (err) => {\n            collector.end();\n            reject(err);\n        });\n        collector.on(\"error\", reject);\n        collector.on(\"finish\", function () {\n            const bytes = new Uint8Array(Buffer.concat(this.bufferedBytes));\n            resolve(bytes);\n        });\n    });\n};\nconst isReadableStreamInstance = (stream) => typeof ReadableStream === \"function\" && stream instanceof ReadableStream;\nasync function collectReadableStream(stream) {\n    const chunks = [];\n    const reader = stream.getReader();\n    let isDone = false;\n    let length = 0;\n    while (!isDone) {\n        const { done, value } = await reader.read();\n        if (value) {\n            chunks.push(value);\n            length += value.length;\n        }\n        isDone = done;\n    }\n    const collected = new Uint8Array(length);\n    let offset = 0;\n    for (const chunk of chunks) {\n        collected.set(chunk, offset);\n        offset += chunk.length;\n    }\n    return collected;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/stream-collector/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/timing.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/timing.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timing: () => (/* binding */ timing)\n/* harmony export */ });\nconst timing = {\n    setTimeout: (cb, ms) => setTimeout(cb, ms),\n    clearTimeout: (timeoutId) => clearTimeout(timeoutId),\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStub2RlLWh0dHAtaGFuZGxlckA0LjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS9ub2RlLWh0dHAtaGFuZGxlci9kaXN0LWVzL3RpbWluZy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K25vZGUtaHR0cC1oYW5kbGVyQDQuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L25vZGUtaHR0cC1oYW5kbGVyL2Rpc3QtZXMvdGltaW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCB0aW1pbmcgPSB7XG4gICAgc2V0VGltZW91dDogKGNiLCBtcykgPT4gc2V0VGltZW91dChjYiwgbXMpLFxuICAgIGNsZWFyVGltZW91dDogKHRpbWVvdXRJZCkgPT4gY2xlYXJUaW1lb3V0KHRpbWVvdXRJZCksXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/timing.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/write-request-body.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/write-request-body.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   writeRequestBody: () => (/* binding */ writeRequestBody)\n/* harmony export */ });\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _timing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./timing */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/timing.js\");\n\n\nconst MIN_WAIT_TIME = 6000;\nasync function writeRequestBody(httpRequest, request, maxContinueTimeoutMs = MIN_WAIT_TIME) {\n    const headers = request.headers ?? {};\n    const expect = headers[\"Expect\"] || headers[\"expect\"];\n    let timeoutId = -1;\n    let sendBody = true;\n    if (expect === \"100-continue\") {\n        sendBody = await Promise.race([\n            new Promise((resolve) => {\n                timeoutId = Number(_timing__WEBPACK_IMPORTED_MODULE_1__.timing.setTimeout(() => resolve(true), Math.max(MIN_WAIT_TIME, maxContinueTimeoutMs)));\n            }),\n            new Promise((resolve) => {\n                httpRequest.on(\"continue\", () => {\n                    _timing__WEBPACK_IMPORTED_MODULE_1__.timing.clearTimeout(timeoutId);\n                    resolve(true);\n                });\n                httpRequest.on(\"response\", () => {\n                    _timing__WEBPACK_IMPORTED_MODULE_1__.timing.clearTimeout(timeoutId);\n                    resolve(false);\n                });\n                httpRequest.on(\"error\", () => {\n                    _timing__WEBPACK_IMPORTED_MODULE_1__.timing.clearTimeout(timeoutId);\n                    resolve(false);\n                });\n            }),\n        ]);\n    }\n    if (sendBody) {\n        writeBody(httpRequest, request.body);\n    }\n}\nfunction writeBody(httpRequest, body) {\n    if (body instanceof stream__WEBPACK_IMPORTED_MODULE_0__.Readable) {\n        body.pipe(httpRequest);\n        return;\n    }\n    if (body) {\n        if (Buffer.isBuffer(body) || typeof body === \"string\") {\n            httpRequest.end(body);\n            return;\n        }\n        const uint8 = body;\n        if (typeof uint8 === \"object\" &&\n            uint8.buffer &&\n            typeof uint8.byteOffset === \"number\" &&\n            typeof uint8.byteLength === \"number\") {\n            httpRequest.end(Buffer.from(uint8.buffer, uint8.byteOffset, uint8.byteLength));\n            return;\n        }\n        httpRequest.end(Buffer.from(body));\n        return;\n    }\n    httpRequest.end();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/write-request-body.js\n");

/***/ })

};
;