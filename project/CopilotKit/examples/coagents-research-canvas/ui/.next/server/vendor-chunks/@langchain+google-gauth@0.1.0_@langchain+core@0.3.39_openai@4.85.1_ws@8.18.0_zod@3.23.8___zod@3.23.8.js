/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8";
exports.ids = ["vendor-chunks/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/auth.cjs":
/*!************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/auth.cjs ***!
  \************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GAuthClient = exports.NodeJsonStream = void 0;\nconst google_common_1 = __webpack_require__(/*! @langchain/google-common */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/index.cjs\");\nconst google_auth_library_1 = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/.pnpm/google-auth-library@8.9.0/node_modules/google-auth-library/build/src/index.js\");\nclass NodeJsonStream extends google_common_1.JsonStream {\n    constructor(data) {\n        super();\n        const decoder = new TextDecoder(\"utf-8\");\n        data.on(\"data\", (data) => {\n            const text = decoder.decode(data, { stream: true });\n            this.appendBuffer(text);\n        });\n        data.on(\"end\", () => {\n            const rest = decoder.decode();\n            this.appendBuffer(rest);\n            this.closeBuffer();\n        });\n    }\n}\nexports.NodeJsonStream = NodeJsonStream;\nclass GAuthClient {\n    constructor(fields) {\n        Object.defineProperty(this, \"gauth\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        const options = (0, google_common_1.ensureAuthOptionScopes)(fields?.authOptions, \"scopes\", fields?.platformType);\n        this.gauth = new google_auth_library_1.GoogleAuth(options);\n    }\n    get clientType() {\n        return \"gauth\";\n    }\n    async getProjectId() {\n        return this.gauth.getProjectId();\n    }\n    async request(opts) {\n        try {\n            const ret = await this.gauth.request(opts);\n            return opts.responseType !== \"stream\"\n                ? ret\n                : {\n                    ...ret,\n                    data: new NodeJsonStream(ret.data),\n                };\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        }\n        catch (xx) {\n            console.error(\"call to gauth.request\", JSON.stringify(xx, null, 2));\n            console.error(\"call to gauth.request opts=\", JSON.stringify(opts, null, 2));\n            console.error(\"call to gauth.request message:\", xx?.message);\n            throw xx;\n        }\n    }\n}\nexports.GAuthClient = GAuthClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/auth.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/chat_models.cjs":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/chat_models.cjs ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ChatGoogle = void 0;\nconst google_common_1 = __webpack_require__(/*! @langchain/google-common */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/index.cjs\");\nconst auth_js_1 = __webpack_require__(/*! ./auth.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/auth.cjs\");\n/**\n * Integration with a Google chat model.\n */\nclass ChatGoogle extends google_common_1.ChatGoogleBase {\n    // Used for tracing, replace with the same name as your class\n    static lc_name() {\n        return \"ChatGoogle\";\n    }\n    constructor(fields) {\n        super(fields);\n    }\n    buildAbstractedClient(fields) {\n        return new auth_js_1.GAuthClient(fields);\n    }\n}\nexports.ChatGoogle = ChatGoogle;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtZ2F1dGhAMC4xLjBfQGxhbmdjaGFpbitjb3JlQDAuMy4zOV9vcGVuYWlANC44NS4xX3dzQDguMTguMF96b2RAMy4yMy44X19fem9kQDMuMjMuOC9ub2RlX21vZHVsZXMvQGxhbmdjaGFpbi9nb29nbGUtZ2F1dGgvZGlzdC9jaGF0X21vZGVscy5janMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0JBQWtCO0FBQ2xCLHdCQUF3QixtQkFBTyxDQUFDLGtOQUEwQjtBQUMxRCxrQkFBa0IsbUJBQU8sQ0FBQyxzTUFBWTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AbGFuZ2NoYWluK2dvb2dsZS1nYXV0aEAwLjEuMF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX196b2RAMy4yMy44L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2dvb2dsZS1nYXV0aC9kaXN0L2NoYXRfbW9kZWxzLmNqcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuQ2hhdEdvb2dsZSA9IHZvaWQgMDtcbmNvbnN0IGdvb2dsZV9jb21tb25fMSA9IHJlcXVpcmUoXCJAbGFuZ2NoYWluL2dvb2dsZS1jb21tb25cIik7XG5jb25zdCBhdXRoX2pzXzEgPSByZXF1aXJlKFwiLi9hdXRoLmNqc1wiKTtcbi8qKlxuICogSW50ZWdyYXRpb24gd2l0aCBhIEdvb2dsZSBjaGF0IG1vZGVsLlxuICovXG5jbGFzcyBDaGF0R29vZ2xlIGV4dGVuZHMgZ29vZ2xlX2NvbW1vbl8xLkNoYXRHb29nbGVCYXNlIHtcbiAgICAvLyBVc2VkIGZvciB0cmFjaW5nLCByZXBsYWNlIHdpdGggdGhlIHNhbWUgbmFtZSBhcyB5b3VyIGNsYXNzXG4gICAgc3RhdGljIGxjX25hbWUoKSB7XG4gICAgICAgIHJldHVybiBcIkNoYXRHb29nbGVcIjtcbiAgICB9XG4gICAgY29uc3RydWN0b3IoZmllbGRzKSB7XG4gICAgICAgIHN1cGVyKGZpZWxkcyk7XG4gICAgfVxuICAgIGJ1aWxkQWJzdHJhY3RlZENsaWVudChmaWVsZHMpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBhdXRoX2pzXzEuR0F1dGhDbGllbnQoZmllbGRzKTtcbiAgICB9XG59XG5leHBvcnRzLkNoYXRHb29nbGUgPSBDaGF0R29vZ2xlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/chat_models.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/embeddings.cjs":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/embeddings.cjs ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GoogleEmbeddings = void 0;\nconst google_common_1 = __webpack_require__(/*! @langchain/google-common */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/index.cjs\");\nconst auth_js_1 = __webpack_require__(/*! ./auth.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/auth.cjs\");\n/**\n * Integration with an Google embeddings model.\n */\nclass GoogleEmbeddings extends google_common_1.BaseGoogleEmbeddings {\n    // Used for tracing, replace with the same name as your class\n    static lc_name() {\n        return \"GoogleEmbeddings\";\n    }\n    constructor(fields) {\n        super(fields);\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n    }\n    buildAbstractedClient(fields) {\n        return new auth_js_1.GAuthClient(fields);\n    }\n}\nexports.GoogleEmbeddings = GoogleEmbeddings;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/embeddings.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/index.cjs":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/index.cjs ***!
  \*************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./chat_models.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/chat_models.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./llms.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/llms.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./embeddings.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/embeddings.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./media.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/media.cjs\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/index.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/llms.cjs":
/*!************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/llms.cjs ***!
  \************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GoogleLLM = void 0;\nconst google_common_1 = __webpack_require__(/*! @langchain/google-common */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/index.cjs\");\nconst auth_js_1 = __webpack_require__(/*! ./auth.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/auth.cjs\");\n/**\n * Integration with a Google LLM.\n */\nclass GoogleLLM extends google_common_1.GoogleBaseLLM {\n    // Used for tracing, replace with the same name as your class\n    static lc_name() {\n        return \"GoogleLLM\";\n    }\n    constructor(fields) {\n        super(fields);\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n    }\n    buildAbstractedClient(fields) {\n        return new auth_js_1.GAuthClient(fields);\n    }\n}\nexports.GoogleLLM = GoogleLLM;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtZ2F1dGhAMC4xLjBfQGxhbmdjaGFpbitjb3JlQDAuMy4zOV9vcGVuYWlANC44NS4xX3dzQDguMTguMF96b2RAMy4yMy44X19fem9kQDMuMjMuOC9ub2RlX21vZHVsZXMvQGxhbmdjaGFpbi9nb29nbGUtZ2F1dGgvZGlzdC9sbG1zLmNqcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQkFBaUI7QUFDakIsd0JBQXdCLG1CQUFPLENBQUMsa05BQTBCO0FBQzFELGtCQUFrQixtQkFBTyxDQUFDLHNNQUFZO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtZ2F1dGhAMC4xLjBfQGxhbmdjaGFpbitjb3JlQDAuMy4zOV9vcGVuYWlANC44NS4xX3dzQDguMTguMF96b2RAMy4yMy44X19fem9kQDMuMjMuOC9ub2RlX21vZHVsZXMvQGxhbmdjaGFpbi9nb29nbGUtZ2F1dGgvZGlzdC9sbG1zLmNqcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuR29vZ2xlTExNID0gdm9pZCAwO1xuY29uc3QgZ29vZ2xlX2NvbW1vbl8xID0gcmVxdWlyZShcIkBsYW5nY2hhaW4vZ29vZ2xlLWNvbW1vblwiKTtcbmNvbnN0IGF1dGhfanNfMSA9IHJlcXVpcmUoXCIuL2F1dGguY2pzXCIpO1xuLyoqXG4gKiBJbnRlZ3JhdGlvbiB3aXRoIGEgR29vZ2xlIExMTS5cbiAqL1xuY2xhc3MgR29vZ2xlTExNIGV4dGVuZHMgZ29vZ2xlX2NvbW1vbl8xLkdvb2dsZUJhc2VMTE0ge1xuICAgIC8vIFVzZWQgZm9yIHRyYWNpbmcsIHJlcGxhY2Ugd2l0aCB0aGUgc2FtZSBuYW1lIGFzIHlvdXIgY2xhc3NcbiAgICBzdGF0aWMgbGNfbmFtZSgpIHtcbiAgICAgICAgcmV0dXJuIFwiR29vZ2xlTExNXCI7XG4gICAgfVxuICAgIGNvbnN0cnVjdG9yKGZpZWxkcykge1xuICAgICAgICBzdXBlcihmaWVsZHMpO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJsY19zZXJpYWxpemFibGVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IHRydWVcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGJ1aWxkQWJzdHJhY3RlZENsaWVudChmaWVsZHMpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBhdXRoX2pzXzEuR0F1dGhDbGllbnQoZmllbGRzKTtcbiAgICB9XG59XG5leHBvcnRzLkdvb2dsZUxMTSA9IEdvb2dsZUxMTTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/llms.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/media.cjs":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/media.cjs ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BlobStoreAIStudioFile = exports.BlobStoreGoogleCloudStorage = void 0;\nconst media_1 = __webpack_require__(/*! @langchain/google-common/experimental/media */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/experimental/media.cjs\");\nconst auth_js_1 = __webpack_require__(/*! ./auth.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/auth.cjs\");\nclass BlobStoreGoogleCloudStorage extends media_1.BlobStoreGoogleCloudStorageBase {\n    buildClient(fields) {\n        return new auth_js_1.GAuthClient(fields);\n    }\n}\nexports.BlobStoreGoogleCloudStorage = BlobStoreGoogleCloudStorage;\nclass BlobStoreAIStudioFile extends media_1.BlobStoreAIStudioFileBase {\n    buildAbstractedClient(fields) {\n        return new auth_js_1.GAuthClient(fields);\n    }\n}\nexports.BlobStoreAIStudioFile = BlobStoreAIStudioFile;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtZ2F1dGhAMC4xLjBfQGxhbmdjaGFpbitjb3JlQDAuMy4zOV9vcGVuYWlANC44NS4xX3dzQDguMTguMF96b2RAMy4yMy44X19fem9kQDMuMjMuOC9ub2RlX21vZHVsZXMvQGxhbmdjaGFpbi9nb29nbGUtZ2F1dGgvZGlzdC9tZWRpYS5janMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsNkJBQTZCLEdBQUcsbUNBQW1DO0FBQ25FLGdCQUFnQixtQkFBTyxDQUFDLGtQQUE2QztBQUNyRSxrQkFBa0IsbUJBQU8sQ0FBQyxzTUFBWTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AbGFuZ2NoYWluK2dvb2dsZS1nYXV0aEAwLjEuMF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX196b2RAMy4yMy44L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2dvb2dsZS1nYXV0aC9kaXN0L21lZGlhLmNqcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuQmxvYlN0b3JlQUlTdHVkaW9GaWxlID0gZXhwb3J0cy5CbG9iU3RvcmVHb29nbGVDbG91ZFN0b3JhZ2UgPSB2b2lkIDA7XG5jb25zdCBtZWRpYV8xID0gcmVxdWlyZShcIkBsYW5nY2hhaW4vZ29vZ2xlLWNvbW1vbi9leHBlcmltZW50YWwvbWVkaWFcIik7XG5jb25zdCBhdXRoX2pzXzEgPSByZXF1aXJlKFwiLi9hdXRoLmNqc1wiKTtcbmNsYXNzIEJsb2JTdG9yZUdvb2dsZUNsb3VkU3RvcmFnZSBleHRlbmRzIG1lZGlhXzEuQmxvYlN0b3JlR29vZ2xlQ2xvdWRTdG9yYWdlQmFzZSB7XG4gICAgYnVpbGRDbGllbnQoZmllbGRzKSB7XG4gICAgICAgIHJldHVybiBuZXcgYXV0aF9qc18xLkdBdXRoQ2xpZW50KGZpZWxkcyk7XG4gICAgfVxufVxuZXhwb3J0cy5CbG9iU3RvcmVHb29nbGVDbG91ZFN0b3JhZ2UgPSBCbG9iU3RvcmVHb29nbGVDbG91ZFN0b3JhZ2U7XG5jbGFzcyBCbG9iU3RvcmVBSVN0dWRpb0ZpbGUgZXh0ZW5kcyBtZWRpYV8xLkJsb2JTdG9yZUFJU3R1ZGlvRmlsZUJhc2Uge1xuICAgIGJ1aWxkQWJzdHJhY3RlZENsaWVudChmaWVsZHMpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBhdXRoX2pzXzEuR0F1dGhDbGllbnQoZmllbGRzKTtcbiAgICB9XG59XG5leHBvcnRzLkJsb2JTdG9yZUFJU3R1ZGlvRmlsZSA9IEJsb2JTdG9yZUFJU3R1ZGlvRmlsZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/media.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/index.cjs":
/*!********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/index.cjs ***!
  \********************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/index.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/dist/index.cjs\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtZ2F1dGhAMC4xLjBfQGxhbmdjaGFpbitjb3JlQDAuMy4zOV9vcGVuYWlANC44NS4xX3dzQDguMTguMF96b2RAMy4yMy44X19fem9kQDMuMjMuOC9ub2RlX21vZHVsZXMvQGxhbmdjaGFpbi9nb29nbGUtZ2F1dGgvaW5kZXguY2pzIiwibWFwcGluZ3MiOiJBQUFBLG1QQUE0QyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BsYW5nY2hhaW4rZ29vZ2xlLWdhdXRoQDAuMS4wX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fX3pvZEAzLjIzLjgvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vZ29vZ2xlLWdhdXRoL2luZGV4LmNqcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGlzdC9pbmRleC5janMnKTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-gauth@0.1.0_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-gauth/index.cjs\n");

/***/ })

};
;