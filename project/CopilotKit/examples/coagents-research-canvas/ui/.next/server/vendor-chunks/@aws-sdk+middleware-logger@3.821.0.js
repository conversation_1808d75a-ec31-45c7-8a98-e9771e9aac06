"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+middleware-logger@3.821.0";
exports.ids = ["vendor-chunks/@aws-sdk+middleware-logger@3.821.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-logger@3.821.0/node_modules/@aws-sdk/middleware-logger/dist-es/index.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-logger@3.821.0/node_modules/@aws-sdk/middleware-logger/dist-es/index.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLoggerPlugin: () => (/* reexport safe */ _loggerMiddleware__WEBPACK_IMPORTED_MODULE_0__.getLoggerPlugin),\n/* harmony export */   loggerMiddleware: () => (/* reexport safe */ _loggerMiddleware__WEBPACK_IMPORTED_MODULE_0__.loggerMiddleware),\n/* harmony export */   loggerMiddlewareOptions: () => (/* reexport safe */ _loggerMiddleware__WEBPACK_IMPORTED_MODULE_0__.loggerMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _loggerMiddleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./loggerMiddleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-logger@3.821.0/node_modules/@aws-sdk/middleware-logger/dist-es/loggerMiddleware.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1sb2dnZXJAMy44MjEuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1sb2dnZXIvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW1DIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1sb2dnZXJAMy44MjEuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1sb2dnZXIvZGlzdC1lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9sb2dnZXJNaWRkbGV3YXJlXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-logger@3.821.0/node_modules/@aws-sdk/middleware-logger/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-logger@3.821.0/node_modules/@aws-sdk/middleware-logger/dist-es/loggerMiddleware.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-logger@3.821.0/node_modules/@aws-sdk/middleware-logger/dist-es/loggerMiddleware.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLoggerPlugin: () => (/* binding */ getLoggerPlugin),\n/* harmony export */   loggerMiddleware: () => (/* binding */ loggerMiddleware),\n/* harmony export */   loggerMiddlewareOptions: () => (/* binding */ loggerMiddlewareOptions)\n/* harmony export */ });\nconst loggerMiddleware = () => (next, context) => async (args) => {\n    try {\n        const response = await next(args);\n        const { clientName, commandName, logger, dynamoDbDocumentClientOptions = {} } = context;\n        const { overrideInputFilterSensitiveLog, overrideOutputFilterSensitiveLog } = dynamoDbDocumentClientOptions;\n        const inputFilterSensitiveLog = overrideInputFilterSensitiveLog ?? context.inputFilterSensitiveLog;\n        const outputFilterSensitiveLog = overrideOutputFilterSensitiveLog ?? context.outputFilterSensitiveLog;\n        const { $metadata, ...outputWithoutMetadata } = response.output;\n        logger?.info?.({\n            clientName,\n            commandName,\n            input: inputFilterSensitiveLog(args.input),\n            output: outputFilterSensitiveLog(outputWithoutMetadata),\n            metadata: $metadata,\n        });\n        return response;\n    }\n    catch (error) {\n        const { clientName, commandName, logger, dynamoDbDocumentClientOptions = {} } = context;\n        const { overrideInputFilterSensitiveLog } = dynamoDbDocumentClientOptions;\n        const inputFilterSensitiveLog = overrideInputFilterSensitiveLog ?? context.inputFilterSensitiveLog;\n        logger?.error?.({\n            clientName,\n            commandName,\n            input: inputFilterSensitiveLog(args.input),\n            error,\n            metadata: error.$metadata,\n        });\n        throw error;\n    }\n};\nconst loggerMiddlewareOptions = {\n    name: \"loggerMiddleware\",\n    tags: [\"LOGGER\"],\n    step: \"initialize\",\n    override: true,\n};\nconst getLoggerPlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(loggerMiddleware(), loggerMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-logger@3.821.0/node_modules/@aws-sdk/middleware-logger/dist-es/loggerMiddleware.js\n");

/***/ })

};
;