"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+util-stream@4.2.2";
exports.ids = ["vendor-chunks/@smithy+util-stream@4.2.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/ByteArrayCollector.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/ByteArrayCollector.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ByteArrayCollector: () => (/* binding */ ByteArrayCollector)\n/* harmony export */ });\nclass ByteArrayCollector {\n    constructor(allocByteArray) {\n        this.allocByteArray = allocByteArray;\n        this.byteLength = 0;\n        this.byteArrays = [];\n    }\n    push(byteArray) {\n        this.byteArrays.push(byteArray);\n        this.byteLength += byteArray.byteLength;\n    }\n    flush() {\n        if (this.byteArrays.length === 1) {\n            const bytes = this.byteArrays[0];\n            this.reset();\n            return bytes;\n        }\n        const aggregation = this.allocByteArray(this.byteLength);\n        let cursor = 0;\n        for (let i = 0; i < this.byteArrays.length; ++i) {\n            const bytes = this.byteArrays[i];\n            aggregation.set(bytes, cursor);\n            cursor += bytes.byteLength;\n        }\n        this.reset();\n        return aggregation;\n    }\n    reset() {\n        this.byteArrays = [];\n        this.byteLength = 0;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL0J5dGVBcnJheUNvbGxlY3Rvci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLDRCQUE0QjtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtc3RyZWFtQDQuMi4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtc3RyZWFtL2Rpc3QtZXMvQnl0ZUFycmF5Q29sbGVjdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBCeXRlQXJyYXlDb2xsZWN0b3Ige1xuICAgIGNvbnN0cnVjdG9yKGFsbG9jQnl0ZUFycmF5KSB7XG4gICAgICAgIHRoaXMuYWxsb2NCeXRlQXJyYXkgPSBhbGxvY0J5dGVBcnJheTtcbiAgICAgICAgdGhpcy5ieXRlTGVuZ3RoID0gMDtcbiAgICAgICAgdGhpcy5ieXRlQXJyYXlzID0gW107XG4gICAgfVxuICAgIHB1c2goYnl0ZUFycmF5KSB7XG4gICAgICAgIHRoaXMuYnl0ZUFycmF5cy5wdXNoKGJ5dGVBcnJheSk7XG4gICAgICAgIHRoaXMuYnl0ZUxlbmd0aCArPSBieXRlQXJyYXkuYnl0ZUxlbmd0aDtcbiAgICB9XG4gICAgZmx1c2goKSB7XG4gICAgICAgIGlmICh0aGlzLmJ5dGVBcnJheXMubGVuZ3RoID09PSAxKSB7XG4gICAgICAgICAgICBjb25zdCBieXRlcyA9IHRoaXMuYnl0ZUFycmF5c1swXTtcbiAgICAgICAgICAgIHRoaXMucmVzZXQoKTtcbiAgICAgICAgICAgIHJldHVybiBieXRlcztcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBhZ2dyZWdhdGlvbiA9IHRoaXMuYWxsb2NCeXRlQXJyYXkodGhpcy5ieXRlTGVuZ3RoKTtcbiAgICAgICAgbGV0IGN1cnNvciA9IDA7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5ieXRlQXJyYXlzLmxlbmd0aDsgKytpKSB7XG4gICAgICAgICAgICBjb25zdCBieXRlcyA9IHRoaXMuYnl0ZUFycmF5c1tpXTtcbiAgICAgICAgICAgIGFnZ3JlZ2F0aW9uLnNldChieXRlcywgY3Vyc29yKTtcbiAgICAgICAgICAgIGN1cnNvciArPSBieXRlcy5ieXRlTGVuZ3RoO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMucmVzZXQoKTtcbiAgICAgICAgcmV0dXJuIGFnZ3JlZ2F0aW9uO1xuICAgIH1cbiAgICByZXNldCgpIHtcbiAgICAgICAgdGhpcy5ieXRlQXJyYXlzID0gW107XG4gICAgICAgIHRoaXMuYnl0ZUxlbmd0aCA9IDA7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/ByteArrayCollector.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/blob/Uint8ArrayBlobAdapter.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/blob/Uint8ArrayBlobAdapter.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Uint8ArrayBlobAdapter: () => (/* binding */ Uint8ArrayBlobAdapter)\n/* harmony export */ });\n/* harmony import */ var _transforms__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./transforms */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/blob/transforms.js\");\n\nclass Uint8ArrayBlobAdapter extends Uint8Array {\n    static fromString(source, encoding = \"utf-8\") {\n        switch (typeof source) {\n            case \"string\":\n                return (0,_transforms__WEBPACK_IMPORTED_MODULE_0__.transformFromString)(source, encoding);\n            default:\n                throw new Error(`Unsupported conversion from ${typeof source} to Uint8ArrayBlobAdapter.`);\n        }\n    }\n    static mutate(source) {\n        Object.setPrototypeOf(source, Uint8ArrayBlobAdapter.prototype);\n        return source;\n    }\n    transformToString(encoding = \"utf-8\") {\n        return (0,_transforms__WEBPACK_IMPORTED_MODULE_0__.transformToString)(this, encoding);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL2Jsb2IvVWludDhBcnJheUJsb2JBZGFwdGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNFO0FBQy9EO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGdFQUFtQjtBQUMxQztBQUNBLCtEQUErRCxlQUFlO0FBQzlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSw4REFBaUI7QUFDaEM7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1zdHJlYW1ANC4yLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1zdHJlYW0vZGlzdC1lcy9ibG9iL1VpbnQ4QXJyYXlCbG9iQWRhcHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0cmFuc2Zvcm1Gcm9tU3RyaW5nLCB0cmFuc2Zvcm1Ub1N0cmluZyB9IGZyb20gXCIuL3RyYW5zZm9ybXNcIjtcbmV4cG9ydCBjbGFzcyBVaW50OEFycmF5QmxvYkFkYXB0ZXIgZXh0ZW5kcyBVaW50OEFycmF5IHtcbiAgICBzdGF0aWMgZnJvbVN0cmluZyhzb3VyY2UsIGVuY29kaW5nID0gXCJ1dGYtOFwiKSB7XG4gICAgICAgIHN3aXRjaCAodHlwZW9mIHNvdXJjZSkge1xuICAgICAgICAgICAgY2FzZSBcInN0cmluZ1wiOlxuICAgICAgICAgICAgICAgIHJldHVybiB0cmFuc2Zvcm1Gcm9tU3RyaW5nKHNvdXJjZSwgZW5jb2RpbmcpO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuc3VwcG9ydGVkIGNvbnZlcnNpb24gZnJvbSAke3R5cGVvZiBzb3VyY2V9IHRvIFVpbnQ4QXJyYXlCbG9iQWRhcHRlci5gKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzdGF0aWMgbXV0YXRlKHNvdXJjZSkge1xuICAgICAgICBPYmplY3Quc2V0UHJvdG90eXBlT2Yoc291cmNlLCBVaW50OEFycmF5QmxvYkFkYXB0ZXIucHJvdG90eXBlKTtcbiAgICAgICAgcmV0dXJuIHNvdXJjZTtcbiAgICB9XG4gICAgdHJhbnNmb3JtVG9TdHJpbmcoZW5jb2RpbmcgPSBcInV0Zi04XCIpIHtcbiAgICAgICAgcmV0dXJuIHRyYW5zZm9ybVRvU3RyaW5nKHRoaXMsIGVuY29kaW5nKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/blob/Uint8ArrayBlobAdapter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/blob/transforms.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/blob/transforms.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transformFromString: () => (/* binding */ transformFromString),\n/* harmony export */   transformToString: () => (/* binding */ transformToString)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_base64__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-base64 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n/* harmony import */ var _Uint8ArrayBlobAdapter__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Uint8ArrayBlobAdapter */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/blob/Uint8ArrayBlobAdapter.js\");\n\n\n\nfunction transformToString(payload, encoding = \"utf-8\") {\n    if (encoding === \"base64\") {\n        return (0,_smithy_util_base64__WEBPACK_IMPORTED_MODULE_0__.toBase64)(payload);\n    }\n    return (0,_smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__.toUtf8)(payload);\n}\nfunction transformFromString(str, encoding) {\n    if (encoding === \"base64\") {\n        return _Uint8ArrayBlobAdapter__WEBPACK_IMPORTED_MODULE_2__.Uint8ArrayBlobAdapter.mutate((0,_smithy_util_base64__WEBPACK_IMPORTED_MODULE_0__.fromBase64)(str));\n    }\n    return _Uint8ArrayBlobAdapter__WEBPACK_IMPORTED_MODULE_2__.Uint8ArrayBlobAdapter.mutate((0,_smithy_util_utf8__WEBPACK_IMPORTED_MODULE_1__.fromUtf8)(str));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL2Jsb2IvdHJhbnNmb3Jtcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEyRDtBQUNOO0FBQ1c7QUFDekQ7QUFDUDtBQUNBLGVBQWUsNkRBQVE7QUFDdkI7QUFDQSxXQUFXLHlEQUFNO0FBQ2pCO0FBQ087QUFDUDtBQUNBLGVBQWUseUVBQXFCLFFBQVEsK0RBQVU7QUFDdEQ7QUFDQSxXQUFXLHlFQUFxQixRQUFRLDJEQUFRO0FBQ2hEIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL2Jsb2IvdHJhbnNmb3Jtcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBmcm9tQmFzZTY0LCB0b0Jhc2U2NCB9IGZyb20gXCJAc21pdGh5L3V0aWwtYmFzZTY0XCI7XG5pbXBvcnQgeyBmcm9tVXRmOCwgdG9VdGY4IH0gZnJvbSBcIkBzbWl0aHkvdXRpbC11dGY4XCI7XG5pbXBvcnQgeyBVaW50OEFycmF5QmxvYkFkYXB0ZXIgfSBmcm9tIFwiLi9VaW50OEFycmF5QmxvYkFkYXB0ZXJcIjtcbmV4cG9ydCBmdW5jdGlvbiB0cmFuc2Zvcm1Ub1N0cmluZyhwYXlsb2FkLCBlbmNvZGluZyA9IFwidXRmLThcIikge1xuICAgIGlmIChlbmNvZGluZyA9PT0gXCJiYXNlNjRcIikge1xuICAgICAgICByZXR1cm4gdG9CYXNlNjQocGF5bG9hZCk7XG4gICAgfVxuICAgIHJldHVybiB0b1V0ZjgocGF5bG9hZCk7XG59XG5leHBvcnQgZnVuY3Rpb24gdHJhbnNmb3JtRnJvbVN0cmluZyhzdHIsIGVuY29kaW5nKSB7XG4gICAgaWYgKGVuY29kaW5nID09PSBcImJhc2U2NFwiKSB7XG4gICAgICAgIHJldHVybiBVaW50OEFycmF5QmxvYkFkYXB0ZXIubXV0YXRlKGZyb21CYXNlNjQoc3RyKSk7XG4gICAgfVxuICAgIHJldHVybiBVaW50OEFycmF5QmxvYkFkYXB0ZXIubXV0YXRlKGZyb21VdGY4KHN0cikpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/blob/transforms.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/ChecksumStream.browser.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/ChecksumStream.browser.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChecksumStream: () => (/* binding */ ChecksumStream)\n/* harmony export */ });\nconst ReadableStreamRef = typeof ReadableStream === \"function\" ? ReadableStream : function () { };\nclass ChecksumStream extends ReadableStreamRef {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL2NoZWNrc3VtL0NoZWNrc3VtU3RyZWFtLmJyb3dzZXIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ087QUFDUCIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1zdHJlYW1ANC4yLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1zdHJlYW0vZGlzdC1lcy9jaGVja3N1bS9DaGVja3N1bVN0cmVhbS5icm93c2VyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IFJlYWRhYmxlU3RyZWFtUmVmID0gdHlwZW9mIFJlYWRhYmxlU3RyZWFtID09PSBcImZ1bmN0aW9uXCIgPyBSZWFkYWJsZVN0cmVhbSA6IGZ1bmN0aW9uICgpIHsgfTtcbmV4cG9ydCBjbGFzcyBDaGVja3N1bVN0cmVhbSBleHRlbmRzIFJlYWRhYmxlU3RyZWFtUmVmIHtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/ChecksumStream.browser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/ChecksumStream.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/ChecksumStream.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChecksumStream: () => (/* binding */ ChecksumStream)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_base64__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-base64 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_1__);\n\n\nclass ChecksumStream extends stream__WEBPACK_IMPORTED_MODULE_1__.Duplex {\n    constructor({ expectedChecksum, checksum, source, checksumSourceLocation, base64Encoder, }) {\n        super();\n        if (typeof source.pipe === \"function\") {\n            this.source = source;\n        }\n        else {\n            throw new Error(`@smithy/util-stream: unsupported source type ${source?.constructor?.name ?? source} in ChecksumStream.`);\n        }\n        this.base64Encoder = base64Encoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_0__.toBase64;\n        this.expectedChecksum = expectedChecksum;\n        this.checksum = checksum;\n        this.checksumSourceLocation = checksumSourceLocation;\n        this.source.pipe(this);\n    }\n    _read(size) { }\n    _write(chunk, encoding, callback) {\n        try {\n            this.checksum.update(chunk);\n            this.push(chunk);\n        }\n        catch (e) {\n            return callback(e);\n        }\n        return callback();\n    }\n    async _final(callback) {\n        try {\n            const digest = await this.checksum.digest();\n            const received = this.base64Encoder(digest);\n            if (this.expectedChecksum !== received) {\n                return callback(new Error(`Checksum mismatch: expected \"${this.expectedChecksum}\" but received \"${received}\"` +\n                    ` in response header \"${this.checksumSourceLocation}\".`));\n            }\n        }\n        catch (e) {\n            return callback(e);\n        }\n        this.push(null);\n        return callback();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/ChecksumStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/createChecksumStream.browser.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/createChecksumStream.browser.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createChecksumStream: () => (/* binding */ createChecksumStream)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_base64__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-base64 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js\");\n/* harmony import */ var _stream_type_check__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../stream-type-check */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/stream-type-check.js\");\n/* harmony import */ var _ChecksumStream_browser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChecksumStream.browser */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/ChecksumStream.browser.js\");\n\n\n\nconst createChecksumStream = ({ expectedChecksum, checksum, source, checksumSourceLocation, base64Encoder, }) => {\n    if (!(0,_stream_type_check__WEBPACK_IMPORTED_MODULE_1__.isReadableStream)(source)) {\n        throw new Error(`@smithy/util-stream: unsupported source type ${source?.constructor?.name ?? source} in ChecksumStream.`);\n    }\n    const encoder = base64Encoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_0__.toBase64;\n    if (typeof TransformStream !== \"function\") {\n        throw new Error(\"@smithy/util-stream: unable to instantiate ChecksumStream because API unavailable: ReadableStream/TransformStream.\");\n    }\n    const transform = new TransformStream({\n        start() { },\n        async transform(chunk, controller) {\n            checksum.update(chunk);\n            controller.enqueue(chunk);\n        },\n        async flush(controller) {\n            const digest = await checksum.digest();\n            const received = encoder(digest);\n            if (expectedChecksum !== received) {\n                const error = new Error(`Checksum mismatch: expected \"${expectedChecksum}\" but received \"${received}\"` +\n                    ` in response header \"${checksumSourceLocation}\".`);\n                controller.error(error);\n            }\n            else {\n                controller.terminate();\n            }\n        },\n    });\n    source.pipeThrough(transform);\n    const readable = transform.readable;\n    Object.setPrototypeOf(readable, _ChecksumStream_browser__WEBPACK_IMPORTED_MODULE_2__.ChecksumStream.prototype);\n    return readable;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/createChecksumStream.browser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/createChecksumStream.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/createChecksumStream.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createChecksumStream: () => (/* binding */ createChecksumStream)\n/* harmony export */ });\n/* harmony import */ var _stream_type_check__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stream-type-check */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/stream-type-check.js\");\n/* harmony import */ var _ChecksumStream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ChecksumStream */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/ChecksumStream.js\");\n/* harmony import */ var _createChecksumStream_browser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createChecksumStream.browser */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/createChecksumStream.browser.js\");\n\n\n\nfunction createChecksumStream(init) {\n    if (typeof ReadableStream === \"function\" && (0,_stream_type_check__WEBPACK_IMPORTED_MODULE_0__.isReadableStream)(init.source)) {\n        return (0,_createChecksumStream_browser__WEBPACK_IMPORTED_MODULE_2__.createChecksumStream)(init);\n    }\n    return new _ChecksumStream__WEBPACK_IMPORTED_MODULE_1__.ChecksumStream(init);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL2NoZWNrc3VtL2NyZWF0ZUNoZWNrc3VtU3RyZWFtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBd0Q7QUFDTjtBQUMrQztBQUMxRjtBQUNQLGdEQUFnRCxvRUFBZ0I7QUFDaEUsZUFBZSxtRkFBdUI7QUFDdEM7QUFDQSxlQUFlLDJEQUFjO0FBQzdCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL2NoZWNrc3VtL2NyZWF0ZUNoZWNrc3VtU3RyZWFtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzUmVhZGFibGVTdHJlYW0gfSBmcm9tIFwiLi4vc3RyZWFtLXR5cGUtY2hlY2tcIjtcbmltcG9ydCB7IENoZWNrc3VtU3RyZWFtIH0gZnJvbSBcIi4vQ2hlY2tzdW1TdHJlYW1cIjtcbmltcG9ydCB7IGNyZWF0ZUNoZWNrc3VtU3RyZWFtIGFzIGNyZWF0ZUNoZWNrc3VtU3RyZWFtV2ViIH0gZnJvbSBcIi4vY3JlYXRlQ2hlY2tzdW1TdHJlYW0uYnJvd3NlclwiO1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUNoZWNrc3VtU3RyZWFtKGluaXQpIHtcbiAgICBpZiAodHlwZW9mIFJlYWRhYmxlU3RyZWFtID09PSBcImZ1bmN0aW9uXCIgJiYgaXNSZWFkYWJsZVN0cmVhbShpbml0LnNvdXJjZSkpIHtcbiAgICAgICAgcmV0dXJuIGNyZWF0ZUNoZWNrc3VtU3RyZWFtV2ViKGluaXQpO1xuICAgIH1cbiAgICByZXR1cm4gbmV3IENoZWNrc3VtU3RyZWFtKGluaXQpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/createChecksumStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/createBufferedReadable.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/createBufferedReadable.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBufferedReadable: () => (/* binding */ createBufferedReadable)\n/* harmony export */ });\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(node_stream__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ByteArrayCollector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ByteArrayCollector */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/ByteArrayCollector.js\");\n/* harmony import */ var _createBufferedReadableStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createBufferedReadableStream */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/createBufferedReadableStream.js\");\n/* harmony import */ var _stream_type_check__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./stream-type-check */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/stream-type-check.js\");\n\n\n\n\nfunction createBufferedReadable(upstream, size, logger) {\n    if ((0,_stream_type_check__WEBPACK_IMPORTED_MODULE_3__.isReadableStream)(upstream)) {\n        return (0,_createBufferedReadableStream__WEBPACK_IMPORTED_MODULE_2__.createBufferedReadableStream)(upstream, size, logger);\n    }\n    const downstream = new node_stream__WEBPACK_IMPORTED_MODULE_0__.Readable({ read() { } });\n    let streamBufferingLoggedWarning = false;\n    let bytesSeen = 0;\n    const buffers = [\n        \"\",\n        new _ByteArrayCollector__WEBPACK_IMPORTED_MODULE_1__.ByteArrayCollector((size) => new Uint8Array(size)),\n        new _ByteArrayCollector__WEBPACK_IMPORTED_MODULE_1__.ByteArrayCollector((size) => Buffer.from(new Uint8Array(size))),\n    ];\n    let mode = -1;\n    upstream.on(\"data\", (chunk) => {\n        const chunkMode = (0,_createBufferedReadableStream__WEBPACK_IMPORTED_MODULE_2__.modeOf)(chunk, true);\n        if (mode !== chunkMode) {\n            if (mode >= 0) {\n                downstream.push((0,_createBufferedReadableStream__WEBPACK_IMPORTED_MODULE_2__.flush)(buffers, mode));\n            }\n            mode = chunkMode;\n        }\n        if (mode === -1) {\n            downstream.push(chunk);\n            return;\n        }\n        const chunkSize = (0,_createBufferedReadableStream__WEBPACK_IMPORTED_MODULE_2__.sizeOf)(chunk);\n        bytesSeen += chunkSize;\n        const bufferSize = (0,_createBufferedReadableStream__WEBPACK_IMPORTED_MODULE_2__.sizeOf)(buffers[mode]);\n        if (chunkSize >= size && bufferSize === 0) {\n            downstream.push(chunk);\n        }\n        else {\n            const newSize = (0,_createBufferedReadableStream__WEBPACK_IMPORTED_MODULE_2__.merge)(buffers, mode, chunk);\n            if (!streamBufferingLoggedWarning && bytesSeen > size * 2) {\n                streamBufferingLoggedWarning = true;\n                logger?.warn(`@smithy/util-stream - stream chunk size ${chunkSize} is below threshold of ${size}, automatically buffering.`);\n            }\n            if (newSize >= size) {\n                downstream.push((0,_createBufferedReadableStream__WEBPACK_IMPORTED_MODULE_2__.flush)(buffers, mode));\n            }\n        }\n    });\n    upstream.on(\"end\", () => {\n        if (mode !== -1) {\n            const remainder = (0,_createBufferedReadableStream__WEBPACK_IMPORTED_MODULE_2__.flush)(buffers, mode);\n            if ((0,_createBufferedReadableStream__WEBPACK_IMPORTED_MODULE_2__.sizeOf)(remainder) > 0) {\n                downstream.push(remainder);\n            }\n        }\n        downstream.push(null);\n    });\n    return downstream;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL2NyZWF0ZUJ1ZmZlcmVkUmVhZGFibGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXVDO0FBQ21CO0FBQ2tEO0FBQ3JEO0FBQ2hEO0FBQ1AsUUFBUSxvRUFBZ0I7QUFDeEIsZUFBZSwyRkFBNEI7QUFDM0M7QUFDQSwyQkFBMkIsaURBQVEsR0FBRyxZQUFZO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxtRUFBa0I7QUFDOUIsWUFBWSxtRUFBa0I7QUFDOUI7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLHFFQUFNO0FBQ2hDO0FBQ0E7QUFDQSxnQ0FBZ0Msb0VBQUs7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIscUVBQU07QUFDaEM7QUFDQSwyQkFBMkIscUVBQU07QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsb0VBQUs7QUFDakM7QUFDQTtBQUNBLHdFQUF3RSxXQUFXLHdCQUF3QixLQUFLO0FBQ2hIO0FBQ0E7QUFDQSxnQ0FBZ0Msb0VBQUs7QUFDckM7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsOEJBQThCLG9FQUFLO0FBQ25DLGdCQUFnQixxRUFBTTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL2NyZWF0ZUJ1ZmZlcmVkUmVhZGFibGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUmVhZGFibGUgfSBmcm9tIFwibm9kZTpzdHJlYW1cIjtcbmltcG9ydCB7IEJ5dGVBcnJheUNvbGxlY3RvciB9IGZyb20gXCIuL0J5dGVBcnJheUNvbGxlY3RvclwiO1xuaW1wb3J0IHsgY3JlYXRlQnVmZmVyZWRSZWFkYWJsZVN0cmVhbSwgZmx1c2gsIG1lcmdlLCBtb2RlT2YsIHNpemVPZiB9IGZyb20gXCIuL2NyZWF0ZUJ1ZmZlcmVkUmVhZGFibGVTdHJlYW1cIjtcbmltcG9ydCB7IGlzUmVhZGFibGVTdHJlYW0gfSBmcm9tIFwiLi9zdHJlYW0tdHlwZS1jaGVja1wiO1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUJ1ZmZlcmVkUmVhZGFibGUodXBzdHJlYW0sIHNpemUsIGxvZ2dlcikge1xuICAgIGlmIChpc1JlYWRhYmxlU3RyZWFtKHVwc3RyZWFtKSkge1xuICAgICAgICByZXR1cm4gY3JlYXRlQnVmZmVyZWRSZWFkYWJsZVN0cmVhbSh1cHN0cmVhbSwgc2l6ZSwgbG9nZ2VyKTtcbiAgICB9XG4gICAgY29uc3QgZG93bnN0cmVhbSA9IG5ldyBSZWFkYWJsZSh7IHJlYWQoKSB7IH0gfSk7XG4gICAgbGV0IHN0cmVhbUJ1ZmZlcmluZ0xvZ2dlZFdhcm5pbmcgPSBmYWxzZTtcbiAgICBsZXQgYnl0ZXNTZWVuID0gMDtcbiAgICBjb25zdCBidWZmZXJzID0gW1xuICAgICAgICBcIlwiLFxuICAgICAgICBuZXcgQnl0ZUFycmF5Q29sbGVjdG9yKChzaXplKSA9PiBuZXcgVWludDhBcnJheShzaXplKSksXG4gICAgICAgIG5ldyBCeXRlQXJyYXlDb2xsZWN0b3IoKHNpemUpID0+IEJ1ZmZlci5mcm9tKG5ldyBVaW50OEFycmF5KHNpemUpKSksXG4gICAgXTtcbiAgICBsZXQgbW9kZSA9IC0xO1xuICAgIHVwc3RyZWFtLm9uKFwiZGF0YVwiLCAoY2h1bmspID0+IHtcbiAgICAgICAgY29uc3QgY2h1bmtNb2RlID0gbW9kZU9mKGNodW5rLCB0cnVlKTtcbiAgICAgICAgaWYgKG1vZGUgIT09IGNodW5rTW9kZSkge1xuICAgICAgICAgICAgaWYgKG1vZGUgPj0gMCkge1xuICAgICAgICAgICAgICAgIGRvd25zdHJlYW0ucHVzaChmbHVzaChidWZmZXJzLCBtb2RlKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBtb2RlID0gY2h1bmtNb2RlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChtb2RlID09PSAtMSkge1xuICAgICAgICAgICAgZG93bnN0cmVhbS5wdXNoKGNodW5rKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBjaHVua1NpemUgPSBzaXplT2YoY2h1bmspO1xuICAgICAgICBieXRlc1NlZW4gKz0gY2h1bmtTaXplO1xuICAgICAgICBjb25zdCBidWZmZXJTaXplID0gc2l6ZU9mKGJ1ZmZlcnNbbW9kZV0pO1xuICAgICAgICBpZiAoY2h1bmtTaXplID49IHNpemUgJiYgYnVmZmVyU2l6ZSA9PT0gMCkge1xuICAgICAgICAgICAgZG93bnN0cmVhbS5wdXNoKGNodW5rKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGNvbnN0IG5ld1NpemUgPSBtZXJnZShidWZmZXJzLCBtb2RlLCBjaHVuayk7XG4gICAgICAgICAgICBpZiAoIXN0cmVhbUJ1ZmZlcmluZ0xvZ2dlZFdhcm5pbmcgJiYgYnl0ZXNTZWVuID4gc2l6ZSAqIDIpIHtcbiAgICAgICAgICAgICAgICBzdHJlYW1CdWZmZXJpbmdMb2dnZWRXYXJuaW5nID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICBsb2dnZXI/Lndhcm4oYEBzbWl0aHkvdXRpbC1zdHJlYW0gLSBzdHJlYW0gY2h1bmsgc2l6ZSAke2NodW5rU2l6ZX0gaXMgYmVsb3cgdGhyZXNob2xkIG9mICR7c2l6ZX0sIGF1dG9tYXRpY2FsbHkgYnVmZmVyaW5nLmApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKG5ld1NpemUgPj0gc2l6ZSkge1xuICAgICAgICAgICAgICAgIGRvd25zdHJlYW0ucHVzaChmbHVzaChidWZmZXJzLCBtb2RlKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9KTtcbiAgICB1cHN0cmVhbS5vbihcImVuZFwiLCAoKSA9PiB7XG4gICAgICAgIGlmIChtb2RlICE9PSAtMSkge1xuICAgICAgICAgICAgY29uc3QgcmVtYWluZGVyID0gZmx1c2goYnVmZmVycywgbW9kZSk7XG4gICAgICAgICAgICBpZiAoc2l6ZU9mKHJlbWFpbmRlcikgPiAwKSB7XG4gICAgICAgICAgICAgICAgZG93bnN0cmVhbS5wdXNoKHJlbWFpbmRlcik7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZG93bnN0cmVhbS5wdXNoKG51bGwpO1xuICAgIH0pO1xuICAgIHJldHVybiBkb3duc3RyZWFtO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/createBufferedReadable.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/createBufferedReadableStream.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/createBufferedReadableStream.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBufferedReadable: () => (/* binding */ createBufferedReadable),\n/* harmony export */   createBufferedReadableStream: () => (/* binding */ createBufferedReadableStream),\n/* harmony export */   flush: () => (/* binding */ flush),\n/* harmony export */   merge: () => (/* binding */ merge),\n/* harmony export */   modeOf: () => (/* binding */ modeOf),\n/* harmony export */   sizeOf: () => (/* binding */ sizeOf)\n/* harmony export */ });\n/* harmony import */ var _ByteArrayCollector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ByteArrayCollector */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/ByteArrayCollector.js\");\n\nfunction createBufferedReadableStream(upstream, size, logger) {\n    const reader = upstream.getReader();\n    let streamBufferingLoggedWarning = false;\n    let bytesSeen = 0;\n    const buffers = [\"\", new _ByteArrayCollector__WEBPACK_IMPORTED_MODULE_0__.ByteArrayCollector((size) => new Uint8Array(size))];\n    let mode = -1;\n    const pull = async (controller) => {\n        const { value, done } = await reader.read();\n        const chunk = value;\n        if (done) {\n            if (mode !== -1) {\n                const remainder = flush(buffers, mode);\n                if (sizeOf(remainder) > 0) {\n                    controller.enqueue(remainder);\n                }\n            }\n            controller.close();\n        }\n        else {\n            const chunkMode = modeOf(chunk, false);\n            if (mode !== chunkMode) {\n                if (mode >= 0) {\n                    controller.enqueue(flush(buffers, mode));\n                }\n                mode = chunkMode;\n            }\n            if (mode === -1) {\n                controller.enqueue(chunk);\n                return;\n            }\n            const chunkSize = sizeOf(chunk);\n            bytesSeen += chunkSize;\n            const bufferSize = sizeOf(buffers[mode]);\n            if (chunkSize >= size && bufferSize === 0) {\n                controller.enqueue(chunk);\n            }\n            else {\n                const newSize = merge(buffers, mode, chunk);\n                if (!streamBufferingLoggedWarning && bytesSeen > size * 2) {\n                    streamBufferingLoggedWarning = true;\n                    logger?.warn(`@smithy/util-stream - stream chunk size ${chunkSize} is below threshold of ${size}, automatically buffering.`);\n                }\n                if (newSize >= size) {\n                    controller.enqueue(flush(buffers, mode));\n                }\n                else {\n                    await pull(controller);\n                }\n            }\n        }\n    };\n    return new ReadableStream({\n        pull,\n    });\n}\nconst createBufferedReadable = createBufferedReadableStream;\nfunction merge(buffers, mode, chunk) {\n    switch (mode) {\n        case 0:\n            buffers[0] += chunk;\n            return sizeOf(buffers[0]);\n        case 1:\n        case 2:\n            buffers[mode].push(chunk);\n            return sizeOf(buffers[mode]);\n    }\n}\nfunction flush(buffers, mode) {\n    switch (mode) {\n        case 0:\n            const s = buffers[0];\n            buffers[0] = \"\";\n            return s;\n        case 1:\n        case 2:\n            return buffers[mode].flush();\n    }\n    throw new Error(`@smithy/util-stream - invalid index ${mode} given to flush()`);\n}\nfunction sizeOf(chunk) {\n    return chunk?.byteLength ?? chunk?.length ?? 0;\n}\nfunction modeOf(chunk, allowBuffer = true) {\n    if (allowBuffer && typeof Buffer !== \"undefined\" && chunk instanceof Buffer) {\n        return 2;\n    }\n    if (chunk instanceof Uint8Array) {\n        return 1;\n    }\n    if (typeof chunk === \"string\") {\n        return 0;\n    }\n    return -1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/createBufferedReadableStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/getAwsChunkedEncodingStream.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/getAwsChunkedEncodingStream.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAwsChunkedEncodingStream: () => (/* binding */ getAwsChunkedEncodingStream)\n/* harmony export */ });\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_0__);\n\nconst getAwsChunkedEncodingStream = (readableStream, options) => {\n    const { base64Encoder, bodyLengthChecker, checksumAlgorithmFn, checksumLocationName, streamHasher } = options;\n    const checksumRequired = base64Encoder !== undefined &&\n        checksumAlgorithmFn !== undefined &&\n        checksumLocationName !== undefined &&\n        streamHasher !== undefined;\n    const digest = checksumRequired ? streamHasher(checksumAlgorithmFn, readableStream) : undefined;\n    const awsChunkedEncodingStream = new stream__WEBPACK_IMPORTED_MODULE_0__.Readable({ read: () => { } });\n    readableStream.on(\"data\", (data) => {\n        const length = bodyLengthChecker(data) || 0;\n        awsChunkedEncodingStream.push(`${length.toString(16)}\\r\\n`);\n        awsChunkedEncodingStream.push(data);\n        awsChunkedEncodingStream.push(\"\\r\\n\");\n    });\n    readableStream.on(\"end\", async () => {\n        awsChunkedEncodingStream.push(`0\\r\\n`);\n        if (checksumRequired) {\n            const checksum = base64Encoder(await digest);\n            awsChunkedEncodingStream.push(`${checksumLocationName}:${checksum}\\r\\n`);\n            awsChunkedEncodingStream.push(`\\r\\n`);\n        }\n        awsChunkedEncodingStream.push(null);\n    });\n    return awsChunkedEncodingStream;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/getAwsChunkedEncodingStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/headStream.browser.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/headStream.browser.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headStream: () => (/* binding */ headStream)\n/* harmony export */ });\nasync function headStream(stream, bytes) {\n    let byteLengthCounter = 0;\n    const chunks = [];\n    const reader = stream.getReader();\n    let isDone = false;\n    while (!isDone) {\n        const { done, value } = await reader.read();\n        if (value) {\n            chunks.push(value);\n            byteLengthCounter += value?.byteLength ?? 0;\n        }\n        if (byteLengthCounter >= bytes) {\n            break;\n        }\n        isDone = done;\n    }\n    reader.releaseLock();\n    const collected = new Uint8Array(Math.min(bytes, byteLengthCounter));\n    let offset = 0;\n    for (const chunk of chunks) {\n        if (chunk.byteLength > collected.byteLength - offset) {\n            collected.set(chunk.subarray(0, collected.byteLength - offset), offset);\n            break;\n        }\n        else {\n            collected.set(chunk, offset);\n        }\n        offset += chunk.length;\n    }\n    return collected;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/headStream.browser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/headStream.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/headStream.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headStream: () => (/* binding */ headStream)\n/* harmony export */ });\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _headStream_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./headStream.browser */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/headStream.browser.js\");\n/* harmony import */ var _stream_type_check__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stream-type-check */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/stream-type-check.js\");\n\n\n\nconst headStream = (stream, bytes) => {\n    if ((0,_stream_type_check__WEBPACK_IMPORTED_MODULE_2__.isReadableStream)(stream)) {\n        return (0,_headStream_browser__WEBPACK_IMPORTED_MODULE_1__.headStream)(stream, bytes);\n    }\n    return new Promise((resolve, reject) => {\n        const collector = new Collector();\n        collector.limit = bytes;\n        stream.pipe(collector);\n        stream.on(\"error\", (err) => {\n            collector.end();\n            reject(err);\n        });\n        collector.on(\"error\", reject);\n        collector.on(\"finish\", function () {\n            const bytes = new Uint8Array(Buffer.concat(this.buffers));\n            resolve(bytes);\n        });\n    });\n};\nclass Collector extends stream__WEBPACK_IMPORTED_MODULE_0__.Writable {\n    constructor() {\n        super(...arguments);\n        this.buffers = [];\n        this.limit = Infinity;\n        this.bytesBuffered = 0;\n    }\n    _write(chunk, encoding, callback) {\n        this.buffers.push(chunk);\n        this.bytesBuffered += chunk.byteLength ?? 0;\n        if (this.bytesBuffered >= this.limit) {\n            const excess = this.bytesBuffered - this.limit;\n            const tailBuffer = this.buffers[this.buffers.length - 1];\n            this.buffers[this.buffers.length - 1] = tailBuffer.subarray(0, tailBuffer.byteLength - excess);\n            this.emit(\"finish\");\n        }\n        callback();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/headStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/index.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/index.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChecksumStream: () => (/* reexport safe */ _checksum_ChecksumStream__WEBPACK_IMPORTED_MODULE_1__.ChecksumStream),\n/* harmony export */   Uint8ArrayBlobAdapter: () => (/* reexport safe */ _blob_Uint8ArrayBlobAdapter__WEBPACK_IMPORTED_MODULE_0__.Uint8ArrayBlobAdapter),\n/* harmony export */   createBufferedReadable: () => (/* reexport safe */ _createBufferedReadable__WEBPACK_IMPORTED_MODULE_3__.createBufferedReadable),\n/* harmony export */   createChecksumStream: () => (/* reexport safe */ _checksum_createChecksumStream__WEBPACK_IMPORTED_MODULE_2__.createChecksumStream),\n/* harmony export */   getAwsChunkedEncodingStream: () => (/* reexport safe */ _getAwsChunkedEncodingStream__WEBPACK_IMPORTED_MODULE_4__.getAwsChunkedEncodingStream),\n/* harmony export */   headStream: () => (/* reexport safe */ _headStream__WEBPACK_IMPORTED_MODULE_5__.headStream),\n/* harmony export */   isBlob: () => (/* reexport safe */ _stream_type_check__WEBPACK_IMPORTED_MODULE_8__.isBlob),\n/* harmony export */   isReadableStream: () => (/* reexport safe */ _stream_type_check__WEBPACK_IMPORTED_MODULE_8__.isReadableStream),\n/* harmony export */   sdkStreamMixin: () => (/* reexport safe */ _sdk_stream_mixin__WEBPACK_IMPORTED_MODULE_6__.sdkStreamMixin),\n/* harmony export */   splitStream: () => (/* reexport safe */ _splitStream__WEBPACK_IMPORTED_MODULE_7__.splitStream)\n/* harmony export */ });\n/* harmony import */ var _blob_Uint8ArrayBlobAdapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blob/Uint8ArrayBlobAdapter */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/blob/Uint8ArrayBlobAdapter.js\");\n/* harmony import */ var _checksum_ChecksumStream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./checksum/ChecksumStream */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/ChecksumStream.js\");\n/* harmony import */ var _checksum_createChecksumStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./checksum/createChecksumStream */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/checksum/createChecksumStream.js\");\n/* harmony import */ var _createBufferedReadable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./createBufferedReadable */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/createBufferedReadable.js\");\n/* harmony import */ var _getAwsChunkedEncodingStream__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getAwsChunkedEncodingStream */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/getAwsChunkedEncodingStream.js\");\n/* harmony import */ var _headStream__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./headStream */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/headStream.js\");\n/* harmony import */ var _sdk_stream_mixin__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./sdk-stream-mixin */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/sdk-stream-mixin.js\");\n/* harmony import */ var _splitStream__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./splitStream */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/splitStream.js\");\n/* harmony import */ var _stream_type_check__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./stream-type-check */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/stream-type-check.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNkM7QUFDSDtBQUNNO0FBQ1A7QUFDSztBQUNqQjtBQUNNO0FBQ0w7QUFDTSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1zdHJlYW1ANC4yLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1zdHJlYW0vZGlzdC1lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9ibG9iL1VpbnQ4QXJyYXlCbG9iQWRhcHRlclwiO1xuZXhwb3J0ICogZnJvbSBcIi4vY2hlY2tzdW0vQ2hlY2tzdW1TdHJlYW1cIjtcbmV4cG9ydCAqIGZyb20gXCIuL2NoZWNrc3VtL2NyZWF0ZUNoZWNrc3VtU3RyZWFtXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jcmVhdGVCdWZmZXJlZFJlYWRhYmxlXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9nZXRBd3NDaHVua2VkRW5jb2RpbmdTdHJlYW1cIjtcbmV4cG9ydCAqIGZyb20gXCIuL2hlYWRTdHJlYW1cIjtcbmV4cG9ydCAqIGZyb20gXCIuL3Nkay1zdHJlYW0tbWl4aW5cIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NwbGl0U3RyZWFtXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zdHJlYW0tdHlwZS1jaGVja1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/sdk-stream-mixin.browser.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/sdk-stream-mixin.browser.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sdkStreamMixin: () => (/* binding */ sdkStreamMixin)\n/* harmony export */ });\n/* harmony import */ var _smithy_fetch_http_handler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/fetch-http-handler */ \"(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/index.js\");\n/* harmony import */ var _smithy_util_base64__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-base64 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js\");\n/* harmony import */ var _smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/util-hex-encoding */ \"(rsc)/./node_modules/.pnpm/@smithy+util-hex-encoding@4.0.0/node_modules/@smithy/util-hex-encoding/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n/* harmony import */ var _stream_type_check__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./stream-type-check */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/stream-type-check.js\");\n\n\n\n\n\nconst ERR_MSG_STREAM_HAS_BEEN_TRANSFORMED = \"The stream has already been transformed.\";\nconst sdkStreamMixin = (stream) => {\n    if (!isBlobInstance(stream) && !(0,_stream_type_check__WEBPACK_IMPORTED_MODULE_4__.isReadableStream)(stream)) {\n        const name = stream?.__proto__?.constructor?.name || stream;\n        throw new Error(`Unexpected stream implementation, expect Blob or ReadableStream, got ${name}`);\n    }\n    let transformed = false;\n    const transformToByteArray = async () => {\n        if (transformed) {\n            throw new Error(ERR_MSG_STREAM_HAS_BEEN_TRANSFORMED);\n        }\n        transformed = true;\n        return await (0,_smithy_fetch_http_handler__WEBPACK_IMPORTED_MODULE_0__.streamCollector)(stream);\n    };\n    const blobToWebStream = (blob) => {\n        if (typeof blob.stream !== \"function\") {\n            throw new Error(\"Cannot transform payload Blob to web stream. Please make sure the Blob.stream() is polyfilled.\\n\" +\n                \"If you are using React Native, this API is not yet supported, see: https://react-native.canny.io/feature-requests/p/fetch-streaming-body\");\n        }\n        return blob.stream();\n    };\n    return Object.assign(stream, {\n        transformToByteArray: transformToByteArray,\n        transformToString: async (encoding) => {\n            const buf = await transformToByteArray();\n            if (encoding === \"base64\") {\n                return (0,_smithy_util_base64__WEBPACK_IMPORTED_MODULE_1__.toBase64)(buf);\n            }\n            else if (encoding === \"hex\") {\n                return (0,_smithy_util_hex_encoding__WEBPACK_IMPORTED_MODULE_2__.toHex)(buf);\n            }\n            else if (encoding === undefined || encoding === \"utf8\" || encoding === \"utf-8\") {\n                return (0,_smithy_util_utf8__WEBPACK_IMPORTED_MODULE_3__.toUtf8)(buf);\n            }\n            else if (typeof TextDecoder === \"function\") {\n                return new TextDecoder(encoding).decode(buf);\n            }\n            else {\n                throw new Error(\"TextDecoder is not available, please make sure polyfill is provided.\");\n            }\n        },\n        transformToWebStream: () => {\n            if (transformed) {\n                throw new Error(ERR_MSG_STREAM_HAS_BEEN_TRANSFORMED);\n            }\n            transformed = true;\n            if (isBlobInstance(stream)) {\n                return blobToWebStream(stream);\n            }\n            else if ((0,_stream_type_check__WEBPACK_IMPORTED_MODULE_4__.isReadableStream)(stream)) {\n                return stream;\n            }\n            else {\n                throw new Error(`Cannot transform payload to web stream, got ${stream}`);\n            }\n        },\n    });\n};\nconst isBlobInstance = (stream) => typeof Blob === \"function\" && stream instanceof Blob;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/sdk-stream-mixin.browser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/sdk-stream-mixin.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/sdk-stream-mixin.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sdkStreamMixin: () => (/* binding */ sdkStreamMixin)\n/* harmony export */ });\n/* harmony import */ var _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/node-http-handler */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/index.js\");\n/* harmony import */ var _smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-buffer-from */ \"(rsc)/./node_modules/.pnpm/@smithy+util-buffer-from@4.0.0/node_modules/@smithy/util-buffer-from/dist-es/index.js\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _sdk_stream_mixin_browser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sdk-stream-mixin.browser */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/sdk-stream-mixin.browser.js\");\n\n\n\n\nconst ERR_MSG_STREAM_HAS_BEEN_TRANSFORMED = \"The stream has already been transformed.\";\nconst sdkStreamMixin = (stream) => {\n    if (!(stream instanceof stream__WEBPACK_IMPORTED_MODULE_2__.Readable)) {\n        try {\n            return (0,_sdk_stream_mixin_browser__WEBPACK_IMPORTED_MODULE_3__.sdkStreamMixin)(stream);\n        }\n        catch (e) {\n            const name = stream?.__proto__?.constructor?.name || stream;\n            throw new Error(`Unexpected stream implementation, expect Stream.Readable instance, got ${name}`);\n        }\n    }\n    let transformed = false;\n    const transformToByteArray = async () => {\n        if (transformed) {\n            throw new Error(ERR_MSG_STREAM_HAS_BEEN_TRANSFORMED);\n        }\n        transformed = true;\n        return await (0,_smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_0__.streamCollector)(stream);\n    };\n    return Object.assign(stream, {\n        transformToByteArray,\n        transformToString: async (encoding) => {\n            const buf = await transformToByteArray();\n            if (encoding === undefined || Buffer.isEncoding(encoding)) {\n                return (0,_smithy_util_buffer_from__WEBPACK_IMPORTED_MODULE_1__.fromArrayBuffer)(buf.buffer, buf.byteOffset, buf.byteLength).toString(encoding);\n            }\n            else {\n                const decoder = new TextDecoder(encoding);\n                return decoder.decode(buf);\n            }\n        },\n        transformToWebStream: () => {\n            if (transformed) {\n                throw new Error(ERR_MSG_STREAM_HAS_BEEN_TRANSFORMED);\n            }\n            if (stream.readableFlowing !== null) {\n                throw new Error(\"The stream has been consumed by other callbacks.\");\n            }\n            if (typeof stream__WEBPACK_IMPORTED_MODULE_2__.Readable.toWeb !== \"function\") {\n                throw new Error(\"Readable.toWeb() is not supported. Please ensure a polyfill is available.\");\n            }\n            transformed = true;\n            return stream__WEBPACK_IMPORTED_MODULE_2__.Readable.toWeb(stream);\n        },\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/sdk-stream-mixin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/splitStream.browser.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/splitStream.browser.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   splitStream: () => (/* binding */ splitStream)\n/* harmony export */ });\nasync function splitStream(stream) {\n    if (typeof stream.stream === \"function\") {\n        stream = stream.stream();\n    }\n    const readableStream = stream;\n    return readableStream.tee();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL3NwbGl0U3RyZWFtLmJyb3dzZXIuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL3NwbGl0U3RyZWFtLmJyb3dzZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNwbGl0U3RyZWFtKHN0cmVhbSkge1xuICAgIGlmICh0eXBlb2Ygc3RyZWFtLnN0cmVhbSA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgIHN0cmVhbSA9IHN0cmVhbS5zdHJlYW0oKTtcbiAgICB9XG4gICAgY29uc3QgcmVhZGFibGVTdHJlYW0gPSBzdHJlYW07XG4gICAgcmV0dXJuIHJlYWRhYmxlU3RyZWFtLnRlZSgpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/splitStream.browser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/splitStream.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/splitStream.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   splitStream: () => (/* binding */ splitStream)\n/* harmony export */ });\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(stream__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _splitStream_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./splitStream.browser */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/splitStream.browser.js\");\n/* harmony import */ var _stream_type_check__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stream-type-check */ \"(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/stream-type-check.js\");\n\n\n\nasync function splitStream(stream) {\n    if ((0,_stream_type_check__WEBPACK_IMPORTED_MODULE_2__.isReadableStream)(stream) || (0,_stream_type_check__WEBPACK_IMPORTED_MODULE_2__.isBlob)(stream)) {\n        return (0,_splitStream_browser__WEBPACK_IMPORTED_MODULE_1__.splitStream)(stream);\n    }\n    const stream1 = new stream__WEBPACK_IMPORTED_MODULE_0__.PassThrough();\n    const stream2 = new stream__WEBPACK_IMPORTED_MODULE_0__.PassThrough();\n    stream.pipe(stream1);\n    stream.pipe(stream2);\n    return [stream1, stream2];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL3NwbGl0U3RyZWFtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFDO0FBQ2lDO0FBQ1A7QUFDeEQ7QUFDUCxRQUFRLG9FQUFnQixZQUFZLDBEQUFNO0FBQzFDLGVBQWUsaUVBQWM7QUFDN0I7QUFDQSx3QkFBd0IsK0NBQVc7QUFDbkMsd0JBQXdCLCtDQUFXO0FBQ25DO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL3NwbGl0U3RyZWFtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhc3NUaHJvdWdoIH0gZnJvbSBcInN0cmVhbVwiO1xuaW1wb3J0IHsgc3BsaXRTdHJlYW0gYXMgc3BsaXRXZWJTdHJlYW0gfSBmcm9tIFwiLi9zcGxpdFN0cmVhbS5icm93c2VyXCI7XG5pbXBvcnQgeyBpc0Jsb2IsIGlzUmVhZGFibGVTdHJlYW0gfSBmcm9tIFwiLi9zdHJlYW0tdHlwZS1jaGVja1wiO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNwbGl0U3RyZWFtKHN0cmVhbSkge1xuICAgIGlmIChpc1JlYWRhYmxlU3RyZWFtKHN0cmVhbSkgfHwgaXNCbG9iKHN0cmVhbSkpIHtcbiAgICAgICAgcmV0dXJuIHNwbGl0V2ViU3RyZWFtKHN0cmVhbSk7XG4gICAgfVxuICAgIGNvbnN0IHN0cmVhbTEgPSBuZXcgUGFzc1Rocm91Z2goKTtcbiAgICBjb25zdCBzdHJlYW0yID0gbmV3IFBhc3NUaHJvdWdoKCk7XG4gICAgc3RyZWFtLnBpcGUoc3RyZWFtMSk7XG4gICAgc3RyZWFtLnBpcGUoc3RyZWFtMik7XG4gICAgcmV0dXJuIFtzdHJlYW0xLCBzdHJlYW0yXTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/splitStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/stream-type-check.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/stream-type-check.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBlob: () => (/* binding */ isBlob),\n/* harmony export */   isReadableStream: () => (/* binding */ isReadableStream)\n/* harmony export */ });\nconst isReadableStream = (stream) => typeof ReadableStream === \"function\" &&\n    (stream?.constructor?.name === ReadableStream.name || stream instanceof ReadableStream);\nconst isBlob = (blob) => {\n    return typeof Blob === \"function\" && (blob?.constructor?.name === Blob.name || blob instanceof Blob);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLXN0cmVhbUA0LjIuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLXN0cmVhbS9kaXN0LWVzL3N0cmVhbS10eXBlLWNoZWNrLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1zdHJlYW1ANC4yLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1zdHJlYW0vZGlzdC1lcy9zdHJlYW0tdHlwZS1jaGVjay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgaXNSZWFkYWJsZVN0cmVhbSA9IChzdHJlYW0pID0+IHR5cGVvZiBSZWFkYWJsZVN0cmVhbSA9PT0gXCJmdW5jdGlvblwiICYmXG4gICAgKHN0cmVhbT8uY29uc3RydWN0b3I/Lm5hbWUgPT09IFJlYWRhYmxlU3RyZWFtLm5hbWUgfHwgc3RyZWFtIGluc3RhbmNlb2YgUmVhZGFibGVTdHJlYW0pO1xuZXhwb3J0IGNvbnN0IGlzQmxvYiA9IChibG9iKSA9PiB7XG4gICAgcmV0dXJuIHR5cGVvZiBCbG9iID09PSBcImZ1bmN0aW9uXCIgJiYgKGJsb2I/LmNvbnN0cnVjdG9yPy5uYW1lID09PSBCbG9iLm5hbWUgfHwgYmxvYiBpbnN0YW5jZW9mIEJsb2IpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-stream@4.2.2/node_modules/@smithy/util-stream/dist-es/stream-type-check.js\n");

/***/ })

};
;