/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__";
exports.ids = ["vendor-chunks/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/chat_models.cjs":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/chat_models.cjs ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ChatBedrockConverse = void 0;\nconst messages_1 = __webpack_require__(/*! @langchain/core/messages */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/messages.cjs\");\nconst chat_models_1 = __webpack_require__(/*! @langchain/core/language_models/chat_models */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/language_models/chat_models.cjs\");\nconst client_bedrock_runtime_1 = __webpack_require__(/*! @aws-sdk/client-bedrock-runtime */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/index.js\");\nconst outputs_1 = __webpack_require__(/*! @langchain/core/outputs */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/outputs.cjs\");\nconst env_1 = __webpack_require__(/*! @langchain/core/utils/env */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/env.cjs\");\nconst credential_provider_node_1 = __webpack_require__(/*! @aws-sdk/credential-provider-node */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-node@3.830.0/node_modules/@aws-sdk/credential-provider-node/dist-es/index.js\");\nconst runnables_1 = __webpack_require__(/*! @langchain/core/runnables */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/runnables.cjs\");\nconst types_1 = __webpack_require__(/*! @langchain/core/utils/types */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/types.cjs\");\nconst json_schema_1 = __webpack_require__(/*! @langchain/core/utils/json_schema */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/json_schema.cjs\");\nconst common_js_1 = __webpack_require__(/*! ./common.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/common.cjs\");\n/**\n * AWS Bedrock Converse chat model integration.\n *\n * Setup:\n * Install `@langchain/aws` and set the following environment variables:\n *\n * ```bash\n * npm install @langchain/aws\n * export BEDROCK_AWS_REGION=\"your-aws-region\"\n * export BEDROCK_AWS_SECRET_ACCESS_KEY=\"your-aws-secret-access-key\"\n * export BEDROCK_AWS_ACCESS_KEY_ID=\"your-aws-access-key-id\"\n * ```\n *\n * ## [Constructor args](https://api.js.langchain.com/classes/langchain_aws.ChatBedrockConverse.html#constructor)\n *\n * ## [Runtime args](https://api.js.langchain.com/interfaces/langchain_aws.ChatBedrockConverseCallOptions.html)\n *\n * Runtime args can be passed as the second argument to any of the base runnable methods `.invoke`. `.stream`, `.batch`, etc.\n * They can also be passed via `.withConfig`, or the second arg in `.bindTools`, like shown in the examples below:\n *\n * ```typescript\n * // When calling `.withConfig`, call options should be passed via the first argument\n * const llmWithArgsBound = llm.withConfig({\n *   stop: [\"\\n\"],\n *   tools: [...],\n * });\n *\n * // When calling `.bindTools`, call options should be passed via the second argument\n * const llmWithTools = llm.bindTools(\n *   [...],\n *   {\n *     stop: [\"\\n\"],\n *   }\n * );\n * ```\n *\n * ## Examples\n *\n * <details open>\n * <summary><strong>Instantiate</strong></summary>\n *\n * ```typescript\n * import { ChatBedrockConverse } from '@langchain/aws';\n *\n * const llm = new ChatBedrockConverse({\n *   model: \"anthropic.claude-3-5-sonnet-20240620-v1:0\",\n *   temperature: 0,\n *   maxTokens: undefined,\n *   timeout: undefined,\n *   maxRetries: 2,\n *   region: process.env.BEDROCK_AWS_REGION,\n *   credentials: {\n *     secretAccessKey: process.env.BEDROCK_AWS_SECRET_ACCESS_KEY!,\n *     accessKeyId: process.env.BEDROCK_AWS_ACCESS_KEY_ID!,\n *   },\n *   // other params...\n * });\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Invoking</strong></summary>\n *\n * ```typescript\n * const input = `Translate \"I love programming\" into French.`;\n *\n * // Models also accept a list of chat messages or a formatted prompt\n * const result = await llm.invoke(input);\n * console.log(result);\n * ```\n *\n * ```txt\n * AIMessage {\n *   \"id\": \"81a27f7a-550c-473d-8307-c2fbb9c74956\",\n *   \"content\": \"Here's the translation to French:\\n\\nJ'adore la programmation.\",\n *   \"response_metadata\": {\n *     \"$metadata\": {\n *       \"httpStatusCode\": 200,\n *       \"requestId\": \"81a27f7a-550c-473d-8307-c2fbb9c74956\",\n *       \"attempts\": 1,\n *       \"totalRetryDelay\": 0\n *     },\n *     \"metrics\": {\n *       \"latencyMs\": 1109\n *     },\n *     \"stopReason\": \"end_turn\",\n *     \"usage\": {\n *       \"inputTokens\": 25,\n *       \"outputTokens\": 19,\n *       \"totalTokens\": 44\n *     }\n *   },\n *   \"usage_metadata\": {\n *     \"input_tokens\": 25,\n *     \"output_tokens\": 19,\n *     \"total_tokens\": 44\n *   }\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Streaming Chunks</strong></summary>\n *\n * ```typescript\n * for await (const chunk of await llm.stream(input)) {\n *   console.log(chunk);\n * }\n * ```\n *\n * ```txt\n * AIMessageChunk {\n *   \"content\": \"\"\n *   \"response_metadata\": {\n *     \"messageStart\": {\n *       \"p\": \"abcdefghijk\",\n *       \"role\": \"assistant\"\n *     }\n *   }\n * }\n * AIMessageChunk {\n *   \"content\": \"Here\"\n * }\n * AIMessageChunk {\n *   \"content\": \"'s\"\n * }\n * AIMessageChunk {\n *   \"content\": \" the translation\"\n * }\n * AIMessageChunk {\n *   \"content\": \" to\"\n * }\n * AIMessageChunk {\n *   \"content\": \" French:\\n\\nJ\"\n * }\n * AIMessageChunk {\n *   \"content\": \"'adore la\"\n * }\n * AIMessageChunk {\n *   \"content\": \" programmation.\"\n * }\n * AIMessageChunk {\n *   \"content\": \"\"\n *   \"response_metadata\": {\n *     \"contentBlockStop\": {\n *       \"contentBlockIndex\": 0,\n *       \"p\": \"abcdefghijk\"\n *     }\n *   }\n * }\n * AIMessageChunk {\n *   \"content\": \"\"\n *   \"response_metadata\": {\n *     \"messageStop\": {\n *       \"stopReason\": \"end_turn\"\n *     }\n *   }\n * }\n * AIMessageChunk {\n *   \"content\": \"\"\n *   \"response_metadata\": {\n *     \"metadata\": {\n *       \"metrics\": {\n *         \"latencyMs\": 838\n *       },\n *       \"p\": \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123\",\n *       \"usage\": {\n *         \"inputTokens\": 25,\n *         \"outputTokens\": 19,\n *         \"totalTokens\": 44\n *       }\n *     }\n *   }\n *   \"usage_metadata\": {\n *     \"input_tokens\": 25,\n *     \"output_tokens\": 19,\n *     \"total_tokens\": 44\n *   }\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Aggregate Streamed Chunks</strong></summary>\n *\n * ```typescript\n * import { AIMessageChunk } from '@langchain/core/messages';\n * import { concat } from '@langchain/core/utils/stream';\n *\n * const stream = await llm.stream(input);\n * let full: AIMessageChunk | undefined;\n * for await (const chunk of stream) {\n *   full = !full ? chunk : concat(full, chunk);\n * }\n * console.log(full);\n * ```\n *\n * ```txt\n * AIMessageChunk {\n *   \"content\": \"Here's the translation to French:\\n\\nJ'adore la programmation.\",\n *   \"response_metadata\": {\n *     \"messageStart\": {\n *       \"p\": \"ab\",\n *       \"role\": \"assistant\"\n *     },\n *     \"contentBlockStop\": {\n *       \"contentBlockIndex\": 0,\n *       \"p\": \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJK\"\n *     },\n *     \"messageStop\": {\n *       \"stopReason\": \"end_turn\"\n *     },\n *     \"metadata\": {\n *       \"metrics\": {\n *         \"latencyMs\": 838\n *       },\n *       \"p\": \"abcdefghijklmnopqrstuvwxyz\",\n *       \"usage\": {\n *         \"inputTokens\": 25,\n *         \"outputTokens\": 19,\n *         \"totalTokens\": 44\n *       }\n *     }\n *   },\n *   \"usage_metadata\": {\n *     \"input_tokens\": 25,\n *     \"output_tokens\": 19,\n *     \"total_tokens\": 44\n *   }\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Bind tools</strong></summary>\n *\n * ```typescript\n * import { z } from 'zod';\n *\n * const GetWeather = {\n *   name: \"GetWeather\",\n *   description: \"Get the current weather in a given location\",\n *   schema: z.object({\n *     location: z.string().describe(\"The city and state, e.g. San Francisco, CA\")\n *   }),\n * }\n *\n * const GetPopulation = {\n *   name: \"GetPopulation\",\n *   description: \"Get the current population in a given location\",\n *   schema: z.object({\n *     location: z.string().describe(\"The city and state, e.g. San Francisco, CA\")\n *   }),\n * }\n *\n * const llmWithTools = llm.bindTools(\n *   [GetWeather, GetPopulation],\n *   {\n *     // strict: true  // enforce tool args schema is respected\n *   }\n * );\n * const aiMsg = await llmWithTools.invoke(\n *   \"Which city is hotter today and which is bigger: LA or NY?\"\n * );\n * console.log(aiMsg.tool_calls);\n * ```\n *\n * ```txt\n * [\n *   {\n *     id: 'tooluse_hIaiqfweRtSiJyi6J4naJA',\n *     name: 'GetWeather',\n *     args: { location: 'Los Angeles, CA' },\n *     type: 'tool_call'\n *   },\n *   {\n *     id: 'tooluse_nOS8B0UlTd2FdpH4MSHw9w',\n *     name: 'GetWeather',\n *     args: { location: 'New York, NY' },\n *     type: 'tool_call'\n *   },\n *   {\n *     id: 'tooluse_XxMpZiETQ5aVS5opVDyIaw',\n *     name: 'GetPopulation',\n *     args: { location: 'Los Angeles, CA' },\n *     type: 'tool_call'\n *   },\n *   {\n *     id: 'tooluse_GpYvAfldT2aR8VQfH-p4PQ',\n *     name: 'GetPopulation',\n *     args: { location: 'New York, NY' },\n *     type: 'tool_call'\n *   }\n * ]\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Structured Output</strong></summary>\n *\n * ```typescript\n * import { z } from 'zod';\n *\n * const Joke = z.object({\n *   setup: z.string().describe(\"The setup of the joke\"),\n *   punchline: z.string().describe(\"The punchline to the joke\"),\n *   rating: z.number().optional().describe(\"How funny the joke is, from 1 to 10\")\n * }).describe('Joke to tell user.');\n *\n * const structuredLlm = llm.withStructuredOutput(Joke, { name: \"Joke\" });\n * const jokeResult = await structuredLlm.invoke(\"Tell me a joke about cats\");\n * console.log(jokeResult);\n * ```\n *\n * ```txt\n * {\n *   setup: \"Why don't cats play poker in the jungle?\",\n *   punchline: 'Too many cheetahs!',\n *   rating: 7\n * }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Multimodal</strong></summary>\n *\n * ```typescript\n * import { HumanMessage } from '@langchain/core/messages';\n *\n * const imageUrl = \"https://example.com/image.jpg\";\n * const imageData = await fetch(imageUrl).then(res => res.arrayBuffer());\n * const base64Image = Buffer.from(imageData).toString('base64');\n *\n * const message = new HumanMessage({\n *   content: [\n *     { type: \"text\", text: \"describe the weather in this image\" },\n *     {\n *       type: \"image_url\",\n *       image_url: { url: `data:image/jpeg;base64,${base64Image}` },\n *     },\n *   ]\n * });\n *\n * const imageDescriptionAiMsg = await llm.invoke([message]);\n * console.log(imageDescriptionAiMsg.content);\n * ```\n *\n * ```txt\n * The weather in this image appears to be clear and pleasant. The sky is a vibrant blue with scattered white clouds, suggesting a sunny day with good visibility. The clouds are light and wispy, indicating fair weather conditions. There's no sign of rain, storm, or any adverse weather patterns. The lush green grass on the rolling hills looks well-watered and healthy, which could indicate recent rainfall or generally favorable weather conditions. Overall, the image depicts a beautiful, calm day with blue skies and sunshine - perfect weather for enjoying the outdoors.\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Usage Metadata</strong></summary>\n *\n * ```typescript\n * const aiMsgForMetadata = await llm.invoke(input);\n * console.log(aiMsgForMetadata.usage_metadata);\n * ```\n *\n * ```txt\n * { input_tokens: 25, output_tokens: 19, total_tokens: 44 }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Stream Usage Metadata</strong></summary>\n *\n * ```typescript\n * const streamForMetadata = await llm.stream(input);\n * let fullForMetadata: AIMessageChunk | undefined;\n * for await (const chunk of streamForMetadata) {\n *   fullForMetadata = !fullForMetadata ? chunk : concat(fullForMetadata, chunk);\n * }\n * console.log(fullForMetadata?.usage_metadata);\n * ```\n *\n * ```txt\n * { input_tokens: 25, output_tokens: 19, total_tokens: 44 }\n * ```\n * </details>\n *\n * <br />\n *\n * <details>\n * <summary><strong>Response Metadata</strong></summary>\n *\n * ```typescript\n * const aiMsgForResponseMetadata = await llm.invoke(input);\n * console.log(aiMsgForResponseMetadata.response_metadata);\n * ```\n *\n * ```txt\n * {\n *   '$metadata': {\n *     httpStatusCode: 200,\n *     requestId: '5de2a2e5-d1dc-4dff-bb02-31361f4107bc',\n *     extendedRequestId: undefined,\n *     cfId: undefined,\n *     attempts: 1,\n *     totalRetryDelay: 0\n *   },\n *   metrics: { latencyMs: 1163 },\n *   stopReason: 'end_turn',\n *   usage: { inputTokens: 25, outputTokens: 19, totalTokens: 44 }\n * }\n * ```\n * </details>\n *\n * <br />\n */\nclass ChatBedrockConverse extends chat_models_1.BaseChatModel {\n    // Used for tracing, replace with the same name as your class\n    static lc_name() {\n        return \"ChatBedrockConverse\";\n    }\n    /**\n     * Replace with any secrets this class passes to `super`.\n     * See {@link ../../langchain-cohere/src/chat_model.ts} for\n     * an example.\n     */\n    get lc_secrets() {\n        return {\n            apiKey: \"API_KEY_NAME\",\n        };\n    }\n    get lc_aliases() {\n        return {\n            apiKey: \"API_KEY_NAME\",\n        };\n    }\n    constructor(fields) {\n        super(fields ?? {});\n        Object.defineProperty(this, \"model\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"anthropic.claude-3-haiku-20240307-v1:0\"\n        });\n        Object.defineProperty(this, \"streaming\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: false\n        });\n        Object.defineProperty(this, \"region\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"temperature\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: undefined\n        });\n        Object.defineProperty(this, \"maxTokens\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: undefined\n        });\n        Object.defineProperty(this, \"endpointHost\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"topP\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"additionalModelRequestFields\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"streamUsage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"guardrailConfig\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"performanceConfig\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"client\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * Which types of `tool_choice` values the model supports.\n         *\n         * Inferred if not specified. Inferred as ['auto', 'any', 'tool'] if a 'claude-3'\n         * model is used, ['auto', 'any'] if a 'mistral-large' model is used, empty otherwise.\n         */\n        Object.defineProperty(this, \"supportsToolChoiceValues\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        const { profile, filepath, configFilepath, ignoreCache, mfaCodeProvider, roleAssumer, roleArn, webIdentityTokenFile, roleAssumerWithWebIdentity, ...rest } = fields ?? {};\n        const credentials = rest?.credentials ??\n            (0, credential_provider_node_1.defaultProvider)({\n                profile,\n                filepath,\n                configFilepath,\n                ignoreCache,\n                mfaCodeProvider,\n                roleAssumer,\n                roleArn,\n                webIdentityTokenFile,\n                roleAssumerWithWebIdentity,\n            });\n        const region = rest?.region ?? (0, env_1.getEnvironmentVariable)(\"AWS_DEFAULT_REGION\");\n        if (!region) {\n            throw new Error(\"Please set the AWS_DEFAULT_REGION environment variable or pass it to the constructor as the region field.\");\n        }\n        this.client =\n            fields?.client ??\n                new client_bedrock_runtime_1.BedrockRuntimeClient({\n                    region,\n                    credentials,\n                    endpoint: rest.endpointHost\n                        ? `https://${rest.endpointHost}`\n                        : undefined,\n                });\n        this.region = region;\n        this.model = rest?.model ?? this.model;\n        this.streaming = rest?.streaming ?? this.streaming;\n        this.temperature = rest?.temperature;\n        this.maxTokens = rest?.maxTokens;\n        this.endpointHost = rest?.endpointHost;\n        this.topP = rest?.topP;\n        this.additionalModelRequestFields = rest?.additionalModelRequestFields;\n        this.streamUsage = rest?.streamUsage ?? this.streamUsage;\n        this.guardrailConfig = rest?.guardrailConfig;\n        this.performanceConfig = rest?.performanceConfig;\n        if (rest?.supportsToolChoiceValues === undefined) {\n            if (this.model.includes(\"claude-3\")) {\n                this.supportsToolChoiceValues = [\"auto\", \"any\", \"tool\"];\n            }\n            else if (this.model.includes(\"mistral-large\")) {\n                this.supportsToolChoiceValues = [\"auto\", \"any\"];\n            }\n            else {\n                this.supportsToolChoiceValues = undefined;\n            }\n        }\n        else {\n            this.supportsToolChoiceValues = rest.supportsToolChoiceValues;\n        }\n    }\n    getLsParams(options) {\n        const params = this.invocationParams(options);\n        return {\n            ls_provider: \"amazon_bedrock\",\n            ls_model_name: this.model,\n            ls_model_type: \"chat\",\n            ls_temperature: params.inferenceConfig?.temperature ?? this.temperature,\n            ls_max_tokens: params.inferenceConfig?.maxTokens ?? undefined,\n            ls_stop: options.stop,\n        };\n    }\n    bindTools(tools, kwargs) {\n        return this.withConfig({\n            tools: (0, common_js_1.convertToConverseTools)(tools),\n            ...kwargs,\n        });\n    }\n    // Replace\n    _llmType() {\n        return \"chat_bedrock_converse\";\n    }\n    invocationParams(options) {\n        let toolConfig;\n        if (options?.tools && options.tools.length) {\n            const tools = (0, common_js_1.convertToConverseTools)(options.tools);\n            toolConfig = {\n                tools,\n                toolChoice: options.tool_choice\n                    ? (0, common_js_1.convertToBedrockToolChoice)(options.tool_choice, tools, {\n                        model: this.model,\n                        supportsToolChoiceValues: this.supportsToolChoiceValues,\n                    })\n                    : undefined,\n            };\n        }\n        return {\n            inferenceConfig: {\n                maxTokens: this.maxTokens,\n                temperature: this.temperature,\n                topP: this.topP,\n                stopSequences: options?.stop,\n            },\n            toolConfig,\n            additionalModelRequestFields: this.additionalModelRequestFields ??\n                options?.additionalModelRequestFields,\n            guardrailConfig: options?.guardrailConfig,\n            performanceConfig: options?.performanceConfig,\n        };\n    }\n    async _generate(messages, options, runManager) {\n        if (this.streaming) {\n            const stream = this._streamResponseChunks(messages, options, runManager);\n            let finalResult;\n            for await (const chunk of stream) {\n                if (finalResult === undefined) {\n                    finalResult = chunk;\n                }\n                else {\n                    finalResult = finalResult.concat(chunk);\n                }\n            }\n            if (finalResult === undefined) {\n                throw new Error(\"Could not parse final output from Bedrock streaming call.\");\n            }\n            return {\n                generations: [finalResult],\n                llmOutput: finalResult.generationInfo,\n            };\n        }\n        return this._generateNonStreaming(messages, options, runManager);\n    }\n    async _generateNonStreaming(messages, options, _runManager) {\n        const { converseMessages, converseSystem } = (0, common_js_1.convertToConverseMessages)(messages);\n        const params = this.invocationParams(options);\n        const command = new client_bedrock_runtime_1.ConverseCommand({\n            modelId: this.model,\n            messages: converseMessages,\n            system: converseSystem,\n            ...params,\n        });\n        const response = await this.client.send(command, {\n            abortSignal: options.signal,\n        });\n        const { output, ...responseMetadata } = response;\n        if (!output?.message) {\n            throw new Error(\"No message found in Bedrock response.\");\n        }\n        const message = (0, common_js_1.convertConverseMessageToLangChainMessage)(output.message, responseMetadata);\n        return {\n            generations: [\n                {\n                    text: typeof message.content === \"string\" ? message.content : \"\",\n                    message,\n                },\n            ],\n        };\n    }\n    async *_streamResponseChunks(messages, options, runManager) {\n        const { converseMessages, converseSystem } = (0, common_js_1.convertToConverseMessages)(messages);\n        const params = this.invocationParams(options);\n        let { streamUsage } = this;\n        if (options.streamUsage !== undefined) {\n            streamUsage = options.streamUsage;\n        }\n        const command = new client_bedrock_runtime_1.ConverseStreamCommand({\n            modelId: this.model,\n            messages: converseMessages,\n            system: converseSystem,\n            ...params,\n        });\n        const response = await this.client.send(command, {\n            abortSignal: options.signal,\n        });\n        if (response.stream) {\n            for await (const chunk of response.stream) {\n                if (chunk.contentBlockStart) {\n                    yield (0, common_js_1.handleConverseStreamContentBlockStart)(chunk.contentBlockStart);\n                }\n                else if (chunk.contentBlockDelta) {\n                    const textChatGeneration = (0, common_js_1.handleConverseStreamContentBlockDelta)(chunk.contentBlockDelta);\n                    yield textChatGeneration;\n                    await runManager?.handleLLMNewToken(textChatGeneration.text, undefined, undefined, undefined, undefined, {\n                        chunk: textChatGeneration,\n                    });\n                }\n                else if (chunk.metadata) {\n                    yield (0, common_js_1.handleConverseStreamMetadata)(chunk.metadata, {\n                        streamUsage,\n                    });\n                }\n                else {\n                    yield new outputs_1.ChatGenerationChunk({\n                        text: \"\",\n                        message: new messages_1.AIMessageChunk({\n                            content: \"\",\n                            response_metadata: chunk,\n                        }),\n                    });\n                }\n            }\n        }\n    }\n    withStructuredOutput(outputSchema, config) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const schema = outputSchema;\n        const name = config?.name;\n        const description = (0, types_1.getSchemaDescription)(schema) ?? \"A function available to call.\";\n        const method = config?.method;\n        const includeRaw = config?.includeRaw;\n        if (method === \"jsonMode\") {\n            throw new Error(`ChatBedrockConverse does not support 'jsonMode'.`);\n        }\n        let functionName = name ?? \"extract\";\n        let tools;\n        if ((0, types_1.isInteropZodSchema)(schema)) {\n            tools = [\n                {\n                    type: \"function\",\n                    function: {\n                        name: functionName,\n                        description,\n                        parameters: (0, json_schema_1.toJsonSchema)(schema),\n                    },\n                },\n            ];\n        }\n        else {\n            if (\"name\" in schema) {\n                functionName = schema.name;\n            }\n            tools = [\n                {\n                    type: \"function\",\n                    function: {\n                        name: functionName,\n                        description,\n                        parameters: schema,\n                    },\n                },\n            ];\n        }\n        const supportsToolChoiceValues = this.supportsToolChoiceValues ?? [];\n        let toolChoiceObj;\n        if (supportsToolChoiceValues.includes(\"tool\")) {\n            toolChoiceObj = {\n                tool_choice: tools[0].function.name,\n            };\n        }\n        else if (supportsToolChoiceValues.includes(\"any\")) {\n            toolChoiceObj = {\n                tool_choice: \"any\",\n            };\n        }\n        const llm = this.bindTools(tools, toolChoiceObj);\n        const outputParser = runnables_1.RunnableLambda.from((input) => {\n            if (!input.tool_calls || input.tool_calls.length === 0) {\n                throw new Error(\"No tool calls found in the response.\");\n            }\n            const toolCall = input.tool_calls.find((tc) => tc.name === functionName);\n            if (!toolCall) {\n                throw new Error(`No tool call found with name ${functionName}.`);\n            }\n            return toolCall.args;\n        });\n        if (!includeRaw) {\n            return llm.pipe(outputParser).withConfig({\n                runName: \"StructuredOutput\",\n            });\n        }\n        const parserAssign = runnables_1.RunnablePassthrough.assign({\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            parsed: (input, config) => outputParser.invoke(input.raw, config),\n        });\n        const parserNone = runnables_1.RunnablePassthrough.assign({\n            parsed: () => null,\n        });\n        const parsedWithFallback = parserAssign.withFallbacks({\n            fallbacks: [parserNone],\n        });\n        return runnables_1.RunnableSequence.from([\n            {\n                raw: llm,\n            },\n            parsedWithFallback,\n        ]).withConfig({\n            runName: \"StructuredOutputRunnable\",\n        });\n    }\n}\nexports.ChatBedrockConverse = ChatBedrockConverse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/chat_models.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/common.cjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/common.cjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.concatenateLangchainReasoningBlocks = exports.langchainReasoningBlockToBedrockReasoningBlock = exports.bedrockReasoningBlockToLangchainReasoningBlock = exports.bedrockReasoningDeltaToLangchainPartialReasoningBlock = exports.handleConverseStreamMetadata = exports.handleConverseStreamContentBlockStart = exports.handleConverseStreamContentBlockDelta = exports.convertConverseMessageToLangChainMessage = exports.convertToBedrockToolChoice = exports.convertToConverseTools = exports.isBedrockTool = exports.convertToConverseMessages = exports.extractImageInfo = void 0;\nconst messages_1 = __webpack_require__(/*! @langchain/core/messages */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/messages.cjs\");\nconst base_1 = __webpack_require__(/*! @langchain/core/language_models/base */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/language_models/base.cjs\");\nconst function_calling_1 = __webpack_require__(/*! @langchain/core/utils/function_calling */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/function_calling.cjs\");\nconst outputs_1 = __webpack_require__(/*! @langchain/core/outputs */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/outputs.cjs\");\nconst types_1 = __webpack_require__(/*! @langchain/core/utils/types */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/types.cjs\");\nconst json_schema_1 = __webpack_require__(/*! @langchain/core/utils/json_schema */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/json_schema.cjs\");\nconst standardContentBlockConverter = {\n    providerName: \"ChatBedrockConverse\",\n    fromStandardTextBlock(block) {\n        return {\n            text: block.text,\n        };\n    },\n    fromStandardImageBlock(block) {\n        let format;\n        if (block.source_type === \"url\") {\n            const parsedData = (0, messages_1.parseBase64DataUrl)({\n                dataUrl: block.url,\n                asTypedArray: true,\n            });\n            if (parsedData) {\n                const parsedMimeType = (0, messages_1.parseMimeType)(parsedData.mime_type);\n                format = parsedMimeType.type;\n                return {\n                    image: {\n                        format,\n                        source: {\n                            bytes: parsedData.data,\n                        },\n                    },\n                };\n            }\n            else {\n                throw new Error(\"Only base64 data URLs are supported for image blocks with source type 'url' with ChatBedrockConverse.\");\n            }\n        }\n        else if (block.source_type === \"base64\") {\n            if (block.mime_type) {\n                const parsedMimeType = (0, messages_1.parseMimeType)(block.mime_type);\n                format = parsedMimeType.subtype;\n            }\n            if (format && ![\"gif\", \"jpeg\", \"png\", \"webp\"].includes(format)) {\n                throw new Error(`Unsupported image mime type: \"${block.mime_type}\" ChatBedrockConverse only supports \"image/gif\", \"image/jpeg\", \"image/png\", and \"image/webp\" formats.`);\n            }\n            return {\n                image: {\n                    format,\n                    source: {\n                        bytes: Uint8Array.from(atob(block.data), (c) => c.charCodeAt(0)),\n                    },\n                },\n            };\n        }\n        else if (block.source_type === \"id\") {\n            throw new Error(\"Image source type 'id' not supported with ChatBedrockConverse.\");\n        }\n        else {\n            throw new Error(`Unsupported image source type: \"${block.source_type}\" with ChatBedrockConverse.`);\n        }\n    },\n    fromStandardFileBlock(block) {\n        const mimeTypeToDocumentFormat = {\n            \"text/csv\": \"csv\",\n            \"application/msword\": \"doc\",\n            \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": \"docx\",\n            \"text/html\": \"html\",\n            \"text/markdown\": \"md\",\n            \"application/pdf\": \"pdf\",\n            \"text/plain\": \"txt\",\n            \"application/vnd.ms-excel\": \"xls\",\n            \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\": \"xlsx\",\n        };\n        const name = (block.metadata?.name ??\n            block.metadata?.filename ??\n            block.metadata?.title);\n        if (block.source_type === \"text\") {\n            return {\n                document: {\n                    name,\n                    format: \"txt\",\n                    source: {\n                        bytes: new TextEncoder().encode(block.text),\n                    },\n                },\n            };\n        }\n        if (block.source_type === \"url\") {\n            const parsedData = (0, messages_1.parseBase64DataUrl)({\n                dataUrl: block.url,\n                asTypedArray: true,\n            });\n            if (parsedData) {\n                const parsedMimeType = (0, messages_1.parseMimeType)(parsedData.mime_type ?? block.mime_type);\n                const mimeType = `${parsedMimeType.type}/${parsedMimeType.subtype}`;\n                const format = mimeTypeToDocumentFormat[mimeType];\n                return {\n                    document: {\n                        name,\n                        format,\n                        source: {\n                            bytes: parsedData.data,\n                        },\n                    },\n                };\n            }\n            throw new Error(\"Only base64 data URLs are supported for file blocks with source type 'url' with ChatBedrockConverse.\");\n        }\n        if (block.source_type === \"base64\") {\n            let format;\n            if (block.mime_type) {\n                const parsedMimeType = (0, messages_1.parseMimeType)(block.mime_type);\n                const mimeType = `${parsedMimeType.type}/${parsedMimeType.subtype}`;\n                format = mimeTypeToDocumentFormat[mimeType];\n                if (format === undefined) {\n                    throw new Error(`Unsupported file mime type: \"${block.mime_type}\" ChatBedrockConverse only supports ${Object.keys(mimeTypeToDocumentFormat).join(\", \")} formats.`);\n                }\n            }\n            return {\n                document: {\n                    name,\n                    format,\n                    source: {\n                        bytes: Uint8Array.from(atob(block.data), (c) => c.charCodeAt(0)),\n                    },\n                },\n            };\n        }\n        if (block.source_type === \"id\") {\n            throw new Error(\"File source type 'id' not supported with ChatBedrockConverse.\");\n        }\n        throw new Error(`Unsupported file source type: \"${block.source_type}\" with ChatBedrockConverse.`);\n    },\n};\nfunction extractImageInfo(base64) {\n    // Extract the format from the base64 string\n    const formatMatch = base64.match(/^data:image\\/(\\w+);base64,/);\n    let format;\n    if (formatMatch) {\n        const extractedFormat = formatMatch[1].toLowerCase();\n        if ([\"gif\", \"jpeg\", \"png\", \"webp\"].includes(extractedFormat)) {\n            format = extractedFormat;\n        }\n    }\n    // Remove the data URL prefix if present\n    const base64Data = base64.replace(/^data:image\\/\\w+;base64,/, \"\");\n    // Convert base64 to Uint8Array\n    const binaryString = atob(base64Data);\n    const bytes = new Uint8Array(binaryString.length);\n    for (let i = 0; i < binaryString.length; i += 1) {\n        bytes[i] = binaryString.charCodeAt(i);\n    }\n    return {\n        image: {\n            format,\n            source: {\n                bytes,\n            },\n        },\n    };\n}\nexports.extractImageInfo = extractImageInfo;\nfunction convertLangChainContentBlockToConverseContentBlock({ block, onUnknown = \"throw\", }) {\n    if (typeof block === \"string\") {\n        return { text: block };\n    }\n    if ((0, messages_1.isDataContentBlock)(block)) {\n        return (0, messages_1.convertToProviderContentBlock)(block, standardContentBlockConverter);\n    }\n    if (block.type === \"text\") {\n        return { text: block.text };\n    }\n    if (block.type === \"image_url\") {\n        return extractImageInfo(typeof block.image_url === \"string\"\n            ? block.image_url\n            : block.image_url.url);\n    }\n    if (block.type === \"document\" && block.document !== undefined) {\n        return {\n            document: block.document,\n        };\n    }\n    if (block.type === \"image\" && block.image !== undefined) {\n        return {\n            image: block.image,\n        };\n    }\n    if (onUnknown === \"throw\") {\n        throw new Error(`Unsupported content block type: ${block.type}`);\n    }\n    else {\n        return block;\n    }\n}\nfunction convertSystemMessageToConverseMessage(msg) {\n    if (typeof msg.content === \"string\") {\n        return { text: msg.content };\n    }\n    else if (msg.content.length === 1 && msg.content[0].type === \"text\") {\n        return { text: msg.content[0].text };\n    }\n    throw new Error(\"System message content must be either a string, or a content array containing a single text object.\");\n}\nfunction convertAIMessageToConverseMessage(msg) {\n    const assistantMsg = {\n        role: \"assistant\",\n        content: [],\n    };\n    if (typeof msg.content === \"string\" && msg.content !== \"\") {\n        assistantMsg.content?.push({\n            text: msg.content,\n        });\n    }\n    else if (Array.isArray(msg.content)) {\n        const concatenatedBlocks = concatenateLangchainReasoningBlocks(msg.content);\n        const contentBlocks = concatenatedBlocks.map((block) => {\n            if (block.type === \"text\" && block.text !== \"\") {\n                return {\n                    text: block.text,\n                };\n            }\n            else if (block.type === \"reasoning_content\") {\n                return {\n                    reasoningContent: langchainReasoningBlockToBedrockReasoningBlock(block),\n                };\n            }\n            else {\n                const blockValues = Object.fromEntries(Object.entries(block).filter(([key]) => key !== \"type\"));\n                throw new Error(`Unsupported content block type: ${block.type} with content of ${JSON.stringify(blockValues, null, 2)}`);\n            }\n        });\n        assistantMsg.content = [\n            ...(assistantMsg.content ? assistantMsg.content : []),\n            ...contentBlocks,\n        ];\n    }\n    // Important: this must be placed after any reasoning content blocks, else claude models will return an error.\n    if (msg.tool_calls && msg.tool_calls.length) {\n        assistantMsg.content = [\n            ...(assistantMsg.content ? assistantMsg.content : []),\n            ...msg.tool_calls.map((tc) => ({\n                toolUse: {\n                    toolUseId: tc.id,\n                    name: tc.name,\n                    input: tc.args,\n                },\n            })),\n        ];\n    }\n    return assistantMsg;\n}\nfunction convertHumanMessageToConverseMessage(msg) {\n    if (msg.content === \"\") {\n        throw new Error(`Invalid message content: empty string. '${msg._getType()}' must contain non-empty content.`);\n    }\n    const content = Array.isArray(msg.content)\n        ? msg.content.map((c) => convertLangChainContentBlockToConverseContentBlock({\n            block: c,\n            onUnknown: \"throw\",\n        }))\n        : [\n            convertLangChainContentBlockToConverseContentBlock({\n                block: msg.content,\n                onUnknown: \"throw\",\n            }),\n        ];\n    return {\n        role: \"user\",\n        content,\n    };\n}\nfunction convertToolMessageToConverseMessage(msg) {\n    const castMsg = msg;\n    if (typeof castMsg.content === \"string\") {\n        return {\n            // Tool use messages are always from the user\n            role: \"user\",\n            content: [\n                {\n                    toolResult: {\n                        toolUseId: castMsg.tool_call_id,\n                        content: [\n                            {\n                                text: castMsg.content,\n                            },\n                        ],\n                    },\n                },\n            ],\n        };\n    }\n    else {\n        return {\n            // Tool use messages are always from the user\n            role: \"user\",\n            content: [\n                {\n                    toolResult: {\n                        toolUseId: castMsg.tool_call_id,\n                        content: msg.content.map((c) => {\n                            const converted = convertLangChainContentBlockToConverseContentBlock({\n                                block: c,\n                                onUnknown: \"returnUnmodified\",\n                            });\n                            if (converted !== c) {\n                                return converted;\n                            }\n                            return { json: c };\n                        }),\n                    },\n                },\n            ],\n        };\n    }\n}\nfunction convertToConverseMessages(messages) {\n    const converseSystem = messages\n        .filter((msg) => msg._getType() === \"system\")\n        .map((msg) => convertSystemMessageToConverseMessage(msg));\n    const converseMessages = messages\n        .filter((msg) => msg._getType() !== \"system\")\n        .map((msg) => {\n        if (msg._getType() === \"ai\") {\n            return convertAIMessageToConverseMessage(msg);\n        }\n        else if (msg._getType() === \"human\" || msg._getType() === \"generic\") {\n            return convertHumanMessageToConverseMessage(msg);\n        }\n        else if (msg._getType() === \"tool\") {\n            return convertToolMessageToConverseMessage(msg);\n        }\n        else {\n            throw new Error(`Unsupported message type: ${msg._getType()}`);\n        }\n    });\n    // Combine consecutive user tool result messages into a single message\n    const combinedConverseMessages = converseMessages.reduce((acc, curr) => {\n        const lastMessage = acc[acc.length - 1];\n        if (lastMessage &&\n            lastMessage.role === \"user\" &&\n            lastMessage.content?.some((c) => \"toolResult\" in c) &&\n            curr.role === \"user\" &&\n            curr.content?.some((c) => \"toolResult\" in c)) {\n            lastMessage.content = lastMessage.content.concat(curr.content);\n        }\n        else {\n            acc.push(curr);\n        }\n        return acc;\n    }, []);\n    return { converseMessages: combinedConverseMessages, converseSystem };\n}\nexports.convertToConverseMessages = convertToConverseMessages;\nfunction isBedrockTool(tool) {\n    if (typeof tool === \"object\" && tool && \"toolSpec\" in tool) {\n        return true;\n    }\n    return false;\n}\nexports.isBedrockTool = isBedrockTool;\nfunction convertToConverseTools(tools) {\n    if (tools.every(base_1.isOpenAITool)) {\n        return tools.map((tool) => ({\n            toolSpec: {\n                name: tool.function.name,\n                description: tool.function.description,\n                inputSchema: {\n                    json: tool.function.parameters,\n                },\n            },\n        }));\n    }\n    else if (tools.every(function_calling_1.isLangChainTool)) {\n        return tools.map((tool) => ({\n            toolSpec: {\n                name: tool.name,\n                description: tool.description,\n                inputSchema: {\n                    json: ((0, types_1.isInteropZodSchema)(tool.schema)\n                        ? (0, json_schema_1.toJsonSchema)(tool.schema)\n                        : tool.schema),\n                },\n            },\n        }));\n    }\n    else if (tools.every(isBedrockTool)) {\n        return tools;\n    }\n    throw new Error(\"Invalid tools passed. Must be an array of StructuredToolInterface, ToolDefinition, or BedrockTool.\");\n}\nexports.convertToConverseTools = convertToConverseTools;\nfunction convertToBedrockToolChoice(toolChoice, tools, fields) {\n    const supportsToolChoiceValues = fields.supportsToolChoiceValues ?? [];\n    let bedrockToolChoice;\n    if (typeof toolChoice === \"string\") {\n        switch (toolChoice) {\n            case \"any\":\n                bedrockToolChoice = {\n                    any: {},\n                };\n                break;\n            case \"auto\":\n                bedrockToolChoice = {\n                    auto: {},\n                };\n                break;\n            default: {\n                const foundTool = tools.find((tool) => tool.toolSpec?.name === toolChoice);\n                if (!foundTool) {\n                    throw new Error(`Tool with name ${toolChoice} not found in tools list.`);\n                }\n                bedrockToolChoice = {\n                    tool: {\n                        name: toolChoice,\n                    },\n                };\n            }\n        }\n    }\n    else {\n        bedrockToolChoice = toolChoice;\n    }\n    const toolChoiceType = Object.keys(bedrockToolChoice)[0];\n    if (!supportsToolChoiceValues.includes(toolChoiceType)) {\n        let supportedTxt = \"\";\n        if (supportsToolChoiceValues.length) {\n            supportedTxt =\n                `Model ${fields.model} does not currently support 'tool_choice' ` +\n                    `of type ${toolChoiceType}. The following 'tool_choice' types ` +\n                    `are supported: ${supportsToolChoiceValues.join(\", \")}.`;\n        }\n        else {\n            supportedTxt = `Model ${fields.model} does not currently support 'tool_choice'.`;\n        }\n        throw new Error(`${supportedTxt} Please see` +\n            \"https://docs.aws.amazon.com/bedrock/latest/APIReference/API_runtime_ToolChoice.html\" +\n            \"for the latest documentation on models that support tool choice.\");\n    }\n    return bedrockToolChoice;\n}\nexports.convertToBedrockToolChoice = convertToBedrockToolChoice;\nfunction convertConverseMessageToLangChainMessage(message, responseMetadata) {\n    if (!message.content) {\n        throw new Error(\"No message content found in response.\");\n    }\n    if (message.role !== \"assistant\") {\n        throw new Error(`Unsupported message role received in ChatBedrockConverse response: ${message.role}`);\n    }\n    let requestId;\n    if (\"$metadata\" in responseMetadata &&\n        responseMetadata.$metadata &&\n        typeof responseMetadata.$metadata === \"object\" &&\n        \"requestId\" in responseMetadata.$metadata) {\n        requestId = responseMetadata.$metadata.requestId;\n    }\n    let tokenUsage;\n    if (responseMetadata.usage) {\n        const input_tokens = responseMetadata.usage.inputTokens ?? 0;\n        const output_tokens = responseMetadata.usage.outputTokens ?? 0;\n        tokenUsage = {\n            input_tokens,\n            output_tokens,\n            total_tokens: responseMetadata.usage.totalTokens ?? input_tokens + output_tokens,\n        };\n    }\n    if (message.content?.length === 1 &&\n        \"text\" in message.content[0] &&\n        typeof message.content[0].text === \"string\") {\n        return new messages_1.AIMessage({\n            content: message.content[0].text,\n            response_metadata: responseMetadata,\n            usage_metadata: tokenUsage,\n            id: requestId,\n        });\n    }\n    else {\n        const toolCalls = [];\n        const content = [];\n        message.content.forEach((c) => {\n            if (\"toolUse\" in c &&\n                c.toolUse &&\n                c.toolUse.name &&\n                c.toolUse.input &&\n                typeof c.toolUse.input === \"object\") {\n                toolCalls.push({\n                    id: c.toolUse.toolUseId,\n                    name: c.toolUse.name,\n                    args: c.toolUse.input,\n                    type: \"tool_call\",\n                });\n            }\n            else if (\"text\" in c && typeof c.text === \"string\") {\n                content.push({ type: \"text\", text: c.text });\n            }\n            else if (\"reasoningContent\" in c) {\n                content.push(bedrockReasoningBlockToLangchainReasoningBlock(c.reasoningContent));\n            }\n            else {\n                content.push(c);\n            }\n        });\n        return new messages_1.AIMessage({\n            content: content.length ? content : \"\",\n            tool_calls: toolCalls.length ? toolCalls : undefined,\n            response_metadata: responseMetadata,\n            usage_metadata: tokenUsage,\n            id: requestId,\n        });\n    }\n}\nexports.convertConverseMessageToLangChainMessage = convertConverseMessageToLangChainMessage;\nfunction handleConverseStreamContentBlockDelta(contentBlockDelta) {\n    if (!contentBlockDelta.delta) {\n        throw new Error(\"No delta found in content block.\");\n    }\n    if (typeof contentBlockDelta.delta.text === \"string\") {\n        return new outputs_1.ChatGenerationChunk({\n            text: contentBlockDelta.delta.text,\n            message: new messages_1.AIMessageChunk({\n                content: contentBlockDelta.delta.text,\n            }),\n        });\n    }\n    else if (contentBlockDelta.delta.toolUse) {\n        const index = contentBlockDelta.contentBlockIndex;\n        return new outputs_1.ChatGenerationChunk({\n            text: \"\",\n            message: new messages_1.AIMessageChunk({\n                content: \"\",\n                tool_call_chunks: [\n                    {\n                        args: contentBlockDelta.delta.toolUse.input,\n                        index,\n                        type: \"tool_call_chunk\",\n                    },\n                ],\n            }),\n        });\n    }\n    else if (contentBlockDelta.delta.reasoningContent) {\n        return new outputs_1.ChatGenerationChunk({\n            text: \"\",\n            message: new messages_1.AIMessageChunk({\n                content: [\n                    bedrockReasoningDeltaToLangchainPartialReasoningBlock(contentBlockDelta.delta.reasoningContent),\n                ],\n            }),\n        });\n    }\n    else {\n        throw new Error(`Unsupported content block type(s): ${JSON.stringify(contentBlockDelta.delta, null, 2)}`);\n    }\n}\nexports.handleConverseStreamContentBlockDelta = handleConverseStreamContentBlockDelta;\nfunction handleConverseStreamContentBlockStart(contentBlockStart) {\n    const index = contentBlockStart.contentBlockIndex;\n    if (contentBlockStart.start?.toolUse) {\n        return new outputs_1.ChatGenerationChunk({\n            text: \"\",\n            message: new messages_1.AIMessageChunk({\n                content: \"\",\n                tool_call_chunks: [\n                    {\n                        name: contentBlockStart.start.toolUse.name,\n                        id: contentBlockStart.start.toolUse.toolUseId,\n                        index,\n                        type: \"tool_call_chunk\",\n                    },\n                ],\n            }),\n        });\n    }\n    throw new Error(\"Unsupported content block start event.\");\n}\nexports.handleConverseStreamContentBlockStart = handleConverseStreamContentBlockStart;\nfunction handleConverseStreamMetadata(metadata, extra) {\n    const inputTokens = metadata.usage?.inputTokens ?? 0;\n    const outputTokens = metadata.usage?.outputTokens ?? 0;\n    const usage_metadata = {\n        input_tokens: inputTokens,\n        output_tokens: outputTokens,\n        total_tokens: metadata.usage?.totalTokens ?? inputTokens + outputTokens,\n    };\n    return new outputs_1.ChatGenerationChunk({\n        text: \"\",\n        message: new messages_1.AIMessageChunk({\n            content: \"\",\n            usage_metadata: extra.streamUsage ? usage_metadata : undefined,\n            response_metadata: {\n                // Use the same key as returned from the Converse API\n                metadata,\n            },\n        }),\n    });\n}\nexports.handleConverseStreamMetadata = handleConverseStreamMetadata;\nfunction bedrockReasoningDeltaToLangchainPartialReasoningBlock(reasoningContent) {\n    const { text, redactedContent, signature } = reasoningContent;\n    if (typeof text === \"string\") {\n        return {\n            type: \"reasoning_content\",\n            reasoningText: { text },\n        };\n    }\n    if (signature) {\n        return {\n            type: \"reasoning_content\",\n            reasoningText: { signature },\n        };\n    }\n    if (redactedContent) {\n        return {\n            type: \"reasoning_content\",\n            redactedContent: Buffer.from(redactedContent).toString(\"base64\"),\n        };\n    }\n    throw new Error(\"Invalid reasoning content\");\n}\nexports.bedrockReasoningDeltaToLangchainPartialReasoningBlock = bedrockReasoningDeltaToLangchainPartialReasoningBlock;\nfunction bedrockReasoningBlockToLangchainReasoningBlock(reasoningContent) {\n    const { reasoningText, redactedContent } = reasoningContent;\n    if (reasoningText) {\n        return {\n            type: \"reasoning_content\",\n            reasoningText: reasoningText,\n        };\n    }\n    if (redactedContent) {\n        return {\n            type: \"reasoning_content\",\n            redactedContent: Buffer.from(redactedContent).toString(\"base64\"),\n        };\n    }\n    throw new Error(\"Invalid reasoning content\");\n}\nexports.bedrockReasoningBlockToLangchainReasoningBlock = bedrockReasoningBlockToLangchainReasoningBlock;\nfunction langchainReasoningBlockToBedrockReasoningBlock(content) {\n    if (content.type !== \"reasoning_content\") {\n        throw new Error(\"Invalid reasoning content\");\n    }\n    if (\"reasoningText\" in content) {\n        return {\n            reasoningText: content.reasoningText,\n        };\n    }\n    if (\"redactedContent\" in content) {\n        return {\n            redactedContent: Buffer.from(content.redactedContent, \"base64\"),\n        };\n    }\n    throw new Error(\"Invalid reasoning content\");\n}\nexports.langchainReasoningBlockToBedrockReasoningBlock = langchainReasoningBlockToBedrockReasoningBlock;\nfunction concatenateLangchainReasoningBlocks(content) {\n    const concatenatedBlocks = [];\n    let concatenatedBlock = {};\n    for (const block of content) {\n        if (block.type !== \"reasoning_content\") {\n            // if it's some other block type, end the current block, but keep it so we preserve order\n            if (Object.keys(concatenatedBlock).length > 0) {\n                concatenatedBlocks.push(concatenatedBlock);\n                concatenatedBlock = {};\n            }\n            concatenatedBlocks.push(block);\n            continue;\n        }\n        // non-redacted block\n        if (\"reasoningText\" in block && typeof block.reasoningText === \"object\") {\n            if (\"redactedContent\" in concatenatedBlock) {\n                // new type of block, so end the previous one\n                concatenatedBlocks.push(concatenatedBlock);\n                concatenatedBlock = {};\n            }\n            const { text, signature } = block.reasoningText;\n            const { text: prevText, signature: prevSignature } = (\"reasoningText\" in concatenatedBlock\n                ? concatenatedBlock.reasoningText\n                : {});\n            concatenatedBlock = {\n                type: \"reasoning_content\",\n                reasoningText: {\n                    ...(concatenatedBlock\n                        .reasoningText ?? {}),\n                    ...(prevText !== undefined || text !== undefined\n                        ? { text: (prevText ?? \"\") + (text ?? \"\") }\n                        : {}),\n                    ...(prevSignature !== undefined || signature !== undefined\n                        ? { signature: (prevSignature ?? \"\") + (signature ?? \"\") }\n                        : {}),\n                },\n            };\n            // if a partial block chunk has a signature, the next one will begin a new reasoning block.\n            // full blocks always have signatures, so we start one now, anyway\n            if (\"signature\" in block.reasoningText) {\n                concatenatedBlocks.push(concatenatedBlock);\n                concatenatedBlock = {};\n            }\n        }\n        if (\"redactedContent\" in block) {\n            if (\"reasoningText\" in concatenatedBlock) {\n                // New type of block, so end the previous one. We should't really hit\n                // this, as we'll have created a new block upon encountering the\n                // signature above, but better safe than sorry.\n                concatenatedBlocks.push(concatenatedBlock);\n                concatenatedBlock = {};\n            }\n            const { redactedContent } = block;\n            const prevRedactedContent = (\"redactedContent\" in concatenatedBlock\n                ? concatenatedBlock.redactedContent\n                : \"\");\n            concatenatedBlock = {\n                type: \"reasoning_content\",\n                redactedContent: prevRedactedContent + redactedContent,\n            };\n        }\n    }\n    if (Object.keys(concatenatedBlock).length > 0) {\n        concatenatedBlocks.push(concatenatedBlock);\n    }\n    return concatenatedBlocks;\n}\nexports.concatenateLangchainReasoningBlocks = concatenateLangchainReasoningBlocks;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbithd3NAMC4xLjExX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fL25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2F3cy9kaXN0L2NvbW1vbi5janMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsMkNBQTJDLEdBQUcsc0RBQXNELEdBQUcsc0RBQXNELEdBQUcsNkRBQTZELEdBQUcsb0NBQW9DLEdBQUcsNkNBQTZDLEdBQUcsNkNBQTZDLEdBQUcsZ0RBQWdELEdBQUcsa0NBQWtDLEdBQUcsOEJBQThCLEdBQUcscUJBQXFCLEdBQUcsaUNBQWlDLEdBQUcsd0JBQXdCO0FBQ3BqQixtQkFBbUIsbUJBQU8sQ0FBQyxpS0FBMEI7QUFDckQsZUFBZSxtQkFBTyxDQUFDLHlMQUFzQztBQUM3RCwyQkFBMkIsbUJBQU8sQ0FBQyw2TEFBd0M7QUFDM0Usa0JBQWtCLG1CQUFPLENBQUMsK0pBQXlCO0FBQ25ELGdCQUFnQixtQkFBTyxDQUFDLHVLQUE2QjtBQUNyRCxzQkFBc0IsbUJBQU8sQ0FBQyxtTEFBbUM7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBaUUsZ0JBQWdCO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0RBQStELGtCQUFrQjtBQUNqRjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQixpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxvQ0FBb0Msb0JBQW9CLEdBQUcsdUJBQXVCO0FBQ2xGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLG9CQUFvQixHQUFHLHVCQUF1QjtBQUNsRjtBQUNBO0FBQ0Esb0VBQW9FLGdCQUFnQixzQ0FBc0Msa0RBQWtEO0FBQzVLO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwREFBMEQsa0JBQWtCO0FBQzVFLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSx5REFBeUQ7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdEQUF3RDtBQUN4RDtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IseUJBQXlCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0Esd0JBQXdCO0FBQ3hCLDhEQUE4RCw2QkFBNkI7QUFDM0Y7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJELFdBQVc7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUVBQW1FLFlBQVksa0JBQWtCLHFDQUFxQztBQUN0STtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtRUFBbUUsZUFBZTtBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0EscUJBQXFCO0FBQ3JCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDLHlCQUF5QjtBQUN6QixxQkFBcUI7QUFDckIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RCxlQUFlO0FBQ3hFO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsYUFBYTtBQUNiO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRCxZQUFZO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixjQUFjO0FBQ3ZDLCtCQUErQixlQUFlO0FBQzlDLHNDQUFzQyxvQ0FBb0M7QUFDMUU7QUFDQTtBQUNBLG9DQUFvQyxjQUFjO0FBQ2xEO0FBQ0EsMkJBQTJCLGNBQWM7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEZBQThGLGFBQWE7QUFDM0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLCtCQUErQiw0QkFBNEI7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxnREFBZ0Q7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQSw4REFBOEQsaURBQWlEO0FBQy9HO0FBQ0E7QUFDQSw2Q0FBNkM7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0Esb0NBQW9DO0FBQ3BDO0FBQ0EsWUFBWSxtQ0FBbUM7QUFDL0M7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLE1BQU07QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixXQUFXO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkRBQTZEO0FBQzdEO0FBQ0EsWUFBWSxpQ0FBaUM7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRDtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRDtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGtCQUFrQjtBQUN0QyxvQkFBb0IsMkNBQTJDO0FBQy9EO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDO0FBQzVDO0FBQ0EsNEJBQTRCO0FBQzVCLDRCQUE0QjtBQUM1QjtBQUNBLDRCQUE0QjtBQUM1Qiw0QkFBNEI7QUFDNUIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGtCQUFrQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbithd3NAMC4xLjExX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fL25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2F3cy9kaXN0L2NvbW1vbi5janMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmNvbmNhdGVuYXRlTGFuZ2NoYWluUmVhc29uaW5nQmxvY2tzID0gZXhwb3J0cy5sYW5nY2hhaW5SZWFzb25pbmdCbG9ja1RvQmVkcm9ja1JlYXNvbmluZ0Jsb2NrID0gZXhwb3J0cy5iZWRyb2NrUmVhc29uaW5nQmxvY2tUb0xhbmdjaGFpblJlYXNvbmluZ0Jsb2NrID0gZXhwb3J0cy5iZWRyb2NrUmVhc29uaW5nRGVsdGFUb0xhbmdjaGFpblBhcnRpYWxSZWFzb25pbmdCbG9jayA9IGV4cG9ydHMuaGFuZGxlQ29udmVyc2VTdHJlYW1NZXRhZGF0YSA9IGV4cG9ydHMuaGFuZGxlQ29udmVyc2VTdHJlYW1Db250ZW50QmxvY2tTdGFydCA9IGV4cG9ydHMuaGFuZGxlQ29udmVyc2VTdHJlYW1Db250ZW50QmxvY2tEZWx0YSA9IGV4cG9ydHMuY29udmVydENvbnZlcnNlTWVzc2FnZVRvTGFuZ0NoYWluTWVzc2FnZSA9IGV4cG9ydHMuY29udmVydFRvQmVkcm9ja1Rvb2xDaG9pY2UgPSBleHBvcnRzLmNvbnZlcnRUb0NvbnZlcnNlVG9vbHMgPSBleHBvcnRzLmlzQmVkcm9ja1Rvb2wgPSBleHBvcnRzLmNvbnZlcnRUb0NvbnZlcnNlTWVzc2FnZXMgPSBleHBvcnRzLmV4dHJhY3RJbWFnZUluZm8gPSB2b2lkIDA7XG5jb25zdCBtZXNzYWdlc18xID0gcmVxdWlyZShcIkBsYW5nY2hhaW4vY29yZS9tZXNzYWdlc1wiKTtcbmNvbnN0IGJhc2VfMSA9IHJlcXVpcmUoXCJAbGFuZ2NoYWluL2NvcmUvbGFuZ3VhZ2VfbW9kZWxzL2Jhc2VcIik7XG5jb25zdCBmdW5jdGlvbl9jYWxsaW5nXzEgPSByZXF1aXJlKFwiQGxhbmdjaGFpbi9jb3JlL3V0aWxzL2Z1bmN0aW9uX2NhbGxpbmdcIik7XG5jb25zdCBvdXRwdXRzXzEgPSByZXF1aXJlKFwiQGxhbmdjaGFpbi9jb3JlL291dHB1dHNcIik7XG5jb25zdCB0eXBlc18xID0gcmVxdWlyZShcIkBsYW5nY2hhaW4vY29yZS91dGlscy90eXBlc1wiKTtcbmNvbnN0IGpzb25fc2NoZW1hXzEgPSByZXF1aXJlKFwiQGxhbmdjaGFpbi9jb3JlL3V0aWxzL2pzb25fc2NoZW1hXCIpO1xuY29uc3Qgc3RhbmRhcmRDb250ZW50QmxvY2tDb252ZXJ0ZXIgPSB7XG4gICAgcHJvdmlkZXJOYW1lOiBcIkNoYXRCZWRyb2NrQ29udmVyc2VcIixcbiAgICBmcm9tU3RhbmRhcmRUZXh0QmxvY2soYmxvY2spIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHRleHQ6IGJsb2NrLnRleHQsXG4gICAgICAgIH07XG4gICAgfSxcbiAgICBmcm9tU3RhbmRhcmRJbWFnZUJsb2NrKGJsb2NrKSB7XG4gICAgICAgIGxldCBmb3JtYXQ7XG4gICAgICAgIGlmIChibG9jay5zb3VyY2VfdHlwZSA9PT0gXCJ1cmxcIikge1xuICAgICAgICAgICAgY29uc3QgcGFyc2VkRGF0YSA9ICgwLCBtZXNzYWdlc18xLnBhcnNlQmFzZTY0RGF0YVVybCkoe1xuICAgICAgICAgICAgICAgIGRhdGFVcmw6IGJsb2NrLnVybCxcbiAgICAgICAgICAgICAgICBhc1R5cGVkQXJyYXk6IHRydWUsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmIChwYXJzZWREYXRhKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcGFyc2VkTWltZVR5cGUgPSAoMCwgbWVzc2FnZXNfMS5wYXJzZU1pbWVUeXBlKShwYXJzZWREYXRhLm1pbWVfdHlwZSk7XG4gICAgICAgICAgICAgICAgZm9ybWF0ID0gcGFyc2VkTWltZVR5cGUudHlwZTtcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICBpbWFnZToge1xuICAgICAgICAgICAgICAgICAgICAgICAgZm9ybWF0LFxuICAgICAgICAgICAgICAgICAgICAgICAgc291cmNlOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYnl0ZXM6IHBhcnNlZERhdGEuZGF0YSxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIk9ubHkgYmFzZTY0IGRhdGEgVVJMcyBhcmUgc3VwcG9ydGVkIGZvciBpbWFnZSBibG9ja3Mgd2l0aCBzb3VyY2UgdHlwZSAndXJsJyB3aXRoIENoYXRCZWRyb2NrQ29udmVyc2UuXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKGJsb2NrLnNvdXJjZV90eXBlID09PSBcImJhc2U2NFwiKSB7XG4gICAgICAgICAgICBpZiAoYmxvY2subWltZV90eXBlKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcGFyc2VkTWltZVR5cGUgPSAoMCwgbWVzc2FnZXNfMS5wYXJzZU1pbWVUeXBlKShibG9jay5taW1lX3R5cGUpO1xuICAgICAgICAgICAgICAgIGZvcm1hdCA9IHBhcnNlZE1pbWVUeXBlLnN1YnR5cGU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoZm9ybWF0ICYmICFbXCJnaWZcIiwgXCJqcGVnXCIsIFwicG5nXCIsIFwid2VicFwiXS5pbmNsdWRlcyhmb3JtYXQpKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBVbnN1cHBvcnRlZCBpbWFnZSBtaW1lIHR5cGU6IFwiJHtibG9jay5taW1lX3R5cGV9XCIgQ2hhdEJlZHJvY2tDb252ZXJzZSBvbmx5IHN1cHBvcnRzIFwiaW1hZ2UvZ2lmXCIsIFwiaW1hZ2UvanBlZ1wiLCBcImltYWdlL3BuZ1wiLCBhbmQgXCJpbWFnZS93ZWJwXCIgZm9ybWF0cy5gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgaW1hZ2U6IHtcbiAgICAgICAgICAgICAgICAgICAgZm9ybWF0LFxuICAgICAgICAgICAgICAgICAgICBzb3VyY2U6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGJ5dGVzOiBVaW50OEFycmF5LmZyb20oYXRvYihibG9jay5kYXRhKSwgKGMpID0+IGMuY2hhckNvZGVBdCgwKSksXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYmxvY2suc291cmNlX3R5cGUgPT09IFwiaWRcIikge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSW1hZ2Ugc291cmNlIHR5cGUgJ2lkJyBub3Qgc3VwcG9ydGVkIHdpdGggQ2hhdEJlZHJvY2tDb252ZXJzZS5cIik7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuc3VwcG9ydGVkIGltYWdlIHNvdXJjZSB0eXBlOiBcIiR7YmxvY2suc291cmNlX3R5cGV9XCIgd2l0aCBDaGF0QmVkcm9ja0NvbnZlcnNlLmApO1xuICAgICAgICB9XG4gICAgfSxcbiAgICBmcm9tU3RhbmRhcmRGaWxlQmxvY2soYmxvY2spIHtcbiAgICAgICAgY29uc3QgbWltZVR5cGVUb0RvY3VtZW50Rm9ybWF0ID0ge1xuICAgICAgICAgICAgXCJ0ZXh0L2NzdlwiOiBcImNzdlwiLFxuICAgICAgICAgICAgXCJhcHBsaWNhdGlvbi9tc3dvcmRcIjogXCJkb2NcIixcbiAgICAgICAgICAgIFwiYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnRcIjogXCJkb2N4XCIsXG4gICAgICAgICAgICBcInRleHQvaHRtbFwiOiBcImh0bWxcIixcbiAgICAgICAgICAgIFwidGV4dC9tYXJrZG93blwiOiBcIm1kXCIsXG4gICAgICAgICAgICBcImFwcGxpY2F0aW9uL3BkZlwiOiBcInBkZlwiLFxuICAgICAgICAgICAgXCJ0ZXh0L3BsYWluXCI6IFwidHh0XCIsXG4gICAgICAgICAgICBcImFwcGxpY2F0aW9uL3ZuZC5tcy1leGNlbFwiOiBcInhsc1wiLFxuICAgICAgICAgICAgXCJhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldFwiOiBcInhsc3hcIixcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgbmFtZSA9IChibG9jay5tZXRhZGF0YT8ubmFtZSA/P1xuICAgICAgICAgICAgYmxvY2subWV0YWRhdGE/LmZpbGVuYW1lID8/XG4gICAgICAgICAgICBibG9jay5tZXRhZGF0YT8udGl0bGUpO1xuICAgICAgICBpZiAoYmxvY2suc291cmNlX3R5cGUgPT09IFwidGV4dFwiKSB7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIGRvY3VtZW50OiB7XG4gICAgICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgICAgIGZvcm1hdDogXCJ0eHRcIixcbiAgICAgICAgICAgICAgICAgICAgc291cmNlOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBieXRlczogbmV3IFRleHRFbmNvZGVyKCkuZW5jb2RlKGJsb2NrLnRleHQpLFxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIGlmIChibG9jay5zb3VyY2VfdHlwZSA9PT0gXCJ1cmxcIikge1xuICAgICAgICAgICAgY29uc3QgcGFyc2VkRGF0YSA9ICgwLCBtZXNzYWdlc18xLnBhcnNlQmFzZTY0RGF0YVVybCkoe1xuICAgICAgICAgICAgICAgIGRhdGFVcmw6IGJsb2NrLnVybCxcbiAgICAgICAgICAgICAgICBhc1R5cGVkQXJyYXk6IHRydWUsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmIChwYXJzZWREYXRhKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgcGFyc2VkTWltZVR5cGUgPSAoMCwgbWVzc2FnZXNfMS5wYXJzZU1pbWVUeXBlKShwYXJzZWREYXRhLm1pbWVfdHlwZSA/PyBibG9jay5taW1lX3R5cGUpO1xuICAgICAgICAgICAgICAgIGNvbnN0IG1pbWVUeXBlID0gYCR7cGFyc2VkTWltZVR5cGUudHlwZX0vJHtwYXJzZWRNaW1lVHlwZS5zdWJ0eXBlfWA7XG4gICAgICAgICAgICAgICAgY29uc3QgZm9ybWF0ID0gbWltZVR5cGVUb0RvY3VtZW50Rm9ybWF0W21pbWVUeXBlXTtcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICBkb2N1bWVudDoge1xuICAgICAgICAgICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvcm1hdCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHNvdXJjZToge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJ5dGVzOiBwYXJzZWREYXRhLmRhdGEsXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJPbmx5IGJhc2U2NCBkYXRhIFVSTHMgYXJlIHN1cHBvcnRlZCBmb3IgZmlsZSBibG9ja3Mgd2l0aCBzb3VyY2UgdHlwZSAndXJsJyB3aXRoIENoYXRCZWRyb2NrQ29udmVyc2UuXCIpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChibG9jay5zb3VyY2VfdHlwZSA9PT0gXCJiYXNlNjRcIikge1xuICAgICAgICAgICAgbGV0IGZvcm1hdDtcbiAgICAgICAgICAgIGlmIChibG9jay5taW1lX3R5cGUpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBwYXJzZWRNaW1lVHlwZSA9ICgwLCBtZXNzYWdlc18xLnBhcnNlTWltZVR5cGUpKGJsb2NrLm1pbWVfdHlwZSk7XG4gICAgICAgICAgICAgICAgY29uc3QgbWltZVR5cGUgPSBgJHtwYXJzZWRNaW1lVHlwZS50eXBlfS8ke3BhcnNlZE1pbWVUeXBlLnN1YnR5cGV9YDtcbiAgICAgICAgICAgICAgICBmb3JtYXQgPSBtaW1lVHlwZVRvRG9jdW1lbnRGb3JtYXRbbWltZVR5cGVdO1xuICAgICAgICAgICAgICAgIGlmIChmb3JtYXQgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuc3VwcG9ydGVkIGZpbGUgbWltZSB0eXBlOiBcIiR7YmxvY2subWltZV90eXBlfVwiIENoYXRCZWRyb2NrQ29udmVyc2Ugb25seSBzdXBwb3J0cyAke09iamVjdC5rZXlzKG1pbWVUeXBlVG9Eb2N1bWVudEZvcm1hdCkuam9pbihcIiwgXCIpfSBmb3JtYXRzLmApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgZG9jdW1lbnQ6IHtcbiAgICAgICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICAgICAgZm9ybWF0LFxuICAgICAgICAgICAgICAgICAgICBzb3VyY2U6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGJ5dGVzOiBVaW50OEFycmF5LmZyb20oYXRvYihibG9jay5kYXRhKSwgKGMpID0+IGMuY2hhckNvZGVBdCgwKSksXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGJsb2NrLnNvdXJjZV90eXBlID09PSBcImlkXCIpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkZpbGUgc291cmNlIHR5cGUgJ2lkJyBub3Qgc3VwcG9ydGVkIHdpdGggQ2hhdEJlZHJvY2tDb252ZXJzZS5cIik7XG4gICAgICAgIH1cbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBVbnN1cHBvcnRlZCBmaWxlIHNvdXJjZSB0eXBlOiBcIiR7YmxvY2suc291cmNlX3R5cGV9XCIgd2l0aCBDaGF0QmVkcm9ja0NvbnZlcnNlLmApO1xuICAgIH0sXG59O1xuZnVuY3Rpb24gZXh0cmFjdEltYWdlSW5mbyhiYXNlNjQpIHtcbiAgICAvLyBFeHRyYWN0IHRoZSBmb3JtYXQgZnJvbSB0aGUgYmFzZTY0IHN0cmluZ1xuICAgIGNvbnN0IGZvcm1hdE1hdGNoID0gYmFzZTY0Lm1hdGNoKC9eZGF0YTppbWFnZVxcLyhcXHcrKTtiYXNlNjQsLyk7XG4gICAgbGV0IGZvcm1hdDtcbiAgICBpZiAoZm9ybWF0TWF0Y2gpIHtcbiAgICAgICAgY29uc3QgZXh0cmFjdGVkRm9ybWF0ID0gZm9ybWF0TWF0Y2hbMV0udG9Mb3dlckNhc2UoKTtcbiAgICAgICAgaWYgKFtcImdpZlwiLCBcImpwZWdcIiwgXCJwbmdcIiwgXCJ3ZWJwXCJdLmluY2x1ZGVzKGV4dHJhY3RlZEZvcm1hdCkpIHtcbiAgICAgICAgICAgIGZvcm1hdCA9IGV4dHJhY3RlZEZvcm1hdDtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvLyBSZW1vdmUgdGhlIGRhdGEgVVJMIHByZWZpeCBpZiBwcmVzZW50XG4gICAgY29uc3QgYmFzZTY0RGF0YSA9IGJhc2U2NC5yZXBsYWNlKC9eZGF0YTppbWFnZVxcL1xcdys7YmFzZTY0LC8sIFwiXCIpO1xuICAgIC8vIENvbnZlcnQgYmFzZTY0IHRvIFVpbnQ4QXJyYXlcbiAgICBjb25zdCBiaW5hcnlTdHJpbmcgPSBhdG9iKGJhc2U2NERhdGEpO1xuICAgIGNvbnN0IGJ5dGVzID0gbmV3IFVpbnQ4QXJyYXkoYmluYXJ5U3RyaW5nLmxlbmd0aCk7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBiaW5hcnlTdHJpbmcubGVuZ3RoOyBpICs9IDEpIHtcbiAgICAgICAgYnl0ZXNbaV0gPSBiaW5hcnlTdHJpbmcuY2hhckNvZGVBdChpKTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgICAgaW1hZ2U6IHtcbiAgICAgICAgICAgIGZvcm1hdCxcbiAgICAgICAgICAgIHNvdXJjZToge1xuICAgICAgICAgICAgICAgIGJ5dGVzLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICB9O1xufVxuZXhwb3J0cy5leHRyYWN0SW1hZ2VJbmZvID0gZXh0cmFjdEltYWdlSW5mbztcbmZ1bmN0aW9uIGNvbnZlcnRMYW5nQ2hhaW5Db250ZW50QmxvY2tUb0NvbnZlcnNlQ29udGVudEJsb2NrKHsgYmxvY2ssIG9uVW5rbm93biA9IFwidGhyb3dcIiwgfSkge1xuICAgIGlmICh0eXBlb2YgYmxvY2sgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgcmV0dXJuIHsgdGV4dDogYmxvY2sgfTtcbiAgICB9XG4gICAgaWYgKCgwLCBtZXNzYWdlc18xLmlzRGF0YUNvbnRlbnRCbG9jaykoYmxvY2spKSB7XG4gICAgICAgIHJldHVybiAoMCwgbWVzc2FnZXNfMS5jb252ZXJ0VG9Qcm92aWRlckNvbnRlbnRCbG9jaykoYmxvY2ssIHN0YW5kYXJkQ29udGVudEJsb2NrQ29udmVydGVyKTtcbiAgICB9XG4gICAgaWYgKGJsb2NrLnR5cGUgPT09IFwidGV4dFwiKSB7XG4gICAgICAgIHJldHVybiB7IHRleHQ6IGJsb2NrLnRleHQgfTtcbiAgICB9XG4gICAgaWYgKGJsb2NrLnR5cGUgPT09IFwiaW1hZ2VfdXJsXCIpIHtcbiAgICAgICAgcmV0dXJuIGV4dHJhY3RJbWFnZUluZm8odHlwZW9mIGJsb2NrLmltYWdlX3VybCA9PT0gXCJzdHJpbmdcIlxuICAgICAgICAgICAgPyBibG9jay5pbWFnZV91cmxcbiAgICAgICAgICAgIDogYmxvY2suaW1hZ2VfdXJsLnVybCk7XG4gICAgfVxuICAgIGlmIChibG9jay50eXBlID09PSBcImRvY3VtZW50XCIgJiYgYmxvY2suZG9jdW1lbnQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgZG9jdW1lbnQ6IGJsb2NrLmRvY3VtZW50LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBpZiAoYmxvY2sudHlwZSA9PT0gXCJpbWFnZVwiICYmIGJsb2NrLmltYWdlICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGltYWdlOiBibG9jay5pbWFnZSxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgaWYgKG9uVW5rbm93biA9PT0gXCJ0aHJvd1wiKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgVW5zdXBwb3J0ZWQgY29udGVudCBibG9jayB0eXBlOiAke2Jsb2NrLnR5cGV9YCk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICByZXR1cm4gYmxvY2s7XG4gICAgfVxufVxuZnVuY3Rpb24gY29udmVydFN5c3RlbU1lc3NhZ2VUb0NvbnZlcnNlTWVzc2FnZShtc2cpIHtcbiAgICBpZiAodHlwZW9mIG1zZy5jb250ZW50ID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIHJldHVybiB7IHRleHQ6IG1zZy5jb250ZW50IH07XG4gICAgfVxuICAgIGVsc2UgaWYgKG1zZy5jb250ZW50Lmxlbmd0aCA9PT0gMSAmJiBtc2cuY29udGVudFswXS50eXBlID09PSBcInRleHRcIikge1xuICAgICAgICByZXR1cm4geyB0ZXh0OiBtc2cuY29udGVudFswXS50ZXh0IH07XG4gICAgfVxuICAgIHRocm93IG5ldyBFcnJvcihcIlN5c3RlbSBtZXNzYWdlIGNvbnRlbnQgbXVzdCBiZSBlaXRoZXIgYSBzdHJpbmcsIG9yIGEgY29udGVudCBhcnJheSBjb250YWluaW5nIGEgc2luZ2xlIHRleHQgb2JqZWN0LlwiKTtcbn1cbmZ1bmN0aW9uIGNvbnZlcnRBSU1lc3NhZ2VUb0NvbnZlcnNlTWVzc2FnZShtc2cpIHtcbiAgICBjb25zdCBhc3Npc3RhbnRNc2cgPSB7XG4gICAgICAgIHJvbGU6IFwiYXNzaXN0YW50XCIsXG4gICAgICAgIGNvbnRlbnQ6IFtdLFxuICAgIH07XG4gICAgaWYgKHR5cGVvZiBtc2cuY29udGVudCA9PT0gXCJzdHJpbmdcIiAmJiBtc2cuY29udGVudCAhPT0gXCJcIikge1xuICAgICAgICBhc3Npc3RhbnRNc2cuY29udGVudD8ucHVzaCh7XG4gICAgICAgICAgICB0ZXh0OiBtc2cuY29udGVudCxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKEFycmF5LmlzQXJyYXkobXNnLmNvbnRlbnQpKSB7XG4gICAgICAgIGNvbnN0IGNvbmNhdGVuYXRlZEJsb2NrcyA9IGNvbmNhdGVuYXRlTGFuZ2NoYWluUmVhc29uaW5nQmxvY2tzKG1zZy5jb250ZW50KTtcbiAgICAgICAgY29uc3QgY29udGVudEJsb2NrcyA9IGNvbmNhdGVuYXRlZEJsb2Nrcy5tYXAoKGJsb2NrKSA9PiB7XG4gICAgICAgICAgICBpZiAoYmxvY2sudHlwZSA9PT0gXCJ0ZXh0XCIgJiYgYmxvY2sudGV4dCAhPT0gXCJcIikge1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIHRleHQ6IGJsb2NrLnRleHQsXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGJsb2NrLnR5cGUgPT09IFwicmVhc29uaW5nX2NvbnRlbnRcIikge1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIHJlYXNvbmluZ0NvbnRlbnQ6IGxhbmdjaGFpblJlYXNvbmluZ0Jsb2NrVG9CZWRyb2NrUmVhc29uaW5nQmxvY2soYmxvY2spLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBjb25zdCBibG9ja1ZhbHVlcyA9IE9iamVjdC5mcm9tRW50cmllcyhPYmplY3QuZW50cmllcyhibG9jaykuZmlsdGVyKChba2V5XSkgPT4ga2V5ICE9PSBcInR5cGVcIikpO1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgVW5zdXBwb3J0ZWQgY29udGVudCBibG9jayB0eXBlOiAke2Jsb2NrLnR5cGV9IHdpdGggY29udGVudCBvZiAke0pTT04uc3RyaW5naWZ5KGJsb2NrVmFsdWVzLCBudWxsLCAyKX1gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIGFzc2lzdGFudE1zZy5jb250ZW50ID0gW1xuICAgICAgICAgICAgLi4uKGFzc2lzdGFudE1zZy5jb250ZW50ID8gYXNzaXN0YW50TXNnLmNvbnRlbnQgOiBbXSksXG4gICAgICAgICAgICAuLi5jb250ZW50QmxvY2tzLFxuICAgICAgICBdO1xuICAgIH1cbiAgICAvLyBJbXBvcnRhbnQ6IHRoaXMgbXVzdCBiZSBwbGFjZWQgYWZ0ZXIgYW55IHJlYXNvbmluZyBjb250ZW50IGJsb2NrcywgZWxzZSBjbGF1ZGUgbW9kZWxzIHdpbGwgcmV0dXJuIGFuIGVycm9yLlxuICAgIGlmIChtc2cudG9vbF9jYWxscyAmJiBtc2cudG9vbF9jYWxscy5sZW5ndGgpIHtcbiAgICAgICAgYXNzaXN0YW50TXNnLmNvbnRlbnQgPSBbXG4gICAgICAgICAgICAuLi4oYXNzaXN0YW50TXNnLmNvbnRlbnQgPyBhc3Npc3RhbnRNc2cuY29udGVudCA6IFtdKSxcbiAgICAgICAgICAgIC4uLm1zZy50b29sX2NhbGxzLm1hcCgodGMpID0+ICh7XG4gICAgICAgICAgICAgICAgdG9vbFVzZToge1xuICAgICAgICAgICAgICAgICAgICB0b29sVXNlSWQ6IHRjLmlkLFxuICAgICAgICAgICAgICAgICAgICBuYW1lOiB0Yy5uYW1lLFxuICAgICAgICAgICAgICAgICAgICBpbnB1dDogdGMuYXJncyxcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgfSkpLFxuICAgICAgICBdO1xuICAgIH1cbiAgICByZXR1cm4gYXNzaXN0YW50TXNnO1xufVxuZnVuY3Rpb24gY29udmVydEh1bWFuTWVzc2FnZVRvQ29udmVyc2VNZXNzYWdlKG1zZykge1xuICAgIGlmIChtc2cuY29udGVudCA9PT0gXCJcIikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEludmFsaWQgbWVzc2FnZSBjb250ZW50OiBlbXB0eSBzdHJpbmcuICcke21zZy5fZ2V0VHlwZSgpfScgbXVzdCBjb250YWluIG5vbi1lbXB0eSBjb250ZW50LmApO1xuICAgIH1cbiAgICBjb25zdCBjb250ZW50ID0gQXJyYXkuaXNBcnJheShtc2cuY29udGVudClcbiAgICAgICAgPyBtc2cuY29udGVudC5tYXAoKGMpID0+IGNvbnZlcnRMYW5nQ2hhaW5Db250ZW50QmxvY2tUb0NvbnZlcnNlQ29udGVudEJsb2NrKHtcbiAgICAgICAgICAgIGJsb2NrOiBjLFxuICAgICAgICAgICAgb25Vbmtub3duOiBcInRocm93XCIsXG4gICAgICAgIH0pKVxuICAgICAgICA6IFtcbiAgICAgICAgICAgIGNvbnZlcnRMYW5nQ2hhaW5Db250ZW50QmxvY2tUb0NvbnZlcnNlQ29udGVudEJsb2NrKHtcbiAgICAgICAgICAgICAgICBibG9jazogbXNnLmNvbnRlbnQsXG4gICAgICAgICAgICAgICAgb25Vbmtub3duOiBcInRocm93XCIsXG4gICAgICAgICAgICB9KSxcbiAgICAgICAgXTtcbiAgICByZXR1cm4ge1xuICAgICAgICByb2xlOiBcInVzZXJcIixcbiAgICAgICAgY29udGVudCxcbiAgICB9O1xufVxuZnVuY3Rpb24gY29udmVydFRvb2xNZXNzYWdlVG9Db252ZXJzZU1lc3NhZ2UobXNnKSB7XG4gICAgY29uc3QgY2FzdE1zZyA9IG1zZztcbiAgICBpZiAodHlwZW9mIGNhc3RNc2cuY29udGVudCA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLy8gVG9vbCB1c2UgbWVzc2FnZXMgYXJlIGFsd2F5cyBmcm9tIHRoZSB1c2VyXG4gICAgICAgICAgICByb2xlOiBcInVzZXJcIixcbiAgICAgICAgICAgIGNvbnRlbnQ6IFtcbiAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIHRvb2xSZXN1bHQ6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvb2xVc2VJZDogY2FzdE1zZy50b29sX2NhbGxfaWQsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiBbXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0OiBjYXN0TXNnLmNvbnRlbnQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgIF0sXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIF0sXG4gICAgICAgIH07XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLy8gVG9vbCB1c2UgbWVzc2FnZXMgYXJlIGFsd2F5cyBmcm9tIHRoZSB1c2VyXG4gICAgICAgICAgICByb2xlOiBcInVzZXJcIixcbiAgICAgICAgICAgIGNvbnRlbnQ6IFtcbiAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIHRvb2xSZXN1bHQ6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRvb2xVc2VJZDogY2FzdE1zZy50b29sX2NhbGxfaWQsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb250ZW50OiBtc2cuY29udGVudC5tYXAoKGMpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb252ZXJ0ZWQgPSBjb252ZXJ0TGFuZ0NoYWluQ29udGVudEJsb2NrVG9Db252ZXJzZUNvbnRlbnRCbG9jayh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJsb2NrOiBjLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvblVua25vd246IFwicmV0dXJuVW5tb2RpZmllZFwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjb252ZXJ0ZWQgIT09IGMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGNvbnZlcnRlZDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsganNvbjogYyB9O1xuICAgICAgICAgICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIF0sXG4gICAgICAgIH07XG4gICAgfVxufVxuZnVuY3Rpb24gY29udmVydFRvQ29udmVyc2VNZXNzYWdlcyhtZXNzYWdlcykge1xuICAgIGNvbnN0IGNvbnZlcnNlU3lzdGVtID0gbWVzc2FnZXNcbiAgICAgICAgLmZpbHRlcigobXNnKSA9PiBtc2cuX2dldFR5cGUoKSA9PT0gXCJzeXN0ZW1cIilcbiAgICAgICAgLm1hcCgobXNnKSA9PiBjb252ZXJ0U3lzdGVtTWVzc2FnZVRvQ29udmVyc2VNZXNzYWdlKG1zZykpO1xuICAgIGNvbnN0IGNvbnZlcnNlTWVzc2FnZXMgPSBtZXNzYWdlc1xuICAgICAgICAuZmlsdGVyKChtc2cpID0+IG1zZy5fZ2V0VHlwZSgpICE9PSBcInN5c3RlbVwiKVxuICAgICAgICAubWFwKChtc2cpID0+IHtcbiAgICAgICAgaWYgKG1zZy5fZ2V0VHlwZSgpID09PSBcImFpXCIpIHtcbiAgICAgICAgICAgIHJldHVybiBjb252ZXJ0QUlNZXNzYWdlVG9Db252ZXJzZU1lc3NhZ2UobXNnKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChtc2cuX2dldFR5cGUoKSA9PT0gXCJodW1hblwiIHx8IG1zZy5fZ2V0VHlwZSgpID09PSBcImdlbmVyaWNcIikge1xuICAgICAgICAgICAgcmV0dXJuIGNvbnZlcnRIdW1hbk1lc3NhZ2VUb0NvbnZlcnNlTWVzc2FnZShtc2cpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKG1zZy5fZ2V0VHlwZSgpID09PSBcInRvb2xcIikge1xuICAgICAgICAgICAgcmV0dXJuIGNvbnZlcnRUb29sTWVzc2FnZVRvQ29udmVyc2VNZXNzYWdlKG1zZyk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuc3VwcG9ydGVkIG1lc3NhZ2UgdHlwZTogJHttc2cuX2dldFR5cGUoKX1gKTtcbiAgICAgICAgfVxuICAgIH0pO1xuICAgIC8vIENvbWJpbmUgY29uc2VjdXRpdmUgdXNlciB0b29sIHJlc3VsdCBtZXNzYWdlcyBpbnRvIGEgc2luZ2xlIG1lc3NhZ2VcbiAgICBjb25zdCBjb21iaW5lZENvbnZlcnNlTWVzc2FnZXMgPSBjb252ZXJzZU1lc3NhZ2VzLnJlZHVjZSgoYWNjLCBjdXJyKSA9PiB7XG4gICAgICAgIGNvbnN0IGxhc3RNZXNzYWdlID0gYWNjW2FjYy5sZW5ndGggLSAxXTtcbiAgICAgICAgaWYgKGxhc3RNZXNzYWdlICYmXG4gICAgICAgICAgICBsYXN0TWVzc2FnZS5yb2xlID09PSBcInVzZXJcIiAmJlxuICAgICAgICAgICAgbGFzdE1lc3NhZ2UuY29udGVudD8uc29tZSgoYykgPT4gXCJ0b29sUmVzdWx0XCIgaW4gYykgJiZcbiAgICAgICAgICAgIGN1cnIucm9sZSA9PT0gXCJ1c2VyXCIgJiZcbiAgICAgICAgICAgIGN1cnIuY29udGVudD8uc29tZSgoYykgPT4gXCJ0b29sUmVzdWx0XCIgaW4gYykpIHtcbiAgICAgICAgICAgIGxhc3RNZXNzYWdlLmNvbnRlbnQgPSBsYXN0TWVzc2FnZS5jb250ZW50LmNvbmNhdChjdXJyLmNvbnRlbnQpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgYWNjLnB1c2goY3Vycik7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGFjYztcbiAgICB9LCBbXSk7XG4gICAgcmV0dXJuIHsgY29udmVyc2VNZXNzYWdlczogY29tYmluZWRDb252ZXJzZU1lc3NhZ2VzLCBjb252ZXJzZVN5c3RlbSB9O1xufVxuZXhwb3J0cy5jb252ZXJ0VG9Db252ZXJzZU1lc3NhZ2VzID0gY29udmVydFRvQ29udmVyc2VNZXNzYWdlcztcbmZ1bmN0aW9uIGlzQmVkcm9ja1Rvb2wodG9vbCkge1xuICAgIGlmICh0eXBlb2YgdG9vbCA9PT0gXCJvYmplY3RcIiAmJiB0b29sICYmIFwidG9vbFNwZWNcIiBpbiB0b29sKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICByZXR1cm4gZmFsc2U7XG59XG5leHBvcnRzLmlzQmVkcm9ja1Rvb2wgPSBpc0JlZHJvY2tUb29sO1xuZnVuY3Rpb24gY29udmVydFRvQ29udmVyc2VUb29scyh0b29scykge1xuICAgIGlmICh0b29scy5ldmVyeShiYXNlXzEuaXNPcGVuQUlUb29sKSkge1xuICAgICAgICByZXR1cm4gdG9vbHMubWFwKCh0b29sKSA9PiAoe1xuICAgICAgICAgICAgdG9vbFNwZWM6IHtcbiAgICAgICAgICAgICAgICBuYW1lOiB0b29sLmZ1bmN0aW9uLm5hbWUsXG4gICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IHRvb2wuZnVuY3Rpb24uZGVzY3JpcHRpb24sXG4gICAgICAgICAgICAgICAgaW5wdXRTY2hlbWE6IHtcbiAgICAgICAgICAgICAgICAgICAganNvbjogdG9vbC5mdW5jdGlvbi5wYXJhbWV0ZXJzLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9LFxuICAgICAgICB9KSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKHRvb2xzLmV2ZXJ5KGZ1bmN0aW9uX2NhbGxpbmdfMS5pc0xhbmdDaGFpblRvb2wpKSB7XG4gICAgICAgIHJldHVybiB0b29scy5tYXAoKHRvb2wpID0+ICh7XG4gICAgICAgICAgICB0b29sU3BlYzoge1xuICAgICAgICAgICAgICAgIG5hbWU6IHRvb2wubmFtZSxcbiAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogdG9vbC5kZXNjcmlwdGlvbixcbiAgICAgICAgICAgICAgICBpbnB1dFNjaGVtYToge1xuICAgICAgICAgICAgICAgICAgICBqc29uOiAoKDAsIHR5cGVzXzEuaXNJbnRlcm9wWm9kU2NoZW1hKSh0b29sLnNjaGVtYSlcbiAgICAgICAgICAgICAgICAgICAgICAgID8gKDAsIGpzb25fc2NoZW1hXzEudG9Kc29uU2NoZW1hKSh0b29sLnNjaGVtYSlcbiAgICAgICAgICAgICAgICAgICAgICAgIDogdG9vbC5zY2hlbWEpLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9LFxuICAgICAgICB9KSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKHRvb2xzLmV2ZXJ5KGlzQmVkcm9ja1Rvb2wpKSB7XG4gICAgICAgIHJldHVybiB0b29scztcbiAgICB9XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCB0b29scyBwYXNzZWQuIE11c3QgYmUgYW4gYXJyYXkgb2YgU3RydWN0dXJlZFRvb2xJbnRlcmZhY2UsIFRvb2xEZWZpbml0aW9uLCBvciBCZWRyb2NrVG9vbC5cIik7XG59XG5leHBvcnRzLmNvbnZlcnRUb0NvbnZlcnNlVG9vbHMgPSBjb252ZXJ0VG9Db252ZXJzZVRvb2xzO1xuZnVuY3Rpb24gY29udmVydFRvQmVkcm9ja1Rvb2xDaG9pY2UodG9vbENob2ljZSwgdG9vbHMsIGZpZWxkcykge1xuICAgIGNvbnN0IHN1cHBvcnRzVG9vbENob2ljZVZhbHVlcyA9IGZpZWxkcy5zdXBwb3J0c1Rvb2xDaG9pY2VWYWx1ZXMgPz8gW107XG4gICAgbGV0IGJlZHJvY2tUb29sQ2hvaWNlO1xuICAgIGlmICh0eXBlb2YgdG9vbENob2ljZSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICBzd2l0Y2ggKHRvb2xDaG9pY2UpIHtcbiAgICAgICAgICAgIGNhc2UgXCJhbnlcIjpcbiAgICAgICAgICAgICAgICBiZWRyb2NrVG9vbENob2ljZSA9IHtcbiAgICAgICAgICAgICAgICAgICAgYW55OiB7fSxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSBcImF1dG9cIjpcbiAgICAgICAgICAgICAgICBiZWRyb2NrVG9vbENob2ljZSA9IHtcbiAgICAgICAgICAgICAgICAgICAgYXV0bzoge30sXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGRlZmF1bHQ6IHtcbiAgICAgICAgICAgICAgICBjb25zdCBmb3VuZFRvb2wgPSB0b29scy5maW5kKCh0b29sKSA9PiB0b29sLnRvb2xTcGVjPy5uYW1lID09PSB0b29sQ2hvaWNlKTtcbiAgICAgICAgICAgICAgICBpZiAoIWZvdW5kVG9vbCkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFRvb2wgd2l0aCBuYW1lICR7dG9vbENob2ljZX0gbm90IGZvdW5kIGluIHRvb2xzIGxpc3QuYCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGJlZHJvY2tUb29sQ2hvaWNlID0ge1xuICAgICAgICAgICAgICAgICAgICB0b29sOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiB0b29sQ2hvaWNlLFxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGJlZHJvY2tUb29sQ2hvaWNlID0gdG9vbENob2ljZTtcbiAgICB9XG4gICAgY29uc3QgdG9vbENob2ljZVR5cGUgPSBPYmplY3Qua2V5cyhiZWRyb2NrVG9vbENob2ljZSlbMF07XG4gICAgaWYgKCFzdXBwb3J0c1Rvb2xDaG9pY2VWYWx1ZXMuaW5jbHVkZXModG9vbENob2ljZVR5cGUpKSB7XG4gICAgICAgIGxldCBzdXBwb3J0ZWRUeHQgPSBcIlwiO1xuICAgICAgICBpZiAoc3VwcG9ydHNUb29sQ2hvaWNlVmFsdWVzLmxlbmd0aCkge1xuICAgICAgICAgICAgc3VwcG9ydGVkVHh0ID1cbiAgICAgICAgICAgICAgICBgTW9kZWwgJHtmaWVsZHMubW9kZWx9IGRvZXMgbm90IGN1cnJlbnRseSBzdXBwb3J0ICd0b29sX2Nob2ljZScgYCArXG4gICAgICAgICAgICAgICAgICAgIGBvZiB0eXBlICR7dG9vbENob2ljZVR5cGV9LiBUaGUgZm9sbG93aW5nICd0b29sX2Nob2ljZScgdHlwZXMgYCArXG4gICAgICAgICAgICAgICAgICAgIGBhcmUgc3VwcG9ydGVkOiAke3N1cHBvcnRzVG9vbENob2ljZVZhbHVlcy5qb2luKFwiLCBcIil9LmA7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBzdXBwb3J0ZWRUeHQgPSBgTW9kZWwgJHtmaWVsZHMubW9kZWx9IGRvZXMgbm90IGN1cnJlbnRseSBzdXBwb3J0ICd0b29sX2Nob2ljZScuYDtcbiAgICAgICAgfVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYCR7c3VwcG9ydGVkVHh0fSBQbGVhc2Ugc2VlYCArXG4gICAgICAgICAgICBcImh0dHBzOi8vZG9jcy5hd3MuYW1hem9uLmNvbS9iZWRyb2NrL2xhdGVzdC9BUElSZWZlcmVuY2UvQVBJX3J1bnRpbWVfVG9vbENob2ljZS5odG1sXCIgK1xuICAgICAgICAgICAgXCJmb3IgdGhlIGxhdGVzdCBkb2N1bWVudGF0aW9uIG9uIG1vZGVscyB0aGF0IHN1cHBvcnQgdG9vbCBjaG9pY2UuXCIpO1xuICAgIH1cbiAgICByZXR1cm4gYmVkcm9ja1Rvb2xDaG9pY2U7XG59XG5leHBvcnRzLmNvbnZlcnRUb0JlZHJvY2tUb29sQ2hvaWNlID0gY29udmVydFRvQmVkcm9ja1Rvb2xDaG9pY2U7XG5mdW5jdGlvbiBjb252ZXJ0Q29udmVyc2VNZXNzYWdlVG9MYW5nQ2hhaW5NZXNzYWdlKG1lc3NhZ2UsIHJlc3BvbnNlTWV0YWRhdGEpIHtcbiAgICBpZiAoIW1lc3NhZ2UuY29udGVudCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJObyBtZXNzYWdlIGNvbnRlbnQgZm91bmQgaW4gcmVzcG9uc2UuXCIpO1xuICAgIH1cbiAgICBpZiAobWVzc2FnZS5yb2xlICE9PSBcImFzc2lzdGFudFwiKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgVW5zdXBwb3J0ZWQgbWVzc2FnZSByb2xlIHJlY2VpdmVkIGluIENoYXRCZWRyb2NrQ29udmVyc2UgcmVzcG9uc2U6ICR7bWVzc2FnZS5yb2xlfWApO1xuICAgIH1cbiAgICBsZXQgcmVxdWVzdElkO1xuICAgIGlmIChcIiRtZXRhZGF0YVwiIGluIHJlc3BvbnNlTWV0YWRhdGEgJiZcbiAgICAgICAgcmVzcG9uc2VNZXRhZGF0YS4kbWV0YWRhdGEgJiZcbiAgICAgICAgdHlwZW9mIHJlc3BvbnNlTWV0YWRhdGEuJG1ldGFkYXRhID09PSBcIm9iamVjdFwiICYmXG4gICAgICAgIFwicmVxdWVzdElkXCIgaW4gcmVzcG9uc2VNZXRhZGF0YS4kbWV0YWRhdGEpIHtcbiAgICAgICAgcmVxdWVzdElkID0gcmVzcG9uc2VNZXRhZGF0YS4kbWV0YWRhdGEucmVxdWVzdElkO1xuICAgIH1cbiAgICBsZXQgdG9rZW5Vc2FnZTtcbiAgICBpZiAocmVzcG9uc2VNZXRhZGF0YS51c2FnZSkge1xuICAgICAgICBjb25zdCBpbnB1dF90b2tlbnMgPSByZXNwb25zZU1ldGFkYXRhLnVzYWdlLmlucHV0VG9rZW5zID8/IDA7XG4gICAgICAgIGNvbnN0IG91dHB1dF90b2tlbnMgPSByZXNwb25zZU1ldGFkYXRhLnVzYWdlLm91dHB1dFRva2VucyA/PyAwO1xuICAgICAgICB0b2tlblVzYWdlID0ge1xuICAgICAgICAgICAgaW5wdXRfdG9rZW5zLFxuICAgICAgICAgICAgb3V0cHV0X3Rva2VucyxcbiAgICAgICAgICAgIHRvdGFsX3Rva2VuczogcmVzcG9uc2VNZXRhZGF0YS51c2FnZS50b3RhbFRva2VucyA/PyBpbnB1dF90b2tlbnMgKyBvdXRwdXRfdG9rZW5zLFxuICAgICAgICB9O1xuICAgIH1cbiAgICBpZiAobWVzc2FnZS5jb250ZW50Py5sZW5ndGggPT09IDEgJiZcbiAgICAgICAgXCJ0ZXh0XCIgaW4gbWVzc2FnZS5jb250ZW50WzBdICYmXG4gICAgICAgIHR5cGVvZiBtZXNzYWdlLmNvbnRlbnRbMF0udGV4dCA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICByZXR1cm4gbmV3IG1lc3NhZ2VzXzEuQUlNZXNzYWdlKHtcbiAgICAgICAgICAgIGNvbnRlbnQ6IG1lc3NhZ2UuY29udGVudFswXS50ZXh0LFxuICAgICAgICAgICAgcmVzcG9uc2VfbWV0YWRhdGE6IHJlc3BvbnNlTWV0YWRhdGEsXG4gICAgICAgICAgICB1c2FnZV9tZXRhZGF0YTogdG9rZW5Vc2FnZSxcbiAgICAgICAgICAgIGlkOiByZXF1ZXN0SWQsXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgY29uc3QgdG9vbENhbGxzID0gW107XG4gICAgICAgIGNvbnN0IGNvbnRlbnQgPSBbXTtcbiAgICAgICAgbWVzc2FnZS5jb250ZW50LmZvckVhY2goKGMpID0+IHtcbiAgICAgICAgICAgIGlmIChcInRvb2xVc2VcIiBpbiBjICYmXG4gICAgICAgICAgICAgICAgYy50b29sVXNlICYmXG4gICAgICAgICAgICAgICAgYy50b29sVXNlLm5hbWUgJiZcbiAgICAgICAgICAgICAgICBjLnRvb2xVc2UuaW5wdXQgJiZcbiAgICAgICAgICAgICAgICB0eXBlb2YgYy50b29sVXNlLmlucHV0ID09PSBcIm9iamVjdFwiKSB7XG4gICAgICAgICAgICAgICAgdG9vbENhbGxzLnB1c2goe1xuICAgICAgICAgICAgICAgICAgICBpZDogYy50b29sVXNlLnRvb2xVc2VJZCxcbiAgICAgICAgICAgICAgICAgICAgbmFtZTogYy50b29sVXNlLm5hbWUsXG4gICAgICAgICAgICAgICAgICAgIGFyZ3M6IGMudG9vbFVzZS5pbnB1dCxcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogXCJ0b29sX2NhbGxcIixcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKFwidGV4dFwiIGluIGMgJiYgdHlwZW9mIGMudGV4dCA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgICAgIGNvbnRlbnQucHVzaCh7IHR5cGU6IFwidGV4dFwiLCB0ZXh0OiBjLnRleHQgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChcInJlYXNvbmluZ0NvbnRlbnRcIiBpbiBjKSB7XG4gICAgICAgICAgICAgICAgY29udGVudC5wdXNoKGJlZHJvY2tSZWFzb25pbmdCbG9ja1RvTGFuZ2NoYWluUmVhc29uaW5nQmxvY2soYy5yZWFzb25pbmdDb250ZW50KSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBjb250ZW50LnB1c2goYyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gbmV3IG1lc3NhZ2VzXzEuQUlNZXNzYWdlKHtcbiAgICAgICAgICAgIGNvbnRlbnQ6IGNvbnRlbnQubGVuZ3RoID8gY29udGVudCA6IFwiXCIsXG4gICAgICAgICAgICB0b29sX2NhbGxzOiB0b29sQ2FsbHMubGVuZ3RoID8gdG9vbENhbGxzIDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgcmVzcG9uc2VfbWV0YWRhdGE6IHJlc3BvbnNlTWV0YWRhdGEsXG4gICAgICAgICAgICB1c2FnZV9tZXRhZGF0YTogdG9rZW5Vc2FnZSxcbiAgICAgICAgICAgIGlkOiByZXF1ZXN0SWQsXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbmV4cG9ydHMuY29udmVydENvbnZlcnNlTWVzc2FnZVRvTGFuZ0NoYWluTWVzc2FnZSA9IGNvbnZlcnRDb252ZXJzZU1lc3NhZ2VUb0xhbmdDaGFpbk1lc3NhZ2U7XG5mdW5jdGlvbiBoYW5kbGVDb252ZXJzZVN0cmVhbUNvbnRlbnRCbG9ja0RlbHRhKGNvbnRlbnRCbG9ja0RlbHRhKSB7XG4gICAgaWYgKCFjb250ZW50QmxvY2tEZWx0YS5kZWx0YSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJObyBkZWx0YSBmb3VuZCBpbiBjb250ZW50IGJsb2NrLlwiKTtcbiAgICB9XG4gICAgaWYgKHR5cGVvZiBjb250ZW50QmxvY2tEZWx0YS5kZWx0YS50ZXh0ID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIHJldHVybiBuZXcgb3V0cHV0c18xLkNoYXRHZW5lcmF0aW9uQ2h1bmsoe1xuICAgICAgICAgICAgdGV4dDogY29udGVudEJsb2NrRGVsdGEuZGVsdGEudGV4dCxcbiAgICAgICAgICAgIG1lc3NhZ2U6IG5ldyBtZXNzYWdlc18xLkFJTWVzc2FnZUNodW5rKHtcbiAgICAgICAgICAgICAgICBjb250ZW50OiBjb250ZW50QmxvY2tEZWx0YS5kZWx0YS50ZXh0LFxuICAgICAgICAgICAgfSksXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBlbHNlIGlmIChjb250ZW50QmxvY2tEZWx0YS5kZWx0YS50b29sVXNlKSB7XG4gICAgICAgIGNvbnN0IGluZGV4ID0gY29udGVudEJsb2NrRGVsdGEuY29udGVudEJsb2NrSW5kZXg7XG4gICAgICAgIHJldHVybiBuZXcgb3V0cHV0c18xLkNoYXRHZW5lcmF0aW9uQ2h1bmsoe1xuICAgICAgICAgICAgdGV4dDogXCJcIixcbiAgICAgICAgICAgIG1lc3NhZ2U6IG5ldyBtZXNzYWdlc18xLkFJTWVzc2FnZUNodW5rKHtcbiAgICAgICAgICAgICAgICBjb250ZW50OiBcIlwiLFxuICAgICAgICAgICAgICAgIHRvb2xfY2FsbF9jaHVua3M6IFtcbiAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgYXJnczogY29udGVudEJsb2NrRGVsdGEuZGVsdGEudG9vbFVzZS5pbnB1dCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4LFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogXCJ0b29sX2NhbGxfY2h1bmtcIixcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBdLFxuICAgICAgICAgICAgfSksXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBlbHNlIGlmIChjb250ZW50QmxvY2tEZWx0YS5kZWx0YS5yZWFzb25pbmdDb250ZW50KSB7XG4gICAgICAgIHJldHVybiBuZXcgb3V0cHV0c18xLkNoYXRHZW5lcmF0aW9uQ2h1bmsoe1xuICAgICAgICAgICAgdGV4dDogXCJcIixcbiAgICAgICAgICAgIG1lc3NhZ2U6IG5ldyBtZXNzYWdlc18xLkFJTWVzc2FnZUNodW5rKHtcbiAgICAgICAgICAgICAgICBjb250ZW50OiBbXG4gICAgICAgICAgICAgICAgICAgIGJlZHJvY2tSZWFzb25pbmdEZWx0YVRvTGFuZ2NoYWluUGFydGlhbFJlYXNvbmluZ0Jsb2NrKGNvbnRlbnRCbG9ja0RlbHRhLmRlbHRhLnJlYXNvbmluZ0NvbnRlbnQpLFxuICAgICAgICAgICAgICAgIF0sXG4gICAgICAgICAgICB9KSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuc3VwcG9ydGVkIGNvbnRlbnQgYmxvY2sgdHlwZShzKTogJHtKU09OLnN0cmluZ2lmeShjb250ZW50QmxvY2tEZWx0YS5kZWx0YSwgbnVsbCwgMil9YCk7XG4gICAgfVxufVxuZXhwb3J0cy5oYW5kbGVDb252ZXJzZVN0cmVhbUNvbnRlbnRCbG9ja0RlbHRhID0gaGFuZGxlQ29udmVyc2VTdHJlYW1Db250ZW50QmxvY2tEZWx0YTtcbmZ1bmN0aW9uIGhhbmRsZUNvbnZlcnNlU3RyZWFtQ29udGVudEJsb2NrU3RhcnQoY29udGVudEJsb2NrU3RhcnQpIHtcbiAgICBjb25zdCBpbmRleCA9IGNvbnRlbnRCbG9ja1N0YXJ0LmNvbnRlbnRCbG9ja0luZGV4O1xuICAgIGlmIChjb250ZW50QmxvY2tTdGFydC5zdGFydD8udG9vbFVzZSkge1xuICAgICAgICByZXR1cm4gbmV3IG91dHB1dHNfMS5DaGF0R2VuZXJhdGlvbkNodW5rKHtcbiAgICAgICAgICAgIHRleHQ6IFwiXCIsXG4gICAgICAgICAgICBtZXNzYWdlOiBuZXcgbWVzc2FnZXNfMS5BSU1lc3NhZ2VDaHVuayh7XG4gICAgICAgICAgICAgICAgY29udGVudDogXCJcIixcbiAgICAgICAgICAgICAgICB0b29sX2NhbGxfY2h1bmtzOiBbXG4gICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6IGNvbnRlbnRCbG9ja1N0YXJ0LnN0YXJ0LnRvb2xVc2UubmFtZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBjb250ZW50QmxvY2tTdGFydC5zdGFydC50b29sVXNlLnRvb2xVc2VJZCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4LFxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogXCJ0b29sX2NhbGxfY2h1bmtcIixcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBdLFxuICAgICAgICAgICAgfSksXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJVbnN1cHBvcnRlZCBjb250ZW50IGJsb2NrIHN0YXJ0IGV2ZW50LlwiKTtcbn1cbmV4cG9ydHMuaGFuZGxlQ29udmVyc2VTdHJlYW1Db250ZW50QmxvY2tTdGFydCA9IGhhbmRsZUNvbnZlcnNlU3RyZWFtQ29udGVudEJsb2NrU3RhcnQ7XG5mdW5jdGlvbiBoYW5kbGVDb252ZXJzZVN0cmVhbU1ldGFkYXRhKG1ldGFkYXRhLCBleHRyYSkge1xuICAgIGNvbnN0IGlucHV0VG9rZW5zID0gbWV0YWRhdGEudXNhZ2U/LmlucHV0VG9rZW5zID8/IDA7XG4gICAgY29uc3Qgb3V0cHV0VG9rZW5zID0gbWV0YWRhdGEudXNhZ2U/Lm91dHB1dFRva2VucyA/PyAwO1xuICAgIGNvbnN0IHVzYWdlX21ldGFkYXRhID0ge1xuICAgICAgICBpbnB1dF90b2tlbnM6IGlucHV0VG9rZW5zLFxuICAgICAgICBvdXRwdXRfdG9rZW5zOiBvdXRwdXRUb2tlbnMsXG4gICAgICAgIHRvdGFsX3Rva2VuczogbWV0YWRhdGEudXNhZ2U/LnRvdGFsVG9rZW5zID8/IGlucHV0VG9rZW5zICsgb3V0cHV0VG9rZW5zLFxuICAgIH07XG4gICAgcmV0dXJuIG5ldyBvdXRwdXRzXzEuQ2hhdEdlbmVyYXRpb25DaHVuayh7XG4gICAgICAgIHRleHQ6IFwiXCIsXG4gICAgICAgIG1lc3NhZ2U6IG5ldyBtZXNzYWdlc18xLkFJTWVzc2FnZUNodW5rKHtcbiAgICAgICAgICAgIGNvbnRlbnQ6IFwiXCIsXG4gICAgICAgICAgICB1c2FnZV9tZXRhZGF0YTogZXh0cmEuc3RyZWFtVXNhZ2UgPyB1c2FnZV9tZXRhZGF0YSA6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIHJlc3BvbnNlX21ldGFkYXRhOiB7XG4gICAgICAgICAgICAgICAgLy8gVXNlIHRoZSBzYW1lIGtleSBhcyByZXR1cm5lZCBmcm9tIHRoZSBDb252ZXJzZSBBUElcbiAgICAgICAgICAgICAgICBtZXRhZGF0YSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0pLFxuICAgIH0pO1xufVxuZXhwb3J0cy5oYW5kbGVDb252ZXJzZVN0cmVhbU1ldGFkYXRhID0gaGFuZGxlQ29udmVyc2VTdHJlYW1NZXRhZGF0YTtcbmZ1bmN0aW9uIGJlZHJvY2tSZWFzb25pbmdEZWx0YVRvTGFuZ2NoYWluUGFydGlhbFJlYXNvbmluZ0Jsb2NrKHJlYXNvbmluZ0NvbnRlbnQpIHtcbiAgICBjb25zdCB7IHRleHQsIHJlZGFjdGVkQ29udGVudCwgc2lnbmF0dXJlIH0gPSByZWFzb25pbmdDb250ZW50O1xuICAgIGlmICh0eXBlb2YgdGV4dCA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdHlwZTogXCJyZWFzb25pbmdfY29udGVudFwiLFxuICAgICAgICAgICAgcmVhc29uaW5nVGV4dDogeyB0ZXh0IH0sXG4gICAgICAgIH07XG4gICAgfVxuICAgIGlmIChzaWduYXR1cmUpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHR5cGU6IFwicmVhc29uaW5nX2NvbnRlbnRcIixcbiAgICAgICAgICAgIHJlYXNvbmluZ1RleHQ6IHsgc2lnbmF0dXJlIH0sXG4gICAgICAgIH07XG4gICAgfVxuICAgIGlmIChyZWRhY3RlZENvbnRlbnQpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHR5cGU6IFwicmVhc29uaW5nX2NvbnRlbnRcIixcbiAgICAgICAgICAgIHJlZGFjdGVkQ29udGVudDogQnVmZmVyLmZyb20ocmVkYWN0ZWRDb250ZW50KS50b1N0cmluZyhcImJhc2U2NFwiKSxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCByZWFzb25pbmcgY29udGVudFwiKTtcbn1cbmV4cG9ydHMuYmVkcm9ja1JlYXNvbmluZ0RlbHRhVG9MYW5nY2hhaW5QYXJ0aWFsUmVhc29uaW5nQmxvY2sgPSBiZWRyb2NrUmVhc29uaW5nRGVsdGFUb0xhbmdjaGFpblBhcnRpYWxSZWFzb25pbmdCbG9jaztcbmZ1bmN0aW9uIGJlZHJvY2tSZWFzb25pbmdCbG9ja1RvTGFuZ2NoYWluUmVhc29uaW5nQmxvY2socmVhc29uaW5nQ29udGVudCkge1xuICAgIGNvbnN0IHsgcmVhc29uaW5nVGV4dCwgcmVkYWN0ZWRDb250ZW50IH0gPSByZWFzb25pbmdDb250ZW50O1xuICAgIGlmIChyZWFzb25pbmdUZXh0KSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0eXBlOiBcInJlYXNvbmluZ19jb250ZW50XCIsXG4gICAgICAgICAgICByZWFzb25pbmdUZXh0OiByZWFzb25pbmdUZXh0LFxuICAgICAgICB9O1xuICAgIH1cbiAgICBpZiAocmVkYWN0ZWRDb250ZW50KSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0eXBlOiBcInJlYXNvbmluZ19jb250ZW50XCIsXG4gICAgICAgICAgICByZWRhY3RlZENvbnRlbnQ6IEJ1ZmZlci5mcm9tKHJlZGFjdGVkQ29udGVudCkudG9TdHJpbmcoXCJiYXNlNjRcIiksXG4gICAgICAgIH07XG4gICAgfVxuICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgcmVhc29uaW5nIGNvbnRlbnRcIik7XG59XG5leHBvcnRzLmJlZHJvY2tSZWFzb25pbmdCbG9ja1RvTGFuZ2NoYWluUmVhc29uaW5nQmxvY2sgPSBiZWRyb2NrUmVhc29uaW5nQmxvY2tUb0xhbmdjaGFpblJlYXNvbmluZ0Jsb2NrO1xuZnVuY3Rpb24gbGFuZ2NoYWluUmVhc29uaW5nQmxvY2tUb0JlZHJvY2tSZWFzb25pbmdCbG9jayhjb250ZW50KSB7XG4gICAgaWYgKGNvbnRlbnQudHlwZSAhPT0gXCJyZWFzb25pbmdfY29udGVudFwiKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgcmVhc29uaW5nIGNvbnRlbnRcIik7XG4gICAgfVxuICAgIGlmIChcInJlYXNvbmluZ1RleHRcIiBpbiBjb250ZW50KSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICByZWFzb25pbmdUZXh0OiBjb250ZW50LnJlYXNvbmluZ1RleHQsXG4gICAgICAgIH07XG4gICAgfVxuICAgIGlmIChcInJlZGFjdGVkQ29udGVudFwiIGluIGNvbnRlbnQpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHJlZGFjdGVkQ29udGVudDogQnVmZmVyLmZyb20oY29udGVudC5yZWRhY3RlZENvbnRlbnQsIFwiYmFzZTY0XCIpLFxuICAgICAgICB9O1xuICAgIH1cbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJJbnZhbGlkIHJlYXNvbmluZyBjb250ZW50XCIpO1xufVxuZXhwb3J0cy5sYW5nY2hhaW5SZWFzb25pbmdCbG9ja1RvQmVkcm9ja1JlYXNvbmluZ0Jsb2NrID0gbGFuZ2NoYWluUmVhc29uaW5nQmxvY2tUb0JlZHJvY2tSZWFzb25pbmdCbG9jaztcbmZ1bmN0aW9uIGNvbmNhdGVuYXRlTGFuZ2NoYWluUmVhc29uaW5nQmxvY2tzKGNvbnRlbnQpIHtcbiAgICBjb25zdCBjb25jYXRlbmF0ZWRCbG9ja3MgPSBbXTtcbiAgICBsZXQgY29uY2F0ZW5hdGVkQmxvY2sgPSB7fTtcbiAgICBmb3IgKGNvbnN0IGJsb2NrIG9mIGNvbnRlbnQpIHtcbiAgICAgICAgaWYgKGJsb2NrLnR5cGUgIT09IFwicmVhc29uaW5nX2NvbnRlbnRcIikge1xuICAgICAgICAgICAgLy8gaWYgaXQncyBzb21lIG90aGVyIGJsb2NrIHR5cGUsIGVuZCB0aGUgY3VycmVudCBibG9jaywgYnV0IGtlZXAgaXQgc28gd2UgcHJlc2VydmUgb3JkZXJcbiAgICAgICAgICAgIGlmIChPYmplY3Qua2V5cyhjb25jYXRlbmF0ZWRCbG9jaykubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgIGNvbmNhdGVuYXRlZEJsb2Nrcy5wdXNoKGNvbmNhdGVuYXRlZEJsb2NrKTtcbiAgICAgICAgICAgICAgICBjb25jYXRlbmF0ZWRCbG9jayA9IHt9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uY2F0ZW5hdGVkQmxvY2tzLnB1c2goYmxvY2spO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgLy8gbm9uLXJlZGFjdGVkIGJsb2NrXG4gICAgICAgIGlmIChcInJlYXNvbmluZ1RleHRcIiBpbiBibG9jayAmJiB0eXBlb2YgYmxvY2sucmVhc29uaW5nVGV4dCA9PT0gXCJvYmplY3RcIikge1xuICAgICAgICAgICAgaWYgKFwicmVkYWN0ZWRDb250ZW50XCIgaW4gY29uY2F0ZW5hdGVkQmxvY2spIHtcbiAgICAgICAgICAgICAgICAvLyBuZXcgdHlwZSBvZiBibG9jaywgc28gZW5kIHRoZSBwcmV2aW91cyBvbmVcbiAgICAgICAgICAgICAgICBjb25jYXRlbmF0ZWRCbG9ja3MucHVzaChjb25jYXRlbmF0ZWRCbG9jayk7XG4gICAgICAgICAgICAgICAgY29uY2F0ZW5hdGVkQmxvY2sgPSB7fTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHsgdGV4dCwgc2lnbmF0dXJlIH0gPSBibG9jay5yZWFzb25pbmdUZXh0O1xuICAgICAgICAgICAgY29uc3QgeyB0ZXh0OiBwcmV2VGV4dCwgc2lnbmF0dXJlOiBwcmV2U2lnbmF0dXJlIH0gPSAoXCJyZWFzb25pbmdUZXh0XCIgaW4gY29uY2F0ZW5hdGVkQmxvY2tcbiAgICAgICAgICAgICAgICA/IGNvbmNhdGVuYXRlZEJsb2NrLnJlYXNvbmluZ1RleHRcbiAgICAgICAgICAgICAgICA6IHt9KTtcbiAgICAgICAgICAgIGNvbmNhdGVuYXRlZEJsb2NrID0ge1xuICAgICAgICAgICAgICAgIHR5cGU6IFwicmVhc29uaW5nX2NvbnRlbnRcIixcbiAgICAgICAgICAgICAgICByZWFzb25pbmdUZXh0OiB7XG4gICAgICAgICAgICAgICAgICAgIC4uLihjb25jYXRlbmF0ZWRCbG9ja1xuICAgICAgICAgICAgICAgICAgICAgICAgLnJlYXNvbmluZ1RleHQgPz8ge30pLFxuICAgICAgICAgICAgICAgICAgICAuLi4ocHJldlRleHQgIT09IHVuZGVmaW5lZCB8fCB0ZXh0ICE9PSB1bmRlZmluZWRcbiAgICAgICAgICAgICAgICAgICAgICAgID8geyB0ZXh0OiAocHJldlRleHQgPz8gXCJcIikgKyAodGV4dCA/PyBcIlwiKSB9XG4gICAgICAgICAgICAgICAgICAgICAgICA6IHt9KSxcbiAgICAgICAgICAgICAgICAgICAgLi4uKHByZXZTaWduYXR1cmUgIT09IHVuZGVmaW5lZCB8fCBzaWduYXR1cmUgIT09IHVuZGVmaW5lZFxuICAgICAgICAgICAgICAgICAgICAgICAgPyB7IHNpZ25hdHVyZTogKHByZXZTaWduYXR1cmUgPz8gXCJcIikgKyAoc2lnbmF0dXJlID8/IFwiXCIpIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIDoge30pLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgLy8gaWYgYSBwYXJ0aWFsIGJsb2NrIGNodW5rIGhhcyBhIHNpZ25hdHVyZSwgdGhlIG5leHQgb25lIHdpbGwgYmVnaW4gYSBuZXcgcmVhc29uaW5nIGJsb2NrLlxuICAgICAgICAgICAgLy8gZnVsbCBibG9ja3MgYWx3YXlzIGhhdmUgc2lnbmF0dXJlcywgc28gd2Ugc3RhcnQgb25lIG5vdywgYW55d2F5XG4gICAgICAgICAgICBpZiAoXCJzaWduYXR1cmVcIiBpbiBibG9jay5yZWFzb25pbmdUZXh0KSB7XG4gICAgICAgICAgICAgICAgY29uY2F0ZW5hdGVkQmxvY2tzLnB1c2goY29uY2F0ZW5hdGVkQmxvY2spO1xuICAgICAgICAgICAgICAgIGNvbmNhdGVuYXRlZEJsb2NrID0ge307XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKFwicmVkYWN0ZWRDb250ZW50XCIgaW4gYmxvY2spIHtcbiAgICAgICAgICAgIGlmIChcInJlYXNvbmluZ1RleHRcIiBpbiBjb25jYXRlbmF0ZWRCbG9jaykge1xuICAgICAgICAgICAgICAgIC8vIE5ldyB0eXBlIG9mIGJsb2NrLCBzbyBlbmQgdGhlIHByZXZpb3VzIG9uZS4gV2Ugc2hvdWxkJ3QgcmVhbGx5IGhpdFxuICAgICAgICAgICAgICAgIC8vIHRoaXMsIGFzIHdlJ2xsIGhhdmUgY3JlYXRlZCBhIG5ldyBibG9jayB1cG9uIGVuY291bnRlcmluZyB0aGVcbiAgICAgICAgICAgICAgICAvLyBzaWduYXR1cmUgYWJvdmUsIGJ1dCBiZXR0ZXIgc2FmZSB0aGFuIHNvcnJ5LlxuICAgICAgICAgICAgICAgIGNvbmNhdGVuYXRlZEJsb2Nrcy5wdXNoKGNvbmNhdGVuYXRlZEJsb2NrKTtcbiAgICAgICAgICAgICAgICBjb25jYXRlbmF0ZWRCbG9jayA9IHt9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgeyByZWRhY3RlZENvbnRlbnQgfSA9IGJsb2NrO1xuICAgICAgICAgICAgY29uc3QgcHJldlJlZGFjdGVkQ29udGVudCA9IChcInJlZGFjdGVkQ29udGVudFwiIGluIGNvbmNhdGVuYXRlZEJsb2NrXG4gICAgICAgICAgICAgICAgPyBjb25jYXRlbmF0ZWRCbG9jay5yZWRhY3RlZENvbnRlbnRcbiAgICAgICAgICAgICAgICA6IFwiXCIpO1xuICAgICAgICAgICAgY29uY2F0ZW5hdGVkQmxvY2sgPSB7XG4gICAgICAgICAgICAgICAgdHlwZTogXCJyZWFzb25pbmdfY29udGVudFwiLFxuICAgICAgICAgICAgICAgIHJlZGFjdGVkQ29udGVudDogcHJldlJlZGFjdGVkQ29udGVudCArIHJlZGFjdGVkQ29udGVudCxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9XG4gICAgaWYgKE9iamVjdC5rZXlzKGNvbmNhdGVuYXRlZEJsb2NrKS5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbmNhdGVuYXRlZEJsb2Nrcy5wdXNoKGNvbmNhdGVuYXRlZEJsb2NrKTtcbiAgICB9XG4gICAgcmV0dXJuIGNvbmNhdGVuYXRlZEJsb2Nrcztcbn1cbmV4cG9ydHMuY29uY2F0ZW5hdGVMYW5nY2hhaW5SZWFzb25pbmdCbG9ja3MgPSBjb25jYXRlbmF0ZUxhbmdjaGFpblJlYXNvbmluZ0Jsb2NrcztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/common.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/embeddings.cjs":
/*!**************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/embeddings.cjs ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BedrockEmbeddings = void 0;\nconst client_bedrock_runtime_1 = __webpack_require__(/*! @aws-sdk/client-bedrock-runtime */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-runtime/dist-es/index.js\");\nconst embeddings_1 = __webpack_require__(/*! @langchain/core/embeddings */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/embeddings.cjs\");\n/**\n * Class that extends the Embeddings class and provides methods for\n * generating embeddings using the Bedrock API.\n * @example\n * ```typescript\n * const embeddings = new BedrockEmbeddings({\n *   region: \"your-aws-region\",\n *   credentials: {\n *     accessKeyId: \"your-access-key-id\",\n *     secretAccessKey: \"your-secret-access-key\",\n *   },\n *   model: \"amazon.titan-embed-text-v1\",\n * });\n *\n * // Embed a query and log the result\n * const res = await embeddings.embedQuery(\n *   \"What would be a good company name for a company that makes colorful socks?\"\n * );\n * console.log({ res });\n * ```\n */\nclass BedrockEmbeddings extends embeddings_1.Embeddings {\n    constructor(fields) {\n        super(fields ?? {});\n        Object.defineProperty(this, \"model\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"client\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"batchSize\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 512\n        });\n        this.model = fields?.model ?? \"amazon.titan-embed-text-v1\";\n        this.client =\n            fields?.client ??\n                new client_bedrock_runtime_1.BedrockRuntimeClient({\n                    region: fields?.region,\n                    credentials: fields?.credentials,\n                });\n    }\n    /**\n     * Protected method to make a request to the Bedrock API to generate\n     * embeddings. Handles the retry logic and returns the response from the\n     * API.\n     * @param request Request to send to the Bedrock API.\n     * @returns Promise that resolves to the response from the API.\n     */\n    async _embedText(text) {\n        return this.caller.call(async () => {\n            try {\n                // replace newlines, which can negatively affect performance.\n                const cleanedText = text.replace(/\\n/g, \" \");\n                const res = await this.client.send(new client_bedrock_runtime_1.InvokeModelCommand({\n                    modelId: this.model,\n                    body: JSON.stringify({\n                        inputText: cleanedText,\n                    }),\n                    contentType: \"application/json\",\n                    accept: \"application/json\",\n                }));\n                const body = new TextDecoder().decode(res.body);\n                return JSON.parse(body).embedding;\n            }\n            catch (e) {\n                console.error({\n                    error: e,\n                });\n                // eslint-disable-next-line no-instanceof/no-instanceof\n                if (e instanceof Error) {\n                    throw new Error(`An error occurred while embedding documents with Bedrock: ${e.message}`);\n                }\n                throw new Error(\"An error occurred while embedding documents with Bedrock\");\n            }\n        });\n    }\n    /**\n     * Method that takes a document as input and returns a promise that\n     * resolves to an embedding for the document. It calls the _embedText\n     * method with the document as the input.\n     * @param document Document for which to generate an embedding.\n     * @returns Promise that resolves to an embedding for the input document.\n     */\n    embedQuery(document) {\n        return this.caller.callWithOptions({}, this._embedText.bind(this), document);\n    }\n    /**\n     * Method to generate embeddings for an array of texts. Calls _embedText\n     * method which batches and handles retry logic when calling the AWS Bedrock API.\n     * @param documents Array of texts for which to generate embeddings.\n     * @returns Promise that resolves to a 2D array of embeddings for each input document.\n     */\n    async embedDocuments(documents) {\n        return Promise.all(documents.map((document) => this._embedText(document)));\n    }\n}\nexports.BedrockEmbeddings = BedrockEmbeddings;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/embeddings.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/index.cjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/index.cjs ***!
  \*********************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./chat_models.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/chat_models.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./types.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/types.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./retrievers/index.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/retrievers/index.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./embeddings.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/embeddings.cjs\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/index.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/retrievers/bedrock.cjs":
/*!**********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/retrievers/bedrock.cjs ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AmazonKnowledgeBaseRetriever = void 0;\nconst client_bedrock_agent_runtime_1 = __webpack_require__(/*! @aws-sdk/client-bedrock-agent-runtime */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-bedrock-agent-runtime@3.830.0/node_modules/@aws-sdk/client-bedrock-agent-runtime/dist-es/index.js\");\nconst retrievers_1 = __webpack_require__(/*! @langchain/core/retrievers */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/retrievers.cjs\");\n/**\n * Class for interacting with Amazon Bedrock Knowledge Bases, a RAG workflow oriented service\n * provided by AWS. Extends the BaseRetriever class.\n * @example\n * ```typescript\n * const retriever = new AmazonKnowledgeBaseRetriever({\n *   topK: 10,\n *   knowledgeBaseId: \"YOUR_KNOWLEDGE_BASE_ID\",\n *   region: \"us-east-2\",\n *   clientOptions: {\n *     credentials: {\n *       accessKeyId: \"YOUR_ACCESS_KEY_ID\",\n *       secretAccessKey: \"YOUR_SECRET_ACCESS_KEY\",\n *     },\n *   },\n * });\n *\n * const docs = await retriever.getRelevantDocuments(\"How are clouds formed?\");\n * ```\n */\nclass AmazonKnowledgeBaseRetriever extends retrievers_1.BaseRetriever {\n    static lc_name() {\n        return \"AmazonKnowledgeBaseRetriever\";\n    }\n    constructor({ knowledgeBaseId, topK = 10, clientOptions, region, filter, overrideSearchType, }) {\n        super();\n        Object.defineProperty(this, \"lc_namespace\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: [\"langchain\", \"retrievers\", \"amazon_bedrock_knowledge_base\"]\n        });\n        Object.defineProperty(this, \"knowledgeBaseId\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"topK\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"bedrockAgentRuntimeClient\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"filter\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"overrideSearchType\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.topK = topK;\n        this.filter = filter;\n        this.overrideSearchType = overrideSearchType;\n        this.bedrockAgentRuntimeClient = new client_bedrock_agent_runtime_1.BedrockAgentRuntimeClient({\n            region,\n            ...clientOptions,\n        });\n        this.knowledgeBaseId = knowledgeBaseId;\n    }\n    /**\n     * Cleans the result text by replacing sequences of whitespace with a\n     * single space and removing ellipses.\n     * @param resText The result text to clean.\n     * @returns The cleaned result text.\n     */\n    cleanResult(resText) {\n        const res = resText.replace(/\\s+/g, \" \").replace(/\\.\\.\\./g, \"\");\n        return res;\n    }\n    async queryKnowledgeBase(query, topK, filter, overrideSearchType) {\n        const retrieveCommand = new client_bedrock_agent_runtime_1.RetrieveCommand({\n            knowledgeBaseId: this.knowledgeBaseId,\n            retrievalQuery: {\n                text: query,\n            },\n            retrievalConfiguration: {\n                vectorSearchConfiguration: {\n                    numberOfResults: topK,\n                    overrideSearchType,\n                    filter,\n                },\n            },\n        });\n        const retrieveResponse = await this.bedrockAgentRuntimeClient.send(retrieveCommand);\n        return (retrieveResponse.retrievalResults?.map((result) => {\n            let source;\n            switch (result.location?.type) {\n                case \"CONFLUENCE\":\n                    source = result.location?.confluenceLocation?.url;\n                    break;\n                case \"S3\":\n                    source = result.location?.s3Location?.uri;\n                    break;\n                case \"SALESFORCE\":\n                    source = result.location?.salesforceLocation?.url;\n                    break;\n                case \"SHAREPOINT\":\n                    source = result.location?.sharePointLocation?.url;\n                    break;\n                case \"WEB\":\n                    source = result.location?.webLocation?.url;\n                    break;\n                default:\n                    source = result.location?.s3Location?.uri;\n                    break;\n            }\n            return {\n                pageContent: this.cleanResult(result.content?.text || \"\"),\n                metadata: {\n                    source,\n                    score: result.score,\n                    ...result.metadata,\n                },\n            };\n        }) ?? []);\n    }\n    async _getRelevantDocuments(query) {\n        const docs = await this.queryKnowledgeBase(query, this.topK, this.filter, this.overrideSearchType);\n        return docs;\n    }\n}\nexports.AmazonKnowledgeBaseRetriever = AmazonKnowledgeBaseRetriever;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/retrievers/bedrock.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/retrievers/index.cjs":
/*!********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/retrievers/index.cjs ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./bedrock.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/retrievers/bedrock.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./kendra.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/retrievers/kendra.cjs\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/retrievers/index.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/retrievers/kendra.cjs":
/*!*********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/retrievers/kendra.cjs ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AmazonKendraRetriever = void 0;\nconst client_kendra_1 = __webpack_require__(/*! @aws-sdk/client-kendra */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-kendra@3.830.0/node_modules/@aws-sdk/client-kendra/dist-es/index.js\");\nconst retrievers_1 = __webpack_require__(/*! @langchain/core/retrievers */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/retrievers.cjs\");\nconst documents_1 = __webpack_require__(/*! @langchain/core/documents */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/documents.cjs\");\n/**\n * Class for interacting with Amazon Kendra, an intelligent search service\n * provided by AWS. Extends the BaseRetriever class.\n * @example\n * ```typescript\n * const retriever = new AmazonKendraRetriever({\n *   topK: 10,\n *   indexId: \"YOUR_INDEX_ID\",\n *   region: \"us-east-2\",\n *   clientOptions: {\n *     credentials: {\n *       accessKeyId: \"YOUR_ACCESS_KEY_ID\",\n *       secretAccessKey: \"YOUR_SECRET_ACCESS_KEY\",\n *     },\n *   },\n * });\n *\n * const docs = await retriever.getRelevantDocuments(\"How are clouds formed?\");\n * ```\n */\nclass AmazonKendraRetriever extends retrievers_1.BaseRetriever {\n    static lc_name() {\n        return \"AmazonKendraRetriever\";\n    }\n    constructor({ indexId, topK = 10, clientOptions, attributeFilter, region, }) {\n        super();\n        Object.defineProperty(this, \"lc_namespace\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: [\"langchain\", \"retrievers\", \"amazon_kendra\"]\n        });\n        Object.defineProperty(this, \"indexId\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"topK\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"kendraClient\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"attributeFilter\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        if (!region) {\n            throw new Error(\"Please pass regionName field to the constructor!\");\n        }\n        if (!indexId) {\n            throw new Error(\"Please pass Kendra Index Id to the constructor\");\n        }\n        this.topK = topK;\n        this.kendraClient = new client_kendra_1.KendraClient({\n            region,\n            ...clientOptions,\n        });\n        this.attributeFilter = attributeFilter;\n        this.indexId = indexId;\n    }\n    // A method to combine title and excerpt into a single string.\n    /**\n     * Combines title and excerpt into a single string.\n     * @param title The title of the document.\n     * @param excerpt An excerpt from the document.\n     * @returns A single string combining the title and excerpt.\n     */\n    combineText(title, excerpt) {\n        let text = \"\";\n        if (title) {\n            text += `Document Title: ${title}\\n`;\n        }\n        if (excerpt) {\n            text += `Document Excerpt: \\n${excerpt}\\n`;\n        }\n        return text;\n    }\n    // A method to clean the result text by replacing sequences of whitespace with a single space and removing ellipses.\n    /**\n     * Cleans the result text by replacing sequences of whitespace with a\n     * single space and removing ellipses.\n     * @param resText The result text to clean.\n     * @returns The cleaned result text.\n     */\n    cleanResult(resText) {\n        const res = resText.replace(/\\s+/g, \" \").replace(/\\.\\.\\./g, \"\");\n        return res;\n    }\n    // A method to extract the attribute value from a DocumentAttributeValue object.\n    /**\n     * Extracts the attribute value from a DocumentAttributeValue object.\n     * @param value The DocumentAttributeValue object to extract the value from.\n     * @returns The extracted attribute value.\n     */\n    getDocAttributeValue(value) {\n        if (value.DateValue) {\n            return value.DateValue;\n        }\n        if (value.LongValue) {\n            return value.LongValue;\n        }\n        if (value.StringListValue) {\n            return value.StringListValue;\n        }\n        if (value.StringValue) {\n            return value.StringValue;\n        }\n        return \"\";\n    }\n    // A method to extract the attribute key-value pairs from an array of DocumentAttribute objects.\n    /**\n     * Extracts the attribute key-value pairs from an array of\n     * DocumentAttribute objects.\n     * @param documentAttributes The array of DocumentAttribute objects to extract the key-value pairs from.\n     * @returns An object containing the extracted attribute key-value pairs.\n     */\n    getDocAttributes(documentAttributes) {\n        const attributes = {};\n        if (documentAttributes) {\n            for (const attr of documentAttributes) {\n                if (attr.Key && attr.Value) {\n                    attributes[attr.Key] = this.getDocAttributeValue(attr.Value);\n                }\n            }\n        }\n        return attributes;\n    }\n    // A method to convert a RetrieveResultItem object into a Document object.\n    /**\n     * Converts a RetrieveResultItem object into a Document object.\n     * @param item The RetrieveResultItem object to convert.\n     * @returns A Document object.\n     */\n    convertRetrieverItem(item) {\n        const title = item.DocumentTitle || \"\";\n        const excerpt = item.Content ? this.cleanResult(item.Content) : \"\";\n        const pageContent = this.combineText(title, excerpt);\n        const source = item.DocumentURI;\n        const attributes = this.getDocAttributes(item.DocumentAttributes);\n        const metadata = {\n            source,\n            title,\n            excerpt,\n            document_attributes: attributes,\n        };\n        return new documents_1.Document({ pageContent, metadata });\n    }\n    // A method to extract the top-k documents from a RetrieveCommandOutput object.\n    /**\n     * Extracts the top-k documents from a RetrieveCommandOutput object.\n     * @param response The RetrieveCommandOutput object to extract the documents from.\n     * @param pageSize The number of documents to extract.\n     * @returns An array of Document objects.\n     */\n    getRetrieverDocs(response, pageSize) {\n        if (!response.ResultItems)\n            return [];\n        const { length } = response.ResultItems;\n        const count = length < pageSize ? length : pageSize;\n        return response.ResultItems.slice(0, count).map((item) => this.convertRetrieverItem(item));\n    }\n    // A method to extract the excerpt text from a QueryResultItem object.\n    /**\n     * Extracts the excerpt text from a QueryResultItem object.\n     * @param item The QueryResultItem object to extract the excerpt text from.\n     * @returns The extracted excerpt text.\n     */\n    getQueryItemExcerpt(item) {\n        if (item.AdditionalAttributes &&\n            item.AdditionalAttributes.length &&\n            item.AdditionalAttributes[0].Key === \"AnswerText\") {\n            if (!item.AdditionalAttributes) {\n                return \"\";\n            }\n            if (!item.AdditionalAttributes[0]) {\n                return \"\";\n            }\n            return this.cleanResult(item.AdditionalAttributes[0].Value?.TextWithHighlightsValue?.Text || \"\");\n        }\n        else if (item.DocumentExcerpt) {\n            return this.cleanResult(item.DocumentExcerpt.Text || \"\");\n        }\n        else {\n            return \"\";\n        }\n    }\n    // A method to convert a QueryResultItem object into a Document object.\n    /**\n     * Converts a QueryResultItem object into a Document object.\n     * @param item The QueryResultItem object to convert.\n     * @returns A Document object.\n     */\n    convertQueryItem(item) {\n        const title = item.DocumentTitle?.Text || \"\";\n        const excerpt = this.getQueryItemExcerpt(item);\n        const pageContent = this.combineText(title, excerpt);\n        const source = item.DocumentURI;\n        const attributes = this.getDocAttributes(item.DocumentAttributes);\n        const metadata = {\n            source,\n            title,\n            excerpt,\n            document_attributes: attributes,\n        };\n        return new documents_1.Document({ pageContent, metadata });\n    }\n    // A method to extract the top-k documents from a QueryCommandOutput object.\n    /**\n     * Extracts the top-k documents from a QueryCommandOutput object.\n     * @param response The QueryCommandOutput object to extract the documents from.\n     * @param pageSize The number of documents to extract.\n     * @returns An array of Document objects.\n     */\n    getQueryDocs(response, pageSize) {\n        if (!response.ResultItems)\n            return [];\n        const { length } = response.ResultItems;\n        const count = length < pageSize ? length : pageSize;\n        return response.ResultItems.slice(0, count).map((item) => this.convertQueryItem(item));\n    }\n    // A method to send a retrieve or query request to Kendra and return the top-k documents.\n    /**\n     * Sends a retrieve or query request to Kendra and returns the top-k\n     * documents.\n     * @param query The query to send to Kendra.\n     * @param topK The number of top documents to return.\n     * @param attributeFilter Optional filter to apply when retrieving documents.\n     * @returns A Promise that resolves to an array of Document objects.\n     */\n    async queryKendra(query, topK, attributeFilter) {\n        const retrieveCommand = new client_kendra_1.RetrieveCommand({\n            IndexId: this.indexId,\n            QueryText: query,\n            PageSize: topK,\n            AttributeFilter: attributeFilter,\n        });\n        const retrieveResponse = await this.kendraClient.send(retrieveCommand);\n        const retriveLength = retrieveResponse.ResultItems?.length;\n        if (retriveLength === 0) {\n            // Retrieve API returned 0 results, call query API\n            const queryCommand = new client_kendra_1.QueryCommand({\n                IndexId: this.indexId,\n                QueryText: query,\n                PageSize: topK,\n                AttributeFilter: attributeFilter,\n            });\n            const queryResponse = await this.kendraClient.send(queryCommand);\n            return this.getQueryDocs(queryResponse, this.topK);\n        }\n        else {\n            return this.getRetrieverDocs(retrieveResponse, this.topK);\n        }\n    }\n    async _getRelevantDocuments(query) {\n        const docs = await this.queryKendra(query, this.topK, this.attributeFilter);\n        return docs;\n    }\n}\nexports.AmazonKendraRetriever = AmazonKendraRetriever;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/retrievers/kendra.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/types.cjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/types.cjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbithd3NAMC4xLjExX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fL25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2F3cy9kaXN0L3R5cGVzLmNqcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BsYW5nY2hhaW4rYXdzQDAuMS4xMV9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfXy9ub2RlX21vZHVsZXMvQGxhbmdjaGFpbi9hd3MvZGlzdC90eXBlcy5janMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/types.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/index.cjs":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/index.cjs ***!
  \****************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/index.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/dist/index.cjs\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbithd3NAMC4xLjExX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fL25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2F3cy9pbmRleC5janMiLCJtYXBwaW5ncyI6IkFBQUEsdU5BQTRDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbithd3NAMC4xLjExX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fL25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2F3cy9pbmRleC5janMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvaW5kZXguY2pzJyk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+aws@0.1.11_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8__/node_modules/@langchain/aws/index.cjs\n");

/***/ })

};
;