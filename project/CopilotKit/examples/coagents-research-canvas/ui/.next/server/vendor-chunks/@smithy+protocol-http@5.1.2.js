"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+protocol-http@5.1.2";
exports.ids = ["vendor-chunks/@smithy+protocol-http@5.1.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/Field.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/Field.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Field: () => (/* binding */ Field)\n/* harmony export */ });\n/* harmony import */ var _smithy_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/types */ \"(rsc)/./node_modules/.pnpm/@smithy+types@4.3.1/node_modules/@smithy/types/dist-es/index.js\");\n\nclass Field {\n    constructor({ name, kind = _smithy_types__WEBPACK_IMPORTED_MODULE_0__.FieldPosition.HEADER, values = [] }) {\n        this.name = name;\n        this.kind = kind;\n        this.values = values;\n    }\n    add(value) {\n        this.values.push(value);\n    }\n    set(values) {\n        this.values = values;\n    }\n    remove(value) {\n        this.values = this.values.filter((v) => v !== value);\n    }\n    toString() {\n        return this.values.map((v) => (v.includes(\",\") || v.includes(\" \") ? `\"${v}\"` : v)).join(\", \");\n    }\n    get() {\n        return this.values;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm90b2NvbC1odHRwQDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3Byb3RvY29sLWh0dHAvZGlzdC1lcy9GaWVsZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4QztBQUN2QztBQUNQLGtCQUFrQixhQUFhLHdEQUFhLHNCQUFzQjtBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0ZBQWdGLEVBQUU7QUFDbEY7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrcHJvdG9jb2wtaHR0cEA1LjEuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9wcm90b2NvbC1odHRwL2Rpc3QtZXMvRmllbGQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRmllbGRQb3NpdGlvbiB9IGZyb20gXCJAc21pdGh5L3R5cGVzXCI7XG5leHBvcnQgY2xhc3MgRmllbGQge1xuICAgIGNvbnN0cnVjdG9yKHsgbmFtZSwga2luZCA9IEZpZWxkUG9zaXRpb24uSEVBREVSLCB2YWx1ZXMgPSBbXSB9KSB7XG4gICAgICAgIHRoaXMubmFtZSA9IG5hbWU7XG4gICAgICAgIHRoaXMua2luZCA9IGtpbmQ7XG4gICAgICAgIHRoaXMudmFsdWVzID0gdmFsdWVzO1xuICAgIH1cbiAgICBhZGQodmFsdWUpIHtcbiAgICAgICAgdGhpcy52YWx1ZXMucHVzaCh2YWx1ZSk7XG4gICAgfVxuICAgIHNldCh2YWx1ZXMpIHtcbiAgICAgICAgdGhpcy52YWx1ZXMgPSB2YWx1ZXM7XG4gICAgfVxuICAgIHJlbW92ZSh2YWx1ZSkge1xuICAgICAgICB0aGlzLnZhbHVlcyA9IHRoaXMudmFsdWVzLmZpbHRlcigodikgPT4gdiAhPT0gdmFsdWUpO1xuICAgIH1cbiAgICB0b1N0cmluZygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudmFsdWVzLm1hcCgodikgPT4gKHYuaW5jbHVkZXMoXCIsXCIpIHx8IHYuaW5jbHVkZXMoXCIgXCIpID8gYFwiJHt2fVwiYCA6IHYpKS5qb2luKFwiLCBcIik7XG4gICAgfVxuICAgIGdldCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudmFsdWVzO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/Field.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/Fields.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/Fields.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Fields: () => (/* binding */ Fields)\n/* harmony export */ });\nclass Fields {\n    constructor({ fields = [], encoding = \"utf-8\" }) {\n        this.entries = {};\n        fields.forEach(this.setField.bind(this));\n        this.encoding = encoding;\n    }\n    setField(field) {\n        this.entries[field.name.toLowerCase()] = field;\n    }\n    getField(name) {\n        return this.entries[name.toLowerCase()];\n    }\n    removeField(name) {\n        delete this.entries[name.toLowerCase()];\n    }\n    getByType(kind) {\n        return Object.values(this.entries).filter((field) => field.kind === kind);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm90b2NvbC1odHRwQDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3Byb3RvY29sLWh0dHAvZGlzdC1lcy9GaWVsZHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1Asa0JBQWtCLGlDQUFpQztBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm90b2NvbC1odHRwQDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3Byb3RvY29sLWh0dHAvZGlzdC1lcy9GaWVsZHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIEZpZWxkcyB7XG4gICAgY29uc3RydWN0b3IoeyBmaWVsZHMgPSBbXSwgZW5jb2RpbmcgPSBcInV0Zi04XCIgfSkge1xuICAgICAgICB0aGlzLmVudHJpZXMgPSB7fTtcbiAgICAgICAgZmllbGRzLmZvckVhY2godGhpcy5zZXRGaWVsZC5iaW5kKHRoaXMpKTtcbiAgICAgICAgdGhpcy5lbmNvZGluZyA9IGVuY29kaW5nO1xuICAgIH1cbiAgICBzZXRGaWVsZChmaWVsZCkge1xuICAgICAgICB0aGlzLmVudHJpZXNbZmllbGQubmFtZS50b0xvd2VyQ2FzZSgpXSA9IGZpZWxkO1xuICAgIH1cbiAgICBnZXRGaWVsZChuYW1lKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmVudHJpZXNbbmFtZS50b0xvd2VyQ2FzZSgpXTtcbiAgICB9XG4gICAgcmVtb3ZlRmllbGQobmFtZSkge1xuICAgICAgICBkZWxldGUgdGhpcy5lbnRyaWVzW25hbWUudG9Mb3dlckNhc2UoKV07XG4gICAgfVxuICAgIGdldEJ5VHlwZShraW5kKSB7XG4gICAgICAgIHJldHVybiBPYmplY3QudmFsdWVzKHRoaXMuZW50cmllcykuZmlsdGVyKChmaWVsZCkgPT4gZmllbGQua2luZCA9PT0ga2luZCk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/Fields.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/extensions/httpExtensionConfiguration.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/extensions/httpExtensionConfiguration.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHttpHandlerExtensionConfiguration: () => (/* binding */ getHttpHandlerExtensionConfiguration),\n/* harmony export */   resolveHttpHandlerRuntimeConfig: () => (/* binding */ resolveHttpHandlerRuntimeConfig)\n/* harmony export */ });\nconst getHttpHandlerExtensionConfiguration = (runtimeConfig) => {\n    return {\n        setHttpHandler(handler) {\n            runtimeConfig.httpHandler = handler;\n        },\n        httpHandler() {\n            return runtimeConfig.httpHandler;\n        },\n        updateHttpClientConfig(key, value) {\n            runtimeConfig.httpHandler?.updateHttpClientConfig(key, value);\n        },\n        httpHandlerConfigs() {\n            return runtimeConfig.httpHandler.httpHandlerConfigs();\n        },\n    };\n};\nconst resolveHttpHandlerRuntimeConfig = (httpHandlerExtensionConfiguration) => {\n    return {\n        httpHandler: httpHandlerExtensionConfiguration.httpHandler(),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm90b2NvbC1odHRwQDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3Byb3RvY29sLWh0dHAvZGlzdC1lcy9leHRlbnNpb25zL2h0dHBFeHRlbnNpb25Db25maWd1cmF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3Byb3RvY29sLWh0dHBANS4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvcHJvdG9jb2wtaHR0cC9kaXN0LWVzL2V4dGVuc2lvbnMvaHR0cEV4dGVuc2lvbkNvbmZpZ3VyYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGdldEh0dHBIYW5kbGVyRXh0ZW5zaW9uQ29uZmlndXJhdGlvbiA9IChydW50aW1lQ29uZmlnKSA9PiB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgc2V0SHR0cEhhbmRsZXIoaGFuZGxlcikge1xuICAgICAgICAgICAgcnVudGltZUNvbmZpZy5odHRwSGFuZGxlciA9IGhhbmRsZXI7XG4gICAgICAgIH0sXG4gICAgICAgIGh0dHBIYW5kbGVyKCkge1xuICAgICAgICAgICAgcmV0dXJuIHJ1bnRpbWVDb25maWcuaHR0cEhhbmRsZXI7XG4gICAgICAgIH0sXG4gICAgICAgIHVwZGF0ZUh0dHBDbGllbnRDb25maWcoa2V5LCB2YWx1ZSkge1xuICAgICAgICAgICAgcnVudGltZUNvbmZpZy5odHRwSGFuZGxlcj8udXBkYXRlSHR0cENsaWVudENvbmZpZyhrZXksIHZhbHVlKTtcbiAgICAgICAgfSxcbiAgICAgICAgaHR0cEhhbmRsZXJDb25maWdzKCkge1xuICAgICAgICAgICAgcmV0dXJuIHJ1bnRpbWVDb25maWcuaHR0cEhhbmRsZXIuaHR0cEhhbmRsZXJDb25maWdzKCk7XG4gICAgICAgIH0sXG4gICAgfTtcbn07XG5leHBvcnQgY29uc3QgcmVzb2x2ZUh0dHBIYW5kbGVyUnVudGltZUNvbmZpZyA9IChodHRwSGFuZGxlckV4dGVuc2lvbkNvbmZpZ3VyYXRpb24pID0+IHtcbiAgICByZXR1cm4ge1xuICAgICAgICBodHRwSGFuZGxlcjogaHR0cEhhbmRsZXJFeHRlbnNpb25Db25maWd1cmF0aW9uLmh0dHBIYW5kbGVyKCksXG4gICAgfTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/extensions/httpExtensionConfiguration.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/extensions/index.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/extensions/index.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHttpHandlerExtensionConfiguration: () => (/* reexport safe */ _httpExtensionConfiguration__WEBPACK_IMPORTED_MODULE_0__.getHttpHandlerExtensionConfiguration),\n/* harmony export */   resolveHttpHandlerRuntimeConfig: () => (/* reexport safe */ _httpExtensionConfiguration__WEBPACK_IMPORTED_MODULE_0__.resolveHttpHandlerRuntimeConfig)\n/* harmony export */ });\n/* harmony import */ var _httpExtensionConfiguration__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./httpExtensionConfiguration */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/extensions/httpExtensionConfiguration.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm90b2NvbC1odHRwQDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3Byb3RvY29sLWh0dHAvZGlzdC1lcy9leHRlbnNpb25zL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrcHJvdG9jb2wtaHR0cEA1LjEuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9wcm90b2NvbC1odHRwL2Rpc3QtZXMvZXh0ZW5zaW9ucy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9odHRwRXh0ZW5zaW9uQ29uZmlndXJhdGlvblwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/extensions/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/httpHandler.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/httpHandler.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm90b2NvbC1odHRwQDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3Byb3RvY29sLWh0dHAvZGlzdC1lcy9odHRwSGFuZGxlci5qcyIsIm1hcHBpbmdzIjoiO0FBQVUiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3Byb3RvY29sLWh0dHBANS4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvcHJvdG9jb2wtaHR0cC9kaXN0LWVzL2h0dHBIYW5kbGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/httpHandler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/httpRequest.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/httpRequest.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpRequest: () => (/* binding */ HttpRequest)\n/* harmony export */ });\nclass HttpRequest {\n    constructor(options) {\n        this.method = options.method || \"GET\";\n        this.hostname = options.hostname || \"localhost\";\n        this.port = options.port;\n        this.query = options.query || {};\n        this.headers = options.headers || {};\n        this.body = options.body;\n        this.protocol = options.protocol\n            ? options.protocol.slice(-1) !== \":\"\n                ? `${options.protocol}:`\n                : options.protocol\n            : \"https:\";\n        this.path = options.path ? (options.path.charAt(0) !== \"/\" ? `/${options.path}` : options.path) : \"/\";\n        this.username = options.username;\n        this.password = options.password;\n        this.fragment = options.fragment;\n    }\n    static clone(request) {\n        const cloned = new HttpRequest({\n            ...request,\n            headers: { ...request.headers },\n        });\n        if (cloned.query) {\n            cloned.query = cloneQuery(cloned.query);\n        }\n        return cloned;\n    }\n    static isInstance(request) {\n        if (!request) {\n            return false;\n        }\n        const req = request;\n        return (\"method\" in req &&\n            \"protocol\" in req &&\n            \"hostname\" in req &&\n            \"path\" in req &&\n            typeof req[\"query\"] === \"object\" &&\n            typeof req[\"headers\"] === \"object\");\n    }\n    clone() {\n        return HttpRequest.clone(this);\n    }\n}\nfunction cloneQuery(query) {\n    return Object.keys(query).reduce((carry, paramName) => {\n        const param = query[paramName];\n        return {\n            ...carry,\n            [paramName]: Array.isArray(param) ? [...param] : param,\n        };\n    }, {});\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/httpRequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/httpResponse.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/httpResponse.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpResponse: () => (/* binding */ HttpResponse)\n/* harmony export */ });\nclass HttpResponse {\n    constructor(options) {\n        this.statusCode = options.statusCode;\n        this.reason = options.reason;\n        this.headers = options.headers || {};\n        this.body = options.body;\n    }\n    static isInstance(response) {\n        if (!response)\n            return false;\n        const resp = response;\n        return typeof resp.statusCode === \"number\" && typeof resp.headers === \"object\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm90b2NvbC1odHRwQDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3Byb3RvY29sLWh0dHAvZGlzdC1lcy9odHRwUmVzcG9uc2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3Byb3RvY29sLWh0dHBANS4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvcHJvdG9jb2wtaHR0cC9kaXN0LWVzL2h0dHBSZXNwb25zZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgSHR0cFJlc3BvbnNlIHtcbiAgICBjb25zdHJ1Y3RvcihvcHRpb25zKSB7XG4gICAgICAgIHRoaXMuc3RhdHVzQ29kZSA9IG9wdGlvbnMuc3RhdHVzQ29kZTtcbiAgICAgICAgdGhpcy5yZWFzb24gPSBvcHRpb25zLnJlYXNvbjtcbiAgICAgICAgdGhpcy5oZWFkZXJzID0gb3B0aW9ucy5oZWFkZXJzIHx8IHt9O1xuICAgICAgICB0aGlzLmJvZHkgPSBvcHRpb25zLmJvZHk7XG4gICAgfVxuICAgIHN0YXRpYyBpc0luc3RhbmNlKHJlc3BvbnNlKSB7XG4gICAgICAgIGlmICghcmVzcG9uc2UpXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIGNvbnN0IHJlc3AgPSByZXNwb25zZTtcbiAgICAgICAgcmV0dXJuIHR5cGVvZiByZXNwLnN0YXR1c0NvZGUgPT09IFwibnVtYmVyXCIgJiYgdHlwZW9mIHJlc3AuaGVhZGVycyA9PT0gXCJvYmplY3RcIjtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/httpResponse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Field: () => (/* reexport safe */ _Field__WEBPACK_IMPORTED_MODULE_1__.Field),\n/* harmony export */   Fields: () => (/* reexport safe */ _Fields__WEBPACK_IMPORTED_MODULE_2__.Fields),\n/* harmony export */   HttpRequest: () => (/* reexport safe */ _httpRequest__WEBPACK_IMPORTED_MODULE_4__.HttpRequest),\n/* harmony export */   HttpResponse: () => (/* reexport safe */ _httpResponse__WEBPACK_IMPORTED_MODULE_5__.HttpResponse),\n/* harmony export */   getHttpHandlerExtensionConfiguration: () => (/* reexport safe */ _extensions__WEBPACK_IMPORTED_MODULE_0__.getHttpHandlerExtensionConfiguration),\n/* harmony export */   isValidHostname: () => (/* reexport safe */ _isValidHostname__WEBPACK_IMPORTED_MODULE_6__.isValidHostname),\n/* harmony export */   resolveHttpHandlerRuntimeConfig: () => (/* reexport safe */ _extensions__WEBPACK_IMPORTED_MODULE_0__.resolveHttpHandlerRuntimeConfig)\n/* harmony export */ });\n/* harmony import */ var _extensions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./extensions */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/extensions/index.js\");\n/* harmony import */ var _Field__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Field */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/Field.js\");\n/* harmony import */ var _Fields__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Fields */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/Fields.js\");\n/* harmony import */ var _httpHandler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./httpHandler */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/httpHandler.js\");\n/* harmony import */ var _httpRequest__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./httpRequest */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/httpRequest.js\");\n/* harmony import */ var _httpResponse__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./httpResponse */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/httpResponse.js\");\n/* harmony import */ var _isValidHostname__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./isValidHostname */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/isValidHostname.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/types.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm90b2NvbC1odHRwQDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3Byb3RvY29sLWh0dHAvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNkI7QUFDTDtBQUNDO0FBQ0s7QUFDQTtBQUNDO0FBQ0c7QUFDViIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrcHJvdG9jb2wtaHR0cEA1LjEuMi9ub2RlX21vZHVsZXMvQHNtaXRoeS9wcm90b2NvbC1odHRwL2Rpc3QtZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vZXh0ZW5zaW9uc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vRmllbGRcIjtcbmV4cG9ydCAqIGZyb20gXCIuL0ZpZWxkc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vaHR0cEhhbmRsZXJcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2h0dHBSZXF1ZXN0XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9odHRwUmVzcG9uc2VcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2lzVmFsaWRIb3N0bmFtZVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHlwZXNcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/isValidHostname.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/isValidHostname.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isValidHostname: () => (/* binding */ isValidHostname)\n/* harmony export */ });\nfunction isValidHostname(hostname) {\n    const hostPattern = /^[a-z0-9][a-z0-9\\.\\-]*[a-z0-9]$/;\n    return hostPattern.test(hostname);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm90b2NvbC1odHRwQDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3Byb3RvY29sLWh0dHAvZGlzdC1lcy9pc1ZhbGlkSG9zdG5hbWUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm90b2NvbC1odHRwQDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3Byb3RvY29sLWh0dHAvZGlzdC1lcy9pc1ZhbGlkSG9zdG5hbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGlzVmFsaWRIb3N0bmFtZShob3N0bmFtZSkge1xuICAgIGNvbnN0IGhvc3RQYXR0ZXJuID0gL15bYS16MC05XVthLXowLTlcXC5cXC1dKlthLXowLTldJC87XG4gICAgcmV0dXJuIGhvc3RQYXR0ZXJuLnRlc3QoaG9zdG5hbWUpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/isValidHostname.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/types.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/types.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStwcm90b2NvbC1odHRwQDUuMS4yL25vZGVfbW9kdWxlcy9Ac21pdGh5L3Byb3RvY29sLWh0dHAvZGlzdC1lcy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiO0FBQVUiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3Byb3RvY29sLWh0dHBANS4xLjIvbm9kZV9tb2R1bGVzL0BzbWl0aHkvcHJvdG9jb2wtaHR0cC9kaXN0LWVzL3R5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/types.js\n");

/***/ })

};
;