"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+middleware-eventstream@3.821.0";
exports.ids = ["vendor-chunks/@aws-sdk+middleware-eventstream@3.821.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamConfiguration.js":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamConfiguration.js ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveEventStreamConfig: () => (/* binding */ resolveEventStreamConfig)\n/* harmony export */ });\nfunction resolveEventStreamConfig(input) {\n    const eventSigner = input.signer;\n    const messageSigner = input.signer;\n    const newInput = Object.assign(input, {\n        eventSigner,\n        messageSigner,\n    });\n    const eventStreamPayloadHandler = newInput.eventStreamPayloadHandlerProvider(newInput);\n    return Object.assign(newInput, {\n        eventStreamPayloadHandler,\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1ldmVudHN0cmVhbUAzLjgyMS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWV2ZW50c3RyZWFtL2Rpc3QtZXMvZXZlbnRTdHJlYW1Db25maWd1cmF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1ldmVudHN0cmVhbUAzLjgyMS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWV2ZW50c3RyZWFtL2Rpc3QtZXMvZXZlbnRTdHJlYW1Db25maWd1cmF0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiByZXNvbHZlRXZlbnRTdHJlYW1Db25maWcoaW5wdXQpIHtcbiAgICBjb25zdCBldmVudFNpZ25lciA9IGlucHV0LnNpZ25lcjtcbiAgICBjb25zdCBtZXNzYWdlU2lnbmVyID0gaW5wdXQuc2lnbmVyO1xuICAgIGNvbnN0IG5ld0lucHV0ID0gT2JqZWN0LmFzc2lnbihpbnB1dCwge1xuICAgICAgICBldmVudFNpZ25lcixcbiAgICAgICAgbWVzc2FnZVNpZ25lcixcbiAgICB9KTtcbiAgICBjb25zdCBldmVudFN0cmVhbVBheWxvYWRIYW5kbGVyID0gbmV3SW5wdXQuZXZlbnRTdHJlYW1QYXlsb2FkSGFuZGxlclByb3ZpZGVyKG5ld0lucHV0KTtcbiAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihuZXdJbnB1dCwge1xuICAgICAgICBldmVudFN0cmVhbVBheWxvYWRIYW5kbGVyLFxuICAgIH0pO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamConfiguration.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamHandlingMiddleware.js":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamHandlingMiddleware.js ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventStreamHandlingMiddleware: () => (/* binding */ eventStreamHandlingMiddleware),\n/* harmony export */   eventStreamHandlingMiddlewareOptions: () => (/* binding */ eventStreamHandlingMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n\nconst eventStreamHandlingMiddleware = (options) => (next, context) => async (args) => {\n    const { request } = args;\n    if (!_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.isInstance(request))\n        return next(args);\n    return options.eventStreamPayloadHandler.handle(next, args, context);\n};\nconst eventStreamHandlingMiddlewareOptions = {\n    tags: [\"EVENT_STREAM\", \"SIGNATURE\", \"HANDLE\"],\n    name: \"eventStreamHandlingMiddleware\",\n    relation: \"after\",\n    toMiddleware: \"awsAuthMiddleware\",\n    override: true,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1ldmVudHN0cmVhbUAzLjgyMS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWV2ZW50c3RyZWFtL2Rpc3QtZXMvZXZlbnRTdHJlYW1IYW5kbGluZ01pZGRsZXdhcmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQW9EO0FBQzdDO0FBQ1AsWUFBWSxVQUFVO0FBQ3RCLFNBQVMsOERBQVc7QUFDcEI7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayttaWRkbGV3YXJlLWV2ZW50c3RyZWFtQDMuODIxLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL21pZGRsZXdhcmUtZXZlbnRzdHJlYW0vZGlzdC1lcy9ldmVudFN0cmVhbUhhbmRsaW5nTWlkZGxld2FyZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdHRwUmVxdWVzdCB9IGZyb20gXCJAc21pdGh5L3Byb3RvY29sLWh0dHBcIjtcbmV4cG9ydCBjb25zdCBldmVudFN0cmVhbUhhbmRsaW5nTWlkZGxld2FyZSA9IChvcHRpb25zKSA9PiAobmV4dCwgY29udGV4dCkgPT4gYXN5bmMgKGFyZ3MpID0+IHtcbiAgICBjb25zdCB7IHJlcXVlc3QgfSA9IGFyZ3M7XG4gICAgaWYgKCFIdHRwUmVxdWVzdC5pc0luc3RhbmNlKHJlcXVlc3QpKVxuICAgICAgICByZXR1cm4gbmV4dChhcmdzKTtcbiAgICByZXR1cm4gb3B0aW9ucy5ldmVudFN0cmVhbVBheWxvYWRIYW5kbGVyLmhhbmRsZShuZXh0LCBhcmdzLCBjb250ZXh0KTtcbn07XG5leHBvcnQgY29uc3QgZXZlbnRTdHJlYW1IYW5kbGluZ01pZGRsZXdhcmVPcHRpb25zID0ge1xuICAgIHRhZ3M6IFtcIkVWRU5UX1NUUkVBTVwiLCBcIlNJR05BVFVSRVwiLCBcIkhBTkRMRVwiXSxcbiAgICBuYW1lOiBcImV2ZW50U3RyZWFtSGFuZGxpbmdNaWRkbGV3YXJlXCIsXG4gICAgcmVsYXRpb246IFwiYWZ0ZXJcIixcbiAgICB0b01pZGRsZXdhcmU6IFwiYXdzQXV0aE1pZGRsZXdhcmVcIixcbiAgICBvdmVycmlkZTogdHJ1ZSxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamHandlingMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamHeaderMiddleware.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamHeaderMiddleware.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventStreamHeaderMiddleware: () => (/* binding */ eventStreamHeaderMiddleware),\n/* harmony export */   eventStreamHeaderMiddlewareOptions: () => (/* binding */ eventStreamHeaderMiddlewareOptions)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n\nconst eventStreamHeaderMiddleware = (next) => async (args) => {\n    const { request } = args;\n    if (!_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.isInstance(request))\n        return next(args);\n    request.headers = {\n        ...request.headers,\n        \"content-type\": \"application/vnd.amazon.eventstream\",\n        \"x-amz-content-sha256\": \"STREAMING-AWS4-HMAC-SHA256-EVENTS\",\n    };\n    return next({\n        ...args,\n        request,\n    });\n};\nconst eventStreamHeaderMiddlewareOptions = {\n    step: \"build\",\n    tags: [\"EVENT_STREAM\", \"HEADER\", \"CONTENT_TYPE\", \"CONTENT_SHA256\"],\n    name: \"eventStreamHeaderMiddleware\",\n    override: true,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1ldmVudHN0cmVhbUAzLjgyMS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWV2ZW50c3RyZWFtL2Rpc3QtZXMvZXZlbnRTdHJlYW1IZWFkZXJNaWRkbGV3YXJlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvRDtBQUM3QztBQUNQLFlBQVksVUFBVTtBQUN0QixTQUFTLDhEQUFXO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK21pZGRsZXdhcmUtZXZlbnRzdHJlYW1AMy44MjEuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1ldmVudHN0cmVhbS9kaXN0LWVzL2V2ZW50U3RyZWFtSGVhZGVyTWlkZGxld2FyZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdHRwUmVxdWVzdCB9IGZyb20gXCJAc21pdGh5L3Byb3RvY29sLWh0dHBcIjtcbmV4cG9ydCBjb25zdCBldmVudFN0cmVhbUhlYWRlck1pZGRsZXdhcmUgPSAobmV4dCkgPT4gYXN5bmMgKGFyZ3MpID0+IHtcbiAgICBjb25zdCB7IHJlcXVlc3QgfSA9IGFyZ3M7XG4gICAgaWYgKCFIdHRwUmVxdWVzdC5pc0luc3RhbmNlKHJlcXVlc3QpKVxuICAgICAgICByZXR1cm4gbmV4dChhcmdzKTtcbiAgICByZXF1ZXN0LmhlYWRlcnMgPSB7XG4gICAgICAgIC4uLnJlcXVlc3QuaGVhZGVycyxcbiAgICAgICAgXCJjb250ZW50LXR5cGVcIjogXCJhcHBsaWNhdGlvbi92bmQuYW1hem9uLmV2ZW50c3RyZWFtXCIsXG4gICAgICAgIFwieC1hbXotY29udGVudC1zaGEyNTZcIjogXCJTVFJFQU1JTkctQVdTNC1ITUFDLVNIQTI1Ni1FVkVOVFNcIixcbiAgICB9O1xuICAgIHJldHVybiBuZXh0KHtcbiAgICAgICAgLi4uYXJncyxcbiAgICAgICAgcmVxdWVzdCxcbiAgICB9KTtcbn07XG5leHBvcnQgY29uc3QgZXZlbnRTdHJlYW1IZWFkZXJNaWRkbGV3YXJlT3B0aW9ucyA9IHtcbiAgICBzdGVwOiBcImJ1aWxkXCIsXG4gICAgdGFnczogW1wiRVZFTlRfU1RSRUFNXCIsIFwiSEVBREVSXCIsIFwiQ09OVEVOVF9UWVBFXCIsIFwiQ09OVEVOVF9TSEEyNTZcIl0sXG4gICAgbmFtZTogXCJldmVudFN0cmVhbUhlYWRlck1pZGRsZXdhcmVcIixcbiAgICBvdmVycmlkZTogdHJ1ZSxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamHeaderMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/getEventStreamPlugin.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/getEventStreamPlugin.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEventStreamPlugin: () => (/* binding */ getEventStreamPlugin)\n/* harmony export */ });\n/* harmony import */ var _eventStreamHandlingMiddleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./eventStreamHandlingMiddleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamHandlingMiddleware.js\");\n/* harmony import */ var _eventStreamHeaderMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./eventStreamHeaderMiddleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamHeaderMiddleware.js\");\n\n\nconst getEventStreamPlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo((0,_eventStreamHandlingMiddleware__WEBPACK_IMPORTED_MODULE_0__.eventStreamHandlingMiddleware)(options), _eventStreamHandlingMiddleware__WEBPACK_IMPORTED_MODULE_0__.eventStreamHandlingMiddlewareOptions);\n        clientStack.add(_eventStreamHeaderMiddleware__WEBPACK_IMPORTED_MODULE_1__.eventStreamHeaderMiddleware, _eventStreamHeaderMiddleware__WEBPACK_IMPORTED_MODULE_1__.eventStreamHeaderMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1ldmVudHN0cmVhbUAzLjgyMS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWV2ZW50c3RyZWFtL2Rpc3QtZXMvZ2V0RXZlbnRTdHJlYW1QbHVnaW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNIO0FBQ047QUFDekc7QUFDUDtBQUNBLGtDQUFrQyw2RkFBNkIsV0FBVyxnR0FBb0M7QUFDOUcsd0JBQXdCLHFGQUEyQixFQUFFLDRGQUFrQztBQUN2RixLQUFLO0FBQ0wsQ0FBQyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK21pZGRsZXdhcmUtZXZlbnRzdHJlYW1AMy44MjEuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1ldmVudHN0cmVhbS9kaXN0LWVzL2dldEV2ZW50U3RyZWFtUGx1Z2luLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGV2ZW50U3RyZWFtSGFuZGxpbmdNaWRkbGV3YXJlLCBldmVudFN0cmVhbUhhbmRsaW5nTWlkZGxld2FyZU9wdGlvbnMgfSBmcm9tIFwiLi9ldmVudFN0cmVhbUhhbmRsaW5nTWlkZGxld2FyZVwiO1xuaW1wb3J0IHsgZXZlbnRTdHJlYW1IZWFkZXJNaWRkbGV3YXJlLCBldmVudFN0cmVhbUhlYWRlck1pZGRsZXdhcmVPcHRpb25zIH0gZnJvbSBcIi4vZXZlbnRTdHJlYW1IZWFkZXJNaWRkbGV3YXJlXCI7XG5leHBvcnQgY29uc3QgZ2V0RXZlbnRTdHJlYW1QbHVnaW4gPSAob3B0aW9ucykgPT4gKHtcbiAgICBhcHBseVRvU3RhY2s6IChjbGllbnRTdGFjaykgPT4ge1xuICAgICAgICBjbGllbnRTdGFjay5hZGRSZWxhdGl2ZVRvKGV2ZW50U3RyZWFtSGFuZGxpbmdNaWRkbGV3YXJlKG9wdGlvbnMpLCBldmVudFN0cmVhbUhhbmRsaW5nTWlkZGxld2FyZU9wdGlvbnMpO1xuICAgICAgICBjbGllbnRTdGFjay5hZGQoZXZlbnRTdHJlYW1IZWFkZXJNaWRkbGV3YXJlLCBldmVudFN0cmVhbUhlYWRlck1pZGRsZXdhcmVPcHRpb25zKTtcbiAgICB9LFxufSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/getEventStreamPlugin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/index.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/index.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   eventStreamHandlingMiddleware: () => (/* reexport safe */ _eventStreamHandlingMiddleware__WEBPACK_IMPORTED_MODULE_1__.eventStreamHandlingMiddleware),\n/* harmony export */   eventStreamHandlingMiddlewareOptions: () => (/* reexport safe */ _eventStreamHandlingMiddleware__WEBPACK_IMPORTED_MODULE_1__.eventStreamHandlingMiddlewareOptions),\n/* harmony export */   eventStreamHeaderMiddleware: () => (/* reexport safe */ _eventStreamHeaderMiddleware__WEBPACK_IMPORTED_MODULE_2__.eventStreamHeaderMiddleware),\n/* harmony export */   eventStreamHeaderMiddlewareOptions: () => (/* reexport safe */ _eventStreamHeaderMiddleware__WEBPACK_IMPORTED_MODULE_2__.eventStreamHeaderMiddlewareOptions),\n/* harmony export */   getEventStreamPlugin: () => (/* reexport safe */ _getEventStreamPlugin__WEBPACK_IMPORTED_MODULE_3__.getEventStreamPlugin),\n/* harmony export */   resolveEventStreamConfig: () => (/* reexport safe */ _eventStreamConfiguration__WEBPACK_IMPORTED_MODULE_0__.resolveEventStreamConfig)\n/* harmony export */ });\n/* harmony import */ var _eventStreamConfiguration__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./eventStreamConfiguration */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamConfiguration.js\");\n/* harmony import */ var _eventStreamHandlingMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./eventStreamHandlingMiddleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamHandlingMiddleware.js\");\n/* harmony import */ var _eventStreamHeaderMiddleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./eventStreamHeaderMiddleware */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/eventStreamHeaderMiddleware.js\");\n/* harmony import */ var _getEventStreamPlugin__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getEventStreamPlugin */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/getEventStreamPlugin.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrbWlkZGxld2FyZS1ldmVudHN0cmVhbUAzLjgyMS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9taWRkbGV3YXJlLWV2ZW50c3RyZWFtL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUEyQztBQUNLO0FBQ0Y7QUFDUCIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK21pZGRsZXdhcmUtZXZlbnRzdHJlYW1AMy44MjEuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvbWlkZGxld2FyZS1ldmVudHN0cmVhbS9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2V2ZW50U3RyZWFtQ29uZmlndXJhdGlvblwiO1xuZXhwb3J0ICogZnJvbSBcIi4vZXZlbnRTdHJlYW1IYW5kbGluZ01pZGRsZXdhcmVcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2V2ZW50U3RyZWFtSGVhZGVyTWlkZGxld2FyZVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vZ2V0RXZlbnRTdHJlYW1QbHVnaW5cIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-eventstream@3.821.0/node_modules/@aws-sdk/middleware-eventstream/dist-es/index.js\n");

/***/ })

};
;