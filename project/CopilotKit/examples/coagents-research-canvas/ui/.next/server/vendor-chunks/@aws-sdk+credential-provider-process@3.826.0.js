"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+credential-provider-process@3.826.0";
exports.ids = ["vendor-chunks/@aws-sdk+credential-provider-process@3.826.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/fromProcess.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/fromProcess.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromProcess: () => (/* binding */ fromProcess)\n/* harmony export */ });\n/* harmony import */ var _smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/shared-ini-file-loader */ \"(rsc)/./node_modules/.pnpm/@smithy+shared-ini-file-loader@4.0.4/node_modules/@smithy/shared-ini-file-loader/dist-es/index.js\");\n/* harmony import */ var _resolveProcessCredentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./resolveProcessCredentials */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/resolveProcessCredentials.js\");\n\n\nconst fromProcess = (init = {}) => async ({ callerClientConfig } = {}) => {\n    init.logger?.debug(\"@aws-sdk/credential-provider-process - fromProcess\");\n    const profiles = await (0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_0__.parseKnownFiles)(init);\n    return (0,_resolveProcessCredentials__WEBPACK_IMPORTED_MODULE_1__.resolveProcessCredentials)((0,_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_0__.getProfileName)({\n        profile: init.profile ?? callerClientConfig?.profile,\n    }), profiles, init.logger);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1wcm9jZXNzQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItcHJvY2Vzcy9kaXN0LWVzL2Zyb21Qcm9jZXNzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpRjtBQUNUO0FBQ2pFLDhCQUE4QixjQUFjLHFCQUFxQixJQUFJO0FBQzVFO0FBQ0EsMkJBQTJCLCtFQUFlO0FBQzFDLFdBQVcscUZBQXlCLENBQUMsOEVBQWM7QUFDbkQ7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytjcmVkZW50aWFsLXByb3ZpZGVyLXByb2Nlc3NAMy44MjYuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1wcm9jZXNzL2Rpc3QtZXMvZnJvbVByb2Nlc3MuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0UHJvZmlsZU5hbWUsIHBhcnNlS25vd25GaWxlcyB9IGZyb20gXCJAc21pdGh5L3NoYXJlZC1pbmktZmlsZS1sb2FkZXJcIjtcbmltcG9ydCB7IHJlc29sdmVQcm9jZXNzQ3JlZGVudGlhbHMgfSBmcm9tIFwiLi9yZXNvbHZlUHJvY2Vzc0NyZWRlbnRpYWxzXCI7XG5leHBvcnQgY29uc3QgZnJvbVByb2Nlc3MgPSAoaW5pdCA9IHt9KSA9PiBhc3luYyAoeyBjYWxsZXJDbGllbnRDb25maWcgfSA9IHt9KSA9PiB7XG4gICAgaW5pdC5sb2dnZXI/LmRlYnVnKFwiQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1wcm9jZXNzIC0gZnJvbVByb2Nlc3NcIik7XG4gICAgY29uc3QgcHJvZmlsZXMgPSBhd2FpdCBwYXJzZUtub3duRmlsZXMoaW5pdCk7XG4gICAgcmV0dXJuIHJlc29sdmVQcm9jZXNzQ3JlZGVudGlhbHMoZ2V0UHJvZmlsZU5hbWUoe1xuICAgICAgICBwcm9maWxlOiBpbml0LnByb2ZpbGUgPz8gY2FsbGVyQ2xpZW50Q29uZmlnPy5wcm9maWxlLFxuICAgIH0pLCBwcm9maWxlcywgaW5pdC5sb2dnZXIpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/fromProcess.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/getValidatedProcessCredentials.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/getValidatedProcessCredentials.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getValidatedProcessCredentials: () => (/* binding */ getValidatedProcessCredentials)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/core/client */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/setCredentialFeature.js\");\n\nconst getValidatedProcessCredentials = (profileName, data, profiles) => {\n    if (data.Version !== 1) {\n        throw Error(`Profile ${profileName} credential_process did not return Version 1.`);\n    }\n    if (data.AccessKeyId === undefined || data.SecretAccessKey === undefined) {\n        throw Error(`Profile ${profileName} credential_process returned invalid credentials.`);\n    }\n    if (data.Expiration) {\n        const currentTime = new Date();\n        const expireTime = new Date(data.Expiration);\n        if (expireTime < currentTime) {\n            throw Error(`Profile ${profileName} credential_process returned expired credentials.`);\n        }\n    }\n    let accountId = data.AccountId;\n    if (!accountId && profiles?.[profileName]?.aws_account_id) {\n        accountId = profiles[profileName].aws_account_id;\n    }\n    const credentials = {\n        accessKeyId: data.AccessKeyId,\n        secretAccessKey: data.SecretAccessKey,\n        ...(data.SessionToken && { sessionToken: data.SessionToken }),\n        ...(data.Expiration && { expiration: new Date(data.Expiration) }),\n        ...(data.CredentialScope && { credentialScope: data.CredentialScope }),\n        ...(accountId && { accountId }),\n    };\n    (0,_aws_sdk_core_client__WEBPACK_IMPORTED_MODULE_0__.setCredentialFeature)(credentials, \"CREDENTIALS_PROCESS\", \"w\");\n    return credentials;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1wcm9jZXNzQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItcHJvY2Vzcy9kaXN0LWVzL2dldFZhbGlkYXRlZFByb2Nlc3NDcmVkZW50aWFscy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0RDtBQUNyRDtBQUNQO0FBQ0EsK0JBQStCLGFBQWE7QUFDNUM7QUFDQTtBQUNBLCtCQUErQixhQUFhO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsYUFBYTtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsaUNBQWlDO0FBQ3BFLGlDQUFpQyx1Q0FBdUM7QUFDeEUsc0NBQXNDLHVDQUF1QztBQUM3RSwyQkFBMkIsV0FBVztBQUN0QztBQUNBLElBQUksMEVBQW9CO0FBQ3hCO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytjcmVkZW50aWFsLXByb3ZpZGVyLXByb2Nlc3NAMy44MjYuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1wcm9jZXNzL2Rpc3QtZXMvZ2V0VmFsaWRhdGVkUHJvY2Vzc0NyZWRlbnRpYWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHNldENyZWRlbnRpYWxGZWF0dXJlIH0gZnJvbSBcIkBhd3Mtc2RrL2NvcmUvY2xpZW50XCI7XG5leHBvcnQgY29uc3QgZ2V0VmFsaWRhdGVkUHJvY2Vzc0NyZWRlbnRpYWxzID0gKHByb2ZpbGVOYW1lLCBkYXRhLCBwcm9maWxlcykgPT4ge1xuICAgIGlmIChkYXRhLlZlcnNpb24gIT09IDEpIHtcbiAgICAgICAgdGhyb3cgRXJyb3IoYFByb2ZpbGUgJHtwcm9maWxlTmFtZX0gY3JlZGVudGlhbF9wcm9jZXNzIGRpZCBub3QgcmV0dXJuIFZlcnNpb24gMS5gKTtcbiAgICB9XG4gICAgaWYgKGRhdGEuQWNjZXNzS2V5SWQgPT09IHVuZGVmaW5lZCB8fCBkYXRhLlNlY3JldEFjY2Vzc0tleSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHRocm93IEVycm9yKGBQcm9maWxlICR7cHJvZmlsZU5hbWV9IGNyZWRlbnRpYWxfcHJvY2VzcyByZXR1cm5lZCBpbnZhbGlkIGNyZWRlbnRpYWxzLmApO1xuICAgIH1cbiAgICBpZiAoZGF0YS5FeHBpcmF0aW9uKSB7XG4gICAgICAgIGNvbnN0IGN1cnJlbnRUaW1lID0gbmV3IERhdGUoKTtcbiAgICAgICAgY29uc3QgZXhwaXJlVGltZSA9IG5ldyBEYXRlKGRhdGEuRXhwaXJhdGlvbik7XG4gICAgICAgIGlmIChleHBpcmVUaW1lIDwgY3VycmVudFRpbWUpIHtcbiAgICAgICAgICAgIHRocm93IEVycm9yKGBQcm9maWxlICR7cHJvZmlsZU5hbWV9IGNyZWRlbnRpYWxfcHJvY2VzcyByZXR1cm5lZCBleHBpcmVkIGNyZWRlbnRpYWxzLmApO1xuICAgICAgICB9XG4gICAgfVxuICAgIGxldCBhY2NvdW50SWQgPSBkYXRhLkFjY291bnRJZDtcbiAgICBpZiAoIWFjY291bnRJZCAmJiBwcm9maWxlcz8uW3Byb2ZpbGVOYW1lXT8uYXdzX2FjY291bnRfaWQpIHtcbiAgICAgICAgYWNjb3VudElkID0gcHJvZmlsZXNbcHJvZmlsZU5hbWVdLmF3c19hY2NvdW50X2lkO1xuICAgIH1cbiAgICBjb25zdCBjcmVkZW50aWFscyA9IHtcbiAgICAgICAgYWNjZXNzS2V5SWQ6IGRhdGEuQWNjZXNzS2V5SWQsXG4gICAgICAgIHNlY3JldEFjY2Vzc0tleTogZGF0YS5TZWNyZXRBY2Nlc3NLZXksXG4gICAgICAgIC4uLihkYXRhLlNlc3Npb25Ub2tlbiAmJiB7IHNlc3Npb25Ub2tlbjogZGF0YS5TZXNzaW9uVG9rZW4gfSksXG4gICAgICAgIC4uLihkYXRhLkV4cGlyYXRpb24gJiYgeyBleHBpcmF0aW9uOiBuZXcgRGF0ZShkYXRhLkV4cGlyYXRpb24pIH0pLFxuICAgICAgICAuLi4oZGF0YS5DcmVkZW50aWFsU2NvcGUgJiYgeyBjcmVkZW50aWFsU2NvcGU6IGRhdGEuQ3JlZGVudGlhbFNjb3BlIH0pLFxuICAgICAgICAuLi4oYWNjb3VudElkICYmIHsgYWNjb3VudElkIH0pLFxuICAgIH07XG4gICAgc2V0Q3JlZGVudGlhbEZlYXR1cmUoY3JlZGVudGlhbHMsIFwiQ1JFREVOVElBTFNfUFJPQ0VTU1wiLCBcIndcIik7XG4gICAgcmV0dXJuIGNyZWRlbnRpYWxzO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/getValidatedProcessCredentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/index.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/index.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromProcess: () => (/* reexport safe */ _fromProcess__WEBPACK_IMPORTED_MODULE_0__.fromProcess)\n/* harmony export */ });\n/* harmony import */ var _fromProcess__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fromProcess */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/fromProcess.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1wcm9jZXNzQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItcHJvY2Vzcy9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQThCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1wcm9jZXNzQDMuODI2LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItcHJvY2Vzcy9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2Zyb21Qcm9jZXNzXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/resolveProcessCredentials.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/resolveProcessCredentials.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveProcessCredentials: () => (/* binding */ resolveProcessCredentials)\n/* harmony export */ });\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! util */ \"util\");\n/* harmony import */ var util__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(util__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _getValidatedProcessCredentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getValidatedProcessCredentials */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/getValidatedProcessCredentials.js\");\n\n\n\n\nconst resolveProcessCredentials = async (profileName, profiles, logger) => {\n    const profile = profiles[profileName];\n    if (profiles[profileName]) {\n        const credentialProcess = profile[\"credential_process\"];\n        if (credentialProcess !== undefined) {\n            const execPromise = (0,util__WEBPACK_IMPORTED_MODULE_2__.promisify)(child_process__WEBPACK_IMPORTED_MODULE_1__.exec);\n            try {\n                const { stdout } = await execPromise(credentialProcess);\n                let data;\n                try {\n                    data = JSON.parse(stdout.trim());\n                }\n                catch {\n                    throw Error(`Profile ${profileName} credential_process returned invalid JSON.`);\n                }\n                return (0,_getValidatedProcessCredentials__WEBPACK_IMPORTED_MODULE_3__.getValidatedProcessCredentials)(profileName, data, profiles);\n            }\n            catch (error) {\n                throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(error.message, { logger });\n            }\n        }\n        else {\n            throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`Profile ${profileName} did not contain credential_process.`, { logger });\n        }\n    }\n    else {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(`Profile ${profileName} could not be found in shared credentials file.`, {\n            logger,\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/resolveProcessCredentials.js\n");

/***/ })

};
;