"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gaxios@5.1.3";
exports.ids = ["vendor-chunks/gaxios@5.1.3"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/common.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/common.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GaxiosError = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\nclass GaxiosError extends Error {\n    constructor(message, options, response) {\n        super(message);\n        this.response = response;\n        this.config = options;\n        this.response.data = translateData(options.responseType, response.data);\n        this.code = response.status.toString();\n    }\n}\nexports.GaxiosError = GaxiosError;\nfunction translateData(responseType, data) {\n    switch (responseType) {\n        case 'stream':\n            return data;\n        case 'json':\n            return JSON.parse(JSON.stringify(data));\n        case 'arraybuffer':\n            return JSON.parse(Buffer.from(data).toString('utf8'));\n        case 'blob':\n            return JSON.parse(data.text());\n        default:\n            return data;\n    }\n}\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/gaxios.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/gaxios.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Gaxios = void 0;\nconst extend_1 = __importDefault(__webpack_require__(/*! extend */ \"(rsc)/./node_modules/.pnpm/extend@3.0.2/node_modules/extend/index.js\"));\nconst https_1 = __webpack_require__(/*! https */ \"https\");\nconst node_fetch_1 = __importDefault(__webpack_require__(/*! node-fetch */ \"(rsc)/./node_modules/.pnpm/node-fetch@2.7.0/node_modules/node-fetch/lib/index.mjs\"));\nconst querystring_1 = __importDefault(__webpack_require__(/*! querystring */ \"querystring\"));\nconst is_stream_1 = __importDefault(__webpack_require__(/*! is-stream */ \"(rsc)/./node_modules/.pnpm/is-stream@2.0.1/node_modules/is-stream/index.js\"));\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst common_1 = __webpack_require__(/*! ./common */ \"(rsc)/./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/common.js\");\nconst retry_1 = __webpack_require__(/*! ./retry */ \"(rsc)/./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/retry.js\");\n/* eslint-disable @typescript-eslint/no-explicit-any */\nconst fetch = hasFetch() ? window.fetch : node_fetch_1.default;\nfunction hasWindow() {\n    return typeof window !== 'undefined' && !!window;\n}\nfunction hasFetch() {\n    return hasWindow() && !!window.fetch;\n}\nfunction hasBuffer() {\n    return typeof Buffer !== 'undefined';\n}\nfunction hasHeader(options, header) {\n    return !!getHeader(options, header);\n}\nfunction getHeader(options, header) {\n    header = header.toLowerCase();\n    for (const key of Object.keys((options === null || options === void 0 ? void 0 : options.headers) || {})) {\n        if (header === key.toLowerCase()) {\n            return options.headers[key];\n        }\n    }\n    return undefined;\n}\nlet HttpsProxyAgent;\nfunction loadProxy() {\n    var _a, _b, _c, _d;\n    const proxy = ((_a = process === null || process === void 0 ? void 0 : process.env) === null || _a === void 0 ? void 0 : _a.HTTPS_PROXY) ||\n        ((_b = process === null || process === void 0 ? void 0 : process.env) === null || _b === void 0 ? void 0 : _b.https_proxy) ||\n        ((_c = process === null || process === void 0 ? void 0 : process.env) === null || _c === void 0 ? void 0 : _c.HTTP_PROXY) ||\n        ((_d = process === null || process === void 0 ? void 0 : process.env) === null || _d === void 0 ? void 0 : _d.http_proxy);\n    if (proxy) {\n        HttpsProxyAgent = __webpack_require__(/*! https-proxy-agent */ \"(rsc)/./node_modules/.pnpm/https-proxy-agent@5.0.1/node_modules/https-proxy-agent/dist/index.js\");\n    }\n    return proxy;\n}\nloadProxy();\nfunction skipProxy(url) {\n    var _a;\n    const noProxyEnv = (_a = process.env.NO_PROXY) !== null && _a !== void 0 ? _a : process.env.no_proxy;\n    if (!noProxyEnv) {\n        return false;\n    }\n    const noProxyUrls = noProxyEnv.split(',');\n    const parsedURL = new url_1.URL(url);\n    return !!noProxyUrls.find(url => {\n        if (url.startsWith('*.') || url.startsWith('.')) {\n            url = url.replace(/^\\*\\./, '.');\n            return parsedURL.hostname.endsWith(url);\n        }\n        else {\n            return url === parsedURL.origin || url === parsedURL.hostname;\n        }\n    });\n}\n// Figure out if we should be using a proxy. Only if it's required, load\n// the https-proxy-agent module as it adds startup cost.\nfunction getProxy(url) {\n    // If there is a match between the no_proxy env variables and the url, then do not proxy\n    if (skipProxy(url)) {\n        return undefined;\n        // If there is not a match between the no_proxy env variables and the url, check to see if there should be a proxy\n    }\n    else {\n        return loadProxy();\n    }\n}\nclass Gaxios {\n    /**\n     * The Gaxios class is responsible for making HTTP requests.\n     * @param defaults The default set of options to be used for this instance.\n     */\n    constructor(defaults) {\n        this.agentCache = new Map();\n        this.defaults = defaults || {};\n    }\n    /**\n     * Perform an HTTP request with the given options.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */\n    async request(opts = {}) {\n        opts = this.validateOpts(opts);\n        return this._request(opts);\n    }\n    async _defaultAdapter(opts) {\n        const fetchImpl = opts.fetchImplementation || fetch;\n        const res = (await fetchImpl(opts.url, opts));\n        const data = await this.getResponseData(opts, res);\n        return this.translateResponse(opts, res, data);\n    }\n    /**\n     * Internal, retryable version of the `request` method.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */\n    async _request(opts = {}) {\n        try {\n            let translatedResponse;\n            if (opts.adapter) {\n                translatedResponse = await opts.adapter(opts, this._defaultAdapter.bind(this));\n            }\n            else {\n                translatedResponse = await this._defaultAdapter(opts);\n            }\n            if (!opts.validateStatus(translatedResponse.status)) {\n                if (opts.responseType === 'stream') {\n                    let response = '';\n                    await new Promise(resolve => {\n                        translatedResponse.data.on('data', chunk => {\n                            response += chunk;\n                        });\n                        translatedResponse.data.on('end', resolve);\n                    });\n                    translatedResponse.data = response;\n                }\n                throw new common_1.GaxiosError(`Request failed with status code ${translatedResponse.status}`, opts, translatedResponse);\n            }\n            return translatedResponse;\n        }\n        catch (e) {\n            const err = e;\n            err.config = opts;\n            const { shouldRetry, config } = await (0, retry_1.getRetryConfig)(err);\n            if (shouldRetry && config) {\n                err.config.retryConfig.currentRetryAttempt =\n                    config.retryConfig.currentRetryAttempt;\n                return this._request(err.config);\n            }\n            throw err;\n        }\n    }\n    async getResponseData(opts, res) {\n        switch (opts.responseType) {\n            case 'stream':\n                return res.body;\n            case 'json': {\n                let data = await res.text();\n                try {\n                    data = JSON.parse(data);\n                }\n                catch (_a) {\n                    // continue\n                }\n                return data;\n            }\n            case 'arraybuffer':\n                return res.arrayBuffer();\n            case 'blob':\n                return res.blob();\n            default:\n                return res.text();\n        }\n    }\n    /**\n     * Validates the options, and merges them with defaults.\n     * @param opts The original options passed from the client.\n     */\n    validateOpts(options) {\n        const opts = (0, extend_1.default)(true, {}, this.defaults, options);\n        if (!opts.url) {\n            throw new Error('URL is required.');\n        }\n        // baseUrl has been deprecated, remove in 2.0\n        const baseUrl = opts.baseUrl || opts.baseURL;\n        if (baseUrl) {\n            opts.url = baseUrl + opts.url;\n        }\n        opts.paramsSerializer = opts.paramsSerializer || this.paramsSerializer;\n        if (opts.params && Object.keys(opts.params).length > 0) {\n            let additionalQueryParams = opts.paramsSerializer(opts.params);\n            if (additionalQueryParams.startsWith('?')) {\n                additionalQueryParams = additionalQueryParams.slice(1);\n            }\n            const prefix = opts.url.includes('?') ? '&' : '?';\n            opts.url = opts.url + prefix + additionalQueryParams;\n        }\n        if (typeof options.maxContentLength === 'number') {\n            opts.size = options.maxContentLength;\n        }\n        if (typeof options.maxRedirects === 'number') {\n            opts.follow = options.maxRedirects;\n        }\n        opts.headers = opts.headers || {};\n        if (opts.data) {\n            const isFormData = typeof FormData === 'undefined'\n                ? false\n                : (opts === null || opts === void 0 ? void 0 : opts.data) instanceof FormData;\n            if (is_stream_1.default.readable(opts.data)) {\n                opts.body = opts.data;\n            }\n            else if (hasBuffer() && Buffer.isBuffer(opts.data)) {\n                // Do not attempt to JSON.stringify() a Buffer:\n                opts.body = opts.data;\n                if (!hasHeader(opts, 'Content-Type')) {\n                    opts.headers['Content-Type'] = 'application/json';\n                }\n            }\n            else if (typeof opts.data === 'object') {\n                // If www-form-urlencoded content type has been set, but data is\n                // provided as an object, serialize the content using querystring:\n                if (!isFormData) {\n                    if (getHeader(opts, 'content-type') ===\n                        'application/x-www-form-urlencoded') {\n                        opts.body = opts.paramsSerializer(opts.data);\n                    }\n                    else {\n                        // } else if (!(opts.data instanceof FormData)) {\n                        if (!hasHeader(opts, 'Content-Type')) {\n                            opts.headers['Content-Type'] = 'application/json';\n                        }\n                        opts.body = JSON.stringify(opts.data);\n                    }\n                }\n            }\n            else {\n                opts.body = opts.data;\n            }\n        }\n        opts.validateStatus = opts.validateStatus || this.validateStatus;\n        opts.responseType = opts.responseType || 'json';\n        if (!opts.headers['Accept'] && opts.responseType === 'json') {\n            opts.headers['Accept'] = 'application/json';\n        }\n        opts.method = opts.method || 'GET';\n        const proxy = getProxy(opts.url);\n        if (proxy) {\n            if (this.agentCache.has(proxy)) {\n                opts.agent = this.agentCache.get(proxy);\n            }\n            else {\n                // Proxy is being used in conjunction with mTLS.\n                if (opts.cert && opts.key) {\n                    const parsedURL = new url_1.URL(proxy);\n                    opts.agent = new HttpsProxyAgent({\n                        port: parsedURL.port,\n                        host: parsedURL.host,\n                        protocol: parsedURL.protocol,\n                        cert: opts.cert,\n                        key: opts.key,\n                    });\n                }\n                else {\n                    opts.agent = new HttpsProxyAgent(proxy);\n                }\n                this.agentCache.set(proxy, opts.agent);\n            }\n        }\n        else if (opts.cert && opts.key) {\n            // Configure client for mTLS:\n            if (this.agentCache.has(opts.key)) {\n                opts.agent = this.agentCache.get(opts.key);\n            }\n            else {\n                opts.agent = new https_1.Agent({\n                    cert: opts.cert,\n                    key: opts.key,\n                });\n                this.agentCache.set(opts.key, opts.agent);\n            }\n        }\n        return opts;\n    }\n    /**\n     * By default, throw for any non-2xx status code\n     * @param status status code from the HTTP response\n     */\n    validateStatus(status) {\n        return status >= 200 && status < 300;\n    }\n    /**\n     * Encode a set of key/value pars into a querystring format (?foo=bar&baz=boo)\n     * @param params key value pars to encode\n     */\n    paramsSerializer(params) {\n        return querystring_1.default.stringify(params);\n    }\n    translateResponse(opts, res, data) {\n        // headers need to be converted from a map to an obj\n        const headers = {};\n        res.headers.forEach((value, key) => {\n            headers[key] = value;\n        });\n        return {\n            config: opts,\n            data: data,\n            headers,\n            status: res.status,\n            statusText: res.statusText,\n            // XMLHttpRequestLike\n            request: {\n                responseURL: res.url,\n            },\n        };\n    }\n}\nexports.Gaxios = Gaxios;\n//# sourceMappingURL=gaxios.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/gaxios.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/index.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/index.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.request = exports.instance = exports.Gaxios = exports.GaxiosError = void 0;\nconst gaxios_1 = __webpack_require__(/*! ./gaxios */ \"(rsc)/./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/gaxios.js\");\nObject.defineProperty(exports, \"Gaxios\", ({ enumerable: true, get: function () { return gaxios_1.Gaxios; } }));\nvar common_1 = __webpack_require__(/*! ./common */ \"(rsc)/./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/common.js\");\nObject.defineProperty(exports, \"GaxiosError\", ({ enumerable: true, get: function () { return common_1.GaxiosError; } }));\n/**\n * The default instance used when the `request` method is directly\n * invoked.\n */\nexports.instance = new gaxios_1.Gaxios();\n/**\n * Make an HTTP request using the given options.\n * @param opts Options for the request\n */\nasync function request(opts) {\n    return exports.instance.request(opts);\n}\nexports.request = request;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/retry.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/retry.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getRetryConfig = void 0;\nasync function getRetryConfig(err) {\n    var _a;\n    let config = getConfig(err);\n    if (!err || !err.config || (!config && !err.config.retry)) {\n        return { shouldRetry: false };\n    }\n    config = config || {};\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    config.retry =\n        config.retry === undefined || config.retry === null ? 3 : config.retry;\n    config.httpMethodsToRetry = config.httpMethodsToRetry || [\n        'GET',\n        'HEAD',\n        'PUT',\n        'OPTIONS',\n        'DELETE',\n    ];\n    config.noResponseRetries =\n        config.noResponseRetries === undefined || config.noResponseRetries === null\n            ? 2\n            : config.noResponseRetries;\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    const retryRanges = [\n        // https://en.wikipedia.org/wiki/List_of_HTTP_status_codes\n        // 1xx - Retry (Informational, request still processing)\n        // 2xx - Do not retry (Success)\n        // 3xx - Do not retry (Redirect)\n        // 4xx - Do not retry (Client errors)\n        // 429 - Retry (\"Too Many Requests\")\n        // 5xx - Retry (Server errors)\n        [100, 199],\n        [429, 429],\n        [500, 599],\n    ];\n    config.statusCodesToRetry = config.statusCodesToRetry || retryRanges;\n    // Put the config back into the err\n    err.config.retryConfig = config;\n    // Determine if we should retry the request\n    const shouldRetryFn = config.shouldRetry || shouldRetryRequest;\n    if (!(await shouldRetryFn(err))) {\n        return { shouldRetry: false, config: err.config };\n    }\n    // Calculate time to wait with exponential backoff.\n    // If this is the first retry, look for a configured retryDelay.\n    const retryDelay = config.currentRetryAttempt ? 0 : (_a = config.retryDelay) !== null && _a !== void 0 ? _a : 100;\n    // Formula: retryDelay + ((2^c - 1 / 2) * 1000)\n    const delay = retryDelay + ((Math.pow(2, config.currentRetryAttempt) - 1) / 2) * 1000;\n    // We're going to retry!  Incremenent the counter.\n    err.config.retryConfig.currentRetryAttempt += 1;\n    // Create a promise that invokes the retry after the backOffDelay\n    const backoff = config.retryBackoff\n        ? config.retryBackoff(err, delay)\n        : new Promise(resolve => {\n            setTimeout(resolve, delay);\n        });\n    // Notify the user if they added an `onRetryAttempt` handler\n    if (config.onRetryAttempt) {\n        config.onRetryAttempt(err);\n    }\n    // Return the promise in which recalls Gaxios to retry the request\n    await backoff;\n    return { shouldRetry: true, config: err.config };\n}\nexports.getRetryConfig = getRetryConfig;\n/**\n * Determine based on config if we should retry the request.\n * @param err The GaxiosError passed to the interceptor.\n */\nfunction shouldRetryRequest(err) {\n    const config = getConfig(err);\n    // node-fetch raises an AbortError if signaled:\n    // https://github.com/bitinn/node-fetch#request-cancellation-with-abortsignal\n    if (err.name === 'AbortError') {\n        return false;\n    }\n    // If there's no config, or retries are disabled, return.\n    if (!config || config.retry === 0) {\n        return false;\n    }\n    // Check if this error has no response (ETIMEDOUT, ENOTFOUND, etc)\n    if (!err.response &&\n        (config.currentRetryAttempt || 0) >= config.noResponseRetries) {\n        return false;\n    }\n    // Only retry with configured HttpMethods.\n    if (!err.config.method ||\n        config.httpMethodsToRetry.indexOf(err.config.method.toUpperCase()) < 0) {\n        return false;\n    }\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    if (err.response && err.response.status) {\n        let isInRange = false;\n        for (const [min, max] of config.statusCodesToRetry) {\n            const status = err.response.status;\n            if (status >= min && status <= max) {\n                isInRange = true;\n                break;\n            }\n        }\n        if (!isInRange) {\n            return false;\n        }\n    }\n    // If we are out of retry attempts, return\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    if (config.currentRetryAttempt >= config.retry) {\n        return false;\n    }\n    return true;\n}\n/**\n * Acquire the raxConfig object from an GaxiosError if available.\n * @param err The Gaxios error with a config object.\n */\nfunction getConfig(err) {\n    if (err && err.config && err.config.retryConfig) {\n        return err.config.retryConfig;\n    }\n    return;\n}\n//# sourceMappingURL=retry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/gaxios@5.1.3/node_modules/gaxios/build/src/retry.js\n");

/***/ })

};
;