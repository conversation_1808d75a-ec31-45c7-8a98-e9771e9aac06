"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+fetch-http-handler@5.0.4";
exports.ids = ["vendor-chunks/@smithy+fetch-http-handler@5.0.4"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/create-request.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/create-request.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRequest: () => (/* binding */ createRequest)\n/* harmony export */ });\nfunction createRequest(url, requestOptions) {\n    return new Request(url, requestOptions);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStmZXRjaC1odHRwLWhhbmRsZXJANS4wLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvZmV0Y2gtaHR0cC1oYW5kbGVyL2Rpc3QtZXMvY3JlYXRlLXJlcXVlc3QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrZmV0Y2gtaHR0cC1oYW5kbGVyQDUuMC40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2ZldGNoLWh0dHAtaGFuZGxlci9kaXN0LWVzL2NyZWF0ZS1yZXF1ZXN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBjcmVhdGVSZXF1ZXN0KHVybCwgcmVxdWVzdE9wdGlvbnMpIHtcbiAgICByZXR1cm4gbmV3IFJlcXVlc3QodXJsLCByZXF1ZXN0T3B0aW9ucyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/create-request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/fetch-http-handler.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/fetch-http-handler.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FetchHttpHandler: () => (/* binding */ FetchHttpHandler),\n/* harmony export */   keepAliveSupport: () => (/* binding */ keepAliveSupport)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_querystring_builder__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/querystring-builder */ \"(rsc)/./node_modules/.pnpm/@smithy+querystring-builder@4.0.4/node_modules/@smithy/querystring-builder/dist-es/index.js\");\n/* harmony import */ var _create_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./create-request */ \"(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/create-request.js\");\n/* harmony import */ var _request_timeout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./request-timeout */ \"(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/request-timeout.js\");\n\n\n\n\nconst keepAliveSupport = {\n    supported: undefined,\n};\nclass FetchHttpHandler {\n    static create(instanceOrOptions) {\n        if (typeof instanceOrOptions?.handle === \"function\") {\n            return instanceOrOptions;\n        }\n        return new FetchHttpHandler(instanceOrOptions);\n    }\n    constructor(options) {\n        if (typeof options === \"function\") {\n            this.configProvider = options().then((opts) => opts || {});\n        }\n        else {\n            this.config = options ?? {};\n            this.configProvider = Promise.resolve(this.config);\n        }\n        if (keepAliveSupport.supported === undefined) {\n            keepAliveSupport.supported = Boolean(typeof Request !== \"undefined\" && \"keepalive\" in (0,_create_request__WEBPACK_IMPORTED_MODULE_2__.createRequest)(\"https://[::1]\"));\n        }\n    }\n    destroy() {\n    }\n    async handle(request, { abortSignal } = {}) {\n        if (!this.config) {\n            this.config = await this.configProvider;\n        }\n        const requestTimeoutInMs = this.config.requestTimeout;\n        const keepAlive = this.config.keepAlive === true;\n        const credentials = this.config.credentials;\n        if (abortSignal?.aborted) {\n            const abortError = new Error(\"Request aborted\");\n            abortError.name = \"AbortError\";\n            return Promise.reject(abortError);\n        }\n        let path = request.path;\n        const queryString = (0,_smithy_querystring_builder__WEBPACK_IMPORTED_MODULE_1__.buildQueryString)(request.query || {});\n        if (queryString) {\n            path += `?${queryString}`;\n        }\n        if (request.fragment) {\n            path += `#${request.fragment}`;\n        }\n        let auth = \"\";\n        if (request.username != null || request.password != null) {\n            const username = request.username ?? \"\";\n            const password = request.password ?? \"\";\n            auth = `${username}:${password}@`;\n        }\n        const { port, method } = request;\n        const url = `${request.protocol}//${auth}${request.hostname}${port ? `:${port}` : \"\"}${path}`;\n        const body = method === \"GET\" || method === \"HEAD\" ? undefined : request.body;\n        const requestOptions = {\n            body,\n            headers: new Headers(request.headers),\n            method: method,\n            credentials,\n        };\n        if (this.config?.cache) {\n            requestOptions.cache = this.config.cache;\n        }\n        if (body) {\n            requestOptions.duplex = \"half\";\n        }\n        if (typeof AbortController !== \"undefined\") {\n            requestOptions.signal = abortSignal;\n        }\n        if (keepAliveSupport.supported) {\n            requestOptions.keepalive = keepAlive;\n        }\n        if (typeof this.config.requestInit === \"function\") {\n            Object.assign(requestOptions, this.config.requestInit(request));\n        }\n        let removeSignalEventListener = () => { };\n        const fetchRequest = (0,_create_request__WEBPACK_IMPORTED_MODULE_2__.createRequest)(url, requestOptions);\n        const raceOfPromises = [\n            fetch(fetchRequest).then((response) => {\n                const fetchHeaders = response.headers;\n                const transformedHeaders = {};\n                for (const pair of fetchHeaders.entries()) {\n                    transformedHeaders[pair[0]] = pair[1];\n                }\n                const hasReadableStream = response.body != undefined;\n                if (!hasReadableStream) {\n                    return response.blob().then((body) => ({\n                        response: new _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpResponse({\n                            headers: transformedHeaders,\n                            reason: response.statusText,\n                            statusCode: response.status,\n                            body,\n                        }),\n                    }));\n                }\n                return {\n                    response: new _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpResponse({\n                        headers: transformedHeaders,\n                        reason: response.statusText,\n                        statusCode: response.status,\n                        body: response.body,\n                    }),\n                };\n            }),\n            (0,_request_timeout__WEBPACK_IMPORTED_MODULE_3__.requestTimeout)(requestTimeoutInMs),\n        ];\n        if (abortSignal) {\n            raceOfPromises.push(new Promise((resolve, reject) => {\n                const onAbort = () => {\n                    const abortError = new Error(\"Request aborted\");\n                    abortError.name = \"AbortError\";\n                    reject(abortError);\n                };\n                if (typeof abortSignal.addEventListener === \"function\") {\n                    const signal = abortSignal;\n                    signal.addEventListener(\"abort\", onAbort, { once: true });\n                    removeSignalEventListener = () => signal.removeEventListener(\"abort\", onAbort);\n                }\n                else {\n                    abortSignal.onabort = onAbort;\n                }\n            }));\n        }\n        return Promise.race(raceOfPromises).finally(removeSignalEventListener);\n    }\n    updateHttpClientConfig(key, value) {\n        this.config = undefined;\n        this.configProvider = this.configProvider.then((config) => {\n            config[key] = value;\n            return config;\n        });\n    }\n    httpHandlerConfigs() {\n        return this.config ?? {};\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/fetch-http-handler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/index.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/index.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FetchHttpHandler: () => (/* reexport safe */ _fetch_http_handler__WEBPACK_IMPORTED_MODULE_0__.FetchHttpHandler),\n/* harmony export */   keepAliveSupport: () => (/* reexport safe */ _fetch_http_handler__WEBPACK_IMPORTED_MODULE_0__.keepAliveSupport),\n/* harmony export */   streamCollector: () => (/* reexport safe */ _stream_collector__WEBPACK_IMPORTED_MODULE_1__.streamCollector)\n/* harmony export */ });\n/* harmony import */ var _fetch_http_handler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./fetch-http-handler */ \"(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/fetch-http-handler.js\");\n/* harmony import */ var _stream_collector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./stream-collector */ \"(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/stream-collector.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStmZXRjaC1odHRwLWhhbmRsZXJANS4wLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvZmV0Y2gtaHR0cC1oYW5kbGVyL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBcUM7QUFDRiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrZmV0Y2gtaHR0cC1oYW5kbGVyQDUuMC40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2ZldGNoLWh0dHAtaGFuZGxlci9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2ZldGNoLWh0dHAtaGFuZGxlclwiO1xuZXhwb3J0ICogZnJvbSBcIi4vc3RyZWFtLWNvbGxlY3RvclwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/request-timeout.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/request-timeout.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   requestTimeout: () => (/* binding */ requestTimeout)\n/* harmony export */ });\nfunction requestTimeout(timeoutInMs = 0) {\n    return new Promise((resolve, reject) => {\n        if (timeoutInMs) {\n            setTimeout(() => {\n                const timeoutError = new Error(`Request did not complete within ${timeoutInMs} ms`);\n                timeoutError.name = \"TimeoutError\";\n                reject(timeoutError);\n            }, timeoutInMs);\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStmZXRjaC1odHRwLWhhbmRsZXJANS4wLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvZmV0Y2gtaHR0cC1oYW5kbGVyL2Rpc3QtZXMvcmVxdWVzdC10aW1lb3V0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLGtGQUFrRixhQUFhO0FBQy9GO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2ZldGNoLWh0dHAtaGFuZGxlckA1LjAuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9mZXRjaC1odHRwLWhhbmRsZXIvZGlzdC1lcy9yZXF1ZXN0LXRpbWVvdXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHJlcXVlc3RUaW1lb3V0KHRpbWVvdXRJbk1zID0gMCkge1xuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICAgIGlmICh0aW1lb3V0SW5Ncykge1xuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgdGltZW91dEVycm9yID0gbmV3IEVycm9yKGBSZXF1ZXN0IGRpZCBub3QgY29tcGxldGUgd2l0aGluICR7dGltZW91dEluTXN9IG1zYCk7XG4gICAgICAgICAgICAgICAgdGltZW91dEVycm9yLm5hbWUgPSBcIlRpbWVvdXRFcnJvclwiO1xuICAgICAgICAgICAgICAgIHJlamVjdCh0aW1lb3V0RXJyb3IpO1xuICAgICAgICAgICAgfSwgdGltZW91dEluTXMpO1xuICAgICAgICB9XG4gICAgfSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/request-timeout.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/stream-collector.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/stream-collector.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   streamCollector: () => (/* binding */ streamCollector)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_base64__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-base64 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js\");\n\nconst streamCollector = async (stream) => {\n    if ((typeof Blob === \"function\" && stream instanceof Blob) || stream.constructor?.name === \"Blob\") {\n        if (Blob.prototype.arrayBuffer !== undefined) {\n            return new Uint8Array(await stream.arrayBuffer());\n        }\n        return collectBlob(stream);\n    }\n    return collectStream(stream);\n};\nasync function collectBlob(blob) {\n    const base64 = await readToBase64(blob);\n    const arrayBuffer = (0,_smithy_util_base64__WEBPACK_IMPORTED_MODULE_0__.fromBase64)(base64);\n    return new Uint8Array(arrayBuffer);\n}\nasync function collectStream(stream) {\n    const chunks = [];\n    const reader = stream.getReader();\n    let isDone = false;\n    let length = 0;\n    while (!isDone) {\n        const { done, value } = await reader.read();\n        if (value) {\n            chunks.push(value);\n            length += value.length;\n        }\n        isDone = done;\n    }\n    const collected = new Uint8Array(length);\n    let offset = 0;\n    for (const chunk of chunks) {\n        collected.set(chunk, offset);\n        offset += chunk.length;\n    }\n    return collected;\n}\nfunction readToBase64(blob) {\n    return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onloadend = () => {\n            if (reader.readyState !== 2) {\n                return reject(new Error(\"Reader aborted too early\"));\n            }\n            const result = (reader.result ?? \"\");\n            const commaIndex = result.indexOf(\",\");\n            const dataOffset = commaIndex > -1 ? commaIndex + 1 : result.length;\n            resolve(result.substring(dataOffset));\n        };\n        reader.onabort = () => reject(new Error(\"Read aborted\"));\n        reader.onerror = () => reject(reader.error);\n        reader.readAsDataURL(blob);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+fetch-http-handler@5.0.4/node_modules/@smithy/fetch-http-handler/dist-es/stream-collector.js\n");

/***/ })

};
;