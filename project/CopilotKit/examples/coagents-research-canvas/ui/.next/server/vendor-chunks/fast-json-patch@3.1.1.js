"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-json-patch@3.1.1";
exports.ids = ["vendor-chunks/fast-json-patch@3.1.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/index.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/index.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JsonPatchError: () => (/* reexport safe */ _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__.PatchError),\n/* harmony export */   _areEquals: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__._areEquals),\n/* harmony export */   applyOperation: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__.applyOperation),\n/* harmony export */   applyPatch: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__.applyPatch),\n/* harmony export */   applyReducer: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__.applyReducer),\n/* harmony export */   compare: () => (/* reexport safe */ _module_duplex_mjs__WEBPACK_IMPORTED_MODULE_1__.compare),\n/* harmony export */   deepClone: () => (/* reexport safe */ _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__._deepClone),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   escapePathComponent: () => (/* reexport safe */ _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__.escapePathComponent),\n/* harmony export */   generate: () => (/* reexport safe */ _module_duplex_mjs__WEBPACK_IMPORTED_MODULE_1__.generate),\n/* harmony export */   getValueByPointer: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__.getValueByPointer),\n/* harmony export */   observe: () => (/* reexport safe */ _module_duplex_mjs__WEBPACK_IMPORTED_MODULE_1__.observe),\n/* harmony export */   unescapePathComponent: () => (/* reexport safe */ _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__.unescapePathComponent),\n/* harmony export */   unobserve: () => (/* reexport safe */ _module_duplex_mjs__WEBPACK_IMPORTED_MODULE_1__.unobserve),\n/* harmony export */   validate: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__.validate),\n/* harmony export */   validator: () => (/* reexport safe */ _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__.validator)\n/* harmony export */ });\n/* harmony import */ var _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./module/core.mjs */ \"(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/core.mjs\");\n/* harmony import */ var _module_duplex_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./module/duplex.mjs */ \"(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/duplex.mjs\");\n/* harmony import */ var _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./module/helpers.mjs */ \"(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/helpers.mjs\");\n\n\n\n\n\n/**\n * Default export for backwards compat\n */\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Object.assign({}, _module_core_mjs__WEBPACK_IMPORTED_MODULE_0__, _module_duplex_mjs__WEBPACK_IMPORTED_MODULE_1__, {\n    JsonPatchError: _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__.PatchError,\n    deepClone: _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__._deepClone,\n    escapePathComponent: _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__.escapePathComponent,\n    unescapePathComponent: _module_helpers_mjs__WEBPACK_IMPORTED_MODULE_2__.unescapePathComponent\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZmFzdC1qc29uLXBhdGNoQDMuMS4xL25vZGVfbW9kdWxlcy9mYXN0LWpzb24tcGF0Y2gvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBa0M7QUFDRTtBQU1OOzs7QUFHOUI7QUFDQTtBQUNBOztBQUUwQztBQUNJO0FBTWhCOztBQUU5QixpRUFBZSxnQkFBZ0IsRUFBRSw2Q0FBSSxFQUFFLCtDQUFNO0FBQzdDLGtCQUFrQjtBQUNsQixhQUFhO0FBQ2IsdUJBQXVCO0FBQ3ZCLHlCQUF5QjtBQUN6QixDQUFDLENBQUMiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9mYXN0LWpzb24tcGF0Y2hAMy4xLjEvbm9kZV9tb2R1bGVzL2Zhc3QtanNvbi1wYXRjaC9pbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9tb2R1bGUvY29yZS5tanMnO1xuZXhwb3J0ICogZnJvbSAnLi9tb2R1bGUvZHVwbGV4Lm1qcyc7XG5leHBvcnQge1xuICAgIFBhdGNoRXJyb3IgYXMgSnNvblBhdGNoRXJyb3IsXG4gICAgX2RlZXBDbG9uZSBhcyBkZWVwQ2xvbmUsXG4gICAgZXNjYXBlUGF0aENvbXBvbmVudCxcbiAgICB1bmVzY2FwZVBhdGhDb21wb25lbnRcbn0gZnJvbSAnLi9tb2R1bGUvaGVscGVycy5tanMnO1xuXG5cbi8qKlxuICogRGVmYXVsdCBleHBvcnQgZm9yIGJhY2t3YXJkcyBjb21wYXRcbiAqL1xuXG5pbXBvcnQgKiBhcyBjb3JlIGZyb20gJy4vbW9kdWxlL2NvcmUubWpzJztcbmltcG9ydCAqIGFzIGR1cGxleCBmcm9tICcuL21vZHVsZS9kdXBsZXgubWpzJztcbmltcG9ydCB7XG4gICAgUGF0Y2hFcnJvciBhcyBKc29uUGF0Y2hFcnJvcixcbiAgICBfZGVlcENsb25lIGFzIGRlZXBDbG9uZSxcbiAgICBlc2NhcGVQYXRoQ29tcG9uZW50LFxuICAgIHVuZXNjYXBlUGF0aENvbXBvbmVudFxufSBmcm9tICcuL21vZHVsZS9oZWxwZXJzLm1qcyc7XG5cbmV4cG9ydCBkZWZhdWx0IE9iamVjdC5hc3NpZ24oe30sIGNvcmUsIGR1cGxleCwge1xuICAgIEpzb25QYXRjaEVycm9yLFxuICAgIGRlZXBDbG9uZSxcbiAgICBlc2NhcGVQYXRoQ29tcG9uZW50LFxuICAgIHVuZXNjYXBlUGF0aENvbXBvbmVudFxufSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/core.mjs":
/*!***********************************************************************************************!*\
  !*** ./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/core.mjs ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JsonPatchError: () => (/* binding */ JsonPatchError),\n/* harmony export */   _areEquals: () => (/* binding */ _areEquals),\n/* harmony export */   applyOperation: () => (/* binding */ applyOperation),\n/* harmony export */   applyPatch: () => (/* binding */ applyPatch),\n/* harmony export */   applyReducer: () => (/* binding */ applyReducer),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   getValueByPointer: () => (/* binding */ getValueByPointer),\n/* harmony export */   validate: () => (/* binding */ validate),\n/* harmony export */   validator: () => (/* binding */ validator)\n/* harmony export */ });\n/* harmony import */ var _helpers_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers.mjs */ \"(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/helpers.mjs\");\n\nvar JsonPatchError = _helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.PatchError;\nvar deepClone = _helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone;\n/* We use a Javascript hash to store each\n function. Each hash entry (property) uses\n the operation identifiers specified in rfc6902.\n In this way, we can map each patch operation\n to its dedicated function in efficient way.\n */\n/* The operations applicable to an object */\nvar objOps = {\n    add: function (obj, key, document) {\n        obj[key] = this.value;\n        return { newDocument: document };\n    },\n    remove: function (obj, key, document) {\n        var removed = obj[key];\n        delete obj[key];\n        return { newDocument: document, removed: removed };\n    },\n    replace: function (obj, key, document) {\n        var removed = obj[key];\n        obj[key] = this.value;\n        return { newDocument: document, removed: removed };\n    },\n    move: function (obj, key, document) {\n        /* in case move target overwrites an existing value,\n        return the removed value, this can be taxing performance-wise,\n        and is potentially unneeded */\n        var removed = getValueByPointer(document, this.path);\n        if (removed) {\n            removed = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(removed);\n        }\n        var originalValue = applyOperation(document, { op: \"remove\", path: this.from }).removed;\n        applyOperation(document, { op: \"add\", path: this.path, value: originalValue });\n        return { newDocument: document, removed: removed };\n    },\n    copy: function (obj, key, document) {\n        var valueToCopy = getValueByPointer(document, this.from);\n        // enforce copy by value so further operations don't affect source (see issue #177)\n        applyOperation(document, { op: \"add\", path: this.path, value: (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(valueToCopy) });\n        return { newDocument: document };\n    },\n    test: function (obj, key, document) {\n        return { newDocument: document, test: _areEquals(obj[key], this.value) };\n    },\n    _get: function (obj, key, document) {\n        this.value = obj[key];\n        return { newDocument: document };\n    }\n};\n/* The operations applicable to an array. Many are the same as for the object */\nvar arrOps = {\n    add: function (arr, i, document) {\n        if ((0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.isInteger)(i)) {\n            arr.splice(i, 0, this.value);\n        }\n        else { // array props\n            arr[i] = this.value;\n        }\n        // this may be needed when using '-' in an array\n        return { newDocument: document, index: i };\n    },\n    remove: function (arr, i, document) {\n        var removedList = arr.splice(i, 1);\n        return { newDocument: document, removed: removedList[0] };\n    },\n    replace: function (arr, i, document) {\n        var removed = arr[i];\n        arr[i] = this.value;\n        return { newDocument: document, removed: removed };\n    },\n    move: objOps.move,\n    copy: objOps.copy,\n    test: objOps.test,\n    _get: objOps._get\n};\n/**\n * Retrieves a value from a JSON document by a JSON pointer.\n * Returns the value.\n *\n * @param document The document to get the value from\n * @param pointer an escaped JSON pointer\n * @return The retrieved value\n */\nfunction getValueByPointer(document, pointer) {\n    if (pointer == '') {\n        return document;\n    }\n    var getOriginalDestination = { op: \"_get\", path: pointer };\n    applyOperation(document, getOriginalDestination);\n    return getOriginalDestination.value;\n}\n/**\n * Apply a single JSON Patch Operation on a JSON document.\n * Returns the {newDocument, result} of the operation.\n * It modifies the `document` and `operation` objects - it gets the values by reference.\n * If you would like to avoid touching your values, clone them:\n * `jsonpatch.applyOperation(document, jsonpatch._deepClone(operation))`.\n *\n * @param document The document to patch\n * @param operation The operation to apply\n * @param validateOperation `false` is without validation, `true` to use default jsonpatch's validation, or you can pass a `validateOperation` callback to be used for validation.\n * @param mutateDocument Whether to mutate the original document or clone it before applying\n * @param banPrototypeModifications Whether to ban modifications to `__proto__`, defaults to `true`.\n * @return `{newDocument, result}` after the operation\n */\nfunction applyOperation(document, operation, validateOperation, mutateDocument, banPrototypeModifications, index) {\n    if (validateOperation === void 0) { validateOperation = false; }\n    if (mutateDocument === void 0) { mutateDocument = true; }\n    if (banPrototypeModifications === void 0) { banPrototypeModifications = true; }\n    if (index === void 0) { index = 0; }\n    if (validateOperation) {\n        if (typeof validateOperation == 'function') {\n            validateOperation(operation, 0, document, operation.path);\n        }\n        else {\n            validator(operation, 0);\n        }\n    }\n    /* ROOT OPERATIONS */\n    if (operation.path === \"\") {\n        var returnValue = { newDocument: document };\n        if (operation.op === 'add') {\n            returnValue.newDocument = operation.value;\n            return returnValue;\n        }\n        else if (operation.op === 'replace') {\n            returnValue.newDocument = operation.value;\n            returnValue.removed = document; //document we removed\n            return returnValue;\n        }\n        else if (operation.op === 'move' || operation.op === 'copy') { // it's a move or copy to root\n            returnValue.newDocument = getValueByPointer(document, operation.from); // get the value by json-pointer in `from` field\n            if (operation.op === 'move') { // report removed item\n                returnValue.removed = document;\n            }\n            return returnValue;\n        }\n        else if (operation.op === 'test') {\n            returnValue.test = _areEquals(document, operation.value);\n            if (returnValue.test === false) {\n                throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n            }\n            returnValue.newDocument = document;\n            return returnValue;\n        }\n        else if (operation.op === 'remove') { // a remove on root\n            returnValue.removed = document;\n            returnValue.newDocument = null;\n            return returnValue;\n        }\n        else if (operation.op === '_get') {\n            operation.value = document;\n            return returnValue;\n        }\n        else { /* bad operation */\n            if (validateOperation) {\n                throw new JsonPatchError('Operation `op` property is not one of operations defined in RFC-6902', 'OPERATION_OP_INVALID', index, operation, document);\n            }\n            else {\n                return returnValue;\n            }\n        }\n    } /* END ROOT OPERATIONS */\n    else {\n        if (!mutateDocument) {\n            document = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(document);\n        }\n        var path = operation.path || \"\";\n        var keys = path.split('/');\n        var obj = document;\n        var t = 1; //skip empty element - http://jsperf.com/to-shift-or-not-to-shift\n        var len = keys.length;\n        var existingPathFragment = undefined;\n        var key = void 0;\n        var validateFunction = void 0;\n        if (typeof validateOperation == 'function') {\n            validateFunction = validateOperation;\n        }\n        else {\n            validateFunction = validator;\n        }\n        while (true) {\n            key = keys[t];\n            if (key && key.indexOf('~') != -1) {\n                key = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.unescapePathComponent)(key);\n            }\n            if (banPrototypeModifications &&\n                (key == '__proto__' ||\n                    (key == 'prototype' && t > 0 && keys[t - 1] == 'constructor'))) {\n                throw new TypeError('JSON-Patch: modifying `__proto__` or `constructor/prototype` prop is banned for security reasons, if this was on purpose, please set `banPrototypeModifications` flag false and pass it to this function. More info in fast-json-patch README');\n            }\n            if (validateOperation) {\n                if (existingPathFragment === undefined) {\n                    if (obj[key] === undefined) {\n                        existingPathFragment = keys.slice(0, t).join('/');\n                    }\n                    else if (t == len - 1) {\n                        existingPathFragment = operation.path;\n                    }\n                    if (existingPathFragment !== undefined) {\n                        validateFunction(operation, 0, document, existingPathFragment);\n                    }\n                }\n            }\n            t++;\n            if (Array.isArray(obj)) {\n                if (key === '-') {\n                    key = obj.length;\n                }\n                else {\n                    if (validateOperation && !(0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.isInteger)(key)) {\n                        throw new JsonPatchError(\"Expected an unsigned base-10 integer value, making the new referenced value the array element with the zero-based index\", \"OPERATION_PATH_ILLEGAL_ARRAY_INDEX\", index, operation, document);\n                    } // only parse key when it's an integer for `arr.prop` to work\n                    else if ((0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.isInteger)(key)) {\n                        key = ~~key;\n                    }\n                }\n                if (t >= len) {\n                    if (validateOperation && operation.op === \"add\" && key > obj.length) {\n                        throw new JsonPatchError(\"The specified index MUST NOT be greater than the number of elements in the array\", \"OPERATION_VALUE_OUT_OF_BOUNDS\", index, operation, document);\n                    }\n                    var returnValue = arrOps[operation.op].call(operation, obj, key, document); // Apply patch\n                    if (returnValue.test === false) {\n                        throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n                    }\n                    return returnValue;\n                }\n            }\n            else {\n                if (t >= len) {\n                    var returnValue = objOps[operation.op].call(operation, obj, key, document); // Apply patch\n                    if (returnValue.test === false) {\n                        throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n                    }\n                    return returnValue;\n                }\n            }\n            obj = obj[key];\n            // If we have more keys in the path, but the next value isn't a non-null object,\n            // throw an OPERATION_PATH_UNRESOLVABLE error instead of iterating again.\n            if (validateOperation && t < len && (!obj || typeof obj !== \"object\")) {\n                throw new JsonPatchError('Cannot perform operation at the desired path', 'OPERATION_PATH_UNRESOLVABLE', index, operation, document);\n            }\n        }\n    }\n}\n/**\n * Apply a full JSON Patch array on a JSON document.\n * Returns the {newDocument, result} of the patch.\n * It modifies the `document` object and `patch` - it gets the values by reference.\n * If you would like to avoid touching your values, clone them:\n * `jsonpatch.applyPatch(document, jsonpatch._deepClone(patch))`.\n *\n * @param document The document to patch\n * @param patch The patch to apply\n * @param validateOperation `false` is without validation, `true` to use default jsonpatch's validation, or you can pass a `validateOperation` callback to be used for validation.\n * @param mutateDocument Whether to mutate the original document or clone it before applying\n * @param banPrototypeModifications Whether to ban modifications to `__proto__`, defaults to `true`.\n * @return An array of `{newDocument, result}` after the patch\n */\nfunction applyPatch(document, patch, validateOperation, mutateDocument, banPrototypeModifications) {\n    if (mutateDocument === void 0) { mutateDocument = true; }\n    if (banPrototypeModifications === void 0) { banPrototypeModifications = true; }\n    if (validateOperation) {\n        if (!Array.isArray(patch)) {\n            throw new JsonPatchError('Patch sequence must be an array', 'SEQUENCE_NOT_AN_ARRAY');\n        }\n    }\n    if (!mutateDocument) {\n        document = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(document);\n    }\n    var results = new Array(patch.length);\n    for (var i = 0, length_1 = patch.length; i < length_1; i++) {\n        // we don't need to pass mutateDocument argument because if it was true, we already deep cloned the object, we'll just pass `true`\n        results[i] = applyOperation(document, patch[i], validateOperation, true, banPrototypeModifications, i);\n        document = results[i].newDocument; // in case root was replaced\n    }\n    results.newDocument = document;\n    return results;\n}\n/**\n * Apply a single JSON Patch Operation on a JSON document.\n * Returns the updated document.\n * Suitable as a reducer.\n *\n * @param document The document to patch\n * @param operation The operation to apply\n * @return The updated document\n */\nfunction applyReducer(document, operation, index) {\n    var operationResult = applyOperation(document, operation);\n    if (operationResult.test === false) { // failed test\n        throw new JsonPatchError(\"Test operation failed\", 'TEST_OPERATION_FAILED', index, operation, document);\n    }\n    return operationResult.newDocument;\n}\n/**\n * Validates a single operation. Called from `jsonpatch.validate`. Throws `JsonPatchError` in case of an error.\n * @param {object} operation - operation object (patch)\n * @param {number} index - index of operation in the sequence\n * @param {object} [document] - object where the operation is supposed to be applied\n * @param {string} [existingPathFragment] - comes along with `document`\n */\nfunction validator(operation, index, document, existingPathFragment) {\n    if (typeof operation !== 'object' || operation === null || Array.isArray(operation)) {\n        throw new JsonPatchError('Operation is not an object', 'OPERATION_NOT_AN_OBJECT', index, operation, document);\n    }\n    else if (!objOps[operation.op]) {\n        throw new JsonPatchError('Operation `op` property is not one of operations defined in RFC-6902', 'OPERATION_OP_INVALID', index, operation, document);\n    }\n    else if (typeof operation.path !== 'string') {\n        throw new JsonPatchError('Operation `path` property is not a string', 'OPERATION_PATH_INVALID', index, operation, document);\n    }\n    else if (operation.path.indexOf('/') !== 0 && operation.path.length > 0) {\n        // paths that aren't empty string should start with \"/\"\n        throw new JsonPatchError('Operation `path` property must start with \"/\"', 'OPERATION_PATH_INVALID', index, operation, document);\n    }\n    else if ((operation.op === 'move' || operation.op === 'copy') && typeof operation.from !== 'string') {\n        throw new JsonPatchError('Operation `from` property is not present (applicable in `move` and `copy` operations)', 'OPERATION_FROM_REQUIRED', index, operation, document);\n    }\n    else if ((operation.op === 'add' || operation.op === 'replace' || operation.op === 'test') && operation.value === undefined) {\n        throw new JsonPatchError('Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)', 'OPERATION_VALUE_REQUIRED', index, operation, document);\n    }\n    else if ((operation.op === 'add' || operation.op === 'replace' || operation.op === 'test') && (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.hasUndefined)(operation.value)) {\n        throw new JsonPatchError('Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)', 'OPERATION_VALUE_CANNOT_CONTAIN_UNDEFINED', index, operation, document);\n    }\n    else if (document) {\n        if (operation.op == \"add\") {\n            var pathLen = operation.path.split(\"/\").length;\n            var existingPathLen = existingPathFragment.split(\"/\").length;\n            if (pathLen !== existingPathLen + 1 && pathLen !== existingPathLen) {\n                throw new JsonPatchError('Cannot perform an `add` operation at the desired path', 'OPERATION_PATH_CANNOT_ADD', index, operation, document);\n            }\n        }\n        else if (operation.op === 'replace' || operation.op === 'remove' || operation.op === '_get') {\n            if (operation.path !== existingPathFragment) {\n                throw new JsonPatchError('Cannot perform the operation at a path that does not exist', 'OPERATION_PATH_UNRESOLVABLE', index, operation, document);\n            }\n        }\n        else if (operation.op === 'move' || operation.op === 'copy') {\n            var existingValue = { op: \"_get\", path: operation.from, value: undefined };\n            var error = validate([existingValue], document);\n            if (error && error.name === 'OPERATION_PATH_UNRESOLVABLE') {\n                throw new JsonPatchError('Cannot perform the operation from a path that does not exist', 'OPERATION_FROM_UNRESOLVABLE', index, operation, document);\n            }\n        }\n    }\n}\n/**\n * Validates a sequence of operations. If `document` parameter is provided, the sequence is additionally validated against the object document.\n * If error is encountered, returns a JsonPatchError object\n * @param sequence\n * @param document\n * @returns {JsonPatchError|undefined}\n */\nfunction validate(sequence, document, externalValidator) {\n    try {\n        if (!Array.isArray(sequence)) {\n            throw new JsonPatchError('Patch sequence must be an array', 'SEQUENCE_NOT_AN_ARRAY');\n        }\n        if (document) {\n            //clone document and sequence so that we can safely try applying operations\n            applyPatch((0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(document), (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(sequence), externalValidator || true);\n        }\n        else {\n            externalValidator = externalValidator || validator;\n            for (var i = 0; i < sequence.length; i++) {\n                externalValidator(sequence[i], i, document, undefined);\n            }\n        }\n    }\n    catch (e) {\n        if (e instanceof JsonPatchError) {\n            return e;\n        }\n        else {\n            throw e;\n        }\n    }\n}\n// based on https://github.com/epoberezkin/fast-deep-equal\n// MIT License\n// Copyright (c) 2017 Evgeny Poberezkin\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\nfunction _areEquals(a, b) {\n    if (a === b)\n        return true;\n    if (a && b && typeof a == 'object' && typeof b == 'object') {\n        var arrA = Array.isArray(a), arrB = Array.isArray(b), i, length, key;\n        if (arrA && arrB) {\n            length = a.length;\n            if (length != b.length)\n                return false;\n            for (i = length; i-- !== 0;)\n                if (!_areEquals(a[i], b[i]))\n                    return false;\n            return true;\n        }\n        if (arrA != arrB)\n            return false;\n        var keys = Object.keys(a);\n        length = keys.length;\n        if (length !== Object.keys(b).length)\n            return false;\n        for (i = length; i-- !== 0;)\n            if (!b.hasOwnProperty(keys[i]))\n                return false;\n        for (i = length; i-- !== 0;) {\n            key = keys[i];\n            if (!_areEquals(a[key], b[key]))\n                return false;\n        }\n        return true;\n    }\n    return a !== a && b !== b;\n}\n;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/core.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/duplex.mjs":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/duplex.mjs ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compare: () => (/* binding */ compare),\n/* harmony export */   generate: () => (/* binding */ generate),\n/* harmony export */   observe: () => (/* binding */ observe),\n/* harmony export */   unobserve: () => (/* binding */ unobserve)\n/* harmony export */ });\n/* harmony import */ var _helpers_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers.mjs */ \"(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/helpers.mjs\");\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./core.mjs */ \"(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/core.mjs\");\n/*!\n * https://github.com/Starcounter-Jack/JSON-Patch\n * (c) 2017-2021 Joachim Wester\n * MIT license\n */\n\n\nvar beforeDict = new WeakMap();\nvar Mirror = /** @class */ (function () {\n    function Mirror(obj) {\n        this.observers = new Map();\n        this.obj = obj;\n    }\n    return Mirror;\n}());\nvar ObserverInfo = /** @class */ (function () {\n    function ObserverInfo(callback, observer) {\n        this.callback = callback;\n        this.observer = observer;\n    }\n    return ObserverInfo;\n}());\nfunction getMirror(obj) {\n    return beforeDict.get(obj);\n}\nfunction getObserverFromMirror(mirror, callback) {\n    return mirror.observers.get(callback);\n}\nfunction removeObserverFromMirror(mirror, observer) {\n    mirror.observers.delete(observer.callback);\n}\n/**\n * Detach an observer from an object\n */\nfunction unobserve(root, observer) {\n    observer.unobserve();\n}\n/**\n * Observes changes made to an object, which can then be retrieved using generate\n */\nfunction observe(obj, callback) {\n    var patches = [];\n    var observer;\n    var mirror = getMirror(obj);\n    if (!mirror) {\n        mirror = new Mirror(obj);\n        beforeDict.set(obj, mirror);\n    }\n    else {\n        var observerInfo = getObserverFromMirror(mirror, callback);\n        observer = observerInfo && observerInfo.observer;\n    }\n    if (observer) {\n        return observer;\n    }\n    observer = {};\n    mirror.value = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(obj);\n    if (callback) {\n        observer.callback = callback;\n        observer.next = null;\n        var dirtyCheck = function () {\n            generate(observer);\n        };\n        var fastCheck = function () {\n            clearTimeout(observer.next);\n            observer.next = setTimeout(dirtyCheck);\n        };\n        if (typeof window !== 'undefined') { //not Node\n            window.addEventListener('mouseup', fastCheck);\n            window.addEventListener('keyup', fastCheck);\n            window.addEventListener('mousedown', fastCheck);\n            window.addEventListener('keydown', fastCheck);\n            window.addEventListener('change', fastCheck);\n        }\n    }\n    observer.patches = patches;\n    observer.object = obj;\n    observer.unobserve = function () {\n        generate(observer);\n        clearTimeout(observer.next);\n        removeObserverFromMirror(mirror, observer);\n        if (typeof window !== 'undefined') {\n            window.removeEventListener('mouseup', fastCheck);\n            window.removeEventListener('keyup', fastCheck);\n            window.removeEventListener('mousedown', fastCheck);\n            window.removeEventListener('keydown', fastCheck);\n            window.removeEventListener('change', fastCheck);\n        }\n    };\n    mirror.observers.set(callback, new ObserverInfo(callback, observer));\n    return observer;\n}\n/**\n * Generate an array of patches from an observer\n */\nfunction generate(observer, invertible) {\n    if (invertible === void 0) { invertible = false; }\n    var mirror = beforeDict.get(observer.object);\n    _generate(mirror.value, observer.object, observer.patches, \"\", invertible);\n    if (observer.patches.length) {\n        (0,_core_mjs__WEBPACK_IMPORTED_MODULE_1__.applyPatch)(mirror.value, observer.patches);\n    }\n    var temp = observer.patches;\n    if (temp.length > 0) {\n        observer.patches = [];\n        if (observer.callback) {\n            observer.callback(temp);\n        }\n    }\n    return temp;\n}\n// Dirty check if obj is different from mirror, generate patches and update mirror\nfunction _generate(mirror, obj, patches, path, invertible) {\n    if (obj === mirror) {\n        return;\n    }\n    if (typeof obj.toJSON === \"function\") {\n        obj = obj.toJSON();\n    }\n    var newKeys = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._objectKeys)(obj);\n    var oldKeys = (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._objectKeys)(mirror);\n    var changed = false;\n    var deleted = false;\n    //if ever \"move\" operation is implemented here, make sure this test runs OK: \"should not generate the same patch twice (move)\"\n    for (var t = oldKeys.length - 1; t >= 0; t--) {\n        var key = oldKeys[t];\n        var oldVal = mirror[key];\n        if ((0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.hasOwnProperty)(obj, key) && !(obj[key] === undefined && oldVal !== undefined && Array.isArray(obj) === false)) {\n            var newVal = obj[key];\n            if (typeof oldVal == \"object\" && oldVal != null && typeof newVal == \"object\" && newVal != null && Array.isArray(oldVal) === Array.isArray(newVal)) {\n                _generate(oldVal, newVal, patches, path + \"/\" + (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.escapePathComponent)(key), invertible);\n            }\n            else {\n                if (oldVal !== newVal) {\n                    changed = true;\n                    if (invertible) {\n                        patches.push({ op: \"test\", path: path + \"/\" + (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.escapePathComponent)(key), value: (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(oldVal) });\n                    }\n                    patches.push({ op: \"replace\", path: path + \"/\" + (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.escapePathComponent)(key), value: (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(newVal) });\n                }\n            }\n        }\n        else if (Array.isArray(mirror) === Array.isArray(obj)) {\n            if (invertible) {\n                patches.push({ op: \"test\", path: path + \"/\" + (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.escapePathComponent)(key), value: (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(oldVal) });\n            }\n            patches.push({ op: \"remove\", path: path + \"/\" + (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.escapePathComponent)(key) });\n            deleted = true; // property has been deleted\n        }\n        else {\n            if (invertible) {\n                patches.push({ op: \"test\", path: path, value: mirror });\n            }\n            patches.push({ op: \"replace\", path: path, value: obj });\n            changed = true;\n        }\n    }\n    if (!deleted && newKeys.length == oldKeys.length) {\n        return;\n    }\n    for (var t = 0; t < newKeys.length; t++) {\n        var key = newKeys[t];\n        if (!(0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.hasOwnProperty)(mirror, key) && obj[key] !== undefined) {\n            patches.push({ op: \"add\", path: path + \"/\" + (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__.escapePathComponent)(key), value: (0,_helpers_mjs__WEBPACK_IMPORTED_MODULE_0__._deepClone)(obj[key]) });\n        }\n    }\n}\n/**\n * Create an array of patches from the differences in two objects\n */\nfunction compare(tree1, tree2, invertible) {\n    if (invertible === void 0) { invertible = false; }\n    var patches = [];\n    _generate(tree1, tree2, patches, '', invertible);\n    return patches;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/duplex.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/helpers.mjs":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/helpers.mjs ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PatchError: () => (/* binding */ PatchError),\n/* harmony export */   _deepClone: () => (/* binding */ _deepClone),\n/* harmony export */   _getPathRecursive: () => (/* binding */ _getPathRecursive),\n/* harmony export */   _objectKeys: () => (/* binding */ _objectKeys),\n/* harmony export */   escapePathComponent: () => (/* binding */ escapePathComponent),\n/* harmony export */   getPath: () => (/* binding */ getPath),\n/* harmony export */   hasOwnProperty: () => (/* binding */ hasOwnProperty),\n/* harmony export */   hasUndefined: () => (/* binding */ hasUndefined),\n/* harmony export */   isInteger: () => (/* binding */ isInteger),\n/* harmony export */   unescapePathComponent: () => (/* binding */ unescapePathComponent)\n/* harmony export */ });\n/*!\n * https://github.com/Starcounter-Jack/JSON-Patch\n * (c) 2017-2022 Joachim Wester\n * MIT licensed\n */\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar _hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction hasOwnProperty(obj, key) {\n    return _hasOwnProperty.call(obj, key);\n}\nfunction _objectKeys(obj) {\n    if (Array.isArray(obj)) {\n        var keys_1 = new Array(obj.length);\n        for (var k = 0; k < keys_1.length; k++) {\n            keys_1[k] = \"\" + k;\n        }\n        return keys_1;\n    }\n    if (Object.keys) {\n        return Object.keys(obj);\n    }\n    var keys = [];\n    for (var i in obj) {\n        if (hasOwnProperty(obj, i)) {\n            keys.push(i);\n        }\n    }\n    return keys;\n}\n;\n/**\n* Deeply clone the object.\n* https://jsperf.com/deep-copy-vs-json-stringify-json-parse/25 (recursiveDeepCopy)\n* @param  {any} obj value to clone\n* @return {any} cloned obj\n*/\nfunction _deepClone(obj) {\n    switch (typeof obj) {\n        case \"object\":\n            return JSON.parse(JSON.stringify(obj)); //Faster than ES5 clone - http://jsperf.com/deep-cloning-of-objects/5\n        case \"undefined\":\n            return null; //this is how JSON.stringify behaves for array items\n        default:\n            return obj; //no need to clone primitives\n    }\n}\n//3x faster than cached /^\\d+$/.test(str)\nfunction isInteger(str) {\n    var i = 0;\n    var len = str.length;\n    var charCode;\n    while (i < len) {\n        charCode = str.charCodeAt(i);\n        if (charCode >= 48 && charCode <= 57) {\n            i++;\n            continue;\n        }\n        return false;\n    }\n    return true;\n}\n/**\n* Escapes a json pointer path\n* @param path The raw pointer\n* @return the Escaped path\n*/\nfunction escapePathComponent(path) {\n    if (path.indexOf('/') === -1 && path.indexOf('~') === -1)\n        return path;\n    return path.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n/**\n * Unescapes a json pointer path\n * @param path The escaped pointer\n * @return The unescaped path\n */\nfunction unescapePathComponent(path) {\n    return path.replace(/~1/g, '/').replace(/~0/g, '~');\n}\nfunction _getPathRecursive(root, obj) {\n    var found;\n    for (var key in root) {\n        if (hasOwnProperty(root, key)) {\n            if (root[key] === obj) {\n                return escapePathComponent(key) + '/';\n            }\n            else if (typeof root[key] === 'object') {\n                found = _getPathRecursive(root[key], obj);\n                if (found != '') {\n                    return escapePathComponent(key) + '/' + found;\n                }\n            }\n        }\n    }\n    return '';\n}\nfunction getPath(root, obj) {\n    if (root === obj) {\n        return '/';\n    }\n    var path = _getPathRecursive(root, obj);\n    if (path === '') {\n        throw new Error(\"Object not found in root\");\n    }\n    return \"/\" + path;\n}\n/**\n* Recursively checks whether an object has any undefined values inside.\n*/\nfunction hasUndefined(obj) {\n    if (obj === undefined) {\n        return true;\n    }\n    if (obj) {\n        if (Array.isArray(obj)) {\n            for (var i_1 = 0, len = obj.length; i_1 < len; i_1++) {\n                if (hasUndefined(obj[i_1])) {\n                    return true;\n                }\n            }\n        }\n        else if (typeof obj === \"object\") {\n            var objKeys = _objectKeys(obj);\n            var objKeysLength = objKeys.length;\n            for (var i = 0; i < objKeysLength; i++) {\n                if (hasUndefined(obj[objKeys[i]])) {\n                    return true;\n                }\n            }\n        }\n    }\n    return false;\n}\nfunction patchErrorMessageFormatter(message, args) {\n    var messageParts = [message];\n    for (var key in args) {\n        var value = typeof args[key] === 'object' ? JSON.stringify(args[key], null, 2) : args[key]; // pretty print\n        if (typeof value !== 'undefined') {\n            messageParts.push(key + \": \" + value);\n        }\n    }\n    return messageParts.join('\\n');\n}\nvar PatchError = /** @class */ (function (_super) {\n    __extends(PatchError, _super);\n    function PatchError(message, name, index, operation, tree) {\n        var _newTarget = this.constructor;\n        var _this = _super.call(this, patchErrorMessageFormatter(message, { name: name, index: index, operation: operation, tree: tree })) || this;\n        _this.name = name;\n        _this.index = index;\n        _this.operation = operation;\n        _this.tree = tree;\n        Object.setPrototypeOf(_this, _newTarget.prototype); // restore prototype chain, see https://stackoverflow.com/a/48342359\n        _this.message = patchErrorMessageFormatter(message, { name: name, index: index, operation: operation, tree: tree });\n        return _this;\n    }\n    return PatchError;\n}(Error));\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/fast-json-patch@3.1.1/node_modules/fast-json-patch/module/helpers.mjs\n");

/***/ })

};
;