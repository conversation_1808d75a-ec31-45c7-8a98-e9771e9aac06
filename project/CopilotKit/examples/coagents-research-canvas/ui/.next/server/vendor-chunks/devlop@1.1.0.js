"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/devlop@1.1.0";
exports.ids = ["vendor-chunks/devlop@1.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/devlop@1.1.0/node_modules/devlop/lib/development.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/devlop@1.1.0/node_modules/devlop/lib/development.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deprecate: () => (/* binding */ deprecate),\n/* harmony export */   equal: () => (/* binding */ equal),\n/* harmony export */   ok: () => (/* binding */ ok),\n/* harmony export */   unreachable: () => (/* binding */ unreachable)\n/* harmony export */ });\n/* harmony import */ var dequal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dequal */ \"(ssr)/./node_modules/.pnpm/dequal@2.0.3/node_modules/dequal/dist/index.mjs\");\n\n\n/**\n * @type {Set<string>}\n */\nconst codesWarned = new Set()\n\nclass AssertionError extends Error {\n  name = /** @type {const} */ ('Assertion')\n  code = /** @type {const} */ ('ERR_ASSERTION')\n\n  /**\n   * Create an assertion error.\n   *\n   * @param {string} message\n   *   Message explaining error.\n   * @param {unknown} actual\n   *   Value.\n   * @param {unknown} expected\n   *   Baseline.\n   * @param {string} operator\n   *   Name of equality operation.\n   * @param {boolean} generated\n   *   Whether `message` is a custom message or not\n   * @returns\n   *   Instance.\n   */\n  // eslint-disable-next-line max-params\n  constructor(message, actual, expected, operator, generated) {\n    super(message)\n\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor)\n    }\n\n    /**\n     * @type {unknown}\n     */\n    this.actual = actual\n\n    /**\n     * @type {unknown}\n     */\n    this.expected = expected\n\n    /**\n     * @type {boolean}\n     */\n    this.generated = generated\n\n    /**\n     * @type {string}\n     */\n    this.operator = operator\n  }\n}\n\nclass DeprecationError extends Error {\n  name = /** @type {const} */ ('DeprecationWarning')\n\n  /**\n   * Create a deprecation message.\n   *\n   * @param {string} message\n   *   Message explaining deprecation.\n   * @param {string | undefined} code\n   *   Deprecation identifier; deprecation messages will be generated only once per code.\n   * @returns\n   *   Instance.\n   */\n  constructor(message, code) {\n    super(message)\n\n    /**\n     * @type {string | undefined}\n     */\n    this.code = code\n  }\n}\n\n/**\n * Wrap a function or class to show a deprecation message when first called.\n *\n * > 👉 **Important**: only shows a message when the `development` condition is\n * > used, does nothing in production.\n *\n * When the resulting wrapped `fn` is called, emits a warning once to\n * `console.error` (`stderr`).\n * If a code is given, one warning message will be emitted in total per code.\n *\n * @template {Function} T\n *   Function or class kind.\n * @param {T} fn\n *   Function or class.\n * @param {string} message\n *   Message explaining deprecation.\n * @param {string | null | undefined} [code]\n *   Deprecation identifier (optional); deprecation messages will be generated\n *   only once per code.\n * @returns {T}\n *   Wrapped `fn`.\n */\nfunction deprecate(fn, message, code) {\n  let warned = false\n\n  // The wrapper will keep the same prototype as fn to maintain prototype chain\n  Object.setPrototypeOf(deprecated, fn)\n\n  // @ts-expect-error: it’s perfect, typescript…\n  return deprecated\n\n  /**\n   * @this {unknown}\n   * @param  {...Array<unknown>} args\n   * @returns {unknown}\n   */\n  function deprecated(...args) {\n    if (!warned) {\n      warned = true\n\n      if (typeof code === 'string' && codesWarned.has(code)) {\n        // Empty.\n      } else {\n        console.error(new DeprecationError(message, code || undefined))\n\n        if (typeof code === 'string') codesWarned.add(code)\n      }\n    }\n\n    return new.target\n      ? Reflect.construct(fn, args, new.target)\n      : Reflect.apply(fn, this, args)\n  }\n}\n\n/**\n * Assert deep strict equivalence.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @template {unknown} T\n *   Expected kind.\n * @param {unknown} actual\n *   Value.\n * @param {T} expected\n *   Baseline.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected values to be deeply equal'`).\n * @returns {asserts actual is T}\n *   Nothing; throws when `actual` is not deep strict equal to `expected`.\n * @throws {AssertionError}\n *   Throws when `actual` is not deep strict equal to `expected`.\n */\nfunction equal(actual, expected, message) {\n  assert(\n    (0,dequal__WEBPACK_IMPORTED_MODULE_0__.dequal)(actual, expected),\n    actual,\n    expected,\n    'equal',\n    'Expected values to be deeply equal',\n    message\n  )\n}\n\n/**\n * Assert if `value` is truthy.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {unknown} value\n *   Value to assert.\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Expected value to be truthy'`).\n * @returns {asserts value}\n *   Nothing; throws when `value` is falsey.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nfunction ok(value, message) {\n  assert(\n    Boolean(value),\n    false,\n    true,\n    'ok',\n    'Expected value to be truthy',\n    message\n  )\n}\n\n/**\n * Assert that a code path never happens.\n *\n * > 👉 **Important**: only asserts when the `development` condition is used,\n * > does nothing in production.\n *\n * @param {Error | string | null | undefined} [message]\n *   Message for assertion error (default: `'Unreachable'`).\n * @returns {never}\n *   Nothing; always throws.\n * @throws {AssertionError}\n *   Throws when `value` is falsey.\n */\nfunction unreachable(message) {\n  assert(false, false, true, 'ok', 'Unreachable', message)\n}\n\n/**\n * @param {boolean} bool\n *   Whether to skip this operation.\n * @param {unknown} actual\n *   Actual value.\n * @param {unknown} expected\n *   Expected value.\n * @param {string} operator\n *   Operator.\n * @param {string} defaultMessage\n *   Default message for operation.\n * @param {Error | string | null | undefined} userMessage\n *   User-provided message.\n * @returns {asserts bool}\n *   Nothing; throws when falsey.\n */\n// eslint-disable-next-line max-params\nfunction assert(bool, actual, expected, operator, defaultMessage, userMessage) {\n  if (!bool) {\n    throw userMessage instanceof Error\n      ? userMessage\n      : new AssertionError(\n          userMessage || defaultMessage,\n          actual,\n          expected,\n          operator,\n          !userMessage\n        )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/devlop@1.1.0/node_modules/devlop/lib/development.js\n");

/***/ })

};
;