"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+middleware-host-header@3.821.0";
exports.ids = ["vendor-chunks/@aws-sdk+middleware-host-header@3.821.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-host-header@3.821.0/node_modules/@aws-sdk/middleware-host-header/dist-es/index.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+middleware-host-header@3.821.0/node_modules/@aws-sdk/middleware-host-header/dist-es/index.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHostHeaderPlugin: () => (/* binding */ getHostHeaderPlugin),\n/* harmony export */   hostHeaderMiddleware: () => (/* binding */ hostHeaderMiddleware),\n/* harmony export */   hostHeaderMiddlewareOptions: () => (/* binding */ hostHeaderMiddlewareOptions),\n/* harmony export */   resolveHostHeaderConfig: () => (/* binding */ resolveHostHeaderConfig)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n\nfunction resolveHostHeaderConfig(input) {\n    return input;\n}\nconst hostHeaderMiddleware = (options) => (next) => async (args) => {\n    if (!_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpRequest.isInstance(args.request))\n        return next(args);\n    const { request } = args;\n    const { handlerProtocol = \"\" } = options.requestHandler.metadata || {};\n    if (handlerProtocol.indexOf(\"h2\") >= 0 && !request.headers[\":authority\"]) {\n        delete request.headers[\"host\"];\n        request.headers[\":authority\"] = request.hostname + (request.port ? \":\" + request.port : \"\");\n    }\n    else if (!request.headers[\"host\"]) {\n        let host = request.hostname;\n        if (request.port != null)\n            host += `:${request.port}`;\n        request.headers[\"host\"] = host;\n    }\n    return next(args);\n};\nconst hostHeaderMiddlewareOptions = {\n    name: \"hostHeaderMiddleware\",\n    step: \"build\",\n    priority: \"low\",\n    tags: [\"HOST\"],\n    override: true,\n};\nconst getHostHeaderPlugin = (options) => ({\n    applyToStack: (clientStack) => {\n        clientStack.add(hostHeaderMiddleware(options), hostHeaderMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-host-header@3.821.0/node_modules/@aws-sdk/middleware-host-header/dist-es/index.js\n");

/***/ })

};
;