"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@kamilkisiela+fast-url-parser@1.1.4";
exports.ids = ["vendor-chunks/@kamilkisiela+fast-url-parser@1.1.4"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@kamilkisiela+fast-url-parser@1.1.4/node_modules/@kamilkisiela/fast-url-parser/src/punycode.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@kamilkisiela+fast-url-parser@1.1.4/node_modules/@kamilkisiela/fast-url-parser/src/punycode.js ***!
  \***************************************************************************************************************************/
/***/ ((module) => {

eval("// taken directly from https://www.npmjs.com/package/punycode\n\n\n/** Highest positive signed 32-bit float value */\nvar maxInt = 2147483647; // aka. 0x7FFFFFFF or 2^31-1\n\n/** Bootstring parameters */\nvar base = 36;\nvar tMin = 1;\nvar tMax = 26;\nvar skew = 38;\nvar damp = 700;\nvar initialBias = 72;\nvar initialN = 128; // 0x80\nvar delimiter = \"-\"; // '\\x2D'\n\n/** Regular expressions */\nvar regexNonASCII = /[^\\0-\\x7F]/; // Note: U+007F DEL is excluded too.\nvar regexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g; // RFC 3490 separators\n\n/** Error messages */\nvar errors = {\n  \"overflow\": \"Overflow: input needs wider integers to process\",\n  \"not-basic\": \"Illegal input >= 0x80 (not a basic code point)\",\n  \"invalid-input\": \"Invalid input\",\n};\n\n/** Convenience shortcuts */\nvar baseMinusTMin = base - tMin;\nvar floor = Math.floor;\nvar stringFromCharCode = String.fromCharCode;\n\n/*--------------------------------------------------------------------------*/\n\n/**\n * A generic error utility function.\n * @private\n * @param {String} type The error type.\n * @returns {Error} Throws a `RangeError` with the applicable error message.\n */\nfunction error(type) {\n  throw new RangeError(errors[type]);\n}\n\n/**\n * A generic `Array#map` utility function.\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} callback The function that gets called for every array\n * item.\n * @returns {Array} A new array of values returned by the callback function.\n */\nfunction map(array, callback) {\n  var result = [];\n  var length = array.length;\n  while (length--) {\n    result[length] = callback(array[length]);\n  }\n  return result;\n}\n\n/**\n * A simple `Array#map`-like wrapper to work with domain name strings or email\n * addresses.\n * @private\n * @param {String} domain The domain name or email address.\n * @param {Function} callback The function that gets called for every\n * character.\n * @returns {String} A new string of characters returned by the callback\n * function.\n */\nfunction mapDomain(domain, callback) {\n  var parts = domain.split(\"@\");\n  var result = \"\";\n  if (parts.length > 1) {\n    // In email addresses, only the domain name should be punycoded. Leave\n    // the local part (i.e. everything up to `@`) intact.\n    result = parts[0] + \"@\";\n    domain = parts[1];\n  }\n  // Avoid `split(regex)` for IE8 compatibility. See #17.\n  domain = domain.replace(regexSeparators, \"\\x2E\");\n  var labels = domain.split(\".\");\n  var encoded = map(labels, callback).join(\".\");\n  return result + encoded;\n}\n\n/**\n * Creates an array containing the numeric code points of each Unicode\n * character in the string. While JavaScript uses UCS-2 internally,\n * this function will convert a pair of surrogate halves (each of which\n * UCS-2 exposes as separate characters) into a single code point,\n * matching UTF-16.\n * @see `punycode.ucs2.encode`\n * @see <https://mathiasbynens.be/notes/javascript-encoding>\n * @memberOf punycode.ucs2\n * @name decode\n * @param {String} string The Unicode input string (UCS-2).\n * @returns {Array} The new array of code points.\n */\nfunction ucs2decode(string) {\n  var output = [];\n  var counter = 0;\n  var length = string.length;\n  while (counter < length) {\n    var value = string.charCodeAt(counter++);\n    if (value >= 0xd800 && value <= 0xdbff && counter < length) {\n      // It's a high surrogate, and there is a next character.\n      var extra = string.charCodeAt(counter++);\n      if ((extra & 0xfc00) === 0xdc00) {\n        // Low surrogate.\n        output.push(((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000);\n      } else {\n        // It's an unmatched surrogate; only append this code unit, in case the\n        // next code unit is the high surrogate of a surrogate pair.\n        output.push(value);\n        counter--;\n      }\n    } else {\n      output.push(value);\n    }\n  }\n  return output;\n}\n\n/**\n * Converts a digit/integer into a basic code point.\n * @see `basicToDigit()`\n * @private\n * @param {Number} digit The numeric value of a basic code point.\n * @returns {Number} The basic code point whose value (when used for\n * representing integers) is `digit`, which needs to be in the range\n * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n * used; else, the lowercase form is used. The behavior is undefined\n * if `flag` is non-zero and `digit` has no uppercase form.\n */\nvar digitToBasic = function (digit, flag) {\n  //  0..25 map to ASCII a..z or A..Z\n  // 26..35 map to ASCII 0..9\n  return digit + 22 + 75 * (digit < 26) - ((flag !== 0) << 5);\n};\n\n/**\n * Bias adaptation function as per section 3.4 of RFC 3492.\n * https://tools.ietf.org/html/rfc3492#section-3.4\n * @private\n */\nvar adapt = function (delta, numPoints, firstTime) {\n  var k = 0;\n  delta = firstTime ? floor(delta / damp) : delta >> 1;\n  delta += floor(delta / numPoints);\n  for (; /* no initialization */ delta > (baseMinusTMin * tMax) >> 1; k += base) {\n    delta = floor(delta / baseMinusTMin);\n  }\n  return floor(k + ((baseMinusTMin + 1) * delta) / (delta + skew));\n};\n\n/**\n * Converts a string of Unicode symbols (e.g. a domain name label) to a\n * Punycode string of ASCII-only symbols.\n * @memberOf punycode\n * @param {String} input The string of Unicode symbols.\n * @returns {String} The resulting Punycode string of ASCII-only symbols.\n */\nvar encode = function (input) {\n  var output = [];\n\n  // Convert the input in UCS-2 to an array of Unicode code points.\n  input = ucs2decode(input);\n\n  // Cache the length.\n  var inputLength = input.length;\n\n  // Initialize the state.\n  var n = initialN;\n  var delta = 0;\n  var bias = initialBias;\n\n  // Handle the basic code points.\n  for (var currentValue of input) {\n    if (currentValue < 0x80) {\n      output.push(stringFromCharCode(currentValue));\n    }\n  }\n\n  var basicLength = output.length;\n  var handledCPCount = basicLength;\n\n  // `handledCPCount` is the number of code points that have been handled;\n  // `basicLength` is the number of basic code points.\n\n  // Finish the basic string with a delimiter unless it's empty.\n  if (basicLength) {\n    output.push(delimiter);\n  }\n\n  // Main encoding loop:\n  while (handledCPCount < inputLength) {\n    // All non-basic code points < n have been handled already. Find the next\n    // larger one:\n    var m = maxInt;\n    for (var currentValue of input) {\n      if (currentValue >= n && currentValue < m) {\n        m = currentValue;\n      }\n    }\n\n    // Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n    // but guard against overflow.\n    var handledCPCountPlusOne = handledCPCount + 1;\n    if (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n      error(\"overflow\");\n    }\n\n    delta += (m - n) * handledCPCountPlusOne;\n    n = m;\n\n    for (var currentValue of input) {\n      if (currentValue < n && ++delta > maxInt) {\n        error(\"overflow\");\n      }\n      if (currentValue === n) {\n        // Represent delta as a generalized variable-length integer.\n        var q = delta;\n        for (var k = base /* no condition */; ; k += base) {\n          var t = k <= bias ? tMin : k >= bias + tMax ? tMax : k - bias;\n          if (q < t) {\n            break;\n          }\n          var qMinusT = q - t;\n          var baseMinusT = base - t;\n          output.push(stringFromCharCode(digitToBasic(t + (qMinusT % baseMinusT), 0)));\n          q = floor(qMinusT / baseMinusT);\n        }\n\n        output.push(stringFromCharCode(digitToBasic(q, 0)));\n        bias = adapt(delta, handledCPCountPlusOne, handledCPCount === basicLength);\n        delta = 0;\n        ++handledCPCount;\n      }\n    }\n\n    ++delta;\n    ++n;\n  }\n  return output.join(\"\");\n};\n\n/**\n * Converts a Unicode string representing a domain name or an email address to\n * Punycode. Only the non-ASCII parts of the domain name will be converted,\n * i.e. it doesn't matter if you call it with a domain that's already in\n * ASCII.\n * @memberOf punycode\n * @param {String} input The domain name or email address to convert, as a\n * Unicode string.\n * @returns {String} The Punycode representation of the given domain name or\n * email address.\n */\nvar toASCII = function (input) {\n  return mapDomain(input, function (string) {\n    return regexNonASCII.test(string) ? \"xn--\" + encode(string) : string;\n  });\n};\n\n/*--------------------------------------------------------------------------*/\n\nmodule.exports = {\n    toASCII: toASCII\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGthbWlsa2lzaWVsYStmYXN0LXVybC1wYXJzZXJAMS4xLjQvbm9kZV9tb2R1bGVzL0BrYW1pbGtpc2llbGEvZmFzdC11cmwtcGFyc2VyL3NyYy9wdW55Y29kZS5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNhOztBQUViO0FBQ0EseUJBQXlCOztBQUV6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQixxQkFBcUI7O0FBRXJCO0FBQ0Esa0NBQWtDO0FBQ2xDLG1EQUFtRDs7QUFFbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixhQUFhLE9BQU87QUFDcEI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCLFdBQVcsVUFBVTtBQUNyQjtBQUNBLGFBQWEsT0FBTztBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxVQUFVO0FBQ3JCO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYSxPQUFPO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSLHdDQUF3QztBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsNkRBQTZEO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0Q7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQjtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BrYW1pbGtpc2llbGErZmFzdC11cmwtcGFyc2VyQDEuMS40L25vZGVfbW9kdWxlcy9Aa2FtaWxraXNpZWxhL2Zhc3QtdXJsLXBhcnNlci9zcmMvcHVueWNvZGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gdGFrZW4gZGlyZWN0bHkgZnJvbSBodHRwczovL3d3dy5ucG1qcy5jb20vcGFja2FnZS9wdW55Y29kZVxuXCJ1c2Ugc3RyaWN0XCI7XG5cbi8qKiBIaWdoZXN0IHBvc2l0aXZlIHNpZ25lZCAzMi1iaXQgZmxvYXQgdmFsdWUgKi9cbnZhciBtYXhJbnQgPSAyMTQ3NDgzNjQ3OyAvLyBha2EuIDB4N0ZGRkZGRkYgb3IgMl4zMS0xXG5cbi8qKiBCb290c3RyaW5nIHBhcmFtZXRlcnMgKi9cbnZhciBiYXNlID0gMzY7XG52YXIgdE1pbiA9IDE7XG52YXIgdE1heCA9IDI2O1xudmFyIHNrZXcgPSAzODtcbnZhciBkYW1wID0gNzAwO1xudmFyIGluaXRpYWxCaWFzID0gNzI7XG52YXIgaW5pdGlhbE4gPSAxMjg7IC8vIDB4ODBcbnZhciBkZWxpbWl0ZXIgPSBcIi1cIjsgLy8gJ1xceDJEJ1xuXG4vKiogUmVndWxhciBleHByZXNzaW9ucyAqL1xudmFyIHJlZ2V4Tm9uQVNDSUkgPSAvW15cXDAtXFx4N0ZdLzsgLy8gTm90ZTogVSswMDdGIERFTCBpcyBleGNsdWRlZCB0b28uXG52YXIgcmVnZXhTZXBhcmF0b3JzID0gL1tcXHgyRVxcdTMwMDJcXHVGRjBFXFx1RkY2MV0vZzsgLy8gUkZDIDM0OTAgc2VwYXJhdG9yc1xuXG4vKiogRXJyb3IgbWVzc2FnZXMgKi9cbnZhciBlcnJvcnMgPSB7XG4gIFwib3ZlcmZsb3dcIjogXCJPdmVyZmxvdzogaW5wdXQgbmVlZHMgd2lkZXIgaW50ZWdlcnMgdG8gcHJvY2Vzc1wiLFxuICBcIm5vdC1iYXNpY1wiOiBcIklsbGVnYWwgaW5wdXQgPj0gMHg4MCAobm90IGEgYmFzaWMgY29kZSBwb2ludClcIixcbiAgXCJpbnZhbGlkLWlucHV0XCI6IFwiSW52YWxpZCBpbnB1dFwiLFxufTtcblxuLyoqIENvbnZlbmllbmNlIHNob3J0Y3V0cyAqL1xudmFyIGJhc2VNaW51c1RNaW4gPSBiYXNlIC0gdE1pbjtcbnZhciBmbG9vciA9IE1hdGguZmxvb3I7XG52YXIgc3RyaW5nRnJvbUNoYXJDb2RlID0gU3RyaW5nLmZyb21DaGFyQ29kZTtcblxuLyotLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbi8qKlxuICogQSBnZW5lcmljIGVycm9yIHV0aWxpdHkgZnVuY3Rpb24uXG4gKiBAcHJpdmF0ZVxuICogQHBhcmFtIHtTdHJpbmd9IHR5cGUgVGhlIGVycm9yIHR5cGUuXG4gKiBAcmV0dXJucyB7RXJyb3J9IFRocm93cyBhIGBSYW5nZUVycm9yYCB3aXRoIHRoZSBhcHBsaWNhYmxlIGVycm9yIG1lc3NhZ2UuXG4gKi9cbmZ1bmN0aW9uIGVycm9yKHR5cGUpIHtcbiAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoZXJyb3JzW3R5cGVdKTtcbn1cblxuLyoqXG4gKiBBIGdlbmVyaWMgYEFycmF5I21hcGAgdXRpbGl0eSBmdW5jdGlvbi5cbiAqIEBwcml2YXRlXG4gKiBAcGFyYW0ge0FycmF5fSBhcnJheSBUaGUgYXJyYXkgdG8gaXRlcmF0ZSBvdmVyLlxuICogQHBhcmFtIHtGdW5jdGlvbn0gY2FsbGJhY2sgVGhlIGZ1bmN0aW9uIHRoYXQgZ2V0cyBjYWxsZWQgZm9yIGV2ZXJ5IGFycmF5XG4gKiBpdGVtLlxuICogQHJldHVybnMge0FycmF5fSBBIG5ldyBhcnJheSBvZiB2YWx1ZXMgcmV0dXJuZWQgYnkgdGhlIGNhbGxiYWNrIGZ1bmN0aW9uLlxuICovXG5mdW5jdGlvbiBtYXAoYXJyYXksIGNhbGxiYWNrKSB7XG4gIHZhciByZXN1bHQgPSBbXTtcbiAgdmFyIGxlbmd0aCA9IGFycmF5Lmxlbmd0aDtcbiAgd2hpbGUgKGxlbmd0aC0tKSB7XG4gICAgcmVzdWx0W2xlbmd0aF0gPSBjYWxsYmFjayhhcnJheVtsZW5ndGhdKTtcbiAgfVxuICByZXR1cm4gcmVzdWx0O1xufVxuXG4vKipcbiAqIEEgc2ltcGxlIGBBcnJheSNtYXBgLWxpa2Ugd3JhcHBlciB0byB3b3JrIHdpdGggZG9tYWluIG5hbWUgc3RyaW5ncyBvciBlbWFpbFxuICogYWRkcmVzc2VzLlxuICogQHByaXZhdGVcbiAqIEBwYXJhbSB7U3RyaW5nfSBkb21haW4gVGhlIGRvbWFpbiBuYW1lIG9yIGVtYWlsIGFkZHJlc3MuXG4gKiBAcGFyYW0ge0Z1bmN0aW9ufSBjYWxsYmFjayBUaGUgZnVuY3Rpb24gdGhhdCBnZXRzIGNhbGxlZCBmb3IgZXZlcnlcbiAqIGNoYXJhY3Rlci5cbiAqIEByZXR1cm5zIHtTdHJpbmd9IEEgbmV3IHN0cmluZyBvZiBjaGFyYWN0ZXJzIHJldHVybmVkIGJ5IHRoZSBjYWxsYmFja1xuICogZnVuY3Rpb24uXG4gKi9cbmZ1bmN0aW9uIG1hcERvbWFpbihkb21haW4sIGNhbGxiYWNrKSB7XG4gIHZhciBwYXJ0cyA9IGRvbWFpbi5zcGxpdChcIkBcIik7XG4gIHZhciByZXN1bHQgPSBcIlwiO1xuICBpZiAocGFydHMubGVuZ3RoID4gMSkge1xuICAgIC8vIEluIGVtYWlsIGFkZHJlc3Nlcywgb25seSB0aGUgZG9tYWluIG5hbWUgc2hvdWxkIGJlIHB1bnljb2RlZC4gTGVhdmVcbiAgICAvLyB0aGUgbG9jYWwgcGFydCAoaS5lLiBldmVyeXRoaW5nIHVwIHRvIGBAYCkgaW50YWN0LlxuICAgIHJlc3VsdCA9IHBhcnRzWzBdICsgXCJAXCI7XG4gICAgZG9tYWluID0gcGFydHNbMV07XG4gIH1cbiAgLy8gQXZvaWQgYHNwbGl0KHJlZ2V4KWAgZm9yIElFOCBjb21wYXRpYmlsaXR5LiBTZWUgIzE3LlxuICBkb21haW4gPSBkb21haW4ucmVwbGFjZShyZWdleFNlcGFyYXRvcnMsIFwiXFx4MkVcIik7XG4gIHZhciBsYWJlbHMgPSBkb21haW4uc3BsaXQoXCIuXCIpO1xuICB2YXIgZW5jb2RlZCA9IG1hcChsYWJlbHMsIGNhbGxiYWNrKS5qb2luKFwiLlwiKTtcbiAgcmV0dXJuIHJlc3VsdCArIGVuY29kZWQ7XG59XG5cbi8qKlxuICogQ3JlYXRlcyBhbiBhcnJheSBjb250YWluaW5nIHRoZSBudW1lcmljIGNvZGUgcG9pbnRzIG9mIGVhY2ggVW5pY29kZVxuICogY2hhcmFjdGVyIGluIHRoZSBzdHJpbmcuIFdoaWxlIEphdmFTY3JpcHQgdXNlcyBVQ1MtMiBpbnRlcm5hbGx5LFxuICogdGhpcyBmdW5jdGlvbiB3aWxsIGNvbnZlcnQgYSBwYWlyIG9mIHN1cnJvZ2F0ZSBoYWx2ZXMgKGVhY2ggb2Ygd2hpY2hcbiAqIFVDUy0yIGV4cG9zZXMgYXMgc2VwYXJhdGUgY2hhcmFjdGVycykgaW50byBhIHNpbmdsZSBjb2RlIHBvaW50LFxuICogbWF0Y2hpbmcgVVRGLTE2LlxuICogQHNlZSBgcHVueWNvZGUudWNzMi5lbmNvZGVgXG4gKiBAc2VlIDxodHRwczovL21hdGhpYXNieW5lbnMuYmUvbm90ZXMvamF2YXNjcmlwdC1lbmNvZGluZz5cbiAqIEBtZW1iZXJPZiBwdW55Y29kZS51Y3MyXG4gKiBAbmFtZSBkZWNvZGVcbiAqIEBwYXJhbSB7U3RyaW5nfSBzdHJpbmcgVGhlIFVuaWNvZGUgaW5wdXQgc3RyaW5nIChVQ1MtMikuXG4gKiBAcmV0dXJucyB7QXJyYXl9IFRoZSBuZXcgYXJyYXkgb2YgY29kZSBwb2ludHMuXG4gKi9cbmZ1bmN0aW9uIHVjczJkZWNvZGUoc3RyaW5nKSB7XG4gIHZhciBvdXRwdXQgPSBbXTtcbiAgdmFyIGNvdW50ZXIgPSAwO1xuICB2YXIgbGVuZ3RoID0gc3RyaW5nLmxlbmd0aDtcbiAgd2hpbGUgKGNvdW50ZXIgPCBsZW5ndGgpIHtcbiAgICB2YXIgdmFsdWUgPSBzdHJpbmcuY2hhckNvZGVBdChjb3VudGVyKyspO1xuICAgIGlmICh2YWx1ZSA+PSAweGQ4MDAgJiYgdmFsdWUgPD0gMHhkYmZmICYmIGNvdW50ZXIgPCBsZW5ndGgpIHtcbiAgICAgIC8vIEl0J3MgYSBoaWdoIHN1cnJvZ2F0ZSwgYW5kIHRoZXJlIGlzIGEgbmV4dCBjaGFyYWN0ZXIuXG4gICAgICB2YXIgZXh0cmEgPSBzdHJpbmcuY2hhckNvZGVBdChjb3VudGVyKyspO1xuICAgICAgaWYgKChleHRyYSAmIDB4ZmMwMCkgPT09IDB4ZGMwMCkge1xuICAgICAgICAvLyBMb3cgc3Vycm9nYXRlLlxuICAgICAgICBvdXRwdXQucHVzaCgoKHZhbHVlICYgMHgzZmYpIDw8IDEwKSArIChleHRyYSAmIDB4M2ZmKSArIDB4MTAwMDApO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gSXQncyBhbiB1bm1hdGNoZWQgc3Vycm9nYXRlOyBvbmx5IGFwcGVuZCB0aGlzIGNvZGUgdW5pdCwgaW4gY2FzZSB0aGVcbiAgICAgICAgLy8gbmV4dCBjb2RlIHVuaXQgaXMgdGhlIGhpZ2ggc3Vycm9nYXRlIG9mIGEgc3Vycm9nYXRlIHBhaXIuXG4gICAgICAgIG91dHB1dC5wdXNoKHZhbHVlKTtcbiAgICAgICAgY291bnRlci0tO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBvdXRwdXQucHVzaCh2YWx1ZSk7XG4gICAgfVxuICB9XG4gIHJldHVybiBvdXRwdXQ7XG59XG5cbi8qKlxuICogQ29udmVydHMgYSBkaWdpdC9pbnRlZ2VyIGludG8gYSBiYXNpYyBjb2RlIHBvaW50LlxuICogQHNlZSBgYmFzaWNUb0RpZ2l0KClgXG4gKiBAcHJpdmF0ZVxuICogQHBhcmFtIHtOdW1iZXJ9IGRpZ2l0IFRoZSBudW1lcmljIHZhbHVlIG9mIGEgYmFzaWMgY29kZSBwb2ludC5cbiAqIEByZXR1cm5zIHtOdW1iZXJ9IFRoZSBiYXNpYyBjb2RlIHBvaW50IHdob3NlIHZhbHVlICh3aGVuIHVzZWQgZm9yXG4gKiByZXByZXNlbnRpbmcgaW50ZWdlcnMpIGlzIGBkaWdpdGAsIHdoaWNoIG5lZWRzIHRvIGJlIGluIHRoZSByYW5nZVxuICogYDBgIHRvIGBiYXNlIC0gMWAuIElmIGBmbGFnYCBpcyBub24temVybywgdGhlIHVwcGVyY2FzZSBmb3JtIGlzXG4gKiB1c2VkOyBlbHNlLCB0aGUgbG93ZXJjYXNlIGZvcm0gaXMgdXNlZC4gVGhlIGJlaGF2aW9yIGlzIHVuZGVmaW5lZFxuICogaWYgYGZsYWdgIGlzIG5vbi16ZXJvIGFuZCBgZGlnaXRgIGhhcyBubyB1cHBlcmNhc2UgZm9ybS5cbiAqL1xudmFyIGRpZ2l0VG9CYXNpYyA9IGZ1bmN0aW9uIChkaWdpdCwgZmxhZykge1xuICAvLyAgMC4uMjUgbWFwIHRvIEFTQ0lJIGEuLnogb3IgQS4uWlxuICAvLyAyNi4uMzUgbWFwIHRvIEFTQ0lJIDAuLjlcbiAgcmV0dXJuIGRpZ2l0ICsgMjIgKyA3NSAqIChkaWdpdCA8IDI2KSAtICgoZmxhZyAhPT0gMCkgPDwgNSk7XG59O1xuXG4vKipcbiAqIEJpYXMgYWRhcHRhdGlvbiBmdW5jdGlvbiBhcyBwZXIgc2VjdGlvbiAzLjQgb2YgUkZDIDM0OTIuXG4gKiBodHRwczovL3Rvb2xzLmlldGYub3JnL2h0bWwvcmZjMzQ5MiNzZWN0aW9uLTMuNFxuICogQHByaXZhdGVcbiAqL1xudmFyIGFkYXB0ID0gZnVuY3Rpb24gKGRlbHRhLCBudW1Qb2ludHMsIGZpcnN0VGltZSkge1xuICB2YXIgayA9IDA7XG4gIGRlbHRhID0gZmlyc3RUaW1lID8gZmxvb3IoZGVsdGEgLyBkYW1wKSA6IGRlbHRhID4+IDE7XG4gIGRlbHRhICs9IGZsb29yKGRlbHRhIC8gbnVtUG9pbnRzKTtcbiAgZm9yICg7IC8qIG5vIGluaXRpYWxpemF0aW9uICovIGRlbHRhID4gKGJhc2VNaW51c1RNaW4gKiB0TWF4KSA+PiAxOyBrICs9IGJhc2UpIHtcbiAgICBkZWx0YSA9IGZsb29yKGRlbHRhIC8gYmFzZU1pbnVzVE1pbik7XG4gIH1cbiAgcmV0dXJuIGZsb29yKGsgKyAoKGJhc2VNaW51c1RNaW4gKyAxKSAqIGRlbHRhKSAvIChkZWx0YSArIHNrZXcpKTtcbn07XG5cbi8qKlxuICogQ29udmVydHMgYSBzdHJpbmcgb2YgVW5pY29kZSBzeW1ib2xzIChlLmcuIGEgZG9tYWluIG5hbWUgbGFiZWwpIHRvIGFcbiAqIFB1bnljb2RlIHN0cmluZyBvZiBBU0NJSS1vbmx5IHN5bWJvbHMuXG4gKiBAbWVtYmVyT2YgcHVueWNvZGVcbiAqIEBwYXJhbSB7U3RyaW5nfSBpbnB1dCBUaGUgc3RyaW5nIG9mIFVuaWNvZGUgc3ltYm9scy5cbiAqIEByZXR1cm5zIHtTdHJpbmd9IFRoZSByZXN1bHRpbmcgUHVueWNvZGUgc3RyaW5nIG9mIEFTQ0lJLW9ubHkgc3ltYm9scy5cbiAqL1xudmFyIGVuY29kZSA9IGZ1bmN0aW9uIChpbnB1dCkge1xuICB2YXIgb3V0cHV0ID0gW107XG5cbiAgLy8gQ29udmVydCB0aGUgaW5wdXQgaW4gVUNTLTIgdG8gYW4gYXJyYXkgb2YgVW5pY29kZSBjb2RlIHBvaW50cy5cbiAgaW5wdXQgPSB1Y3MyZGVjb2RlKGlucHV0KTtcblxuICAvLyBDYWNoZSB0aGUgbGVuZ3RoLlxuICB2YXIgaW5wdXRMZW5ndGggPSBpbnB1dC5sZW5ndGg7XG5cbiAgLy8gSW5pdGlhbGl6ZSB0aGUgc3RhdGUuXG4gIHZhciBuID0gaW5pdGlhbE47XG4gIHZhciBkZWx0YSA9IDA7XG4gIHZhciBiaWFzID0gaW5pdGlhbEJpYXM7XG5cbiAgLy8gSGFuZGxlIHRoZSBiYXNpYyBjb2RlIHBvaW50cy5cbiAgZm9yICh2YXIgY3VycmVudFZhbHVlIG9mIGlucHV0KSB7XG4gICAgaWYgKGN1cnJlbnRWYWx1ZSA8IDB4ODApIHtcbiAgICAgIG91dHB1dC5wdXNoKHN0cmluZ0Zyb21DaGFyQ29kZShjdXJyZW50VmFsdWUpKTtcbiAgICB9XG4gIH1cblxuICB2YXIgYmFzaWNMZW5ndGggPSBvdXRwdXQubGVuZ3RoO1xuICB2YXIgaGFuZGxlZENQQ291bnQgPSBiYXNpY0xlbmd0aDtcblxuICAvLyBgaGFuZGxlZENQQ291bnRgIGlzIHRoZSBudW1iZXIgb2YgY29kZSBwb2ludHMgdGhhdCBoYXZlIGJlZW4gaGFuZGxlZDtcbiAgLy8gYGJhc2ljTGVuZ3RoYCBpcyB0aGUgbnVtYmVyIG9mIGJhc2ljIGNvZGUgcG9pbnRzLlxuXG4gIC8vIEZpbmlzaCB0aGUgYmFzaWMgc3RyaW5nIHdpdGggYSBkZWxpbWl0ZXIgdW5sZXNzIGl0J3MgZW1wdHkuXG4gIGlmIChiYXNpY0xlbmd0aCkge1xuICAgIG91dHB1dC5wdXNoKGRlbGltaXRlcik7XG4gIH1cblxuICAvLyBNYWluIGVuY29kaW5nIGxvb3A6XG4gIHdoaWxlIChoYW5kbGVkQ1BDb3VudCA8IGlucHV0TGVuZ3RoKSB7XG4gICAgLy8gQWxsIG5vbi1iYXNpYyBjb2RlIHBvaW50cyA8IG4gaGF2ZSBiZWVuIGhhbmRsZWQgYWxyZWFkeS4gRmluZCB0aGUgbmV4dFxuICAgIC8vIGxhcmdlciBvbmU6XG4gICAgdmFyIG0gPSBtYXhJbnQ7XG4gICAgZm9yICh2YXIgY3VycmVudFZhbHVlIG9mIGlucHV0KSB7XG4gICAgICBpZiAoY3VycmVudFZhbHVlID49IG4gJiYgY3VycmVudFZhbHVlIDwgbSkge1xuICAgICAgICBtID0gY3VycmVudFZhbHVlO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEluY3JlYXNlIGBkZWx0YWAgZW5vdWdoIHRvIGFkdmFuY2UgdGhlIGRlY29kZXIncyA8bixpPiBzdGF0ZSB0byA8bSwwPixcbiAgICAvLyBidXQgZ3VhcmQgYWdhaW5zdCBvdmVyZmxvdy5cbiAgICB2YXIgaGFuZGxlZENQQ291bnRQbHVzT25lID0gaGFuZGxlZENQQ291bnQgKyAxO1xuICAgIGlmIChtIC0gbiA+IGZsb29yKChtYXhJbnQgLSBkZWx0YSkgLyBoYW5kbGVkQ1BDb3VudFBsdXNPbmUpKSB7XG4gICAgICBlcnJvcihcIm92ZXJmbG93XCIpO1xuICAgIH1cblxuICAgIGRlbHRhICs9IChtIC0gbikgKiBoYW5kbGVkQ1BDb3VudFBsdXNPbmU7XG4gICAgbiA9IG07XG5cbiAgICBmb3IgKHZhciBjdXJyZW50VmFsdWUgb2YgaW5wdXQpIHtcbiAgICAgIGlmIChjdXJyZW50VmFsdWUgPCBuICYmICsrZGVsdGEgPiBtYXhJbnQpIHtcbiAgICAgICAgZXJyb3IoXCJvdmVyZmxvd1wiKTtcbiAgICAgIH1cbiAgICAgIGlmIChjdXJyZW50VmFsdWUgPT09IG4pIHtcbiAgICAgICAgLy8gUmVwcmVzZW50IGRlbHRhIGFzIGEgZ2VuZXJhbGl6ZWQgdmFyaWFibGUtbGVuZ3RoIGludGVnZXIuXG4gICAgICAgIHZhciBxID0gZGVsdGE7XG4gICAgICAgIGZvciAodmFyIGsgPSBiYXNlIC8qIG5vIGNvbmRpdGlvbiAqLzsgOyBrICs9IGJhc2UpIHtcbiAgICAgICAgICB2YXIgdCA9IGsgPD0gYmlhcyA/IHRNaW4gOiBrID49IGJpYXMgKyB0TWF4ID8gdE1heCA6IGsgLSBiaWFzO1xuICAgICAgICAgIGlmIChxIDwgdCkge1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgfVxuICAgICAgICAgIHZhciBxTWludXNUID0gcSAtIHQ7XG4gICAgICAgICAgdmFyIGJhc2VNaW51c1QgPSBiYXNlIC0gdDtcbiAgICAgICAgICBvdXRwdXQucHVzaChzdHJpbmdGcm9tQ2hhckNvZGUoZGlnaXRUb0Jhc2ljKHQgKyAocU1pbnVzVCAlIGJhc2VNaW51c1QpLCAwKSkpO1xuICAgICAgICAgIHEgPSBmbG9vcihxTWludXNUIC8gYmFzZU1pbnVzVCk7XG4gICAgICAgIH1cblxuICAgICAgICBvdXRwdXQucHVzaChzdHJpbmdGcm9tQ2hhckNvZGUoZGlnaXRUb0Jhc2ljKHEsIDApKSk7XG4gICAgICAgIGJpYXMgPSBhZGFwdChkZWx0YSwgaGFuZGxlZENQQ291bnRQbHVzT25lLCBoYW5kbGVkQ1BDb3VudCA9PT0gYmFzaWNMZW5ndGgpO1xuICAgICAgICBkZWx0YSA9IDA7XG4gICAgICAgICsraGFuZGxlZENQQ291bnQ7XG4gICAgICB9XG4gICAgfVxuXG4gICAgKytkZWx0YTtcbiAgICArK247XG4gIH1cbiAgcmV0dXJuIG91dHB1dC5qb2luKFwiXCIpO1xufTtcblxuLyoqXG4gKiBDb252ZXJ0cyBhIFVuaWNvZGUgc3RyaW5nIHJlcHJlc2VudGluZyBhIGRvbWFpbiBuYW1lIG9yIGFuIGVtYWlsIGFkZHJlc3MgdG9cbiAqIFB1bnljb2RlLiBPbmx5IHRoZSBub24tQVNDSUkgcGFydHMgb2YgdGhlIGRvbWFpbiBuYW1lIHdpbGwgYmUgY29udmVydGVkLFxuICogaS5lLiBpdCBkb2Vzbid0IG1hdHRlciBpZiB5b3UgY2FsbCBpdCB3aXRoIGEgZG9tYWluIHRoYXQncyBhbHJlYWR5IGluXG4gKiBBU0NJSS5cbiAqIEBtZW1iZXJPZiBwdW55Y29kZVxuICogQHBhcmFtIHtTdHJpbmd9IGlucHV0IFRoZSBkb21haW4gbmFtZSBvciBlbWFpbCBhZGRyZXNzIHRvIGNvbnZlcnQsIGFzIGFcbiAqIFVuaWNvZGUgc3RyaW5nLlxuICogQHJldHVybnMge1N0cmluZ30gVGhlIFB1bnljb2RlIHJlcHJlc2VudGF0aW9uIG9mIHRoZSBnaXZlbiBkb21haW4gbmFtZSBvclxuICogZW1haWwgYWRkcmVzcy5cbiAqL1xudmFyIHRvQVNDSUkgPSBmdW5jdGlvbiAoaW5wdXQpIHtcbiAgcmV0dXJuIG1hcERvbWFpbihpbnB1dCwgZnVuY3Rpb24gKHN0cmluZykge1xuICAgIHJldHVybiByZWdleE5vbkFTQ0lJLnRlc3Qoc3RyaW5nKSA/IFwieG4tLVwiICsgZW5jb2RlKHN0cmluZykgOiBzdHJpbmc7XG4gIH0pO1xufTtcblxuLyotLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICAgIHRvQVNDSUk6IHRvQVNDSUlcbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@kamilkisiela+fast-url-parser@1.1.4/node_modules/@kamilkisiela/fast-url-parser/src/punycode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@kamilkisiela+fast-url-parser@1.1.4/node_modules/@kamilkisiela/fast-url-parser/src/urlparser.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@kamilkisiela+fast-url-parser@1.1.4/node_modules/@kamilkisiela/fast-url-parser/src/urlparser.js ***!
  \****************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/*\nCopyright (c) 2014 Petka Antonov\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\nfunction Url() {\n    //For more efficient internal representation and laziness.\n    //The non-underscore versions of these properties are accessor functions\n    //defined on the prototype.\n    this._protocol = null;\n    this._href = \"\";\n    this._port = -1;\n    this._query = null;\n\n    this.auth = null;\n    this.slashes = null;\n    this.host = null;\n    this.hostname = null;\n    this.hash = null;\n    this.search = null;\n    this.pathname = null;\n\n    this._prependSlash = false;\n}\n\nvar querystring = __webpack_require__(/*! querystring */ \"querystring\");\n\nUrl.queryString = querystring;\n\nUrl.prototype.parse =\nfunction Url$parse(str, parseQueryString, hostDenotesSlash, disableAutoEscapeChars) {\n    if (typeof str !== \"string\") {\n        throw new TypeError(\"Parameter 'url' must be a string, not \" +\n            typeof str);\n    }\n    var start = 0;\n    var end = str.length - 1;\n\n    //Trim leading and trailing ws\n    while (str.charCodeAt(start) <= 0x20 /*' '*/) start++;\n    while (str.charCodeAt(end) <= 0x20 /*' '*/) end--;\n\n    start = this._parseProtocol(str, start, end);\n\n    //Javascript doesn't have host\n    if (this._protocol !== \"javascript\") {\n        start = this._parseHost(str, start, end, hostDenotesSlash);\n        var proto = this._protocol;\n        if (!this.hostname &&\n            (this.slashes || (proto && !slashProtocols[proto]))) {\n            this.hostname = this.host = \"\";\n        }\n    }\n\n    if (start <= end) {\n        var ch = str.charCodeAt(start);\n\n        if (ch === 0x2F /*'/'*/ || ch === 0x5C /*'\\'*/) {\n            this._parsePath(str, start, end, disableAutoEscapeChars);\n        }\n        else if (ch === 0x3F /*'?'*/) {\n            this._parseQuery(str, start, end, disableAutoEscapeChars);\n        }\n        else if (ch === 0x23 /*'#'*/) {\n          this._parseHash(str, start, end, disableAutoEscapeChars);\n        }\n        else if (this._protocol !== \"javascript\") {\n            this._parsePath(str, start, end, disableAutoEscapeChars);\n        }\n        else { //For javascript the pathname is just the rest of it\n            this.pathname = str.slice(start, end + 1 );\n        }\n\n    }\n\n    if (!this.pathname && this.hostname &&\n        this._slashProtocols[this._protocol]) {\n        this.pathname = \"/\";\n    }\n\n    if (parseQueryString) {\n        var search = this.search;\n        if (search == null) {\n            search = this.search = \"\";\n        }\n        if (search.charCodeAt(0) === 0x3F /*'?'*/) {\n            search = search.slice(1);\n        }\n        //This calls a setter function, there is no .query data property\n        this.query = Url.queryString.parse(search);\n    }\n};\n\nUrl.prototype.resolve = function Url$resolve(relative) {\n    return this.resolveObject(Url.parse(relative, false, true)).format();\n};\n\nUrl.prototype.format = function Url$format() {\n    var auth = this.auth || \"\";\n\n    if (auth) {\n        auth = encodeURIComponent(auth);\n        auth = auth.replace(/%3A/i, \":\");\n        auth += \"@\";\n    }\n\n    var protocol = this.protocol || \"\";\n    var pathname = this.pathname || \"\";\n    var hash = this.hash || \"\";\n    var search = this.search || \"\";\n    var query = \"\";\n    var hostname = this.hostname || \"\";\n    var port = this.port || \"\";\n    var host = false;\n    var scheme = \"\";\n\n    //Cache the result of the getter function\n    var q = this.query;\n    if (q && typeof q === \"object\") {\n        query = Url.queryString.stringify(q);\n    }\n\n    if (!search) {\n        search = query ? \"?\" + query : \"\";\n    }\n\n    if (protocol && protocol.charCodeAt(protocol.length - 1) !== 0x3A /*':'*/)\n        protocol += \":\";\n\n    if (this.host) {\n        host = auth + this.host;\n    }\n    else if (hostname) {\n        var ip6 = hostname.indexOf(\":\") > -1;\n        if (ip6) hostname = \"[\" + hostname + \"]\";\n        host = auth + hostname + (port ? \":\" + port : \"\");\n    }\n\n    var slashes = this.slashes ||\n        ((!protocol ||\n        slashProtocols[protocol]) && host !== false);\n\n\n    if (protocol) scheme = protocol + (slashes ? \"//\" : \"\");\n    else if (slashes) scheme = \"//\";\n\n    if (slashes && pathname && pathname.charCodeAt(0) !== 0x2F /*'/'*/) {\n        pathname = \"/\" + pathname;\n    }\n    if (search && search.charCodeAt(0) !== 0x3F /*'?'*/)\n        search = \"?\" + search;\n    if (hash && hash.charCodeAt(0) !== 0x23 /*'#'*/)\n        hash = \"#\" + hash;\n\n    pathname = escapePathName(pathname);\n    search = escapeSearch(search);\n\n    return scheme + (host === false ? \"\" : host) + pathname + search + hash;\n};\n\nUrl.prototype.resolveObject = function Url$resolveObject(relative) {\n    if (typeof relative === \"string\")\n        relative = Url.parse(relative, false, true);\n\n    var result = this._clone();\n\n    // hash is always overridden, no matter what.\n    // even href=\"\" will remove it.\n    result.hash = relative.hash;\n\n    // if the relative url is empty, then there\"s nothing left to do here.\n    if (!relative.href) {\n        result._href = \"\";\n        return result;\n    }\n\n    // hrefs like //foo/bar always cut to the protocol.\n    if (relative.slashes && !relative._protocol) {\n        relative._copyPropsTo(result, true);\n\n        if (slashProtocols[result._protocol] &&\n            result.hostname && !result.pathname) {\n            result.pathname = \"/\";\n        }\n        result._href = \"\";\n        return result;\n    }\n\n    if (relative._protocol && relative._protocol !== result._protocol) {\n        // if it\"s a known url protocol, then changing\n        // the protocol does weird things\n        // first, if it\"s not file:, then we MUST have a host,\n        // and if there was a path\n        // to begin with, then we MUST have a path.\n        // if it is file:, then the host is dropped,\n        // because that\"s known to be hostless.\n        // anything else is assumed to be absolute.\n        if (!slashProtocols[relative._protocol]) {\n            relative._copyPropsTo(result, false);\n            result._href = \"\";\n            return result;\n        }\n\n        result._protocol = relative._protocol;\n        if (!relative.host && relative._protocol !== \"javascript\") {\n            var relPath = (relative.pathname || \"\").split(\"/\");\n            while (relPath.length && !(relative.host = relPath.shift()));\n            if (!relative.host) relative.host = \"\";\n            if (!relative.hostname) relative.hostname = \"\";\n            if (relPath[0] !== \"\") relPath.unshift(\"\");\n            if (relPath.length < 2) relPath.unshift(\"\");\n            result.pathname = relPath.join(\"/\");\n        } else {\n            result.pathname = relative.pathname;\n        }\n\n        result.search = relative.search;\n        result.host = relative.host || \"\";\n        result.auth = relative.auth;\n        result.hostname = relative.hostname || relative.host;\n        result._port = relative._port;\n        result.slashes = result.slashes || relative.slashes;\n        result._href = \"\";\n        return result;\n    }\n\n    var isSourceAbs =\n        (result.pathname && result.pathname.charCodeAt(0) === 0x2F /*'/'*/);\n    var isRelAbs = (\n            relative.host ||\n            (relative.pathname &&\n            relative.pathname.charCodeAt(0) === 0x2F /*'/'*/)\n        );\n    var mustEndAbs = (isRelAbs || isSourceAbs ||\n                        (result.host && relative.pathname));\n\n    var removeAllDots = mustEndAbs;\n\n    var srcPath = result.pathname && result.pathname.split(\"/\") || [];\n    var relPath = relative.pathname && relative.pathname.split(\"/\") || [];\n    var psychotic = result._protocol && !slashProtocols[result._protocol];\n\n    // if the url is a non-slashed url, then relative\n    // links like ../.. should be able\n    // to crawl up to the hostname, as well.  This is strange.\n    // result.protocol has already been set by now.\n    // Later on, put the first path part into the host field.\n    if (psychotic) {\n        result.hostname = \"\";\n        result._port = -1;\n        if (result.host) {\n            if (srcPath[0] === \"\") srcPath[0] = result.host;\n            else srcPath.unshift(result.host);\n        }\n        result.host = \"\";\n        if (relative._protocol) {\n            relative.hostname = \"\";\n            relative._port = -1;\n            if (relative.host) {\n                if (relPath[0] === \"\") relPath[0] = relative.host;\n                else relPath.unshift(relative.host);\n            }\n            relative.host = \"\";\n        }\n        mustEndAbs = mustEndAbs && (relPath[0] === \"\" || srcPath[0] === \"\");\n    }\n\n    if (isRelAbs) {\n        // it\"s absolute.\n        result.host = relative.host ?\n            relative.host : result.host;\n        result.hostname = relative.hostname ?\n            relative.hostname : result.hostname;\n        result.search = relative.search;\n        srcPath = relPath;\n        // fall through to the dot-handling below.\n    } else if (relPath.length) {\n        // it\"s relative\n        // throw away the existing file, and take the new path instead.\n        if (!srcPath) srcPath = [];\n        srcPath.pop();\n        srcPath = srcPath.concat(relPath);\n        result.search = relative.search;\n    } else if (relative.search) {\n        // just pull out the search.\n        // like href=\"?foo\".\n        // Put this after the other two cases because it simplifies the booleans\n        if (psychotic) {\n            result.hostname = result.host = srcPath.shift();\n            //occationaly the auth can get stuck only in host\n            //this especialy happens in cases like\n            //url.resolveObject(\"mailto:local1@domain1\", \"local2@domain2\")\n            var authInHost = result.host && result.host.indexOf(\"@\") > 0 ?\n                result.host.split(\"@\") : false;\n            if (authInHost) {\n                result.auth = authInHost.shift();\n                result.host = result.hostname = authInHost.shift();\n            }\n        }\n        result.search = relative.search;\n        result._href = \"\";\n        return result;\n    }\n\n    if (!srcPath.length) {\n        // no path at all.  easy.\n        // we\"ve already handled the other stuff above.\n        result.pathname = null;\n        result._href = \"\";\n        return result;\n    }\n\n    // if a url ENDs in . or .., then it must get a trailing slash.\n    // however, if it ends in anything else non-slashy,\n    // then it must NOT get a trailing slash.\n    var last = srcPath.slice(-1)[0];\n    var hasTrailingSlash = (\n        (result.host || relative.host) && (last === \".\" || last === \"..\") ||\n        last === \"\");\n\n    // strip single dots, resolve double dots to parent dir\n    // if the path tries to go above the root, `up` ends up > 0\n    var up = 0;\n    for (var i = srcPath.length; i >= 0; i--) {\n        last = srcPath[i];\n        if (last === \".\") {\n            srcPath.splice(i, 1);\n        } else if (last === \"..\") {\n            srcPath.splice(i, 1);\n            up++;\n        } else if (up) {\n            srcPath.splice(i, 1);\n            up--;\n        }\n    }\n\n    // if the path is allowed to go above the root, restore leading ..s\n    if (!mustEndAbs && !removeAllDots) {\n        for (; up--; up) {\n            srcPath.unshift(\"..\");\n        }\n    }\n\n    if (mustEndAbs && srcPath[0] !== \"\" &&\n        (!srcPath[0] || srcPath[0].charCodeAt(0) !== 0x2F /*'/'*/)) {\n        srcPath.unshift(\"\");\n    }\n\n    if (hasTrailingSlash && (srcPath.join(\"/\").substr(-1) !== \"/\")) {\n        srcPath.push(\"\");\n    }\n\n    var isAbsolute = srcPath[0] === \"\" ||\n        (srcPath[0] && srcPath[0].charCodeAt(0) === 0x2F /*'/'*/);\n\n    // put the host back\n    if (psychotic) {\n        result.hostname = result.host = isAbsolute ? \"\" :\n            srcPath.length ? srcPath.shift() : \"\";\n        //occationaly the auth can get stuck only in host\n        //this especialy happens in cases like\n        //url.resolveObject(\"mailto:local1@domain1\", \"local2@domain2\")\n        var authInHost = result.host && result.host.indexOf(\"@\") > 0 ?\n            result.host.split(\"@\") : false;\n        if (authInHost) {\n            result.auth = authInHost.shift();\n            result.host = result.hostname = authInHost.shift();\n        }\n    }\n\n    mustEndAbs = mustEndAbs || (result.host && srcPath.length);\n\n    if (mustEndAbs && !isAbsolute) {\n        srcPath.unshift(\"\");\n    }\n\n    result.pathname = srcPath.length === 0 ? null : srcPath.join(\"/\");\n    result.auth = relative.auth || result.auth;\n    result.slashes = result.slashes || relative.slashes;\n    result._href = \"\";\n    return result;\n};\n\nvar punycode = __webpack_require__(/*! ./punycode */ \"(rsc)/./node_modules/.pnpm/@kamilkisiela+fast-url-parser@1.1.4/node_modules/@kamilkisiela/fast-url-parser/src/punycode.js\");\nUrl.prototype._hostIdna = function Url$_hostIdna(hostname) {\n    // IDNA Support: Returns a punycoded representation of \"domain\".\n    // It only converts parts of the domain name that\n    // have non-ASCII characters, i.e. it doesn't matter if\n    // you call it with a domain that already is ASCII-only.\n    return punycode.toASCII(hostname);\n};\n\nvar escapePathName = Url.prototype._escapePathName =\nfunction Url$_escapePathName(pathname) {\n    if (!containsCharacter2(pathname, 0x23 /*'#'*/, 0x3F /*'?'*/)) {\n        return pathname;\n    }\n    //Avoid closure creation to keep this inlinable\n    return _escapePath(pathname);\n};\n\nvar escapeSearch = Url.prototype._escapeSearch =\nfunction Url$_escapeSearch(search) {\n    if (!containsCharacter2(search, 0x23 /*'#'*/, -1)) return search;\n    //Avoid closure creation to keep this inlinable\n    return _escapeSearch(search);\n};\n\nUrl.prototype._parseProtocol = function Url$_parseProtocol(str, start, end) {\n    var doLowerCase = false;\n    var protocolCharacters = this._protocolCharacters;\n\n    for (var i = start; i <= end; ++i) {\n        var ch = str.charCodeAt(i);\n\n        if (ch === 0x3A /*':'*/) {\n            var protocol = str.slice(start, i);\n            if (doLowerCase) protocol = protocol.toLowerCase();\n            this._protocol = protocol;\n            return i + 1;\n        }\n        else if (protocolCharacters[ch] === 1) {\n            if (ch < 0x61 /*'a'*/)\n                doLowerCase = true;\n        }\n        else {\n            return start;\n        }\n\n    }\n    return start;\n};\n\nUrl.prototype._parseAuth = function Url$_parseAuth(str, start, end, decode) {\n    var auth = str.slice(start, end + 1);\n    if (decode) {\n        auth = decodeURIComponent(auth);\n    }\n    this.auth = auth;\n};\n\nUrl.prototype._parsePort = function Url$_parsePort(str, start, end) {\n    //Internal format is integer for more efficient parsing\n    //and for efficient trimming of leading zeros\n    var port = 0;\n    //Distinguish between :0 and : (no port number at all)\n    var hadChars = false;\n    var validPort = true;\n\n    for (var i = start; i <= end; ++i) {\n        var ch = str.charCodeAt(i);\n\n        if (0x30 /*'0'*/ <= ch && ch <= 0x39 /*'9'*/) {\n            port = (10 * port) + (ch - 0x30 /*'0'*/);\n            hadChars = true;\n        }\n        else {\n            validPort = false;\n            if (ch === 0x5C/*'\\'*/ || ch === 0x2F/*'/'*/) {\n                validPort = true;\n            }\n            break;\n        }\n\n    }\n    if ((port === 0 && !hadChars) || !validPort) {\n        if (!validPort) {\n            this._port = -2;\n        }\n        return 0;\n    }\n\n    this._port = port;\n    return i - start;\n};\n\nUrl.prototype._parseHost =\nfunction Url$_parseHost(str, start, end, slashesDenoteHost) {\n    var hostEndingCharacters = this._hostEndingCharacters;\n    var first = str.charCodeAt(start);\n    var second = str.charCodeAt(start + 1);\n    if ((first === 0x2F /*'/'*/ || first === 0x5C /*'\\'*/) &&\n        (second === 0x2F /*'/'*/ || second === 0x5C /*'\\'*/)) {\n        this.slashes = true;\n\n        //The string starts with //\n        if (start === 0) {\n            //The string is just \"//\"\n            if (end < 2) return start;\n            //If slashes do not denote host and there is no auth,\n            //there is no host when the string starts with //\n            var hasAuth =\n                containsCharacter(str, 0x40 /*'@'*/, 2, hostEndingCharacters);\n            if (!hasAuth && !slashesDenoteHost) {\n                this.slashes = null;\n                return start;\n            }\n        }\n        //There is a host that starts after the //\n        start += 2;\n    }\n    //If there is no slashes, there is no hostname if\n    //1. there was no protocol at all\n    else if (!this._protocol ||\n        //2. there was a protocol that requires slashes\n        //e.g. in 'http:asd' 'asd' is not a hostname\n        slashProtocols[this._protocol]\n    ) {\n        return start;\n    }\n\n    var doLowerCase = false;\n    var idna = false;\n    var hostNameStart = start;\n    var hostNameEnd = end;\n    var lastCh = -1;\n    var portLength = 0;\n    var charsAfterDot = 0;\n    var authNeedsDecoding = false;\n\n    var j = -1;\n\n    //Find the last occurrence of an @-sign until hostending character is met\n    //also mark if decoding is needed for the auth portion\n    for (var i = start; i <= end; ++i) {\n        var ch = str.charCodeAt(i);\n\n        if (ch === 0x40 /*'@'*/) {\n            j = i;\n        }\n        //This check is very, very cheap. Unneeded decodeURIComponent is very\n        //very expensive\n        else if (ch === 0x25 /*'%'*/) {\n            authNeedsDecoding = true;\n        }\n        else if (hostEndingCharacters[ch] === 1) {\n            break;\n        }\n    }\n\n    //@-sign was found at index j, everything to the left from it\n    //is auth part\n    if (j > -1) {\n        this._parseAuth(str, start, j - 1, authNeedsDecoding);\n        //hostname starts after the last @-sign\n        start = hostNameStart = j + 1;\n    }\n\n    //Host name is starting with a [\n    if (str.charCodeAt(start) === 0x5B /*'['*/) {\n        for (var i = start + 1; i <= end; ++i) {\n            var ch = str.charCodeAt(i);\n\n            //Assume valid IP6 is between the brackets\n            if (ch === 0x5D /*']'*/) {\n                if (str.charCodeAt(i + 1) === 0x3A /*':'*/) {\n                    portLength = this._parsePort(str, i + 2, end) + 1;\n                }\n                var hostname = str.slice(start + 1, i).toLowerCase();\n                this.hostname = hostname;\n                this.host = this._port > 0 ?\n                    \"[\" + hostname + \"]:\" + this._port :\n                    \"[\" + hostname + \"]\";\n                this.pathname = \"/\";\n                return i + portLength + 1;\n            }\n        }\n        //Empty hostname, [ starts a path\n        return start;\n    }\n\n    for (var i = start; i <= end; ++i) {\n        if (charsAfterDot > 62) {\n            this.hostname = this.host = str.slice(start, i);\n            return i;\n        }\n        var ch = str.charCodeAt(i);\n\n        if (ch === 0x3A /*':'*/) {\n            portLength = this._parsePort(str, i + 1, end) + 1;\n            hostNameEnd = i - 1;\n            break;\n        }\n        else if (ch < 0x61 /*'a'*/) {\n            if (ch === 0x2E /*'.'*/) {\n                //Node.js ignores this error\n                /*\n                if (lastCh === DOT || lastCh === -1) {\n                    this.hostname = this.host = \"\";\n                    return start;\n                }\n                */\n                charsAfterDot = -1;\n            }\n            else if (0x41 /*'A'*/ <= ch && ch <= 0x5A /*'Z'*/) {\n                doLowerCase = true;\n            }\n            //Valid characters other than ASCII letters -, _, +, 0-9\n            else if (!(ch === 0x2D /*'-'*/ ||\n                       ch === 0x5F /*'_'*/ ||\n                       ch === 0x2B /*'+'*/ ||\n                       (0x30 /*'0'*/ <= ch && ch <= 0x39 /*'9'*/))\n                ) {\n                if (hostEndingCharacters[ch] === 0 &&\n                    this._noPrependSlashHostEnders[ch] === 0) {\n                    this._prependSlash = true;\n                }\n                hostNameEnd = i - 1;\n                break;\n            }\n        }\n        else if (ch >= 0x7B /*'{'*/) {\n            if (ch <= 0x7E /*'~'*/) {\n                if (this._noPrependSlashHostEnders[ch] === 0) {\n                    this._prependSlash = true;\n                }\n                hostNameEnd = i - 1;\n                break;\n            }\n            idna = true;\n        }\n        lastCh = ch;\n        charsAfterDot++;\n    }\n\n    //Node.js ignores this error\n    /*\n    if (lastCh === DOT) {\n        hostNameEnd--;\n    }\n    */\n\n    if (hostNameEnd + 1 !== start &&\n        hostNameEnd - hostNameStart <= 256) {\n        var hostname = str.slice(hostNameStart, hostNameEnd + 1);\n        if (doLowerCase) hostname = hostname.toLowerCase();\n        if (idna) hostname = this._hostIdna(hostname);\n        this.hostname = hostname;\n        this.host = this._port > 0 ? hostname + \":\" + this._port : hostname;\n    }\n\n    return hostNameEnd + 1 + portLength;\n\n};\n\nUrl.prototype._copyPropsTo = function Url$_copyPropsTo(input, noProtocol) {\n    if (!noProtocol) {\n        input._protocol = this._protocol;\n    }\n    input._href = this._href;\n    input._port = this._port;\n    input._prependSlash = this._prependSlash;\n    input.auth = this.auth;\n    input.slashes = this.slashes;\n    input.host = this.host;\n    input.hostname = this.hostname;\n    input.hash = this.hash;\n    input.search = this.search;\n    input.pathname = this.pathname;\n};\n\nUrl.prototype._clone = function Url$_clone() {\n    var ret = new Url();\n    ret._protocol = this._protocol;\n    ret._href = this._href;\n    ret._port = this._port;\n    ret._prependSlash = this._prependSlash;\n    ret.auth = this.auth;\n    ret.slashes = this.slashes;\n    ret.host = this.host;\n    ret.hostname = this.hostname;\n    ret.hash = this.hash;\n    ret.search = this.search;\n    ret.pathname = this.pathname;\n    return ret;\n};\n\nUrl.prototype._getComponentEscaped =\nfunction Url$_getComponentEscaped(str, start, end, isAfterQuery) {\n    var cur = start;\n    var i = start;\n    var ret = \"\";\n    var autoEscapeMap = isAfterQuery ?\n        this._afterQueryAutoEscapeMap : this._autoEscapeMap;\n    for (; i <= end; ++i) {\n        var ch = str.charCodeAt(i);\n        var escaped = autoEscapeMap[ch];\n\n        if (escaped !== \"\" && escaped !== undefined) {\n            if (cur < i) ret += str.slice(cur, i);\n            ret += escaped;\n            cur = i + 1;\n        }\n    }\n    if (cur < i + 1) ret += str.slice(cur, i);\n    return ret;\n};\n\nUrl.prototype._parsePath =\nfunction Url$_parsePath(str, start, end, disableAutoEscapeChars) {\n    var pathStart = start;\n    var pathEnd = end;\n    var escape = false;\n    var autoEscapeCharacters = this._autoEscapeCharacters;\n    var prePath = this._port === -2 ? \"/:\" : \"\";\n\n    for (var i = start; i <= end; ++i) {\n        var ch = str.charCodeAt(i);\n        if (ch === 0x23 /*'#'*/) {\n          this._parseHash(str, i, end, disableAutoEscapeChars);\n            pathEnd = i - 1;\n            break;\n        }\n        else if (ch === 0x3F /*'?'*/) {\n            this._parseQuery(str, i, end, disableAutoEscapeChars);\n            pathEnd = i - 1;\n            break;\n        }\n        else if (!disableAutoEscapeChars && !escape && autoEscapeCharacters[ch] === 1) {\n            escape = true;\n        }\n    }\n\n    if (pathStart > pathEnd) {\n        this.pathname = prePath === \"\" ? \"/\" : prePath;\n        return;\n    }\n\n    var path;\n    if (escape) {\n        path = this._getComponentEscaped(str, pathStart, pathEnd, false);\n    }\n    else {\n        path = str.slice(pathStart, pathEnd + 1);\n    }\n    this.pathname = prePath === \"\"\n        ? (this._prependSlash ? \"/\" + path : path)\n        : prePath + path;\n};\n\nUrl.prototype._parseQuery = function Url$_parseQuery(str, start, end, disableAutoEscapeChars) {\n    var queryStart = start;\n    var queryEnd = end;\n    var escape = false;\n    var autoEscapeCharacters = this._autoEscapeCharacters;\n\n    for (var i = start; i <= end; ++i) {\n        var ch = str.charCodeAt(i);\n\n        if (ch === 0x23 /*'#'*/) {\n            this._parseHash(str, i, end, disableAutoEscapeChars);\n            queryEnd = i - 1;\n            break;\n        }\n        else if (!disableAutoEscapeChars && !escape && autoEscapeCharacters[ch] === 1) {\n            escape = true;\n        }\n    }\n\n    if (queryStart > queryEnd) {\n        this.search = \"\";\n        return;\n    }\n\n    var query;\n    if (escape) {\n        query = this._getComponentEscaped(str, queryStart, queryEnd, true);\n    }\n    else {\n        query = str.slice(queryStart, queryEnd + 1);\n    }\n    this.search = query;\n};\n\nUrl.prototype._parseHash = function Url$_parseHash(str, start, end, disableAutoEscapeChars) {\n    if (start > end) {\n        this.hash = \"\";\n        return;\n    }\n\n    this.hash = disableAutoEscapeChars ?\n        str.slice(start, end + 1) : this._getComponentEscaped(str, start, end, true);\n};\n\nObject.defineProperty(Url.prototype, \"port\", {\n    get: function() {\n        if (this._port >= 0) {\n            return (\"\" + this._port);\n        }\n        return null;\n    },\n    set: function(v) {\n        if (v == null) {\n            this._port = -1;\n        }\n        else {\n            this._port = parseInt(v, 10);\n        }\n    }\n});\n\nObject.defineProperty(Url.prototype, \"query\", {\n    get: function() {\n        var query = this._query;\n        if (query != null) {\n            return query;\n        }\n        var search = this.search;\n\n        if (search) {\n            if (search.charCodeAt(0) === 0x3F /*'?'*/) {\n                search = search.slice(1);\n            }\n            if (search !== \"\") {\n                this._query = search;\n                return search;\n            }\n        }\n        return search;\n    },\n    set: function(v) {\n        this._query = v;\n    }\n});\n\nObject.defineProperty(Url.prototype, \"path\", {\n    get: function() {\n        var p = this.pathname || \"\";\n        var s = this.search || \"\";\n        if (p || s) {\n            return p + s;\n        }\n        return (p == null && s) ? (\"/\" + s) : null;\n    },\n    set: function() {}\n});\n\nObject.defineProperty(Url.prototype, \"protocol\", {\n    get: function() {\n        var proto = this._protocol;\n        return proto ? proto + \":\" : proto;\n    },\n    set: function(v) {\n        if (typeof v === \"string\") {\n            var end = v.length - 1;\n            if (v.charCodeAt(end) === 0x3A /*':'*/) {\n                this._protocol = v.slice(0, end);\n            }\n            else {\n                this._protocol = v;\n            }\n        }\n        else if (v == null) {\n            this._protocol = null;\n        }\n    }\n});\n\nObject.defineProperty(Url.prototype, \"href\", {\n    get: function() {\n        var href = this._href;\n        if (!href) {\n            href = this._href = this.format();\n        }\n        return href;\n    },\n    set: function(v) {\n        this._href = v;\n    }\n});\n\nUrl.parse = function Url$Parse(str, parseQueryString, hostDenotesSlash, disableAutoEscapeChars) {\n    if (str instanceof Url) return str;\n    var ret = new Url();\n    ret.parse(str, !!parseQueryString, !!hostDenotesSlash, !!disableAutoEscapeChars);\n    return ret;\n};\n\nUrl.format = function Url$Format(obj) {\n    if (typeof obj === \"string\") {\n        obj = Url.parse(obj);\n    }\n    if (!(obj instanceof Url)) {\n        return Url.prototype.format.call(obj);\n    }\n    return obj.format();\n};\n\nUrl.resolve = function Url$Resolve(source, relative) {\n    return Url.parse(source, false, true).resolve(relative);\n};\n\nUrl.resolveObject = function Url$ResolveObject(source, relative) {\n    if (!source) return relative;\n    return Url.parse(source, false, true).resolveObject(relative);\n};\n\nfunction _escapePath(pathname) {\n    return pathname.replace(/[?#]/g, function(match) {\n        return encodeURIComponent(match);\n    });\n}\n\nfunction _escapeSearch(search) {\n    return search.replace(/#/g, function(match) {\n        return encodeURIComponent(match);\n    });\n}\n\n//Search `char1` (integer code for a character) in `string`\n//starting from `fromIndex` and ending at `string.length - 1`\n//or when a stop character is found\nfunction containsCharacter(string, char1, fromIndex, stopCharacterTable) {\n    var len = string.length;\n    for (var i = fromIndex; i < len; ++i) {\n        var ch = string.charCodeAt(i);\n\n        if (ch === char1) {\n            return true;\n        }\n        else if (stopCharacterTable[ch] === 1) {\n            return false;\n        }\n    }\n    return false;\n}\n\n//See if `char1` or `char2` (integer codes for characters)\n//is contained in `string`\nfunction containsCharacter2(string, char1, char2) {\n    for (var i = 0, len = string.length; i < len; ++i) {\n        var ch = string.charCodeAt(i);\n        if (ch === char1 || ch === char2) return true;\n    }\n    return false;\n}\n\n//Makes an array of 128 uint8's which represent boolean values.\n//Spec is an array of ascii code points or ascii code point ranges\n//ranges are expressed as [start, end]\n\n//Create a table with the characters 0x30-0x39 (decimals '0' - '9') and\n//0x7A (lowercaseletter 'z') as `true`:\n//\n//var a = makeAsciiTable([[0x30, 0x39], 0x7A]);\n//a[0x30]; //1\n//a[0x15]; //0\n//a[0x35]; //1\nfunction makeAsciiTable(spec) {\n    var ret = new Uint8Array(128);\n    spec.forEach(function(item){\n        if (typeof item === \"number\") {\n            ret[item] = 1;\n        }\n        else {\n            var start = item[0];\n            var end = item[1];\n            for (var j = start; j <= end; ++j) {\n                ret[j] = 1;\n            }\n        }\n    });\n\n    return ret;\n}\n\n\nvar autoEscape = [\"<\", \">\", \"\\\"\", \"`\", \" \", \"\\r\", \"\\n\",\n    \"\\t\", \"{\", \"}\", \"|\", \"\\\\\", \"^\", \"`\", \"'\"];\n\nvar autoEscapeMap = new Array(128);\n\n\n\nfor (var i = 0, len = autoEscapeMap.length; i < len; ++i) {\n    autoEscapeMap[i] = \"\";\n}\n\nfor (var i = 0, len = autoEscape.length; i < len; ++i) {\n    var c = autoEscape[i];\n    var esc = encodeURIComponent(c);\n    if (esc === c) {\n        esc = escape(c);\n    }\n    autoEscapeMap[c.charCodeAt(0)] = esc;\n}\nvar afterQueryAutoEscapeMap = autoEscapeMap.slice();\nautoEscapeMap[0x5C /*'\\'*/] = \"/\";\n\nvar slashProtocols = Url.prototype._slashProtocols = {\n    http: true,\n    https: true,\n    gopher: true,\n    file: true,\n    ftp: true,\n\n    \"http:\": true,\n    \"https:\": true,\n    \"gopher:\": true,\n    \"file:\": true,\n    \"ftp:\": true\n};\n\n//Optimize back from normalized object caused by non-identifier keys\nfunction f(){}\nf.prototype = slashProtocols;\n\nUrl.prototype._protocolCharacters = makeAsciiTable([\n    [0x61 /*'a'*/, 0x7A /*'z'*/],\n    [0x41 /*'A'*/, 0x5A /*'Z'*/],\n    0x2E /*'.'*/, 0x2B /*'+'*/, 0x2D /*'-'*/\n]);\n\nUrl.prototype._hostEndingCharacters = makeAsciiTable([\n    0x23 /*'#'*/, 0x3F /*'?'*/, 0x2F /*'/'*/, 0x5C /*'\\'*/\n]);\n\nUrl.prototype._autoEscapeCharacters = makeAsciiTable(\n    autoEscape.map(function(v) {\n        return v.charCodeAt(0);\n    })\n);\n\n//If these characters end a host name, the path will not be prepended a /\nUrl.prototype._noPrependSlashHostEnders = makeAsciiTable(\n    [\n        \"<\", \">\", \"'\", \"`\", \" \", \"\\r\",\n        \"\\n\", \"\\t\", \"{\", \"}\", \"|\",\n        \"^\", \"`\", \"\\\"\", \"%\", \";\"\n    ].map(function(v) {\n        return v.charCodeAt(0);\n    })\n);\n\nUrl.prototype._autoEscapeMap = autoEscapeMap;\nUrl.prototype._afterQueryAutoEscapeMap = afterQueryAutoEscapeMap;\n\nmodule.exports = Url;\n\nUrl.replace = function Url$Replace() {\n    __webpack_require__.c.url = {\n        exports: Url\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@kamilkisiela+fast-url-parser@1.1.4/node_modules/@kamilkisiela/fast-url-parser/src/urlparser.js\n");

/***/ })

};
;