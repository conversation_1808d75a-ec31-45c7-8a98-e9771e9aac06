"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/thread-stream@3.1.0";
exports.ids = ["vendor-chunks/thread-stream@3.1.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/index.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/index.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { version } = __webpack_require__(/*! ./package.json */ \"(rsc)/./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/package.json\")\nconst { EventEmitter } = __webpack_require__(/*! events */ \"events\")\nconst { Worker } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\nconst { join } = __webpack_require__(/*! path */ \"path\")\nconst { pathToFileURL } = __webpack_require__(/*! url */ \"url\")\nconst { wait } = __webpack_require__(/*! ./lib/wait */ \"(rsc)/./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/lib/wait.js\")\nconst {\n  WRITE_INDEX,\n  READ_INDEX\n} = __webpack_require__(/*! ./lib/indexes */ \"(rsc)/./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/lib/indexes.js\")\nconst buffer = __webpack_require__(/*! buffer */ \"buffer\")\nconst assert = __webpack_require__(/*! assert */ \"assert\")\n\nconst kImpl = Symbol('kImpl')\n\n// V8 limit for string size\nconst MAX_STRING = buffer.constants.MAX_STRING_LENGTH\n\nclass FakeWeakRef {\n  constructor (value) {\n    this._value = value\n  }\n\n  deref () {\n    return this._value\n  }\n}\n\nclass FakeFinalizationRegistry {\n  register () {}\n\n  unregister () {}\n}\n\n// Currently using FinalizationRegistry with code coverage breaks the world\n// Ref: https://github.com/nodejs/node/issues/49344\nconst FinalizationRegistry = process.env.NODE_V8_COVERAGE ? FakeFinalizationRegistry : global.FinalizationRegistry || FakeFinalizationRegistry\nconst WeakRef = process.env.NODE_V8_COVERAGE ? FakeWeakRef : global.WeakRef || FakeWeakRef\n\nconst registry = new FinalizationRegistry((worker) => {\n  if (worker.exited) {\n    return\n  }\n  worker.terminate()\n})\n\nfunction createWorker (stream, opts) {\n  const { filename, workerData } = opts\n\n  const bundlerOverrides = '__bundlerPathsOverrides' in globalThis ? globalThis.__bundlerPathsOverrides : {}\n  const toExecute = bundlerOverrides['thread-stream-worker'] || join(__dirname, 'lib', 'worker.js')\n\n  const worker = new Worker(toExecute, {\n    ...opts.workerOpts,\n    trackUnmanagedFds: false,\n    workerData: {\n      filename: filename.indexOf('file://') === 0\n        ? filename\n        : pathToFileURL(filename).href,\n      dataBuf: stream[kImpl].dataBuf,\n      stateBuf: stream[kImpl].stateBuf,\n      workerData: {\n        $context: {\n          threadStreamVersion: version\n        },\n        ...workerData\n      }\n    }\n  })\n\n  // We keep a strong reference for now,\n  // we need to start writing first\n  worker.stream = new FakeWeakRef(stream)\n\n  worker.on('message', onWorkerMessage)\n  worker.on('exit', onWorkerExit)\n  registry.register(stream, worker)\n\n  return worker\n}\n\nfunction drain (stream) {\n  assert(!stream[kImpl].sync)\n  if (stream[kImpl].needDrain) {\n    stream[kImpl].needDrain = false\n    stream.emit('drain')\n  }\n}\n\nfunction nextFlush (stream) {\n  const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n  let leftover = stream[kImpl].data.length - writeIndex\n\n  if (leftover > 0) {\n    if (stream[kImpl].buf.length === 0) {\n      stream[kImpl].flushing = false\n\n      if (stream[kImpl].ending) {\n        end(stream)\n      } else if (stream[kImpl].needDrain) {\n        process.nextTick(drain, stream)\n      }\n\n      return\n    }\n\n    let toWrite = stream[kImpl].buf.slice(0, leftover)\n    let toWriteBytes = Buffer.byteLength(toWrite)\n    if (toWriteBytes <= leftover) {\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      // process._rawDebug('writing ' + toWrite.length)\n      write(stream, toWrite, nextFlush.bind(null, stream))\n    } else {\n      // multi-byte utf-8\n      stream.flush(() => {\n        // err is already handled in flush()\n        if (stream.destroyed) {\n          return\n        }\n\n        Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n        Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n\n        // Find a toWrite length that fits the buffer\n        // it must exists as the buffer is at least 4 bytes length\n        // and the max utf-8 length for a char is 4 bytes.\n        while (toWriteBytes > stream[kImpl].data.length) {\n          leftover = leftover / 2\n          toWrite = stream[kImpl].buf.slice(0, leftover)\n          toWriteBytes = Buffer.byteLength(toWrite)\n        }\n        stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n        write(stream, toWrite, nextFlush.bind(null, stream))\n      })\n    }\n  } else if (leftover === 0) {\n    if (writeIndex === 0 && stream[kImpl].buf.length === 0) {\n      // we had a flushSync in the meanwhile\n      return\n    }\n    stream.flush(() => {\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n      nextFlush(stream)\n    })\n  } else {\n    // This should never happen\n    destroy(stream, new Error('overwritten'))\n  }\n}\n\nfunction onWorkerMessage (msg) {\n  const stream = this.stream.deref()\n  if (stream === undefined) {\n    this.exited = true\n    // Terminate the worker.\n    this.terminate()\n    return\n  }\n\n  switch (msg.code) {\n    case 'READY':\n      // Replace the FakeWeakRef with a\n      // proper one.\n      this.stream = new WeakRef(stream)\n\n      stream.flush(() => {\n        stream[kImpl].ready = true\n        stream.emit('ready')\n      })\n      break\n    case 'ERROR':\n      destroy(stream, msg.err)\n      break\n    case 'EVENT':\n      if (Array.isArray(msg.args)) {\n        stream.emit(msg.name, ...msg.args)\n      } else {\n        stream.emit(msg.name, msg.args)\n      }\n      break\n    case 'WARNING':\n      process.emitWarning(msg.err)\n      break\n    default:\n      destroy(stream, new Error('this should not happen: ' + msg.code))\n  }\n}\n\nfunction onWorkerExit (code) {\n  const stream = this.stream.deref()\n  if (stream === undefined) {\n    // Nothing to do, the worker already exit\n    return\n  }\n  registry.unregister(stream)\n  stream.worker.exited = true\n  stream.worker.off('exit', onWorkerExit)\n  destroy(stream, code !== 0 ? new Error('the worker thread exited') : null)\n}\n\nclass ThreadStream extends EventEmitter {\n  constructor (opts = {}) {\n    super()\n\n    if (opts.bufferSize < 4) {\n      throw new Error('bufferSize must at least fit a 4-byte utf-8 char')\n    }\n\n    this[kImpl] = {}\n    this[kImpl].stateBuf = new SharedArrayBuffer(128)\n    this[kImpl].state = new Int32Array(this[kImpl].stateBuf)\n    this[kImpl].dataBuf = new SharedArrayBuffer(opts.bufferSize || 4 * 1024 * 1024)\n    this[kImpl].data = Buffer.from(this[kImpl].dataBuf)\n    this[kImpl].sync = opts.sync || false\n    this[kImpl].ending = false\n    this[kImpl].ended = false\n    this[kImpl].needDrain = false\n    this[kImpl].destroyed = false\n    this[kImpl].flushing = false\n    this[kImpl].ready = false\n    this[kImpl].finished = false\n    this[kImpl].errored = null\n    this[kImpl].closed = false\n    this[kImpl].buf = ''\n\n    // TODO (fix): Make private?\n    this.worker = createWorker(this, opts) // TODO (fix): make private\n    this.on('message', (message, transferList) => {\n      this.worker.postMessage(message, transferList)\n    })\n  }\n\n  write (data) {\n    if (this[kImpl].destroyed) {\n      error(this, new Error('the worker has exited'))\n      return false\n    }\n\n    if (this[kImpl].ending) {\n      error(this, new Error('the worker is ending'))\n      return false\n    }\n\n    if (this[kImpl].flushing && this[kImpl].buf.length + data.length >= MAX_STRING) {\n      try {\n        writeSync(this)\n        this[kImpl].flushing = true\n      } catch (err) {\n        destroy(this, err)\n        return false\n      }\n    }\n\n    this[kImpl].buf += data\n\n    if (this[kImpl].sync) {\n      try {\n        writeSync(this)\n        return true\n      } catch (err) {\n        destroy(this, err)\n        return false\n      }\n    }\n\n    if (!this[kImpl].flushing) {\n      this[kImpl].flushing = true\n      setImmediate(nextFlush, this)\n    }\n\n    this[kImpl].needDrain = this[kImpl].data.length - this[kImpl].buf.length - Atomics.load(this[kImpl].state, WRITE_INDEX) <= 0\n    return !this[kImpl].needDrain\n  }\n\n  end () {\n    if (this[kImpl].destroyed) {\n      return\n    }\n\n    this[kImpl].ending = true\n    end(this)\n  }\n\n  flush (cb) {\n    if (this[kImpl].destroyed) {\n      if (typeof cb === 'function') {\n        process.nextTick(cb, new Error('the worker has exited'))\n      }\n      return\n    }\n\n    // TODO write all .buf\n    const writeIndex = Atomics.load(this[kImpl].state, WRITE_INDEX)\n    // process._rawDebug(`(flush) readIndex (${Atomics.load(this.state, READ_INDEX)}) writeIndex (${Atomics.load(this.state, WRITE_INDEX)})`)\n    wait(this[kImpl].state, READ_INDEX, writeIndex, Infinity, (err, res) => {\n      if (err) {\n        destroy(this, err)\n        process.nextTick(cb, err)\n        return\n      }\n      if (res === 'not-equal') {\n        // TODO handle deadlock\n        this.flush(cb)\n        return\n      }\n      process.nextTick(cb)\n    })\n  }\n\n  flushSync () {\n    if (this[kImpl].destroyed) {\n      return\n    }\n\n    writeSync(this)\n    flushSync(this)\n  }\n\n  unref () {\n    this.worker.unref()\n  }\n\n  ref () {\n    this.worker.ref()\n  }\n\n  get ready () {\n    return this[kImpl].ready\n  }\n\n  get destroyed () {\n    return this[kImpl].destroyed\n  }\n\n  get closed () {\n    return this[kImpl].closed\n  }\n\n  get writable () {\n    return !this[kImpl].destroyed && !this[kImpl].ending\n  }\n\n  get writableEnded () {\n    return this[kImpl].ending\n  }\n\n  get writableFinished () {\n    return this[kImpl].finished\n  }\n\n  get writableNeedDrain () {\n    return this[kImpl].needDrain\n  }\n\n  get writableObjectMode () {\n    return false\n  }\n\n  get writableErrored () {\n    return this[kImpl].errored\n  }\n}\n\nfunction error (stream, err) {\n  setImmediate(() => {\n    stream.emit('error', err)\n  })\n}\n\nfunction destroy (stream, err) {\n  if (stream[kImpl].destroyed) {\n    return\n  }\n  stream[kImpl].destroyed = true\n\n  if (err) {\n    stream[kImpl].errored = err\n    error(stream, err)\n  }\n\n  if (!stream.worker.exited) {\n    stream.worker.terminate()\n      .catch(() => {})\n      .then(() => {\n        stream[kImpl].closed = true\n        stream.emit('close')\n      })\n  } else {\n    setImmediate(() => {\n      stream[kImpl].closed = true\n      stream.emit('close')\n    })\n  }\n}\n\nfunction write (stream, data, cb) {\n  // data is smaller than the shared buffer length\n  const current = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n  const length = Buffer.byteLength(data)\n  stream[kImpl].data.write(data, current)\n  Atomics.store(stream[kImpl].state, WRITE_INDEX, current + length)\n  Atomics.notify(stream[kImpl].state, WRITE_INDEX)\n  cb()\n  return true\n}\n\nfunction end (stream) {\n  if (stream[kImpl].ended || !stream[kImpl].ending || stream[kImpl].flushing) {\n    return\n  }\n  stream[kImpl].ended = true\n\n  try {\n    stream.flushSync()\n\n    let readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n    // process._rawDebug('writing index')\n    Atomics.store(stream[kImpl].state, WRITE_INDEX, -1)\n    // process._rawDebug(`(end) readIndex (${Atomics.load(stream.state, READ_INDEX)}) writeIndex (${Atomics.load(stream.state, WRITE_INDEX)})`)\n    Atomics.notify(stream[kImpl].state, WRITE_INDEX)\n\n    // Wait for the process to complete\n    let spins = 0\n    while (readIndex !== -1) {\n      // process._rawDebug(`read = ${read}`)\n      Atomics.wait(stream[kImpl].state, READ_INDEX, readIndex, 1000)\n      readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n      if (readIndex === -2) {\n        destroy(stream, new Error('end() failed'))\n        return\n      }\n\n      if (++spins === 10) {\n        destroy(stream, new Error('end() took too long (10s)'))\n        return\n      }\n    }\n\n    process.nextTick(() => {\n      stream[kImpl].finished = true\n      stream.emit('finish')\n    })\n  } catch (err) {\n    destroy(stream, err)\n  }\n  // process._rawDebug('end finished...')\n}\n\nfunction writeSync (stream) {\n  const cb = () => {\n    if (stream[kImpl].ending) {\n      end(stream)\n    } else if (stream[kImpl].needDrain) {\n      process.nextTick(drain, stream)\n    }\n  }\n  stream[kImpl].flushing = false\n\n  while (stream[kImpl].buf.length !== 0) {\n    const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n    let leftover = stream[kImpl].data.length - writeIndex\n    if (leftover === 0) {\n      flushSync(stream)\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n      continue\n    } else if (leftover < 0) {\n      // stream should never happen\n      throw new Error('overwritten')\n    }\n\n    let toWrite = stream[kImpl].buf.slice(0, leftover)\n    let toWriteBytes = Buffer.byteLength(toWrite)\n    if (toWriteBytes <= leftover) {\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      // process._rawDebug('writing ' + toWrite.length)\n      write(stream, toWrite, cb)\n    } else {\n      // multi-byte utf-8\n      flushSync(stream)\n      Atomics.store(stream[kImpl].state, READ_INDEX, 0)\n      Atomics.store(stream[kImpl].state, WRITE_INDEX, 0)\n\n      // Find a toWrite length that fits the buffer\n      // it must exists as the buffer is at least 4 bytes length\n      // and the max utf-8 length for a char is 4 bytes.\n      while (toWriteBytes > stream[kImpl].buf.length) {\n        leftover = leftover / 2\n        toWrite = stream[kImpl].buf.slice(0, leftover)\n        toWriteBytes = Buffer.byteLength(toWrite)\n      }\n      stream[kImpl].buf = stream[kImpl].buf.slice(leftover)\n      write(stream, toWrite, cb)\n    }\n  }\n}\n\nfunction flushSync (stream) {\n  if (stream[kImpl].flushing) {\n    throw new Error('unable to flush while flushing')\n  }\n\n  // process._rawDebug('flushSync started')\n\n  const writeIndex = Atomics.load(stream[kImpl].state, WRITE_INDEX)\n\n  let spins = 0\n\n  // TODO handle deadlock\n  while (true) {\n    const readIndex = Atomics.load(stream[kImpl].state, READ_INDEX)\n\n    if (readIndex === -2) {\n      throw Error('_flushSync failed')\n    }\n\n    // process._rawDebug(`(flushSync) readIndex (${readIndex}) writeIndex (${writeIndex})`)\n    if (readIndex !== writeIndex) {\n      // TODO stream timeouts for some reason.\n      Atomics.wait(stream[kImpl].state, READ_INDEX, readIndex, 1000)\n    } else {\n      break\n    }\n\n    if (++spins === 10) {\n      throw new Error('_flushSync took too long (10s)')\n    }\n  }\n  // process._rawDebug('flushSync finished')\n}\n\nmodule.exports = ThreadStream\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/lib/indexes.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/lib/indexes.js ***!
  \******************************************************************************************/
/***/ ((module) => {

eval("\n\nconst WRITE_INDEX = 4\nconst READ_INDEX = 8\n\nmodule.exports = {\n  WRITE_INDEX,\n  READ_INDEX\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vdGhyZWFkLXN0cmVhbUAzLjEuMC9ub2RlX21vZHVsZXMvdGhyZWFkLXN0cmVhbS9saWIvaW5kZXhlcy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vdGhyZWFkLXN0cmVhbUAzLjEuMC9ub2RlX21vZHVsZXMvdGhyZWFkLXN0cmVhbS9saWIvaW5kZXhlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgV1JJVEVfSU5ERVggPSA0XG5jb25zdCBSRUFEX0lOREVYID0gOFxuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgV1JJVEVfSU5ERVgsXG4gIFJFQURfSU5ERVhcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/lib/indexes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/lib/wait.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/lib/wait.js ***!
  \***************************************************************************************/
/***/ ((module) => {

eval("\n\nconst MAX_TIMEOUT = 1000\n\nfunction wait (state, index, expected, timeout, done) {\n  const max = Date.now() + timeout\n  let current = Atomics.load(state, index)\n  if (current === expected) {\n    done(null, 'ok')\n    return\n  }\n  let prior = current\n  const check = (backoff) => {\n    if (Date.now() > max) {\n      done(null, 'timed-out')\n    } else {\n      setTimeout(() => {\n        prior = current\n        current = Atomics.load(state, index)\n        if (current === prior) {\n          check(backoff >= MAX_TIMEOUT ? MAX_TIMEOUT : backoff * 2)\n        } else {\n          if (current === expected) done(null, 'ok')\n          else done(null, 'not-equal')\n        }\n      }, backoff)\n    }\n  }\n  check(1)\n}\n\n// let waitDiffCount = 0\nfunction waitDiff (state, index, expected, timeout, done) {\n  // const id = waitDiffCount++\n  // process._rawDebug(`>>> waitDiff ${id}`)\n  const max = Date.now() + timeout\n  let current = Atomics.load(state, index)\n  if (current !== expected) {\n    done(null, 'ok')\n    return\n  }\n  const check = (backoff) => {\n    // process._rawDebug(`${id} ${index} current ${current} expected ${expected}`)\n    // process._rawDebug('' + backoff)\n    if (Date.now() > max) {\n      done(null, 'timed-out')\n    } else {\n      setTimeout(() => {\n        current = Atomics.load(state, index)\n        if (current !== expected) {\n          done(null, 'ok')\n        } else {\n          check(backoff >= MAX_TIMEOUT ? MAX_TIMEOUT : backoff * 2)\n        }\n      }, backoff)\n    }\n  }\n  check(1)\n}\n\nmodule.exports = { wait, waitDiff }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/lib/wait.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/package.json":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/thread-stream@3.1.0/node_modules/thread-stream/package.json ***!
  \****************************************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"thread-stream","version":"3.1.0","description":"A streaming way to send data to a Node.js Worker Thread","main":"index.js","types":"index.d.ts","dependencies":{"real-require":"^0.2.0"},"devDependencies":{"@types/node":"^20.1.0","@types/tap":"^15.0.0","@yao-pkg/pkg":"^5.11.5","desm":"^1.3.0","fastbench":"^1.0.1","husky":"^9.0.6","pino-elasticsearch":"^8.0.0","sonic-boom":"^4.0.1","standard":"^17.0.0","tap":"^16.2.0","ts-node":"^10.8.0","typescript":"^5.3.2","why-is-node-running":"^2.2.2"},"scripts":{"build":"tsc --noEmit","test":"standard && npm run build && npm run transpile && tap \\"test/**/*.test.*js\\" && tap --ts test/*.test.*ts","test:ci":"standard && npm run transpile && npm run test:ci:js && npm run test:ci:ts","test:ci:js":"tap --no-check-coverage --timeout=120 --coverage-report=lcovonly \\"test/**/*.test.*js\\"","test:ci:ts":"tap --ts --no-check-coverage --coverage-report=lcovonly \\"test/**/*.test.*ts\\"","test:yarn":"npm run transpile && tap \\"test/**/*.test.js\\" --no-check-coverage","transpile":"sh ./test/ts/transpile.sh","prepare":"husky install"},"standard":{"ignore":["test/ts/**/*","test/syntax-error.mjs"]},"repository":{"type":"git","url":"git+https://github.com/mcollina/thread-stream.git"},"keywords":["worker","thread","threads","stream"],"author":"Matteo Collina <<EMAIL>>","license":"MIT","bugs":{"url":"https://github.com/mcollina/thread-stream/issues"},"homepage":"https://github.com/mcollina/thread-stream#readme"}');

/***/ })

};
;