"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonic-boom@4.1.0";
exports.ids = ["vendor-chunks/sonic-boom@4.1.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/sonic-boom@4.1.0/node_modules/sonic-boom/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/sonic-boom@4.1.0/node_modules/sonic-boom/index.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst fs = __webpack_require__(/*! fs */ \"fs\")\nconst EventEmitter = __webpack_require__(/*! events */ \"events\")\nconst inherits = (__webpack_require__(/*! util */ \"util\").inherits)\nconst path = __webpack_require__(/*! path */ \"path\")\nconst sleep = __webpack_require__(/*! atomic-sleep */ \"(rsc)/./node_modules/.pnpm/atomic-sleep@1.0.0/node_modules/atomic-sleep/index.js\")\n\nconst BUSY_WRITE_TIMEOUT = 100\nconst kEmptyBuffer = Buffer.allocUnsafe(0)\n\n// 16 KB. Don't write more than docker buffer size.\n// https://github.com/moby/moby/blob/513ec73831269947d38a644c278ce3cac36783b2/daemon/logger/copier.go#L13\nconst MAX_WRITE = 16 * 1024\n\nconst kContentModeBuffer = 'buffer'\nconst kContentModeUtf8 = 'utf8'\n\nconst [major, minor] = process.versions.node.split('.').map(Number)\nconst kCopyBuffer = major >= 22 && minor >= 7\n\nfunction openFile (file, sonic) {\n  sonic._opening = true\n  sonic._writing = true\n  sonic._asyncDrainScheduled = false\n\n  // NOTE: 'error' and 'ready' events emitted below only relevant when sonic.sync===false\n  // for sync mode, there is no way to add a listener that will receive these\n\n  function fileOpened (err, fd) {\n    if (err) {\n      sonic._reopening = false\n      sonic._writing = false\n      sonic._opening = false\n\n      if (sonic.sync) {\n        process.nextTick(() => {\n          if (sonic.listenerCount('error') > 0) {\n            sonic.emit('error', err)\n          }\n        })\n      } else {\n        sonic.emit('error', err)\n      }\n      return\n    }\n\n    const reopening = sonic._reopening\n\n    sonic.fd = fd\n    sonic.file = file\n    sonic._reopening = false\n    sonic._opening = false\n    sonic._writing = false\n\n    if (sonic.sync) {\n      process.nextTick(() => sonic.emit('ready'))\n    } else {\n      sonic.emit('ready')\n    }\n\n    if (sonic.destroyed) {\n      return\n    }\n\n    // start\n    if ((!sonic._writing && sonic._len > sonic.minLength) || sonic._flushPending) {\n      sonic._actualWrite()\n    } else if (reopening) {\n      process.nextTick(() => sonic.emit('drain'))\n    }\n  }\n\n  const flags = sonic.append ? 'a' : 'w'\n  const mode = sonic.mode\n\n  if (sonic.sync) {\n    try {\n      if (sonic.mkdir) fs.mkdirSync(path.dirname(file), { recursive: true })\n      const fd = fs.openSync(file, flags, mode)\n      fileOpened(null, fd)\n    } catch (err) {\n      fileOpened(err)\n      throw err\n    }\n  } else if (sonic.mkdir) {\n    fs.mkdir(path.dirname(file), { recursive: true }, (err) => {\n      if (err) return fileOpened(err)\n      fs.open(file, flags, mode, fileOpened)\n    })\n  } else {\n    fs.open(file, flags, mode, fileOpened)\n  }\n}\n\nfunction SonicBoom (opts) {\n  if (!(this instanceof SonicBoom)) {\n    return new SonicBoom(opts)\n  }\n\n  let { fd, dest, minLength, maxLength, maxWrite, periodicFlush, sync, append = true, mkdir, retryEAGAIN, fsync, contentMode, mode } = opts || {}\n\n  fd = fd || dest\n\n  this._len = 0\n  this.fd = -1\n  this._bufs = []\n  this._lens = []\n  this._writing = false\n  this._ending = false\n  this._reopening = false\n  this._asyncDrainScheduled = false\n  this._flushPending = false\n  this._hwm = Math.max(minLength || 0, 16387)\n  this.file = null\n  this.destroyed = false\n  this.minLength = minLength || 0\n  this.maxLength = maxLength || 0\n  this.maxWrite = maxWrite || MAX_WRITE\n  this._periodicFlush = periodicFlush || 0\n  this._periodicFlushTimer = undefined\n  this.sync = sync || false\n  this.writable = true\n  this._fsync = fsync || false\n  this.append = append || false\n  this.mode = mode\n  this.retryEAGAIN = retryEAGAIN || (() => true)\n  this.mkdir = mkdir || false\n\n  let fsWriteSync\n  let fsWrite\n  if (contentMode === kContentModeBuffer) {\n    this._writingBuf = kEmptyBuffer\n    this.write = writeBuffer\n    this.flush = flushBuffer\n    this.flushSync = flushBufferSync\n    this._actualWrite = actualWriteBuffer\n    fsWriteSync = () => fs.writeSync(this.fd, this._writingBuf)\n    fsWrite = () => fs.write(this.fd, this._writingBuf, this.release)\n  } else if (contentMode === undefined || contentMode === kContentModeUtf8) {\n    this._writingBuf = ''\n    this.write = write\n    this.flush = flush\n    this.flushSync = flushSync\n    this._actualWrite = actualWrite\n    fsWriteSync = () => fs.writeSync(this.fd, this._writingBuf, 'utf8')\n    fsWrite = () => fs.write(this.fd, this._writingBuf, 'utf8', this.release)\n  } else {\n    throw new Error(`SonicBoom supports \"${kContentModeUtf8}\" and \"${kContentModeBuffer}\", but passed ${contentMode}`)\n  }\n\n  if (typeof fd === 'number') {\n    this.fd = fd\n    process.nextTick(() => this.emit('ready'))\n  } else if (typeof fd === 'string') {\n    openFile(fd, this)\n  } else {\n    throw new Error('SonicBoom supports only file descriptors and files')\n  }\n  if (this.minLength >= this.maxWrite) {\n    throw new Error(`minLength should be smaller than maxWrite (${this.maxWrite})`)\n  }\n\n  this.release = (err, n) => {\n    if (err) {\n      if ((err.code === 'EAGAIN' || err.code === 'EBUSY') && this.retryEAGAIN(err, this._writingBuf.length, this._len - this._writingBuf.length)) {\n        if (this.sync) {\n          // This error code should not happen in sync mode, because it is\n          // not using the underlining operating system asynchronous functions.\n          // However it happens, and so we handle it.\n          // Ref: https://github.com/pinojs/pino/issues/783\n          try {\n            sleep(BUSY_WRITE_TIMEOUT)\n            this.release(undefined, 0)\n          } catch (err) {\n            this.release(err)\n          }\n        } else {\n          // Let's give the destination some time to process the chunk.\n          setTimeout(fsWrite, BUSY_WRITE_TIMEOUT)\n        }\n      } else {\n        this._writing = false\n\n        this.emit('error', err)\n      }\n      return\n    }\n\n    this.emit('write', n)\n    const releasedBufObj = releaseWritingBuf(this._writingBuf, this._len, n)\n    this._len = releasedBufObj.len\n    this._writingBuf = releasedBufObj.writingBuf\n\n    if (this._writingBuf.length) {\n      if (!this.sync) {\n        fsWrite()\n        return\n      }\n\n      try {\n        do {\n          const n = fsWriteSync()\n          const releasedBufObj = releaseWritingBuf(this._writingBuf, this._len, n)\n          this._len = releasedBufObj.len\n          this._writingBuf = releasedBufObj.writingBuf\n        } while (this._writingBuf.length)\n      } catch (err) {\n        this.release(err)\n        return\n      }\n    }\n\n    if (this._fsync) {\n      fs.fsyncSync(this.fd)\n    }\n\n    const len = this._len\n    if (this._reopening) {\n      this._writing = false\n      this._reopening = false\n      this.reopen()\n    } else if (len > this.minLength) {\n      this._actualWrite()\n    } else if (this._ending) {\n      if (len > 0) {\n        this._actualWrite()\n      } else {\n        this._writing = false\n        actualClose(this)\n      }\n    } else {\n      this._writing = false\n      if (this.sync) {\n        if (!this._asyncDrainScheduled) {\n          this._asyncDrainScheduled = true\n          process.nextTick(emitDrain, this)\n        }\n      } else {\n        this.emit('drain')\n      }\n    }\n  }\n\n  this.on('newListener', function (name) {\n    if (name === 'drain') {\n      this._asyncDrainScheduled = false\n    }\n  })\n\n  if (this._periodicFlush !== 0) {\n    this._periodicFlushTimer = setInterval(() => this.flush(null), this._periodicFlush)\n    this._periodicFlushTimer.unref()\n  }\n}\n\n/**\n * Release the writingBuf after fs.write n bytes data\n * @param {string | Buffer} writingBuf - currently writing buffer, usually be instance._writingBuf.\n * @param {number} len - currently buffer length, usually be instance._len.\n * @param {number} n - number of bytes fs already written\n * @returns {{writingBuf: string | Buffer, len: number}} released writingBuf and length\n */\nfunction releaseWritingBuf (writingBuf, len, n) {\n  // if Buffer.byteLength is equal to n, that means writingBuf contains no multi-byte character\n  if (typeof writingBuf === 'string' && Buffer.byteLength(writingBuf) !== n) {\n    // Since the fs.write callback parameter `n` means how many bytes the passed of string\n    // We calculate the original string length for avoiding the multi-byte character issue\n    n = Buffer.from(writingBuf).subarray(0, n).toString().length\n  }\n  len = Math.max(len - n, 0)\n  writingBuf = writingBuf.slice(n)\n  return { writingBuf, len }\n}\n\nfunction emitDrain (sonic) {\n  const hasListeners = sonic.listenerCount('drain') > 0\n  if (!hasListeners) return\n  sonic._asyncDrainScheduled = false\n  sonic.emit('drain')\n}\n\ninherits(SonicBoom, EventEmitter)\n\nfunction mergeBuf (bufs, len) {\n  if (bufs.length === 0) {\n    return kEmptyBuffer\n  }\n\n  if (bufs.length === 1) {\n    return bufs[0]\n  }\n\n  return Buffer.concat(bufs, len)\n}\n\nfunction write (data) {\n  if (this.destroyed) {\n    throw new Error('SonicBoom destroyed')\n  }\n\n  const len = this._len + data.length\n  const bufs = this._bufs\n\n  if (this.maxLength && len > this.maxLength) {\n    this.emit('drop', data)\n    return this._len < this._hwm\n  }\n\n  if (\n    bufs.length === 0 ||\n    bufs[bufs.length - 1].length + data.length > this.maxWrite\n  ) {\n    bufs.push('' + data)\n  } else {\n    bufs[bufs.length - 1] += data\n  }\n\n  this._len = len\n\n  if (!this._writing && this._len >= this.minLength) {\n    this._actualWrite()\n  }\n\n  return this._len < this._hwm\n}\n\nfunction writeBuffer (data) {\n  if (this.destroyed) {\n    throw new Error('SonicBoom destroyed')\n  }\n\n  const len = this._len + data.length\n  const bufs = this._bufs\n  const lens = this._lens\n\n  if (this.maxLength && len > this.maxLength) {\n    this.emit('drop', data)\n    return this._len < this._hwm\n  }\n\n  if (\n    bufs.length === 0 ||\n    lens[lens.length - 1] + data.length > this.maxWrite\n  ) {\n    bufs.push([data])\n    lens.push(data.length)\n  } else {\n    bufs[bufs.length - 1].push(data)\n    lens[lens.length - 1] += data.length\n  }\n\n  this._len = len\n\n  if (!this._writing && this._len >= this.minLength) {\n    this._actualWrite()\n  }\n\n  return this._len < this._hwm\n}\n\nfunction callFlushCallbackOnDrain (cb) {\n  this._flushPending = true\n  const onDrain = () => {\n    // only if _fsync is false to avoid double fsync\n    if (!this._fsync) {\n      fs.fsync(this.fd, (err) => {\n        this._flushPending = false\n        cb(err)\n      })\n    } else {\n      this._flushPending = false\n      cb()\n    }\n    this.off('error', onError)\n  }\n  const onError = (err) => {\n    this._flushPending = false\n    cb(err)\n    this.off('drain', onDrain)\n  }\n\n  this.once('drain', onDrain)\n  this.once('error', onError)\n}\n\nfunction flush (cb) {\n  if (cb != null && typeof cb !== 'function') {\n    throw new Error('flush cb must be a function')\n  }\n\n  if (this.destroyed) {\n    const error = new Error('SonicBoom destroyed')\n    if (cb) {\n      cb(error)\n      return\n    }\n\n    throw error\n  }\n\n  if (this.minLength <= 0) {\n    cb?.()\n    return\n  }\n\n  if (cb) {\n    callFlushCallbackOnDrain.call(this, cb)\n  }\n\n  if (this._writing) {\n    return\n  }\n\n  if (this._bufs.length === 0) {\n    this._bufs.push('')\n  }\n\n  this._actualWrite()\n}\n\nfunction flushBuffer (cb) {\n  if (cb != null && typeof cb !== 'function') {\n    throw new Error('flush cb must be a function')\n  }\n\n  if (this.destroyed) {\n    const error = new Error('SonicBoom destroyed')\n    if (cb) {\n      cb(error)\n      return\n    }\n\n    throw error\n  }\n\n  if (this.minLength <= 0) {\n    cb?.()\n    return\n  }\n\n  if (cb) {\n    callFlushCallbackOnDrain.call(this, cb)\n  }\n\n  if (this._writing) {\n    return\n  }\n\n  if (this._bufs.length === 0) {\n    this._bufs.push([])\n    this._lens.push(0)\n  }\n\n  this._actualWrite()\n}\n\nSonicBoom.prototype.reopen = function (file) {\n  if (this.destroyed) {\n    throw new Error('SonicBoom destroyed')\n  }\n\n  if (this._opening) {\n    this.once('ready', () => {\n      this.reopen(file)\n    })\n    return\n  }\n\n  if (this._ending) {\n    return\n  }\n\n  if (!this.file) {\n    throw new Error('Unable to reopen a file descriptor, you must pass a file to SonicBoom')\n  }\n\n  if (file) {\n    this.file = file\n  }\n  this._reopening = true\n\n  if (this._writing) {\n    return\n  }\n\n  const fd = this.fd\n  this.once('ready', () => {\n    if (fd !== this.fd) {\n      fs.close(fd, (err) => {\n        if (err) {\n          return this.emit('error', err)\n        }\n      })\n    }\n  })\n\n  openFile(this.file, this)\n}\n\nSonicBoom.prototype.end = function () {\n  if (this.destroyed) {\n    throw new Error('SonicBoom destroyed')\n  }\n\n  if (this._opening) {\n    this.once('ready', () => {\n      this.end()\n    })\n    return\n  }\n\n  if (this._ending) {\n    return\n  }\n\n  this._ending = true\n\n  if (this._writing) {\n    return\n  }\n\n  if (this._len > 0 && this.fd >= 0) {\n    this._actualWrite()\n  } else {\n    actualClose(this)\n  }\n}\n\nfunction flushSync () {\n  if (this.destroyed) {\n    throw new Error('SonicBoom destroyed')\n  }\n\n  if (this.fd < 0) {\n    throw new Error('sonic boom is not ready yet')\n  }\n\n  if (!this._writing && this._writingBuf.length > 0) {\n    this._bufs.unshift(this._writingBuf)\n    this._writingBuf = ''\n  }\n\n  let buf = ''\n  while (this._bufs.length || buf) {\n    if (buf.length <= 0) {\n      buf = this._bufs[0]\n    }\n    try {\n      const n = fs.writeSync(this.fd, buf, 'utf8')\n      const releasedBufObj = releaseWritingBuf(buf, this._len, n)\n      buf = releasedBufObj.writingBuf\n      this._len = releasedBufObj.len\n      if (buf.length <= 0) {\n        this._bufs.shift()\n      }\n    } catch (err) {\n      const shouldRetry = err.code === 'EAGAIN' || err.code === 'EBUSY'\n      if (shouldRetry && !this.retryEAGAIN(err, buf.length, this._len - buf.length)) {\n        throw err\n      }\n\n      sleep(BUSY_WRITE_TIMEOUT)\n    }\n  }\n\n  try {\n    fs.fsyncSync(this.fd)\n  } catch {\n    // Skip the error. The fd might not support fsync.\n  }\n}\n\nfunction flushBufferSync () {\n  if (this.destroyed) {\n    throw new Error('SonicBoom destroyed')\n  }\n\n  if (this.fd < 0) {\n    throw new Error('sonic boom is not ready yet')\n  }\n\n  if (!this._writing && this._writingBuf.length > 0) {\n    this._bufs.unshift([this._writingBuf])\n    this._writingBuf = kEmptyBuffer\n  }\n\n  let buf = kEmptyBuffer\n  while (this._bufs.length || buf.length) {\n    if (buf.length <= 0) {\n      buf = mergeBuf(this._bufs[0], this._lens[0])\n    }\n    try {\n      const n = fs.writeSync(this.fd, buf)\n      buf = buf.subarray(n)\n      this._len = Math.max(this._len - n, 0)\n      if (buf.length <= 0) {\n        this._bufs.shift()\n        this._lens.shift()\n      }\n    } catch (err) {\n      const shouldRetry = err.code === 'EAGAIN' || err.code === 'EBUSY'\n      if (shouldRetry && !this.retryEAGAIN(err, buf.length, this._len - buf.length)) {\n        throw err\n      }\n\n      sleep(BUSY_WRITE_TIMEOUT)\n    }\n  }\n}\n\nSonicBoom.prototype.destroy = function () {\n  if (this.destroyed) {\n    return\n  }\n  actualClose(this)\n}\n\nfunction actualWrite () {\n  const release = this.release\n  this._writing = true\n  this._writingBuf = this._writingBuf || this._bufs.shift() || ''\n\n  if (this.sync) {\n    try {\n      const written = fs.writeSync(this.fd, this._writingBuf, 'utf8')\n      release(null, written)\n    } catch (err) {\n      release(err)\n    }\n  } else {\n    fs.write(this.fd, this._writingBuf, 'utf8', release)\n  }\n}\n\nfunction actualWriteBuffer () {\n  const release = this.release\n  this._writing = true\n  this._writingBuf = this._writingBuf.length ? this._writingBuf : mergeBuf(this._bufs.shift(), this._lens.shift())\n\n  if (this.sync) {\n    try {\n      const written = fs.writeSync(this.fd, this._writingBuf)\n      release(null, written)\n    } catch (err) {\n      release(err)\n    }\n  } else {\n    // fs.write will need to copy string to buffer anyway so\n    // we do it here to avoid the overhead of calculating the buffer size\n    // in releaseWritingBuf.\n    if (kCopyBuffer) {\n      this._writingBuf = Buffer.from(this._writingBuf)\n    }\n    fs.write(this.fd, this._writingBuf, release)\n  }\n}\n\nfunction actualClose (sonic) {\n  if (sonic.fd === -1) {\n    sonic.once('ready', actualClose.bind(null, sonic))\n    return\n  }\n\n  if (sonic._periodicFlushTimer !== undefined) {\n    clearInterval(sonic._periodicFlushTimer)\n  }\n\n  sonic.destroyed = true\n  sonic._bufs = []\n  sonic._lens = []\n\n  fs.fsync(sonic.fd, closeWrapped)\n\n  function closeWrapped () {\n    // We skip errors in fsync\n\n    if (sonic.fd !== 1 && sonic.fd !== 2) {\n      fs.close(sonic.fd, done)\n    } else {\n      done()\n    }\n  }\n\n  function done (err) {\n    if (err) {\n      sonic.emit('error', err)\n      return\n    }\n\n    if (sonic._ending && !sonic._writing) {\n      sonic.emit('finish')\n    }\n    sonic.emit('close')\n  }\n}\n\n/**\n * These export configurations enable JS and TS developers\n * to consumer SonicBoom in whatever way best suits their needs.\n * Some examples of supported import syntax includes:\n * - `const SonicBoom = require('SonicBoom')`\n * - `const { SonicBoom } = require('SonicBoom')`\n * - `import * as SonicBoom from 'SonicBoom'`\n * - `import { SonicBoom } from 'SonicBoom'`\n * - `import SonicBoom from 'SonicBoom'`\n */\nSonicBoom.SonicBoom = SonicBoom\nSonicBoom.default = SonicBoom\nmodule.exports = SonicBoom\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/sonic-boom@4.1.0/node_modules/sonic-boom/index.js\n");

/***/ })

};
;