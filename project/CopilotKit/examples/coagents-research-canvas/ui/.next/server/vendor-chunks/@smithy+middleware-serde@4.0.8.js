"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+middleware-serde@4.0.8";
exports.ids = ["vendor-chunks/@smithy+middleware-serde@4.0.8"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/deserializerMiddleware.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/deserializerMiddleware.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserializerMiddleware: () => (/* binding */ deserializerMiddleware)\n/* harmony export */ });\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n\nconst deserializerMiddleware = (options, deserializer) => (next, context) => async (args) => {\n    const { response } = await next(args);\n    try {\n        const parsed = await deserializer(response, options);\n        return {\n            response,\n            output: parsed,\n        };\n    }\n    catch (error) {\n        Object.defineProperty(error, \"$response\", {\n            value: response,\n        });\n        if (!(\"$metadata\" in error)) {\n            const hint = `Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.`;\n            try {\n                error.message += \"\\n  \" + hint;\n            }\n            catch (e) {\n                if (!context.logger || context.logger?.constructor?.name === \"NoOpLogger\") {\n                    console.warn(hint);\n                }\n                else {\n                    context.logger?.warn?.(hint);\n                }\n            }\n            if (typeof error.$responseBodyText !== \"undefined\") {\n                if (error.$response) {\n                    error.$response.body = error.$responseBodyText;\n                }\n            }\n            try {\n                if (_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_0__.HttpResponse.isInstance(response)) {\n                    const { headers = {} } = response;\n                    const headerEntries = Object.entries(headers);\n                    error.$metadata = {\n                        httpStatusCode: response.statusCode,\n                        requestId: findHeader(/^x-[\\w-]+-request-?id$/, headerEntries),\n                        extendedRequestId: findHeader(/^x-[\\w-]+-id-2$/, headerEntries),\n                        cfId: findHeader(/^x-[\\w-]+-cf-id$/, headerEntries),\n                    };\n                }\n            }\n            catch (e) {\n            }\n        }\n        throw error;\n    }\n};\nconst findHeader = (pattern, headers) => {\n    return (headers.find(([k]) => {\n        return k.match(pattern);\n    }) || [void 0, void 1])[1];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/deserializerMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserializerMiddleware: () => (/* reexport safe */ _deserializerMiddleware__WEBPACK_IMPORTED_MODULE_0__.deserializerMiddleware),\n/* harmony export */   deserializerMiddlewareOption: () => (/* reexport safe */ _serdePlugin__WEBPACK_IMPORTED_MODULE_1__.deserializerMiddlewareOption),\n/* harmony export */   getSerdePlugin: () => (/* reexport safe */ _serdePlugin__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin),\n/* harmony export */   serializerMiddleware: () => (/* reexport safe */ _serializerMiddleware__WEBPACK_IMPORTED_MODULE_2__.serializerMiddleware),\n/* harmony export */   serializerMiddlewareOption: () => (/* reexport safe */ _serdePlugin__WEBPACK_IMPORTED_MODULE_1__.serializerMiddlewareOption)\n/* harmony export */ });\n/* harmony import */ var _deserializerMiddleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deserializerMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/deserializerMiddleware.js\");\n/* harmony import */ var _serdePlugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serdePlugin */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/serdePlugin.js\");\n/* harmony import */ var _serializerMiddleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./serializerMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/serializerMiddleware.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLXNlcmRlQDQuMC44L25vZGVfbW9kdWxlcy9Ac21pdGh5L21pZGRsZXdhcmUtc2VyZGUvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF5QztBQUNYO0FBQ1MiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K21pZGRsZXdhcmUtc2VyZGVANC4wLjgvbm9kZV9tb2R1bGVzL0BzbWl0aHkvbWlkZGxld2FyZS1zZXJkZS9kaXN0LWVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2Rlc2VyaWFsaXplck1pZGRsZXdhcmVcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NlcmRlUGx1Z2luXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zZXJpYWxpemVyTWlkZGxld2FyZVwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/serdePlugin.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/serdePlugin.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deserializerMiddlewareOption: () => (/* binding */ deserializerMiddlewareOption),\n/* harmony export */   getSerdePlugin: () => (/* binding */ getSerdePlugin),\n/* harmony export */   serializerMiddlewareOption: () => (/* binding */ serializerMiddlewareOption)\n/* harmony export */ });\n/* harmony import */ var _deserializerMiddleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deserializerMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/deserializerMiddleware.js\");\n/* harmony import */ var _serializerMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./serializerMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/serializerMiddleware.js\");\n\n\nconst deserializerMiddlewareOption = {\n    name: \"deserializerMiddleware\",\n    step: \"deserialize\",\n    tags: [\"DESERIALIZER\"],\n    override: true,\n};\nconst serializerMiddlewareOption = {\n    name: \"serializerMiddleware\",\n    step: \"serialize\",\n    tags: [\"SERIALIZER\"],\n    override: true,\n};\nfunction getSerdePlugin(config, serializer, deserializer) {\n    return {\n        applyToStack: (commandStack) => {\n            commandStack.add((0,_deserializerMiddleware__WEBPACK_IMPORTED_MODULE_0__.deserializerMiddleware)(config, deserializer), deserializerMiddlewareOption);\n            commandStack.add((0,_serializerMiddleware__WEBPACK_IMPORTED_MODULE_1__.serializerMiddleware)(config, serializer), serializerMiddlewareOption);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLXNlcmRlQDQuMC44L25vZGVfbW9kdWxlcy9Ac21pdGh5L21pZGRsZXdhcmUtc2VyZGUvZGlzdC1lcy9zZXJkZVBsdWdpbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFrRTtBQUNKO0FBQ3ZEO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLDZCQUE2QiwrRUFBc0I7QUFDbkQsNkJBQTZCLDJFQUFvQjtBQUNqRCxTQUFTO0FBQ1Q7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrbWlkZGxld2FyZS1zZXJkZUA0LjAuOC9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLXNlcmRlL2Rpc3QtZXMvc2VyZGVQbHVnaW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVzZXJpYWxpemVyTWlkZGxld2FyZSB9IGZyb20gXCIuL2Rlc2VyaWFsaXplck1pZGRsZXdhcmVcIjtcbmltcG9ydCB7IHNlcmlhbGl6ZXJNaWRkbGV3YXJlIH0gZnJvbSBcIi4vc2VyaWFsaXplck1pZGRsZXdhcmVcIjtcbmV4cG9ydCBjb25zdCBkZXNlcmlhbGl6ZXJNaWRkbGV3YXJlT3B0aW9uID0ge1xuICAgIG5hbWU6IFwiZGVzZXJpYWxpemVyTWlkZGxld2FyZVwiLFxuICAgIHN0ZXA6IFwiZGVzZXJpYWxpemVcIixcbiAgICB0YWdzOiBbXCJERVNFUklBTElaRVJcIl0sXG4gICAgb3ZlcnJpZGU6IHRydWUsXG59O1xuZXhwb3J0IGNvbnN0IHNlcmlhbGl6ZXJNaWRkbGV3YXJlT3B0aW9uID0ge1xuICAgIG5hbWU6IFwic2VyaWFsaXplck1pZGRsZXdhcmVcIixcbiAgICBzdGVwOiBcInNlcmlhbGl6ZVwiLFxuICAgIHRhZ3M6IFtcIlNFUklBTElaRVJcIl0sXG4gICAgb3ZlcnJpZGU6IHRydWUsXG59O1xuZXhwb3J0IGZ1bmN0aW9uIGdldFNlcmRlUGx1Z2luKGNvbmZpZywgc2VyaWFsaXplciwgZGVzZXJpYWxpemVyKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgYXBwbHlUb1N0YWNrOiAoY29tbWFuZFN0YWNrKSA9PiB7XG4gICAgICAgICAgICBjb21tYW5kU3RhY2suYWRkKGRlc2VyaWFsaXplck1pZGRsZXdhcmUoY29uZmlnLCBkZXNlcmlhbGl6ZXIpLCBkZXNlcmlhbGl6ZXJNaWRkbGV3YXJlT3B0aW9uKTtcbiAgICAgICAgICAgIGNvbW1hbmRTdGFjay5hZGQoc2VyaWFsaXplck1pZGRsZXdhcmUoY29uZmlnLCBzZXJpYWxpemVyKSwgc2VyaWFsaXplck1pZGRsZXdhcmVPcHRpb24pO1xuICAgICAgICB9LFxuICAgIH07XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/serdePlugin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/serializerMiddleware.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/serializerMiddleware.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   serializerMiddleware: () => (/* binding */ serializerMiddleware)\n/* harmony export */ });\nconst serializerMiddleware = (options, serializer) => (next, context) => async (args) => {\n    const endpointConfig = options;\n    const endpoint = context.endpointV2?.url && endpointConfig.urlParser\n        ? async () => endpointConfig.urlParser(context.endpointV2.url)\n        : endpointConfig.endpoint;\n    if (!endpoint) {\n        throw new Error(\"No valid endpoint provider available.\");\n    }\n    const request = await serializer(args.input, { ...options, endpoint });\n    return next({\n        ...args,\n        request,\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLXNlcmRlQDQuMC44L25vZGVfbW9kdWxlcy9Ac21pdGh5L21pZGRsZXdhcmUtc2VyZGUvZGlzdC1lcy9zZXJpYWxpemVyTWlkZGxld2FyZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1EQUFtRCxzQkFBc0I7QUFDekU7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLXNlcmRlQDQuMC44L25vZGVfbW9kdWxlcy9Ac21pdGh5L21pZGRsZXdhcmUtc2VyZGUvZGlzdC1lcy9zZXJpYWxpemVyTWlkZGxld2FyZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3Qgc2VyaWFsaXplck1pZGRsZXdhcmUgPSAob3B0aW9ucywgc2VyaWFsaXplcikgPT4gKG5leHQsIGNvbnRleHQpID0+IGFzeW5jIChhcmdzKSA9PiB7XG4gICAgY29uc3QgZW5kcG9pbnRDb25maWcgPSBvcHRpb25zO1xuICAgIGNvbnN0IGVuZHBvaW50ID0gY29udGV4dC5lbmRwb2ludFYyPy51cmwgJiYgZW5kcG9pbnRDb25maWcudXJsUGFyc2VyXG4gICAgICAgID8gYXN5bmMgKCkgPT4gZW5kcG9pbnRDb25maWcudXJsUGFyc2VyKGNvbnRleHQuZW5kcG9pbnRWMi51cmwpXG4gICAgICAgIDogZW5kcG9pbnRDb25maWcuZW5kcG9pbnQ7XG4gICAgaWYgKCFlbmRwb2ludCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJObyB2YWxpZCBlbmRwb2ludCBwcm92aWRlciBhdmFpbGFibGUuXCIpO1xuICAgIH1cbiAgICBjb25zdCByZXF1ZXN0ID0gYXdhaXQgc2VyaWFsaXplcihhcmdzLmlucHV0LCB7IC4uLm9wdGlvbnMsIGVuZHBvaW50IH0pO1xuICAgIHJldHVybiBuZXh0KHtcbiAgICAgICAgLi4uYXJncyxcbiAgICAgICAgcmVxdWVzdCxcbiAgICB9KTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/serializerMiddleware.js\n");

/***/ })

};
;