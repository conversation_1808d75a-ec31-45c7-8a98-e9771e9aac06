"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+util-endpoints@3.0.6";
exports.ids = ["vendor-chunks/@smithy+util-endpoints@3.0.6"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/cache/EndpointCache.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/cache/EndpointCache.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EndpointCache: () => (/* binding */ EndpointCache)\n/* harmony export */ });\nclass EndpointCache {\n    constructor({ size, params }) {\n        this.data = new Map();\n        this.parameters = [];\n        this.capacity = size ?? 50;\n        if (params) {\n            this.parameters = params;\n        }\n    }\n    get(endpointParams, resolver) {\n        const key = this.hash(endpointParams);\n        if (key === false) {\n            return resolver();\n        }\n        if (!this.data.has(key)) {\n            if (this.data.size > this.capacity + 10) {\n                const keys = this.data.keys();\n                let i = 0;\n                while (true) {\n                    const { value, done } = keys.next();\n                    this.data.delete(value);\n                    if (done || ++i > 10) {\n                        break;\n                    }\n                }\n            }\n            this.data.set(key, resolver());\n        }\n        return this.data.get(key);\n    }\n    size() {\n        return this.data.size;\n    }\n    hash(endpointParams) {\n        let buffer = \"\";\n        const { parameters } = this;\n        if (parameters.length === 0) {\n            return false;\n        }\n        for (const param of parameters) {\n            const val = String(endpointParams[param] ?? \"\");\n            if (val.includes(\"|;\")) {\n                return false;\n            }\n            buffer += val + \"|;\";\n        }\n        return buffer;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/cache/EndpointCache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/debugId.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/debugId.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   debugId: () => (/* binding */ debugId)\n/* harmony export */ });\nconst debugId = \"endpoints\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2RlYnVnL2RlYnVnSWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2RlYnVnL2RlYnVnSWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGRlYnVnSWQgPSBcImVuZHBvaW50c1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/debugId.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/index.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   debugId: () => (/* reexport safe */ _debugId__WEBPACK_IMPORTED_MODULE_0__.debugId),\n/* harmony export */   toDebugString: () => (/* reexport safe */ _toDebugString__WEBPACK_IMPORTED_MODULE_1__.toDebugString)\n/* harmony export */ });\n/* harmony import */ var _debugId__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./debugId */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/debugId.js\");\n/* harmony import */ var _toDebugString__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./toDebugString */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/toDebugString.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2RlYnVnL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEI7QUFDTSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1lbmRwb2ludHNAMy4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy9kZWJ1Zy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9kZWJ1Z0lkXCI7XG5leHBvcnQgKiBmcm9tIFwiLi90b0RlYnVnU3RyaW5nXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/toDebugString.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/toDebugString.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toDebugString: () => (/* binding */ toDebugString)\n/* harmony export */ });\nfunction toDebugString(input) {\n    if (typeof input !== \"object\" || input == null) {\n        return input;\n    }\n    if (\"ref\" in input) {\n        return `$${toDebugString(input.ref)}`;\n    }\n    if (\"fn\" in input) {\n        return `${input.fn}(${(input.argv || []).map(toDebugString).join(\", \")})`;\n    }\n    return JSON.stringify(input, null, 2);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2RlYnVnL3RvRGVidWdTdHJpbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIseUJBQXlCO0FBQzVDO0FBQ0E7QUFDQSxrQkFBa0IsU0FBUyxHQUFHLGlEQUFpRDtBQUMvRTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvZGVidWcvdG9EZWJ1Z1N0cmluZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gdG9EZWJ1Z1N0cmluZyhpbnB1dCkge1xuICAgIGlmICh0eXBlb2YgaW5wdXQgIT09IFwib2JqZWN0XCIgfHwgaW5wdXQgPT0gbnVsbCkge1xuICAgICAgICByZXR1cm4gaW5wdXQ7XG4gICAgfVxuICAgIGlmIChcInJlZlwiIGluIGlucHV0KSB7XG4gICAgICAgIHJldHVybiBgJCR7dG9EZWJ1Z1N0cmluZyhpbnB1dC5yZWYpfWA7XG4gICAgfVxuICAgIGlmIChcImZuXCIgaW4gaW5wdXQpIHtcbiAgICAgICAgcmV0dXJuIGAke2lucHV0LmZufSgkeyhpbnB1dC5hcmd2IHx8IFtdKS5tYXAodG9EZWJ1Z1N0cmluZykuam9pbihcIiwgXCIpfSlgO1xuICAgIH1cbiAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkoaW5wdXQsIG51bGwsIDIpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/toDebugString.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/index.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/index.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EndpointCache: () => (/* reexport safe */ _cache_EndpointCache__WEBPACK_IMPORTED_MODULE_0__.EndpointCache),\n/* harmony export */   EndpointError: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_5__.EndpointError),\n/* harmony export */   customEndpointFunctions: () => (/* reexport safe */ _utils_customEndpointFunctions__WEBPACK_IMPORTED_MODULE_3__.customEndpointFunctions),\n/* harmony export */   isIpAddress: () => (/* reexport safe */ _lib_isIpAddress__WEBPACK_IMPORTED_MODULE_1__.isIpAddress),\n/* harmony export */   isValidHostLabel: () => (/* reexport safe */ _lib_isValidHostLabel__WEBPACK_IMPORTED_MODULE_2__.isValidHostLabel),\n/* harmony export */   resolveEndpoint: () => (/* reexport safe */ _resolveEndpoint__WEBPACK_IMPORTED_MODULE_4__.resolveEndpoint)\n/* harmony export */ });\n/* harmony import */ var _cache_EndpointCache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cache/EndpointCache */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/cache/EndpointCache.js\");\n/* harmony import */ var _lib_isIpAddress__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/isIpAddress */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isIpAddress.js\");\n/* harmony import */ var _lib_isValidHostLabel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/isValidHostLabel */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isValidHostLabel.js\");\n/* harmony import */ var _utils_customEndpointFunctions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/customEndpointFunctions */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/customEndpointFunctions.js\");\n/* harmony import */ var _resolveEndpoint__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./resolveEndpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/resolveEndpoint.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFzQztBQUNKO0FBQ0s7QUFDUztBQUNkO0FBQ1YiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vY2FjaGUvRW5kcG9pbnRDYWNoZVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vbGliL2lzSXBBZGRyZXNzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9saWIvaXNWYWxpZEhvc3RMYWJlbFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vdXRpbHMvY3VzdG9tRW5kcG9pbnRGdW5jdGlvbnNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3Jlc29sdmVFbmRwb2ludFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHlwZXNcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/booleanEquals.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/booleanEquals.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   booleanEquals: () => (/* binding */ booleanEquals)\n/* harmony export */ });\nconst booleanEquals = (value1, value2) => value1 === value2;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9ib29sZWFuRXF1YWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1lbmRwb2ludHNAMy4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy9saWIvYm9vbGVhbkVxdWFscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgYm9vbGVhbkVxdWFscyA9ICh2YWx1ZTEsIHZhbHVlMikgPT4gdmFsdWUxID09PSB2YWx1ZTI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/booleanEquals.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/getAttr.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/getAttr.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAttr: () => (/* binding */ getAttr)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js\");\n/* harmony import */ var _getAttrPathList__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getAttrPathList */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/getAttrPathList.js\");\n\n\nconst getAttr = (value, path) => (0,_getAttrPathList__WEBPACK_IMPORTED_MODULE_1__.getAttrPathList)(path).reduce((acc, index) => {\n    if (typeof acc !== \"object\") {\n        throw new _types__WEBPACK_IMPORTED_MODULE_0__.EndpointError(`Index '${index}' in '${path}' not found in '${JSON.stringify(value)}'`);\n    }\n    else if (Array.isArray(acc)) {\n        return acc[parseInt(index)];\n    }\n    return acc[index];\n}, value);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9nZXRBdHRyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF5QztBQUNXO0FBQzdDLGlDQUFpQyxpRUFBZTtBQUN2RDtBQUNBLGtCQUFrQixpREFBYSxXQUFXLE1BQU0sUUFBUSxLQUFLLGtCQUFrQixzQkFBc0I7QUFDckc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvbGliL2dldEF0dHIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRW5kcG9pbnRFcnJvciB9IGZyb20gXCIuLi90eXBlc1wiO1xuaW1wb3J0IHsgZ2V0QXR0clBhdGhMaXN0IH0gZnJvbSBcIi4vZ2V0QXR0clBhdGhMaXN0XCI7XG5leHBvcnQgY29uc3QgZ2V0QXR0ciA9ICh2YWx1ZSwgcGF0aCkgPT4gZ2V0QXR0clBhdGhMaXN0KHBhdGgpLnJlZHVjZSgoYWNjLCBpbmRleCkgPT4ge1xuICAgIGlmICh0eXBlb2YgYWNjICE9PSBcIm9iamVjdFwiKSB7XG4gICAgICAgIHRocm93IG5ldyBFbmRwb2ludEVycm9yKGBJbmRleCAnJHtpbmRleH0nIGluICcke3BhdGh9JyBub3QgZm91bmQgaW4gJyR7SlNPTi5zdHJpbmdpZnkodmFsdWUpfSdgKTtcbiAgICB9XG4gICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheShhY2MpKSB7XG4gICAgICAgIHJldHVybiBhY2NbcGFyc2VJbnQoaW5kZXgpXTtcbiAgICB9XG4gICAgcmV0dXJuIGFjY1tpbmRleF07XG59LCB2YWx1ZSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/getAttr.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/getAttrPathList.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/getAttrPathList.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAttrPathList: () => (/* binding */ getAttrPathList)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js\");\n\nconst getAttrPathList = (path) => {\n    const parts = path.split(\".\");\n    const pathList = [];\n    for (const part of parts) {\n        const squareBracketIndex = part.indexOf(\"[\");\n        if (squareBracketIndex !== -1) {\n            if (part.indexOf(\"]\") !== part.length - 1) {\n                throw new _types__WEBPACK_IMPORTED_MODULE_0__.EndpointError(`Path: '${path}' does not end with ']'`);\n            }\n            const arrayIndex = part.slice(squareBracketIndex + 1, -1);\n            if (Number.isNaN(parseInt(arrayIndex))) {\n                throw new _types__WEBPACK_IMPORTED_MODULE_0__.EndpointError(`Invalid array index: '${arrayIndex}' in path: '${path}'`);\n            }\n            if (squareBracketIndex !== 0) {\n                pathList.push(part.slice(0, squareBracketIndex));\n            }\n            pathList.push(arrayIndex);\n        }\n        else {\n            pathList.push(part);\n        }\n    }\n    return pathList;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/getAttrPathList.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/index.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/index.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   booleanEquals: () => (/* reexport safe */ _booleanEquals__WEBPACK_IMPORTED_MODULE_0__.booleanEquals),\n/* harmony export */   getAttr: () => (/* reexport safe */ _getAttr__WEBPACK_IMPORTED_MODULE_1__.getAttr),\n/* harmony export */   isSet: () => (/* reexport safe */ _isSet__WEBPACK_IMPORTED_MODULE_2__.isSet),\n/* harmony export */   isValidHostLabel: () => (/* reexport safe */ _isValidHostLabel__WEBPACK_IMPORTED_MODULE_3__.isValidHostLabel),\n/* harmony export */   not: () => (/* reexport safe */ _not__WEBPACK_IMPORTED_MODULE_4__.not),\n/* harmony export */   parseURL: () => (/* reexport safe */ _parseURL__WEBPACK_IMPORTED_MODULE_5__.parseURL),\n/* harmony export */   stringEquals: () => (/* reexport safe */ _stringEquals__WEBPACK_IMPORTED_MODULE_6__.stringEquals),\n/* harmony export */   substring: () => (/* reexport safe */ _substring__WEBPACK_IMPORTED_MODULE_7__.substring),\n/* harmony export */   uriEncode: () => (/* reexport safe */ _uriEncode__WEBPACK_IMPORTED_MODULE_8__.uriEncode)\n/* harmony export */ });\n/* harmony import */ var _booleanEquals__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./booleanEquals */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/booleanEquals.js\");\n/* harmony import */ var _getAttr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getAttr */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/getAttr.js\");\n/* harmony import */ var _isSet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isSet */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isSet.js\");\n/* harmony import */ var _isValidHostLabel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./isValidHostLabel */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isValidHostLabel.js\");\n/* harmony import */ var _not__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./not */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/not.js\");\n/* harmony import */ var _parseURL__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./parseURL */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/parseURL.js\");\n/* harmony import */ var _stringEquals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./stringEquals */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/stringEquals.js\");\n/* harmony import */ var _substring__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./substring */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/substring.js\");\n/* harmony import */ var _uriEncode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./uriEncode */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/uriEncode.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBZ0M7QUFDTjtBQUNGO0FBQ1c7QUFDYjtBQUNLO0FBQ0k7QUFDSDtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9ib29sZWFuRXF1YWxzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9nZXRBdHRyXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9pc1NldFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vaXNWYWxpZEhvc3RMYWJlbFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vbm90XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9wYXJzZVVSTFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vc3RyaW5nRXF1YWxzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9zdWJzdHJpbmdcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3VyaUVuY29kZVwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isIpAddress.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isIpAddress.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isIpAddress: () => (/* binding */ isIpAddress)\n/* harmony export */ });\nconst IP_V4_REGEX = new RegExp(`^(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}$`);\nconst isIpAddress = (value) => IP_V4_REGEX.test(value) || (value.startsWith(\"[\") && value.endsWith(\"]\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9pc0lwQWRkcmVzcy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsNkhBQTZILEVBQUU7QUFDeEgiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvbGliL2lzSXBBZGRyZXNzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IElQX1Y0X1JFR0VYID0gbmV3IFJlZ0V4cChgXig/OjI1WzAtNV18MlswLTRdXFxcXGR8MVxcXFxkXFxcXGR8WzEtOV1cXFxcZHxcXFxcZCkoPzpcXFxcLig/OjI1WzAtNV18MlswLTRdXFxcXGR8MVxcXFxkXFxcXGR8WzEtOV1cXFxcZHxcXFxcZCkpezN9JGApO1xuZXhwb3J0IGNvbnN0IGlzSXBBZGRyZXNzID0gKHZhbHVlKSA9PiBJUF9WNF9SRUdFWC50ZXN0KHZhbHVlKSB8fCAodmFsdWUuc3RhcnRzV2l0aChcIltcIikgJiYgdmFsdWUuZW5kc1dpdGgoXCJdXCIpKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isIpAddress.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isSet.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isSet.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isSet: () => (/* binding */ isSet)\n/* harmony export */ });\nconst isSet = (value) => value != null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9pc1NldC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8iLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvbGliL2lzU2V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBpc1NldCA9ICh2YWx1ZSkgPT4gdmFsdWUgIT0gbnVsbDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isSet.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isValidHostLabel.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isValidHostLabel.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isValidHostLabel: () => (/* binding */ isValidHostLabel)\n/* harmony export */ });\nconst VALID_HOST_LABEL_REGEX = new RegExp(`^(?!.*-$)(?!-)[a-zA-Z0-9-]{1,63}$`);\nconst isValidHostLabel = (value, allowSubDomains = false) => {\n    if (!allowSubDomains) {\n        return VALID_HOST_LABEL_REGEX.test(value);\n    }\n    const labels = value.split(\".\");\n    for (const label of labels) {\n        if (!isValidHostLabel(label)) {\n            return false;\n        }\n    }\n    return true;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9pc1ZhbGlkSG9zdExhYmVsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxzRUFBc0UsS0FBSztBQUNwRTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvbGliL2lzVmFsaWRIb3N0TGFiZWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgVkFMSURfSE9TVF9MQUJFTF9SRUdFWCA9IG5ldyBSZWdFeHAoYF4oPyEuKi0kKSg/IS0pW2EtekEtWjAtOS1dezEsNjN9JGApO1xuZXhwb3J0IGNvbnN0IGlzVmFsaWRIb3N0TGFiZWwgPSAodmFsdWUsIGFsbG93U3ViRG9tYWlucyA9IGZhbHNlKSA9PiB7XG4gICAgaWYgKCFhbGxvd1N1YkRvbWFpbnMpIHtcbiAgICAgICAgcmV0dXJuIFZBTElEX0hPU1RfTEFCRUxfUkVHRVgudGVzdCh2YWx1ZSk7XG4gICAgfVxuICAgIGNvbnN0IGxhYmVscyA9IHZhbHVlLnNwbGl0KFwiLlwiKTtcbiAgICBmb3IgKGNvbnN0IGxhYmVsIG9mIGxhYmVscykge1xuICAgICAgICBpZiAoIWlzVmFsaWRIb3N0TGFiZWwobGFiZWwpKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isValidHostLabel.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/not.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/not.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   not: () => (/* binding */ not)\n/* harmony export */ });\nconst not = (value) => !value;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9ub3QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9ub3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IG5vdCA9ICh2YWx1ZSkgPT4gIXZhbHVlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/not.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/parseURL.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/parseURL.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseURL: () => (/* binding */ parseURL)\n/* harmony export */ });\n/* harmony import */ var _smithy_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/types */ \"(rsc)/./node_modules/.pnpm/@smithy+types@4.3.1/node_modules/@smithy/types/dist-es/index.js\");\n/* harmony import */ var _isIpAddress__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./isIpAddress */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/isIpAddress.js\");\n\n\nconst DEFAULT_PORTS = {\n    [_smithy_types__WEBPACK_IMPORTED_MODULE_0__.EndpointURLScheme.HTTP]: 80,\n    [_smithy_types__WEBPACK_IMPORTED_MODULE_0__.EndpointURLScheme.HTTPS]: 443,\n};\nconst parseURL = (value) => {\n    const whatwgURL = (() => {\n        try {\n            if (value instanceof URL) {\n                return value;\n            }\n            if (typeof value === \"object\" && \"hostname\" in value) {\n                const { hostname, port, protocol = \"\", path = \"\", query = {} } = value;\n                const url = new URL(`${protocol}//${hostname}${port ? `:${port}` : \"\"}${path}`);\n                url.search = Object.entries(query)\n                    .map(([k, v]) => `${k}=${v}`)\n                    .join(\"&\");\n                return url;\n            }\n            return new URL(value);\n        }\n        catch (error) {\n            return null;\n        }\n    })();\n    if (!whatwgURL) {\n        console.error(`Unable to parse ${JSON.stringify(value)} as a whatwg URL.`);\n        return null;\n    }\n    const urlString = whatwgURL.href;\n    const { host, hostname, pathname, protocol, search } = whatwgURL;\n    if (search) {\n        return null;\n    }\n    const scheme = protocol.slice(0, -1);\n    if (!Object.values(_smithy_types__WEBPACK_IMPORTED_MODULE_0__.EndpointURLScheme).includes(scheme)) {\n        return null;\n    }\n    const isIp = (0,_isIpAddress__WEBPACK_IMPORTED_MODULE_1__.isIpAddress)(hostname);\n    const inputContainsDefaultPort = urlString.includes(`${host}:${DEFAULT_PORTS[scheme]}`) ||\n        (typeof value === \"string\" && value.includes(`${host}:${DEFAULT_PORTS[scheme]}`));\n    const authority = `${host}${inputContainsDefaultPort ? `:${DEFAULT_PORTS[scheme]}` : ``}`;\n    return {\n        scheme,\n        authority,\n        path: pathname,\n        normalizedPath: pathname.endsWith(\"/\") ? pathname : `${pathname}/`,\n        isIp,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/parseURL.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/stringEquals.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/stringEquals.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stringEquals: () => (/* binding */ stringEquals)\n/* harmony export */ });\nconst stringEquals = (value1, value2) => value1 === value2;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9zdHJpbmdFcXVhbHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9zdHJpbmdFcXVhbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHN0cmluZ0VxdWFscyA9ICh2YWx1ZTEsIHZhbHVlMikgPT4gdmFsdWUxID09PSB2YWx1ZTI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/stringEquals.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/substring.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/substring.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   substring: () => (/* binding */ substring)\n/* harmony export */ });\nconst substring = (input, start, stop, reverse) => {\n    if (start >= stop || input.length < stop) {\n        return null;\n    }\n    if (!reverse) {\n        return input.substring(start, stop);\n    }\n    return input.substring(input.length - stop, input.length - start);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi9zdWJzdHJpbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1lbmRwb2ludHNAMy4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy9saWIvc3Vic3RyaW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBzdWJzdHJpbmcgPSAoaW5wdXQsIHN0YXJ0LCBzdG9wLCByZXZlcnNlKSA9PiB7XG4gICAgaWYgKHN0YXJ0ID49IHN0b3AgfHwgaW5wdXQubGVuZ3RoIDwgc3RvcCkge1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgaWYgKCFyZXZlcnNlKSB7XG4gICAgICAgIHJldHVybiBpbnB1dC5zdWJzdHJpbmcoc3RhcnQsIHN0b3ApO1xuICAgIH1cbiAgICByZXR1cm4gaW5wdXQuc3Vic3RyaW5nKGlucHV0Lmxlbmd0aCAtIHN0b3AsIGlucHV0Lmxlbmd0aCAtIHN0YXJ0KTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/substring.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/uriEncode.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/uriEncode.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uriEncode: () => (/* binding */ uriEncode)\n/* harmony export */ });\nconst uriEncode = (value) => encodeURIComponent(value).replace(/[!*'()]/g, (c) => `%${c.charCodeAt(0).toString(16).toUpperCase()}`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL2xpYi91cmlFbmNvZGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLHNGQUFzRiwyQ0FBMkMiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvbGliL3VyaUVuY29kZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgdXJpRW5jb2RlID0gKHZhbHVlKSA9PiBlbmNvZGVVUklDb21wb25lbnQodmFsdWUpLnJlcGxhY2UoL1shKicoKV0vZywgKGMpID0+IGAlJHtjLmNoYXJDb2RlQXQoMCkudG9TdHJpbmcoMTYpLnRvVXBwZXJDYXNlKCl9YCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/uriEncode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/resolveEndpoint.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/resolveEndpoint.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveEndpoint: () => (/* binding */ resolveEndpoint)\n/* harmony export */ });\n/* harmony import */ var _debug__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./debug */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/index.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/index.js\");\n\n\n\nconst resolveEndpoint = (ruleSetObject, options) => {\n    const { endpointParams, logger } = options;\n    const { parameters, rules } = ruleSetObject;\n    options.logger?.debug?.(`${_debug__WEBPACK_IMPORTED_MODULE_0__.debugId} Initial EndpointParams: ${(0,_debug__WEBPACK_IMPORTED_MODULE_0__.toDebugString)(endpointParams)}`);\n    const paramsWithDefault = Object.entries(parameters)\n        .filter(([, v]) => v.default != null)\n        .map(([k, v]) => [k, v.default]);\n    if (paramsWithDefault.length > 0) {\n        for (const [paramKey, paramDefaultValue] of paramsWithDefault) {\n            endpointParams[paramKey] = endpointParams[paramKey] ?? paramDefaultValue;\n        }\n    }\n    const requiredParams = Object.entries(parameters)\n        .filter(([, v]) => v.required)\n        .map(([k]) => k);\n    for (const requiredParam of requiredParams) {\n        if (endpointParams[requiredParam] == null) {\n            throw new _types__WEBPACK_IMPORTED_MODULE_1__.EndpointError(`Missing required parameter: '${requiredParam}'`);\n        }\n    }\n    const endpoint = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.evaluateRules)(rules, { endpointParams, logger, referenceRecord: {} });\n    options.logger?.debug?.(`${_debug__WEBPACK_IMPORTED_MODULE_0__.debugId} Resolved endpoint: ${(0,_debug__WEBPACK_IMPORTED_MODULE_0__.toDebugString)(endpoint)}`);\n    return endpoint;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/resolveEndpoint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/EndpointError.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/EndpointError.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EndpointError: () => (/* binding */ EndpointError)\n/* harmony export */ });\nclass EndpointError extends Error {\n    constructor(message) {\n        super(message);\n        this.name = \"EndpointError\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3R5cGVzL0VuZHBvaW50RXJyb3IuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1lbmRwb2ludHNAMy4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9FbmRwb2ludEVycm9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBFbmRwb2ludEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UpIHtcbiAgICAgICAgc3VwZXIobWVzc2FnZSk7XG4gICAgICAgIHRoaXMubmFtZSA9IFwiRW5kcG9pbnRFcnJvclwiO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/EndpointError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/EndpointFunctions.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/EndpointFunctions.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3R5cGVzL0VuZHBvaW50RnVuY3Rpb25zLmpzIiwibWFwcGluZ3MiOiI7QUFBVSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1lbmRwb2ludHNAMy4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9FbmRwb2ludEZ1bmN0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge307XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/EndpointFunctions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/EndpointRuleObject.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/EndpointRuleObject.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3R5cGVzL0VuZHBvaW50UnVsZU9iamVjdC5qcyIsIm1hcHBpbmdzIjoiO0FBQVUiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvdHlwZXMvRW5kcG9pbnRSdWxlT2JqZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/EndpointRuleObject.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/ErrorRuleObject.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/ErrorRuleObject.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3R5cGVzL0Vycm9yUnVsZU9iamVjdC5qcyIsIm1hcHBpbmdzIjoiO0FBQVUiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvdHlwZXMvRXJyb3JSdWxlT2JqZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/ErrorRuleObject.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/RuleSetObject.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/RuleSetObject.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3R5cGVzL1J1bGVTZXRPYmplY3QuanMiLCJtYXBwaW5ncyI6IjtBQUFVIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3R5cGVzL1J1bGVTZXRPYmplY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/RuleSetObject.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/TreeRuleObject.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/TreeRuleObject.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3R5cGVzL1RyZWVSdWxlT2JqZWN0LmpzIiwibWFwcGluZ3MiOiI7QUFBVSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1lbmRwb2ludHNAMy4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy90eXBlcy9UcmVlUnVsZU9iamVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge307XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/TreeRuleObject.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EndpointError: () => (/* reexport safe */ _EndpointError__WEBPACK_IMPORTED_MODULE_0__.EndpointError)\n/* harmony export */ });\n/* harmony import */ var _EndpointError__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EndpointError */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/EndpointError.js\");\n/* harmony import */ var _EndpointFunctions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EndpointFunctions */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/EndpointFunctions.js\");\n/* harmony import */ var _EndpointRuleObject__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EndpointRuleObject */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/EndpointRuleObject.js\");\n/* harmony import */ var _ErrorRuleObject__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ErrorRuleObject */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/ErrorRuleObject.js\");\n/* harmony import */ var _RuleSetObject__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RuleSetObject */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/RuleSetObject.js\");\n/* harmony import */ var _TreeRuleObject__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TreeRuleObject */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/TreeRuleObject.js\");\n/* harmony import */ var _shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./shared */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/shared.js\");\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3R5cGVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQWdDO0FBQ0k7QUFDQztBQUNIO0FBQ0Y7QUFDQztBQUNSIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3R5cGVzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL0VuZHBvaW50RXJyb3JcIjtcbmV4cG9ydCAqIGZyb20gXCIuL0VuZHBvaW50RnVuY3Rpb25zXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9FbmRwb2ludFJ1bGVPYmplY3RcIjtcbmV4cG9ydCAqIGZyb20gXCIuL0Vycm9yUnVsZU9iamVjdFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vUnVsZVNldE9iamVjdFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vVHJlZVJ1bGVPYmplY3RcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3NoYXJlZFwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/shared.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/shared.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3R5cGVzL3NoYXJlZC5qcyIsIm1hcHBpbmdzIjoiO0FBQVUiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvdHlwZXMvc2hhcmVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/shared.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/callFunction.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/callFunction.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   callFunction: () => (/* binding */ callFunction)\n/* harmony export */ });\n/* harmony import */ var _customEndpointFunctions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./customEndpointFunctions */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/customEndpointFunctions.js\");\n/* harmony import */ var _endpointFunctions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./endpointFunctions */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/endpointFunctions.js\");\n/* harmony import */ var _evaluateExpression__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./evaluateExpression */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateExpression.js\");\n\n\n\nconst callFunction = ({ fn, argv }, options) => {\n    const evaluatedArgs = argv.map((arg) => [\"boolean\", \"number\"].includes(typeof arg) ? arg : (0,_evaluateExpression__WEBPACK_IMPORTED_MODULE_2__.evaluateExpression)(arg, \"arg\", options));\n    const fnSegments = fn.split(\".\");\n    if (fnSegments[0] in _customEndpointFunctions__WEBPACK_IMPORTED_MODULE_0__.customEndpointFunctions && fnSegments[1] != null) {\n        return _customEndpointFunctions__WEBPACK_IMPORTED_MODULE_0__.customEndpointFunctions[fnSegments[0]][fnSegments[1]](...evaluatedArgs);\n    }\n    return _endpointFunctions__WEBPACK_IMPORTED_MODULE_1__.endpointFunctions[fn](...evaluatedArgs);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2NhbGxGdW5jdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQW9FO0FBQ1o7QUFDRTtBQUNuRCx3QkFBd0IsVUFBVTtBQUN6QywrRkFBK0YsdUVBQWtCO0FBQ2pIO0FBQ0EseUJBQXlCLDZFQUF1QjtBQUNoRCxlQUFlLDZFQUF1QjtBQUN0QztBQUNBLFdBQVcsaUVBQWlCO0FBQzVCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2NhbGxGdW5jdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjdXN0b21FbmRwb2ludEZ1bmN0aW9ucyB9IGZyb20gXCIuL2N1c3RvbUVuZHBvaW50RnVuY3Rpb25zXCI7XG5pbXBvcnQgeyBlbmRwb2ludEZ1bmN0aW9ucyB9IGZyb20gXCIuL2VuZHBvaW50RnVuY3Rpb25zXCI7XG5pbXBvcnQgeyBldmFsdWF0ZUV4cHJlc3Npb24gfSBmcm9tIFwiLi9ldmFsdWF0ZUV4cHJlc3Npb25cIjtcbmV4cG9ydCBjb25zdCBjYWxsRnVuY3Rpb24gPSAoeyBmbiwgYXJndiB9LCBvcHRpb25zKSA9PiB7XG4gICAgY29uc3QgZXZhbHVhdGVkQXJncyA9IGFyZ3YubWFwKChhcmcpID0+IFtcImJvb2xlYW5cIiwgXCJudW1iZXJcIl0uaW5jbHVkZXModHlwZW9mIGFyZykgPyBhcmcgOiBldmFsdWF0ZUV4cHJlc3Npb24oYXJnLCBcImFyZ1wiLCBvcHRpb25zKSk7XG4gICAgY29uc3QgZm5TZWdtZW50cyA9IGZuLnNwbGl0KFwiLlwiKTtcbiAgICBpZiAoZm5TZWdtZW50c1swXSBpbiBjdXN0b21FbmRwb2ludEZ1bmN0aW9ucyAmJiBmblNlZ21lbnRzWzFdICE9IG51bGwpIHtcbiAgICAgICAgcmV0dXJuIGN1c3RvbUVuZHBvaW50RnVuY3Rpb25zW2ZuU2VnbWVudHNbMF1dW2ZuU2VnbWVudHNbMV1dKC4uLmV2YWx1YXRlZEFyZ3MpO1xuICAgIH1cbiAgICByZXR1cm4gZW5kcG9pbnRGdW5jdGlvbnNbZm5dKC4uLmV2YWx1YXRlZEFyZ3MpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/callFunction.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/customEndpointFunctions.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/customEndpointFunctions.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   customEndpointFunctions: () => (/* binding */ customEndpointFunctions)\n/* harmony export */ });\nconst customEndpointFunctions = {};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2N1c3RvbUVuZHBvaW50RnVuY3Rpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1lbmRwb2ludHNAMy4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy91dGlscy9jdXN0b21FbmRwb2ludEZ1bmN0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgY3VzdG9tRW5kcG9pbnRGdW5jdGlvbnMgPSB7fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/customEndpointFunctions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/endpointFunctions.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/endpointFunctions.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   endpointFunctions: () => (/* binding */ endpointFunctions)\n/* harmony export */ });\n/* harmony import */ var _lib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/index.js\");\n\nconst endpointFunctions = {\n    booleanEquals: _lib__WEBPACK_IMPORTED_MODULE_0__.booleanEquals,\n    getAttr: _lib__WEBPACK_IMPORTED_MODULE_0__.getAttr,\n    isSet: _lib__WEBPACK_IMPORTED_MODULE_0__.isSet,\n    isValidHostLabel: _lib__WEBPACK_IMPORTED_MODULE_0__.isValidHostLabel,\n    not: _lib__WEBPACK_IMPORTED_MODULE_0__.not,\n    parseURL: _lib__WEBPACK_IMPORTED_MODULE_0__.parseURL,\n    stringEquals: _lib__WEBPACK_IMPORTED_MODULE_0__.stringEquals,\n    substring: _lib__WEBPACK_IMPORTED_MODULE_0__.substring,\n    uriEncode: _lib__WEBPACK_IMPORTED_MODULE_0__.uriEncode,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2VuZHBvaW50RnVuY3Rpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTZIO0FBQ3RIO0FBQ1AsaUJBQWlCO0FBQ2pCLFdBQVc7QUFDWCxTQUFTO0FBQ1Qsb0JBQW9CO0FBQ3BCLE9BQU87QUFDUCxZQUFZO0FBQ1osZ0JBQWdCO0FBQ2hCLGFBQWE7QUFDYixhQUFhO0FBQ2IiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvdXRpbHMvZW5kcG9pbnRGdW5jdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYm9vbGVhbkVxdWFscywgZ2V0QXR0ciwgaXNTZXQsIGlzVmFsaWRIb3N0TGFiZWwsIG5vdCwgcGFyc2VVUkwsIHN0cmluZ0VxdWFscywgc3Vic3RyaW5nLCB1cmlFbmNvZGUsIH0gZnJvbSBcIi4uL2xpYlwiO1xuZXhwb3J0IGNvbnN0IGVuZHBvaW50RnVuY3Rpb25zID0ge1xuICAgIGJvb2xlYW5FcXVhbHMsXG4gICAgZ2V0QXR0cixcbiAgICBpc1NldCxcbiAgICBpc1ZhbGlkSG9zdExhYmVsLFxuICAgIG5vdCxcbiAgICBwYXJzZVVSTCxcbiAgICBzdHJpbmdFcXVhbHMsXG4gICAgc3Vic3RyaW5nLFxuICAgIHVyaUVuY29kZSxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/endpointFunctions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateCondition.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateCondition.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   evaluateCondition: () => (/* binding */ evaluateCondition)\n/* harmony export */ });\n/* harmony import */ var _debug__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../debug */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/index.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js\");\n/* harmony import */ var _callFunction__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./callFunction */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/callFunction.js\");\n\n\n\nconst evaluateCondition = ({ assign, ...fnArgs }, options) => {\n    if (assign && assign in options.referenceRecord) {\n        throw new _types__WEBPACK_IMPORTED_MODULE_1__.EndpointError(`'${assign}' is already defined in Reference Record.`);\n    }\n    const value = (0,_callFunction__WEBPACK_IMPORTED_MODULE_2__.callFunction)(fnArgs, options);\n    options.logger?.debug?.(`${_debug__WEBPACK_IMPORTED_MODULE_0__.debugId} evaluateCondition: ${(0,_debug__WEBPACK_IMPORTED_MODULE_0__.toDebugString)(fnArgs)} = ${(0,_debug__WEBPACK_IMPORTED_MODULE_0__.toDebugString)(value)}`);\n    return {\n        result: value === \"\" ? true : !!value,\n        ...(assign != null && { toAssign: { name: assign, value } }),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2V2YWx1YXRlQ29uZGl0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBa0Q7QUFDVDtBQUNLO0FBQ3ZDLDZCQUE2QixtQkFBbUI7QUFDdkQ7QUFDQSxrQkFBa0IsaURBQWEsS0FBSyxPQUFPO0FBQzNDO0FBQ0Esa0JBQWtCLDJEQUFZO0FBQzlCLCtCQUErQiwyQ0FBTyxFQUFFLHFCQUFxQixxREFBYSxVQUFVLElBQUkscURBQWEsUUFBUTtBQUM3RztBQUNBO0FBQ0EsZ0NBQWdDLFlBQVksdUJBQXVCO0FBQ25FO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvdXRpbHMvZXZhbHVhdGVDb25kaXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVidWdJZCwgdG9EZWJ1Z1N0cmluZyB9IGZyb20gXCIuLi9kZWJ1Z1wiO1xuaW1wb3J0IHsgRW5kcG9pbnRFcnJvciB9IGZyb20gXCIuLi90eXBlc1wiO1xuaW1wb3J0IHsgY2FsbEZ1bmN0aW9uIH0gZnJvbSBcIi4vY2FsbEZ1bmN0aW9uXCI7XG5leHBvcnQgY29uc3QgZXZhbHVhdGVDb25kaXRpb24gPSAoeyBhc3NpZ24sIC4uLmZuQXJncyB9LCBvcHRpb25zKSA9PiB7XG4gICAgaWYgKGFzc2lnbiAmJiBhc3NpZ24gaW4gb3B0aW9ucy5yZWZlcmVuY2VSZWNvcmQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVuZHBvaW50RXJyb3IoYCcke2Fzc2lnbn0nIGlzIGFscmVhZHkgZGVmaW5lZCBpbiBSZWZlcmVuY2UgUmVjb3JkLmApO1xuICAgIH1cbiAgICBjb25zdCB2YWx1ZSA9IGNhbGxGdW5jdGlvbihmbkFyZ3MsIG9wdGlvbnMpO1xuICAgIG9wdGlvbnMubG9nZ2VyPy5kZWJ1Zz8uKGAke2RlYnVnSWR9IGV2YWx1YXRlQ29uZGl0aW9uOiAke3RvRGVidWdTdHJpbmcoZm5BcmdzKX0gPSAke3RvRGVidWdTdHJpbmcodmFsdWUpfWApO1xuICAgIHJldHVybiB7XG4gICAgICAgIHJlc3VsdDogdmFsdWUgPT09IFwiXCIgPyB0cnVlIDogISF2YWx1ZSxcbiAgICAgICAgLi4uKGFzc2lnbiAhPSBudWxsICYmIHsgdG9Bc3NpZ246IHsgbmFtZTogYXNzaWduLCB2YWx1ZSB9IH0pLFxuICAgIH07XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateCondition.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateConditions.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateConditions.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   evaluateConditions: () => (/* binding */ evaluateConditions)\n/* harmony export */ });\n/* harmony import */ var _debug__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../debug */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/index.js\");\n/* harmony import */ var _evaluateCondition__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./evaluateCondition */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateCondition.js\");\n\n\nconst evaluateConditions = (conditions = [], options) => {\n    const conditionsReferenceRecord = {};\n    for (const condition of conditions) {\n        const { result, toAssign } = (0,_evaluateCondition__WEBPACK_IMPORTED_MODULE_1__.evaluateCondition)(condition, {\n            ...options,\n            referenceRecord: {\n                ...options.referenceRecord,\n                ...conditionsReferenceRecord,\n            },\n        });\n        if (!result) {\n            return { result };\n        }\n        if (toAssign) {\n            conditionsReferenceRecord[toAssign.name] = toAssign.value;\n            options.logger?.debug?.(`${_debug__WEBPACK_IMPORTED_MODULE_0__.debugId} assign: ${toAssign.name} := ${(0,_debug__WEBPACK_IMPORTED_MODULE_0__.toDebugString)(toAssign.value)}`);\n        }\n    }\n    return { result: true, referenceRecord: conditionsReferenceRecord };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateConditions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateEndpointRule.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateEndpointRule.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   evaluateEndpointRule: () => (/* binding */ evaluateEndpointRule)\n/* harmony export */ });\n/* harmony import */ var _debug__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../debug */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/debug/index.js\");\n/* harmony import */ var _evaluateConditions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./evaluateConditions */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateConditions.js\");\n/* harmony import */ var _getEndpointHeaders__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getEndpointHeaders */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointHeaders.js\");\n/* harmony import */ var _getEndpointProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getEndpointProperties */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointProperties.js\");\n/* harmony import */ var _getEndpointUrl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./getEndpointUrl */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointUrl.js\");\n\n\n\n\n\nconst evaluateEndpointRule = (endpointRule, options) => {\n    const { conditions, endpoint } = endpointRule;\n    const { result, referenceRecord } = (0,_evaluateConditions__WEBPACK_IMPORTED_MODULE_1__.evaluateConditions)(conditions, options);\n    if (!result) {\n        return;\n    }\n    const endpointRuleOptions = {\n        ...options,\n        referenceRecord: { ...options.referenceRecord, ...referenceRecord },\n    };\n    const { url, properties, headers } = endpoint;\n    options.logger?.debug?.(`${_debug__WEBPACK_IMPORTED_MODULE_0__.debugId} Resolving endpoint from template: ${(0,_debug__WEBPACK_IMPORTED_MODULE_0__.toDebugString)(endpoint)}`);\n    return {\n        ...(headers != undefined && {\n            headers: (0,_getEndpointHeaders__WEBPACK_IMPORTED_MODULE_2__.getEndpointHeaders)(headers, endpointRuleOptions),\n        }),\n        ...(properties != undefined && {\n            properties: (0,_getEndpointProperties__WEBPACK_IMPORTED_MODULE_3__.getEndpointProperties)(properties, endpointRuleOptions),\n        }),\n        url: (0,_getEndpointUrl__WEBPACK_IMPORTED_MODULE_4__.getEndpointUrl)(url, endpointRuleOptions),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2V2YWx1YXRlRW5kcG9pbnRSdWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFrRDtBQUNRO0FBQ0E7QUFDTTtBQUNkO0FBQzNDO0FBQ1AsWUFBWSx1QkFBdUI7QUFDbkMsWUFBWSwwQkFBMEIsRUFBRSx1RUFBa0I7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixnREFBZ0Q7QUFDM0U7QUFDQSxZQUFZLDJCQUEyQjtBQUN2QywrQkFBK0IsMkNBQU8sRUFBRSxvQ0FBb0MscURBQWEsV0FBVztBQUNwRztBQUNBO0FBQ0EscUJBQXFCLHVFQUFrQjtBQUN2QyxTQUFTO0FBQ1Q7QUFDQSx3QkFBd0IsNkVBQXFCO0FBQzdDLFNBQVM7QUFDVCxhQUFhLCtEQUFjO0FBQzNCO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvdXRpbHMvZXZhbHVhdGVFbmRwb2ludFJ1bGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVidWdJZCwgdG9EZWJ1Z1N0cmluZyB9IGZyb20gXCIuLi9kZWJ1Z1wiO1xuaW1wb3J0IHsgZXZhbHVhdGVDb25kaXRpb25zIH0gZnJvbSBcIi4vZXZhbHVhdGVDb25kaXRpb25zXCI7XG5pbXBvcnQgeyBnZXRFbmRwb2ludEhlYWRlcnMgfSBmcm9tIFwiLi9nZXRFbmRwb2ludEhlYWRlcnNcIjtcbmltcG9ydCB7IGdldEVuZHBvaW50UHJvcGVydGllcyB9IGZyb20gXCIuL2dldEVuZHBvaW50UHJvcGVydGllc1wiO1xuaW1wb3J0IHsgZ2V0RW5kcG9pbnRVcmwgfSBmcm9tIFwiLi9nZXRFbmRwb2ludFVybFwiO1xuZXhwb3J0IGNvbnN0IGV2YWx1YXRlRW5kcG9pbnRSdWxlID0gKGVuZHBvaW50UnVsZSwgb3B0aW9ucykgPT4ge1xuICAgIGNvbnN0IHsgY29uZGl0aW9ucywgZW5kcG9pbnQgfSA9IGVuZHBvaW50UnVsZTtcbiAgICBjb25zdCB7IHJlc3VsdCwgcmVmZXJlbmNlUmVjb3JkIH0gPSBldmFsdWF0ZUNvbmRpdGlvbnMoY29uZGl0aW9ucywgb3B0aW9ucyk7XG4gICAgaWYgKCFyZXN1bHQpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBjb25zdCBlbmRwb2ludFJ1bGVPcHRpb25zID0ge1xuICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICByZWZlcmVuY2VSZWNvcmQ6IHsgLi4ub3B0aW9ucy5yZWZlcmVuY2VSZWNvcmQsIC4uLnJlZmVyZW5jZVJlY29yZCB9LFxuICAgIH07XG4gICAgY29uc3QgeyB1cmwsIHByb3BlcnRpZXMsIGhlYWRlcnMgfSA9IGVuZHBvaW50O1xuICAgIG9wdGlvbnMubG9nZ2VyPy5kZWJ1Zz8uKGAke2RlYnVnSWR9IFJlc29sdmluZyBlbmRwb2ludCBmcm9tIHRlbXBsYXRlOiAke3RvRGVidWdTdHJpbmcoZW5kcG9pbnQpfWApO1xuICAgIHJldHVybiB7XG4gICAgICAgIC4uLihoZWFkZXJzICE9IHVuZGVmaW5lZCAmJiB7XG4gICAgICAgICAgICBoZWFkZXJzOiBnZXRFbmRwb2ludEhlYWRlcnMoaGVhZGVycywgZW5kcG9pbnRSdWxlT3B0aW9ucyksXG4gICAgICAgIH0pLFxuICAgICAgICAuLi4ocHJvcGVydGllcyAhPSB1bmRlZmluZWQgJiYge1xuICAgICAgICAgICAgcHJvcGVydGllczogZ2V0RW5kcG9pbnRQcm9wZXJ0aWVzKHByb3BlcnRpZXMsIGVuZHBvaW50UnVsZU9wdGlvbnMpLFxuICAgICAgICB9KSxcbiAgICAgICAgdXJsOiBnZXRFbmRwb2ludFVybCh1cmwsIGVuZHBvaW50UnVsZU9wdGlvbnMpLFxuICAgIH07XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateEndpointRule.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateErrorRule.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateErrorRule.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   evaluateErrorRule: () => (/* binding */ evaluateErrorRule)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js\");\n/* harmony import */ var _evaluateConditions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./evaluateConditions */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateConditions.js\");\n/* harmony import */ var _evaluateExpression__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./evaluateExpression */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateExpression.js\");\n\n\n\nconst evaluateErrorRule = (errorRule, options) => {\n    const { conditions, error } = errorRule;\n    const { result, referenceRecord } = (0,_evaluateConditions__WEBPACK_IMPORTED_MODULE_1__.evaluateConditions)(conditions, options);\n    if (!result) {\n        return;\n    }\n    throw new _types__WEBPACK_IMPORTED_MODULE_0__.EndpointError((0,_evaluateExpression__WEBPACK_IMPORTED_MODULE_2__.evaluateExpression)(error, \"Error\", {\n        ...options,\n        referenceRecord: { ...options.referenceRecord, ...referenceRecord },\n    }));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2V2YWx1YXRlRXJyb3JSdWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBeUM7QUFDaUI7QUFDQTtBQUNuRDtBQUNQLFlBQVksb0JBQW9CO0FBQ2hDLFlBQVksMEJBQTBCLEVBQUUsdUVBQWtCO0FBQzFEO0FBQ0E7QUFDQTtBQUNBLGNBQWMsaURBQWEsQ0FBQyx1RUFBa0I7QUFDOUM7QUFDQSwyQkFBMkIsZ0RBQWdEO0FBQzNFLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1lbmRwb2ludHNAMy4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy91dGlscy9ldmFsdWF0ZUVycm9yUnVsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFbmRwb2ludEVycm9yIH0gZnJvbSBcIi4uL3R5cGVzXCI7XG5pbXBvcnQgeyBldmFsdWF0ZUNvbmRpdGlvbnMgfSBmcm9tIFwiLi9ldmFsdWF0ZUNvbmRpdGlvbnNcIjtcbmltcG9ydCB7IGV2YWx1YXRlRXhwcmVzc2lvbiB9IGZyb20gXCIuL2V2YWx1YXRlRXhwcmVzc2lvblwiO1xuZXhwb3J0IGNvbnN0IGV2YWx1YXRlRXJyb3JSdWxlID0gKGVycm9yUnVsZSwgb3B0aW9ucykgPT4ge1xuICAgIGNvbnN0IHsgY29uZGl0aW9ucywgZXJyb3IgfSA9IGVycm9yUnVsZTtcbiAgICBjb25zdCB7IHJlc3VsdCwgcmVmZXJlbmNlUmVjb3JkIH0gPSBldmFsdWF0ZUNvbmRpdGlvbnMoY29uZGl0aW9ucywgb3B0aW9ucyk7XG4gICAgaWYgKCFyZXN1bHQpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICB0aHJvdyBuZXcgRW5kcG9pbnRFcnJvcihldmFsdWF0ZUV4cHJlc3Npb24oZXJyb3IsIFwiRXJyb3JcIiwge1xuICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICByZWZlcmVuY2VSZWNvcmQ6IHsgLi4ub3B0aW9ucy5yZWZlcmVuY2VSZWNvcmQsIC4uLnJlZmVyZW5jZVJlY29yZCB9LFxuICAgIH0pKTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateErrorRule.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateExpression.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateExpression.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   evaluateExpression: () => (/* binding */ evaluateExpression)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js\");\n/* harmony import */ var _callFunction__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./callFunction */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/callFunction.js\");\n/* harmony import */ var _evaluateTemplate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./evaluateTemplate */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateTemplate.js\");\n/* harmony import */ var _getReferenceValue__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getReferenceValue */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getReferenceValue.js\");\n\n\n\n\nconst evaluateExpression = (obj, keyName, options) => {\n    if (typeof obj === \"string\") {\n        return (0,_evaluateTemplate__WEBPACK_IMPORTED_MODULE_2__.evaluateTemplate)(obj, options);\n    }\n    else if (obj[\"fn\"]) {\n        return (0,_callFunction__WEBPACK_IMPORTED_MODULE_1__.callFunction)(obj, options);\n    }\n    else if (obj[\"ref\"]) {\n        return (0,_getReferenceValue__WEBPACK_IMPORTED_MODULE_3__.getReferenceValue)(obj, options);\n    }\n    throw new _types__WEBPACK_IMPORTED_MODULE_0__.EndpointError(`'${keyName}': ${String(obj)} is not a string, function or reference.`);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2V2YWx1YXRlRXhwcmVzc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF5QztBQUNLO0FBQ1E7QUFDRTtBQUNqRDtBQUNQO0FBQ0EsZUFBZSxtRUFBZ0I7QUFDL0I7QUFDQTtBQUNBLGVBQWUsMkRBQVk7QUFDM0I7QUFDQTtBQUNBLGVBQWUscUVBQWlCO0FBQ2hDO0FBQ0EsY0FBYyxpREFBYSxLQUFLLFFBQVEsS0FBSyxhQUFhO0FBQzFEIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2V2YWx1YXRlRXhwcmVzc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFbmRwb2ludEVycm9yIH0gZnJvbSBcIi4uL3R5cGVzXCI7XG5pbXBvcnQgeyBjYWxsRnVuY3Rpb24gfSBmcm9tIFwiLi9jYWxsRnVuY3Rpb25cIjtcbmltcG9ydCB7IGV2YWx1YXRlVGVtcGxhdGUgfSBmcm9tIFwiLi9ldmFsdWF0ZVRlbXBsYXRlXCI7XG5pbXBvcnQgeyBnZXRSZWZlcmVuY2VWYWx1ZSB9IGZyb20gXCIuL2dldFJlZmVyZW5jZVZhbHVlXCI7XG5leHBvcnQgY29uc3QgZXZhbHVhdGVFeHByZXNzaW9uID0gKG9iaiwga2V5TmFtZSwgb3B0aW9ucykgPT4ge1xuICAgIGlmICh0eXBlb2Ygb2JqID09PSBcInN0cmluZ1wiKSB7XG4gICAgICAgIHJldHVybiBldmFsdWF0ZVRlbXBsYXRlKG9iaiwgb3B0aW9ucyk7XG4gICAgfVxuICAgIGVsc2UgaWYgKG9ialtcImZuXCJdKSB7XG4gICAgICAgIHJldHVybiBjYWxsRnVuY3Rpb24ob2JqLCBvcHRpb25zKTtcbiAgICB9XG4gICAgZWxzZSBpZiAob2JqW1wicmVmXCJdKSB7XG4gICAgICAgIHJldHVybiBnZXRSZWZlcmVuY2VWYWx1ZShvYmosIG9wdGlvbnMpO1xuICAgIH1cbiAgICB0aHJvdyBuZXcgRW5kcG9pbnRFcnJvcihgJyR7a2V5TmFtZX0nOiAke1N0cmluZyhvYmopfSBpcyBub3QgYSBzdHJpbmcsIGZ1bmN0aW9uIG9yIHJlZmVyZW5jZS5gKTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateExpression.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateRules.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateRules.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   evaluateRules: () => (/* binding */ evaluateRules)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js\");\n/* harmony import */ var _evaluateEndpointRule__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./evaluateEndpointRule */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateEndpointRule.js\");\n/* harmony import */ var _evaluateErrorRule__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./evaluateErrorRule */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateErrorRule.js\");\n/* harmony import */ var _evaluateTreeRule__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./evaluateTreeRule */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateTreeRule.js\");\n\n\n\n\nconst evaluateRules = (rules, options) => {\n    for (const rule of rules) {\n        if (rule.type === \"endpoint\") {\n            const endpointOrUndefined = (0,_evaluateEndpointRule__WEBPACK_IMPORTED_MODULE_1__.evaluateEndpointRule)(rule, options);\n            if (endpointOrUndefined) {\n                return endpointOrUndefined;\n            }\n        }\n        else if (rule.type === \"error\") {\n            (0,_evaluateErrorRule__WEBPACK_IMPORTED_MODULE_2__.evaluateErrorRule)(rule, options);\n        }\n        else if (rule.type === \"tree\") {\n            const endpointOrUndefined = (0,_evaluateTreeRule__WEBPACK_IMPORTED_MODULE_3__.evaluateTreeRule)(rule, options);\n            if (endpointOrUndefined) {\n                return endpointOrUndefined;\n            }\n        }\n        else {\n            throw new _types__WEBPACK_IMPORTED_MODULE_0__.EndpointError(`Unknown endpoint rule: ${rule}`);\n        }\n    }\n    throw new _types__WEBPACK_IMPORTED_MODULE_0__.EndpointError(`Rules evaluation failed`);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateRules.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateTemplate.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateTemplate.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   evaluateTemplate: () => (/* binding */ evaluateTemplate)\n/* harmony export */ });\n/* harmony import */ var _lib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/lib/index.js\");\n\nconst evaluateTemplate = (template, options) => {\n    const evaluatedTemplateArr = [];\n    const templateContext = {\n        ...options.endpointParams,\n        ...options.referenceRecord,\n    };\n    let currentIndex = 0;\n    while (currentIndex < template.length) {\n        const openingBraceIndex = template.indexOf(\"{\", currentIndex);\n        if (openingBraceIndex === -1) {\n            evaluatedTemplateArr.push(template.slice(currentIndex));\n            break;\n        }\n        evaluatedTemplateArr.push(template.slice(currentIndex, openingBraceIndex));\n        const closingBraceIndex = template.indexOf(\"}\", openingBraceIndex);\n        if (closingBraceIndex === -1) {\n            evaluatedTemplateArr.push(template.slice(openingBraceIndex));\n            break;\n        }\n        if (template[openingBraceIndex + 1] === \"{\" && template[closingBraceIndex + 1] === \"}\") {\n            evaluatedTemplateArr.push(template.slice(openingBraceIndex + 1, closingBraceIndex));\n            currentIndex = closingBraceIndex + 2;\n        }\n        const parameterName = template.substring(openingBraceIndex + 1, closingBraceIndex);\n        if (parameterName.includes(\"#\")) {\n            const [refName, attrName] = parameterName.split(\"#\");\n            evaluatedTemplateArr.push((0,_lib__WEBPACK_IMPORTED_MODULE_0__.getAttr)(templateContext[refName], attrName));\n        }\n        else {\n            evaluatedTemplateArr.push(templateContext[parameterName]);\n        }\n        currentIndex = closingBraceIndex + 1;\n    }\n    return evaluatedTemplateArr.join(\"\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateTemplate.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateTreeRule.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateTreeRule.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   evaluateTreeRule: () => (/* binding */ evaluateTreeRule)\n/* harmony export */ });\n/* harmony import */ var _evaluateConditions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./evaluateConditions */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateConditions.js\");\n/* harmony import */ var _evaluateRules__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./evaluateRules */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateRules.js\");\n\n\nconst evaluateTreeRule = (treeRule, options) => {\n    const { conditions, rules } = treeRule;\n    const { result, referenceRecord } = (0,_evaluateConditions__WEBPACK_IMPORTED_MODULE_0__.evaluateConditions)(conditions, options);\n    if (!result) {\n        return;\n    }\n    return (0,_evaluateRules__WEBPACK_IMPORTED_MODULE_1__.evaluateRules)(rules, {\n        ...options,\n        referenceRecord: { ...options.referenceRecord, ...referenceRecord },\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2V2YWx1YXRlVHJlZVJ1bGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBEO0FBQ1Y7QUFDekM7QUFDUCxZQUFZLG9CQUFvQjtBQUNoQyxZQUFZLDBCQUEwQixFQUFFLHVFQUFrQjtBQUMxRDtBQUNBO0FBQ0E7QUFDQSxXQUFXLDZEQUFhO0FBQ3hCO0FBQ0EsMkJBQTJCLGdEQUFnRDtBQUMzRSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvdXRpbHMvZXZhbHVhdGVUcmVlUnVsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBldmFsdWF0ZUNvbmRpdGlvbnMgfSBmcm9tIFwiLi9ldmFsdWF0ZUNvbmRpdGlvbnNcIjtcbmltcG9ydCB7IGV2YWx1YXRlUnVsZXMgfSBmcm9tIFwiLi9ldmFsdWF0ZVJ1bGVzXCI7XG5leHBvcnQgY29uc3QgZXZhbHVhdGVUcmVlUnVsZSA9ICh0cmVlUnVsZSwgb3B0aW9ucykgPT4ge1xuICAgIGNvbnN0IHsgY29uZGl0aW9ucywgcnVsZXMgfSA9IHRyZWVSdWxlO1xuICAgIGNvbnN0IHsgcmVzdWx0LCByZWZlcmVuY2VSZWNvcmQgfSA9IGV2YWx1YXRlQ29uZGl0aW9ucyhjb25kaXRpb25zLCBvcHRpb25zKTtcbiAgICBpZiAoIXJlc3VsdCkge1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIHJldHVybiBldmFsdWF0ZVJ1bGVzKHJ1bGVzLCB7XG4gICAgICAgIC4uLm9wdGlvbnMsXG4gICAgICAgIHJlZmVyZW5jZVJlY29yZDogeyAuLi5vcHRpb25zLnJlZmVyZW5jZVJlY29yZCwgLi4ucmVmZXJlbmNlUmVjb3JkIH0sXG4gICAgfSk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateTreeRule.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointHeaders.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointHeaders.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEndpointHeaders: () => (/* binding */ getEndpointHeaders)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js\");\n/* harmony import */ var _evaluateExpression__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./evaluateExpression */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateExpression.js\");\n\n\nconst getEndpointHeaders = (headers, options) => Object.entries(headers).reduce((acc, [headerKey, headerVal]) => ({\n    ...acc,\n    [headerKey]: headerVal.map((headerValEntry) => {\n        const processedExpr = (0,_evaluateExpression__WEBPACK_IMPORTED_MODULE_1__.evaluateExpression)(headerValEntry, \"Header value entry\", options);\n        if (typeof processedExpr !== \"string\") {\n            throw new _types__WEBPACK_IMPORTED_MODULE_0__.EndpointError(`Header '${headerKey}' value '${processedExpr}' is not a string`);\n        }\n        return processedExpr;\n    }),\n}), {});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2dldEVuZHBvaW50SGVhZGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBeUM7QUFDaUI7QUFDbkQ7QUFDUDtBQUNBO0FBQ0EsOEJBQThCLHVFQUFrQjtBQUNoRDtBQUNBLHNCQUFzQixpREFBYSxZQUFZLFVBQVUsV0FBVyxjQUFjO0FBQ2xGO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsQ0FBQyxLQUFLIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2dldEVuZHBvaW50SGVhZGVycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFbmRwb2ludEVycm9yIH0gZnJvbSBcIi4uL3R5cGVzXCI7XG5pbXBvcnQgeyBldmFsdWF0ZUV4cHJlc3Npb24gfSBmcm9tIFwiLi9ldmFsdWF0ZUV4cHJlc3Npb25cIjtcbmV4cG9ydCBjb25zdCBnZXRFbmRwb2ludEhlYWRlcnMgPSAoaGVhZGVycywgb3B0aW9ucykgPT4gT2JqZWN0LmVudHJpZXMoaGVhZGVycykucmVkdWNlKChhY2MsIFtoZWFkZXJLZXksIGhlYWRlclZhbF0pID0+ICh7XG4gICAgLi4uYWNjLFxuICAgIFtoZWFkZXJLZXldOiBoZWFkZXJWYWwubWFwKChoZWFkZXJWYWxFbnRyeSkgPT4ge1xuICAgICAgICBjb25zdCBwcm9jZXNzZWRFeHByID0gZXZhbHVhdGVFeHByZXNzaW9uKGhlYWRlclZhbEVudHJ5LCBcIkhlYWRlciB2YWx1ZSBlbnRyeVwiLCBvcHRpb25zKTtcbiAgICAgICAgaWYgKHR5cGVvZiBwcm9jZXNzZWRFeHByICE9PSBcInN0cmluZ1wiKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRW5kcG9pbnRFcnJvcihgSGVhZGVyICcke2hlYWRlcktleX0nIHZhbHVlICcke3Byb2Nlc3NlZEV4cHJ9JyBpcyBub3QgYSBzdHJpbmdgKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcHJvY2Vzc2VkRXhwcjtcbiAgICB9KSxcbn0pLCB7fSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointHeaders.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointProperties.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointProperties.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEndpointProperties: () => (/* binding */ getEndpointProperties)\n/* harmony export */ });\n/* harmony import */ var _getEndpointProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getEndpointProperty */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointProperty.js\");\n\nconst getEndpointProperties = (properties, options) => Object.entries(properties).reduce((acc, [propertyKey, propertyVal]) => ({\n    ...acc,\n    [propertyKey]: (0,_getEndpointProperty__WEBPACK_IMPORTED_MODULE_0__.getEndpointProperty)(propertyVal, options),\n}), {});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2dldEVuZHBvaW50UHJvcGVydGllcy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0RDtBQUNyRDtBQUNQO0FBQ0EsbUJBQW1CLHlFQUFtQjtBQUN0QyxDQUFDLEtBQUsiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvdXRpbHMvZ2V0RW5kcG9pbnRQcm9wZXJ0aWVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdldEVuZHBvaW50UHJvcGVydHkgfSBmcm9tIFwiLi9nZXRFbmRwb2ludFByb3BlcnR5XCI7XG5leHBvcnQgY29uc3QgZ2V0RW5kcG9pbnRQcm9wZXJ0aWVzID0gKHByb3BlcnRpZXMsIG9wdGlvbnMpID0+IE9iamVjdC5lbnRyaWVzKHByb3BlcnRpZXMpLnJlZHVjZSgoYWNjLCBbcHJvcGVydHlLZXksIHByb3BlcnR5VmFsXSkgPT4gKHtcbiAgICAuLi5hY2MsXG4gICAgW3Byb3BlcnR5S2V5XTogZ2V0RW5kcG9pbnRQcm9wZXJ0eShwcm9wZXJ0eVZhbCwgb3B0aW9ucyksXG59KSwge30pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointProperties.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointProperty.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointProperty.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEndpointProperty: () => (/* binding */ getEndpointProperty)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js\");\n/* harmony import */ var _evaluateTemplate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./evaluateTemplate */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateTemplate.js\");\n/* harmony import */ var _getEndpointProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getEndpointProperties */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointProperties.js\");\n\n\n\nconst getEndpointProperty = (property, options) => {\n    if (Array.isArray(property)) {\n        return property.map((propertyEntry) => getEndpointProperty(propertyEntry, options));\n    }\n    switch (typeof property) {\n        case \"string\":\n            return (0,_evaluateTemplate__WEBPACK_IMPORTED_MODULE_1__.evaluateTemplate)(property, options);\n        case \"object\":\n            if (property === null) {\n                throw new _types__WEBPACK_IMPORTED_MODULE_0__.EndpointError(`Unexpected endpoint property: ${property}`);\n            }\n            return (0,_getEndpointProperties__WEBPACK_IMPORTED_MODULE_2__.getEndpointProperties)(property, options);\n        case \"boolean\":\n            return property;\n        default:\n            throw new _types__WEBPACK_IMPORTED_MODULE_0__.EndpointError(`Unexpected endpoint property type: ${typeof property}`);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointProperty.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointUrl.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointUrl.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEndpointUrl: () => (/* binding */ getEndpointUrl)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/types/index.js\");\n/* harmony import */ var _evaluateExpression__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./evaluateExpression */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateExpression.js\");\n\n\nconst getEndpointUrl = (endpointUrl, options) => {\n    const expression = (0,_evaluateExpression__WEBPACK_IMPORTED_MODULE_1__.evaluateExpression)(endpointUrl, \"Endpoint URL\", options);\n    if (typeof expression === \"string\") {\n        try {\n            return new URL(expression);\n        }\n        catch (error) {\n            console.error(`Failed to construct URL with ${expression}`, error);\n            throw error;\n        }\n    }\n    throw new _types__WEBPACK_IMPORTED_MODULE_0__.EndpointError(`Endpoint URL must be a string, got ${typeof expression}`);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2dldEVuZHBvaW50VXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF5QztBQUNpQjtBQUNuRDtBQUNQLHVCQUF1Qix1RUFBa0I7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBEQUEwRCxXQUFXO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBLGNBQWMsaURBQWEsdUNBQXVDLGtCQUFrQjtBQUNwRiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1lbmRwb2ludHNAMy4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy91dGlscy9nZXRFbmRwb2ludFVybC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFbmRwb2ludEVycm9yIH0gZnJvbSBcIi4uL3R5cGVzXCI7XG5pbXBvcnQgeyBldmFsdWF0ZUV4cHJlc3Npb24gfSBmcm9tIFwiLi9ldmFsdWF0ZUV4cHJlc3Npb25cIjtcbmV4cG9ydCBjb25zdCBnZXRFbmRwb2ludFVybCA9IChlbmRwb2ludFVybCwgb3B0aW9ucykgPT4ge1xuICAgIGNvbnN0IGV4cHJlc3Npb24gPSBldmFsdWF0ZUV4cHJlc3Npb24oZW5kcG9pbnRVcmwsIFwiRW5kcG9pbnQgVVJMXCIsIG9wdGlvbnMpO1xuICAgIGlmICh0eXBlb2YgZXhwcmVzc2lvbiA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgcmV0dXJuIG5ldyBVUkwoZXhwcmVzc2lvbik7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKGBGYWlsZWQgdG8gY29uc3RydWN0IFVSTCB3aXRoICR7ZXhwcmVzc2lvbn1gLCBlcnJvcik7XG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgIH1cbiAgICB0aHJvdyBuZXcgRW5kcG9pbnRFcnJvcihgRW5kcG9pbnQgVVJMIG11c3QgYmUgYSBzdHJpbmcsIGdvdCAke3R5cGVvZiBleHByZXNzaW9ufWApO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getEndpointUrl.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getReferenceValue.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getReferenceValue.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getReferenceValue: () => (/* binding */ getReferenceValue)\n/* harmony export */ });\nconst getReferenceValue = ({ ref }, options) => {\n    const referenceRecord = {\n        ...options.endpointParams,\n        ...options.referenceRecord,\n    };\n    return referenceRecord[ref];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2dldFJlZmVyZW5jZVZhbHVlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyw2QkFBNkIsS0FBSztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K3V0aWwtZW5kcG9pbnRzQDMuMC42L25vZGVfbW9kdWxlcy9Ac21pdGh5L3V0aWwtZW5kcG9pbnRzL2Rpc3QtZXMvdXRpbHMvZ2V0UmVmZXJlbmNlVmFsdWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGdldFJlZmVyZW5jZVZhbHVlID0gKHsgcmVmIH0sIG9wdGlvbnMpID0+IHtcbiAgICBjb25zdCByZWZlcmVuY2VSZWNvcmQgPSB7XG4gICAgICAgIC4uLm9wdGlvbnMuZW5kcG9pbnRQYXJhbXMsXG4gICAgICAgIC4uLm9wdGlvbnMucmVmZXJlbmNlUmVjb3JkLFxuICAgIH07XG4gICAgcmV0dXJuIHJlZmVyZW5jZVJlY29yZFtyZWZdO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/getReferenceValue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/index.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   customEndpointFunctions: () => (/* reexport safe */ _customEndpointFunctions__WEBPACK_IMPORTED_MODULE_0__.customEndpointFunctions),\n/* harmony export */   evaluateRules: () => (/* reexport safe */ _evaluateRules__WEBPACK_IMPORTED_MODULE_1__.evaluateRules)\n/* harmony export */ });\n/* harmony import */ var _customEndpointFunctions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./customEndpointFunctions */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/customEndpointFunctions.js\");\n/* harmony import */ var _evaluateRules__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./evaluateRules */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/evaluateRules.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSt1dGlsLWVuZHBvaW50c0AzLjAuNi9ub2RlX21vZHVsZXMvQHNtaXRoeS91dGlsLWVuZHBvaW50cy9kaXN0LWVzL3V0aWxzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEM7QUFDViIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrdXRpbC1lbmRwb2ludHNAMy4wLjYvbm9kZV9tb2R1bGVzL0BzbWl0aHkvdXRpbC1lbmRwb2ludHMvZGlzdC1lcy91dGlscy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9jdXN0b21FbmRwb2ludEZ1bmN0aW9uc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vZXZhbHVhdGVSdWxlc1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/utils/index.js\n");

/***/ })

};
;