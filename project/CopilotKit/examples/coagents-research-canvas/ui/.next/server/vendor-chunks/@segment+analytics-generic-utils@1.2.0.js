"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@segment+analytics-generic-utils@1.2.0";
exports.ids = ["vendor-chunks/@segment+analytics-generic-utils@1.2.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-generic-utils@1.2.0/node_modules/@segment/analytics-generic-utils/dist/esm/create-deferred/create-deferred.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-generic-utils@1.2.0/node_modules/@segment/analytics-generic-utils/dist/esm/create-deferred/create-deferred.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDeferred: () => (/* binding */ createDeferred)\n/* harmony export */ });\n/**\n * Return a promise that can be externally resolved\n */\nvar createDeferred = function () {\n    var resolve;\n    var reject;\n    var settled = false;\n    var promise = new Promise(function (_resolve, _reject) {\n        resolve = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            settled = true;\n            _resolve.apply(void 0, args);\n        };\n        reject = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            settled = true;\n            _reject.apply(void 0, args);\n        };\n    });\n    return {\n        resolve: resolve,\n        reject: reject,\n        promise: promise,\n        isSettled: function () { return settled; },\n    };\n};\n//# sourceMappingURL=create-deferred.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-generic-utils@1.2.0/node_modules/@segment/analytics-generic-utils/dist/esm/create-deferred/create-deferred.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-generic-utils@1.2.0/node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-generic-utils@1.2.0/node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Emitter: () => (/* binding */ Emitter)\n/* harmony export */ });\n/**\n * Event Emitter that takes the expected contract as a generic\n * @example\n * ```ts\n *  type Contract = {\n *    delivery_success: [DeliverySuccessResponse, Metrics],\n *    delivery_failure: [DeliveryError]\n * }\n *  new Emitter<Contract>()\n *  .on('delivery_success', (res, metrics) => ...)\n *  .on('delivery_failure', (err) => ...)\n * ```\n */\nvar Emitter = /** @class */ (function () {\n    function Emitter(options) {\n        var _a;\n        this.callbacks = {};\n        this.warned = false;\n        this.maxListeners = (_a = options === null || options === void 0 ? void 0 : options.maxListeners) !== null && _a !== void 0 ? _a : 10;\n    }\n    Emitter.prototype.warnIfPossibleMemoryLeak = function (event) {\n        if (this.warned) {\n            return;\n        }\n        if (this.maxListeners &&\n            this.callbacks[event].length > this.maxListeners) {\n            console.warn(\"Event Emitter: Possible memory leak detected; \".concat(String(event), \" has exceeded \").concat(this.maxListeners, \" listeners.\"));\n            this.warned = true;\n        }\n    };\n    Emitter.prototype.on = function (event, callback) {\n        if (!this.callbacks[event]) {\n            this.callbacks[event] = [callback];\n        }\n        else {\n            this.callbacks[event].push(callback);\n            this.warnIfPossibleMemoryLeak(event);\n        }\n        return this;\n    };\n    Emitter.prototype.once = function (event, callback) {\n        var _this = this;\n        var on = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            _this.off(event, on);\n            callback.apply(_this, args);\n        };\n        this.on(event, on);\n        return this;\n    };\n    Emitter.prototype.off = function (event, callback) {\n        var _a;\n        var fns = (_a = this.callbacks[event]) !== null && _a !== void 0 ? _a : [];\n        var without = fns.filter(function (fn) { return fn !== callback; });\n        this.callbacks[event] = without;\n        return this;\n    };\n    Emitter.prototype.emit = function (event) {\n        var _this = this;\n        var _a;\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        var callbacks = (_a = this.callbacks[event]) !== null && _a !== void 0 ? _a : [];\n        callbacks.forEach(function (callback) {\n            callback.apply(_this, args);\n        });\n        return this;\n    };\n    return Emitter;\n}());\n\n//# sourceMappingURL=emitter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-generic-utils@1.2.0/node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js\n");

/***/ })

};
;