"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-core-commonmark@1.1.0";
exports.ids = ["vendor-chunks/micromark-core-commonmark@1.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/attention.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/attention.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attention: () => (/* binding */ attention)\n/* harmony export */ });\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/.pnpm/micromark-util-chunked@1.1.0/node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-classify-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-classify-character@1.1.0/node_modules/micromark-util-classify-character/dev/index.js\");\n/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ \"(ssr)/./node_modules/.pnpm/micromark-util-resolve-all@1.1.0/node_modules/micromark-util-resolve-all/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').Point} Point\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst attention = {\n  name: 'attention',\n  tokenize: tokenizeAttention,\n  resolveAll: resolveAllAttention\n}\n\n/**\n * Take all events and resolve attention to emphasis or strong.\n *\n * @type {Resolver}\n */\nfunction resolveAllAttention(events, context) {\n  let index = -1\n  /** @type {number} */\n  let open\n  /** @type {Token} */\n  let group\n  /** @type {Token} */\n  let text\n  /** @type {Token} */\n  let openingSequence\n  /** @type {Token} */\n  let closingSequence\n  /** @type {number} */\n  let use\n  /** @type {Array<Event>} */\n  let nextEvents\n  /** @type {number} */\n  let offset\n\n  // Walk through all events.\n  //\n  // Note: performance of this is fine on an mb of normal markdown, but it’s\n  // a bottleneck for malicious stuff.\n  while (++index < events.length) {\n    // Find a token that can close.\n    if (\n      events[index][0] === 'enter' &&\n      events[index][1].type === 'attentionSequence' &&\n      events[index][1]._close\n    ) {\n      open = index\n\n      // Now walk back to find an opener.\n      while (open--) {\n        // Find a token that can open the closer.\n        if (\n          events[open][0] === 'exit' &&\n          events[open][1].type === 'attentionSequence' &&\n          events[open][1]._open &&\n          // If the markers are the same:\n          context.sliceSerialize(events[open][1]).charCodeAt(0) ===\n            context.sliceSerialize(events[index][1]).charCodeAt(0)\n        ) {\n          // If the opening can close or the closing can open,\n          // and the close size *is not* a multiple of three,\n          // but the sum of the opening and closing size *is* multiple of three,\n          // then don’t match.\n          if (\n            (events[open][1]._close || events[index][1]._open) &&\n            (events[index][1].end.offset - events[index][1].start.offset) % 3 &&\n            !(\n              (events[open][1].end.offset -\n                events[open][1].start.offset +\n                events[index][1].end.offset -\n                events[index][1].start.offset) %\n              3\n            )\n          ) {\n            continue\n          }\n\n          // Number of markers to use from the sequence.\n          use =\n            events[open][1].end.offset - events[open][1].start.offset > 1 &&\n            events[index][1].end.offset - events[index][1].start.offset > 1\n              ? 2\n              : 1\n\n          const start = Object.assign({}, events[open][1].end)\n          const end = Object.assign({}, events[index][1].start)\n          movePoint(start, -use)\n          movePoint(end, use)\n\n          openingSequence = {\n            type: use > 1 ? micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.strongSequence : micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.emphasisSequence,\n            start,\n            end: Object.assign({}, events[open][1].end)\n          }\n          closingSequence = {\n            type: use > 1 ? micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.strongSequence : micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.emphasisSequence,\n            start: Object.assign({}, events[index][1].start),\n            end\n          }\n          text = {\n            type: use > 1 ? micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.strongText : micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.emphasisText,\n            start: Object.assign({}, events[open][1].end),\n            end: Object.assign({}, events[index][1].start)\n          }\n          group = {\n            type: use > 1 ? micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.strong : micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.emphasis,\n            start: Object.assign({}, openingSequence.start),\n            end: Object.assign({}, closingSequence.end)\n          }\n\n          events[open][1].end = Object.assign({}, openingSequence.start)\n          events[index][1].start = Object.assign({}, closingSequence.end)\n\n          nextEvents = []\n\n          // If there are more markers in the opening, add them before.\n          if (events[open][1].end.offset - events[open][1].start.offset) {\n            nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(nextEvents, [\n              ['enter', events[open][1], context],\n              ['exit', events[open][1], context]\n            ])\n          }\n\n          // Opening.\n          nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(nextEvents, [\n            ['enter', group, context],\n            ['enter', openingSequence, context],\n            ['exit', openingSequence, context],\n            ['enter', text, context]\n          ])\n\n          // Always populated by defaults.\n          ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n            context.parser.constructs.insideSpan.null,\n            'expected `insideSpan` to be populated'\n          )\n\n          // Between.\n          nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(\n            nextEvents,\n            (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(\n              context.parser.constructs.insideSpan.null,\n              events.slice(open + 1, index),\n              context\n            )\n          )\n\n          // Closing.\n          nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(nextEvents, [\n            ['exit', text, context],\n            ['enter', closingSequence, context],\n            ['exit', closingSequence, context],\n            ['exit', group, context]\n          ])\n\n          // If there are more markers in the closing, add them after.\n          if (events[index][1].end.offset - events[index][1].start.offset) {\n            offset = 2\n            nextEvents = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(nextEvents, [\n              ['enter', events[index][1], context],\n              ['exit', events[index][1], context]\n            ])\n          } else {\n            offset = 0\n          }\n\n          (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(events, open - 1, index - open + 3, nextEvents)\n\n          index = open + nextEvents.length - offset - 2\n          break\n        }\n      }\n    }\n  }\n\n  // Remove remaining sequences.\n  index = -1\n\n  while (++index < events.length) {\n    if (events[index][1].type === 'attentionSequence') {\n      events[index][1].type = 'data'\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeAttention(effects, ok) {\n  const attentionMarkers = this.parser.constructs.attentionMarkers.null\n  const previous = this.previous\n  const before = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_4__.classifyCharacter)(previous)\n\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Before a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_5__.codes.asterisk || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_5__.codes.underscore,\n      'expected asterisk or underscore'\n    )\n    marker = code\n    effects.enter('attentionSequence')\n    return inside(code)\n  }\n\n  /**\n   * In a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return inside\n    }\n\n    const token = effects.exit('attentionSequence')\n\n    // To do: next major: move this to resolver, just like `markdown-rs`.\n    const after = (0,micromark_util_classify_character__WEBPACK_IMPORTED_MODULE_4__.classifyCharacter)(code)\n\n    // Always populated by defaults.\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(attentionMarkers, 'expected `attentionMarkers` to be populated')\n\n    const open =\n      !after ||\n      (after === micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_6__.constants.characterGroupPunctuation && before) ||\n      attentionMarkers.includes(code)\n    const close =\n      !before ||\n      (before === micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_6__.constants.characterGroupPunctuation && after) ||\n      attentionMarkers.includes(previous)\n\n    token._open = Boolean(\n      marker === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_5__.codes.asterisk ? open : open && (before || !close)\n    )\n    token._close = Boolean(\n      marker === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_5__.codes.asterisk ? close : close && (after || !open)\n    )\n    return ok(code)\n  }\n}\n\n/**\n * Move a point a bit.\n *\n * Note: `move` only works inside lines! It’s not possible to move past other\n * chunks (replacement characters, tabs, or line endings).\n *\n * @param {Point} point\n * @param {number} offset\n * @returns {void}\n */\nfunction movePoint(point, offset) {\n  point.column += offset\n  point.offset += offset\n  point._bufferIndex += offset\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/attention.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/autolink.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/autolink.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autolink: () => (/* binding */ autolink)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst autolink = {name: 'autolink', tokenize: tokenizeAutolink}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeAutolink(effects, ok, nok) {\n  let size = 0\n\n  return start\n\n  /**\n   * Start of an autolink.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *      ^\n   * > | a<<EMAIL>>b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan, 'expected `<`')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.autolink)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.autolinkProtocol)\n    return open\n  }\n\n  /**\n   * After `<`, at protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *       ^\n   * > | a<<EMAIL>>b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return schemeOrEmailAtext\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * At second byte of protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeOrEmailAtext(code) {\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.plusSign ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dash ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dot ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)\n    ) {\n      // Count the previous alphabetical from `open` too.\n      size = 1\n      return schemeInsideOrEmailAtext(code)\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * In ambiguous protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeInsideOrEmailAtext(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n      effects.consume(code)\n      size = 0\n      return urlInside\n    }\n\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.plusSign ||\n        code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dash ||\n        code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dot ||\n        (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)) &&\n      size++ < micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_4__.constants.autolinkSchemeSizeMax\n    ) {\n      effects.consume(code)\n      return schemeInsideOrEmailAtext\n    }\n\n    size = 0\n    return emailAtext(code)\n  }\n\n  /**\n   * After protocol, in URL.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function urlInside(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.autolinkProtocol)\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.autolink)\n      return ok\n    }\n\n    // ASCII control, space, or `<`.\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.space ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiControl)(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return urlInside\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtext(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.atSign) {\n      effects.consume(code)\n      return emailAtSignOrDot\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAtext)(code)) {\n      effects.consume(code)\n      return emailAtext\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In label, after at-sign or dot.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                 ^       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtSignOrDot(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code) ? emailLabel(code) : nok(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are allowed.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailLabel(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dot) {\n      effects.consume(code)\n      size = 0\n      return emailAtSignOrDot\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      // Exit, then change the token type.\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.autolinkProtocol).type = micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.autolinkEmail\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.autolinkMarker)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.autolink)\n      return ok\n    }\n\n    return emailValue(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are *not* allowed.\n   *\n   * Though, this is also used in `emailLabel` to parse other values.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailValue(code) {\n    // ASCII alphanumeric or `-`.\n    if (\n      (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dash || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)) &&\n      size++ < micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_4__.constants.autolinkDomainSizeMax\n    ) {\n      const next = code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dash ? emailValue : emailLabel\n      effects.consume(code)\n      return next\n    }\n\n    return nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/autolink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/blank-line.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/blank-line.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blankLine: () => (/* binding */ blankLine)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@1.1.0/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst blankLine = {tokenize: tokenizeBlankLine, partial: true}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLine(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of blank line.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *     ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_0__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_1__.factorySpace)(effects, after, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix)(code)\n      : after(code)\n  }\n\n  /**\n   * At eof/eol, after optional whitespace.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *       ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_3__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_0__.markdownLineEnding)(code) ? ok(code) : nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWljcm9tYXJrLWNvcmUtY29tbW9ubWFya0AxLjEuMC9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWNvcmUtY29tbW9ubWFyay9kZXYvbGliL2JsYW5rLWxpbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNBLGFBQWEsMENBQTBDO0FBQ3ZELGFBQWEsc0NBQXNDO0FBQ25ELGFBQWEsZ0RBQWdEO0FBQzdELGFBQWEsMENBQTBDO0FBQ3ZEOztBQUVvRDtBQUNzQjtBQUN0QjtBQUNBOztBQUVwRCxXQUFXLFdBQVc7QUFDZixtQkFBbUI7O0FBRTFCO0FBQ0EsVUFBVTtBQUNWLFVBQVU7QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxXQUFXLHVFQUFhO0FBQ3hCLFFBQVEscUVBQVksaUJBQWlCLGlFQUFLO0FBQzFDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxvQkFBb0IsaUVBQUssUUFBUSw0RUFBa0I7QUFDbkQ7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL21pY3JvbWFyay1jb3JlLWNvbW1vbm1hcmtAMS4xLjAvbm9kZV9tb2R1bGVzL21pY3JvbWFyay1jb3JlLWNvbW1vbm1hcmsvZGV2L2xpYi9ibGFuay1saW5lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnbWljcm9tYXJrLXV0aWwtdHlwZXMnKS5Db25zdHJ1Y3R9IENvbnN0cnVjdFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWljcm9tYXJrLXV0aWwtdHlwZXMnKS5TdGF0ZX0gU3RhdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21pY3JvbWFyay11dGlsLXR5cGVzJykuVG9rZW5pemVDb250ZXh0fSBUb2tlbml6ZUNvbnRleHRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21pY3JvbWFyay11dGlsLXR5cGVzJykuVG9rZW5pemVyfSBUb2tlbml6ZXJcbiAqL1xuXG5pbXBvcnQge2ZhY3RvcnlTcGFjZX0gZnJvbSAnbWljcm9tYXJrLWZhY3Rvcnktc3BhY2UnXG5pbXBvcnQge21hcmtkb3duTGluZUVuZGluZywgbWFya2Rvd25TcGFjZX0gZnJvbSAnbWljcm9tYXJrLXV0aWwtY2hhcmFjdGVyJ1xuaW1wb3J0IHtjb2Rlc30gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3ltYm9sL2NvZGVzLmpzJ1xuaW1wb3J0IHt0eXBlc30gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3ltYm9sL3R5cGVzLmpzJ1xuXG4vKiogQHR5cGUge0NvbnN0cnVjdH0gKi9cbmV4cG9ydCBjb25zdCBibGFua0xpbmUgPSB7dG9rZW5pemU6IHRva2VuaXplQmxhbmtMaW5lLCBwYXJ0aWFsOiB0cnVlfVxuXG4vKipcbiAqIEB0aGlzIHtUb2tlbml6ZUNvbnRleHR9XG4gKiBAdHlwZSB7VG9rZW5pemVyfVxuICovXG5mdW5jdGlvbiB0b2tlbml6ZUJsYW5rTGluZShlZmZlY3RzLCBvaywgbm9rKSB7XG4gIHJldHVybiBzdGFydFxuXG4gIC8qKlxuICAgKiBTdGFydCBvZiBibGFuayBsaW5lLlxuICAgKlxuICAgKiA+IPCfkYkgKipOb3RlKio6IGDikKBgIHJlcHJlc2VudHMgYSBzcGFjZSBjaGFyYWN0ZXIuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCDikKDikKDikIpcbiAgICogICAgIF5cbiAgICogPiB8IOKQilxuICAgKiAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gc3RhcnQoY29kZSkge1xuICAgIHJldHVybiBtYXJrZG93blNwYWNlKGNvZGUpXG4gICAgICA/IGZhY3RvcnlTcGFjZShlZmZlY3RzLCBhZnRlciwgdHlwZXMubGluZVByZWZpeCkoY29kZSlcbiAgICAgIDogYWZ0ZXIoY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBBdCBlb2YvZW9sLCBhZnRlciBvcHRpb25hbCB3aGl0ZXNwYWNlLlxuICAgKlxuICAgKiA+IPCfkYkgKipOb3RlKio6IGDikKBgIHJlcHJlc2VudHMgYSBzcGFjZSBjaGFyYWN0ZXIuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCDikKDikKDikIpcbiAgICogICAgICAgXlxuICAgKiA+IHwg4pCKXG4gICAqICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBhZnRlcihjb2RlKSB7XG4gICAgcmV0dXJuIGNvZGUgPT09IGNvZGVzLmVvZiB8fCBtYXJrZG93bkxpbmVFbmRpbmcoY29kZSkgPyBvayhjb2RlKSA6IG5vayhjb2RlKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/blank-line.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/block-quote.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/block-quote.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockQuote: () => (/* binding */ blockQuote)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@1.1.0/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Exiter} Exiter\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst blockQuote = {\n  name: 'blockQuote',\n  tokenize: tokenizeBlockQuoteStart,\n  continuation: {tokenize: tokenizeBlockQuoteContinuation},\n  exit\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteStart(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of block quote.\n   *\n   * ```markdown\n   * > | > a\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      const state = self.containerState\n\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(state, 'expected `containerState` to be defined in container')\n\n      if (!state.open) {\n        effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.blockQuote, {_container: true})\n        state.open = true\n      }\n\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefix)\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.blockQuoteMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.blockQuoteMarker)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `>`, before optional whitespace.\n   *\n   * ```markdown\n   * > | > a\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefixWhitespace)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefixWhitespace)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefix)\n      return ok\n    }\n\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.blockQuotePrefix)\n    return ok(code)\n  }\n}\n\n/**\n * Start of block quote continuation.\n *\n * ```markdown\n *   | > a\n * > | > b\n *     ^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteContinuation(effects, ok, nok) {\n  const self = this\n\n  return contStart\n\n  /**\n   * Start of block quote continuation.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contStart(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      // Always populated by defaults.\n      (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(\n        effects,\n        contBefore,\n        micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n      )(code)\n    }\n\n    return contBefore(code)\n  }\n\n  /**\n   * At `>`, after optional whitespace.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contBefore(code) {\n    return effects.attempt(blockQuote, ok, nok)(code)\n  }\n}\n\n/** @type {Exiter} */\nfunction exit(effects) {\n  effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.blockQuote)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/block-quote.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/character-escape.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/character-escape.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterEscape: () => (/* binding */ characterEscape)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst characterEscape = {\n  name: 'characterEscape',\n  tokenize: tokenizeCharacterEscape\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of character escape.\n   *\n   * ```markdown\n   * > | a\\*b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.backslash, 'expected `\\\\`')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterEscape)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.escapeMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.escapeMarker)\n    return inside\n  }\n\n  /**\n   * After `\\`, at punctuation.\n   *\n   * ```markdown\n   * > | a\\*b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // ASCII punctuation.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiPunctuation)(code)) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterEscapeValue)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterEscapeValue)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterEscape)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/character-escape.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/character-reference.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/character-reference.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterReference: () => (/* binding */ characterReference)\n/* harmony export */ });\n/* harmony import */ var decode_named_character_reference__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! decode-named-character-reference */ \"(ssr)/./node_modules/.pnpm/decode-named-character-reference@1.0.2/node_modules/decode-named-character-reference/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst characterReference = {\n  name: 'characterReference',\n  tokenize: tokenizeCharacterReference\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterReference(effects, ok, nok) {\n  const self = this\n  let size = 0\n  /** @type {number} */\n  let max\n  /** @type {(code: Code) => boolean} */\n  let test\n\n  return start\n\n  /**\n   * Start of character reference.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *      ^\n   * > | a&#123;b\n   *      ^\n   * > | a&#x9;b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.ampersand, 'expected `&`')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReference)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarker)\n    return open\n  }\n\n  /**\n   * After `&`, at `#` for numeric references or alphanumeric for named\n   * references.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^\n   * > | a&#123;b\n   *       ^\n   * > | a&#x9;b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.numberSign) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarkerNumeric)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarkerNumeric)\n      return numeric\n    }\n\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceValue)\n    max = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.characterReferenceNamedSizeMax\n    test = micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlphanumeric\n    return value(code)\n  }\n\n  /**\n   * After `#`, at `x` for hexadecimals or digit for decimals.\n   *\n   * ```markdown\n   * > | a&#123;b\n   *        ^\n   * > | a&#x9;b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function numeric(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.uppercaseX || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.lowercaseX) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarkerHexadecimal)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarkerHexadecimal)\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceValue)\n      max = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.characterReferenceHexadecimalSizeMax\n      test = micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiHexDigit\n      return value\n    }\n\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceValue)\n    max = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.characterReferenceDecimalSizeMax\n    test = micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiDigit\n    return value(code)\n  }\n\n  /**\n   * After markers (`&#x`, `&#`, or `&`), in value, before `;`.\n   *\n   * The character reference kind defines what and how many characters are\n   * allowed.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^^^\n   * > | a&#123;b\n   *        ^^^\n   * > | a&#x9;b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function value(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.semicolon && size) {\n      const token = effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceValue)\n\n      if (\n        test === micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlphanumeric &&\n        !(0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_5__.decodeNamedCharacterReference)(self.sliceSerialize(token))\n      ) {\n        return nok(code)\n      }\n\n      // To do: `markdown-rs` uses a different name:\n      // `CharacterReferenceMarkerSemi`.\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReferenceMarker)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.characterReference)\n      return ok\n    }\n\n    if (test(code) && size++ < max) {\n      effects.consume(code)\n      return value\n    }\n\n    return nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/character-reference.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/code-fenced.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/code-fenced.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codeFenced: () => (/* binding */ codeFenced)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@1.1.0/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  tokenize: tokenizeNonLazyContinuation,\n  partial: true\n}\n\n/** @type {Construct} */\nconst codeFenced = {\n  name: 'codeFenced',\n  tokenize: tokenizeCodeFenced,\n  concrete: true\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeCodeFenced(effects, ok, nok) {\n  const self = this\n  /** @type {Construct} */\n  const closeStart = {tokenize: tokenizeCloseStart, partial: true}\n  let initialPrefix = 0\n  let sizeOpen = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of code.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse whitespace like `markdown-rs`.\n    return beforeSequenceOpen(code)\n  }\n\n  /**\n   * In opening fence, after prefix, at sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeSequenceOpen(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.tilde,\n      'expected `` ` `` or `~`'\n    )\n\n    const tail = self.events[self.events.length - 1]\n    initialPrefix =\n      tail && tail[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix\n        ? tail[2].sliceSerialize(tail[1], true).length\n        : 0\n\n    marker = code\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFenced)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFence)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *      ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === marker) {\n      sizeOpen++\n      effects.consume(code)\n      return sequenceOpen\n    }\n\n    if (sizeOpen < micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.codeFencedSequenceSizeMin) {\n      return nok(code)\n    }\n\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceSequence)\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(effects, infoBefore, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.whitespace)(code)\n      : infoBefore(code)\n  }\n\n  /**\n   * In opening fence, after the sequence (and optional whitespace), before info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function infoBefore(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFence)\n      return self.interrupt\n        ? ok(code)\n        : effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceInfo)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.chunkString, {contentType: micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.contentTypeString})\n    return info(code)\n  }\n\n  /**\n   * In info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function info(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.chunkString)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceInfo)\n      return infoBefore(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.chunkString)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceInfo)\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(effects, metaBefore, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.whitespace)(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return info\n  }\n\n  /**\n   * In opening fence, after info and whitespace, before meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function metaBefore(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return infoBefore(code)\n    }\n\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceMeta)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.chunkString, {contentType: micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.contentTypeString})\n    return meta(code)\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.chunkString)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceMeta)\n      return infoBefore(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return meta\n  }\n\n  /**\n   * At eol/eof in code, before a non-lazy closing fence or content.\n   *\n   * ```markdown\n   * > | ~~~js\n   *          ^\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function atNonLazyBreak(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')\n    return effects.attempt(closeStart, after, contentBefore)(code)\n  }\n\n  /**\n   * Before code content, not a closing fence, at eol.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentBefore(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    return contentStart\n  }\n\n  /**\n   * Before code content, not a closing fence.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return initialPrefix > 0 && (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(\n          effects,\n          beforeContentChunk,\n          micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,\n          initialPrefix + 1\n        )(code)\n      : beforeContentChunk(code)\n  }\n\n  /**\n   * Before code content, after optional prefix.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFlowValue)\n    return contentChunk(code)\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^^^^^^^^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFlowValue)\n      return beforeContentChunk(code)\n    }\n\n    effects.consume(code)\n    return contentChunk\n  }\n\n  /**\n   * After code.\n   *\n   * ```markdown\n   *   | ~~~js\n   *   | alert(1)\n   * > | ~~~\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFenced)\n    return ok(code)\n  }\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */\n  function tokenizeCloseStart(effects, ok, nok) {\n    let size = 0\n\n    return startBefore\n\n    /**\n     *\n     *\n     * @type {State}\n     */\n    function startBefore(code) {\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n      return start\n    }\n\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      // Always populated by defaults.\n      (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      // To do: `enter` here or in next state?\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFence)\n      return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)\n        ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(\n            effects,\n            beforeSequenceClose,\n            micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,\n            self.parser.constructs.disable.null.includes('codeIndented')\n              ? undefined\n              : micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.tabSize\n          )(code)\n        : beforeSequenceClose(code)\n    }\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      if (code === marker) {\n        effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceSequence)\n        return sequenceClose(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === marker) {\n        size++\n        effects.consume(code)\n        return sequenceClose\n      }\n\n      if (size >= sizeOpen) {\n        effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFenceSequence)\n        return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)\n          ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(effects, sequenceCloseAfter, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.whitespace)(code)\n          : sequenceCloseAfter(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceCloseAfter(code) {\n      if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n        effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFencedFence)\n        return ok(code)\n      }\n\n      return nok(code)\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return nok(code)\n    }\n\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected eol')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    return lineStart\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/code-fenced.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/code-indented.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/code-indented.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codeIndented: () => (/* binding */ codeIndented)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@1.1.0/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst codeIndented = {\n  name: 'codeIndented',\n  tokenize: tokenizeCodeIndented\n}\n\n/** @type {Construct} */\nconst furtherStart = {tokenize: tokenizeFurtherStart, partial: true}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeCodeIndented(effects, ok, nok) {\n  const self = this\n  return start\n\n  /**\n   * Start of code (indented).\n   *\n   * > **Parsing note**: it is not needed to check if this first line is a\n   * > filled line (that it has a non-whitespace character), because blank lines\n   * > are parsed already, so we never run into that.\n   *\n   * ```markdown\n   * > |     aaa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: manually check if interrupting like `markdown-rs`.\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownSpace)(code))\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeIndented)\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n      effects,\n      afterPrefix,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,\n      micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize + 1\n    )(code)\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize\n      ? atBreak(code)\n      : nok(code)\n  }\n\n  /**\n   * At a break.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_5__.codes.eof) {\n      return after(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code)) {\n      return effects.attempt(furtherStart, atBreak, after)(code)\n    }\n\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFlowValue)\n    return inside(code)\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_5__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeFlowValue)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return inside\n  }\n\n  /** @type {State} */\n  function after(code) {\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.codeIndented)\n    // To do: allow interrupting like `markdown-rs`.\n    // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeFurtherStart(effects, ok, nok) {\n  const self = this\n\n  return furtherStart\n\n  /**\n   * At eol, trying to parse another indent.\n   *\n   * ```markdown\n   * > |     aaa\n   *            ^\n   *   |     bbb\n   * ```\n   *\n   * @type {State}\n   */\n  function furtherStart(code) {\n    // To do: improve `lazy` / `pierce` handling.\n    // If this is a lazy line, it can’t be code.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code)) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n      return furtherStart\n    }\n\n    // To do: the code here in `micromark-js` is a bit different from\n    // `markdown-rs` because there it can attempt spaces.\n    // We can’t yet.\n    //\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n      effects,\n      afterPrefix,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,\n      micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize + 1\n    )(code)\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize\n      ? ok(code)\n      : (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code)\n      ? furtherStart(code)\n      : nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/code-indented.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/code-text.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/code-text.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codeText: () => (/* binding */ codeText)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Previous} Previous\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst codeText = {\n  name: 'codeText',\n  tokenize: tokenizeCodeText,\n  resolve: resolveCodeText,\n  previous\n}\n\n// To do: next major: don’t resolve, like `markdown-rs`.\n/** @type {Resolver} */\nfunction resolveCodeText(events) {\n  let tailExitIndex = events.length - 4\n  let headEnterIndex = 3\n  /** @type {number} */\n  let index\n  /** @type {number | undefined} */\n  let enter\n\n  // If we start and end with an EOL or a space.\n  if (\n    (events[headEnterIndex][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding ||\n      events[headEnterIndex][1].type === 'space') &&\n    (events[tailExitIndex][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding ||\n      events[tailExitIndex][1].type === 'space')\n  ) {\n    index = headEnterIndex\n\n    // And we have data.\n    while (++index < tailExitIndex) {\n      if (events[index][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.codeTextData) {\n        // Then we have padding.\n        events[headEnterIndex][1].type = micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.codeTextPadding\n        events[tailExitIndex][1].type = micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.codeTextPadding\n        headEnterIndex += 2\n        tailExitIndex -= 2\n        break\n      }\n    }\n  }\n\n  // Merge adjacent spaces and data.\n  index = headEnterIndex - 1\n  tailExitIndex++\n\n  while (++index <= tailExitIndex) {\n    if (enter === undefined) {\n      if (\n        index !== tailExitIndex &&\n        events[index][1].type !== micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding\n      ) {\n        enter = index\n      }\n    } else if (\n      index === tailExitIndex ||\n      events[index][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding\n    ) {\n      events[enter][1].type = micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.codeTextData\n\n      if (index !== enter + 2) {\n        events[enter][1].end = events[index - 1][1].end\n        events.splice(enter + 2, index - enter - 2)\n        tailExitIndex -= index - enter - 2\n        index = enter + 2\n      }\n\n      enter = undefined\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Previous}\n */\nfunction previous(code) {\n  // If there is a previous code, there will always be a tail.\n  return (\n    code !== micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.graveAccent ||\n    this.events[this.events.length - 1][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.characterEscape\n  )\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeCodeText(effects, ok, nok) {\n  const self = this\n  let sizeOpen = 0\n  /** @type {number} */\n  let size\n  /** @type {Token} */\n  let token\n\n  return start\n\n  /**\n   * Start of code (text).\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * > | \\`a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.graveAccent, 'expected `` ` ``')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(previous.call(self, self.previous), 'expected correct previous')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.codeText)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.codeTextSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.graveAccent) {\n      effects.consume(code)\n      sizeOpen++\n      return sequenceOpen\n    }\n\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.codeTextSequence)\n    return between(code)\n  }\n\n  /**\n   * Between something and something else.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function between(code) {\n    // EOF.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof) {\n      return nok(code)\n    }\n\n    // To do: next major: don’t do spaces in resolve, but when compiling,\n    // like `markdown-rs`.\n    // Tabs don’t work, and virtual spaces don’t make sense.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.space) {\n      effects.enter('space')\n      effects.consume(code)\n      effects.exit('space')\n      return between\n    }\n\n    // Closing fence? Could also be data.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.graveAccent) {\n      token = effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.codeTextSequence)\n      size = 0\n      return sequenceClose(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n      return between\n    }\n\n    // Data.\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.codeTextData)\n    return data(code)\n  }\n\n  /**\n   * In data.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.space ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.graveAccent ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)\n    ) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.codeTextData)\n      return between(code)\n    }\n\n    effects.consume(code)\n    return data\n  }\n\n  /**\n   * In closing sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceClose(code) {\n    // More.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.graveAccent) {\n      effects.consume(code)\n      size++\n      return sequenceClose\n    }\n\n    // Done!\n    if (size === sizeOpen) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.codeTextSequence)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.codeText)\n      return ok(code)\n    }\n\n    // More or less accents: mark as data.\n    token.type = micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.codeTextData\n    return data(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/code-text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/content.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/content.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: () => (/* binding */ content)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@1.1.0/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-subtokenize */ \"(ssr)/./node_modules/.pnpm/micromark-util-subtokenize@1.1.0/node_modules/micromark-util-subtokenize/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n\n\n/**\n * No name because it must not be turned off.\n * @type {Construct}\n */\nconst content = {tokenize: tokenizeContent, resolve: resolveContent}\n\n/** @type {Construct} */\nconst continuationConstruct = {tokenize: tokenizeContinuation, partial: true}\n\n/**\n * Content is transparent: it’s parsed right now. That way, definitions are also\n * parsed right now: before text in paragraphs (specifically, media) are parsed.\n *\n * @type {Resolver}\n */\nfunction resolveContent(events) {\n  ;(0,micromark_util_subtokenize__WEBPACK_IMPORTED_MODULE_1__.subtokenize)(events)\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeContent(effects, ok) {\n  /** @type {Token | undefined} */\n  let previous\n\n  return chunkStart\n\n  /**\n   * Before a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkStart(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      code !== micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof && !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code),\n      'expected no eof or eol'\n    )\n\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.content)\n    previous = effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent, {\n      contentType: micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeContent\n    })\n    return chunkInside(code)\n  }\n\n  /**\n   * In a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkInside(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof) {\n      return contentEnd(code)\n    }\n\n    // To do: in `markdown-rs`, each line is parsed on its own, and everything\n    // is stitched together resolving.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      return effects.check(\n        continuationConstruct,\n        contentContinue,\n        contentEnd\n      )(code)\n    }\n\n    // Data.\n    effects.consume(code)\n    return chunkInside\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentEnd(code) {\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.content)\n    return ok(code)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentContinue(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code), 'expected eol')\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent)\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(previous, 'expected previous token')\n    previous.next = effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent, {\n      contentType: micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeContent,\n      previous\n    })\n    previous = previous.next\n    return chunkInside\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeContinuation(effects, ok, nok) {\n  const self = this\n\n  return startLookahead\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function startLookahead(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code), 'expected a line ending')\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.chunkContent)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.lineEnding)\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(effects, prefixed, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.linePrefix)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function prefixed(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      return nok(code)\n    }\n\n    // Always populated by defaults.\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n\n    const tail = self.events[self.events.length - 1]\n\n    if (\n      !self.parser.constructs.disable.null.includes('codeIndented') &&\n      tail &&\n      tail[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n    ) {\n      return ok(code)\n    }\n\n    return effects.interrupt(self.parser.constructs.flow, nok, ok)(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/content.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/definition.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/definition.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   definition: () => (/* binding */ definition)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_destination__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-factory-destination */ \"(ssr)/./node_modules/.pnpm/micromark-factory-destination@1.1.0/node_modules/micromark-factory-destination/dev/index.js\");\n/* harmony import */ var micromark_factory_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-label */ \"(ssr)/./node_modules/.pnpm/micromark-factory-label@1.1.0/node_modules/micromark-factory-label/dev/index.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@1.1.0/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_factory_title__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-factory-title */ \"(ssr)/./node_modules/.pnpm/micromark-factory-title@1.1.0/node_modules/micromark-factory-title/dev/index.js\");\n/* harmony import */ var micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-whitespace */ \"(ssr)/./node_modules/.pnpm/micromark-factory-whitespace@1.1.0/node_modules/micromark-factory-whitespace/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/.pnpm/micromark-util-normalize-identifier@1.1.0/node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst definition = {name: 'definition', tokenize: tokenizeDefinition}\n\n/** @type {Construct} */\nconst titleBefore = {tokenize: tokenizeTitleBefore, partial: true}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDefinition(effects, ok, nok) {\n  const self = this\n  /** @type {string} */\n  let identifier\n\n  return start\n\n  /**\n   * At start of a definition.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Do not interrupt paragraphs (but do follow definitions).\n    // To do: do `interrupt` the way `markdown-rs` does.\n    // To do: parse whitespace the way `markdown-rs` does.\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definition)\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at `[`.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    // To do: parse whitespace the way `markdown-rs` does.\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.leftSquareBracket, 'expected `[`')\n    return micromark_factory_label__WEBPACK_IMPORTED_MODULE_3__.factoryLabel.call(\n      self,\n      effects,\n      labelAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definitionLabel,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definitionLabelMarker,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definitionLabelString\n    )(code)\n  }\n\n  /**\n   * After label.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_4__.normalizeIdentifier)(\n      self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n    )\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.colon) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definitionMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definitionMarker)\n      return markerAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After marker.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function markerAfter(code) {\n    // Note: whitespace is optional.\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)\n      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_6__.factoryWhitespace)(effects, destinationBefore)(code)\n      : destinationBefore(code)\n  }\n\n  /**\n   * Before destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationBefore(code) {\n    return (0,micromark_factory_destination__WEBPACK_IMPORTED_MODULE_7__.factoryDestination)(\n      effects,\n      destinationAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definitionDestination,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definitionDestinationLiteral,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definitionDestinationLiteralMarker,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definitionDestinationRaw,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definitionDestinationString\n    )(code)\n  }\n\n  /**\n   * After destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationAfter(code) {\n    return effects.attempt(titleBefore, after, after)(code)\n  }\n\n  /**\n   * After definition.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_8__.factorySpace)(effects, afterWhitespace, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace)(code)\n      : afterWhitespace(code)\n  }\n\n  /**\n   * After definition, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterWhitespace(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definition)\n\n      // Note: we don’t care about uniqueness.\n      // It’s likely that that doesn’t happen very frequently.\n      // It is more likely that it wastes precious time.\n      self.parser.defined.push(identifier)\n\n      // To do: `markdown-rs` interrupt.\n      // // You’d be interrupting.\n      // tokenizer.interrupt = true\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTitleBefore(effects, ok, nok) {\n  return titleBefore\n\n  /**\n   * After destination, at whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleBefore(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)\n      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_6__.factoryWhitespace)(effects, beforeMarker)(code)\n      : nok(code)\n  }\n\n  /**\n   * At title.\n   *\n   * ```markdown\n   *   | [a]: b\n   * > | \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeMarker(code) {\n    return (0,micromark_factory_title__WEBPACK_IMPORTED_MODULE_9__.factoryTitle)(\n      effects,\n      titleAfter,\n      nok,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definitionTitle,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definitionTitleMarker,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definitionTitleString\n    )(code)\n  }\n\n  /**\n   * After title.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfter(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_8__.factorySpace)(\n          effects,\n          titleAfterOptionalWhitespace,\n          micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace\n        )(code)\n      : titleAfterOptionalWhitespace(code)\n  }\n\n  /**\n   * After title, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfterOptionalWhitespace(code) {\n    return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code) ? ok(code) : nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWljcm9tYXJrLWNvcmUtY29tbW9ubWFya0AxLjEuMC9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWNvcmUtY29tbW9ubWFyay9kZXYvbGliL2RlZmluaXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBLGFBQWEsMENBQTBDO0FBQ3ZELGFBQWEsc0NBQXNDO0FBQ25ELGFBQWEsZ0RBQWdEO0FBQzdELGFBQWEsMENBQTBDO0FBQ3ZEOztBQUVnRTtBQUNaO0FBQ0E7QUFDQTtBQUNVO0FBSzdCO0FBQ3NDO0FBQ25CO0FBQ0E7QUFDYjs7QUFFdkMsV0FBVyxXQUFXO0FBQ2Ysb0JBQW9COztBQUUzQixXQUFXLFdBQVc7QUFDdEIscUJBQXFCOztBQUVyQjtBQUNBLFVBQVU7QUFDVixVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixpRUFBSztBQUN2QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsSUFBSSw4Q0FBTSxVQUFVLGlFQUFLO0FBQ3pCLFdBQVcsaUVBQVk7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0saUVBQUs7QUFDWCxNQUFNLGlFQUFLO0FBQ1gsTUFBTSxpRUFBSztBQUNYO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsaUJBQWlCLHdGQUFtQjtBQUNwQztBQUNBOztBQUVBLGlCQUFpQixpRUFBSztBQUN0QixvQkFBb0IsaUVBQUs7QUFDekI7QUFDQSxtQkFBbUIsaUVBQUs7QUFDeEI7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsV0FBVyxtRkFBeUI7QUFDcEMsUUFBUSwrRUFBaUI7QUFDekI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxXQUFXLGlGQUFrQjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0saUVBQUs7QUFDWCxNQUFNLGlFQUFLO0FBQ1gsTUFBTSxpRUFBSztBQUNYLE1BQU0saUVBQUs7QUFDWCxNQUFNLGlFQUFLO0FBQ1g7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxXQUFXLHVFQUFhO0FBQ3hCLFFBQVEscUVBQVksMkJBQTJCLGlFQUFLO0FBQ3BEO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLGlCQUFpQixpRUFBSyxRQUFRLDRFQUFrQjtBQUNoRCxtQkFBbUIsaUVBQUs7O0FBRXhCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsVUFBVTtBQUNWLFVBQVU7QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLFdBQVcsbUZBQXlCO0FBQ3BDLFFBQVEsK0VBQWlCO0FBQ3pCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxXQUFXLHFFQUFZO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBLE1BQU0saUVBQUs7QUFDWCxNQUFNLGlFQUFLO0FBQ1gsTUFBTSxpRUFBSztBQUNYO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsV0FBVyx1RUFBYTtBQUN4QixRQUFRLHFFQUFZO0FBQ3BCO0FBQ0E7QUFDQSxVQUFVLGlFQUFLO0FBQ2Y7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLG9CQUFvQixpRUFBSyxRQUFRLDRFQUFrQjtBQUNuRDtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vbWljcm9tYXJrLWNvcmUtY29tbW9ubWFya0AxLjEuMC9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWNvcmUtY29tbW9ubWFyay9kZXYvbGliL2RlZmluaXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtaWNyb21hcmstdXRpbC10eXBlcycpLkNvbnN0cnVjdH0gQ29uc3RydWN0XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtaWNyb21hcmstdXRpbC10eXBlcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnbWljcm9tYXJrLXV0aWwtdHlwZXMnKS5Ub2tlbml6ZUNvbnRleHR9IFRva2VuaXplQ29udGV4dFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWljcm9tYXJrLXV0aWwtdHlwZXMnKS5Ub2tlbml6ZXJ9IFRva2VuaXplclxuICovXG5cbmltcG9ydCB7ZmFjdG9yeURlc3RpbmF0aW9ufSBmcm9tICdtaWNyb21hcmstZmFjdG9yeS1kZXN0aW5hdGlvbidcbmltcG9ydCB7ZmFjdG9yeUxhYmVsfSBmcm9tICdtaWNyb21hcmstZmFjdG9yeS1sYWJlbCdcbmltcG9ydCB7ZmFjdG9yeVNwYWNlfSBmcm9tICdtaWNyb21hcmstZmFjdG9yeS1zcGFjZSdcbmltcG9ydCB7ZmFjdG9yeVRpdGxlfSBmcm9tICdtaWNyb21hcmstZmFjdG9yeS10aXRsZSdcbmltcG9ydCB7ZmFjdG9yeVdoaXRlc3BhY2V9IGZyb20gJ21pY3JvbWFyay1mYWN0b3J5LXdoaXRlc3BhY2UnXG5pbXBvcnQge1xuICBtYXJrZG93bkxpbmVFbmRpbmcsXG4gIG1hcmtkb3duTGluZUVuZGluZ09yU3BhY2UsXG4gIG1hcmtkb3duU3BhY2Vcbn0gZnJvbSAnbWljcm9tYXJrLXV0aWwtY2hhcmFjdGVyJ1xuaW1wb3J0IHtub3JtYWxpemVJZGVudGlmaWVyfSBmcm9tICdtaWNyb21hcmstdXRpbC1ub3JtYWxpemUtaWRlbnRpZmllcidcbmltcG9ydCB7Y29kZXN9IGZyb20gJ21pY3JvbWFyay11dGlsLXN5bWJvbC9jb2Rlcy5qcydcbmltcG9ydCB7dHlwZXN9IGZyb20gJ21pY3JvbWFyay11dGlsLXN5bWJvbC90eXBlcy5qcydcbmltcG9ydCB7b2sgYXMgYXNzZXJ0fSBmcm9tICd1dnUvYXNzZXJ0J1xuXG4vKiogQHR5cGUge0NvbnN0cnVjdH0gKi9cbmV4cG9ydCBjb25zdCBkZWZpbml0aW9uID0ge25hbWU6ICdkZWZpbml0aW9uJywgdG9rZW5pemU6IHRva2VuaXplRGVmaW5pdGlvbn1cblxuLyoqIEB0eXBlIHtDb25zdHJ1Y3R9ICovXG5jb25zdCB0aXRsZUJlZm9yZSA9IHt0b2tlbml6ZTogdG9rZW5pemVUaXRsZUJlZm9yZSwgcGFydGlhbDogdHJ1ZX1cblxuLyoqXG4gKiBAdGhpcyB7VG9rZW5pemVDb250ZXh0fVxuICogQHR5cGUge1Rva2VuaXplcn1cbiAqL1xuZnVuY3Rpb24gdG9rZW5pemVEZWZpbml0aW9uKGVmZmVjdHMsIG9rLCBub2spIHtcbiAgY29uc3Qgc2VsZiA9IHRoaXNcbiAgLyoqIEB0eXBlIHtzdHJpbmd9ICovXG4gIGxldCBpZGVudGlmaWVyXG5cbiAgcmV0dXJuIHN0YXJ0XG5cbiAgLyoqXG4gICAqIEF0IHN0YXJ0IG9mIGEgZGVmaW5pdGlvbi5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IFthXTogYiBcImNcIlxuICAgKiAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gc3RhcnQoY29kZSkge1xuICAgIC8vIERvIG5vdCBpbnRlcnJ1cHQgcGFyYWdyYXBocyAoYnV0IGRvIGZvbGxvdyBkZWZpbml0aW9ucykuXG4gICAgLy8gVG8gZG86IGRvIGBpbnRlcnJ1cHRgIHRoZSB3YXkgYG1hcmtkb3duLXJzYCBkb2VzLlxuICAgIC8vIFRvIGRvOiBwYXJzZSB3aGl0ZXNwYWNlIHRoZSB3YXkgYG1hcmtkb3duLXJzYCBkb2VzLlxuICAgIGVmZmVjdHMuZW50ZXIodHlwZXMuZGVmaW5pdGlvbilcbiAgICByZXR1cm4gYmVmb3JlKGNvZGUpXG4gIH1cblxuICAvKipcbiAgICogQWZ0ZXIgb3B0aW9uYWwgd2hpdGVzcGFjZSwgYXQgYFtgLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgW2FdOiBiIFwiY1wiXG4gICAqICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBiZWZvcmUoY29kZSkge1xuICAgIC8vIFRvIGRvOiBwYXJzZSB3aGl0ZXNwYWNlIHRoZSB3YXkgYG1hcmtkb3duLXJzYCBkb2VzLlxuICAgIGFzc2VydChjb2RlID09PSBjb2Rlcy5sZWZ0U3F1YXJlQnJhY2tldCwgJ2V4cGVjdGVkIGBbYCcpXG4gICAgcmV0dXJuIGZhY3RvcnlMYWJlbC5jYWxsKFxuICAgICAgc2VsZixcbiAgICAgIGVmZmVjdHMsXG4gICAgICBsYWJlbEFmdGVyLFxuICAgICAgLy8gTm90ZTogd2UgZG9u4oCZdCBuZWVkIHRvIHJlc2V0IHRoZSB3YXkgYG1hcmtkb3duLXJzYCBkb2VzLlxuICAgICAgbm9rLFxuICAgICAgdHlwZXMuZGVmaW5pdGlvbkxhYmVsLFxuICAgICAgdHlwZXMuZGVmaW5pdGlvbkxhYmVsTWFya2VyLFxuICAgICAgdHlwZXMuZGVmaW5pdGlvbkxhYmVsU3RyaW5nXG4gICAgKShjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEFmdGVyIGxhYmVsLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgW2FdOiBiIFwiY1wiXG4gICAqICAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBsYWJlbEFmdGVyKGNvZGUpIHtcbiAgICBpZGVudGlmaWVyID0gbm9ybWFsaXplSWRlbnRpZmllcihcbiAgICAgIHNlbGYuc2xpY2VTZXJpYWxpemUoc2VsZi5ldmVudHNbc2VsZi5ldmVudHMubGVuZ3RoIC0gMV1bMV0pLnNsaWNlKDEsIC0xKVxuICAgIClcblxuICAgIGlmIChjb2RlID09PSBjb2Rlcy5jb2xvbikge1xuICAgICAgZWZmZWN0cy5lbnRlcih0eXBlcy5kZWZpbml0aW9uTWFya2VyKVxuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICBlZmZlY3RzLmV4aXQodHlwZXMuZGVmaW5pdGlvbk1hcmtlcilcbiAgICAgIHJldHVybiBtYXJrZXJBZnRlclxuICAgIH1cblxuICAgIHJldHVybiBub2soY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBBZnRlciBtYXJrZXIuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCBbYV06IGIgXCJjXCJcbiAgICogICAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBtYXJrZXJBZnRlcihjb2RlKSB7XG4gICAgLy8gTm90ZTogd2hpdGVzcGFjZSBpcyBvcHRpb25hbC5cbiAgICByZXR1cm4gbWFya2Rvd25MaW5lRW5kaW5nT3JTcGFjZShjb2RlKVxuICAgICAgPyBmYWN0b3J5V2hpdGVzcGFjZShlZmZlY3RzLCBkZXN0aW5hdGlvbkJlZm9yZSkoY29kZSlcbiAgICAgIDogZGVzdGluYXRpb25CZWZvcmUoY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBCZWZvcmUgZGVzdGluYXRpb24uXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCBbYV06IGIgXCJjXCJcbiAgICogICAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gZGVzdGluYXRpb25CZWZvcmUoY29kZSkge1xuICAgIHJldHVybiBmYWN0b3J5RGVzdGluYXRpb24oXG4gICAgICBlZmZlY3RzLFxuICAgICAgZGVzdGluYXRpb25BZnRlcixcbiAgICAgIC8vIE5vdGU6IHdlIGRvbuKAmXQgbmVlZCB0byByZXNldCB0aGUgd2F5IGBtYXJrZG93bi1yc2AgZG9lcy5cbiAgICAgIG5vayxcbiAgICAgIHR5cGVzLmRlZmluaXRpb25EZXN0aW5hdGlvbixcbiAgICAgIHR5cGVzLmRlZmluaXRpb25EZXN0aW5hdGlvbkxpdGVyYWwsXG4gICAgICB0eXBlcy5kZWZpbml0aW9uRGVzdGluYXRpb25MaXRlcmFsTWFya2VyLFxuICAgICAgdHlwZXMuZGVmaW5pdGlvbkRlc3RpbmF0aW9uUmF3LFxuICAgICAgdHlwZXMuZGVmaW5pdGlvbkRlc3RpbmF0aW9uU3RyaW5nXG4gICAgKShjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEFmdGVyIGRlc3RpbmF0aW9uLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgW2FdOiBiIFwiY1wiXG4gICAqICAgICAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBkZXN0aW5hdGlvbkFmdGVyKGNvZGUpIHtcbiAgICByZXR1cm4gZWZmZWN0cy5hdHRlbXB0KHRpdGxlQmVmb3JlLCBhZnRlciwgYWZ0ZXIpKGNvZGUpXG4gIH1cblxuICAvKipcbiAgICogQWZ0ZXIgZGVmaW5pdGlvbi5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IFthXTogYlxuICAgKiAgICAgICAgICAgXlxuICAgKiA+IHwgW2FdOiBiIFwiY1wiXG4gICAqICAgICAgICAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gYWZ0ZXIoY29kZSkge1xuICAgIHJldHVybiBtYXJrZG93blNwYWNlKGNvZGUpXG4gICAgICA/IGZhY3RvcnlTcGFjZShlZmZlY3RzLCBhZnRlcldoaXRlc3BhY2UsIHR5cGVzLndoaXRlc3BhY2UpKGNvZGUpXG4gICAgICA6IGFmdGVyV2hpdGVzcGFjZShjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEFmdGVyIGRlZmluaXRpb24sIGFmdGVyIG9wdGlvbmFsIHdoaXRlc3BhY2UuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqID4gfCBbYV06IGJcbiAgICogICAgICAgICAgIF5cbiAgICogPiB8IFthXTogYiBcImNcIlxuICAgKiAgICAgICAgICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIGFmdGVyV2hpdGVzcGFjZShjb2RlKSB7XG4gICAgaWYgKGNvZGUgPT09IGNvZGVzLmVvZiB8fCBtYXJrZG93bkxpbmVFbmRpbmcoY29kZSkpIHtcbiAgICAgIGVmZmVjdHMuZXhpdCh0eXBlcy5kZWZpbml0aW9uKVxuXG4gICAgICAvLyBOb3RlOiB3ZSBkb27igJl0IGNhcmUgYWJvdXQgdW5pcXVlbmVzcy5cbiAgICAgIC8vIEl04oCZcyBsaWtlbHkgdGhhdCB0aGF0IGRvZXNu4oCZdCBoYXBwZW4gdmVyeSBmcmVxdWVudGx5LlxuICAgICAgLy8gSXQgaXMgbW9yZSBsaWtlbHkgdGhhdCBpdCB3YXN0ZXMgcHJlY2lvdXMgdGltZS5cbiAgICAgIHNlbGYucGFyc2VyLmRlZmluZWQucHVzaChpZGVudGlmaWVyKVxuXG4gICAgICAvLyBUbyBkbzogYG1hcmtkb3duLXJzYCBpbnRlcnJ1cHQuXG4gICAgICAvLyAvLyBZb3XigJlkIGJlIGludGVycnVwdGluZy5cbiAgICAgIC8vIHRva2VuaXplci5pbnRlcnJ1cHQgPSB0cnVlXG4gICAgICByZXR1cm4gb2soY29kZSlcbiAgICB9XG5cbiAgICByZXR1cm4gbm9rKGNvZGUpXG4gIH1cbn1cblxuLyoqXG4gKiBAdGhpcyB7VG9rZW5pemVDb250ZXh0fVxuICogQHR5cGUge1Rva2VuaXplcn1cbiAqL1xuZnVuY3Rpb24gdG9rZW5pemVUaXRsZUJlZm9yZShlZmZlY3RzLCBvaywgbm9rKSB7XG4gIHJldHVybiB0aXRsZUJlZm9yZVxuXG4gIC8qKlxuICAgKiBBZnRlciBkZXN0aW5hdGlvbiwgYXQgd2hpdGVzcGFjZS5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IFthXTogYlxuICAgKiAgICAgICAgICAgXlxuICAgKiA+IHwgW2FdOiBiIFwiY1wiXG4gICAqICAgICAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiB0aXRsZUJlZm9yZShjb2RlKSB7XG4gICAgcmV0dXJuIG1hcmtkb3duTGluZUVuZGluZ09yU3BhY2UoY29kZSlcbiAgICAgID8gZmFjdG9yeVdoaXRlc3BhY2UoZWZmZWN0cywgYmVmb3JlTWFya2VyKShjb2RlKVxuICAgICAgOiBub2soY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBBdCB0aXRsZS5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogICB8IFthXTogYlxuICAgKiA+IHwgXCJjXCJcbiAgICogICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIGJlZm9yZU1hcmtlcihjb2RlKSB7XG4gICAgcmV0dXJuIGZhY3RvcnlUaXRsZShcbiAgICAgIGVmZmVjdHMsXG4gICAgICB0aXRsZUFmdGVyLFxuICAgICAgbm9rLFxuICAgICAgdHlwZXMuZGVmaW5pdGlvblRpdGxlLFxuICAgICAgdHlwZXMuZGVmaW5pdGlvblRpdGxlTWFya2VyLFxuICAgICAgdHlwZXMuZGVmaW5pdGlvblRpdGxlU3RyaW5nXG4gICAgKShjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEFmdGVyIHRpdGxlLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgW2FdOiBiIFwiY1wiXG4gICAqICAgICAgICAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gdGl0bGVBZnRlcihjb2RlKSB7XG4gICAgcmV0dXJuIG1hcmtkb3duU3BhY2UoY29kZSlcbiAgICAgID8gZmFjdG9yeVNwYWNlKFxuICAgICAgICAgIGVmZmVjdHMsXG4gICAgICAgICAgdGl0bGVBZnRlck9wdGlvbmFsV2hpdGVzcGFjZSxcbiAgICAgICAgICB0eXBlcy53aGl0ZXNwYWNlXG4gICAgICAgICkoY29kZSlcbiAgICAgIDogdGl0bGVBZnRlck9wdGlvbmFsV2hpdGVzcGFjZShjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEFmdGVyIHRpdGxlLCBhZnRlciBvcHRpb25hbCB3aGl0ZXNwYWNlLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgW2FdOiBiIFwiY1wiXG4gICAqICAgICAgICAgICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gdGl0bGVBZnRlck9wdGlvbmFsV2hpdGVzcGFjZShjb2RlKSB7XG4gICAgcmV0dXJuIGNvZGUgPT09IGNvZGVzLmVvZiB8fCBtYXJrZG93bkxpbmVFbmRpbmcoY29kZSkgPyBvayhjb2RlKSA6IG5vayhjb2RlKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/definition.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hardBreakEscape: () => (/* binding */ hardBreakEscape)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst hardBreakEscape = {\n  name: 'hardBreakEscape',\n  tokenize: tokenizeHardBreakEscape\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeHardBreakEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of a hard break (escape).\n   *\n   * ```markdown\n   * > | a\\\n   *      ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.backslash, 'expected `\\\\`')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.hardBreakEscape)\n    effects.consume(code)\n    return after\n  }\n\n  /**\n   * After `\\`, at eol.\n   *\n   * ```markdown\n   * > | a\\\n   *       ^\n   *   | b\n   * ```\n   *\n   *  @type {State}\n   */\n  function after(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.hardBreakEscape)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWljcm9tYXJrLWNvcmUtY29tbW9ubWFya0AxLjEuMC9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWNvcmUtY29tbW9ubWFyay9kZXYvbGliL2hhcmQtYnJlYWstZXNjYXBlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQSxhQUFhLDBDQUEwQztBQUN2RCxhQUFhLHNDQUFzQztBQUNuRCxhQUFhLGdEQUFnRDtBQUM3RCxhQUFhLDBDQUEwQztBQUN2RDs7QUFFMkQ7QUFDUDtBQUNBO0FBQ2I7O0FBRXZDLFdBQVcsV0FBVztBQUNmO0FBQ1A7QUFDQTtBQUNBOztBQUVBO0FBQ0EsVUFBVTtBQUNWLFVBQVU7QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxJQUFJLCtDQUFNLFVBQVUsaUVBQUs7QUFDekIsa0JBQWtCLGlFQUFLO0FBQ3ZCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLFFBQVEsNEVBQWtCO0FBQzFCLG1CQUFtQixpRUFBSztBQUN4QjtBQUNBOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL21pY3JvbWFyay1jb3JlLWNvbW1vbm1hcmtAMS4xLjAvbm9kZV9tb2R1bGVzL21pY3JvbWFyay1jb3JlLWNvbW1vbm1hcmsvZGV2L2xpYi9oYXJkLWJyZWFrLWVzY2FwZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21pY3JvbWFyay11dGlsLXR5cGVzJykuQ29uc3RydWN0fSBDb25zdHJ1Y3RcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21pY3JvbWFyay11dGlsLXR5cGVzJykuU3RhdGV9IFN0YXRlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtaWNyb21hcmstdXRpbC10eXBlcycpLlRva2VuaXplQ29udGV4dH0gVG9rZW5pemVDb250ZXh0XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtaWNyb21hcmstdXRpbC10eXBlcycpLlRva2VuaXplcn0gVG9rZW5pemVyXG4gKi9cblxuaW1wb3J0IHttYXJrZG93bkxpbmVFbmRpbmd9IGZyb20gJ21pY3JvbWFyay11dGlsLWNoYXJhY3RlcidcbmltcG9ydCB7Y29kZXN9IGZyb20gJ21pY3JvbWFyay11dGlsLXN5bWJvbC9jb2Rlcy5qcydcbmltcG9ydCB7dHlwZXN9IGZyb20gJ21pY3JvbWFyay11dGlsLXN5bWJvbC90eXBlcy5qcydcbmltcG9ydCB7b2sgYXMgYXNzZXJ0fSBmcm9tICd1dnUvYXNzZXJ0J1xuXG4vKiogQHR5cGUge0NvbnN0cnVjdH0gKi9cbmV4cG9ydCBjb25zdCBoYXJkQnJlYWtFc2NhcGUgPSB7XG4gIG5hbWU6ICdoYXJkQnJlYWtFc2NhcGUnLFxuICB0b2tlbml6ZTogdG9rZW5pemVIYXJkQnJlYWtFc2NhcGVcbn1cblxuLyoqXG4gKiBAdGhpcyB7VG9rZW5pemVDb250ZXh0fVxuICogQHR5cGUge1Rva2VuaXplcn1cbiAqL1xuZnVuY3Rpb24gdG9rZW5pemVIYXJkQnJlYWtFc2NhcGUoZWZmZWN0cywgb2ssIG5vaykge1xuICByZXR1cm4gc3RhcnRcblxuICAvKipcbiAgICogU3RhcnQgb2YgYSBoYXJkIGJyZWFrIChlc2NhcGUpLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgYVxcXG4gICAqICAgICAgXlxuICAgKiAgIHwgYlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gc3RhcnQoY29kZSkge1xuICAgIGFzc2VydChjb2RlID09PSBjb2Rlcy5iYWNrc2xhc2gsICdleHBlY3RlZCBgXFxcXGAnKVxuICAgIGVmZmVjdHMuZW50ZXIodHlwZXMuaGFyZEJyZWFrRXNjYXBlKVxuICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgIHJldHVybiBhZnRlclxuICB9XG5cbiAgLyoqXG4gICAqIEFmdGVyIGBcXGAsIGF0IGVvbC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IGFcXFxuICAgKiAgICAgICBeXG4gICAqICAgfCBiXG4gICAqIGBgYFxuICAgKlxuICAgKiAgQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gYWZ0ZXIoY29kZSkge1xuICAgIGlmIChtYXJrZG93bkxpbmVFbmRpbmcoY29kZSkpIHtcbiAgICAgIGVmZmVjdHMuZXhpdCh0eXBlcy5oYXJkQnJlYWtFc2NhcGUpXG4gICAgICByZXR1cm4gb2soY29kZSlcbiAgICB9XG5cbiAgICByZXR1cm4gbm9rKGNvZGUpXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/heading-atx.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/heading-atx.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headingAtx: () => (/* binding */ headingAtx)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@1.1.0/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/.pnpm/micromark-util-chunked@1.1.0/node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst headingAtx = {\n  name: 'headingAtx',\n  tokenize: tokenizeHeadingAtx,\n  resolve: resolveHeadingAtx\n}\n\n/** @type {Resolver} */\nfunction resolveHeadingAtx(events, context) {\n  let contentEnd = events.length - 2\n  let contentStart = 3\n  /** @type {Token} */\n  let content\n  /** @type {Token} */\n  let text\n\n  // Prefix whitespace, part of the opening.\n  if (events[contentStart][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace) {\n    contentStart += 2\n  }\n\n  // Suffix whitespace, part of the closing.\n  if (\n    contentEnd - 2 > contentStart &&\n    events[contentEnd][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace\n  ) {\n    contentEnd -= 2\n  }\n\n  if (\n    events[contentEnd][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.atxHeadingSequence &&\n    (contentStart === contentEnd - 1 ||\n      (contentEnd - 4 > contentStart &&\n        events[contentEnd - 2][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace))\n  ) {\n    contentEnd -= contentStart + 1 === contentEnd ? 2 : 4\n  }\n\n  if (contentEnd > contentStart) {\n    content = {\n      type: micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.atxHeadingText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end\n    }\n    text = {\n      type: micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.chunkText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end,\n      contentType: micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_2__.constants.contentTypeText\n    }\n\n    ;(0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__.splice)(events, contentStart, contentEnd - contentStart + 1, [\n      ['enter', content, context],\n      ['enter', text, context],\n      ['exit', text, context],\n      ['exit', content, context]\n    ])\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeHeadingAtx(effects, ok, nok) {\n  let size = 0\n\n  return start\n\n  /**\n   * Start of a heading (atx).\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.atxHeading)\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at `#`.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign, 'expected `#`')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.atxHeadingSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign &&\n      size++ < micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_2__.constants.atxHeadingOpeningFenceSizeMax\n    ) {\n      effects.consume(code)\n      return sequenceOpen\n    }\n\n    // Always at least one `#`.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.atxHeadingSequence)\n      return atBreak(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ## aa\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.atxHeadingSequence)\n      return sequenceFurther(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.atxHeading)\n      // To do: interrupt like `markdown-rs`.\n      // // Feel free to interrupt.\n      // tokenizer.interrupt = false\n      return ok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(effects, atBreak, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace)(code)\n    }\n\n    // To do: generate `data` tokens, add the `text` token later.\n    // Needs edit map, see: `markdown.rs`.\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.atxHeadingText)\n    return data(code)\n  }\n\n  /**\n   * In further sequence (after whitespace).\n   *\n   * Could be normal “visible” hashes in the heading or a final sequence.\n   *\n   * ```markdown\n   * > | ## aa ##\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceFurther(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign) {\n      effects.consume(code)\n      return sequenceFurther\n    }\n\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.atxHeadingSequence)\n    return atBreak(code)\n  }\n\n  /**\n   * In text.\n   *\n   * ```markdown\n   * > | ## aa\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.eof ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.numberSign ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)\n    ) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.atxHeadingText)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return data\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/heading-atx.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/html-flow.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/html-flow.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   htmlFlow: () => (/* binding */ htmlFlow)\n/* harmony export */ });\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_html_tag_name__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-html-tag-name */ \"(ssr)/./node_modules/.pnpm/micromark-util-html-tag-name@1.2.0/node_modules/micromark-util-html-tag-name/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/* harmony import */ var _blank_line_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./blank-line.js */ \"(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst htmlFlow = {\n  name: 'htmlFlow',\n  tokenize: tokenizeHtmlFlow,\n  resolveTo: resolveToHtmlFlow,\n  concrete: true\n}\n\n/** @type {Construct} */\nconst blankLineBefore = {tokenize: tokenizeBlankLineBefore, partial: true}\nconst nonLazyContinuationStart = {\n  tokenize: tokenizeNonLazyContinuationStart,\n  partial: true\n}\n\n/** @type {Resolver} */\nfunction resolveToHtmlFlow(events) {\n  let index = events.length\n\n  while (index--) {\n    if (\n      events[index][0] === 'enter' &&\n      events[index][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.htmlFlow\n    ) {\n      break\n    }\n  }\n\n  if (index > 1 && events[index - 2][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix) {\n    // Add the prefix start to the HTML token.\n    events[index][1].start = events[index - 2][1].start\n    // Add the prefix start to the HTML line token.\n    events[index + 1][1].start = events[index - 2][1].start\n    // Remove the line prefix.\n    events.splice(index - 2, 2)\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlFlow(effects, ok, nok) {\n  const self = this\n  /** @type {number} */\n  let marker\n  /** @type {boolean} */\n  let closingTag\n  /** @type {string} */\n  let buffer\n  /** @type {number} */\n  let index\n  /** @type {Code} */\n  let markerB\n\n  return start\n\n  /**\n   * Start of HTML (flow).\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    return before(code)\n  }\n\n  /**\n   * At `<`, after optional whitespace.\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.lessThan, 'expected `<`')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.htmlFlow)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.htmlFlowData)\n    effects.consume(code)\n    return open\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | <x />\n   *      ^\n   * > | <!doctype>\n   *      ^\n   * > | <!--xxx-->\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.exclamationMark) {\n      effects.consume(code)\n      return declarationOpen\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.slash) {\n      effects.consume(code)\n      closingTag = true\n      return tagCloseStart\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.questionMark) {\n      effects.consume(code)\n      marker = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlInstruction\n      // To do:\n      // tokenizer.concrete = true\n      // To do: use `markdown-rs` style interrupt.\n      // While we’re in an instruction instead of a declaration, we’re on a `?`\n      // right now, so we do need to search for `>`, similar to declarations.\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    // ASCII alphabetical.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code)) {\n      effects.consume(code)\n      // @ts-expect-error: not null.\n      buffer = String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *       ^\n   * > | <!--xxx-->\n   *       ^\n   * > | <![CDATA[>&<]]>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {\n      effects.consume(code)\n      marker = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComment\n      return commentOpenInside\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.leftSquareBracket) {\n      effects.consume(code)\n      marker = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlCdata\n      index = 0\n      return cdataOpenInside\n    }\n\n    // ASCII alphabetical.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code)) {\n      effects.consume(code)\n      marker = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlDeclaration\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!-`, inside a comment, at another `-`.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {\n      effects.consume(code)\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<![`, inside CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *        ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.cdataOpeningString\n\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code)\n\n      if (index === value.length) {\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok : continuation\n      }\n\n      return cdataOpenInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | </x>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code)) {\n      effects.consume(code)\n      // @ts-expect-error: not null.\n      buffer = String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In tag name.\n   *\n   * ```markdown\n   * > | <ab>\n   *      ^^\n   * > | </ab>\n   *       ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagName(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.slash ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEndingOrSpace)(code)\n    ) {\n      const slash = code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.slash\n      const name = buffer.toLowerCase()\n\n      if (!slash && !closingTag && micromark_util_html_tag_name__WEBPACK_IMPORTED_MODULE_5__.htmlRawNames.includes(name)) {\n        marker = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlRaw\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      if (micromark_util_html_tag_name__WEBPACK_IMPORTED_MODULE_5__.htmlBlockNames.includes(buffer.toLowerCase())) {\n        marker = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlBasic\n\n        if (slash) {\n          effects.consume(code)\n          return basicSelfClosing\n        }\n\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      marker = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComplete\n      // Do not support complete HTML when interrupting.\n      return self.interrupt && !self.parser.lazy[self.now().line]\n        ? nok(code)\n        : closingTag\n        ? completeClosingTagAfter(code)\n        : completeAttributeNameBefore(code)\n    }\n\n    // ASCII alphanumerical and `-`.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlphanumeric)(code)) {\n      effects.consume(code)\n      buffer += String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After closing slash of a basic tag name.\n   *\n   * ```markdown\n   * > | <div/>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function basicSelfClosing(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan) {\n      effects.consume(code)\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuation\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After closing slash of a complete tag name.\n   *\n   * ```markdown\n   * > | <x/>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeClosingTagAfter(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {\n      effects.consume(code)\n      return completeClosingTagAfter\n    }\n\n    return completeEnd(code)\n  }\n\n  /**\n   * At an attribute name.\n   *\n   * At first, this state is used after a complete tag name, after whitespace,\n   * where it expects optional attributes or the end of the tag.\n   * It is also reused after attributes, when expecting more optional\n   * attributes.\n   *\n   * ```markdown\n   * > | <a />\n   *        ^\n   * > | <a :b>\n   *        ^\n   * > | <a _b>\n   *        ^\n   * > | <a b>\n   *        ^\n   * > | <a >\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameBefore(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.slash) {\n      effects.consume(code)\n      return completeEnd\n    }\n\n    // ASCII alphanumerical and `:` and `_`.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.colon || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.underscore || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {\n      effects.consume(code)\n      return completeAttributeNameBefore\n    }\n\n    return completeEnd(code)\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | <a :b>\n   *         ^\n   * > | <a _b>\n   *         ^\n   * > | <a b>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeName(code) {\n    // ASCII alphanumerical and `-`, `.`, `:`, and `_`.\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dot ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.colon ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.underscore ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlphanumeric)(code)\n    ) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    return completeAttributeNameAfter(code)\n  }\n\n  /**\n   * After attribute name, at an optional initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b>\n   *         ^\n   * > | <a b=c>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameAfter(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.equalsTo) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {\n      effects.consume(code)\n      return completeAttributeNameAfter\n    }\n\n    return completeAttributeNameBefore(code)\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * > | <a b=\"c\">\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueBefore(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.lessThan ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.equalsTo ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.quotationMark || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.apostrophe) {\n      effects.consume(code)\n      markerB = code\n      return completeAttributeValueQuoted\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    return completeAttributeValueUnquoted(code)\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *           ^\n   * > | <a b='c'>\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuoted(code) {\n    if (code === markerB) {\n      effects.consume(code)\n      markerB = null\n      return completeAttributeValueQuotedAfter\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueQuoted\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueUnquoted(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.quotationMark ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.apostrophe ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.slash ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.lessThan ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.equalsTo ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.graveAccent ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEndingOrSpace)(code)\n    ) {\n      return completeAttributeNameAfter(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueUnquoted\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the\n   * end of the tag.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuotedAfter(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.slash ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)\n    ) {\n      return completeAttributeNameBefore(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In certain circumstances of a complete tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeEnd(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `>` in a complete tag.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAfter(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return continuation(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In continuation of any HTML kind.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuation(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash && marker === micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComment) {\n      effects.consume(code)\n      return continuationCommentInside\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.lessThan && marker === micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlRaw) {\n      effects.consume(code)\n      return continuationRawTagOpen\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan && marker === micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlDeclaration) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.questionMark && marker === micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlInstruction) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.rightSquareBracket && marker === micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlCdata) {\n      effects.consume(code)\n      return continuationCdataInside\n    }\n\n    if (\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code) &&\n      (marker === micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlBasic || marker === micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComplete)\n    ) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.htmlFlowData)\n      return effects.check(\n        blankLineBefore,\n        continuationAfter,\n        continuationStart\n      )(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.htmlFlowData)\n      return continuationStart(code)\n    }\n\n    effects.consume(code)\n    return continuation\n  }\n\n  /**\n   * In continuation, at eol.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStart(code) {\n    return effects.check(\n      nonLazyContinuationStart,\n      continuationStartNonLazy,\n      continuationAfter\n    )(code)\n  }\n\n  /**\n   * In continuation, at eol, before non-lazy content.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStartNonLazy(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code))\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n    return continuationBefore\n  }\n\n  /**\n   * In continuation, before non-lazy content.\n   *\n   * ```markdown\n   *   | <x>\n   * > | asd\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationBefore(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      return continuationStart(code)\n    }\n\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.htmlFlowData)\n    return continuation(code)\n  }\n\n  /**\n   * In comment continuation, after one `-`, expecting another.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCommentInside(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In raw continuation, after `<`, at `/`.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawTagOpen(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.slash) {\n      effects.consume(code)\n      buffer = ''\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In raw continuation, after `</`, in a raw tag name.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                             ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawEndTag(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan) {\n      const name = buffer.toLowerCase()\n\n      if (micromark_util_html_tag_name__WEBPACK_IMPORTED_MODULE_5__.htmlRawNames.includes(name)) {\n        effects.consume(code)\n        return continuationClose\n      }\n\n      return continuation(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.asciiAlpha)(code) && buffer.length < micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlRawSizeMax) {\n      effects.consume(code)\n      // @ts-expect-error: not null.\n      buffer += String.fromCharCode(code)\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In cdata continuation, after `]`, expecting `]>`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *                  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCdataInside(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.rightSquareBracket) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In declaration or instruction continuation, at `>`.\n   *\n   * ```markdown\n   * > | <!-->\n   *         ^\n   * > | <?>\n   *       ^\n   * > | <!q>\n   *        ^\n   * > | <!--ab-->\n   *             ^\n   * > | <![CDATA[>&<]]>\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationDeclarationInside(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.greaterThan) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    // More dashes.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash && marker === micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.htmlComment) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In closed continuation: everything we get until the eol/eof is part of it.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationClose(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.htmlFlowData)\n      return continuationAfter(code)\n    }\n\n    effects.consume(code)\n    return continuationClose\n  }\n\n  /**\n   * Done.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationAfter(code) {\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.htmlFlow)\n    // // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    // // No longer concrete.\n    // tokenizer.concrete = false\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuationStart(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * At eol, before continuation.\n   *\n   * ```markdown\n   * > | * ```js\n   *            ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * A continuation.\n   *\n   * ```markdown\n   *   | * ```js\n   * > | b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLineBefore(effects, ok, nok) {\n  return start\n\n  /**\n   * Before eol, expecting blank line.\n   *\n   * ```markdown\n   * > | <div>\n   *          ^\n   *   |\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), 'expected a line ending')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n    return effects.attempt(_blank_line_js__WEBPACK_IMPORTED_MODULE_6__.blankLine, ok, nok)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/html-flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/html-text.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/html-text.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   htmlText: () => (/* binding */ htmlText)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@1.1.0/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst htmlText = {name: 'htmlText', tokenize: tokenizeHtmlText}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlText(effects, ok, nok) {\n  const self = this\n  /** @type {NonNullable<Code> | undefined} */\n  let marker\n  /** @type {number} */\n  let index\n  /** @type {State} */\n  let returnState\n\n  return start\n\n  /**\n   * Start of HTML (text).\n   *\n   * ```markdown\n   * > | a <b> c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan, 'expected `<`')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.htmlText)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.htmlTextData)\n    effects.consume(code)\n    return open\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | a <b> c\n   *        ^\n   * > | a <!doctype> c\n   *        ^\n   * > | a <!--b--> c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.exclamationMark) {\n      effects.consume(code)\n      return declarationOpen\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.slash) {\n      effects.consume(code)\n      return tagCloseStart\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.questionMark) {\n      effects.consume(code)\n      return instruction\n    }\n\n    // ASCII alphabetical.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | a <!doctype> c\n   *         ^\n   * > | a <!--b--> c\n   *         ^\n   * > | a <![CDATA[>&<]]> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.consume(code)\n      return commentOpenInside\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.leftSquareBracket) {\n      effects.consume(code)\n      index = 0\n      return cdataOpenInside\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return declaration\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In a comment, after `<!-`, at another `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.consume(code)\n      return commentEnd\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In comment.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function comment(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.consume(code)\n      return commentClose\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = comment\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return comment\n  }\n\n  /**\n   * In comment, after `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentClose(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.consume(code)\n      return commentEnd\n    }\n\n    return comment(code)\n  }\n\n  /**\n   * In comment, after `--`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentEnd(code) {\n    return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan\n      ? end(code)\n      : code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dash\n      ? commentClose(code)\n      : comment(code)\n  }\n\n  /**\n   * After `<![`, in CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *          ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_4__.constants.cdataOpeningString\n\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code)\n      return index === value.length ? cdata : cdataOpenInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In CDATA.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdata(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataClose\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = cdata\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return cdata\n  }\n\n  /**\n   * In CDATA, after `]`, at another `]`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataClose(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  /**\n   * In CDATA, after `]]`, at `>`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataEnd(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      return end(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  /**\n   * In declaration.\n   *\n   * ```markdown\n   * > | a <!b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declaration(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      return end(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = declaration\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return declaration\n  }\n\n  /**\n   * In instruction.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instruction(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.questionMark) {\n      effects.consume(code)\n      return instructionClose\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = instruction\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return instruction\n  }\n\n  /**\n   * In instruction, after `?`, at `>`.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instructionClose(code) {\n    return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ? end(code) : instruction(code)\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    // ASCII alphabetical.\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `</x`, in a tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagClose(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dash || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return tagCloseBetween(code)\n  }\n\n  /**\n   * In closing tag, after tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseBetween(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = tagCloseBetween\n      return lineEndingBefore(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      effects.consume(code)\n      return tagCloseBetween\n    }\n\n    return end(code)\n  }\n\n  /**\n   * After `<x`, in opening tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpen(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dash || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEndingOrSpace)(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In opening tag, after tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenBetween(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.slash) {\n      effects.consume(code)\n      return end\n    }\n\n    // ASCII alphabetical and `:` and `_`.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.colon || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.underscore || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlpha)(code)) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = tagOpenBetween\n      return lineEndingBefore(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      effects.consume(code)\n      return tagOpenBetween\n    }\n\n    return end(code)\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeName(code) {\n    // ASCII alphabetical and `-`, `.`, `:`, and `_`.\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dash ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.dot ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.colon ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.underscore ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiAlphanumeric)(code)\n    ) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    return tagOpenAttributeNameAfter(code)\n  }\n\n  /**\n   * After attribute name, before initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeNameAfter(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.equalsTo) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = tagOpenAttributeNameAfter\n      return lineEndingBefore(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      effects.consume(code)\n      return tagOpenAttributeNameAfter\n    }\n\n    return tagOpenBetween(code)\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueBefore(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.equalsTo ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.quotationMark || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.apostrophe) {\n      effects.consume(code)\n      marker = code\n      return tagOpenAttributeValueQuoted\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = tagOpenAttributeValueBefore\n      return lineEndingBefore(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueUnquoted\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuoted(code) {\n    if (code === marker) {\n      effects.consume(code)\n      marker = undefined\n      return tagOpenAttributeValueQuotedAfter\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      returnState = tagOpenAttributeValueQuoted\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueQuoted\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueUnquoted(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.quotationMark ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.apostrophe ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.lessThan ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.equalsTo ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEndingOrSpace)(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueUnquoted\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the end\n   * of the tag.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuotedAfter(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.slash ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEndingOrSpace)(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In certain circumstances of a tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function end(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_1__.codes.greaterThan) {\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.htmlTextData)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.htmlText)\n      return ok\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * At eol.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   * > | a <!--a\n   *            ^\n   *   | b-->\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingBefore(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(returnState, 'expected return state')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code), 'expected eol')\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.htmlTextData)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    return lineEndingAfter\n  }\n\n  /**\n   * After eol, at optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfter(code) {\n    // Always populated by defaults.\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(\n          effects,\n          lineEndingAfterPrefix,\n          micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix,\n          self.parser.constructs.disable.null.includes('codeIndented')\n            ? undefined\n            : micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize\n        )(code)\n      : lineEndingAfterPrefix(code)\n  }\n\n  /**\n   * After eol, after optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfterPrefix(code) {\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.htmlTextData)\n    return returnState(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/html-text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/label-end.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/label-end.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   labelEnd: () => (/* binding */ labelEnd)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_destination__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-factory-destination */ \"(ssr)/./node_modules/.pnpm/micromark-factory-destination@1.1.0/node_modules/micromark-factory-destination/dev/index.js\");\n/* harmony import */ var micromark_factory_label__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-factory-label */ \"(ssr)/./node_modules/.pnpm/micromark-factory-label@1.1.0/node_modules/micromark-factory-label/dev/index.js\");\n/* harmony import */ var micromark_factory_title__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-factory-title */ \"(ssr)/./node_modules/.pnpm/micromark-factory-title@1.1.0/node_modules/micromark-factory-title/dev/index.js\");\n/* harmony import */ var micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-factory-whitespace */ \"(ssr)/./node_modules/.pnpm/micromark-factory-whitespace@1.1.0/node_modules/micromark-factory-whitespace/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/.pnpm/micromark-util-chunked@1.1.0/node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/.pnpm/micromark-util-normalize-identifier@1.1.0/node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-resolve-all */ \"(ssr)/./node_modules/.pnpm/micromark-util-resolve-all@1.1.0/node_modules/micromark-util-resolve-all/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst labelEnd = {\n  name: 'labelEnd',\n  tokenize: tokenizeLabelEnd,\n  resolveTo: resolveToLabelEnd,\n  resolveAll: resolveAllLabelEnd\n}\n\n/** @type {Construct} */\nconst resourceConstruct = {tokenize: tokenizeResource}\n/** @type {Construct} */\nconst referenceFullConstruct = {tokenize: tokenizeReferenceFull}\n/** @type {Construct} */\nconst referenceCollapsedConstruct = {tokenize: tokenizeReferenceCollapsed}\n\n/** @type {Resolver} */\nfunction resolveAllLabelEnd(events) {\n  let index = -1\n\n  while (++index < events.length) {\n    const token = events[index][1]\n\n    if (\n      token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelImage ||\n      token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelLink ||\n      token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelEnd\n    ) {\n      // Remove the marker.\n      events.splice(index + 1, token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelImage ? 4 : 2)\n      token.type = micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.data\n      index++\n    }\n  }\n\n  return events\n}\n\n/** @type {Resolver} */\nfunction resolveToLabelEnd(events, context) {\n  let index = events.length\n  let offset = 0\n  /** @type {Token} */\n  let token\n  /** @type {number | undefined} */\n  let open\n  /** @type {number | undefined} */\n  let close\n  /** @type {Array<Event>} */\n  let media\n\n  // Find an opening.\n  while (index--) {\n    token = events[index][1]\n\n    if (open) {\n      // If we see another link, or inactive link label, we’ve been here before.\n      if (\n        token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.link ||\n        (token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelLink && token._inactive)\n      ) {\n        break\n      }\n\n      // Mark other link openings as inactive, as we can’t have links in\n      // links.\n      if (events[index][0] === 'enter' && token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelLink) {\n        token._inactive = true\n      }\n    } else if (close) {\n      if (\n        events[index][0] === 'enter' &&\n        (token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelImage || token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelLink) &&\n        !token._balanced\n      ) {\n        open = index\n\n        if (token.type !== micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelLink) {\n          offset = 2\n          break\n        }\n      }\n    } else if (token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelEnd) {\n      close = index\n    }\n  }\n\n  (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(open !== undefined, '`open` is supposed to be found')\n  ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(close !== undefined, '`close` is supposed to be found')\n\n  const group = {\n    type: events[open][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelLink ? micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.link : micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.image,\n    start: Object.assign({}, events[open][1].start),\n    end: Object.assign({}, events[events.length - 1][1].end)\n  }\n\n  const label = {\n    type: micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.label,\n    start: Object.assign({}, events[open][1].start),\n    end: Object.assign({}, events[close][1].end)\n  }\n\n  const text = {\n    type: micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelText,\n    start: Object.assign({}, events[open + offset + 2][1].end),\n    end: Object.assign({}, events[close - 2][1].start)\n  }\n\n  media = [\n    ['enter', group, context],\n    ['enter', label, context]\n  ]\n\n  // Opening marker.\n  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(media, events.slice(open + 1, open + offset + 3))\n\n  // Text open.\n  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(media, [['enter', text, context]])\n\n  // Always populated by defaults.\n  ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n    context.parser.constructs.insideSpan.null,\n    'expected `insideSpan.null` to be populated'\n  )\n  // Between.\n  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(\n    media,\n    (0,micromark_util_resolve_all__WEBPACK_IMPORTED_MODULE_3__.resolveAll)(\n      context.parser.constructs.insideSpan.null,\n      events.slice(open + offset + 4, close - 3),\n      context\n    )\n  )\n\n  // Text close, marker close, label close.\n  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(media, [\n    ['exit', text, context],\n    events[close - 2],\n    events[close - 1],\n    ['exit', label, context]\n  ])\n\n  // Reference, resource, or so.\n  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(media, events.slice(close + 1))\n\n  // Media close.\n  media = (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.push)(media, [['exit', group, context]])\n\n  ;(0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_2__.splice)(events, open, events.length, media)\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeLabelEnd(effects, ok, nok) {\n  const self = this\n  let index = self.events.length\n  /** @type {Token} */\n  let labelStart\n  /** @type {boolean} */\n  let defined\n\n  // Find an opening.\n  while (index--) {\n    if (\n      (self.events[index][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelImage ||\n        self.events[index][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelLink) &&\n      !self.events[index][1]._balanced\n    ) {\n      labelStart = self.events[index][1]\n      break\n    }\n  }\n\n  return start\n\n  /**\n   * Start of label end.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.rightSquareBracket, 'expected `]`')\n\n    // If there is not an okay opening.\n    if (!labelStart) {\n      return nok(code)\n    }\n\n    // If the corresponding label (link) start is marked as inactive,\n    // it means we’d be wrapping a link, like this:\n    //\n    // ```markdown\n    // > | a [b [c](d) e](f) g.\n    //                  ^\n    // ```\n    //\n    // We can’t have that, so it’s just balanced brackets.\n    if (labelStart._inactive) {\n      return labelEndNok(code)\n    }\n\n    defined = self.parser.defined.includes(\n      (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_5__.normalizeIdentifier)(\n        self.sliceSerialize({start: labelStart.end, end: self.now()})\n      )\n    )\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelEnd)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelMarker)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.labelEnd)\n    return after\n  }\n\n  /**\n   * After `]`.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Note: `markdown-rs` also parses GFM footnotes here, which for us is in\n    // an extension.\n\n    // Resource (`[asd](fgh)`)?\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.leftParenthesis) {\n      return effects.attempt(\n        resourceConstruct,\n        labelEndOk,\n        defined ? labelEndOk : labelEndNok\n      )(code)\n    }\n\n    // Full (`[asd][fgh]`) or collapsed (`[asd][]`) reference?\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.leftSquareBracket) {\n      return effects.attempt(\n        referenceFullConstruct,\n        labelEndOk,\n        defined ? referenceNotFull : labelEndNok\n      )(code)\n    }\n\n    // Shortcut (`[asd]`) reference?\n    return defined ? labelEndOk(code) : labelEndNok(code)\n  }\n\n  /**\n   * After `]`, at `[`, but not at a full reference.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] b\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceNotFull(code) {\n    return effects.attempt(\n      referenceCollapsedConstruct,\n      labelEndOk,\n      labelEndNok\n    )(code)\n  }\n\n  /**\n   * Done, we found something.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *           ^\n   * > | [a][b] c\n   *           ^\n   * > | [a][] b\n   *          ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndOk(code) {\n    // Note: `markdown-rs` does a bunch of stuff here.\n    return ok(code)\n  }\n\n  /**\n   * Done, it’s nothing.\n   *\n   * There was an okay opening, but we didn’t match anything.\n   *\n   * ```markdown\n   * > | [a](b c\n   *        ^\n   * > | [a][b c\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndNok(code) {\n    labelStart._balanced = true\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeResource(effects, ok, nok) {\n  return resourceStart\n\n  /**\n   * At a resource.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceStart(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.leftParenthesis, 'expected left paren')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resource)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resourceMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resourceMarker)\n    return resourceBefore\n  }\n\n  /**\n   * In resource, after `(`, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBefore(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEndingOrSpace)(code)\n      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_7__.factoryWhitespace)(effects, resourceOpen)(code)\n      : resourceOpen(code)\n  }\n\n  /**\n   * In resource, after optional whitespace, at `)` or a destination.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceOpen(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.rightParenthesis) {\n      return resourceEnd(code)\n    }\n\n    return (0,micromark_factory_destination__WEBPACK_IMPORTED_MODULE_8__.factoryDestination)(\n      effects,\n      resourceDestinationAfter,\n      resourceDestinationMissing,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resourceDestination,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resourceDestinationLiteral,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resourceDestinationLiteralMarker,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resourceDestinationRaw,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resourceDestinationString,\n      micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_9__.constants.linkResourceDestinationBalanceMax\n    )(code)\n  }\n\n  /**\n   * In resource, after destination, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationAfter(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEndingOrSpace)(code)\n      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_7__.factoryWhitespace)(effects, resourceBetween)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * At invalid destination.\n   *\n   * ```markdown\n   * > | [a](<<) b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationMissing(code) {\n    return nok(code)\n  }\n\n  /**\n   * In resource, after destination and whitespace, at `(` or title.\n   *\n   * ```markdown\n   * > | [a](b ) c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBetween(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.quotationMark ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.apostrophe ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.leftParenthesis\n    ) {\n      return (0,micromark_factory_title__WEBPACK_IMPORTED_MODULE_10__.factoryTitle)(\n        effects,\n        resourceTitleAfter,\n        nok,\n        micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resourceTitle,\n        micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resourceTitleMarker,\n        micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resourceTitleString\n      )(code)\n    }\n\n    return resourceEnd(code)\n  }\n\n  /**\n   * In resource, after title, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b \"c\") d\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceTitleAfter(code) {\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_6__.markdownLineEndingOrSpace)(code)\n      ? (0,micromark_factory_whitespace__WEBPACK_IMPORTED_MODULE_7__.factoryWhitespace)(effects, resourceEnd)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * In resource, at `)`.\n   *\n   * ```markdown\n   * > | [a](b) d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceEnd(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.rightParenthesis) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resourceMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resourceMarker)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.resource)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceFull(effects, ok, nok) {\n  const self = this\n\n  return referenceFull\n\n  /**\n   * In a reference (full), at the `[`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFull(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.leftSquareBracket, 'expected left bracket')\n    return micromark_factory_label__WEBPACK_IMPORTED_MODULE_11__.factoryLabel.call(\n      self,\n      effects,\n      referenceFullAfter,\n      referenceFullMissing,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.reference,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.referenceMarker,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.referenceString\n    )(code)\n  }\n\n  /**\n   * In a reference (full), after `]`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullAfter(code) {\n    return self.parser.defined.includes(\n      (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_5__.normalizeIdentifier)(\n        self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n      )\n    )\n      ? ok(code)\n      : nok(code)\n  }\n\n  /**\n   * In reference (full) that was missing.\n   *\n   * ```markdown\n   * > | [a][b d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullMissing(code) {\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceCollapsed(effects, ok, nok) {\n  return referenceCollapsedStart\n\n  /**\n   * In reference (collapsed), at `[`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceCollapsedStart(code) {\n    // We only attempt a collapsed label if there’s a `[`.\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.leftSquareBracket, 'expected left bracket')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.reference)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.referenceMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.referenceMarker)\n    return referenceCollapsedOpen\n  }\n\n  /**\n   * In reference (collapsed), at `]`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *         ^\n   * ```\n   *\n   *  @type {State}\n   */\n  function referenceCollapsedOpen(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_4__.codes.rightSquareBracket) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.referenceMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.referenceMarker)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.reference)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/label-end.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/label-start-image.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/label-start-image.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   labelStartImage: () => (/* binding */ labelStartImage)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/* harmony import */ var _label_end_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./label-end.js */ \"(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/label-end.js\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst labelStartImage = {\n  name: 'labelStartImage',\n  tokenize: tokenizeLabelStartImage,\n  resolveAll: _label_end_js__WEBPACK_IMPORTED_MODULE_1__.labelEnd.resolveAll\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartImage(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of label (image) start.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.exclamationMark, 'expected `!`')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_3__.types.labelImage)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_3__.types.labelImageMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_3__.types.labelImageMarker)\n    return open\n  }\n\n  /**\n   * After `!`, at `[`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.leftSquareBracket) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_3__.types.labelMarker)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_3__.types.labelMarker)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_3__.types.labelImage)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `![`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *         ^\n   * ```\n   *\n   * This is needed in because, when GFM footnotes are enabled, images never\n   * form when started with a `^`.\n   * Instead, links form:\n   *\n   * ```markdown\n   * ![^a](b)\n   *\n   * ![^a][b]\n   *\n   * [b]: c\n   * ```\n   *\n   * ```html\n   * <p>!<a href=\\\"b\\\">^a</a></p>\n   * <p>!<a href=\\\"c\\\">^a</a></p>\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // To do: use a new field to do this, this is still needed for\n    // `micromark-extension-gfm-footnote`, but the `label-start-link`\n    // behavior isn’t.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.caret &&\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? nok(code)\n      : ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/label-start-image.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/label-start-link.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/label-start-link.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   labelStartLink: () => (/* binding */ labelStartLink)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/* harmony import */ var _label_end_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./label-end.js */ \"(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/label-end.js\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst labelStartLink = {\n  name: 'labelStartLink',\n  tokenize: tokenizeLabelStartLink,\n  resolveAll: _label_end_js__WEBPACK_IMPORTED_MODULE_1__.labelEnd.resolveAll\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartLink(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of label (link) start.\n   *\n   * ```markdown\n   * > | a [b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.leftSquareBracket, 'expected `[`')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_3__.types.labelLink)\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_3__.types.labelMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_3__.types.labelMarker)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_3__.types.labelLink)\n    return after\n  }\n\n  /** @type {State} */\n  function after(code) {\n    // To do: this isn’t needed in `micromark-extension-gfm-footnote`,\n    // remove.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.caret &&\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? nok(code)\n      : ok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/label-start-link.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/line-ending.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/line-ending.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   lineEnding: () => (/* binding */ lineEnding)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@1.1.0/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n/** @type {Construct} */\nconst lineEnding = {name: 'lineEnding', tokenize: tokenizeLineEnding}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeLineEnding(effects, ok) {\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_1__.markdownLineEnding)(code), 'expected eol')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.lineEnding)\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, ok, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_2__.types.linePrefix)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/line-ending.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/list.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/list.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   list: () => (/* binding */ list)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@1.1.0/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/* harmony import */ var _blank_line_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./blank-line.js */ \"(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/* harmony import */ var _thematic_break_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./thematic-break.js */ \"(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/thematic-break.js\");\n/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').ContainerState} ContainerState\n * @typedef {import('micromark-util-types').Exiter} Exiter\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst list = {\n  name: 'list',\n  tokenize: tokenizeListStart,\n  continuation: {tokenize: tokenizeListContinuation},\n  exit: tokenizeListEnd\n}\n\n/** @type {Construct} */\nconst listItemPrefixWhitespaceConstruct = {\n  tokenize: tokenizeListItemPrefixWhitespace,\n  partial: true\n}\n\n/** @type {Construct} */\nconst indentConstruct = {tokenize: tokenizeIndent, partial: true}\n\n// To do: `markdown-rs` parses list items on their own and later stitches them\n// together.\n\n/**\n * @type {Tokenizer}\n * @this {TokenizeContext}\n */\nfunction tokenizeListStart(effects, ok, nok) {\n  const self = this\n  const tail = self.events[self.events.length - 1]\n  let initialSize =\n    tail && tail[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix\n      ? tail[2].sliceSerialize(tail[1], true).length\n      : 0\n  let size = 0\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, 'expected state')\n    const kind =\n      self.containerState.type ||\n      (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.asterisk || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.plusSign || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash\n        ? micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listUnordered\n        : micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listOrdered)\n\n    if (\n      kind === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listUnordered\n        ? !self.containerState.marker || code === self.containerState.marker\n        : (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiDigit)(code)\n    ) {\n      if (!self.containerState.type) {\n        self.containerState.type = kind\n        effects.enter(kind, {_container: true})\n      }\n\n      if (kind === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listUnordered) {\n        effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemPrefix)\n        return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.asterisk || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash\n          ? effects.check(_thematic_break_js__WEBPACK_IMPORTED_MODULE_4__.thematicBreak, nok, atMarker)(code)\n          : atMarker(code)\n      }\n\n      if (!self.interrupt || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.digit1) {\n        effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemPrefix)\n        effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemValue)\n        return inside(code)\n      }\n    }\n\n    return nok(code)\n  }\n\n  /** @type {State} */\n  function inside(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, 'expected state')\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.asciiDigit)(code) && ++size < micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__.constants.listItemValueSizeMax) {\n      effects.consume(code)\n      return inside\n    }\n\n    if (\n      (!self.interrupt || size < 2) &&\n      (self.containerState.marker\n        ? code === self.containerState.marker\n        : code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.rightParenthesis || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dot)\n    ) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemValue)\n      return atMarker(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * @type {State}\n   **/\n  function atMarker(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, 'expected state')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(code !== micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof, 'eof (`null`) is not a marker')\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemMarker)\n    effects.consume(code)\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemMarker)\n    self.containerState.marker = self.containerState.marker || code\n    return effects.check(\n      _blank_line_js__WEBPACK_IMPORTED_MODULE_6__.blankLine,\n      // Can’t be empty when interrupting.\n      self.interrupt ? nok : onBlank,\n      effects.attempt(\n        listItemPrefixWhitespaceConstruct,\n        endOfPrefix,\n        otherPrefix\n      )\n    )\n  }\n\n  /** @type {State} */\n  function onBlank(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, 'expected state')\n    self.containerState.initialBlankLine = true\n    initialSize++\n    return endOfPrefix(code)\n  }\n\n  /** @type {State} */\n  function otherPrefix(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemPrefixWhitespace)\n      effects.consume(code)\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemPrefixWhitespace)\n      return endOfPrefix\n    }\n\n    return nok(code)\n  }\n\n  /** @type {State} */\n  function endOfPrefix(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, 'expected state')\n    self.containerState.size =\n      initialSize +\n      self.sliceSerialize(effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemPrefix), true).length\n    return ok(code)\n  }\n}\n\n/**\n * @type {Tokenizer}\n * @this {TokenizeContext}\n */\nfunction tokenizeListContinuation(effects, ok, nok) {\n  const self = this\n\n  ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, 'expected state')\n  self.containerState._closeFlow = undefined\n\n  return effects.check(_blank_line_js__WEBPACK_IMPORTED_MODULE_6__.blankLine, onBlank, notBlank)\n\n  /** @type {State} */\n  function onBlank(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, 'expected state')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof self.containerState.size === 'number', 'expected size')\n    self.containerState.furtherBlankLines =\n      self.containerState.furtherBlankLines ||\n      self.containerState.initialBlankLine\n\n    // We have a blank line.\n    // Still, try to consume at most the items size.\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__.factorySpace)(\n      effects,\n      ok,\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemIndent,\n      self.containerState.size + 1\n    )(code)\n  }\n\n  /** @type {State} */\n  function notBlank(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, 'expected state')\n    if (self.containerState.furtherBlankLines || !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      self.containerState.furtherBlankLines = undefined\n      self.containerState.initialBlankLine = undefined\n      return notInCurrentItem(code)\n    }\n\n    self.containerState.furtherBlankLines = undefined\n    self.containerState.initialBlankLine = undefined\n    return effects.attempt(indentConstruct, ok, notInCurrentItem)(code)\n  }\n\n  /** @type {State} */\n  function notInCurrentItem(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, 'expected state')\n    // While we do continue, we signal that the flow should be closed.\n    self.containerState._closeFlow = true\n    // As we’re closing flow, we’re no longer interrupting.\n    self.interrupt = undefined\n    // Always populated by defaults.\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n    return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__.factorySpace)(\n      effects,\n      effects.attempt(list, ok, nok),\n      micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix,\n      self.parser.constructs.disable.null.includes('codeIndented')\n        ? undefined\n        : micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n    )(code)\n  }\n}\n\n/**\n * @type {Tokenizer}\n * @this {TokenizeContext}\n */\nfunction tokenizeIndent(effects, ok, nok) {\n  const self = this\n\n  ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, 'expected state')\n  ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof self.containerState.size === 'number', 'expected size')\n\n  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__.factorySpace)(\n    effects,\n    afterPrefix,\n    micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemIndent,\n    self.containerState.size + 1\n  )\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(self.containerState, 'expected state')\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemIndent &&\n      tail[2].sliceSerialize(tail[1], true).length === self.containerState.size\n      ? ok(code)\n      : nok(code)\n  }\n}\n\n/**\n * @type {Exiter}\n * @this {TokenizeContext}\n */\nfunction tokenizeListEnd(effects) {\n  (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(this.containerState, 'expected state')\n  ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof this.containerState.type === 'string', 'expected type')\n  effects.exit(this.containerState.type)\n}\n\n/**\n * @type {Tokenizer}\n * @this {TokenizeContext}\n */\nfunction tokenizeListItemPrefixWhitespace(effects, ok, nok) {\n  const self = this\n\n  // Always populated by defaults.\n  ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n    self.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n\n  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_7__.factorySpace)(\n    effects,\n    afterPrefix,\n    micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemPrefixWhitespace,\n    self.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize + 1\n  )\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n\n    return !(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code) &&\n      tail &&\n      tail[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.listItemPrefixWhitespace\n      ? ok(code)\n      : nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/setext-underline.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/setext-underline.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   setextUnderline: () => (/* binding */ setextUnderline)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@1.1.0/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst setextUnderline = {\n  name: 'setextUnderline',\n  tokenize: tokenizeSetextUnderline,\n  resolveTo: resolveToSetextUnderline\n}\n\n/** @type {Resolver} */\nfunction resolveToSetextUnderline(events, context) {\n  // To do: resolve like `markdown-rs`.\n  let index = events.length\n  /** @type {number | undefined} */\n  let content\n  /** @type {number | undefined} */\n  let text\n  /** @type {number | undefined} */\n  let definition\n\n  // Find the opening of the content.\n  // It’ll always exist: we don’t tokenize if it isn’t there.\n  while (index--) {\n    if (events[index][0] === 'enter') {\n      if (events[index][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.content) {\n        content = index\n        break\n      }\n\n      if (events[index][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.paragraph) {\n        text = index\n      }\n    }\n    // Exit\n    else {\n      if (events[index][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.content) {\n        // Remove the content end (if needed we’ll add it later)\n        events.splice(index, 1)\n      }\n\n      if (!definition && events[index][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.definition) {\n        definition = index\n      }\n    }\n  }\n\n  (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(text !== undefined, 'expected a `text` index to be found')\n  ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(content !== undefined, 'expected a `text` index to be found')\n\n  const heading = {\n    type: micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.setextHeading,\n    start: Object.assign({}, events[text][1].start),\n    end: Object.assign({}, events[events.length - 1][1].end)\n  }\n\n  // Change the paragraph to setext heading text.\n  events[text][1].type = micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.setextHeadingText\n\n  // If we have definitions in the content, we’ll keep on having content,\n  // but we need move it.\n  if (definition) {\n    events.splice(text, 0, ['enter', heading, context])\n    events.splice(definition + 1, 0, ['exit', events[content][1], context])\n    events[content][1].end = Object.assign({}, events[definition][1].end)\n  } else {\n    events[content][1] = heading\n  }\n\n  // Add the heading exit at the end.\n  events.push(['exit', heading, context])\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeSetextUnderline(effects, ok, nok) {\n  const self = this\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * At start of heading (setext) underline.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length\n    /** @type {boolean | undefined} */\n    let paragraph\n\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.equalsTo,\n      'expected `=` or `-`'\n    )\n\n    // Find an opening.\n    while (index--) {\n      // Skip enter/exit of line ending, line prefix, and content.\n      // We can now either have a definition or a paragraph.\n      if (\n        self.events[index][1].type !== micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding &&\n        self.events[index][1].type !== micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix &&\n        self.events[index][1].type !== micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.content\n      ) {\n        paragraph = self.events[index][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.paragraph\n        break\n      }\n    }\n\n    // To do: handle lazy/pierce like `markdown-rs`.\n    // To do: parse indent like `markdown-rs`.\n    if (!self.parser.lazy[self.now().line] && (self.interrupt || paragraph)) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.setextHeadingLine)\n      marker = code\n      return before(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After optional whitespace, at `-` or `=`.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.setextHeadingLineSequence)\n    return inside(code)\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return inside\n    }\n\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.setextHeadingLineSequence)\n\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, after, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineSuffix)(code)\n      : after(code)\n  }\n\n  /**\n   * After sequence, after optional whitespace.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.setextHeadingLine)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWljcm9tYXJrLWNvcmUtY29tbW9ubWFya0AxLjEuMC9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWNvcmUtY29tbW9ubWFyay9kZXYvbGliL3NldGV4dC11bmRlcmxpbmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7QUFDQSxhQUFhLHFDQUFxQztBQUNsRCxhQUFhLDBDQUEwQztBQUN2RCxhQUFhLHlDQUF5QztBQUN0RCxhQUFhLHNDQUFzQztBQUNuRCxhQUFhLGdEQUFnRDtBQUM3RCxhQUFhLDBDQUEwQztBQUN2RDs7QUFFb0Q7QUFDc0I7QUFDdEI7QUFDQTtBQUNiOztBQUV2QyxXQUFXLFdBQVc7QUFDZjtBQUNQO0FBQ0E7QUFDQTtBQUNBOztBQUVBLFdBQVcsVUFBVTtBQUNyQjtBQUNBO0FBQ0E7QUFDQSxhQUFhLG9CQUFvQjtBQUNqQztBQUNBLGFBQWEsb0JBQW9CO0FBQ2pDO0FBQ0EsYUFBYSxvQkFBb0I7QUFDakM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsaUVBQUs7QUFDekM7QUFDQTtBQUNBOztBQUVBLG9DQUFvQyxpRUFBSztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLGlFQUFLO0FBQ3pDO0FBQ0E7QUFDQTs7QUFFQSxtREFBbUQsaUVBQUs7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsRUFBRSw4Q0FBTTtBQUNSLEVBQUUsK0NBQU07O0FBRVI7QUFDQSxVQUFVLGlFQUFLO0FBQ2YsMkJBQTJCO0FBQzNCLHlCQUF5QjtBQUN6Qjs7QUFFQTtBQUNBLHlCQUF5QixpRUFBSzs7QUFFOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QztBQUM3QyxJQUFJO0FBQ0o7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxVQUFVO0FBQ1YsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLGFBQWEsbUJBQW1CO0FBQ2hDOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxlQUFlLHFCQUFxQjtBQUNwQzs7QUFFQSxJQUFJLCtDQUFNO0FBQ1YsZUFBZSxpRUFBSyxrQkFBa0IsaUVBQUs7QUFDM0M7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLGlFQUFLO0FBQzVDLHVDQUF1QyxpRUFBSztBQUM1Qyx1Q0FBdUMsaUVBQUs7QUFDNUM7QUFDQSxtREFBbUQsaUVBQUs7QUFDeEQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixpRUFBSztBQUN6QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLGtCQUFrQixpRUFBSztBQUN2QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsaUJBQWlCLGlFQUFLOztBQUV0QixXQUFXLHVFQUFhO0FBQ3hCLFFBQVEscUVBQVksaUJBQWlCLGlFQUFLO0FBQzFDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxpQkFBaUIsaUVBQUssUUFBUSw0RUFBa0I7QUFDaEQsbUJBQW1CLGlFQUFLO0FBQ3hCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vbWljcm9tYXJrLWNvcmUtY29tbW9ubWFya0AxLjEuMC9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWNvcmUtY29tbW9ubWFyay9kZXYvbGliL3NldGV4dC11bmRlcmxpbmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtaWNyb21hcmstdXRpbC10eXBlcycpLkNvZGV9IENvZGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21pY3JvbWFyay11dGlsLXR5cGVzJykuQ29uc3RydWN0fSBDb25zdHJ1Y3RcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21pY3JvbWFyay11dGlsLXR5cGVzJykuUmVzb2x2ZXJ9IFJlc29sdmVyXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtaWNyb21hcmstdXRpbC10eXBlcycpLlN0YXRlfSBTdGF0ZVxuICogQHR5cGVkZWYge2ltcG9ydCgnbWljcm9tYXJrLXV0aWwtdHlwZXMnKS5Ub2tlbml6ZUNvbnRleHR9IFRva2VuaXplQ29udGV4dFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWljcm9tYXJrLXV0aWwtdHlwZXMnKS5Ub2tlbml6ZXJ9IFRva2VuaXplclxuICovXG5cbmltcG9ydCB7ZmFjdG9yeVNwYWNlfSBmcm9tICdtaWNyb21hcmstZmFjdG9yeS1zcGFjZSdcbmltcG9ydCB7bWFya2Rvd25MaW5lRW5kaW5nLCBtYXJrZG93blNwYWNlfSBmcm9tICdtaWNyb21hcmstdXRpbC1jaGFyYWN0ZXInXG5pbXBvcnQge2NvZGVzfSBmcm9tICdtaWNyb21hcmstdXRpbC1zeW1ib2wvY29kZXMuanMnXG5pbXBvcnQge3R5cGVzfSBmcm9tICdtaWNyb21hcmstdXRpbC1zeW1ib2wvdHlwZXMuanMnXG5pbXBvcnQge29rIGFzIGFzc2VydH0gZnJvbSAndXZ1L2Fzc2VydCdcblxuLyoqIEB0eXBlIHtDb25zdHJ1Y3R9ICovXG5leHBvcnQgY29uc3Qgc2V0ZXh0VW5kZXJsaW5lID0ge1xuICBuYW1lOiAnc2V0ZXh0VW5kZXJsaW5lJyxcbiAgdG9rZW5pemU6IHRva2VuaXplU2V0ZXh0VW5kZXJsaW5lLFxuICByZXNvbHZlVG86IHJlc29sdmVUb1NldGV4dFVuZGVybGluZVxufVxuXG4vKiogQHR5cGUge1Jlc29sdmVyfSAqL1xuZnVuY3Rpb24gcmVzb2x2ZVRvU2V0ZXh0VW5kZXJsaW5lKGV2ZW50cywgY29udGV4dCkge1xuICAvLyBUbyBkbzogcmVzb2x2ZSBsaWtlIGBtYXJrZG93bi1yc2AuXG4gIGxldCBpbmRleCA9IGV2ZW50cy5sZW5ndGhcbiAgLyoqIEB0eXBlIHtudW1iZXIgfCB1bmRlZmluZWR9ICovXG4gIGxldCBjb250ZW50XG4gIC8qKiBAdHlwZSB7bnVtYmVyIHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgdGV4dFxuICAvKiogQHR5cGUge251bWJlciB8IHVuZGVmaW5lZH0gKi9cbiAgbGV0IGRlZmluaXRpb25cblxuICAvLyBGaW5kIHRoZSBvcGVuaW5nIG9mIHRoZSBjb250ZW50LlxuICAvLyBJdOKAmWxsIGFsd2F5cyBleGlzdDogd2UgZG9u4oCZdCB0b2tlbml6ZSBpZiBpdCBpc27igJl0IHRoZXJlLlxuICB3aGlsZSAoaW5kZXgtLSkge1xuICAgIGlmIChldmVudHNbaW5kZXhdWzBdID09PSAnZW50ZXInKSB7XG4gICAgICBpZiAoZXZlbnRzW2luZGV4XVsxXS50eXBlID09PSB0eXBlcy5jb250ZW50KSB7XG4gICAgICAgIGNvbnRlbnQgPSBpbmRleFxuICAgICAgICBicmVha1xuICAgICAgfVxuXG4gICAgICBpZiAoZXZlbnRzW2luZGV4XVsxXS50eXBlID09PSB0eXBlcy5wYXJhZ3JhcGgpIHtcbiAgICAgICAgdGV4dCA9IGluZGV4XG4gICAgICB9XG4gICAgfVxuICAgIC8vIEV4aXRcbiAgICBlbHNlIHtcbiAgICAgIGlmIChldmVudHNbaW5kZXhdWzFdLnR5cGUgPT09IHR5cGVzLmNvbnRlbnQpIHtcbiAgICAgICAgLy8gUmVtb3ZlIHRoZSBjb250ZW50IGVuZCAoaWYgbmVlZGVkIHdl4oCZbGwgYWRkIGl0IGxhdGVyKVxuICAgICAgICBldmVudHMuc3BsaWNlKGluZGV4LCAxKVxuICAgICAgfVxuXG4gICAgICBpZiAoIWRlZmluaXRpb24gJiYgZXZlbnRzW2luZGV4XVsxXS50eXBlID09PSB0eXBlcy5kZWZpbml0aW9uKSB7XG4gICAgICAgIGRlZmluaXRpb24gPSBpbmRleFxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIGFzc2VydCh0ZXh0ICE9PSB1bmRlZmluZWQsICdleHBlY3RlZCBhIGB0ZXh0YCBpbmRleCB0byBiZSBmb3VuZCcpXG4gIGFzc2VydChjb250ZW50ICE9PSB1bmRlZmluZWQsICdleHBlY3RlZCBhIGB0ZXh0YCBpbmRleCB0byBiZSBmb3VuZCcpXG5cbiAgY29uc3QgaGVhZGluZyA9IHtcbiAgICB0eXBlOiB0eXBlcy5zZXRleHRIZWFkaW5nLFxuICAgIHN0YXJ0OiBPYmplY3QuYXNzaWduKHt9LCBldmVudHNbdGV4dF1bMV0uc3RhcnQpLFxuICAgIGVuZDogT2JqZWN0LmFzc2lnbih7fSwgZXZlbnRzW2V2ZW50cy5sZW5ndGggLSAxXVsxXS5lbmQpXG4gIH1cblxuICAvLyBDaGFuZ2UgdGhlIHBhcmFncmFwaCB0byBzZXRleHQgaGVhZGluZyB0ZXh0LlxuICBldmVudHNbdGV4dF1bMV0udHlwZSA9IHR5cGVzLnNldGV4dEhlYWRpbmdUZXh0XG5cbiAgLy8gSWYgd2UgaGF2ZSBkZWZpbml0aW9ucyBpbiB0aGUgY29udGVudCwgd2XigJlsbCBrZWVwIG9uIGhhdmluZyBjb250ZW50LFxuICAvLyBidXQgd2UgbmVlZCBtb3ZlIGl0LlxuICBpZiAoZGVmaW5pdGlvbikge1xuICAgIGV2ZW50cy5zcGxpY2UodGV4dCwgMCwgWydlbnRlcicsIGhlYWRpbmcsIGNvbnRleHRdKVxuICAgIGV2ZW50cy5zcGxpY2UoZGVmaW5pdGlvbiArIDEsIDAsIFsnZXhpdCcsIGV2ZW50c1tjb250ZW50XVsxXSwgY29udGV4dF0pXG4gICAgZXZlbnRzW2NvbnRlbnRdWzFdLmVuZCA9IE9iamVjdC5hc3NpZ24oe30sIGV2ZW50c1tkZWZpbml0aW9uXVsxXS5lbmQpXG4gIH0gZWxzZSB7XG4gICAgZXZlbnRzW2NvbnRlbnRdWzFdID0gaGVhZGluZ1xuICB9XG5cbiAgLy8gQWRkIHRoZSBoZWFkaW5nIGV4aXQgYXQgdGhlIGVuZC5cbiAgZXZlbnRzLnB1c2goWydleGl0JywgaGVhZGluZywgY29udGV4dF0pXG5cbiAgcmV0dXJuIGV2ZW50c1xufVxuXG4vKipcbiAqIEB0aGlzIHtUb2tlbml6ZUNvbnRleHR9XG4gKiBAdHlwZSB7VG9rZW5pemVyfVxuICovXG5mdW5jdGlvbiB0b2tlbml6ZVNldGV4dFVuZGVybGluZShlZmZlY3RzLCBvaywgbm9rKSB7XG4gIGNvbnN0IHNlbGYgPSB0aGlzXG4gIC8qKiBAdHlwZSB7Tm9uTnVsbGFibGU8Q29kZT59ICovXG4gIGxldCBtYXJrZXJcblxuICByZXR1cm4gc3RhcnRcblxuICAvKipcbiAgICogQXQgc3RhcnQgb2YgaGVhZGluZyAoc2V0ZXh0KSB1bmRlcmxpbmUuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqICAgfCBhYVxuICAgKiA+IHwgPT1cbiAgICogICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIHN0YXJ0KGNvZGUpIHtcbiAgICBsZXQgaW5kZXggPSBzZWxmLmV2ZW50cy5sZW5ndGhcbiAgICAvKiogQHR5cGUge2Jvb2xlYW4gfCB1bmRlZmluZWR9ICovXG4gICAgbGV0IHBhcmFncmFwaFxuXG4gICAgYXNzZXJ0KFxuICAgICAgY29kZSA9PT0gY29kZXMuZGFzaCB8fCBjb2RlID09PSBjb2Rlcy5lcXVhbHNUbyxcbiAgICAgICdleHBlY3RlZCBgPWAgb3IgYC1gJ1xuICAgIClcblxuICAgIC8vIEZpbmQgYW4gb3BlbmluZy5cbiAgICB3aGlsZSAoaW5kZXgtLSkge1xuICAgICAgLy8gU2tpcCBlbnRlci9leGl0IG9mIGxpbmUgZW5kaW5nLCBsaW5lIHByZWZpeCwgYW5kIGNvbnRlbnQuXG4gICAgICAvLyBXZSBjYW4gbm93IGVpdGhlciBoYXZlIGEgZGVmaW5pdGlvbiBvciBhIHBhcmFncmFwaC5cbiAgICAgIGlmIChcbiAgICAgICAgc2VsZi5ldmVudHNbaW5kZXhdWzFdLnR5cGUgIT09IHR5cGVzLmxpbmVFbmRpbmcgJiZcbiAgICAgICAgc2VsZi5ldmVudHNbaW5kZXhdWzFdLnR5cGUgIT09IHR5cGVzLmxpbmVQcmVmaXggJiZcbiAgICAgICAgc2VsZi5ldmVudHNbaW5kZXhdWzFdLnR5cGUgIT09IHR5cGVzLmNvbnRlbnRcbiAgICAgICkge1xuICAgICAgICBwYXJhZ3JhcGggPSBzZWxmLmV2ZW50c1tpbmRleF1bMV0udHlwZSA9PT0gdHlwZXMucGFyYWdyYXBoXG4gICAgICAgIGJyZWFrXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gVG8gZG86IGhhbmRsZSBsYXp5L3BpZXJjZSBsaWtlIGBtYXJrZG93bi1yc2AuXG4gICAgLy8gVG8gZG86IHBhcnNlIGluZGVudCBsaWtlIGBtYXJrZG93bi1yc2AuXG4gICAgaWYgKCFzZWxmLnBhcnNlci5sYXp5W3NlbGYubm93KCkubGluZV0gJiYgKHNlbGYuaW50ZXJydXB0IHx8IHBhcmFncmFwaCkpIHtcbiAgICAgIGVmZmVjdHMuZW50ZXIodHlwZXMuc2V0ZXh0SGVhZGluZ0xpbmUpXG4gICAgICBtYXJrZXIgPSBjb2RlXG4gICAgICByZXR1cm4gYmVmb3JlKGNvZGUpXG4gICAgfVxuXG4gICAgcmV0dXJuIG5vayhjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEFmdGVyIG9wdGlvbmFsIHdoaXRlc3BhY2UsIGF0IGAtYCBvciBgPWAuXG4gICAqXG4gICAqIGBgYG1hcmtkb3duXG4gICAqICAgfCBhYVxuICAgKiA+IHwgPT1cbiAgICogICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIGJlZm9yZShjb2RlKSB7XG4gICAgZWZmZWN0cy5lbnRlcih0eXBlcy5zZXRleHRIZWFkaW5nTGluZVNlcXVlbmNlKVxuICAgIHJldHVybiBpbnNpZGUoY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBJbiBzZXF1ZW5jZS5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogICB8IGFhXG4gICAqID4gfCA9PVxuICAgKiAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gaW5zaWRlKGNvZGUpIHtcbiAgICBpZiAoY29kZSA9PT0gbWFya2VyKSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHJldHVybiBpbnNpZGVcbiAgICB9XG5cbiAgICBlZmZlY3RzLmV4aXQodHlwZXMuc2V0ZXh0SGVhZGluZ0xpbmVTZXF1ZW5jZSlcblxuICAgIHJldHVybiBtYXJrZG93blNwYWNlKGNvZGUpXG4gICAgICA/IGZhY3RvcnlTcGFjZShlZmZlY3RzLCBhZnRlciwgdHlwZXMubGluZVN1ZmZpeCkoY29kZSlcbiAgICAgIDogYWZ0ZXIoY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBBZnRlciBzZXF1ZW5jZSwgYWZ0ZXIgb3B0aW9uYWwgd2hpdGVzcGFjZS5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogICB8IGFhXG4gICAqID4gfCA9PVxuICAgKiAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBhZnRlcihjb2RlKSB7XG4gICAgaWYgKGNvZGUgPT09IGNvZGVzLmVvZiB8fCBtYXJrZG93bkxpbmVFbmRpbmcoY29kZSkpIHtcbiAgICAgIGVmZmVjdHMuZXhpdCh0eXBlcy5zZXRleHRIZWFkaW5nTGluZSlcbiAgICAgIHJldHVybiBvayhjb2RlKVxuICAgIH1cblxuICAgIHJldHVybiBub2soY29kZSlcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/setext-underline.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/thematic-break.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/thematic-break.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   thematicBreak: () => (/* binding */ thematicBreak)\n/* harmony export */ });\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@1.1.0/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@1.2.0/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@1.1.0/node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/.pnpm/uvu@0.5.6/node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n\n\n\n\n\n\n\n/** @type {Construct} */\nconst thematicBreak = {\n  name: 'thematicBreak',\n  tokenize: tokenizeThematicBreak\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeThematicBreak(effects, ok, nok) {\n  let size = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of thematic break.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.thematicBreak)\n    // To do: parse indent like `markdown-rs`.\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at marker.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.asterisk ||\n        code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash ||\n        code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.underscore,\n      'expected `*`, `-`, or `_`'\n    )\n    marker = code\n    return atBreak(code)\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.thematicBreakSequence)\n      return sequence(code)\n    }\n\n    if (\n      size >= micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_3__.constants.thematicBreakMarkerCountMin &&\n      (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code))\n    ) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.thematicBreak)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequence(code) {\n    if (code === marker) {\n      effects.consume(code)\n      size++\n      return sequence\n    }\n\n    effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.thematicBreakSequence)\n    return (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownSpace)(code)\n      ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_5__.factorySpace)(effects, atBreak, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace)(code)\n      : atBreak(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-core-commonmark@1.1.0/node_modules/micromark-core-commonmark/dev/lib/thematic-break.js\n");

/***/ })

};
;