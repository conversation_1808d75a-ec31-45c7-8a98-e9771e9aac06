/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0";
exports.ids = ["vendor-chunks/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/client.cjs":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/client.cjs ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Client = exports.StoreClient = exports.RunsClient = exports.ThreadsClient = exports.AssistantsClient = exports.CronsClient = exports.getApiKey = void 0;\nconst async_caller_js_1 = __webpack_require__(/*! ./utils/async_caller.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.cjs\");\nconst env_js_1 = __webpack_require__(/*! ./utils/env.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/env.cjs\");\nconst signals_js_1 = __webpack_require__(/*! ./utils/signals.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/signals.cjs\");\nconst sse_js_1 = __webpack_require__(/*! ./utils/sse.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/sse.cjs\");\nconst stream_js_1 = __webpack_require__(/*! ./utils/stream.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/stream.cjs\");\n/**\n * Get the API key from the environment.\n * Precedence:\n *   1. explicit argument\n *   2. LANGGRAPH_API_KEY\n *   3. LANGSMITH_API_KEY\n *   4. LANGCHAIN_API_KEY\n *\n * @param apiKey - Optional API key provided as an argument\n * @returns The API key if found, otherwise undefined\n */\nfunction getApiKey(apiKey) {\n    if (apiKey) {\n        return apiKey;\n    }\n    const prefixes = [\"LANGGRAPH\", \"LANGSMITH\", \"LANGCHAIN\"];\n    for (const prefix of prefixes) {\n        const envKey = (0, env_js_1.getEnvironmentVariable)(`${prefix}_API_KEY`);\n        if (envKey) {\n            // Remove surrounding quotes\n            return envKey.trim().replace(/^[\"']|[\"']$/g, \"\");\n        }\n    }\n    return undefined;\n}\nexports.getApiKey = getApiKey;\nclass BaseClient {\n    constructor(config) {\n        Object.defineProperty(this, \"asyncCaller\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"timeoutMs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"apiUrl\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"defaultHeaders\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        const callerOptions = {\n            maxRetries: 4,\n            maxConcurrency: 4,\n            ...config?.callerOptions,\n        };\n        let defaultApiUrl = \"http://localhost:8123\";\n        if (!config?.apiUrl &&\n            typeof globalThis === \"object\" &&\n            globalThis != null) {\n            const fetchSmb = Symbol.for(\"langgraph_api:fetch\");\n            const urlSmb = Symbol.for(\"langgraph_api:url\");\n            const global = globalThis;\n            if (global[fetchSmb])\n                callerOptions.fetch ??= global[fetchSmb];\n            if (global[urlSmb])\n                defaultApiUrl = global[urlSmb];\n        }\n        this.asyncCaller = new async_caller_js_1.AsyncCaller(callerOptions);\n        this.timeoutMs = config?.timeoutMs;\n        // default limit being capped by Chrome\n        // https://github.com/nodejs/undici/issues/1373\n        // Regex to remove trailing slash, if present\n        this.apiUrl = config?.apiUrl?.replace(/\\/$/, \"\") || defaultApiUrl;\n        this.defaultHeaders = config?.defaultHeaders || {};\n        const apiKey = getApiKey(config?.apiKey);\n        if (apiKey) {\n            this.defaultHeaders[\"X-Api-Key\"] = apiKey;\n        }\n    }\n    prepareFetchOptions(path, options) {\n        const mutatedOptions = {\n            ...options,\n            headers: { ...this.defaultHeaders, ...options?.headers },\n        };\n        if (mutatedOptions.json) {\n            mutatedOptions.body = JSON.stringify(mutatedOptions.json);\n            mutatedOptions.headers = {\n                ...mutatedOptions.headers,\n                \"Content-Type\": \"application/json\",\n            };\n            delete mutatedOptions.json;\n        }\n        let timeoutSignal = null;\n        if (typeof options?.timeoutMs !== \"undefined\") {\n            if (options.timeoutMs != null) {\n                timeoutSignal = AbortSignal.timeout(options.timeoutMs);\n            }\n        }\n        else if (this.timeoutMs != null) {\n            timeoutSignal = AbortSignal.timeout(this.timeoutMs);\n        }\n        mutatedOptions.signal = (0, signals_js_1.mergeSignals)(timeoutSignal, mutatedOptions.signal);\n        const targetUrl = new URL(`${this.apiUrl}${path}`);\n        if (mutatedOptions.params) {\n            for (const [key, value] of Object.entries(mutatedOptions.params)) {\n                if (value == null)\n                    continue;\n                let strValue = typeof value === \"string\" || typeof value === \"number\"\n                    ? value.toString()\n                    : JSON.stringify(value);\n                targetUrl.searchParams.append(key, strValue);\n            }\n            delete mutatedOptions.params;\n        }\n        return [targetUrl, mutatedOptions];\n    }\n    async fetch(path, options) {\n        const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(path, options));\n        if (response.status === 202 || response.status === 204) {\n            return undefined;\n        }\n        return response.json();\n    }\n}\nclass CronsClient extends BaseClient {\n    /**\n     *\n     * @param threadId The ID of the thread.\n     * @param assistantId Assistant ID to use for this cron job.\n     * @param payload Payload for creating a cron job.\n     * @returns The created background run.\n     */\n    async createForThread(threadId, assistantId, payload) {\n        const json = {\n            schedule: payload?.schedule,\n            input: payload?.input,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            webhook: payload?.webhook,\n            multitask_strategy: payload?.multitaskStrategy,\n            if_not_exists: payload?.ifNotExists,\n        };\n        return this.fetch(`/threads/${threadId}/runs/crons`, {\n            method: \"POST\",\n            json,\n        });\n    }\n    /**\n     *\n     * @param assistantId Assistant ID to use for this cron job.\n     * @param payload Payload for creating a cron job.\n     * @returns\n     */\n    async create(assistantId, payload) {\n        const json = {\n            schedule: payload?.schedule,\n            input: payload?.input,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            webhook: payload?.webhook,\n            multitask_strategy: payload?.multitaskStrategy,\n            if_not_exists: payload?.ifNotExists,\n        };\n        return this.fetch(`/runs/crons`, {\n            method: \"POST\",\n            json,\n        });\n    }\n    /**\n     *\n     * @param cronId Cron ID of Cron job to delete.\n     */\n    async delete(cronId) {\n        await this.fetch(`/runs/crons/${cronId}`, {\n            method: \"DELETE\",\n        });\n    }\n    /**\n     *\n     * @param query Query options.\n     * @returns List of crons.\n     */\n    async search(query) {\n        return this.fetch(\"/runs/crons/search\", {\n            method: \"POST\",\n            json: {\n                assistant_id: query?.assistantId ?? undefined,\n                thread_id: query?.threadId ?? undefined,\n                limit: query?.limit ?? 10,\n                offset: query?.offset ?? 0,\n            },\n        });\n    }\n}\nexports.CronsClient = CronsClient;\nclass AssistantsClient extends BaseClient {\n    /**\n     * Get an assistant by ID.\n     *\n     * @param assistantId The ID of the assistant.\n     * @returns Assistant\n     */\n    async get(assistantId) {\n        return this.fetch(`/assistants/${assistantId}`);\n    }\n    /**\n     * Get the JSON representation of the graph assigned to a runnable\n     * @param assistantId The ID of the assistant.\n     * @param options.xray Whether to include subgraphs in the serialized graph representation. If an integer value is provided, only subgraphs with a depth less than or equal to the value will be included.\n     * @returns Serialized graph\n     */\n    async getGraph(assistantId, options) {\n        return this.fetch(`/assistants/${assistantId}/graph`, {\n            params: { xray: options?.xray },\n        });\n    }\n    /**\n     * Get the state and config schema of the graph assigned to a runnable\n     * @param assistantId The ID of the assistant.\n     * @returns Graph schema\n     */\n    async getSchemas(assistantId) {\n        return this.fetch(`/assistants/${assistantId}/schemas`);\n    }\n    /**\n     * Get the schemas of an assistant by ID.\n     *\n     * @param assistantId The ID of the assistant to get the schema of.\n     * @param options Additional options for getting subgraphs, such as namespace or recursion extraction.\n     * @returns The subgraphs of the assistant.\n     */\n    async getSubgraphs(assistantId, options) {\n        if (options?.namespace) {\n            return this.fetch(`/assistants/${assistantId}/subgraphs/${options.namespace}`, { params: { recurse: options?.recurse } });\n        }\n        return this.fetch(`/assistants/${assistantId}/subgraphs`, {\n            params: { recurse: options?.recurse },\n        });\n    }\n    /**\n     * Create a new assistant.\n     * @param payload Payload for creating an assistant.\n     * @returns The created assistant.\n     */\n    async create(payload) {\n        return this.fetch(\"/assistants\", {\n            method: \"POST\",\n            json: {\n                graph_id: payload.graphId,\n                config: payload.config,\n                metadata: payload.metadata,\n                assistant_id: payload.assistantId,\n                if_exists: payload.ifExists,\n                name: payload.name,\n                description: payload.description,\n            },\n        });\n    }\n    /**\n     * Update an assistant.\n     * @param assistantId ID of the assistant.\n     * @param payload Payload for updating the assistant.\n     * @returns The updated assistant.\n     */\n    async update(assistantId, payload) {\n        return this.fetch(`/assistants/${assistantId}`, {\n            method: \"PATCH\",\n            json: {\n                graph_id: payload.graphId,\n                config: payload.config,\n                metadata: payload.metadata,\n                name: payload.name,\n                description: payload.description,\n            },\n        });\n    }\n    /**\n     * Delete an assistant.\n     *\n     * @param assistantId ID of the assistant.\n     */\n    async delete(assistantId) {\n        return this.fetch(`/assistants/${assistantId}`, {\n            method: \"DELETE\",\n        });\n    }\n    /**\n     * List assistants.\n     * @param query Query options.\n     * @returns List of assistants.\n     */\n    async search(query) {\n        return this.fetch(\"/assistants/search\", {\n            method: \"POST\",\n            json: {\n                graph_id: query?.graphId ?? undefined,\n                metadata: query?.metadata ?? undefined,\n                limit: query?.limit ?? 10,\n                offset: query?.offset ?? 0,\n            },\n        });\n    }\n    /**\n     * List all versions of an assistant.\n     *\n     * @param assistantId ID of the assistant.\n     * @returns List of assistant versions.\n     */\n    async getVersions(assistantId, payload) {\n        return this.fetch(`/assistants/${assistantId}/versions`, {\n            method: \"POST\",\n            json: {\n                metadata: payload?.metadata ?? undefined,\n                limit: payload?.limit ?? 10,\n                offset: payload?.offset ?? 0,\n            },\n        });\n    }\n    /**\n     * Change the version of an assistant.\n     *\n     * @param assistantId ID of the assistant.\n     * @param version The version to change to.\n     * @returns The updated assistant.\n     */\n    async setLatest(assistantId, version) {\n        return this.fetch(`/assistants/${assistantId}/latest`, {\n            method: \"POST\",\n            json: { version },\n        });\n    }\n}\nexports.AssistantsClient = AssistantsClient;\nclass ThreadsClient extends BaseClient {\n    /**\n     * Get a thread by ID.\n     *\n     * @param threadId ID of the thread.\n     * @returns The thread.\n     */\n    async get(threadId) {\n        return this.fetch(`/threads/${threadId}`);\n    }\n    /**\n     * Create a new thread.\n     *\n     * @param payload Payload for creating a thread.\n     * @returns The created thread.\n     */\n    async create(payload) {\n        return this.fetch(`/threads`, {\n            method: \"POST\",\n            json: {\n                metadata: {\n                    ...payload?.metadata,\n                    graph_id: payload?.graphId,\n                },\n                thread_id: payload?.threadId,\n                if_exists: payload?.ifExists,\n                supersteps: payload?.supersteps?.map((s) => ({\n                    updates: s.updates.map((u) => ({\n                        values: u.values,\n                        command: u.command,\n                        as_node: u.asNode,\n                    })),\n                })),\n            },\n        });\n    }\n    /**\n     * Copy an existing thread\n     * @param threadId ID of the thread to be copied\n     * @returns Newly copied thread\n     */\n    async copy(threadId) {\n        return this.fetch(`/threads/${threadId}/copy`, {\n            method: \"POST\",\n        });\n    }\n    /**\n     * Update a thread.\n     *\n     * @param threadId ID of the thread.\n     * @param payload Payload for updating the thread.\n     * @returns The updated thread.\n     */\n    async update(threadId, payload) {\n        return this.fetch(`/threads/${threadId}`, {\n            method: \"PATCH\",\n            json: { metadata: payload?.metadata },\n        });\n    }\n    /**\n     * Delete a thread.\n     *\n     * @param threadId ID of the thread.\n     */\n    async delete(threadId) {\n        return this.fetch(`/threads/${threadId}`, {\n            method: \"DELETE\",\n        });\n    }\n    /**\n     * List threads\n     *\n     * @param query Query options\n     * @returns List of threads\n     */\n    async search(query) {\n        return this.fetch(\"/threads/search\", {\n            method: \"POST\",\n            json: {\n                metadata: query?.metadata ?? undefined,\n                limit: query?.limit ?? 10,\n                offset: query?.offset ?? 0,\n                status: query?.status,\n                sort_by: query?.sortBy,\n                sort_order: query?.sortOrder,\n            },\n        });\n    }\n    /**\n     * Get state for a thread.\n     *\n     * @param threadId ID of the thread.\n     * @returns Thread state.\n     */\n    async getState(threadId, checkpoint, options) {\n        if (checkpoint != null) {\n            if (typeof checkpoint !== \"string\") {\n                return this.fetch(`/threads/${threadId}/state/checkpoint`, {\n                    method: \"POST\",\n                    json: { checkpoint, subgraphs: options?.subgraphs },\n                });\n            }\n            // deprecated\n            return this.fetch(`/threads/${threadId}/state/${checkpoint}`, { params: { subgraphs: options?.subgraphs } });\n        }\n        return this.fetch(`/threads/${threadId}/state`, {\n            params: { subgraphs: options?.subgraphs },\n        });\n    }\n    /**\n     * Add state to a thread.\n     *\n     * @param threadId The ID of the thread.\n     * @returns\n     */\n    async updateState(threadId, options) {\n        return this.fetch(`/threads/${threadId}/state`, {\n            method: \"POST\",\n            json: {\n                values: options.values,\n                checkpoint_id: options.checkpointId,\n                checkpoint: options.checkpoint,\n                as_node: options?.asNode,\n            },\n        });\n    }\n    /**\n     * Patch the metadata of a thread.\n     *\n     * @param threadIdOrConfig Thread ID or config to patch the state of.\n     * @param metadata Metadata to patch the state with.\n     */\n    async patchState(threadIdOrConfig, metadata) {\n        let threadId;\n        if (typeof threadIdOrConfig !== \"string\") {\n            if (typeof threadIdOrConfig.configurable?.thread_id !== \"string\") {\n                throw new Error(\"Thread ID is required when updating state with a config.\");\n            }\n            threadId = threadIdOrConfig.configurable.thread_id;\n        }\n        else {\n            threadId = threadIdOrConfig;\n        }\n        return this.fetch(`/threads/${threadId}/state`, {\n            method: \"PATCH\",\n            json: { metadata: metadata },\n        });\n    }\n    /**\n     * Get all past states for a thread.\n     *\n     * @param threadId ID of the thread.\n     * @param options Additional options.\n     * @returns List of thread states.\n     */\n    async getHistory(threadId, options) {\n        return this.fetch(`/threads/${threadId}/history`, {\n            method: \"POST\",\n            json: {\n                limit: options?.limit ?? 10,\n                before: options?.before,\n                metadata: options?.metadata,\n                checkpoint: options?.checkpoint,\n            },\n        });\n    }\n}\nexports.ThreadsClient = ThreadsClient;\nclass RunsClient extends BaseClient {\n    /**\n     * Create a run and stream the results.\n     *\n     * @param threadId The ID of the thread.\n     * @param assistantId Assistant ID to use for this run.\n     * @param payload Payload for creating a run.\n     */\n    async *stream(threadId, assistantId, payload) {\n        const json = {\n            input: payload?.input,\n            command: payload?.command,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            stream_mode: payload?.streamMode,\n            stream_subgraphs: payload?.streamSubgraphs,\n            feedback_keys: payload?.feedbackKeys,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            checkpoint: payload?.checkpoint,\n            checkpoint_id: payload?.checkpointId,\n            webhook: payload?.webhook,\n            multitask_strategy: payload?.multitaskStrategy,\n            on_completion: payload?.onCompletion,\n            on_disconnect: payload?.onDisconnect,\n            after_seconds: payload?.afterSeconds,\n            if_not_exists: payload?.ifNotExists,\n        };\n        const endpoint = threadId == null ? `/runs/stream` : `/threads/${threadId}/runs/stream`;\n        const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(endpoint, {\n            method: \"POST\",\n            json,\n            timeoutMs: null,\n            signal: payload?.signal,\n        }));\n        const stream = (response.body || new ReadableStream({ start: (ctrl) => ctrl.close() }))\n            .pipeThrough(new sse_js_1.BytesLineDecoder())\n            .pipeThrough(new sse_js_1.SSEDecoder());\n        yield* stream_js_1.IterableReadableStream.fromReadableStream(stream);\n    }\n    /**\n     * Create a run.\n     *\n     * @param threadId The ID of the thread.\n     * @param assistantId Assistant ID to use for this run.\n     * @param payload Payload for creating a run.\n     * @returns The created run.\n     */\n    async create(threadId, assistantId, payload) {\n        const json = {\n            input: payload?.input,\n            command: payload?.command,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            stream_mode: payload?.streamMode,\n            stream_subgraphs: payload?.streamSubgraphs,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            webhook: payload?.webhook,\n            checkpoint: payload?.checkpoint,\n            checkpoint_id: payload?.checkpointId,\n            multitask_strategy: payload?.multitaskStrategy,\n            after_seconds: payload?.afterSeconds,\n            if_not_exists: payload?.ifNotExists,\n        };\n        return this.fetch(`/threads/${threadId}/runs`, {\n            method: \"POST\",\n            json,\n            signal: payload?.signal,\n        });\n    }\n    /**\n     * Create a batch of stateless background runs.\n     *\n     * @param payloads An array of payloads for creating runs.\n     * @returns An array of created runs.\n     */\n    async createBatch(payloads) {\n        const filteredPayloads = payloads\n            .map((payload) => ({ ...payload, assistant_id: payload.assistantId }))\n            .map((payload) => {\n            return Object.fromEntries(Object.entries(payload).filter(([_, v]) => v !== undefined));\n        });\n        return this.fetch(\"/runs/batch\", {\n            method: \"POST\",\n            json: filteredPayloads,\n        });\n    }\n    /**\n     * Create a run and wait for it to complete.\n     *\n     * @param threadId The ID of the thread.\n     * @param assistantId Assistant ID to use for this run.\n     * @param payload Payload for creating a run.\n     * @returns The last values chunk of the thread.\n     */\n    async wait(threadId, assistantId, payload) {\n        const json = {\n            input: payload?.input,\n            command: payload?.command,\n            config: payload?.config,\n            metadata: payload?.metadata,\n            assistant_id: assistantId,\n            interrupt_before: payload?.interruptBefore,\n            interrupt_after: payload?.interruptAfter,\n            checkpoint: payload?.checkpoint,\n            checkpoint_id: payload?.checkpointId,\n            webhook: payload?.webhook,\n            multitask_strategy: payload?.multitaskStrategy,\n            on_completion: payload?.onCompletion,\n            on_disconnect: payload?.onDisconnect,\n            after_seconds: payload?.afterSeconds,\n            if_not_exists: payload?.ifNotExists,\n        };\n        const endpoint = threadId == null ? `/runs/wait` : `/threads/${threadId}/runs/wait`;\n        const response = await this.fetch(endpoint, {\n            method: \"POST\",\n            json,\n            timeoutMs: null,\n            signal: payload?.signal,\n        });\n        const raiseError = payload?.raiseError !== undefined ? payload.raiseError : true;\n        if (raiseError &&\n            \"__error__\" in response &&\n            typeof response.__error__ === \"object\" &&\n            response.__error__ &&\n            \"error\" in response.__error__ &&\n            \"message\" in response.__error__) {\n            throw new Error(`${response.__error__?.error}: ${response.__error__?.message}`);\n        }\n        return response;\n    }\n    /**\n     * List all runs for a thread.\n     *\n     * @param threadId The ID of the thread.\n     * @param options Filtering and pagination options.\n     * @returns List of runs.\n     */\n    async list(threadId, options) {\n        return this.fetch(`/threads/${threadId}/runs`, {\n            params: {\n                limit: options?.limit ?? 10,\n                offset: options?.offset ?? 0,\n                status: options?.status ?? undefined,\n            },\n        });\n    }\n    /**\n     * Get a run by ID.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @returns The run.\n     */\n    async get(threadId, runId) {\n        return this.fetch(`/threads/${threadId}/runs/${runId}`);\n    }\n    /**\n     * Cancel a run.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @param wait Whether to block when canceling\n     * @param action Action to take when cancelling the run. Possible values are `interrupt` or `rollback`. Default is `interrupt`.\n     * @returns\n     */\n    async cancel(threadId, runId, wait = false, action = \"interrupt\") {\n        return this.fetch(`/threads/${threadId}/runs/${runId}/cancel`, {\n            method: \"POST\",\n            params: {\n                wait: wait ? \"1\" : \"0\",\n                action: action,\n            },\n        });\n    }\n    /**\n     * Block until a run is done.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @returns\n     */\n    async join(threadId, runId, options) {\n        return this.fetch(`/threads/${threadId}/runs/${runId}/join`, {\n            timeoutMs: null,\n            signal: options?.signal,\n        });\n    }\n    /**\n     * Stream output from a run in real-time, until the run is done.\n     * Output is not buffered, so any output produced before this call will\n     * not be received here.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @param options Additional options for controlling the stream behavior:\n     *   - signal: An AbortSignal that can be used to cancel the stream request\n     *   - cancelOnDisconnect: When true, automatically cancels the run if the client disconnects from the stream\n     *   - streamMode: Controls what types of events to receive from the stream (can be a single mode or array of modes)\n     *        Must be a subset of the stream modes passed when creating the run. Background runs default to having the union of all\n     *        stream modes enabled.\n     * @returns An async generator yielding stream parts.\n     */\n    async *joinStream(threadId, runId, options) {\n        const opts = typeof options === \"object\" &&\n            options != null &&\n            options instanceof AbortSignal\n            ? { signal: options }\n            : options;\n        const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(`/threads/${threadId}/runs/${runId}/stream`, {\n            method: \"GET\",\n            timeoutMs: null,\n            signal: opts?.signal,\n            params: {\n                cancel_on_disconnect: opts?.cancelOnDisconnect ? \"1\" : \"0\",\n                stream_mode: opts?.streamMode,\n            },\n        }));\n        const stream = (response.body || new ReadableStream({ start: (ctrl) => ctrl.close() }))\n            .pipeThrough(new sse_js_1.BytesLineDecoder())\n            .pipeThrough(new sse_js_1.SSEDecoder());\n        yield* stream_js_1.IterableReadableStream.fromReadableStream(stream);\n    }\n    /**\n     * Delete a run.\n     *\n     * @param threadId The ID of the thread.\n     * @param runId The ID of the run.\n     * @returns\n     */\n    async delete(threadId, runId) {\n        return this.fetch(`/threads/${threadId}/runs/${runId}`, {\n            method: \"DELETE\",\n        });\n    }\n}\nexports.RunsClient = RunsClient;\nclass StoreClient extends BaseClient {\n    /**\n     * Store or update an item.\n     *\n     * @param namespace A list of strings representing the namespace path.\n     * @param key The unique identifier for the item within the namespace.\n     * @param value A dictionary containing the item's data.\n     * @param options.index Controls search indexing - null (use defaults), false (disable), or list of field paths to index.\n     * @param options.ttl Optional time-to-live in minutes for the item, or null for no expiration.\n     * @returns Promise<void>\n     *\n     * @example\n     * ```typescript\n     * await client.store.putItem(\n     *   [\"documents\", \"user123\"],\n     *   \"item456\",\n     *   { title: \"My Document\", content: \"Hello World\" },\n     *   { ttl: 60 } // expires in 60 minutes\n     * );\n     * ```\n     */\n    async putItem(namespace, key, value, options) {\n        namespace.forEach((label) => {\n            if (label.includes(\".\")) {\n                throw new Error(`Invalid namespace label '${label}'. Namespace labels cannot contain periods ('.')`);\n            }\n        });\n        const payload = {\n            namespace,\n            key,\n            value,\n            index: options?.index,\n            ttl: options?.ttl,\n        };\n        return this.fetch(\"/store/items\", {\n            method: \"PUT\",\n            json: payload,\n        });\n    }\n    /**\n     * Retrieve a single item.\n     *\n     * @param namespace A list of strings representing the namespace path.\n     * @param key The unique identifier for the item.\n     * @param options.refreshTtl Whether to refresh the TTL on this read operation. If null, uses the store's default behavior.\n     * @returns Promise<Item>\n     *\n     * @example\n     * ```typescript\n     * const item = await client.store.getItem(\n     *   [\"documents\", \"user123\"],\n     *   \"item456\",\n     *   { refreshTtl: true }\n     * );\n     * console.log(item);\n     * // {\n     * //   namespace: [\"documents\", \"user123\"],\n     * //   key: \"item456\",\n     * //   value: { title: \"My Document\", content: \"Hello World\" },\n     * //   createdAt: \"2024-07-30T12:00:00Z\",\n     * //   updatedAt: \"2024-07-30T12:00:00Z\"\n     * // }\n     * ```\n     */\n    async getItem(namespace, key, options) {\n        namespace.forEach((label) => {\n            if (label.includes(\".\")) {\n                throw new Error(`Invalid namespace label '${label}'. Namespace labels cannot contain periods ('.')`);\n            }\n        });\n        const params = {\n            namespace: namespace.join(\".\"),\n            key,\n        };\n        if (options?.refreshTtl !== undefined) {\n            params.refresh_ttl = options.refreshTtl;\n        }\n        const response = await this.fetch(\"/store/items\", {\n            params,\n        });\n        return response\n            ? {\n                ...response,\n                createdAt: response.created_at,\n                updatedAt: response.updated_at,\n            }\n            : null;\n    }\n    /**\n     * Delete an item.\n     *\n     * @param namespace A list of strings representing the namespace path.\n     * @param key The unique identifier for the item.\n     * @returns Promise<void>\n     */\n    async deleteItem(namespace, key) {\n        namespace.forEach((label) => {\n            if (label.includes(\".\")) {\n                throw new Error(`Invalid namespace label '${label}'. Namespace labels cannot contain periods ('.')`);\n            }\n        });\n        return this.fetch(\"/store/items\", {\n            method: \"DELETE\",\n            json: { namespace, key },\n        });\n    }\n    /**\n     * Search for items within a namespace prefix.\n     *\n     * @param namespacePrefix List of strings representing the namespace prefix.\n     * @param options.filter Optional dictionary of key-value pairs to filter results.\n     * @param options.limit Maximum number of items to return (default is 10).\n     * @param options.offset Number of items to skip before returning results (default is 0).\n     * @param options.query Optional search query.\n     * @param options.refreshTtl Whether to refresh the TTL on items returned by this search. If null, uses the store's default behavior.\n     * @returns Promise<SearchItemsResponse>\n     *\n     * @example\n     * ```typescript\n     * const results = await client.store.searchItems(\n     *   [\"documents\"],\n     *   {\n     *     filter: { author: \"John Doe\" },\n     *     limit: 5,\n     *     refreshTtl: true\n     *   }\n     * );\n     * console.log(results);\n     * // {\n     * //   items: [\n     * //     {\n     * //       namespace: [\"documents\", \"user123\"],\n     * //       key: \"item789\",\n     * //       value: { title: \"Another Document\", author: \"John Doe\" },\n     * //       createdAt: \"2024-07-30T12:00:00Z\",\n     * //       updatedAt: \"2024-07-30T12:00:00Z\"\n     * //     },\n     * //     // ... additional items ...\n     * //   ]\n     * // }\n     * ```\n     */\n    async searchItems(namespacePrefix, options) {\n        const payload = {\n            namespace_prefix: namespacePrefix,\n            filter: options?.filter,\n            limit: options?.limit ?? 10,\n            offset: options?.offset ?? 0,\n            query: options?.query,\n            refresh_ttl: options?.refreshTtl,\n        };\n        const response = await this.fetch(\"/store/items/search\", {\n            method: \"POST\",\n            json: payload,\n        });\n        return {\n            items: response.items.map((item) => ({\n                ...item,\n                createdAt: item.created_at,\n                updatedAt: item.updated_at,\n            })),\n        };\n    }\n    /**\n     * List namespaces with optional match conditions.\n     *\n     * @param options.prefix Optional list of strings representing the prefix to filter namespaces.\n     * @param options.suffix Optional list of strings representing the suffix to filter namespaces.\n     * @param options.maxDepth Optional integer specifying the maximum depth of namespaces to return.\n     * @param options.limit Maximum number of namespaces to return (default is 100).\n     * @param options.offset Number of namespaces to skip before returning results (default is 0).\n     * @returns Promise<ListNamespaceResponse>\n     */\n    async listNamespaces(options) {\n        const payload = {\n            prefix: options?.prefix,\n            suffix: options?.suffix,\n            max_depth: options?.maxDepth,\n            limit: options?.limit ?? 100,\n            offset: options?.offset ?? 0,\n        };\n        return this.fetch(\"/store/namespaces\", {\n            method: \"POST\",\n            json: payload,\n        });\n    }\n}\nexports.StoreClient = StoreClient;\nclass UiClient extends BaseClient {\n    static getOrCached(key, fn) {\n        if (UiClient.promiseCache[key] != null) {\n            return UiClient.promiseCache[key];\n        }\n        const promise = fn();\n        UiClient.promiseCache[key] = promise;\n        return promise;\n    }\n    async getComponent(assistantId, agentName) {\n        return UiClient[\"getOrCached\"](`${this.apiUrl}-${assistantId}-${agentName}`, async () => {\n            const response = await this.asyncCaller.fetch(...this.prepareFetchOptions(`/ui/${assistantId}`, {\n                headers: {\n                    Accept: \"text/html\",\n                    \"Content-Type\": \"application/json\",\n                },\n                method: \"POST\",\n                json: { name: agentName },\n            }));\n            return response.text();\n        });\n    }\n}\nObject.defineProperty(UiClient, \"promiseCache\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: {}\n});\nclass Client {\n    constructor(config) {\n        /**\n         * The client for interacting with assistants.\n         */\n        Object.defineProperty(this, \"assistants\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with threads.\n         */\n        Object.defineProperty(this, \"threads\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with runs.\n         */\n        Object.defineProperty(this, \"runs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with cron runs.\n         */\n        Object.defineProperty(this, \"crons\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with the KV store.\n         */\n        Object.defineProperty(this, \"store\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        /**\n         * The client for interacting with the UI.\n         * @internal Used by LoadExternalComponent and the API might change in the future.\n         */\n        Object.defineProperty(this, \"~ui\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.assistants = new AssistantsClient(config);\n        this.threads = new ThreadsClient(config);\n        this.runs = new RunsClient(config);\n        this.crons = new CronsClient(config);\n        this.store = new StoreClient(config);\n        this[\"~ui\"] = new UiClient(config);\n    }\n}\nexports.Client = Client;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/client.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/index.cjs":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/index.cjs ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.overrideFetchImplementation = exports.Client = void 0;\nvar client_js_1 = __webpack_require__(/*! ./client.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/client.cjs\");\nObject.defineProperty(exports, \"Client\", ({ enumerable: true, get: function () { return client_js_1.Client; } }));\nvar fetch_js_1 = __webpack_require__(/*! ./singletons/fetch.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.cjs\");\nObject.defineProperty(exports, \"overrideFetchImplementation\", ({ enumerable: true, get: function () { return fetch_js_1.overrideFetchImplementation; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuMC43MF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vbGFuZ2dyYXBoLXNkay9kaXN0L2luZGV4LmNqcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxtQ0FBbUMsR0FBRyxjQUFjO0FBQ3BELGtCQUFrQixtQkFBTyxDQUFDLCtNQUFjO0FBQ3hDLDBDQUF5QyxFQUFFLHFDQUFxQyw4QkFBOEIsRUFBQztBQUMvRyxpQkFBaUIsbUJBQU8sQ0FBQyxtT0FBd0I7QUFDakQsK0RBQThELEVBQUUscUNBQXFDLGtEQUFrRCxFQUFDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuMC43MF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vbGFuZ2dyYXBoLXNkay9kaXN0L2luZGV4LmNqcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMub3ZlcnJpZGVGZXRjaEltcGxlbWVudGF0aW9uID0gZXhwb3J0cy5DbGllbnQgPSB2b2lkIDA7XG52YXIgY2xpZW50X2pzXzEgPSByZXF1aXJlKFwiLi9jbGllbnQuY2pzXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiQ2xpZW50XCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiBjbGllbnRfanNfMS5DbGllbnQ7IH0gfSk7XG52YXIgZmV0Y2hfanNfMSA9IHJlcXVpcmUoXCIuL3NpbmdsZXRvbnMvZmV0Y2guY2pzXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwib3ZlcnJpZGVGZXRjaEltcGxlbWVudGF0aW9uXCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiBmZXRjaF9qc18xLm92ZXJyaWRlRmV0Y2hJbXBsZW1lbnRhdGlvbjsgfSB9KTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/index.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.cjs":
/*!*****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.cjs ***!
  \*****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports._getFetchImplementation = exports.overrideFetchImplementation = void 0;\n// Wrap the default fetch call due to issues with illegal invocations\n// in some environments:\n// https://stackoverflow.com/questions/69876859/why-does-bind-fix-failed-to-execute-fetch-on-window-illegal-invocation-err\n// @ts-expect-error Broad typing to support a range of fetch implementations\nconst DEFAULT_FETCH_IMPLEMENTATION = (...args) => fetch(...args);\nconst LANGSMITH_FETCH_IMPLEMENTATION_KEY = Symbol.for(\"lg:fetch_implementation\");\n/**\n * Overrides the fetch implementation used for LangSmith calls.\n * You should use this if you need to use an implementation of fetch\n * other than the default global (e.g. for dealing with proxies).\n * @param fetch The new fetch function to use.\n */\nconst overrideFetchImplementation = (fetch) => {\n    globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] = fetch;\n};\nexports.overrideFetchImplementation = overrideFetchImplementation;\n/**\n * @internal\n */\nconst _getFetchImplementation = () => {\n    return (globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] ??\n        DEFAULT_FETCH_IMPLEMENTATION);\n};\nexports._getFetchImplementation = _getFetchImplementation;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.cjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.cjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncCaller = void 0;\nconst p_retry_1 = __importDefault(__webpack_require__(/*! p-retry */ \"(rsc)/./node_modules/.pnpm/p-retry@4.6.2/node_modules/p-retry/index.js\"));\nconst p_queue_1 = __importDefault(__webpack_require__(/*! p-queue */ \"(rsc)/./node_modules/.pnpm/p-queue@6.6.2/node_modules/p-queue/dist/index.js\"));\nconst fetch_js_1 = __webpack_require__(/*! ../singletons/fetch.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/singletons/fetch.cjs\");\nconst STATUS_NO_RETRY = [\n    400, // Bad Request\n    401, // Unauthorized\n    402, // Payment required\n    403, // Forbidden\n    404, // Not Found\n    405, // Method Not Allowed\n    406, // Not Acceptable\n    407, // Proxy Authentication Required\n    408, // Request Timeout\n    409, // Conflict\n    422, // Unprocessable Entity\n];\n/**\n * Do not rely on globalThis.Response, rather just\n * do duck typing\n */\nfunction isResponse(x) {\n    if (x == null || typeof x !== \"object\")\n        return false;\n    return \"status\" in x && \"statusText\" in x && \"text\" in x;\n}\n/**\n * Utility error to properly handle failed requests\n */\nclass HTTPError extends Error {\n    constructor(status, message, response) {\n        super(`HTTP ${status}: ${message}`);\n        Object.defineProperty(this, \"status\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"text\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"response\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.status = status;\n        this.text = message;\n        this.response = response;\n    }\n    static async fromResponse(response, options) {\n        try {\n            return new HTTPError(response.status, await response.text(), options?.includeResponse ? response : undefined);\n        }\n        catch {\n            return new HTTPError(response.status, response.statusText, options?.includeResponse ? response : undefined);\n        }\n    }\n}\n/**\n * A class that can be used to make async calls with concurrency and retry logic.\n *\n * This is useful for making calls to any kind of \"expensive\" external resource,\n * be it because it's rate-limited, subject to network issues, etc.\n *\n * Concurrent calls are limited by the `maxConcurrency` parameter, which defaults\n * to `Infinity`. This means that by default, all calls will be made in parallel.\n *\n * Retries are limited by the `maxRetries` parameter, which defaults to 5. This\n * means that by default, each call will be retried up to 5 times, with an\n * exponential backoff between each attempt.\n */\nclass AsyncCaller {\n    constructor(params) {\n        Object.defineProperty(this, \"maxConcurrency\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"maxRetries\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"queue\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"onFailedResponseHook\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"customFetch\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.maxConcurrency = params.maxConcurrency ?? Infinity;\n        this.maxRetries = params.maxRetries ?? 4;\n        if (\"default\" in p_queue_1.default) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.queue = new p_queue_1.default.default({\n                concurrency: this.maxConcurrency,\n            });\n        }\n        else {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.queue = new p_queue_1.default({ concurrency: this.maxConcurrency });\n        }\n        this.onFailedResponseHook = params?.onFailedResponseHook;\n        this.customFetch = params.fetch;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    call(callable, ...args) {\n        const onFailedResponseHook = this.onFailedResponseHook;\n        return this.queue.add(() => (0, p_retry_1.default)(() => callable(...args).catch(async (error) => {\n            // eslint-disable-next-line no-instanceof/no-instanceof\n            if (error instanceof Error) {\n                throw error;\n            }\n            else if (isResponse(error)) {\n                throw await HTTPError.fromResponse(error, {\n                    includeResponse: !!onFailedResponseHook,\n                });\n            }\n            else {\n                throw new Error(error);\n            }\n        }), {\n            async onFailedAttempt(error) {\n                if (error.message.startsWith(\"Cancel\") ||\n                    error.message.startsWith(\"TimeoutError\") ||\n                    error.message.startsWith(\"AbortError\")) {\n                    throw error;\n                }\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                if (error?.code === \"ECONNABORTED\") {\n                    throw error;\n                }\n                if (error instanceof HTTPError) {\n                    if (STATUS_NO_RETRY.includes(error.status)) {\n                        throw error;\n                    }\n                    if (onFailedResponseHook && error.response) {\n                        await onFailedResponseHook(error.response);\n                    }\n                }\n            },\n            // If needed we can change some of the defaults here,\n            // but they're quite sensible.\n            retries: this.maxRetries,\n            randomize: true,\n        }), { throwOnTimeout: true });\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    callWithOptions(options, callable, ...args) {\n        // Note this doesn't cancel the underlying request,\n        // when available prefer to use the signal option of the underlying call\n        if (options.signal) {\n            return Promise.race([\n                this.call(callable, ...args),\n                new Promise((_, reject) => {\n                    options.signal?.addEventListener(\"abort\", () => {\n                        reject(new Error(\"AbortError\"));\n                    });\n                }),\n            ]);\n        }\n        return this.call(callable, ...args);\n    }\n    fetch(...args) {\n        const fetchFn = this.customFetch ?? (0, fetch_js_1._getFetchImplementation)();\n        return this.call(() => fetchFn(...args).then((res) => (res.ok ? res : Promise.reject(res))));\n    }\n}\nexports.AsyncCaller = AsyncCaller;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuMC43MF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vbGFuZ2dyYXBoLXNkay9kaXN0L3V0aWxzL2FzeW5jX2NhbGxlci5janMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDZDQUE2QztBQUM3QztBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxtQkFBbUI7QUFDbkIsa0NBQWtDLG1CQUFPLENBQUMsdUZBQVM7QUFDbkQsa0NBQWtDLG1CQUFPLENBQUMsNEZBQVM7QUFDbkQsbUJBQW1CLG1CQUFPLENBQUMsb09BQXlCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixPQUFPLElBQUksUUFBUTtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsaURBQWlELGtDQUFrQztBQUNuRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxLQUFLLHNCQUFzQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AbGFuZ2NoYWluK2xhbmdncmFwaC1zZGtAMC4wLjcwX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvQGxhbmdjaGFpbi9sYW5nZ3JhcGgtc2RrL2Rpc3QvdXRpbHMvYXN5bmNfY2FsbGVyLmNqcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2ltcG9ydERlZmF1bHQgPSAodGhpcyAmJiB0aGlzLl9faW1wb3J0RGVmYXVsdCkgfHwgZnVuY3Rpb24gKG1vZCkge1xuICAgIHJldHVybiAobW9kICYmIG1vZC5fX2VzTW9kdWxlKSA/IG1vZCA6IHsgXCJkZWZhdWx0XCI6IG1vZCB9O1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuQXN5bmNDYWxsZXIgPSB2b2lkIDA7XG5jb25zdCBwX3JldHJ5XzEgPSBfX2ltcG9ydERlZmF1bHQocmVxdWlyZShcInAtcmV0cnlcIikpO1xuY29uc3QgcF9xdWV1ZV8xID0gX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCJwLXF1ZXVlXCIpKTtcbmNvbnN0IGZldGNoX2pzXzEgPSByZXF1aXJlKFwiLi4vc2luZ2xldG9ucy9mZXRjaC5janNcIik7XG5jb25zdCBTVEFUVVNfTk9fUkVUUlkgPSBbXG4gICAgNDAwLCAvLyBCYWQgUmVxdWVzdFxuICAgIDQwMSwgLy8gVW5hdXRob3JpemVkXG4gICAgNDAyLCAvLyBQYXltZW50IHJlcXVpcmVkXG4gICAgNDAzLCAvLyBGb3JiaWRkZW5cbiAgICA0MDQsIC8vIE5vdCBGb3VuZFxuICAgIDQwNSwgLy8gTWV0aG9kIE5vdCBBbGxvd2VkXG4gICAgNDA2LCAvLyBOb3QgQWNjZXB0YWJsZVxuICAgIDQwNywgLy8gUHJveHkgQXV0aGVudGljYXRpb24gUmVxdWlyZWRcbiAgICA0MDgsIC8vIFJlcXVlc3QgVGltZW91dFxuICAgIDQwOSwgLy8gQ29uZmxpY3RcbiAgICA0MjIsIC8vIFVucHJvY2Vzc2FibGUgRW50aXR5XG5dO1xuLyoqXG4gKiBEbyBub3QgcmVseSBvbiBnbG9iYWxUaGlzLlJlc3BvbnNlLCByYXRoZXIganVzdFxuICogZG8gZHVjayB0eXBpbmdcbiAqL1xuZnVuY3Rpb24gaXNSZXNwb25zZSh4KSB7XG4gICAgaWYgKHggPT0gbnVsbCB8fCB0eXBlb2YgeCAhPT0gXCJvYmplY3RcIilcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIHJldHVybiBcInN0YXR1c1wiIGluIHggJiYgXCJzdGF0dXNUZXh0XCIgaW4geCAmJiBcInRleHRcIiBpbiB4O1xufVxuLyoqXG4gKiBVdGlsaXR5IGVycm9yIHRvIHByb3Blcmx5IGhhbmRsZSBmYWlsZWQgcmVxdWVzdHNcbiAqL1xuY2xhc3MgSFRUUEVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKHN0YXR1cywgbWVzc2FnZSwgcmVzcG9uc2UpIHtcbiAgICAgICAgc3VwZXIoYEhUVFAgJHtzdGF0dXN9OiAke21lc3NhZ2V9YCk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcInN0YXR1c1wiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogdm9pZCAwXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJ0ZXh0XCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiB2b2lkIDBcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcInJlc3BvbnNlXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiB2b2lkIDBcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMuc3RhdHVzID0gc3RhdHVzO1xuICAgICAgICB0aGlzLnRleHQgPSBtZXNzYWdlO1xuICAgICAgICB0aGlzLnJlc3BvbnNlID0gcmVzcG9uc2U7XG4gICAgfVxuICAgIHN0YXRpYyBhc3luYyBmcm9tUmVzcG9uc2UocmVzcG9uc2UsIG9wdGlvbnMpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIHJldHVybiBuZXcgSFRUUEVycm9yKHJlc3BvbnNlLnN0YXR1cywgYXdhaXQgcmVzcG9uc2UudGV4dCgpLCBvcHRpb25zPy5pbmNsdWRlUmVzcG9uc2UgPyByZXNwb25zZSA6IHVuZGVmaW5lZCk7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2gge1xuICAgICAgICAgICAgcmV0dXJuIG5ldyBIVFRQRXJyb3IocmVzcG9uc2Uuc3RhdHVzLCByZXNwb25zZS5zdGF0dXNUZXh0LCBvcHRpb25zPy5pbmNsdWRlUmVzcG9uc2UgPyByZXNwb25zZSA6IHVuZGVmaW5lZCk7XG4gICAgICAgIH1cbiAgICB9XG59XG4vKipcbiAqIEEgY2xhc3MgdGhhdCBjYW4gYmUgdXNlZCB0byBtYWtlIGFzeW5jIGNhbGxzIHdpdGggY29uY3VycmVuY3kgYW5kIHJldHJ5IGxvZ2ljLlxuICpcbiAqIFRoaXMgaXMgdXNlZnVsIGZvciBtYWtpbmcgY2FsbHMgdG8gYW55IGtpbmQgb2YgXCJleHBlbnNpdmVcIiBleHRlcm5hbCByZXNvdXJjZSxcbiAqIGJlIGl0IGJlY2F1c2UgaXQncyByYXRlLWxpbWl0ZWQsIHN1YmplY3QgdG8gbmV0d29yayBpc3N1ZXMsIGV0Yy5cbiAqXG4gKiBDb25jdXJyZW50IGNhbGxzIGFyZSBsaW1pdGVkIGJ5IHRoZSBgbWF4Q29uY3VycmVuY3lgIHBhcmFtZXRlciwgd2hpY2ggZGVmYXVsdHNcbiAqIHRvIGBJbmZpbml0eWAuIFRoaXMgbWVhbnMgdGhhdCBieSBkZWZhdWx0LCBhbGwgY2FsbHMgd2lsbCBiZSBtYWRlIGluIHBhcmFsbGVsLlxuICpcbiAqIFJldHJpZXMgYXJlIGxpbWl0ZWQgYnkgdGhlIGBtYXhSZXRyaWVzYCBwYXJhbWV0ZXIsIHdoaWNoIGRlZmF1bHRzIHRvIDUuIFRoaXNcbiAqIG1lYW5zIHRoYXQgYnkgZGVmYXVsdCwgZWFjaCBjYWxsIHdpbGwgYmUgcmV0cmllZCB1cCB0byA1IHRpbWVzLCB3aXRoIGFuXG4gKiBleHBvbmVudGlhbCBiYWNrb2ZmIGJldHdlZW4gZWFjaCBhdHRlbXB0LlxuICovXG5jbGFzcyBBc3luY0NhbGxlciB7XG4gICAgY29uc3RydWN0b3IocGFyYW1zKSB7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm1heENvbmN1cnJlbmN5XCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiB2b2lkIDBcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm1heFJldHJpZXNcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IHZvaWQgMFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwicXVldWVcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IHZvaWQgMFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwib25GYWlsZWRSZXNwb25zZUhvb2tcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IHZvaWQgMFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwiY3VzdG9tRmV0Y2hcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IHZvaWQgMFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5tYXhDb25jdXJyZW5jeSA9IHBhcmFtcy5tYXhDb25jdXJyZW5jeSA/PyBJbmZpbml0eTtcbiAgICAgICAgdGhpcy5tYXhSZXRyaWVzID0gcGFyYW1zLm1heFJldHJpZXMgPz8gNDtcbiAgICAgICAgaWYgKFwiZGVmYXVsdFwiIGluIHBfcXVldWVfMS5kZWZhdWx0KSB7XG4gICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLWV4cGxpY2l0LWFueVxuICAgICAgICAgICAgdGhpcy5xdWV1ZSA9IG5ldyBwX3F1ZXVlXzEuZGVmYXVsdC5kZWZhdWx0KHtcbiAgICAgICAgICAgICAgICBjb25jdXJyZW5jeTogdGhpcy5tYXhDb25jdXJyZW5jeSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby1leHBsaWNpdC1hbnlcbiAgICAgICAgICAgIHRoaXMucXVldWUgPSBuZXcgcF9xdWV1ZV8xLmRlZmF1bHQoeyBjb25jdXJyZW5jeTogdGhpcy5tYXhDb25jdXJyZW5jeSB9KTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLm9uRmFpbGVkUmVzcG9uc2VIb29rID0gcGFyYW1zPy5vbkZhaWxlZFJlc3BvbnNlSG9vaztcbiAgICAgICAgdGhpcy5jdXN0b21GZXRjaCA9IHBhcmFtcy5mZXRjaDtcbiAgICB9XG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby1leHBsaWNpdC1hbnlcbiAgICBjYWxsKGNhbGxhYmxlLCAuLi5hcmdzKSB7XG4gICAgICAgIGNvbnN0IG9uRmFpbGVkUmVzcG9uc2VIb29rID0gdGhpcy5vbkZhaWxlZFJlc3BvbnNlSG9vaztcbiAgICAgICAgcmV0dXJuIHRoaXMucXVldWUuYWRkKCgpID0+ICgwLCBwX3JldHJ5XzEuZGVmYXVsdCkoKCkgPT4gY2FsbGFibGUoLi4uYXJncykuY2F0Y2goYXN5bmMgKGVycm9yKSA9PiB7XG4gICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8taW5zdGFuY2VvZi9uby1pbnN0YW5jZW9mXG4gICAgICAgICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAoaXNSZXNwb25zZShlcnJvcikpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBhd2FpdCBIVFRQRXJyb3IuZnJvbVJlc3BvbnNlKGVycm9yLCB7XG4gICAgICAgICAgICAgICAgICAgIGluY2x1ZGVSZXNwb25zZTogISFvbkZhaWxlZFJlc3BvbnNlSG9vayxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvcik7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pLCB7XG4gICAgICAgICAgICBhc3luYyBvbkZhaWxlZEF0dGVtcHQoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBpZiAoZXJyb3IubWVzc2FnZS5zdGFydHNXaXRoKFwiQ2FuY2VsXCIpIHx8XG4gICAgICAgICAgICAgICAgICAgIGVycm9yLm1lc3NhZ2Uuc3RhcnRzV2l0aChcIlRpbWVvdXRFcnJvclwiKSB8fFxuICAgICAgICAgICAgICAgICAgICBlcnJvci5tZXNzYWdlLnN0YXJ0c1dpdGgoXCJBYm9ydEVycm9yXCIpKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLWV4cGxpY2l0LWFueVxuICAgICAgICAgICAgICAgIGlmIChlcnJvcj8uY29kZSA9PT0gXCJFQ09OTkFCT1JURURcIikge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgSFRUUEVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChTVEFUVVNfTk9fUkVUUlkuaW5jbHVkZXMoZXJyb3Iuc3RhdHVzKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgaWYgKG9uRmFpbGVkUmVzcG9uc2VIb29rICYmIGVycm9yLnJlc3BvbnNlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBvbkZhaWxlZFJlc3BvbnNlSG9vayhlcnJvci5yZXNwb25zZSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgLy8gSWYgbmVlZGVkIHdlIGNhbiBjaGFuZ2Ugc29tZSBvZiB0aGUgZGVmYXVsdHMgaGVyZSxcbiAgICAgICAgICAgIC8vIGJ1dCB0aGV5J3JlIHF1aXRlIHNlbnNpYmxlLlxuICAgICAgICAgICAgcmV0cmllczogdGhpcy5tYXhSZXRyaWVzLFxuICAgICAgICAgICAgcmFuZG9taXplOiB0cnVlLFxuICAgICAgICB9KSwgeyB0aHJvd09uVGltZW91dDogdHJ1ZSB9KTtcbiAgICB9XG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby1leHBsaWNpdC1hbnlcbiAgICBjYWxsV2l0aE9wdGlvbnMob3B0aW9ucywgY2FsbGFibGUsIC4uLmFyZ3MpIHtcbiAgICAgICAgLy8gTm90ZSB0aGlzIGRvZXNuJ3QgY2FuY2VsIHRoZSB1bmRlcmx5aW5nIHJlcXVlc3QsXG4gICAgICAgIC8vIHdoZW4gYXZhaWxhYmxlIHByZWZlciB0byB1c2UgdGhlIHNpZ25hbCBvcHRpb24gb2YgdGhlIHVuZGVybHlpbmcgY2FsbFxuICAgICAgICBpZiAob3B0aW9ucy5zaWduYWwpIHtcbiAgICAgICAgICAgIHJldHVybiBQcm9taXNlLnJhY2UoW1xuICAgICAgICAgICAgICAgIHRoaXMuY2FsbChjYWxsYWJsZSwgLi4uYXJncyksXG4gICAgICAgICAgICAgICAgbmV3IFByb21pc2UoKF8sIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBvcHRpb25zLnNpZ25hbD8uYWRkRXZlbnRMaXN0ZW5lcihcImFib3J0XCIsICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlamVjdChuZXcgRXJyb3IoXCJBYm9ydEVycm9yXCIpKTtcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICBdKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5jYWxsKGNhbGxhYmxlLCAuLi5hcmdzKTtcbiAgICB9XG4gICAgZmV0Y2goLi4uYXJncykge1xuICAgICAgICBjb25zdCBmZXRjaEZuID0gdGhpcy5jdXN0b21GZXRjaCA/PyAoMCwgZmV0Y2hfanNfMS5fZ2V0RmV0Y2hJbXBsZW1lbnRhdGlvbikoKTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2FsbCgoKSA9PiBmZXRjaEZuKC4uLmFyZ3MpLnRoZW4oKHJlcykgPT4gKHJlcy5vayA/IHJlcyA6IFByb21pc2UucmVqZWN0KHJlcykpKSk7XG4gICAgfVxufVxuZXhwb3J0cy5Bc3luY0NhbGxlciA9IEFzeW5jQ2FsbGVyO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/async_caller.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/env.cjs":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/env.cjs ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getEnvironmentVariable = void 0;\nfunction getEnvironmentVariable(name) {\n    // Certain setups (Deno, frontend) will throw an error if you try to access environment variables\n    try {\n        return typeof process !== \"undefined\"\n            ? // eslint-disable-next-line no-process-env\n                process.env?.[name]\n            : undefined;\n    }\n    catch (e) {\n        return undefined;\n    }\n}\nexports.getEnvironmentVariable = getEnvironmentVariable;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuMC43MF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vbGFuZ2dyYXBoLXNkay9kaXN0L3V0aWxzL2Vudi5janMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsOEJBQThCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BsYW5nY2hhaW4rbGFuZ2dyYXBoLXNka0AwLjAuNzBfQGxhbmdjaGFpbitjb3JlQDAuMy4zOV9vcGVuYWlANC44NS4xX3dzQDguMTguMF96b2RAMy4yMy44X19fcmVhY3RAMTkuMC4wL25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1zZGsvZGlzdC91dGlscy9lbnYuY2pzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5nZXRFbnZpcm9ubWVudFZhcmlhYmxlID0gdm9pZCAwO1xuZnVuY3Rpb24gZ2V0RW52aXJvbm1lbnRWYXJpYWJsZShuYW1lKSB7XG4gICAgLy8gQ2VydGFpbiBzZXR1cHMgKERlbm8sIGZyb250ZW5kKSB3aWxsIHRocm93IGFuIGVycm9yIGlmIHlvdSB0cnkgdG8gYWNjZXNzIGVudmlyb25tZW50IHZhcmlhYmxlc1xuICAgIHRyeSB7XG4gICAgICAgIHJldHVybiB0eXBlb2YgcHJvY2VzcyAhPT0gXCJ1bmRlZmluZWRcIlxuICAgICAgICAgICAgPyAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tcHJvY2Vzcy1lbnZcbiAgICAgICAgICAgICAgICBwcm9jZXNzLmVudj8uW25hbWVdXG4gICAgICAgICAgICA6IHVuZGVmaW5lZDtcbiAgICB9XG4gICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG59XG5leHBvcnRzLmdldEVudmlyb25tZW50VmFyaWFibGUgPSBnZXRFbnZpcm9ubWVudFZhcmlhYmxlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/env.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/signals.cjs":
/*!**************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/signals.cjs ***!
  \**************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.mergeSignals = void 0;\nfunction mergeSignals(...signals) {\n    const nonZeroSignals = signals.filter((signal) => signal != null);\n    if (nonZeroSignals.length === 0)\n        return undefined;\n    if (nonZeroSignals.length === 1)\n        return nonZeroSignals[0];\n    const controller = new AbortController();\n    for (const signal of signals) {\n        if (signal?.aborted) {\n            controller.abort(signal.reason);\n            return controller.signal;\n        }\n        signal?.addEventListener(\"abort\", () => controller.abort(signal.reason), {\n            once: true,\n        });\n    }\n    return controller.signal;\n}\nexports.mergeSignals = mergeSignals;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuMC43MF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vbGFuZ2dyYXBoLXNkay9kaXN0L3V0aWxzL3NpZ25hbHMuY2pzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BsYW5nY2hhaW4rbGFuZ2dyYXBoLXNka0AwLjAuNzBfQGxhbmdjaGFpbitjb3JlQDAuMy4zOV9vcGVuYWlANC44NS4xX3dzQDguMTguMF96b2RAMy4yMy44X19fcmVhY3RAMTkuMC4wL25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2xhbmdncmFwaC1zZGsvZGlzdC91dGlscy9zaWduYWxzLmNqcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMubWVyZ2VTaWduYWxzID0gdm9pZCAwO1xuZnVuY3Rpb24gbWVyZ2VTaWduYWxzKC4uLnNpZ25hbHMpIHtcbiAgICBjb25zdCBub25aZXJvU2lnbmFscyA9IHNpZ25hbHMuZmlsdGVyKChzaWduYWwpID0+IHNpZ25hbCAhPSBudWxsKTtcbiAgICBpZiAobm9uWmVyb1NpZ25hbHMubGVuZ3RoID09PSAwKVxuICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIGlmIChub25aZXJvU2lnbmFscy5sZW5ndGggPT09IDEpXG4gICAgICAgIHJldHVybiBub25aZXJvU2lnbmFsc1swXTtcbiAgICBjb25zdCBjb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xuICAgIGZvciAoY29uc3Qgc2lnbmFsIG9mIHNpZ25hbHMpIHtcbiAgICAgICAgaWYgKHNpZ25hbD8uYWJvcnRlZCkge1xuICAgICAgICAgICAgY29udHJvbGxlci5hYm9ydChzaWduYWwucmVhc29uKTtcbiAgICAgICAgICAgIHJldHVybiBjb250cm9sbGVyLnNpZ25hbDtcbiAgICAgICAgfVxuICAgICAgICBzaWduYWw/LmFkZEV2ZW50TGlzdGVuZXIoXCJhYm9ydFwiLCAoKSA9PiBjb250cm9sbGVyLmFib3J0KHNpZ25hbC5yZWFzb24pLCB7XG4gICAgICAgICAgICBvbmNlOiB0cnVlLFxuICAgICAgICB9KTtcbiAgICB9XG4gICAgcmV0dXJuIGNvbnRyb2xsZXIuc2lnbmFsO1xufVxuZXhwb3J0cy5tZXJnZVNpZ25hbHMgPSBtZXJnZVNpZ25hbHM7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/signals.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/sse.cjs":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/sse.cjs ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SSEDecoder = exports.BytesLineDecoder = void 0;\nconst CR = \"\\r\".charCodeAt(0);\nconst LF = \"\\n\".charCodeAt(0);\nconst NULL = \"\\0\".charCodeAt(0);\nconst COLON = \":\".charCodeAt(0);\nconst SPACE = \" \".charCodeAt(0);\nconst TRAILING_NEWLINE = [CR, LF];\nclass BytesLineDecoder extends TransformStream {\n    constructor() {\n        let buffer = [];\n        let trailingCr = false;\n        super({\n            start() {\n                buffer = [];\n                trailingCr = false;\n            },\n            transform(chunk, controller) {\n                // See https://docs.python.org/3/glossary.html#term-universal-newlines\n                let text = chunk;\n                // Handle trailing CR from previous chunk\n                if (trailingCr) {\n                    text = joinArrays([[CR], text]);\n                    trailingCr = false;\n                }\n                // Check for trailing CR in current chunk\n                if (text.length > 0 && text.at(-1) === CR) {\n                    trailingCr = true;\n                    text = text.subarray(0, -1);\n                }\n                if (!text.length)\n                    return;\n                const trailingNewline = TRAILING_NEWLINE.includes(text.at(-1));\n                const lastIdx = text.length - 1;\n                const { lines } = text.reduce((acc, cur, idx) => {\n                    if (acc.from > idx)\n                        return acc;\n                    if (cur === CR || cur === LF) {\n                        acc.lines.push(text.subarray(acc.from, idx));\n                        if (cur === CR && text[idx + 1] === LF) {\n                            acc.from = idx + 2;\n                        }\n                        else {\n                            acc.from = idx + 1;\n                        }\n                    }\n                    if (idx === lastIdx && acc.from <= lastIdx) {\n                        acc.lines.push(text.subarray(acc.from));\n                    }\n                    return acc;\n                }, { lines: [], from: 0 });\n                if (lines.length === 1 && !trailingNewline) {\n                    buffer.push(lines[0]);\n                    return;\n                }\n                if (buffer.length) {\n                    // Include existing buffer in first line\n                    buffer.push(lines[0]);\n                    lines[0] = joinArrays(buffer);\n                    buffer = [];\n                }\n                if (!trailingNewline) {\n                    // If the last segment is not newline terminated,\n                    // buffer it for the next chunk\n                    if (lines.length)\n                        buffer = [lines.pop()];\n                }\n                // Enqueue complete lines\n                for (const line of lines) {\n                    controller.enqueue(line);\n                }\n            },\n            flush(controller) {\n                if (buffer.length) {\n                    controller.enqueue(joinArrays(buffer));\n                }\n            },\n        });\n    }\n}\nexports.BytesLineDecoder = BytesLineDecoder;\nclass SSEDecoder extends TransformStream {\n    constructor() {\n        let event = \"\";\n        let data = [];\n        let lastEventId = \"\";\n        let retry = null;\n        const decoder = new TextDecoder();\n        super({\n            transform(chunk, controller) {\n                // Handle empty line case\n                if (!chunk.length) {\n                    if (!event && !data.length && !lastEventId && retry == null)\n                        return;\n                    const sse = {\n                        event,\n                        data: data.length ? decodeArraysToJson(decoder, data) : null,\n                    };\n                    // NOTE: as per the SSE spec, do not reset lastEventId\n                    event = \"\";\n                    data = [];\n                    retry = null;\n                    controller.enqueue(sse);\n                    return;\n                }\n                // Ignore comments\n                if (chunk[0] === COLON)\n                    return;\n                const sepIdx = chunk.indexOf(COLON);\n                if (sepIdx === -1)\n                    return;\n                const fieldName = decoder.decode(chunk.subarray(0, sepIdx));\n                let value = chunk.subarray(sepIdx + 1);\n                if (value[0] === SPACE)\n                    value = value.subarray(1);\n                if (fieldName === \"event\") {\n                    event = decoder.decode(value);\n                }\n                else if (fieldName === \"data\") {\n                    data.push(value);\n                }\n                else if (fieldName === \"id\") {\n                    if (value.indexOf(NULL) === -1)\n                        lastEventId = decoder.decode(value);\n                }\n                else if (fieldName === \"retry\") {\n                    const retryNum = Number.parseInt(decoder.decode(value));\n                    if (!Number.isNaN(retryNum))\n                        retry = retryNum;\n                }\n            },\n            flush(controller) {\n                if (event) {\n                    controller.enqueue({\n                        event,\n                        data: data.length ? decodeArraysToJson(decoder, data) : null,\n                    });\n                }\n            },\n        });\n    }\n}\nexports.SSEDecoder = SSEDecoder;\nfunction joinArrays(data) {\n    const totalLength = data.reduce((acc, curr) => acc + curr.length, 0);\n    let merged = new Uint8Array(totalLength);\n    let offset = 0;\n    for (const c of data) {\n        merged.set(c, offset);\n        offset += c.length;\n    }\n    return merged;\n}\nfunction decodeArraysToJson(decoder, data) {\n    return JSON.parse(decoder.decode(joinArrays(data)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuMC43MF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vbGFuZ2dyYXBoLXNkay9kaXN0L3V0aWxzL3NzZS5janMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0JBQWtCLEdBQUcsd0JBQXdCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixRQUFRO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixJQUFJLG9CQUFvQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0Esd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AbGFuZ2NoYWluK2xhbmdncmFwaC1zZGtAMC4wLjcwX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fX3JlYWN0QDE5LjAuMC9ub2RlX21vZHVsZXMvQGxhbmdjaGFpbi9sYW5nZ3JhcGgtc2RrL2Rpc3QvdXRpbHMvc3NlLmNqcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuU1NFRGVjb2RlciA9IGV4cG9ydHMuQnl0ZXNMaW5lRGVjb2RlciA9IHZvaWQgMDtcbmNvbnN0IENSID0gXCJcXHJcIi5jaGFyQ29kZUF0KDApO1xuY29uc3QgTEYgPSBcIlxcblwiLmNoYXJDb2RlQXQoMCk7XG5jb25zdCBOVUxMID0gXCJcXDBcIi5jaGFyQ29kZUF0KDApO1xuY29uc3QgQ09MT04gPSBcIjpcIi5jaGFyQ29kZUF0KDApO1xuY29uc3QgU1BBQ0UgPSBcIiBcIi5jaGFyQ29kZUF0KDApO1xuY29uc3QgVFJBSUxJTkdfTkVXTElORSA9IFtDUiwgTEZdO1xuY2xhc3MgQnl0ZXNMaW5lRGVjb2RlciBleHRlbmRzIFRyYW5zZm9ybVN0cmVhbSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIGxldCBidWZmZXIgPSBbXTtcbiAgICAgICAgbGV0IHRyYWlsaW5nQ3IgPSBmYWxzZTtcbiAgICAgICAgc3VwZXIoe1xuICAgICAgICAgICAgc3RhcnQoKSB7XG4gICAgICAgICAgICAgICAgYnVmZmVyID0gW107XG4gICAgICAgICAgICAgICAgdHJhaWxpbmdDciA9IGZhbHNlO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHRyYW5zZm9ybShjaHVuaywgY29udHJvbGxlcikge1xuICAgICAgICAgICAgICAgIC8vIFNlZSBodHRwczovL2RvY3MucHl0aG9uLm9yZy8zL2dsb3NzYXJ5Lmh0bWwjdGVybS11bml2ZXJzYWwtbmV3bGluZXNcbiAgICAgICAgICAgICAgICBsZXQgdGV4dCA9IGNodW5rO1xuICAgICAgICAgICAgICAgIC8vIEhhbmRsZSB0cmFpbGluZyBDUiBmcm9tIHByZXZpb3VzIGNodW5rXG4gICAgICAgICAgICAgICAgaWYgKHRyYWlsaW5nQ3IpIHtcbiAgICAgICAgICAgICAgICAgICAgdGV4dCA9IGpvaW5BcnJheXMoW1tDUl0sIHRleHRdKTtcbiAgICAgICAgICAgICAgICAgICAgdHJhaWxpbmdDciA9IGZhbHNlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyBDaGVjayBmb3IgdHJhaWxpbmcgQ1IgaW4gY3VycmVudCBjaHVua1xuICAgICAgICAgICAgICAgIGlmICh0ZXh0Lmxlbmd0aCA+IDAgJiYgdGV4dC5hdCgtMSkgPT09IENSKSB7XG4gICAgICAgICAgICAgICAgICAgIHRyYWlsaW5nQ3IgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICB0ZXh0ID0gdGV4dC5zdWJhcnJheSgwLCAtMSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICghdGV4dC5sZW5ndGgpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICBjb25zdCB0cmFpbGluZ05ld2xpbmUgPSBUUkFJTElOR19ORVdMSU5FLmluY2x1ZGVzKHRleHQuYXQoLTEpKTtcbiAgICAgICAgICAgICAgICBjb25zdCBsYXN0SWR4ID0gdGV4dC5sZW5ndGggLSAxO1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgbGluZXMgfSA9IHRleHQucmVkdWNlKChhY2MsIGN1ciwgaWR4KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChhY2MuZnJvbSA+IGlkeClcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBhY2M7XG4gICAgICAgICAgICAgICAgICAgIGlmIChjdXIgPT09IENSIHx8IGN1ciA9PT0gTEYpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjYy5saW5lcy5wdXNoKHRleHQuc3ViYXJyYXkoYWNjLmZyb20sIGlkeCkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGN1ciA9PT0gQ1IgJiYgdGV4dFtpZHggKyAxXSA9PT0gTEYpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY2MuZnJvbSA9IGlkeCArIDI7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhY2MuZnJvbSA9IGlkeCArIDE7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgaWYgKGlkeCA9PT0gbGFzdElkeCAmJiBhY2MuZnJvbSA8PSBsYXN0SWR4KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhY2MubGluZXMucHVzaCh0ZXh0LnN1YmFycmF5KGFjYy5mcm9tKSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFjYztcbiAgICAgICAgICAgICAgICB9LCB7IGxpbmVzOiBbXSwgZnJvbTogMCB9KTtcbiAgICAgICAgICAgICAgICBpZiAobGluZXMubGVuZ3RoID09PSAxICYmICF0cmFpbGluZ05ld2xpbmUpIHtcbiAgICAgICAgICAgICAgICAgICAgYnVmZmVyLnB1c2gobGluZXNbMF0pO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChidWZmZXIubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIEluY2x1ZGUgZXhpc3RpbmcgYnVmZmVyIGluIGZpcnN0IGxpbmVcbiAgICAgICAgICAgICAgICAgICAgYnVmZmVyLnB1c2gobGluZXNbMF0pO1xuICAgICAgICAgICAgICAgICAgICBsaW5lc1swXSA9IGpvaW5BcnJheXMoYnVmZmVyKTtcbiAgICAgICAgICAgICAgICAgICAgYnVmZmVyID0gW107XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmICghdHJhaWxpbmdOZXdsaW5lKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIElmIHRoZSBsYXN0IHNlZ21lbnQgaXMgbm90IG5ld2xpbmUgdGVybWluYXRlZCxcbiAgICAgICAgICAgICAgICAgICAgLy8gYnVmZmVyIGl0IGZvciB0aGUgbmV4dCBjaHVua1xuICAgICAgICAgICAgICAgICAgICBpZiAobGluZXMubGVuZ3RoKVxuICAgICAgICAgICAgICAgICAgICAgICAgYnVmZmVyID0gW2xpbmVzLnBvcCgpXTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gRW5xdWV1ZSBjb21wbGV0ZSBsaW5lc1xuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgbGluZSBvZiBsaW5lcykge1xuICAgICAgICAgICAgICAgICAgICBjb250cm9sbGVyLmVucXVldWUobGluZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGZsdXNoKGNvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgICAgICBpZiAoYnVmZmVyLmxlbmd0aCkge1xuICAgICAgICAgICAgICAgICAgICBjb250cm9sbGVyLmVucXVldWUoam9pbkFycmF5cyhidWZmZXIpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICB9KTtcbiAgICB9XG59XG5leHBvcnRzLkJ5dGVzTGluZURlY29kZXIgPSBCeXRlc0xpbmVEZWNvZGVyO1xuY2xhc3MgU1NFRGVjb2RlciBleHRlbmRzIFRyYW5zZm9ybVN0cmVhbSB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIGxldCBldmVudCA9IFwiXCI7XG4gICAgICAgIGxldCBkYXRhID0gW107XG4gICAgICAgIGxldCBsYXN0RXZlbnRJZCA9IFwiXCI7XG4gICAgICAgIGxldCByZXRyeSA9IG51bGw7XG4gICAgICAgIGNvbnN0IGRlY29kZXIgPSBuZXcgVGV4dERlY29kZXIoKTtcbiAgICAgICAgc3VwZXIoe1xuICAgICAgICAgICAgdHJhbnNmb3JtKGNodW5rLCBjb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgLy8gSGFuZGxlIGVtcHR5IGxpbmUgY2FzZVxuICAgICAgICAgICAgICAgIGlmICghY2h1bmsubGVuZ3RoKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICghZXZlbnQgJiYgIWRhdGEubGVuZ3RoICYmICFsYXN0RXZlbnRJZCAmJiByZXRyeSA9PSBudWxsKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBzc2UgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBldmVudCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IGRhdGEubGVuZ3RoID8gZGVjb2RlQXJyYXlzVG9Kc29uKGRlY29kZXIsIGRhdGEpIDogbnVsbCxcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgLy8gTk9URTogYXMgcGVyIHRoZSBTU0Ugc3BlYywgZG8gbm90IHJlc2V0IGxhc3RFdmVudElkXG4gICAgICAgICAgICAgICAgICAgIGV2ZW50ID0gXCJcIjtcbiAgICAgICAgICAgICAgICAgICAgZGF0YSA9IFtdO1xuICAgICAgICAgICAgICAgICAgICByZXRyeSA9IG51bGw7XG4gICAgICAgICAgICAgICAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZShzc2UpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIElnbm9yZSBjb21tZW50c1xuICAgICAgICAgICAgICAgIGlmIChjaHVua1swXSA9PT0gQ09MT04pXG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICBjb25zdCBzZXBJZHggPSBjaHVuay5pbmRleE9mKENPTE9OKTtcbiAgICAgICAgICAgICAgICBpZiAoc2VwSWR4ID09PSAtMSlcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkTmFtZSA9IGRlY29kZXIuZGVjb2RlKGNodW5rLnN1YmFycmF5KDAsIHNlcElkeCkpO1xuICAgICAgICAgICAgICAgIGxldCB2YWx1ZSA9IGNodW5rLnN1YmFycmF5KHNlcElkeCArIDEpO1xuICAgICAgICAgICAgICAgIGlmICh2YWx1ZVswXSA9PT0gU1BBQ0UpXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlID0gdmFsdWUuc3ViYXJyYXkoMSk7XG4gICAgICAgICAgICAgICAgaWYgKGZpZWxkTmFtZSA9PT0gXCJldmVudFwiKSB7XG4gICAgICAgICAgICAgICAgICAgIGV2ZW50ID0gZGVjb2Rlci5kZWNvZGUodmFsdWUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmIChmaWVsZE5hbWUgPT09IFwiZGF0YVwiKSB7XG4gICAgICAgICAgICAgICAgICAgIGRhdGEucHVzaCh2YWx1ZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGZpZWxkTmFtZSA9PT0gXCJpZFwiKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICh2YWx1ZS5pbmRleE9mKE5VTEwpID09PSAtMSlcbiAgICAgICAgICAgICAgICAgICAgICAgIGxhc3RFdmVudElkID0gZGVjb2Rlci5kZWNvZGUodmFsdWUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmIChmaWVsZE5hbWUgPT09IFwicmV0cnlcIikge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZXRyeU51bSA9IE51bWJlci5wYXJzZUludChkZWNvZGVyLmRlY29kZSh2YWx1ZSkpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIU51bWJlci5pc05hTihyZXRyeU51bSkpXG4gICAgICAgICAgICAgICAgICAgICAgICByZXRyeSA9IHJldHJ5TnVtO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBmbHVzaChjb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgaWYgKGV2ZW50KSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnRyb2xsZXIuZW5xdWV1ZSh7XG4gICAgICAgICAgICAgICAgICAgICAgICBldmVudCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IGRhdGEubGVuZ3RoID8gZGVjb2RlQXJyYXlzVG9Kc29uKGRlY29kZXIsIGRhdGEpIDogbnVsbCxcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0cy5TU0VEZWNvZGVyID0gU1NFRGVjb2RlcjtcbmZ1bmN0aW9uIGpvaW5BcnJheXMoZGF0YSkge1xuICAgIGNvbnN0IHRvdGFsTGVuZ3RoID0gZGF0YS5yZWR1Y2UoKGFjYywgY3VycikgPT4gYWNjICsgY3Vyci5sZW5ndGgsIDApO1xuICAgIGxldCBtZXJnZWQgPSBuZXcgVWludDhBcnJheSh0b3RhbExlbmd0aCk7XG4gICAgbGV0IG9mZnNldCA9IDA7XG4gICAgZm9yIChjb25zdCBjIG9mIGRhdGEpIHtcbiAgICAgICAgbWVyZ2VkLnNldChjLCBvZmZzZXQpO1xuICAgICAgICBvZmZzZXQgKz0gYy5sZW5ndGg7XG4gICAgfVxuICAgIHJldHVybiBtZXJnZWQ7XG59XG5mdW5jdGlvbiBkZWNvZGVBcnJheXNUb0pzb24oZGVjb2RlciwgZGF0YSkge1xuICAgIHJldHVybiBKU09OLnBhcnNlKGRlY29kZXIuZGVjb2RlKGpvaW5BcnJheXMoZGF0YSkpKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/sse.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/stream.cjs":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/stream.cjs ***!
  \*************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.IterableReadableStream = void 0;\n/*\n * Support async iterator syntax for ReadableStreams in all environments.\n * Source: https://github.com/MattiasBuelens/web-streams-polyfill/pull/122#issuecomment-1627354490\n */\nclass IterableReadableStream extends ReadableStream {\n    constructor() {\n        super(...arguments);\n        Object.defineProperty(this, \"reader\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n    }\n    ensureReader() {\n        if (!this.reader) {\n            this.reader = this.getReader();\n        }\n    }\n    async next() {\n        this.ensureReader();\n        try {\n            const result = await this.reader.read();\n            if (result.done) {\n                this.reader.releaseLock(); // release lock when stream becomes closed\n                return {\n                    done: true,\n                    value: undefined,\n                };\n            }\n            else {\n                return {\n                    done: false,\n                    value: result.value,\n                };\n            }\n        }\n        catch (e) {\n            this.reader.releaseLock(); // release lock when stream becomes errored\n            throw e;\n        }\n    }\n    async return() {\n        this.ensureReader();\n        // If wrapped in a Node stream, cancel is already called.\n        if (this.locked) {\n            const cancelPromise = this.reader.cancel(); // cancel first, but don't await yet\n            this.reader.releaseLock(); // release lock first\n            await cancelPromise; // now await it\n        }\n        return { done: true, value: undefined };\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    async throw(e) {\n        this.ensureReader();\n        if (this.locked) {\n            const cancelPromise = this.reader.cancel(); // cancel first, but don't await yet\n            this.reader.releaseLock(); // release lock first\n            await cancelPromise; // now await it\n        }\n        throw e;\n    }\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore Not present in Node 18 types, required in latest Node 22\n    async [Symbol.asyncDispose]() {\n        await this.return();\n    }\n    [Symbol.asyncIterator]() {\n        return this;\n    }\n    static fromReadableStream(stream) {\n        // From https://developer.mozilla.org/en-US/docs/Web/API/Streams_API/Using_readable_streams#reading_the_stream\n        const reader = stream.getReader();\n        return new IterableReadableStream({\n            start(controller) {\n                return pump();\n                function pump() {\n                    return reader.read().then(({ done, value }) => {\n                        // When no more data needs to be consumed, close the stream\n                        if (done) {\n                            controller.close();\n                            return;\n                        }\n                        // Enqueue the next data chunk into our target stream\n                        controller.enqueue(value);\n                        return pump();\n                    });\n                }\n            },\n            cancel() {\n                reader.releaseLock();\n            },\n        });\n    }\n    static fromAsyncGenerator(generator) {\n        return new IterableReadableStream({\n            async pull(controller) {\n                const { value, done } = await generator.next();\n                // When no more data needs to be consumed, close the stream\n                if (done) {\n                    controller.close();\n                }\n                // Fix: `else if (value)` will hang the streaming when nullish value (e.g. empty string) is pulled\n                controller.enqueue(value);\n            },\n            async cancel(reason) {\n                await generator.return(reason);\n            },\n        });\n    }\n}\nexports.IterableReadableStream = IterableReadableStream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/utils/stream.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/index.cjs":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/index.cjs ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/index.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/dist/index.cjs\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuMC43MF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vbGFuZ2dyYXBoLXNkay9pbmRleC5janMiLCJtYXBwaW5ncyI6IkFBQUEsd1BBQTRDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitsYW5nZ3JhcGgtc2RrQDAuMC43MF9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vbGFuZ2dyYXBoLXNkay9pbmRleC5janMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvaW5kZXguY2pzJyk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+langgraph-sdk@0.0.70_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___react@19.0.0/node_modules/@langchain/langgraph-sdk/index.cjs\n");

/***/ })

};
;