"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@graphql-yoga+logger@2.0.0";
exports.ids = ["vendor-chunks/@graphql-yoga+logger@2.0.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@graphql-yoga+logger@2.0.0/node_modules/@graphql-yoga/logger/cjs/index.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@graphql-yoga+logger@2.0.0/node_modules/@graphql-yoga/logger/cjs/index.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable no-console */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createLogger = exports.debugPrefix = exports.errorPrefix = exports.infoPrefix = exports.warnPrefix = void 0;\nconst ansiCodes = {\n    red: '\\x1b[31m',\n    yellow: '\\x1b[33m',\n    magenta: '\\x1b[35m',\n    cyan: '\\x1b[36m',\n    reset: '\\x1b[0m',\n};\nexports.warnPrefix = ansiCodes.yellow + 'WARN' + ansiCodes.reset;\nexports.infoPrefix = ansiCodes.cyan + 'INFO' + ansiCodes.reset;\nexports.errorPrefix = ansiCodes.red + 'ERR' + ansiCodes.reset;\nexports.debugPrefix = ansiCodes.magenta + 'DEBUG' + ansiCodes.reset;\nconst logLevelScores = {\n    debug: 0,\n    info: 1,\n    warn: 2,\n    error: 3,\n    silent: 4,\n};\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nconst noop = () => { };\nconst consoleLog = (prefix) => (...args) => console.log(prefix, ...args);\nconst debugLog = console.debug\n    ? (...args) => console.debug(exports.debugPrefix, ...args)\n    : consoleLog(exports.debugPrefix);\nconst infoLog = console.info\n    ? (...args) => console.info(exports.infoPrefix, ...args)\n    : consoleLog(exports.infoPrefix);\nconst warnLog = console.warn\n    ? (...args) => console.warn(exports.warnPrefix, ...args)\n    : consoleLog(exports.warnPrefix);\nconst errorLog = console.error\n    ? (...args) => console.error(exports.errorPrefix, ...args)\n    : consoleLog(exports.errorPrefix);\nconst createLogger = (logLevel = globalThis.process?.env['DEBUG'] === '1' ? 'debug' : 'info') => {\n    const score = logLevelScores[logLevel];\n    return {\n        debug: score > logLevelScores.debug ? noop : debugLog,\n        info: score > logLevelScores.info ? noop : infoLog,\n        warn: score > logLevelScores.warn ? noop : warnLog,\n        error: score > logLevelScores.error ? noop : errorLog,\n    };\n};\nexports.createLogger = createLogger;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@graphql-yoga+logger@2.0.0/node_modules/@graphql-yoga/logger/cjs/index.js\n");

/***/ })

};
;