/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@anthropic-ai+sdk@0.27.3";
exports.ids = ["vendor-chunks/@anthropic-ai+sdk@0.27.3"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/MultipartBody.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/MultipartBody.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MultipartBody = void 0;\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nclass MultipartBody {\n    constructor(body) {\n        this.body = body;\n    }\n    get [Symbol.toStringTag]() {\n        return 'MultipartBody';\n    }\n}\nexports.MultipartBody = MultipartBody;\n//# sourceMappingURL=MultipartBody.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGFudGhyb3BpYy1haStzZGtAMC4yNy4zL25vZGVfbW9kdWxlcy9AYW50aHJvcGljLWFpL3Nkay9fc2hpbXMvTXVsdGlwYXJ0Qm9keS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BhbnRocm9waWMtYWkrc2RrQDAuMjcuMy9ub2RlX21vZHVsZXMvQGFudGhyb3BpYy1haS9zZGsvX3NoaW1zL011bHRpcGFydEJvZHkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLk11bHRpcGFydEJvZHkgPSB2b2lkIDA7XG4vKipcbiAqIERpc2NsYWltZXI6IG1vZHVsZXMgaW4gX3NoaW1zIGFyZW4ndCBpbnRlbmRlZCB0byBiZSBpbXBvcnRlZCBieSBTREsgdXNlcnMuXG4gKi9cbmNsYXNzIE11bHRpcGFydEJvZHkge1xuICAgIGNvbnN0cnVjdG9yKGJvZHkpIHtcbiAgICAgICAgdGhpcy5ib2R5ID0gYm9keTtcbiAgICB9XG4gICAgZ2V0IFtTeW1ib2wudG9TdHJpbmdUYWddKCkge1xuICAgICAgICByZXR1cm4gJ011bHRpcGFydEJvZHknO1xuICAgIH1cbn1cbmV4cG9ydHMuTXVsdGlwYXJ0Qm9keSA9IE11bHRpcGFydEJvZHk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1NdWx0aXBhcnRCb2R5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/MultipartBody.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/auto/runtime-node.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/auto/runtime-node.js ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\n__exportStar(__webpack_require__(/*! ../node-runtime.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/node-runtime.js\"), exports);\n//# sourceMappingURL=runtime-node.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/auto/runtime-node.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/index.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/index.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nconst shims = __webpack_require__(/*! ./registry */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/registry.js\");\nconst auto = __webpack_require__(/*! @anthropic-ai/sdk/_shims/auto/runtime */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/auto/runtime-node.js\");\nif (!shims.kind) shims.setShims(auto.getRuntime(), { auto: true });\nfor (const property of Object.keys(shims)) {\n  Object.defineProperty(exports, property, {\n    get() {\n      return shims[property];\n    },\n  });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGFudGhyb3BpYy1haStzZGtAMC4yNy4zL25vZGVfbW9kdWxlcy9AYW50aHJvcGljLWFpL3Nkay9fc2hpbXMvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EsY0FBYyxtQkFBTyxDQUFDLHlIQUFZO0FBQ2xDLGFBQWEsbUJBQU8sQ0FBQyw2SkFBdUM7QUFDNUQscURBQXFELFlBQVk7QUFDakU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGFudGhyb3BpYy1haStzZGtAMC4yNy4zL25vZGVfbW9kdWxlcy9AYW50aHJvcGljLWFpL3Nkay9fc2hpbXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEaXNjbGFpbWVyOiBtb2R1bGVzIGluIF9zaGltcyBhcmVuJ3QgaW50ZW5kZWQgdG8gYmUgaW1wb3J0ZWQgYnkgU0RLIHVzZXJzLlxuICovXG5jb25zdCBzaGltcyA9IHJlcXVpcmUoJy4vcmVnaXN0cnknKTtcbmNvbnN0IGF1dG8gPSByZXF1aXJlKCdAYW50aHJvcGljLWFpL3Nkay9fc2hpbXMvYXV0by9ydW50aW1lJyk7XG5pZiAoIXNoaW1zLmtpbmQpIHNoaW1zLnNldFNoaW1zKGF1dG8uZ2V0UnVudGltZSgpLCB7IGF1dG86IHRydWUgfSk7XG5mb3IgKGNvbnN0IHByb3BlcnR5IG9mIE9iamVjdC5rZXlzKHNoaW1zKSkge1xuICBPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgcHJvcGVydHksIHtcbiAgICBnZXQoKSB7XG4gICAgICByZXR1cm4gc2hpbXNbcHJvcGVydHldO1xuICAgIH0sXG4gIH0pO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/node-runtime.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/node-runtime.js ***!
  \***********************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getRuntime = void 0;\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nconst nf = __importStar(__webpack_require__(/*! node-fetch */ \"(rsc)/./node_modules/.pnpm/node-fetch@2.7.0/node_modules/node-fetch/lib/index.mjs\"));\nconst fd = __importStar(__webpack_require__(/*! formdata-node */ \"(rsc)/./node_modules/.pnpm/formdata-node@4.4.1/node_modules/formdata-node/lib/cjs/index.js\"));\nconst agentkeepalive_1 = __importDefault(__webpack_require__(/*! agentkeepalive */ \"(rsc)/./node_modules/.pnpm/agentkeepalive@4.5.0/node_modules/agentkeepalive/index.js\"));\nconst abort_controller_1 = __webpack_require__(/*! abort-controller */ \"(rsc)/./node_modules/.pnpm/abort-controller@3.0.0/node_modules/abort-controller/dist/abort-controller.js\");\nconst node_fs_1 = __webpack_require__(/*! node:fs */ \"node:fs\");\nconst form_data_encoder_1 = __webpack_require__(/*! form-data-encoder */ \"(rsc)/./node_modules/.pnpm/form-data-encoder@1.7.2/node_modules/form-data-encoder/lib/cjs/index.js\");\nconst node_stream_1 = __webpack_require__(/*! node:stream */ \"node:stream\");\nconst MultipartBody_1 = __webpack_require__(/*! ./MultipartBody.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/MultipartBody.js\");\nconst web_1 = __webpack_require__(/*! node:stream/web */ \"node:stream/web\");\nlet fileFromPathWarned = false;\nasync function fileFromPath(path, ...args) {\n    // this import fails in environments that don't handle export maps correctly, like old versions of Jest\n    const { fileFromPath: _fileFromPath } = await Promise.resolve().then(() => __importStar(__webpack_require__(/*! formdata-node/file-from-path */ \"(rsc)/./node_modules/.pnpm/formdata-node@4.4.1/node_modules/formdata-node/lib/cjs/fileFromPath.js\")));\n    if (!fileFromPathWarned) {\n        console.warn(`fileFromPath is deprecated; use fs.createReadStream(${JSON.stringify(path)}) instead`);\n        fileFromPathWarned = true;\n    }\n    // @ts-ignore\n    return await _fileFromPath(path, ...args);\n}\nconst defaultHttpAgent = new agentkeepalive_1.default({ keepAlive: true, timeout: 5 * 60 * 1000 });\nconst defaultHttpsAgent = new agentkeepalive_1.default.HttpsAgent({ keepAlive: true, timeout: 5 * 60 * 1000 });\nasync function getMultipartRequestOptions(form, opts) {\n    const encoder = new form_data_encoder_1.FormDataEncoder(form);\n    const readable = node_stream_1.Readable.from(encoder);\n    const body = new MultipartBody_1.MultipartBody(readable);\n    const headers = {\n        ...opts.headers,\n        ...encoder.headers,\n        'Content-Length': encoder.contentLength,\n    };\n    return { ...opts, body: body, headers };\n}\nfunction getRuntime() {\n    // Polyfill global object if needed.\n    if (typeof AbortController === 'undefined') {\n        // @ts-expect-error (the types are subtly different, but compatible in practice)\n        globalThis.AbortController = abort_controller_1.AbortController;\n    }\n    return {\n        kind: 'node',\n        fetch: nf.default,\n        Request: nf.Request,\n        Response: nf.Response,\n        Headers: nf.Headers,\n        FormData: fd.FormData,\n        Blob: fd.Blob,\n        File: fd.File,\n        ReadableStream: web_1.ReadableStream,\n        getMultipartRequestOptions,\n        getDefaultAgent: (url) => (url.startsWith('https') ? defaultHttpsAgent : defaultHttpAgent),\n        fileFromPath,\n        isFsReadStream: (value) => value instanceof node_fs_1.ReadStream,\n    };\n}\nexports.getRuntime = getRuntime;\n//# sourceMappingURL=node-runtime.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/node-runtime.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/registry.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/registry.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.setShims = exports.isFsReadStream = exports.fileFromPath = exports.getDefaultAgent = exports.getMultipartRequestOptions = exports.ReadableStream = exports.File = exports.Blob = exports.FormData = exports.Headers = exports.Response = exports.Request = exports.fetch = exports.kind = exports.auto = void 0;\nexports.auto = false;\nexports.kind = undefined;\nexports.fetch = undefined;\nexports.Request = undefined;\nexports.Response = undefined;\nexports.Headers = undefined;\nexports.FormData = undefined;\nexports.Blob = undefined;\nexports.File = undefined;\nexports.ReadableStream = undefined;\nexports.getMultipartRequestOptions = undefined;\nexports.getDefaultAgent = undefined;\nexports.fileFromPath = undefined;\nexports.isFsReadStream = undefined;\nfunction setShims(shims, options = { auto: false }) {\n    if (exports.auto) {\n        throw new Error(`you must \\`import '@anthropic-ai/sdk/shims/${shims.kind}'\\` before importing anything else from @anthropic-ai/sdk`);\n    }\n    if (exports.kind) {\n        throw new Error(`can't \\`import '@anthropic-ai/sdk/shims/${shims.kind}'\\` after \\`import '@anthropic-ai/sdk/shims/${exports.kind}'\\``);\n    }\n    exports.auto = options.auto;\n    exports.kind = shims.kind;\n    exports.fetch = shims.fetch;\n    exports.Request = shims.Request;\n    exports.Response = shims.Response;\n    exports.Headers = shims.Headers;\n    exports.FormData = shims.FormData;\n    exports.Blob = shims.Blob;\n    exports.File = shims.File;\n    exports.ReadableStream = shims.ReadableStream;\n    exports.getMultipartRequestOptions = shims.getMultipartRequestOptions;\n    exports.getDefaultAgent = shims.getDefaultAgent;\n    exports.fileFromPath = shims.fileFromPath;\n    exports.isFsReadStream = shims.isFsReadStream;\n}\nexports.setShims = setShims;\n//# sourceMappingURL=registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/registry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_vendor/partial-json-parser/parser.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_vendor/partial-json-parser/parser.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.partialParse = void 0;\nconst tokenize = (input) => {\n    let current = 0;\n    let tokens = [];\n    while (current < input.length) {\n        let char = input[current];\n        if (char === '\\\\') {\n            current++;\n            continue;\n        }\n        if (char === '{') {\n            tokens.push({\n                type: 'brace',\n                value: '{',\n            });\n            current++;\n            continue;\n        }\n        if (char === '}') {\n            tokens.push({\n                type: 'brace',\n                value: '}',\n            });\n            current++;\n            continue;\n        }\n        if (char === '[') {\n            tokens.push({\n                type: 'paren',\n                value: '[',\n            });\n            current++;\n            continue;\n        }\n        if (char === ']') {\n            tokens.push({\n                type: 'paren',\n                value: ']',\n            });\n            current++;\n            continue;\n        }\n        if (char === ':') {\n            tokens.push({\n                type: 'separator',\n                value: ':',\n            });\n            current++;\n            continue;\n        }\n        if (char === ',') {\n            tokens.push({\n                type: 'delimiter',\n                value: ',',\n            });\n            current++;\n            continue;\n        }\n        if (char === '\"') {\n            let value = '';\n            let danglingQuote = false;\n            char = input[++current];\n            while (char !== '\"') {\n                if (current === input.length) {\n                    danglingQuote = true;\n                    break;\n                }\n                if (char === '\\\\') {\n                    current++;\n                    if (current === input.length) {\n                        danglingQuote = true;\n                        break;\n                    }\n                    value += char + input[current];\n                    char = input[++current];\n                }\n                else {\n                    value += char;\n                    char = input[++current];\n                }\n            }\n            char = input[++current];\n            if (!danglingQuote) {\n                tokens.push({\n                    type: 'string',\n                    value,\n                });\n            }\n            continue;\n        }\n        let WHITESPACE = /\\s/;\n        if (char && WHITESPACE.test(char)) {\n            current++;\n            continue;\n        }\n        let NUMBERS = /[0-9]/;\n        if ((char && NUMBERS.test(char)) || char === '-' || char === '.') {\n            let value = '';\n            if (char === '-') {\n                value += char;\n                char = input[++current];\n            }\n            while ((char && NUMBERS.test(char)) || char === '.') {\n                value += char;\n                char = input[++current];\n            }\n            tokens.push({\n                type: 'number',\n                value,\n            });\n            continue;\n        }\n        let LETTERS = /[a-z]/i;\n        if (char && LETTERS.test(char)) {\n            let value = '';\n            while (char && LETTERS.test(char)) {\n                if (current === input.length) {\n                    break;\n                }\n                value += char;\n                char = input[++current];\n            }\n            if (value == 'true' || value == 'false' || value === 'null') {\n                tokens.push({\n                    type: 'name',\n                    value,\n                });\n            }\n            else {\n                // unknown token, e.g. `nul` which isn't quite `null`\n                current++;\n                continue;\n            }\n            continue;\n        }\n        current++;\n    }\n    return tokens;\n}, strip = (tokens) => {\n    if (tokens.length === 0) {\n        return tokens;\n    }\n    let lastToken = tokens[tokens.length - 1];\n    switch (lastToken.type) {\n        case 'separator':\n            tokens = tokens.slice(0, tokens.length - 1);\n            return strip(tokens);\n            break;\n        case 'number':\n            let lastCharacterOfLastToken = lastToken.value[lastToken.value.length - 1];\n            if (lastCharacterOfLastToken === '.' || lastCharacterOfLastToken === '-') {\n                tokens = tokens.slice(0, tokens.length - 1);\n                return strip(tokens);\n            }\n        case 'string':\n            let tokenBeforeTheLastToken = tokens[tokens.length - 2];\n            if (tokenBeforeTheLastToken?.type === 'delimiter') {\n                tokens = tokens.slice(0, tokens.length - 1);\n                return strip(tokens);\n            }\n            else if (tokenBeforeTheLastToken?.type === 'brace' && tokenBeforeTheLastToken.value === '{') {\n                tokens = tokens.slice(0, tokens.length - 1);\n                return strip(tokens);\n            }\n            break;\n        case 'delimiter':\n            tokens = tokens.slice(0, tokens.length - 1);\n            return strip(tokens);\n            break;\n    }\n    return tokens;\n}, unstrip = (tokens) => {\n    let tail = [];\n    tokens.map((token) => {\n        if (token.type === 'brace') {\n            if (token.value === '{') {\n                tail.push('}');\n            }\n            else {\n                tail.splice(tail.lastIndexOf('}'), 1);\n            }\n        }\n        if (token.type === 'paren') {\n            if (token.value === '[') {\n                tail.push(']');\n            }\n            else {\n                tail.splice(tail.lastIndexOf(']'), 1);\n            }\n        }\n    });\n    if (tail.length > 0) {\n        tail.reverse().map((item) => {\n            if (item === '}') {\n                tokens.push({\n                    type: 'brace',\n                    value: '}',\n                });\n            }\n            else if (item === ']') {\n                tokens.push({\n                    type: 'paren',\n                    value: ']',\n                });\n            }\n        });\n    }\n    return tokens;\n}, generate = (tokens) => {\n    let output = '';\n    tokens.map((token) => {\n        switch (token.type) {\n            case 'string':\n                output += '\"' + token.value + '\"';\n                break;\n            default:\n                output += token.value;\n                break;\n        }\n    });\n    return output;\n}, partialParse = (input) => JSON.parse(generate(unstrip(strip(tokenize(input)))));\nexports.partialParse = partialParse;\n//# sourceMappingURL=parser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_vendor/partial-json-parser/parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/core.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/core.js ***!
  \********************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _AbstractPage_client;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isObj = exports.toBase64 = exports.getRequiredHeader = exports.isHeadersProtocol = exports.isRunningInBrowser = exports.debug = exports.hasOwn = exports.isEmptyObj = exports.maybeCoerceBoolean = exports.maybeCoerceFloat = exports.maybeCoerceInteger = exports.coerceBoolean = exports.coerceFloat = exports.coerceInteger = exports.readEnv = exports.ensurePresent = exports.castToError = exports.sleep = exports.safeJSON = exports.isRequestOptions = exports.createResponseHeaders = exports.PagePromise = exports.AbstractPage = exports.APIClient = exports.APIPromise = exports.createForm = exports.multipartFormRequestOptions = exports.maybeMultipartFormRequestOptions = void 0;\nconst version_1 = __webpack_require__(/*! ./version.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/version.js\");\nconst streaming_1 = __webpack_require__(/*! ./streaming.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/streaming.js\");\nconst error_1 = __webpack_require__(/*! ./error.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/error.js\");\nconst index_1 = __webpack_require__(/*! ./_shims/index.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/index.js\");\nconst uploads_1 = __webpack_require__(/*! ./uploads.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/uploads.js\");\nvar uploads_2 = __webpack_require__(/*! ./uploads.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/uploads.js\");\nObject.defineProperty(exports, \"maybeMultipartFormRequestOptions\", ({ enumerable: true, get: function () { return uploads_2.maybeMultipartFormRequestOptions; } }));\nObject.defineProperty(exports, \"multipartFormRequestOptions\", ({ enumerable: true, get: function () { return uploads_2.multipartFormRequestOptions; } }));\nObject.defineProperty(exports, \"createForm\", ({ enumerable: true, get: function () { return uploads_2.createForm; } }));\nasync function defaultParseResponse(props) {\n    const { response } = props;\n    if (props.options.stream) {\n        debug('response', response.status, response.url, response.headers, response.body);\n        // Note: there is an invariant here that isn't represented in the type system\n        // that if you set `stream: true` the response type must also be `Stream<T>`\n        if (props.options.__streamClass) {\n            return props.options.__streamClass.fromSSEResponse(response, props.controller);\n        }\n        return streaming_1.Stream.fromSSEResponse(response, props.controller);\n    }\n    // fetch refuses to read the body when the status code is 204.\n    if (response.status === 204) {\n        return null;\n    }\n    if (props.options.__binaryResponse) {\n        return response;\n    }\n    const contentType = response.headers.get('content-type');\n    const isJSON = contentType?.includes('application/json') || contentType?.includes('application/vnd.api+json');\n    if (isJSON) {\n        const json = await response.json();\n        debug('response', response.status, response.url, response.headers, json);\n        return json;\n    }\n    const text = await response.text();\n    debug('response', response.status, response.url, response.headers, text);\n    // TODO handle blob, arraybuffer, other content types, etc.\n    return text;\n}\n/**\n * A subclass of `Promise` providing additional helper methods\n * for interacting with the SDK.\n */\nclass APIPromise extends Promise {\n    constructor(responsePromise, parseResponse = defaultParseResponse) {\n        super((resolve) => {\n            // this is maybe a bit weird but this has to be a no-op to not implicitly\n            // parse the response body; instead .then, .catch, .finally are overridden\n            // to parse the response\n            resolve(null);\n        });\n        this.responsePromise = responsePromise;\n        this.parseResponse = parseResponse;\n    }\n    _thenUnwrap(transform) {\n        return new APIPromise(this.responsePromise, async (props) => transform(await this.parseResponse(props)));\n    }\n    /**\n     * Gets the raw `Response` instance instead of parsing the response\n     * data.\n     *\n     * If you want to parse the response body but still get the `Response`\n     * instance, you can use {@link withResponse()}.\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from '@anthropic-ai/sdk'`:\n     * - `import '@anthropic-ai/sdk/shims/node'` (if you're running on Node)\n     * - `import '@anthropic-ai/sdk/shims/web'` (otherwise)\n     */\n    asResponse() {\n        return this.responsePromise.then((p) => p.response);\n    }\n    /**\n     * Gets the parsed response data and the raw `Response` instance.\n     *\n     * If you just want to get the raw `Response` instance without parsing it,\n     * you can use {@link asResponse()}.\n     *\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from '@anthropic-ai/sdk'`:\n     * - `import '@anthropic-ai/sdk/shims/node'` (if you're running on Node)\n     * - `import '@anthropic-ai/sdk/shims/web'` (otherwise)\n     */\n    async withResponse() {\n        const [data, response] = await Promise.all([this.parse(), this.asResponse()]);\n        return { data, response };\n    }\n    parse() {\n        if (!this.parsedPromise) {\n            this.parsedPromise = this.responsePromise.then(this.parseResponse);\n        }\n        return this.parsedPromise;\n    }\n    then(onfulfilled, onrejected) {\n        return this.parse().then(onfulfilled, onrejected);\n    }\n    catch(onrejected) {\n        return this.parse().catch(onrejected);\n    }\n    finally(onfinally) {\n        return this.parse().finally(onfinally);\n    }\n}\nexports.APIPromise = APIPromise;\nclass APIClient {\n    constructor({ baseURL, maxRetries = 2, timeout = 600000, // 10 minutes\n    httpAgent, fetch: overridenFetch, }) {\n        this.baseURL = baseURL;\n        this.maxRetries = validatePositiveInteger('maxRetries', maxRetries);\n        this.timeout = validatePositiveInteger('timeout', timeout);\n        this.httpAgent = httpAgent;\n        this.fetch = overridenFetch ?? index_1.fetch;\n    }\n    authHeaders(opts) {\n        return {};\n    }\n    /**\n     * Override this to add your own default headers, for example:\n     *\n     *  {\n     *    ...super.defaultHeaders(),\n     *    Authorization: 'Bearer 123',\n     *  }\n     */\n    defaultHeaders(opts) {\n        return {\n            Accept: 'application/json',\n            'Content-Type': 'application/json',\n            'User-Agent': this.getUserAgent(),\n            ...getPlatformHeaders(),\n            ...this.authHeaders(opts),\n        };\n    }\n    /**\n     * Override this to add your own headers validation:\n     */\n    validateHeaders(headers, customHeaders) { }\n    defaultIdempotencyKey() {\n        return `stainless-node-retry-${uuid4()}`;\n    }\n    get(path, opts) {\n        return this.methodRequest('get', path, opts);\n    }\n    post(path, opts) {\n        return this.methodRequest('post', path, opts);\n    }\n    patch(path, opts) {\n        return this.methodRequest('patch', path, opts);\n    }\n    put(path, opts) {\n        return this.methodRequest('put', path, opts);\n    }\n    delete(path, opts) {\n        return this.methodRequest('delete', path, opts);\n    }\n    methodRequest(method, path, opts) {\n        return this.request(Promise.resolve(opts).then(async (opts) => {\n            const body = opts && (0, uploads_1.isBlobLike)(opts?.body) ? new DataView(await opts.body.arrayBuffer())\n                : opts?.body instanceof DataView ? opts.body\n                    : opts?.body instanceof ArrayBuffer ? new DataView(opts.body)\n                        : opts && ArrayBuffer.isView(opts?.body) ? new DataView(opts.body.buffer)\n                            : opts?.body;\n            return { method, path, ...opts, body };\n        }));\n    }\n    getAPIList(path, Page, opts) {\n        return this.requestAPIList(Page, { method: 'get', path, ...opts });\n    }\n    calculateContentLength(body) {\n        if (typeof body === 'string') {\n            if (typeof Buffer !== 'undefined') {\n                return Buffer.byteLength(body, 'utf8').toString();\n            }\n            if (typeof TextEncoder !== 'undefined') {\n                const encoder = new TextEncoder();\n                const encoded = encoder.encode(body);\n                return encoded.length.toString();\n            }\n        }\n        else if (ArrayBuffer.isView(body)) {\n            return body.byteLength.toString();\n        }\n        return null;\n    }\n    buildRequest(options) {\n        const { method, path, query, headers: headers = {} } = options;\n        const body = ArrayBuffer.isView(options.body) || (options.__binaryRequest && typeof options.body === 'string') ?\n            options.body\n            : (0, uploads_1.isMultipartBody)(options.body) ? options.body.body\n                : options.body ? JSON.stringify(options.body, null, 2)\n                    : null;\n        const contentLength = this.calculateContentLength(body);\n        const url = this.buildURL(path, query);\n        if ('timeout' in options)\n            validatePositiveInteger('timeout', options.timeout);\n        const timeout = options.timeout ?? this.timeout;\n        const httpAgent = options.httpAgent ?? this.httpAgent ?? (0, index_1.getDefaultAgent)(url);\n        const minAgentTimeout = timeout + 1000;\n        if (typeof httpAgent?.options?.timeout === 'number' &&\n            minAgentTimeout > (httpAgent.options.timeout ?? 0)) {\n            // Allow any given request to bump our agent active socket timeout.\n            // This may seem strange, but leaking active sockets should be rare and not particularly problematic,\n            // and without mutating agent we would need to create more of them.\n            // This tradeoff optimizes for performance.\n            httpAgent.options.timeout = minAgentTimeout;\n        }\n        if (this.idempotencyHeader && method !== 'get') {\n            if (!options.idempotencyKey)\n                options.idempotencyKey = this.defaultIdempotencyKey();\n            headers[this.idempotencyHeader] = options.idempotencyKey;\n        }\n        const reqHeaders = this.buildHeaders({ options, headers, contentLength });\n        const req = {\n            method,\n            ...(body && { body: body }),\n            headers: reqHeaders,\n            ...(httpAgent && { agent: httpAgent }),\n            // @ts-ignore node-fetch uses a custom AbortSignal type that is\n            // not compatible with standard web types\n            signal: options.signal ?? null,\n        };\n        return { req, url, timeout };\n    }\n    buildHeaders({ options, headers, contentLength, }) {\n        const reqHeaders = {};\n        if (contentLength) {\n            reqHeaders['content-length'] = contentLength;\n        }\n        const defaultHeaders = this.defaultHeaders(options);\n        applyHeadersMut(reqHeaders, defaultHeaders);\n        applyHeadersMut(reqHeaders, headers);\n        // let builtin fetch set the Content-Type for multipart bodies\n        if ((0, uploads_1.isMultipartBody)(options.body) && index_1.kind !== 'node') {\n            delete reqHeaders['content-type'];\n        }\n        this.validateHeaders(reqHeaders, headers);\n        return reqHeaders;\n    }\n    /**\n     * Used as a callback for mutating the given `FinalRequestOptions` object.\n     */\n    async prepareOptions(options) { }\n    /**\n     * Used as a callback for mutating the given `RequestInit` object.\n     *\n     * This is useful for cases where you want to add certain headers based off of\n     * the request properties, e.g. `method` or `url`.\n     */\n    async prepareRequest(request, { url, options }) { }\n    parseHeaders(headers) {\n        return (!headers ? {}\n            : Symbol.iterator in headers ?\n                Object.fromEntries(Array.from(headers).map((header) => [...header]))\n                : { ...headers });\n    }\n    makeStatusError(status, error, message, headers) {\n        return error_1.APIError.generate(status, error, message, headers);\n    }\n    request(options, remainingRetries = null) {\n        return new APIPromise(this.makeRequest(options, remainingRetries));\n    }\n    async makeRequest(optionsInput, retriesRemaining) {\n        const options = await optionsInput;\n        if (retriesRemaining == null) {\n            retriesRemaining = options.maxRetries ?? this.maxRetries;\n        }\n        await this.prepareOptions(options);\n        const { req, url, timeout } = this.buildRequest(options);\n        await this.prepareRequest(req, { url, options });\n        debug('request', url, options, req.headers);\n        if (options.signal?.aborted) {\n            throw new error_1.APIUserAbortError();\n        }\n        const controller = new AbortController();\n        const response = await this.fetchWithTimeout(url, req, timeout, controller).catch(exports.castToError);\n        if (response instanceof Error) {\n            if (options.signal?.aborted) {\n                throw new error_1.APIUserAbortError();\n            }\n            if (retriesRemaining) {\n                return this.retryRequest(options, retriesRemaining);\n            }\n            if (response.name === 'AbortError') {\n                throw new error_1.APIConnectionTimeoutError();\n            }\n            throw new error_1.APIConnectionError({ cause: response });\n        }\n        const responseHeaders = (0, exports.createResponseHeaders)(response.headers);\n        if (!response.ok) {\n            if (retriesRemaining && this.shouldRetry(response)) {\n                const retryMessage = `retrying, ${retriesRemaining} attempts remaining`;\n                debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders);\n                return this.retryRequest(options, retriesRemaining, responseHeaders);\n            }\n            const errText = await response.text().catch((e) => (0, exports.castToError)(e).message);\n            const errJSON = (0, exports.safeJSON)(errText);\n            const errMessage = errJSON ? undefined : errText;\n            const retryMessage = retriesRemaining ? `(error; no more retries left)` : `(error; not retryable)`;\n            debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders, errMessage);\n            const err = this.makeStatusError(response.status, errJSON, errMessage, responseHeaders);\n            throw err;\n        }\n        return { response, options, controller };\n    }\n    requestAPIList(Page, options) {\n        const request = this.makeRequest(options, null);\n        return new PagePromise(this, request, Page);\n    }\n    buildURL(path, query) {\n        const url = isAbsoluteURL(path) ?\n            new URL(path)\n            : new URL(this.baseURL + (this.baseURL.endsWith('/') && path.startsWith('/') ? path.slice(1) : path));\n        const defaultQuery = this.defaultQuery();\n        if (!isEmptyObj(defaultQuery)) {\n            query = { ...defaultQuery, ...query };\n        }\n        if (typeof query === 'object' && query && !Array.isArray(query)) {\n            url.search = this.stringifyQuery(query);\n        }\n        return url.toString();\n    }\n    stringifyQuery(query) {\n        return Object.entries(query)\n            .filter(([_, value]) => typeof value !== 'undefined')\n            .map(([key, value]) => {\n            if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n                return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;\n            }\n            if (value === null) {\n                return `${encodeURIComponent(key)}=`;\n            }\n            throw new error_1.AnthropicError(`Cannot stringify type ${typeof value}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`);\n        })\n            .join('&');\n    }\n    async fetchWithTimeout(url, init, ms, controller) {\n        const { signal, ...options } = init || {};\n        if (signal)\n            signal.addEventListener('abort', () => controller.abort());\n        const timeout = setTimeout(() => controller.abort(), ms);\n        return (this.getRequestClient()\n            // use undefined this binding; fetch errors if bound to something else in browser/cloudflare\n            .fetch.call(undefined, url, { signal: controller.signal, ...options })\n            .finally(() => {\n            clearTimeout(timeout);\n        }));\n    }\n    getRequestClient() {\n        return { fetch: this.fetch };\n    }\n    shouldRetry(response) {\n        // Note this is not a standard header.\n        const shouldRetryHeader = response.headers.get('x-should-retry');\n        // If the server explicitly says whether or not to retry, obey.\n        if (shouldRetryHeader === 'true')\n            return true;\n        if (shouldRetryHeader === 'false')\n            return false;\n        // Retry on request timeouts.\n        if (response.status === 408)\n            return true;\n        // Retry on lock timeouts.\n        if (response.status === 409)\n            return true;\n        // Retry on rate limits.\n        if (response.status === 429)\n            return true;\n        // Retry internal errors.\n        if (response.status >= 500)\n            return true;\n        return false;\n    }\n    async retryRequest(options, retriesRemaining, responseHeaders) {\n        let timeoutMillis;\n        // Note the `retry-after-ms` header may not be standard, but is a good idea and we'd like proactive support for it.\n        const retryAfterMillisHeader = responseHeaders?.['retry-after-ms'];\n        if (retryAfterMillisHeader) {\n            const timeoutMs = parseFloat(retryAfterMillisHeader);\n            if (!Number.isNaN(timeoutMs)) {\n                timeoutMillis = timeoutMs;\n            }\n        }\n        // About the Retry-After header: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After\n        const retryAfterHeader = responseHeaders?.['retry-after'];\n        if (retryAfterHeader && !timeoutMillis) {\n            const timeoutSeconds = parseFloat(retryAfterHeader);\n            if (!Number.isNaN(timeoutSeconds)) {\n                timeoutMillis = timeoutSeconds * 1000;\n            }\n            else {\n                timeoutMillis = Date.parse(retryAfterHeader) - Date.now();\n            }\n        }\n        // If the API asks us to wait a certain amount of time (and it's a reasonable amount),\n        // just do what it says, but otherwise calculate a default\n        if (!(timeoutMillis && 0 <= timeoutMillis && timeoutMillis < 60 * 1000)) {\n            const maxRetries = options.maxRetries ?? this.maxRetries;\n            timeoutMillis = this.calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries);\n        }\n        await (0, exports.sleep)(timeoutMillis);\n        return this.makeRequest(options, retriesRemaining - 1);\n    }\n    calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries) {\n        const initialRetryDelay = 0.5;\n        const maxRetryDelay = 8.0;\n        const numRetries = maxRetries - retriesRemaining;\n        // Apply exponential backoff, but not more than the max.\n        const sleepSeconds = Math.min(initialRetryDelay * Math.pow(2, numRetries), maxRetryDelay);\n        // Apply some jitter, take up to at most 25 percent of the retry time.\n        const jitter = 1 - Math.random() * 0.25;\n        return sleepSeconds * jitter * 1000;\n    }\n    getUserAgent() {\n        return `${this.constructor.name}/JS ${version_1.VERSION}`;\n    }\n}\nexports.APIClient = APIClient;\nclass AbstractPage {\n    constructor(client, response, body, options) {\n        _AbstractPage_client.set(this, void 0);\n        __classPrivateFieldSet(this, _AbstractPage_client, client, \"f\");\n        this.options = options;\n        this.response = response;\n        this.body = body;\n    }\n    hasNextPage() {\n        const items = this.getPaginatedItems();\n        if (!items.length)\n            return false;\n        return this.nextPageInfo() != null;\n    }\n    async getNextPage() {\n        const nextInfo = this.nextPageInfo();\n        if (!nextInfo) {\n            throw new error_1.AnthropicError('No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.');\n        }\n        const nextOptions = { ...this.options };\n        if ('params' in nextInfo && typeof nextOptions.query === 'object') {\n            nextOptions.query = { ...nextOptions.query, ...nextInfo.params };\n        }\n        else if ('url' in nextInfo) {\n            const params = [...Object.entries(nextOptions.query || {}), ...nextInfo.url.searchParams.entries()];\n            for (const [key, value] of params) {\n                nextInfo.url.searchParams.set(key, value);\n            }\n            nextOptions.query = undefined;\n            nextOptions.path = nextInfo.url.toString();\n        }\n        return await __classPrivateFieldGet(this, _AbstractPage_client, \"f\").requestAPIList(this.constructor, nextOptions);\n    }\n    async *iterPages() {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        let page = this;\n        yield page;\n        while (page.hasNextPage()) {\n            page = await page.getNextPage();\n            yield page;\n        }\n    }\n    async *[(_AbstractPage_client = new WeakMap(), Symbol.asyncIterator)]() {\n        for await (const page of this.iterPages()) {\n            for (const item of page.getPaginatedItems()) {\n                yield item;\n            }\n        }\n    }\n}\nexports.AbstractPage = AbstractPage;\n/**\n * This subclass of Promise will resolve to an instantiated Page once the request completes.\n *\n * It also implements AsyncIterable to allow auto-paginating iteration on an unawaited list call, eg:\n *\n *    for await (const item of client.items.list()) {\n *      console.log(item)\n *    }\n */\nclass PagePromise extends APIPromise {\n    constructor(client, request, Page) {\n        super(request, async (props) => new Page(client, props.response, await defaultParseResponse(props), props.options));\n    }\n    /**\n     * Allow auto-paginating iteration on an unawaited list call, eg:\n     *\n     *    for await (const item of client.items.list()) {\n     *      console.log(item)\n     *    }\n     */\n    async *[Symbol.asyncIterator]() {\n        const page = await this;\n        for await (const item of page) {\n            yield item;\n        }\n    }\n}\nexports.PagePromise = PagePromise;\nconst createResponseHeaders = (headers) => {\n    return new Proxy(Object.fromEntries(\n    // @ts-ignore\n    headers.entries()), {\n        get(target, name) {\n            const key = name.toString();\n            return target[key.toLowerCase()] || target[key];\n        },\n    });\n};\nexports.createResponseHeaders = createResponseHeaders;\n// This is required so that we can determine if a given object matches the RequestOptions\n// type at runtime. While this requires duplication, it is enforced by the TypeScript\n// compiler such that any missing / extraneous keys will cause an error.\nconst requestOptionsKeys = {\n    method: true,\n    path: true,\n    query: true,\n    body: true,\n    headers: true,\n    maxRetries: true,\n    stream: true,\n    timeout: true,\n    httpAgent: true,\n    signal: true,\n    idempotencyKey: true,\n    __binaryRequest: true,\n    __binaryResponse: true,\n    __streamClass: true,\n};\nconst isRequestOptions = (obj) => {\n    return (typeof obj === 'object' &&\n        obj !== null &&\n        !isEmptyObj(obj) &&\n        Object.keys(obj).every((k) => hasOwn(requestOptionsKeys, k)));\n};\nexports.isRequestOptions = isRequestOptions;\nconst getPlatformProperties = () => {\n    if (typeof Deno !== 'undefined' && Deno.build != null) {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': version_1.VERSION,\n            'X-Stainless-OS': normalizePlatform(Deno.build.os),\n            'X-Stainless-Arch': normalizeArch(Deno.build.arch),\n            'X-Stainless-Runtime': 'deno',\n            'X-Stainless-Runtime-Version': typeof Deno.version === 'string' ? Deno.version : Deno.version?.deno ?? 'unknown',\n        };\n    }\n    if (typeof EdgeRuntime !== 'undefined') {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': version_1.VERSION,\n            'X-Stainless-OS': 'Unknown',\n            'X-Stainless-Arch': `other:${EdgeRuntime}`,\n            'X-Stainless-Runtime': 'edge',\n            'X-Stainless-Runtime-Version': process.version,\n        };\n    }\n    // Check if Node.js\n    if (Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]') {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': version_1.VERSION,\n            'X-Stainless-OS': normalizePlatform(process.platform),\n            'X-Stainless-Arch': normalizeArch(process.arch),\n            'X-Stainless-Runtime': 'node',\n            'X-Stainless-Runtime-Version': process.version,\n        };\n    }\n    const browserInfo = getBrowserInfo();\n    if (browserInfo) {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': version_1.VERSION,\n            'X-Stainless-OS': 'Unknown',\n            'X-Stainless-Arch': 'unknown',\n            'X-Stainless-Runtime': `browser:${browserInfo.browser}`,\n            'X-Stainless-Runtime-Version': browserInfo.version,\n        };\n    }\n    // TODO add support for Cloudflare workers, etc.\n    return {\n        'X-Stainless-Lang': 'js',\n        'X-Stainless-Package-Version': version_1.VERSION,\n        'X-Stainless-OS': 'Unknown',\n        'X-Stainless-Arch': 'unknown',\n        'X-Stainless-Runtime': 'unknown',\n        'X-Stainless-Runtime-Version': 'unknown',\n    };\n};\n// Note: modified from https://github.com/JS-DevTools/host-environment/blob/b1ab79ecde37db5d6e163c050e54fe7d287d7c92/src/isomorphic.browser.ts\nfunction getBrowserInfo() {\n    if (typeof navigator === 'undefined' || !navigator) {\n        return null;\n    }\n    // NOTE: The order matters here!\n    const browserPatterns = [\n        { key: 'edge', pattern: /Edge(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'ie', pattern: /MSIE(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'ie', pattern: /Trident(?:.*rv\\:(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'chrome', pattern: /Chrome(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'firefox', pattern: /Firefox(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'safari', pattern: /(?:Version\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?(?:\\W+Mobile\\S*)?\\W+Safari/ },\n    ];\n    // Find the FIRST matching browser\n    for (const { key, pattern } of browserPatterns) {\n        const match = pattern.exec(navigator.userAgent);\n        if (match) {\n            const major = match[1] || 0;\n            const minor = match[2] || 0;\n            const patch = match[3] || 0;\n            return { browser: key, version: `${major}.${minor}.${patch}` };\n        }\n    }\n    return null;\n}\nconst normalizeArch = (arch) => {\n    // Node docs:\n    // - https://nodejs.org/api/process.html#processarch\n    // Deno docs:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    if (arch === 'x32')\n        return 'x32';\n    if (arch === 'x86_64' || arch === 'x64')\n        return 'x64';\n    if (arch === 'arm')\n        return 'arm';\n    if (arch === 'aarch64' || arch === 'arm64')\n        return 'arm64';\n    if (arch)\n        return `other:${arch}`;\n    return 'unknown';\n};\nconst normalizePlatform = (platform) => {\n    // Node platforms:\n    // - https://nodejs.org/api/process.html#processplatform\n    // Deno platforms:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    // - https://github.com/denoland/deno/issues/14799\n    platform = platform.toLowerCase();\n    // NOTE: this iOS check is untested and may not work\n    // Node does not work natively on IOS, there is a fork at\n    // https://github.com/nodejs-mobile/nodejs-mobile\n    // however it is unknown at the time of writing how to detect if it is running\n    if (platform.includes('ios'))\n        return 'iOS';\n    if (platform === 'android')\n        return 'Android';\n    if (platform === 'darwin')\n        return 'MacOS';\n    if (platform === 'win32')\n        return 'Windows';\n    if (platform === 'freebsd')\n        return 'FreeBSD';\n    if (platform === 'openbsd')\n        return 'OpenBSD';\n    if (platform === 'linux')\n        return 'Linux';\n    if (platform)\n        return `Other:${platform}`;\n    return 'Unknown';\n};\nlet _platformHeaders;\nconst getPlatformHeaders = () => {\n    return (_platformHeaders ?? (_platformHeaders = getPlatformProperties()));\n};\nconst safeJSON = (text) => {\n    try {\n        return JSON.parse(text);\n    }\n    catch (err) {\n        return undefined;\n    }\n};\nexports.safeJSON = safeJSON;\n// https://stackoverflow.com/a/19709846\nconst startsWithSchemeRegexp = new RegExp('^(?:[a-z]+:)?//', 'i');\nconst isAbsoluteURL = (url) => {\n    return startsWithSchemeRegexp.test(url);\n};\nconst sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\nexports.sleep = sleep;\nconst validatePositiveInteger = (name, n) => {\n    if (typeof n !== 'number' || !Number.isInteger(n)) {\n        throw new error_1.AnthropicError(`${name} must be an integer`);\n    }\n    if (n < 0) {\n        throw new error_1.AnthropicError(`${name} must be a positive integer`);\n    }\n    return n;\n};\nconst castToError = (err) => {\n    if (err instanceof Error)\n        return err;\n    return new Error(err);\n};\nexports.castToError = castToError;\nconst ensurePresent = (value) => {\n    if (value == null)\n        throw new error_1.AnthropicError(`Expected a value to be given but received ${value} instead.`);\n    return value;\n};\nexports.ensurePresent = ensurePresent;\n/**\n * Read an environment variable.\n *\n * Trims beginning and trailing whitespace.\n *\n * Will return undefined if the environment variable doesn't exist or cannot be accessed.\n */\nconst readEnv = (env) => {\n    if (typeof process !== 'undefined') {\n        return process.env?.[env]?.trim() ?? undefined;\n    }\n    if (typeof Deno !== 'undefined') {\n        return Deno.env?.get?.(env)?.trim();\n    }\n    return undefined;\n};\nexports.readEnv = readEnv;\nconst coerceInteger = (value) => {\n    if (typeof value === 'number')\n        return Math.round(value);\n    if (typeof value === 'string')\n        return parseInt(value, 10);\n    throw new error_1.AnthropicError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nexports.coerceInteger = coerceInteger;\nconst coerceFloat = (value) => {\n    if (typeof value === 'number')\n        return value;\n    if (typeof value === 'string')\n        return parseFloat(value);\n    throw new error_1.AnthropicError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nexports.coerceFloat = coerceFloat;\nconst coerceBoolean = (value) => {\n    if (typeof value === 'boolean')\n        return value;\n    if (typeof value === 'string')\n        return value === 'true';\n    return Boolean(value);\n};\nexports.coerceBoolean = coerceBoolean;\nconst maybeCoerceInteger = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return (0, exports.coerceInteger)(value);\n};\nexports.maybeCoerceInteger = maybeCoerceInteger;\nconst maybeCoerceFloat = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return (0, exports.coerceFloat)(value);\n};\nexports.maybeCoerceFloat = maybeCoerceFloat;\nconst maybeCoerceBoolean = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return (0, exports.coerceBoolean)(value);\n};\nexports.maybeCoerceBoolean = maybeCoerceBoolean;\n// https://stackoverflow.com/a/34491287\nfunction isEmptyObj(obj) {\n    if (!obj)\n        return true;\n    for (const _k in obj)\n        return false;\n    return true;\n}\nexports.isEmptyObj = isEmptyObj;\n// https://eslint.org/docs/latest/rules/no-prototype-builtins\nfunction hasOwn(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\nexports.hasOwn = hasOwn;\n/**\n * Copies headers from \"newHeaders\" onto \"targetHeaders\",\n * using lower-case for all properties,\n * ignoring any keys with undefined values,\n * and deleting any keys with null values.\n */\nfunction applyHeadersMut(targetHeaders, newHeaders) {\n    for (const k in newHeaders) {\n        if (!hasOwn(newHeaders, k))\n            continue;\n        const lowerKey = k.toLowerCase();\n        if (!lowerKey)\n            continue;\n        const val = newHeaders[k];\n        if (val === null) {\n            delete targetHeaders[lowerKey];\n        }\n        else if (val !== undefined) {\n            targetHeaders[lowerKey] = val;\n        }\n    }\n}\nfunction debug(action, ...args) {\n    if (typeof process !== 'undefined' && process?.env?.['DEBUG'] === 'true') {\n        console.log(`Anthropic:DEBUG:${action}`, ...args);\n    }\n}\nexports.debug = debug;\n/**\n * https://stackoverflow.com/a/2117523\n */\nconst uuid4 = () => {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16) | 0;\n        const v = c === 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n};\nconst isRunningInBrowser = () => {\n    return (\n    // @ts-ignore\n    typeof window !== 'undefined' &&\n        // @ts-ignore\n        typeof window.document !== 'undefined' &&\n        // @ts-ignore\n        typeof navigator !== 'undefined');\n};\nexports.isRunningInBrowser = isRunningInBrowser;\nconst isHeadersProtocol = (headers) => {\n    return typeof headers?.get === 'function';\n};\nexports.isHeadersProtocol = isHeadersProtocol;\nconst getRequiredHeader = (headers, header) => {\n    const lowerCasedHeader = header.toLowerCase();\n    if ((0, exports.isHeadersProtocol)(headers)) {\n        // to deal with the case where the header looks like Stainless-Event-Id\n        const intercapsHeader = header[0]?.toUpperCase() +\n            header.substring(1).replace(/([^\\w])(\\w)/g, (_m, g1, g2) => g1 + g2.toUpperCase());\n        for (const key of [header, lowerCasedHeader, header.toUpperCase(), intercapsHeader]) {\n            const value = headers.get(key);\n            if (value) {\n                return value;\n            }\n        }\n    }\n    for (const [key, value] of Object.entries(headers)) {\n        if (key.toLowerCase() === lowerCasedHeader) {\n            if (Array.isArray(value)) {\n                if (value.length <= 1)\n                    return value[0];\n                console.warn(`Received ${value.length} entries for the ${header} header, using the first entry.`);\n                return value[0];\n            }\n            return value;\n        }\n    }\n    throw new Error(`Could not find ${header} header`);\n};\nexports.getRequiredHeader = getRequiredHeader;\n/**\n * Encodes a string to Base64 format.\n */\nconst toBase64 = (str) => {\n    if (!str)\n        return '';\n    if (typeof Buffer !== 'undefined') {\n        return Buffer.from(str).toString('base64');\n    }\n    if (typeof btoa !== 'undefined') {\n        return btoa(str);\n    }\n    throw new error_1.AnthropicError('Cannot generate b64 string; Expected `Buffer` or `btoa` to be defined');\n};\nexports.toBase64 = toBase64;\nfunction isObj(obj) {\n    return obj != null && typeof obj === 'object' && !Array.isArray(obj);\n}\nexports.isObj = isObj;\n//# sourceMappingURL=core.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/error.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/error.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.InternalServerError = exports.RateLimitError = exports.UnprocessableEntityError = exports.ConflictError = exports.NotFoundError = exports.PermissionDeniedError = exports.AuthenticationError = exports.BadRequestError = exports.APIConnectionTimeoutError = exports.APIConnectionError = exports.APIUserAbortError = exports.APIError = exports.AnthropicError = void 0;\nconst core_1 = __webpack_require__(/*! ./core.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/core.js\");\nclass AnthropicError extends Error {\n}\nexports.AnthropicError = AnthropicError;\nclass APIError extends AnthropicError {\n    constructor(status, error, message, headers) {\n        super(`${APIError.makeMessage(status, error, message)}`);\n        this.status = status;\n        this.headers = headers;\n        this.request_id = headers?.['request-id'];\n        this.error = error;\n    }\n    static makeMessage(status, error, message) {\n        const msg = error?.message ?\n            typeof error.message === 'string' ?\n                error.message\n                : JSON.stringify(error.message)\n            : error ? JSON.stringify(error)\n                : message;\n        if (status && msg) {\n            return `${status} ${msg}`;\n        }\n        if (status) {\n            return `${status} status code (no body)`;\n        }\n        if (msg) {\n            return msg;\n        }\n        return '(no status code or body)';\n    }\n    static generate(status, errorResponse, message, headers) {\n        if (!status) {\n            return new APIConnectionError({ message, cause: (0, core_1.castToError)(errorResponse) });\n        }\n        const error = errorResponse;\n        if (status === 400) {\n            return new BadRequestError(status, error, message, headers);\n        }\n        if (status === 401) {\n            return new AuthenticationError(status, error, message, headers);\n        }\n        if (status === 403) {\n            return new PermissionDeniedError(status, error, message, headers);\n        }\n        if (status === 404) {\n            return new NotFoundError(status, error, message, headers);\n        }\n        if (status === 409) {\n            return new ConflictError(status, error, message, headers);\n        }\n        if (status === 422) {\n            return new UnprocessableEntityError(status, error, message, headers);\n        }\n        if (status === 429) {\n            return new RateLimitError(status, error, message, headers);\n        }\n        if (status >= 500) {\n            return new InternalServerError(status, error, message, headers);\n        }\n        return new APIError(status, error, message, headers);\n    }\n}\nexports.APIError = APIError;\nclass APIUserAbortError extends APIError {\n    constructor({ message } = {}) {\n        super(undefined, undefined, message || 'Request was aborted.', undefined);\n        this.status = undefined;\n    }\n}\nexports.APIUserAbortError = APIUserAbortError;\nclass APIConnectionError extends APIError {\n    constructor({ message, cause }) {\n        super(undefined, undefined, message || 'Connection error.', undefined);\n        this.status = undefined;\n        // in some environments the 'cause' property is already declared\n        // @ts-ignore\n        if (cause)\n            this.cause = cause;\n    }\n}\nexports.APIConnectionError = APIConnectionError;\nclass APIConnectionTimeoutError extends APIConnectionError {\n    constructor({ message } = {}) {\n        super({ message: message ?? 'Request timed out.' });\n    }\n}\nexports.APIConnectionTimeoutError = APIConnectionTimeoutError;\nclass BadRequestError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 400;\n    }\n}\nexports.BadRequestError = BadRequestError;\nclass AuthenticationError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 401;\n    }\n}\nexports.AuthenticationError = AuthenticationError;\nclass PermissionDeniedError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 403;\n    }\n}\nexports.PermissionDeniedError = PermissionDeniedError;\nclass NotFoundError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 404;\n    }\n}\nexports.NotFoundError = NotFoundError;\nclass ConflictError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 409;\n    }\n}\nexports.ConflictError = ConflictError;\nclass UnprocessableEntityError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 422;\n    }\n}\nexports.UnprocessableEntityError = UnprocessableEntityError;\nclass RateLimitError extends APIError {\n    constructor() {\n        super(...arguments);\n        this.status = 429;\n    }\n}\nexports.RateLimitError = RateLimitError;\nclass InternalServerError extends APIError {\n}\nexports.InternalServerError = InternalServerError;\n//# sourceMappingURL=error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/index.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/index.js ***!
  \*********************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.fileFromPath = exports.toFile = exports.UnprocessableEntityError = exports.PermissionDeniedError = exports.InternalServerError = exports.AuthenticationError = exports.BadRequestError = exports.RateLimitError = exports.ConflictError = exports.NotFoundError = exports.APIUserAbortError = exports.APIConnectionTimeoutError = exports.APIConnectionError = exports.APIError = exports.AnthropicError = exports.AI_PROMPT = exports.HUMAN_PROMPT = exports.Anthropic = void 0;\nconst Errors = __importStar(__webpack_require__(/*! ./error.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/error.js\"));\nconst Uploads = __importStar(__webpack_require__(/*! ./uploads.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/uploads.js\"));\nconst Core = __importStar(__webpack_require__(/*! ./core.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/core.js\"));\nconst API = __importStar(__webpack_require__(/*! ./resources/index.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/index.js\"));\n/**\n * API Client for interfacing with the Anthropic API.\n */\nclass Anthropic extends Core.APIClient {\n    /**\n     * API Client for interfacing with the Anthropic API.\n     *\n     * @param {string | null | undefined} [opts.apiKey=process.env['ANTHROPIC_API_KEY'] ?? null]\n     * @param {string | null | undefined} [opts.authToken=process.env['ANTHROPIC_AUTH_TOKEN'] ?? null]\n     * @param {string} [opts.baseURL=process.env['ANTHROPIC_BASE_URL'] ?? https://api.anthropic.com] - Override the default base URL for the API.\n     * @param {number} [opts.timeout=10 minutes] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.\n     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.\n     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.\n     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.\n     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.\n     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.\n     * @param {boolean} [opts.dangerouslyAllowBrowser=false] - By default, client-side use of this library is not allowed, as it risks exposing your secret API credentials to attackers.\n     */\n    constructor({ baseURL = Core.readEnv('ANTHROPIC_BASE_URL'), apiKey = Core.readEnv('ANTHROPIC_API_KEY') ?? null, authToken = Core.readEnv('ANTHROPIC_AUTH_TOKEN') ?? null, ...opts } = {}) {\n        const options = {\n            apiKey,\n            authToken,\n            ...opts,\n            baseURL: baseURL || `https://api.anthropic.com`,\n        };\n        if (!options.dangerouslyAllowBrowser && Core.isRunningInBrowser()) {\n            throw new Errors.AnthropicError(\"It looks like you're running in a browser-like environment.\\n\\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\\nIf you understand the risks and have appropriate mitigations in place,\\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\\n\\nnew Anthropic({ apiKey, dangerouslyAllowBrowser: true });\\n\\nTODO: link!\\n\");\n        }\n        super({\n            baseURL: options.baseURL,\n            timeout: options.timeout ?? 600000 /* 10 minutes */,\n            httpAgent: options.httpAgent,\n            maxRetries: options.maxRetries,\n            fetch: options.fetch,\n        });\n        this.completions = new API.Completions(this);\n        this.messages = new API.Messages(this);\n        this.beta = new API.Beta(this);\n        this._options = options;\n        this.apiKey = apiKey;\n        this.authToken = authToken;\n    }\n    defaultQuery() {\n        return this._options.defaultQuery;\n    }\n    defaultHeaders(opts) {\n        return {\n            ...super.defaultHeaders(opts),\n            ...(this._options.dangerouslyAllowBrowser ?\n                { 'anthropic-dangerous-direct-browser-access': 'true' }\n                : undefined),\n            'anthropic-version': '2023-06-01',\n            ...this._options.defaultHeaders,\n        };\n    }\n    validateHeaders(headers, customHeaders) {\n        if (this.apiKey && headers['x-api-key']) {\n            return;\n        }\n        if (customHeaders['x-api-key'] === null) {\n            return;\n        }\n        if (this.authToken && headers['authorization']) {\n            return;\n        }\n        if (customHeaders['authorization'] === null) {\n            return;\n        }\n        throw new Error('Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the \"X-Api-Key\" or \"Authorization\" headers to be explicitly omitted');\n    }\n    authHeaders(opts) {\n        const apiKeyAuth = this.apiKeyAuth(opts);\n        const bearerAuth = this.bearerAuth(opts);\n        if (apiKeyAuth != null && !Core.isEmptyObj(apiKeyAuth)) {\n            return apiKeyAuth;\n        }\n        if (bearerAuth != null && !Core.isEmptyObj(bearerAuth)) {\n            return bearerAuth;\n        }\n        return {};\n    }\n    apiKeyAuth(opts) {\n        if (this.apiKey == null) {\n            return {};\n        }\n        return { 'X-Api-Key': this.apiKey };\n    }\n    bearerAuth(opts) {\n        if (this.authToken == null) {\n            return {};\n        }\n        return { Authorization: `Bearer ${this.authToken}` };\n    }\n}\nexports.Anthropic = Anthropic;\n_a = Anthropic;\nAnthropic.Anthropic = _a;\nAnthropic.HUMAN_PROMPT = '\\n\\nHuman:';\nAnthropic.AI_PROMPT = '\\n\\nAssistant:';\nAnthropic.DEFAULT_TIMEOUT = 600000; // 10 minutes\nAnthropic.AnthropicError = Errors.AnthropicError;\nAnthropic.APIError = Errors.APIError;\nAnthropic.APIConnectionError = Errors.APIConnectionError;\nAnthropic.APIConnectionTimeoutError = Errors.APIConnectionTimeoutError;\nAnthropic.APIUserAbortError = Errors.APIUserAbortError;\nAnthropic.NotFoundError = Errors.NotFoundError;\nAnthropic.ConflictError = Errors.ConflictError;\nAnthropic.RateLimitError = Errors.RateLimitError;\nAnthropic.BadRequestError = Errors.BadRequestError;\nAnthropic.AuthenticationError = Errors.AuthenticationError;\nAnthropic.InternalServerError = Errors.InternalServerError;\nAnthropic.PermissionDeniedError = Errors.PermissionDeniedError;\nAnthropic.UnprocessableEntityError = Errors.UnprocessableEntityError;\nAnthropic.toFile = Uploads.toFile;\nAnthropic.fileFromPath = Uploads.fileFromPath;\nexports.HUMAN_PROMPT = Anthropic.HUMAN_PROMPT, exports.AI_PROMPT = Anthropic.AI_PROMPT;\nexports.AnthropicError = Errors.AnthropicError, exports.APIError = Errors.APIError, exports.APIConnectionError = Errors.APIConnectionError, exports.APIConnectionTimeoutError = Errors.APIConnectionTimeoutError, exports.APIUserAbortError = Errors.APIUserAbortError, exports.NotFoundError = Errors.NotFoundError, exports.ConflictError = Errors.ConflictError, exports.RateLimitError = Errors.RateLimitError, exports.BadRequestError = Errors.BadRequestError, exports.AuthenticationError = Errors.AuthenticationError, exports.InternalServerError = Errors.InternalServerError, exports.PermissionDeniedError = Errors.PermissionDeniedError, exports.UnprocessableEntityError = Errors.UnprocessableEntityError;\nexports.toFile = Uploads.toFile;\nexports.fileFromPath = Uploads.fileFromPath;\n(function (Anthropic) {\n    Anthropic.Completions = API.Completions;\n    Anthropic.Messages = API.Messages;\n    Anthropic.Beta = API.Beta;\n})(Anthropic = exports.Anthropic || (exports.Anthropic = {}));\nexports = module.exports = Anthropic;\nexports[\"default\"] = Anthropic;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/lib/MessageStream.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/lib/MessageStream.js ***!
  \*********************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _MessageStream_instances, _MessageStream_currentMessageSnapshot, _MessageStream_connectedPromise, _MessageStream_resolveConnectedPromise, _MessageStream_rejectConnectedPromise, _MessageStream_endPromise, _MessageStream_resolveEndPromise, _MessageStream_rejectEndPromise, _MessageStream_listeners, _MessageStream_ended, _MessageStream_errored, _MessageStream_aborted, _MessageStream_catchingPromiseCreated, _MessageStream_getFinalMessage, _MessageStream_getFinalText, _MessageStream_handleError, _MessageStream_beginRequest, _MessageStream_addStreamEvent, _MessageStream_endRequest, _MessageStream_accumulateMessage;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MessageStream = void 0;\nconst error_1 = __webpack_require__(/*! @anthropic-ai/sdk/error */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/error.js\");\nconst streaming_1 = __webpack_require__(/*! @anthropic-ai/sdk/streaming */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/streaming.js\");\nconst parser_1 = __webpack_require__(/*! ../_vendor/partial-json-parser/parser.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_vendor/partial-json-parser/parser.js\");\nconst JSON_BUF_PROPERTY = '__json_buf';\nclass MessageStream {\n    constructor() {\n        _MessageStream_instances.add(this);\n        this.messages = [];\n        this.receivedMessages = [];\n        _MessageStream_currentMessageSnapshot.set(this, void 0);\n        this.controller = new AbortController();\n        _MessageStream_connectedPromise.set(this, void 0);\n        _MessageStream_resolveConnectedPromise.set(this, () => { });\n        _MessageStream_rejectConnectedPromise.set(this, () => { });\n        _MessageStream_endPromise.set(this, void 0);\n        _MessageStream_resolveEndPromise.set(this, () => { });\n        _MessageStream_rejectEndPromise.set(this, () => { });\n        _MessageStream_listeners.set(this, {});\n        _MessageStream_ended.set(this, false);\n        _MessageStream_errored.set(this, false);\n        _MessageStream_aborted.set(this, false);\n        _MessageStream_catchingPromiseCreated.set(this, false);\n        _MessageStream_handleError.set(this, (error) => {\n            __classPrivateFieldSet(this, _MessageStream_errored, true, \"f\");\n            if (error instanceof Error && error.name === 'AbortError') {\n                error = new error_1.APIUserAbortError();\n            }\n            if (error instanceof error_1.APIUserAbortError) {\n                __classPrivateFieldSet(this, _MessageStream_aborted, true, \"f\");\n                return this._emit('abort', error);\n            }\n            if (error instanceof error_1.AnthropicError) {\n                return this._emit('error', error);\n            }\n            if (error instanceof Error) {\n                const anthropicError = new error_1.AnthropicError(error.message);\n                // @ts-ignore\n                anthropicError.cause = error;\n                return this._emit('error', anthropicError);\n            }\n            return this._emit('error', new error_1.AnthropicError(String(error)));\n        });\n        __classPrivateFieldSet(this, _MessageStream_connectedPromise, new Promise((resolve, reject) => {\n            __classPrivateFieldSet(this, _MessageStream_resolveConnectedPromise, resolve, \"f\");\n            __classPrivateFieldSet(this, _MessageStream_rejectConnectedPromise, reject, \"f\");\n        }), \"f\");\n        __classPrivateFieldSet(this, _MessageStream_endPromise, new Promise((resolve, reject) => {\n            __classPrivateFieldSet(this, _MessageStream_resolveEndPromise, resolve, \"f\");\n            __classPrivateFieldSet(this, _MessageStream_rejectEndPromise, reject, \"f\");\n        }), \"f\");\n        // Don't let these promises cause unhandled rejection errors.\n        // we will manually cause an unhandled rejection error later\n        // if the user hasn't registered any error listener or called\n        // any promise-returning method.\n        __classPrivateFieldGet(this, _MessageStream_connectedPromise, \"f\").catch(() => { });\n        __classPrivateFieldGet(this, _MessageStream_endPromise, \"f\").catch(() => { });\n    }\n    /**\n     * Intended for use on the frontend, consuming a stream produced with\n     * `.toReadableStream()` on the backend.\n     *\n     * Note that messages sent to the model do not appear in `.on('message')`\n     * in this context.\n     */\n    static fromReadableStream(stream) {\n        const runner = new MessageStream();\n        runner._run(() => runner._fromReadableStream(stream));\n        return runner;\n    }\n    static createMessage(messages, params, options) {\n        const runner = new MessageStream();\n        for (const message of params.messages) {\n            runner._addMessageParam(message);\n        }\n        runner._run(() => runner._createMessage(messages, { ...params, stream: true }, { ...options, headers: { ...options?.headers, 'X-Stainless-Helper-Method': 'stream' } }));\n        return runner;\n    }\n    _run(executor) {\n        executor().then(() => {\n            this._emitFinal();\n            this._emit('end');\n        }, __classPrivateFieldGet(this, _MessageStream_handleError, \"f\"));\n    }\n    _addMessageParam(message) {\n        this.messages.push(message);\n    }\n    _addMessage(message, emit = true) {\n        this.receivedMessages.push(message);\n        if (emit) {\n            this._emit('message', message);\n        }\n    }\n    async _createMessage(messages, params, options) {\n        const signal = options?.signal;\n        if (signal) {\n            if (signal.aborted)\n                this.controller.abort();\n            signal.addEventListener('abort', () => this.controller.abort());\n        }\n        __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_beginRequest).call(this);\n        const stream = await messages.create({ ...params, stream: true }, { ...options, signal: this.controller.signal });\n        this._connected();\n        for await (const event of stream) {\n            __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_addStreamEvent).call(this, event);\n        }\n        if (stream.controller.signal?.aborted) {\n            throw new error_1.APIUserAbortError();\n        }\n        __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_endRequest).call(this);\n    }\n    _connected() {\n        if (this.ended)\n            return;\n        __classPrivateFieldGet(this, _MessageStream_resolveConnectedPromise, \"f\").call(this);\n        this._emit('connect');\n    }\n    get ended() {\n        return __classPrivateFieldGet(this, _MessageStream_ended, \"f\");\n    }\n    get errored() {\n        return __classPrivateFieldGet(this, _MessageStream_errored, \"f\");\n    }\n    get aborted() {\n        return __classPrivateFieldGet(this, _MessageStream_aborted, \"f\");\n    }\n    abort() {\n        this.controller.abort();\n    }\n    /**\n     * Adds the listener function to the end of the listeners array for the event.\n     * No checks are made to see if the listener has already been added. Multiple calls passing\n     * the same combination of event and listener will result in the listener being added, and\n     * called, multiple times.\n     * @returns this MessageStream, so that calls can be chained\n     */\n    on(event, listener) {\n        const listeners = __classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event] || (__classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event] = []);\n        listeners.push({ listener });\n        return this;\n    }\n    /**\n     * Removes the specified listener from the listener array for the event.\n     * off() will remove, at most, one instance of a listener from the listener array. If any single\n     * listener has been added multiple times to the listener array for the specified event, then\n     * off() must be called multiple times to remove each instance.\n     * @returns this MessageStream, so that calls can be chained\n     */\n    off(event, listener) {\n        const listeners = __classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event];\n        if (!listeners)\n            return this;\n        const index = listeners.findIndex((l) => l.listener === listener);\n        if (index >= 0)\n            listeners.splice(index, 1);\n        return this;\n    }\n    /**\n     * Adds a one-time listener function for the event. The next time the event is triggered,\n     * this listener is removed and then invoked.\n     * @returns this MessageStream, so that calls can be chained\n     */\n    once(event, listener) {\n        const listeners = __classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event] || (__classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event] = []);\n        listeners.push({ listener, once: true });\n        return this;\n    }\n    /**\n     * This is similar to `.once()`, but returns a Promise that resolves the next time\n     * the event is triggered, instead of calling a listener callback.\n     * @returns a Promise that resolves the next time given event is triggered,\n     * or rejects if an error is emitted.  (If you request the 'error' event,\n     * returns a promise that resolves with the error).\n     *\n     * Example:\n     *\n     *   const message = await stream.emitted('message') // rejects if the stream errors\n     */\n    emitted(event) {\n        return new Promise((resolve, reject) => {\n            __classPrivateFieldSet(this, _MessageStream_catchingPromiseCreated, true, \"f\");\n            if (event !== 'error')\n                this.once('error', reject);\n            this.once(event, resolve);\n        });\n    }\n    async done() {\n        __classPrivateFieldSet(this, _MessageStream_catchingPromiseCreated, true, \"f\");\n        await __classPrivateFieldGet(this, _MessageStream_endPromise, \"f\");\n    }\n    get currentMessage() {\n        return __classPrivateFieldGet(this, _MessageStream_currentMessageSnapshot, \"f\");\n    }\n    /**\n     * @returns a promise that resolves with the the final assistant Message response,\n     * or rejects if an error occurred or the stream ended prematurely without producing a Message.\n     */\n    async finalMessage() {\n        await this.done();\n        return __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_getFinalMessage).call(this);\n    }\n    /**\n     * @returns a promise that resolves with the the final assistant Message's text response, concatenated\n     * together if there are more than one text blocks.\n     * Rejects if an error occurred or the stream ended prematurely without producing a Message.\n     */\n    async finalText() {\n        await this.done();\n        return __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_getFinalText).call(this);\n    }\n    _emit(event, ...args) {\n        // make sure we don't emit any MessageStreamEvents after end\n        if (__classPrivateFieldGet(this, _MessageStream_ended, \"f\"))\n            return;\n        if (event === 'end') {\n            __classPrivateFieldSet(this, _MessageStream_ended, true, \"f\");\n            __classPrivateFieldGet(this, _MessageStream_resolveEndPromise, \"f\").call(this);\n        }\n        const listeners = __classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event];\n        if (listeners) {\n            __classPrivateFieldGet(this, _MessageStream_listeners, \"f\")[event] = listeners.filter((l) => !l.once);\n            listeners.forEach(({ listener }) => listener(...args));\n        }\n        if (event === 'abort') {\n            const error = args[0];\n            if (!__classPrivateFieldGet(this, _MessageStream_catchingPromiseCreated, \"f\") && !listeners?.length) {\n                Promise.reject(error);\n            }\n            __classPrivateFieldGet(this, _MessageStream_rejectConnectedPromise, \"f\").call(this, error);\n            __classPrivateFieldGet(this, _MessageStream_rejectEndPromise, \"f\").call(this, error);\n            this._emit('end');\n            return;\n        }\n        if (event === 'error') {\n            // NOTE: _emit('error', error) should only be called from #handleError().\n            const error = args[0];\n            if (!__classPrivateFieldGet(this, _MessageStream_catchingPromiseCreated, \"f\") && !listeners?.length) {\n                // Trigger an unhandled rejection if the user hasn't registered any error handlers.\n                // If you are seeing stack traces here, make sure to handle errors via either:\n                // - runner.on('error', () => ...)\n                // - await runner.done()\n                // - await runner.final...()\n                // - etc.\n                Promise.reject(error);\n            }\n            __classPrivateFieldGet(this, _MessageStream_rejectConnectedPromise, \"f\").call(this, error);\n            __classPrivateFieldGet(this, _MessageStream_rejectEndPromise, \"f\").call(this, error);\n            this._emit('end');\n        }\n    }\n    _emitFinal() {\n        const finalMessage = this.receivedMessages.at(-1);\n        if (finalMessage) {\n            this._emit('finalMessage', __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_getFinalMessage).call(this));\n        }\n    }\n    async _fromReadableStream(readableStream, options) {\n        const signal = options?.signal;\n        if (signal) {\n            if (signal.aborted)\n                this.controller.abort();\n            signal.addEventListener('abort', () => this.controller.abort());\n        }\n        __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_beginRequest).call(this);\n        this._connected();\n        const stream = streaming_1.Stream.fromReadableStream(readableStream, this.controller);\n        for await (const event of stream) {\n            __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_addStreamEvent).call(this, event);\n        }\n        if (stream.controller.signal?.aborted) {\n            throw new error_1.APIUserAbortError();\n        }\n        __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_endRequest).call(this);\n    }\n    [(_MessageStream_currentMessageSnapshot = new WeakMap(), _MessageStream_connectedPromise = new WeakMap(), _MessageStream_resolveConnectedPromise = new WeakMap(), _MessageStream_rejectConnectedPromise = new WeakMap(), _MessageStream_endPromise = new WeakMap(), _MessageStream_resolveEndPromise = new WeakMap(), _MessageStream_rejectEndPromise = new WeakMap(), _MessageStream_listeners = new WeakMap(), _MessageStream_ended = new WeakMap(), _MessageStream_errored = new WeakMap(), _MessageStream_aborted = new WeakMap(), _MessageStream_catchingPromiseCreated = new WeakMap(), _MessageStream_handleError = new WeakMap(), _MessageStream_instances = new WeakSet(), _MessageStream_getFinalMessage = function _MessageStream_getFinalMessage() {\n        if (this.receivedMessages.length === 0) {\n            throw new error_1.AnthropicError('stream ended without producing a Message with role=assistant');\n        }\n        return this.receivedMessages.at(-1);\n    }, _MessageStream_getFinalText = function _MessageStream_getFinalText() {\n        if (this.receivedMessages.length === 0) {\n            throw new error_1.AnthropicError('stream ended without producing a Message with role=assistant');\n        }\n        const textBlocks = this.receivedMessages\n            .at(-1)\n            .content.filter((block) => block.type === 'text')\n            .map((block) => block.text);\n        if (textBlocks.length === 0) {\n            throw new error_1.AnthropicError('stream ended without producing a content block with type=text');\n        }\n        return textBlocks.join(' ');\n    }, _MessageStream_beginRequest = function _MessageStream_beginRequest() {\n        if (this.ended)\n            return;\n        __classPrivateFieldSet(this, _MessageStream_currentMessageSnapshot, undefined, \"f\");\n    }, _MessageStream_addStreamEvent = function _MessageStream_addStreamEvent(event) {\n        if (this.ended)\n            return;\n        const messageSnapshot = __classPrivateFieldGet(this, _MessageStream_instances, \"m\", _MessageStream_accumulateMessage).call(this, event);\n        this._emit('streamEvent', event, messageSnapshot);\n        switch (event.type) {\n            case 'content_block_delta': {\n                const content = messageSnapshot.content.at(-1);\n                if (event.delta.type === 'text_delta' && content.type === 'text') {\n                    this._emit('text', event.delta.text, content.text || '');\n                }\n                else if (event.delta.type === 'input_json_delta' && content.type === 'tool_use') {\n                    if (content.input) {\n                        this._emit('inputJson', event.delta.partial_json, content.input);\n                    }\n                }\n                break;\n            }\n            case 'message_stop': {\n                this._addMessageParam(messageSnapshot);\n                this._addMessage(messageSnapshot, true);\n                break;\n            }\n            case 'content_block_stop': {\n                this._emit('contentBlock', messageSnapshot.content.at(-1));\n                break;\n            }\n            case 'message_start': {\n                __classPrivateFieldSet(this, _MessageStream_currentMessageSnapshot, messageSnapshot, \"f\");\n                break;\n            }\n            case 'content_block_start':\n            case 'message_delta':\n                break;\n        }\n    }, _MessageStream_endRequest = function _MessageStream_endRequest() {\n        if (this.ended) {\n            throw new error_1.AnthropicError(`stream has ended, this shouldn't happen`);\n        }\n        const snapshot = __classPrivateFieldGet(this, _MessageStream_currentMessageSnapshot, \"f\");\n        if (!snapshot) {\n            throw new error_1.AnthropicError(`request ended without sending any chunks`);\n        }\n        __classPrivateFieldSet(this, _MessageStream_currentMessageSnapshot, undefined, \"f\");\n        return snapshot;\n    }, _MessageStream_accumulateMessage = function _MessageStream_accumulateMessage(event) {\n        let snapshot = __classPrivateFieldGet(this, _MessageStream_currentMessageSnapshot, \"f\");\n        if (event.type === 'message_start') {\n            if (snapshot) {\n                throw new error_1.AnthropicError(`Unexpected event order, got ${event.type} before receiving \"message_stop\"`);\n            }\n            return event.message;\n        }\n        if (!snapshot) {\n            throw new error_1.AnthropicError(`Unexpected event order, got ${event.type} before \"message_start\"`);\n        }\n        switch (event.type) {\n            case 'message_stop':\n                return snapshot;\n            case 'message_delta':\n                snapshot.stop_reason = event.delta.stop_reason;\n                snapshot.stop_sequence = event.delta.stop_sequence;\n                snapshot.usage.output_tokens = event.usage.output_tokens;\n                return snapshot;\n            case 'content_block_start':\n                snapshot.content.push(event.content_block);\n                return snapshot;\n            case 'content_block_delta': {\n                const snapshotContent = snapshot.content.at(event.index);\n                if (snapshotContent?.type === 'text' && event.delta.type === 'text_delta') {\n                    snapshotContent.text += event.delta.text;\n                }\n                else if (snapshotContent?.type === 'tool_use' && event.delta.type === 'input_json_delta') {\n                    // we need to keep track of the raw JSON string as well so that we can\n                    // re-parse it for each delta, for now we just store it as an untyped\n                    // non-enumerable property on the snapshot\n                    let jsonBuf = snapshotContent[JSON_BUF_PROPERTY] || '';\n                    jsonBuf += event.delta.partial_json;\n                    Object.defineProperty(snapshotContent, JSON_BUF_PROPERTY, {\n                        value: jsonBuf,\n                        enumerable: false,\n                        writable: true,\n                    });\n                    if (jsonBuf) {\n                        snapshotContent.input = (0, parser_1.partialParse)(jsonBuf);\n                    }\n                }\n                return snapshot;\n            }\n            case 'content_block_stop':\n                return snapshot;\n        }\n    }, Symbol.asyncIterator)]() {\n        const pushQueue = [];\n        const readQueue = [];\n        let done = false;\n        this.on('streamEvent', (event) => {\n            const reader = readQueue.shift();\n            if (reader) {\n                reader.resolve(event);\n            }\n            else {\n                pushQueue.push(event);\n            }\n        });\n        this.on('end', () => {\n            done = true;\n            for (const reader of readQueue) {\n                reader.resolve(undefined);\n            }\n            readQueue.length = 0;\n        });\n        this.on('abort', (err) => {\n            done = true;\n            for (const reader of readQueue) {\n                reader.reject(err);\n            }\n            readQueue.length = 0;\n        });\n        this.on('error', (err) => {\n            done = true;\n            for (const reader of readQueue) {\n                reader.reject(err);\n            }\n            readQueue.length = 0;\n        });\n        return {\n            next: async () => {\n                if (!pushQueue.length) {\n                    if (done) {\n                        return { value: undefined, done: true };\n                    }\n                    return new Promise((resolve, reject) => readQueue.push({ resolve, reject })).then((chunk) => (chunk ? { value: chunk, done: false } : { value: undefined, done: true }));\n                }\n                const chunk = pushQueue.shift();\n                return { value: chunk, done: false };\n            },\n            return: async () => {\n                this.abort();\n                return { value: undefined, done: true };\n            },\n        };\n    }\n    toReadableStream() {\n        const stream = new streaming_1.Stream(this[Symbol.asyncIterator].bind(this), this.controller);\n        return stream.toReadableStream();\n    }\n}\nexports.MessageStream = MessageStream;\n//# sourceMappingURL=MessageStream.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/lib/MessageStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/lib/PromptCachingBetaMessageStream.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/lib/PromptCachingBetaMessageStream.js ***!
  \**************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _PromptCachingBetaMessageStream_instances, _PromptCachingBetaMessageStream_currentMessageSnapshot, _PromptCachingBetaMessageStream_connectedPromise, _PromptCachingBetaMessageStream_resolveConnectedPromise, _PromptCachingBetaMessageStream_rejectConnectedPromise, _PromptCachingBetaMessageStream_endPromise, _PromptCachingBetaMessageStream_resolveEndPromise, _PromptCachingBetaMessageStream_rejectEndPromise, _PromptCachingBetaMessageStream_listeners, _PromptCachingBetaMessageStream_ended, _PromptCachingBetaMessageStream_errored, _PromptCachingBetaMessageStream_aborted, _PromptCachingBetaMessageStream_catchingPromiseCreated, _PromptCachingBetaMessageStream_getFinalMessage, _PromptCachingBetaMessageStream_getFinalText, _PromptCachingBetaMessageStream_handleError, _PromptCachingBetaMessageStream_beginRequest, _PromptCachingBetaMessageStream_addStreamEvent, _PromptCachingBetaMessageStream_endRequest, _PromptCachingBetaMessageStream_accumulateMessage;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PromptCachingBetaMessageStream = void 0;\nconst error_1 = __webpack_require__(/*! @anthropic-ai/sdk/error */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/error.js\");\nconst streaming_1 = __webpack_require__(/*! @anthropic-ai/sdk/streaming */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/streaming.js\");\nconst parser_1 = __webpack_require__(/*! ../_vendor/partial-json-parser/parser.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_vendor/partial-json-parser/parser.js\");\nconst JSON_BUF_PROPERTY = '__json_buf';\nclass PromptCachingBetaMessageStream {\n    constructor() {\n        _PromptCachingBetaMessageStream_instances.add(this);\n        this.messages = [];\n        this.receivedMessages = [];\n        _PromptCachingBetaMessageStream_currentMessageSnapshot.set(this, void 0);\n        this.controller = new AbortController();\n        _PromptCachingBetaMessageStream_connectedPromise.set(this, void 0);\n        _PromptCachingBetaMessageStream_resolveConnectedPromise.set(this, () => { });\n        _PromptCachingBetaMessageStream_rejectConnectedPromise.set(this, () => { });\n        _PromptCachingBetaMessageStream_endPromise.set(this, void 0);\n        _PromptCachingBetaMessageStream_resolveEndPromise.set(this, () => { });\n        _PromptCachingBetaMessageStream_rejectEndPromise.set(this, () => { });\n        _PromptCachingBetaMessageStream_listeners.set(this, {});\n        _PromptCachingBetaMessageStream_ended.set(this, false);\n        _PromptCachingBetaMessageStream_errored.set(this, false);\n        _PromptCachingBetaMessageStream_aborted.set(this, false);\n        _PromptCachingBetaMessageStream_catchingPromiseCreated.set(this, false);\n        _PromptCachingBetaMessageStream_handleError.set(this, (error) => {\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_errored, true, \"f\");\n            if (error instanceof Error && error.name === 'AbortError') {\n                error = new error_1.APIUserAbortError();\n            }\n            if (error instanceof error_1.APIUserAbortError) {\n                __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_aborted, true, \"f\");\n                return this._emit('abort', error);\n            }\n            if (error instanceof error_1.AnthropicError) {\n                return this._emit('error', error);\n            }\n            if (error instanceof Error) {\n                const anthropicError = new error_1.AnthropicError(error.message);\n                // @ts-ignore\n                anthropicError.cause = error;\n                return this._emit('error', anthropicError);\n            }\n            return this._emit('error', new error_1.AnthropicError(String(error)));\n        });\n        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_connectedPromise, new Promise((resolve, reject) => {\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_resolveConnectedPromise, resolve, \"f\");\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_rejectConnectedPromise, reject, \"f\");\n        }), \"f\");\n        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_endPromise, new Promise((resolve, reject) => {\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_resolveEndPromise, resolve, \"f\");\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_rejectEndPromise, reject, \"f\");\n        }), \"f\");\n        // Don't let these promises cause unhandled rejection errors.\n        // we will manually cause an unhandled rejection error later\n        // if the user hasn't registered any error listener or called\n        // any promise-returning method.\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_connectedPromise, \"f\").catch(() => { });\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_endPromise, \"f\").catch(() => { });\n    }\n    /**\n     * Intended for use on the frontend, consuming a stream produced with\n     * `.toReadableStream()` on the backend.\n     *\n     * Note that messages sent to the model do not appear in `.on('message')`\n     * in this context.\n     */\n    static fromReadableStream(stream) {\n        const runner = new PromptCachingBetaMessageStream();\n        runner._run(() => runner._fromReadableStream(stream));\n        return runner;\n    }\n    static createMessage(messages, params, options) {\n        const runner = new PromptCachingBetaMessageStream();\n        for (const message of params.messages) {\n            runner._addPromptCachingBetaMessageParam(message);\n        }\n        runner._run(() => runner._createPromptCachingBetaMessage(messages, { ...params, stream: true }, { ...options, headers: { ...options?.headers, 'X-Stainless-Helper-Method': 'stream' } }));\n        return runner;\n    }\n    _run(executor) {\n        executor().then(() => {\n            this._emitFinal();\n            this._emit('end');\n        }, __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_handleError, \"f\"));\n    }\n    _addPromptCachingBetaMessageParam(message) {\n        this.messages.push(message);\n    }\n    _addPromptCachingBetaMessage(message, emit = true) {\n        this.receivedMessages.push(message);\n        if (emit) {\n            this._emit('message', message);\n        }\n    }\n    async _createPromptCachingBetaMessage(messages, params, options) {\n        const signal = options?.signal;\n        if (signal) {\n            if (signal.aborted)\n                this.controller.abort();\n            signal.addEventListener('abort', () => this.controller.abort());\n        }\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_beginRequest).call(this);\n        const stream = await messages.create({ ...params, stream: true }, { ...options, signal: this.controller.signal });\n        this._connected();\n        for await (const event of stream) {\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_addStreamEvent).call(this, event);\n        }\n        if (stream.controller.signal?.aborted) {\n            throw new error_1.APIUserAbortError();\n        }\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_endRequest).call(this);\n    }\n    _connected() {\n        if (this.ended)\n            return;\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_resolveConnectedPromise, \"f\").call(this);\n        this._emit('connect');\n    }\n    get ended() {\n        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_ended, \"f\");\n    }\n    get errored() {\n        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_errored, \"f\");\n    }\n    get aborted() {\n        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_aborted, \"f\");\n    }\n    abort() {\n        this.controller.abort();\n    }\n    /**\n     * Adds the listener function to the end of the listeners array for the event.\n     * No checks are made to see if the listener has already been added. Multiple calls passing\n     * the same combination of event and listener will result in the listener being added, and\n     * called, multiple times.\n     * @returns this PromptCachingBetaMessageStream, so that calls can be chained\n     */\n    on(event, listener) {\n        const listeners = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event] || (__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event] = []);\n        listeners.push({ listener });\n        return this;\n    }\n    /**\n     * Removes the specified listener from the listener array for the event.\n     * off() will remove, at most, one instance of a listener from the listener array. If any single\n     * listener has been added multiple times to the listener array for the specified event, then\n     * off() must be called multiple times to remove each instance.\n     * @returns this PromptCachingBetaMessageStream, so that calls can be chained\n     */\n    off(event, listener) {\n        const listeners = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event];\n        if (!listeners)\n            return this;\n        const index = listeners.findIndex((l) => l.listener === listener);\n        if (index >= 0)\n            listeners.splice(index, 1);\n        return this;\n    }\n    /**\n     * Adds a one-time listener function for the event. The next time the event is triggered,\n     * this listener is removed and then invoked.\n     * @returns this PromptCachingBetaMessageStream, so that calls can be chained\n     */\n    once(event, listener) {\n        const listeners = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event] || (__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event] = []);\n        listeners.push({ listener, once: true });\n        return this;\n    }\n    /**\n     * This is similar to `.once()`, but returns a Promise that resolves the next time\n     * the event is triggered, instead of calling a listener callback.\n     * @returns a Promise that resolves the next time given event is triggered,\n     * or rejects if an error is emitted.  (If you request the 'error' event,\n     * returns a promise that resolves with the error).\n     *\n     * Example:\n     *\n     *   const message = await stream.emitted('message') // rejects if the stream errors\n     */\n    emitted(event) {\n        return new Promise((resolve, reject) => {\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_catchingPromiseCreated, true, \"f\");\n            if (event !== 'error')\n                this.once('error', reject);\n            this.once(event, resolve);\n        });\n    }\n    async done() {\n        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_catchingPromiseCreated, true, \"f\");\n        await __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_endPromise, \"f\");\n    }\n    get currentMessage() {\n        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, \"f\");\n    }\n    /**\n     * @returns a promise that resolves with the the final assistant PromptCachingBetaMessage response,\n     * or rejects if an error occurred or the stream ended prematurely without producing a PromptCachingBetaMessage.\n     */\n    async finalMessage() {\n        await this.done();\n        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_getFinalMessage).call(this);\n    }\n    /**\n     * @returns a promise that resolves with the the final assistant PromptCachingBetaMessage's text response, concatenated\n     * together if there are more than one text blocks.\n     * Rejects if an error occurred or the stream ended prematurely without producing a PromptCachingBetaMessage.\n     */\n    async finalText() {\n        await this.done();\n        return __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_getFinalText).call(this);\n    }\n    _emit(event, ...args) {\n        // make sure we don't emit any PromptCachingBetaMessageStreamEvents after end\n        if (__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_ended, \"f\"))\n            return;\n        if (event === 'end') {\n            __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_ended, true, \"f\");\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_resolveEndPromise, \"f\").call(this);\n        }\n        const listeners = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event];\n        if (listeners) {\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_listeners, \"f\")[event] = listeners.filter((l) => !l.once);\n            listeners.forEach(({ listener }) => listener(...args));\n        }\n        if (event === 'abort') {\n            const error = args[0];\n            if (!__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_catchingPromiseCreated, \"f\") && !listeners?.length) {\n                Promise.reject(error);\n            }\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_rejectConnectedPromise, \"f\").call(this, error);\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_rejectEndPromise, \"f\").call(this, error);\n            this._emit('end');\n            return;\n        }\n        if (event === 'error') {\n            // NOTE: _emit('error', error) should only be called from #handleError().\n            const error = args[0];\n            if (!__classPrivateFieldGet(this, _PromptCachingBetaMessageStream_catchingPromiseCreated, \"f\") && !listeners?.length) {\n                // Trigger an unhandled rejection if the user hasn't registered any error handlers.\n                // If you are seeing stack traces here, make sure to handle errors via either:\n                // - runner.on('error', () => ...)\n                // - await runner.done()\n                // - await runner.final...()\n                // - etc.\n                Promise.reject(error);\n            }\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_rejectConnectedPromise, \"f\").call(this, error);\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_rejectEndPromise, \"f\").call(this, error);\n            this._emit('end');\n        }\n    }\n    _emitFinal() {\n        const finalPromptCachingBetaMessage = this.receivedMessages.at(-1);\n        if (finalPromptCachingBetaMessage) {\n            this._emit('finalPromptCachingBetaMessage', __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_getFinalMessage).call(this));\n        }\n    }\n    async _fromReadableStream(readableStream, options) {\n        const signal = options?.signal;\n        if (signal) {\n            if (signal.aborted)\n                this.controller.abort();\n            signal.addEventListener('abort', () => this.controller.abort());\n        }\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_beginRequest).call(this);\n        this._connected();\n        const stream = streaming_1.Stream.fromReadableStream(readableStream, this.controller);\n        for await (const event of stream) {\n            __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_addStreamEvent).call(this, event);\n        }\n        if (stream.controller.signal?.aborted) {\n            throw new error_1.APIUserAbortError();\n        }\n        __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_endRequest).call(this);\n    }\n    [(_PromptCachingBetaMessageStream_currentMessageSnapshot = new WeakMap(), _PromptCachingBetaMessageStream_connectedPromise = new WeakMap(), _PromptCachingBetaMessageStream_resolveConnectedPromise = new WeakMap(), _PromptCachingBetaMessageStream_rejectConnectedPromise = new WeakMap(), _PromptCachingBetaMessageStream_endPromise = new WeakMap(), _PromptCachingBetaMessageStream_resolveEndPromise = new WeakMap(), _PromptCachingBetaMessageStream_rejectEndPromise = new WeakMap(), _PromptCachingBetaMessageStream_listeners = new WeakMap(), _PromptCachingBetaMessageStream_ended = new WeakMap(), _PromptCachingBetaMessageStream_errored = new WeakMap(), _PromptCachingBetaMessageStream_aborted = new WeakMap(), _PromptCachingBetaMessageStream_catchingPromiseCreated = new WeakMap(), _PromptCachingBetaMessageStream_handleError = new WeakMap(), _PromptCachingBetaMessageStream_instances = new WeakSet(), _PromptCachingBetaMessageStream_getFinalMessage = function _PromptCachingBetaMessageStream_getFinalMessage() {\n        if (this.receivedMessages.length === 0) {\n            throw new error_1.AnthropicError('stream ended without producing a PromptCachingBetaMessage with role=assistant');\n        }\n        return this.receivedMessages.at(-1);\n    }, _PromptCachingBetaMessageStream_getFinalText = function _PromptCachingBetaMessageStream_getFinalText() {\n        if (this.receivedMessages.length === 0) {\n            throw new error_1.AnthropicError('stream ended without producing a PromptCachingBetaMessage with role=assistant');\n        }\n        const textBlocks = this.receivedMessages\n            .at(-1)\n            .content.filter((block) => block.type === 'text')\n            .map((block) => block.text);\n        if (textBlocks.length === 0) {\n            throw new error_1.AnthropicError('stream ended without producing a content block with type=text');\n        }\n        return textBlocks.join(' ');\n    }, _PromptCachingBetaMessageStream_beginRequest = function _PromptCachingBetaMessageStream_beginRequest() {\n        if (this.ended)\n            return;\n        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, undefined, \"f\");\n    }, _PromptCachingBetaMessageStream_addStreamEvent = function _PromptCachingBetaMessageStream_addStreamEvent(event) {\n        if (this.ended)\n            return;\n        const messageSnapshot = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_instances, \"m\", _PromptCachingBetaMessageStream_accumulateMessage).call(this, event);\n        this._emit('streamEvent', event, messageSnapshot);\n        switch (event.type) {\n            case 'content_block_delta': {\n                const content = messageSnapshot.content.at(-1);\n                if (event.delta.type === 'text_delta' && content.type === 'text') {\n                    this._emit('text', event.delta.text, content.text || '');\n                }\n                else if (event.delta.type === 'input_json_delta' && content.type === 'tool_use') {\n                    if (content.input) {\n                        this._emit('inputJson', event.delta.partial_json, content.input);\n                    }\n                }\n                break;\n            }\n            case 'message_stop': {\n                this._addPromptCachingBetaMessageParam(messageSnapshot);\n                this._addPromptCachingBetaMessage(messageSnapshot, true);\n                break;\n            }\n            case 'content_block_stop': {\n                this._emit('contentBlock', messageSnapshot.content.at(-1));\n                break;\n            }\n            case 'message_start': {\n                __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, messageSnapshot, \"f\");\n                break;\n            }\n            case 'content_block_start':\n            case 'message_delta':\n                break;\n        }\n    }, _PromptCachingBetaMessageStream_endRequest = function _PromptCachingBetaMessageStream_endRequest() {\n        if (this.ended) {\n            throw new error_1.AnthropicError(`stream has ended, this shouldn't happen`);\n        }\n        const snapshot = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, \"f\");\n        if (!snapshot) {\n            throw new error_1.AnthropicError(`request ended without sending any chunks`);\n        }\n        __classPrivateFieldSet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, undefined, \"f\");\n        return snapshot;\n    }, _PromptCachingBetaMessageStream_accumulateMessage = function _PromptCachingBetaMessageStream_accumulateMessage(event) {\n        let snapshot = __classPrivateFieldGet(this, _PromptCachingBetaMessageStream_currentMessageSnapshot, \"f\");\n        if (event.type === 'message_start') {\n            if (snapshot) {\n                throw new error_1.AnthropicError(`Unexpected event order, got ${event.type} before receiving \"message_stop\"`);\n            }\n            return event.message;\n        }\n        if (!snapshot) {\n            throw new error_1.AnthropicError(`Unexpected event order, got ${event.type} before \"message_start\"`);\n        }\n        switch (event.type) {\n            case 'message_stop':\n                return snapshot;\n            case 'message_delta':\n                snapshot.stop_reason = event.delta.stop_reason;\n                snapshot.stop_sequence = event.delta.stop_sequence;\n                snapshot.usage.output_tokens = event.usage.output_tokens;\n                return snapshot;\n            case 'content_block_start':\n                snapshot.content.push(event.content_block);\n                return snapshot;\n            case 'content_block_delta': {\n                const snapshotContent = snapshot.content.at(event.index);\n                if (snapshotContent?.type === 'text' && event.delta.type === 'text_delta') {\n                    snapshotContent.text += event.delta.text;\n                }\n                else if (snapshotContent?.type === 'tool_use' && event.delta.type === 'input_json_delta') {\n                    // we need to keep track of the raw JSON string as well so that we can\n                    // re-parse it for each delta, for now we just store it as an untyped\n                    // non-enumerable property on the snapshot\n                    let jsonBuf = snapshotContent[JSON_BUF_PROPERTY] || '';\n                    jsonBuf += event.delta.partial_json;\n                    Object.defineProperty(snapshotContent, JSON_BUF_PROPERTY, {\n                        value: jsonBuf,\n                        enumerable: false,\n                        writable: true,\n                    });\n                    if (jsonBuf) {\n                        snapshotContent.input = (0, parser_1.partialParse)(jsonBuf);\n                    }\n                }\n                return snapshot;\n            }\n            case 'content_block_stop':\n                return snapshot;\n        }\n    }, Symbol.asyncIterator)]() {\n        const pushQueue = [];\n        const readQueue = [];\n        let done = false;\n        this.on('streamEvent', (event) => {\n            const reader = readQueue.shift();\n            if (reader) {\n                reader.resolve(event);\n            }\n            else {\n                pushQueue.push(event);\n            }\n        });\n        this.on('end', () => {\n            done = true;\n            for (const reader of readQueue) {\n                reader.resolve(undefined);\n            }\n            readQueue.length = 0;\n        });\n        this.on('abort', (err) => {\n            done = true;\n            for (const reader of readQueue) {\n                reader.reject(err);\n            }\n            readQueue.length = 0;\n        });\n        this.on('error', (err) => {\n            done = true;\n            for (const reader of readQueue) {\n                reader.reject(err);\n            }\n            readQueue.length = 0;\n        });\n        return {\n            next: async () => {\n                if (!pushQueue.length) {\n                    if (done) {\n                        return { value: undefined, done: true };\n                    }\n                    return new Promise((resolve, reject) => readQueue.push({ resolve, reject })).then((chunk) => (chunk ? { value: chunk, done: false } : { value: undefined, done: true }));\n                }\n                const chunk = pushQueue.shift();\n                return { value: chunk, done: false };\n            },\n            return: async () => {\n                this.abort();\n                return { value: undefined, done: true };\n            },\n        };\n    }\n    toReadableStream() {\n        const stream = new streaming_1.Stream(this[Symbol.asyncIterator].bind(this), this.controller);\n        return stream.toReadableStream();\n    }\n}\nexports.PromptCachingBetaMessageStream = PromptCachingBetaMessageStream;\n//# sourceMappingURL=PromptCachingBetaMessageStream.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/lib/PromptCachingBetaMessageStream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resource.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resource.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.APIResource = void 0;\nclass APIResource {\n    constructor(client) {\n        this._client = client;\n    }\n}\nexports.APIResource = APIResource;\n//# sourceMappingURL=resource.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGFudGhyb3BpYy1haStzZGtAMC4yNy4zL25vZGVfbW9kdWxlcy9AYW50aHJvcGljLWFpL3Nkay9yZXNvdXJjZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGFudGhyb3BpYy1haStzZGtAMC4yNy4zL25vZGVfbW9kdWxlcy9AYW50aHJvcGljLWFpL3Nkay9yZXNvdXJjZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkFQSVJlc291cmNlID0gdm9pZCAwO1xuY2xhc3MgQVBJUmVzb3VyY2Uge1xuICAgIGNvbnN0cnVjdG9yKGNsaWVudCkge1xuICAgICAgICB0aGlzLl9jbGllbnQgPSBjbGllbnQ7XG4gICAgfVxufVxuZXhwb3J0cy5BUElSZXNvdXJjZSA9IEFQSVJlc291cmNlO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVzb3VyY2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resource.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/beta/beta.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/beta/beta.js ***!
  \***********************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Beta = void 0;\nconst resource_1 = __webpack_require__(/*! ../../resource.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resource.js\");\nconst PromptCachingAPI = __importStar(__webpack_require__(/*! ./prompt-caching/prompt-caching.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/prompt-caching.js\"));\nclass Beta extends resource_1.APIResource {\n    constructor() {\n        super(...arguments);\n        this.promptCaching = new PromptCachingAPI.PromptCaching(this._client);\n    }\n}\nexports.Beta = Beta;\n(function (Beta) {\n    Beta.PromptCaching = PromptCachingAPI.PromptCaching;\n})(Beta = exports.Beta || (exports.Beta = {}));\n//# sourceMappingURL=beta.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/beta/beta.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/messages.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/messages.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Messages = void 0;\nconst resource_1 = __webpack_require__(/*! ../../../resource.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resource.js\");\nconst PromptCachingBetaMessageStream_1 = __webpack_require__(/*! ../../../lib/PromptCachingBetaMessageStream.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/lib/PromptCachingBetaMessageStream.js\");\nclass Messages extends resource_1.APIResource {\n    create(body, options) {\n        return this._client.post('/v1/messages?beta=prompt_caching', {\n            body,\n            timeout: this._client._options.timeout ?? 600000,\n            ...options,\n            headers: { 'anthropic-beta': 'prompt-caching-2024-07-31', ...options?.headers },\n            stream: body.stream ?? false,\n        });\n    }\n    /**\n     * Create a Message stream\n     */\n    stream(body, options) {\n        return PromptCachingBetaMessageStream_1.PromptCachingBetaMessageStream.createMessage(this, body, options);\n    }\n}\nexports.Messages = Messages;\n(function (Messages) {\n})(Messages = exports.Messages || (exports.Messages = {}));\n//# sourceMappingURL=messages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/messages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/prompt-caching.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/prompt-caching.js ***!
  \************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PromptCaching = void 0;\nconst resource_1 = __webpack_require__(/*! ../../../resource.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resource.js\");\nconst MessagesAPI = __importStar(__webpack_require__(/*! ./messages.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/messages.js\"));\nclass PromptCaching extends resource_1.APIResource {\n    constructor() {\n        super(...arguments);\n        this.messages = new MessagesAPI.Messages(this._client);\n    }\n}\nexports.PromptCaching = PromptCaching;\n(function (PromptCaching) {\n    PromptCaching.Messages = MessagesAPI.Messages;\n})(PromptCaching = exports.PromptCaching || (exports.PromptCaching = {}));\n//# sourceMappingURL=prompt-caching.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/beta/prompt-caching/prompt-caching.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/completions.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/completions.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Completions = void 0;\nconst resource_1 = __webpack_require__(/*! ../resource.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resource.js\");\nclass Completions extends resource_1.APIResource {\n    create(body, options) {\n        return this._client.post('/v1/complete', {\n            body,\n            timeout: this._client._options.timeout ?? 600000,\n            ...options,\n            stream: body.stream ?? false,\n        });\n    }\n}\nexports.Completions = Completions;\n(function (Completions) {\n})(Completions = exports.Completions || (exports.Completions = {}));\n//# sourceMappingURL=completions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGFudGhyb3BpYy1haStzZGtAMC4yNy4zL25vZGVfbW9kdWxlcy9AYW50aHJvcGljLWFpL3Nkay9yZXNvdXJjZXMvY29tcGxldGlvbnMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxtQkFBbUI7QUFDbkIsbUJBQW1CLG1CQUFPLENBQUMsc0hBQWdCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQSxDQUFDLHdDQUF3QyxtQkFBbUIsS0FBSztBQUNqRSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BhbnRocm9waWMtYWkrc2RrQDAuMjcuMy9ub2RlX21vZHVsZXMvQGFudGhyb3BpYy1haS9zZGsvcmVzb3VyY2VzL2NvbXBsZXRpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuQ29tcGxldGlvbnMgPSB2b2lkIDA7XG5jb25zdCByZXNvdXJjZV8xID0gcmVxdWlyZShcIi4uL3Jlc291cmNlLmpzXCIpO1xuY2xhc3MgQ29tcGxldGlvbnMgZXh0ZW5kcyByZXNvdXJjZV8xLkFQSVJlc291cmNlIHtcbiAgICBjcmVhdGUoYm9keSwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LnBvc3QoJy92MS9jb21wbGV0ZScsIHtcbiAgICAgICAgICAgIGJvZHksXG4gICAgICAgICAgICB0aW1lb3V0OiB0aGlzLl9jbGllbnQuX29wdGlvbnMudGltZW91dCA/PyA2MDAwMDAsXG4gICAgICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAgICAgc3RyZWFtOiBib2R5LnN0cmVhbSA/PyBmYWxzZSxcbiAgICAgICAgfSk7XG4gICAgfVxufVxuZXhwb3J0cy5Db21wbGV0aW9ucyA9IENvbXBsZXRpb25zO1xuKGZ1bmN0aW9uIChDb21wbGV0aW9ucykge1xufSkoQ29tcGxldGlvbnMgPSBleHBvcnRzLkNvbXBsZXRpb25zIHx8IChleHBvcnRzLkNvbXBsZXRpb25zID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbXBsZXRpb25zLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/completions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/index.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/index.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Messages = exports.Completions = exports.Beta = void 0;\nvar beta_1 = __webpack_require__(/*! ./beta/beta.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/beta/beta.js\");\nObject.defineProperty(exports, \"Beta\", ({ enumerable: true, get: function () { return beta_1.Beta; } }));\nvar completions_1 = __webpack_require__(/*! ./completions.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/completions.js\");\nObject.defineProperty(exports, \"Completions\", ({ enumerable: true, get: function () { return completions_1.Completions; } }));\nvar messages_1 = __webpack_require__(/*! ./messages.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/messages.js\");\nObject.defineProperty(exports, \"Messages\", ({ enumerable: true, get: function () { return messages_1.Messages; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGFudGhyb3BpYy1haStzZGtAMC4yNy4zL25vZGVfbW9kdWxlcy9AYW50aHJvcGljLWFpL3Nkay9yZXNvdXJjZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxnQkFBZ0IsR0FBRyxtQkFBbUIsR0FBRyxZQUFZO0FBQ3JELGFBQWEsbUJBQU8sQ0FBQyxpSUFBZ0I7QUFDckMsd0NBQXVDLEVBQUUscUNBQXFDLHVCQUF1QixFQUFDO0FBQ3RHLG9CQUFvQixtQkFBTyxDQUFDLHFJQUFrQjtBQUM5QywrQ0FBOEMsRUFBRSxxQ0FBcUMscUNBQXFDLEVBQUM7QUFDM0gsaUJBQWlCLG1CQUFPLENBQUMsK0hBQWU7QUFDeEMsNENBQTJDLEVBQUUscUNBQXFDLCtCQUErQixFQUFDO0FBQ2xIIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGFudGhyb3BpYy1haStzZGtAMC4yNy4zL25vZGVfbW9kdWxlcy9AYW50aHJvcGljLWFpL3Nkay9yZXNvdXJjZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBGaWxlIGdlbmVyYXRlZCBmcm9tIG91ciBPcGVuQVBJIHNwZWMgYnkgU3RhaW5sZXNzLiBTZWUgQ09OVFJJQlVUSU5HLm1kIGZvciBkZXRhaWxzLlxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5NZXNzYWdlcyA9IGV4cG9ydHMuQ29tcGxldGlvbnMgPSBleHBvcnRzLkJldGEgPSB2b2lkIDA7XG52YXIgYmV0YV8xID0gcmVxdWlyZShcIi4vYmV0YS9iZXRhLmpzXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiQmV0YVwiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gYmV0YV8xLkJldGE7IH0gfSk7XG52YXIgY29tcGxldGlvbnNfMSA9IHJlcXVpcmUoXCIuL2NvbXBsZXRpb25zLmpzXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiQ29tcGxldGlvbnNcIiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIGNvbXBsZXRpb25zXzEuQ29tcGxldGlvbnM7IH0gfSk7XG52YXIgbWVzc2FnZXNfMSA9IHJlcXVpcmUoXCIuL21lc3NhZ2VzLmpzXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiTWVzc2FnZXNcIiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIG1lc3NhZ2VzXzEuTWVzc2FnZXM7IH0gfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/messages.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/messages.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Messages = exports.MessageStream = void 0;\nconst resource_1 = __webpack_require__(/*! ../resource.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resource.js\");\nconst MessageStream_1 = __webpack_require__(/*! ../lib/MessageStream.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/lib/MessageStream.js\");\nvar MessageStream_2 = __webpack_require__(/*! ../lib/MessageStream.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/lib/MessageStream.js\");\nObject.defineProperty(exports, \"MessageStream\", ({ enumerable: true, get: function () { return MessageStream_2.MessageStream; } }));\nclass Messages extends resource_1.APIResource {\n    create(body, options) {\n        if (body.model in DEPRECATED_MODELS) {\n            console.warn(`The model '${body.model}' is deprecated and will reach end-of-life on ${DEPRECATED_MODELS[body.model]}\\nPlease migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);\n        }\n        return this._client.post('/v1/messages', {\n            body,\n            timeout: this._client._options.timeout ?? 600000,\n            ...options,\n            stream: body.stream ?? false,\n        });\n    }\n    /**\n     * Create a Message stream\n     */\n    stream(body, options) {\n        return MessageStream_1.MessageStream.createMessage(this, body, options);\n    }\n}\nexports.Messages = Messages;\nconst DEPRECATED_MODELS = {\n    'claude-1.3': 'November 6th, 2024',\n    'claude-1.3-100k': 'November 6th, 2024',\n    'claude-instant-1.1': 'November 6th, 2024',\n    'claude-instant-1.1-100k': 'November 6th, 2024',\n    'claude-instant-1.2': 'November 6th, 2024',\n};\n(function (Messages) {\n})(Messages = exports.Messages || (exports.Messages = {}));\n//# sourceMappingURL=messages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/resources/messages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/streaming.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/streaming.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.readableStreamAsyncIterable = exports._decodeChunks = exports._iterSSEMessages = exports.Stream = void 0;\nconst index_1 = __webpack_require__(/*! ./_shims/index.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/index.js\");\nconst error_1 = __webpack_require__(/*! ./error.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/error.js\");\nconst core_1 = __webpack_require__(/*! @anthropic-ai/sdk/core */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/core.js\");\nconst error_2 = __webpack_require__(/*! @anthropic-ai/sdk/error */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/error.js\");\nclass Stream {\n    constructor(iterator, controller) {\n        this.iterator = iterator;\n        this.controller = controller;\n    }\n    static fromSSEResponse(response, controller) {\n        let consumed = false;\n        async function* iterator() {\n            if (consumed) {\n                throw new Error('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n            }\n            consumed = true;\n            let done = false;\n            try {\n                for await (const sse of _iterSSEMessages(response, controller)) {\n                    if (sse.event === 'completion') {\n                        try {\n                            yield JSON.parse(sse.data);\n                        }\n                        catch (e) {\n                            console.error(`Could not parse message into JSON:`, sse.data);\n                            console.error(`From chunk:`, sse.raw);\n                            throw e;\n                        }\n                    }\n                    if (sse.event === 'message_start' ||\n                        sse.event === 'message_delta' ||\n                        sse.event === 'message_stop' ||\n                        sse.event === 'content_block_start' ||\n                        sse.event === 'content_block_delta' ||\n                        sse.event === 'content_block_stop') {\n                        try {\n                            yield JSON.parse(sse.data);\n                        }\n                        catch (e) {\n                            console.error(`Could not parse message into JSON:`, sse.data);\n                            console.error(`From chunk:`, sse.raw);\n                            throw e;\n                        }\n                    }\n                    if (sse.event === 'ping') {\n                        continue;\n                    }\n                    if (sse.event === 'error') {\n                        throw error_2.APIError.generate(undefined, `SSE Error: ${sse.data}`, sse.data, (0, core_1.createResponseHeaders)(response.headers));\n                    }\n                }\n                done = true;\n            }\n            catch (e) {\n                // If the user calls `stream.controller.abort()`, we should exit without throwing.\n                if (e instanceof Error && e.name === 'AbortError')\n                    return;\n                throw e;\n            }\n            finally {\n                // If the user `break`s, abort the ongoing request.\n                if (!done)\n                    controller.abort();\n            }\n        }\n        return new Stream(iterator, controller);\n    }\n    /**\n     * Generates a Stream from a newline-separated ReadableStream\n     * where each item is a JSON value.\n     */\n    static fromReadableStream(readableStream, controller) {\n        let consumed = false;\n        async function* iterLines() {\n            const lineDecoder = new LineDecoder();\n            const iter = readableStreamAsyncIterable(readableStream);\n            for await (const chunk of iter) {\n                for (const line of lineDecoder.decode(chunk)) {\n                    yield line;\n                }\n            }\n            for (const line of lineDecoder.flush()) {\n                yield line;\n            }\n        }\n        async function* iterator() {\n            if (consumed) {\n                throw new Error('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n            }\n            consumed = true;\n            let done = false;\n            try {\n                for await (const line of iterLines()) {\n                    if (done)\n                        continue;\n                    if (line)\n                        yield JSON.parse(line);\n                }\n                done = true;\n            }\n            catch (e) {\n                // If the user calls `stream.controller.abort()`, we should exit without throwing.\n                if (e instanceof Error && e.name === 'AbortError')\n                    return;\n                throw e;\n            }\n            finally {\n                // If the user `break`s, abort the ongoing request.\n                if (!done)\n                    controller.abort();\n            }\n        }\n        return new Stream(iterator, controller);\n    }\n    [Symbol.asyncIterator]() {\n        return this.iterator();\n    }\n    /**\n     * Splits the stream into two streams which can be\n     * independently read from at different speeds.\n     */\n    tee() {\n        const left = [];\n        const right = [];\n        const iterator = this.iterator();\n        const teeIterator = (queue) => {\n            return {\n                next: () => {\n                    if (queue.length === 0) {\n                        const result = iterator.next();\n                        left.push(result);\n                        right.push(result);\n                    }\n                    return queue.shift();\n                },\n            };\n        };\n        return [\n            new Stream(() => teeIterator(left), this.controller),\n            new Stream(() => teeIterator(right), this.controller),\n        ];\n    }\n    /**\n     * Converts this stream to a newline-separated ReadableStream of\n     * JSON stringified values in the stream\n     * which can be turned back into a Stream with `Stream.fromReadableStream()`.\n     */\n    toReadableStream() {\n        const self = this;\n        let iter;\n        const encoder = new TextEncoder();\n        return new index_1.ReadableStream({\n            async start() {\n                iter = self[Symbol.asyncIterator]();\n            },\n            async pull(ctrl) {\n                try {\n                    const { value, done } = await iter.next();\n                    if (done)\n                        return ctrl.close();\n                    const bytes = encoder.encode(JSON.stringify(value) + '\\n');\n                    ctrl.enqueue(bytes);\n                }\n                catch (err) {\n                    ctrl.error(err);\n                }\n            },\n            async cancel() {\n                await iter.return?.();\n            },\n        });\n    }\n}\nexports.Stream = Stream;\nasync function* _iterSSEMessages(response, controller) {\n    if (!response.body) {\n        controller.abort();\n        throw new error_1.AnthropicError(`Attempted to iterate over a response with no body`);\n    }\n    const sseDecoder = new SSEDecoder();\n    const lineDecoder = new LineDecoder();\n    const iter = readableStreamAsyncIterable(response.body);\n    for await (const sseChunk of iterSSEChunks(iter)) {\n        for (const line of lineDecoder.decode(sseChunk)) {\n            const sse = sseDecoder.decode(line);\n            if (sse)\n                yield sse;\n        }\n    }\n    for (const line of lineDecoder.flush()) {\n        const sse = sseDecoder.decode(line);\n        if (sse)\n            yield sse;\n    }\n}\nexports._iterSSEMessages = _iterSSEMessages;\n/**\n * Given an async iterable iterator, iterates over it and yields full\n * SSE chunks, i.e. yields when a double new-line is encountered.\n */\nasync function* iterSSEChunks(iterator) {\n    let data = new Uint8Array();\n    for await (const chunk of iterator) {\n        if (chunk == null) {\n            continue;\n        }\n        const binaryChunk = chunk instanceof ArrayBuffer ? new Uint8Array(chunk)\n            : typeof chunk === 'string' ? new TextEncoder().encode(chunk)\n                : chunk;\n        let newData = new Uint8Array(data.length + binaryChunk.length);\n        newData.set(data);\n        newData.set(binaryChunk, data.length);\n        data = newData;\n        let patternIndex;\n        while ((patternIndex = findDoubleNewlineIndex(data)) !== -1) {\n            yield data.slice(0, patternIndex);\n            data = data.slice(patternIndex);\n        }\n    }\n    if (data.length > 0) {\n        yield data;\n    }\n}\nfunction findDoubleNewlineIndex(buffer) {\n    // This function searches the buffer for the end patterns (\\r\\r, \\n\\n, \\r\\n\\r\\n)\n    // and returns the index right after the first occurrence of any pattern,\n    // or -1 if none of the patterns are found.\n    const newline = 0x0a; // \\n\n    const carriage = 0x0d; // \\r\n    for (let i = 0; i < buffer.length - 2; i++) {\n        if (buffer[i] === newline && buffer[i + 1] === newline) {\n            // \\n\\n\n            return i + 2;\n        }\n        if (buffer[i] === carriage && buffer[i + 1] === carriage) {\n            // \\r\\r\n            return i + 2;\n        }\n        if (buffer[i] === carriage &&\n            buffer[i + 1] === newline &&\n            i + 3 < buffer.length &&\n            buffer[i + 2] === carriage &&\n            buffer[i + 3] === newline) {\n            // \\r\\n\\r\\n\n            return i + 4;\n        }\n    }\n    return -1;\n}\nclass SSEDecoder {\n    constructor() {\n        this.event = null;\n        this.data = [];\n        this.chunks = [];\n    }\n    decode(line) {\n        if (line.endsWith('\\r')) {\n            line = line.substring(0, line.length - 1);\n        }\n        if (!line) {\n            // empty line and we didn't previously encounter any messages\n            if (!this.event && !this.data.length)\n                return null;\n            const sse = {\n                event: this.event,\n                data: this.data.join('\\n'),\n                raw: this.chunks,\n            };\n            this.event = null;\n            this.data = [];\n            this.chunks = [];\n            return sse;\n        }\n        this.chunks.push(line);\n        if (line.startsWith(':')) {\n            return null;\n        }\n        let [fieldname, _, value] = partition(line, ':');\n        if (value.startsWith(' ')) {\n            value = value.substring(1);\n        }\n        if (fieldname === 'event') {\n            this.event = value;\n        }\n        else if (fieldname === 'data') {\n            this.data.push(value);\n        }\n        return null;\n    }\n}\n/**\n * A re-implementation of httpx's `LineDecoder` in Python that handles incrementally\n * reading lines from text.\n *\n * https://github.com/encode/httpx/blob/920333ea98118e9cf617f246905d7b202510941c/httpx/_decoders.py#L258\n */\nclass LineDecoder {\n    constructor() {\n        this.buffer = [];\n        this.trailingCR = false;\n    }\n    decode(chunk) {\n        let text = this.decodeText(chunk);\n        if (this.trailingCR) {\n            text = '\\r' + text;\n            this.trailingCR = false;\n        }\n        if (text.endsWith('\\r')) {\n            this.trailingCR = true;\n            text = text.slice(0, -1);\n        }\n        if (!text) {\n            return [];\n        }\n        const trailingNewline = LineDecoder.NEWLINE_CHARS.has(text[text.length - 1] || '');\n        let lines = text.split(LineDecoder.NEWLINE_REGEXP);\n        // if there is a trailing new line then the last entry will be an empty\n        // string which we don't care about\n        if (trailingNewline) {\n            lines.pop();\n        }\n        if (lines.length === 1 && !trailingNewline) {\n            this.buffer.push(lines[0]);\n            return [];\n        }\n        if (this.buffer.length > 0) {\n            lines = [this.buffer.join('') + lines[0], ...lines.slice(1)];\n            this.buffer = [];\n        }\n        if (!trailingNewline) {\n            this.buffer = [lines.pop() || ''];\n        }\n        return lines;\n    }\n    decodeText(bytes) {\n        if (bytes == null)\n            return '';\n        if (typeof bytes === 'string')\n            return bytes;\n        // Node:\n        if (typeof Buffer !== 'undefined') {\n            if (bytes instanceof Buffer) {\n                return bytes.toString();\n            }\n            if (bytes instanceof Uint8Array) {\n                return Buffer.from(bytes).toString();\n            }\n            throw new error_1.AnthropicError(`Unexpected: received non-Uint8Array (${bytes.constructor.name}) stream chunk in an environment with a global \"Buffer\" defined, which this library assumes to be Node. Please report this error.`);\n        }\n        // Browser\n        if (typeof TextDecoder !== 'undefined') {\n            if (bytes instanceof Uint8Array || bytes instanceof ArrayBuffer) {\n                this.textDecoder ?? (this.textDecoder = new TextDecoder('utf8'));\n                return this.textDecoder.decode(bytes);\n            }\n            throw new error_1.AnthropicError(`Unexpected: received non-Uint8Array/ArrayBuffer (${bytes.constructor.name}) in a web platform. Please report this error.`);\n        }\n        throw new error_1.AnthropicError(`Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.`);\n    }\n    flush() {\n        if (!this.buffer.length && !this.trailingCR) {\n            return [];\n        }\n        const lines = [this.buffer.join('')];\n        this.buffer = [];\n        this.trailingCR = false;\n        return lines;\n    }\n}\n// prettier-ignore\nLineDecoder.NEWLINE_CHARS = new Set(['\\n', '\\r']);\nLineDecoder.NEWLINE_REGEXP = /\\r\\n|[\\n\\r]/g;\n/** This is an internal helper function that's just used for testing */\nfunction _decodeChunks(chunks) {\n    const decoder = new LineDecoder();\n    const lines = [];\n    for (const chunk of chunks) {\n        lines.push(...decoder.decode(chunk));\n    }\n    return lines;\n}\nexports._decodeChunks = _decodeChunks;\nfunction partition(str, delimiter) {\n    const index = str.indexOf(delimiter);\n    if (index !== -1) {\n        return [str.substring(0, index), delimiter, str.substring(index + delimiter.length)];\n    }\n    return [str, '', ''];\n}\n/**\n * Most browsers don't yet have async iterable support for ReadableStream,\n * and Node has a very different way of reading bytes from its \"ReadableStream\".\n *\n * This polyfill was pulled from https://github.com/MattiasBuelens/web-streams-polyfill/pull/122#issuecomment-1627354490\n */\nfunction readableStreamAsyncIterable(stream) {\n    if (stream[Symbol.asyncIterator])\n        return stream;\n    const reader = stream.getReader();\n    return {\n        async next() {\n            try {\n                const result = await reader.read();\n                if (result?.done)\n                    reader.releaseLock(); // release lock when stream becomes closed\n                return result;\n            }\n            catch (e) {\n                reader.releaseLock(); // release lock when stream becomes errored\n                throw e;\n            }\n        },\n        async return() {\n            const cancelPromise = reader.cancel();\n            reader.releaseLock();\n            await cancelPromise;\n            return { done: true, value: undefined };\n        },\n        [Symbol.asyncIterator]() {\n            return this;\n        },\n    };\n}\nexports.readableStreamAsyncIterable = readableStreamAsyncIterable;\n//# sourceMappingURL=streaming.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/streaming.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/uploads.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/uploads.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.createForm = exports.multipartFormRequestOptions = exports.maybeMultipartFormRequestOptions = exports.isMultipartBody = exports.toFile = exports.isUploadable = exports.isBlobLike = exports.isFileLike = exports.isResponseLike = exports.fileFromPath = void 0;\nconst index_1 = __webpack_require__(/*! ./_shims/index.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/index.js\");\nvar index_2 = __webpack_require__(/*! ./_shims/index.js */ \"(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/_shims/index.js\");\nObject.defineProperty(exports, \"fileFromPath\", ({ enumerable: true, get: function () { return index_2.fileFromPath; } }));\nconst isResponseLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.url === 'string' &&\n    typeof value.blob === 'function';\nexports.isResponseLike = isResponseLike;\nconst isFileLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.name === 'string' &&\n    typeof value.lastModified === 'number' &&\n    (0, exports.isBlobLike)(value);\nexports.isFileLike = isFileLike;\n/**\n * The BlobLike type omits arrayBuffer() because @types/node-fetch@^2.6.4 lacks it; but this check\n * adds the arrayBuffer() method type because it is available and used at runtime\n */\nconst isBlobLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.size === 'number' &&\n    typeof value.type === 'string' &&\n    typeof value.text === 'function' &&\n    typeof value.slice === 'function' &&\n    typeof value.arrayBuffer === 'function';\nexports.isBlobLike = isBlobLike;\nconst isUploadable = (value) => {\n    return (0, exports.isFileLike)(value) || (0, exports.isResponseLike)(value) || (0, index_1.isFsReadStream)(value);\n};\nexports.isUploadable = isUploadable;\n/**\n * Helper for creating a {@link File} to pass to an SDK upload method from a variety of different data formats\n * @param value the raw content of the file.  Can be an {@link Uploadable}, {@link BlobLikePart}, or {@link AsyncIterable} of {@link BlobLikePart}s\n * @param {string=} name the name of the file. If omitted, toFile will try to determine a file name from bits if possible\n * @param {Object=} options additional properties\n * @param {string=} options.type the MIME type of the content\n * @param {number=} options.lastModified the last modified timestamp\n * @returns a {@link File} with the given properties\n */\nasync function toFile(value, name, options) {\n    // If it's a promise, resolve it.\n    value = await value;\n    // If we've been given a `File` we don't need to do anything\n    if ((0, exports.isFileLike)(value)) {\n        return value;\n    }\n    if ((0, exports.isResponseLike)(value)) {\n        const blob = await value.blob();\n        name || (name = new URL(value.url).pathname.split(/[\\\\/]/).pop() ?? 'unknown_file');\n        // we need to convert the `Blob` into an array buffer because the `Blob` class\n        // that `node-fetch` defines is incompatible with the web standard which results\n        // in `new File` interpreting it as a string instead of binary data.\n        const data = (0, exports.isBlobLike)(blob) ? [(await blob.arrayBuffer())] : [blob];\n        return new index_1.File(data, name, options);\n    }\n    const bits = await getBytes(value);\n    name || (name = getName(value) ?? 'unknown_file');\n    if (!options?.type) {\n        const type = bits[0]?.type;\n        if (typeof type === 'string') {\n            options = { ...options, type };\n        }\n    }\n    return new index_1.File(bits, name, options);\n}\nexports.toFile = toFile;\nasync function getBytes(value) {\n    let parts = [];\n    if (typeof value === 'string' ||\n        ArrayBuffer.isView(value) || // includes Uint8Array, Buffer, etc.\n        value instanceof ArrayBuffer) {\n        parts.push(value);\n    }\n    else if ((0, exports.isBlobLike)(value)) {\n        parts.push(await value.arrayBuffer());\n    }\n    else if (isAsyncIterableIterator(value) // includes Readable, ReadableStream, etc.\n    ) {\n        for await (const chunk of value) {\n            parts.push(chunk); // TODO, consider validating?\n        }\n    }\n    else {\n        throw new Error(`Unexpected data type: ${typeof value}; constructor: ${value?.constructor\n            ?.name}; props: ${propsForError(value)}`);\n    }\n    return parts;\n}\nfunction propsForError(value) {\n    const props = Object.getOwnPropertyNames(value);\n    return `[${props.map((p) => `\"${p}\"`).join(', ')}]`;\n}\nfunction getName(value) {\n    return (getStringFromMaybeBuffer(value.name) ||\n        getStringFromMaybeBuffer(value.filename) ||\n        // For fs.ReadStream\n        getStringFromMaybeBuffer(value.path)?.split(/[\\\\/]/).pop());\n}\nconst getStringFromMaybeBuffer = (x) => {\n    if (typeof x === 'string')\n        return x;\n    if (typeof Buffer !== 'undefined' && x instanceof Buffer)\n        return String(x);\n    return undefined;\n};\nconst isAsyncIterableIterator = (value) => value != null && typeof value === 'object' && typeof value[Symbol.asyncIterator] === 'function';\nconst isMultipartBody = (body) => body && typeof body === 'object' && body.body && body[Symbol.toStringTag] === 'MultipartBody';\nexports.isMultipartBody = isMultipartBody;\n/**\n * Returns a multipart/form-data request if any part of the given request body contains a File / Blob value.\n * Otherwise returns the request as is.\n */\nconst maybeMultipartFormRequestOptions = async (opts) => {\n    if (!hasUploadableValue(opts.body))\n        return opts;\n    const form = await (0, exports.createForm)(opts.body);\n    return (0, index_1.getMultipartRequestOptions)(form, opts);\n};\nexports.maybeMultipartFormRequestOptions = maybeMultipartFormRequestOptions;\nconst multipartFormRequestOptions = async (opts) => {\n    const form = await (0, exports.createForm)(opts.body);\n    return (0, index_1.getMultipartRequestOptions)(form, opts);\n};\nexports.multipartFormRequestOptions = multipartFormRequestOptions;\nconst createForm = async (body) => {\n    const form = new index_1.FormData();\n    await Promise.all(Object.entries(body || {}).map(([key, value]) => addFormValue(form, key, value)));\n    return form;\n};\nexports.createForm = createForm;\nconst hasUploadableValue = (value) => {\n    if ((0, exports.isUploadable)(value))\n        return true;\n    if (Array.isArray(value))\n        return value.some(hasUploadableValue);\n    if (value && typeof value === 'object') {\n        for (const k in value) {\n            if (hasUploadableValue(value[k]))\n                return true;\n        }\n    }\n    return false;\n};\nconst addFormValue = async (form, key, value) => {\n    if (value === undefined)\n        return;\n    if (value == null) {\n        throw new TypeError(`Received null for \"${key}\"; to pass null in FormData, you must use the string 'null'`);\n    }\n    // TODO: make nested formats configurable\n    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n        form.append(key, String(value));\n    }\n    else if ((0, exports.isUploadable)(value)) {\n        const file = await toFile(value);\n        form.append(key, file);\n    }\n    else if (Array.isArray(value)) {\n        await Promise.all(value.map((entry) => addFormValue(form, key + '[]', entry)));\n    }\n    else if (typeof value === 'object') {\n        await Promise.all(Object.entries(value).map(([name, prop]) => addFormValue(form, `${key}[${name}]`, prop)));\n    }\n    else {\n        throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${value} instead`);\n    }\n};\n//# sourceMappingURL=uploads.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/uploads.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/version.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/version.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.VERSION = void 0;\nexports.VERSION = '0.27.3'; // x-release-please-version\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGFudGhyb3BpYy1haStzZGtAMC4yNy4zL25vZGVfbW9kdWxlcy9AYW50aHJvcGljLWFpL3Nkay92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGVBQWU7QUFDZixlQUFlLGFBQWE7QUFDNUIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYW50aHJvcGljLWFpK3Nka0AwLjI3LjMvbm9kZV9tb2R1bGVzL0BhbnRocm9waWMtYWkvc2RrL3ZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlZFUlNJT04gPSB2b2lkIDA7XG5leHBvcnRzLlZFUlNJT04gPSAnMC4yNy4zJzsgLy8geC1yZWxlYXNlLXBsZWFzZS12ZXJzaW9uXG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@anthropic-ai+sdk@0.27.3/node_modules/@anthropic-ai/sdk/version.js\n");

/***/ })

};
;