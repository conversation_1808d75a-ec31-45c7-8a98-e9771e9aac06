"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/google-p12-pem@4.0.1";
exports.ids = ["vendor-chunks/google-p12-pem@4.0.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/google-p12-pem@4.0.1/node_modules/google-p12-pem/build/src/index.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/google-p12-pem@4.0.1/node_modules/google-p12-pem/build/src/index.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2018 Google LLC\n *\n * Distributed under MIT license.\n * See file LICENSE for detail or copy at https://opensource.org/licenses/MIT\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getPem = void 0;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst forge = __webpack_require__(/*! node-forge */ \"(rsc)/./node_modules/.pnpm/node-forge@1.3.1/node_modules/node-forge/lib/index.js\");\nconst util_1 = __webpack_require__(/*! util */ \"util\");\nconst readFile = (0, util_1.promisify)(fs.readFile);\nfunction getPem(filename, callback) {\n    if (callback) {\n        getPemAsync(filename)\n            .then(pem => callback(null, pem))\n            .catch(err => callback(err, null));\n    }\n    else {\n        return getPemAsync(filename);\n    }\n}\nexports.getPem = getPem;\nfunction getPemAsync(filename) {\n    return readFile(filename, { encoding: 'base64' }).then(keyp12 => {\n        return convertToPem(keyp12);\n    });\n}\n/**\n * Converts a P12 in base64 encoding to a pem.\n * @param p12base64 String containing base64 encoded p12.\n * @returns a string containing the pem.\n */\nfunction convertToPem(p12base64) {\n    const p12Der = forge.util.decode64(p12base64);\n    const p12Asn1 = forge.asn1.fromDer(p12Der);\n    const p12 = forge.pkcs12.pkcs12FromAsn1(p12Asn1, 'notasecret');\n    const bags = p12.getBags({ friendlyName: 'privatekey' });\n    if (bags.friendlyName) {\n        const privateKey = bags.friendlyName[0].key;\n        const pem = forge.pki.privateKeyToPem(privateKey);\n        return pem.replace(/\\r\\n/g, '\\n');\n    }\n    else {\n        throw new Error('Unable to get friendly name.');\n    }\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/google-p12-pem@4.0.1/node_modules/google-p12-pem/build/src/index.js\n");

/***/ })

};
;