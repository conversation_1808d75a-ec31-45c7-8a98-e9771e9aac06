"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+util-user-agent-node@3.828.0";
exports.ids = ["vendor-chunks/@aws-sdk+util-user-agent-node@3.828.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/crt-availability.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/crt-availability.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crtAvailability: () => (/* binding */ crtAvailability)\n/* harmony export */ });\nconst crtAvailability = {\n    isCrtAvailable: false,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC11c2VyLWFnZW50LW5vZGVAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC11c2VyLWFnZW50LW5vZGUvZGlzdC1lcy9jcnQtYXZhaWxhYmlsaXR5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayt1dGlsLXVzZXItYWdlbnQtbm9kZUAzLjgyOC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay91dGlsLXVzZXItYWdlbnQtbm9kZS9kaXN0LWVzL2NydC1hdmFpbGFiaWxpdHkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGNydEF2YWlsYWJpbGl0eSA9IHtcbiAgICBpc0NydEF2YWlsYWJsZTogZmFsc2UsXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/crt-availability.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/defaultUserAgent.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/defaultUserAgent.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDefaultUserAgentProvider: () => (/* binding */ createDefaultUserAgentProvider),\n/* harmony export */   crtAvailability: () => (/* reexport safe */ _crt_availability__WEBPACK_IMPORTED_MODULE_3__.crtAvailability),\n/* harmony export */   defaultUserAgent: () => (/* binding */ defaultUserAgent)\n/* harmony export */ });\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! os */ \"os\");\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(os__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var process__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! process */ \"process\");\n/* harmony import */ var process__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(process__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _is_crt_available__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is-crt-available */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/is-crt-available.js\");\n/* harmony import */ var _crt_availability__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./crt-availability */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/crt-availability.js\");\n\n\n\n\nconst createDefaultUserAgentProvider = ({ serviceId, clientVersion }) => {\n    return async (config) => {\n        const sections = [\n            [\"aws-sdk-js\", clientVersion],\n            [\"ua\", \"2.1\"],\n            [`os/${(0,os__WEBPACK_IMPORTED_MODULE_0__.platform)()}`, (0,os__WEBPACK_IMPORTED_MODULE_0__.release)()],\n            [\"lang/js\"],\n            [\"md/nodejs\", `${process__WEBPACK_IMPORTED_MODULE_1__.versions.node}`],\n        ];\n        const crtAvailable = (0,_is_crt_available__WEBPACK_IMPORTED_MODULE_2__.isCrtAvailable)();\n        if (crtAvailable) {\n            sections.push(crtAvailable);\n        }\n        if (serviceId) {\n            sections.push([`api/${serviceId}`, clientVersion]);\n        }\n        if (process__WEBPACK_IMPORTED_MODULE_1__.env.AWS_EXECUTION_ENV) {\n            sections.push([`exec-env/${process__WEBPACK_IMPORTED_MODULE_1__.env.AWS_EXECUTION_ENV}`]);\n        }\n        const appId = await config?.userAgentAppId?.();\n        const resolvedUserAgent = appId ? [...sections, [`app/${appId}`]] : [...sections];\n        return resolvedUserAgent;\n    };\n};\nconst defaultUserAgent = createDefaultUserAgentProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/defaultUserAgent.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/index.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/index.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NODE_APP_ID_CONFIG_OPTIONS: () => (/* reexport safe */ _nodeAppIdConfigOptions__WEBPACK_IMPORTED_MODULE_1__.NODE_APP_ID_CONFIG_OPTIONS),\n/* harmony export */   UA_APP_ID_ENV_NAME: () => (/* reexport safe */ _nodeAppIdConfigOptions__WEBPACK_IMPORTED_MODULE_1__.UA_APP_ID_ENV_NAME),\n/* harmony export */   UA_APP_ID_INI_NAME: () => (/* reexport safe */ _nodeAppIdConfigOptions__WEBPACK_IMPORTED_MODULE_1__.UA_APP_ID_INI_NAME),\n/* harmony export */   createDefaultUserAgentProvider: () => (/* reexport safe */ _defaultUserAgent__WEBPACK_IMPORTED_MODULE_0__.createDefaultUserAgentProvider),\n/* harmony export */   crtAvailability: () => (/* reexport safe */ _defaultUserAgent__WEBPACK_IMPORTED_MODULE_0__.crtAvailability),\n/* harmony export */   defaultUserAgent: () => (/* reexport safe */ _defaultUserAgent__WEBPACK_IMPORTED_MODULE_0__.defaultUserAgent)\n/* harmony export */ });\n/* harmony import */ var _defaultUserAgent__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultUserAgent */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/defaultUserAgent.js\");\n/* harmony import */ var _nodeAppIdConfigOptions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./nodeAppIdConfigOptions */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/nodeAppIdConfigOptions.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC11c2VyLWFnZW50LW5vZGVAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC11c2VyLWFnZW50LW5vZGUvZGlzdC1lcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFtQztBQUNNIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC11c2VyLWFnZW50LW5vZGVAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC11c2VyLWFnZW50LW5vZGUvZGlzdC1lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9kZWZhdWx0VXNlckFnZW50XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9ub2RlQXBwSWRDb25maWdPcHRpb25zXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/is-crt-available.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/is-crt-available.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isCrtAvailable: () => (/* binding */ isCrtAvailable)\n/* harmony export */ });\n/* harmony import */ var _crt_availability__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./crt-availability */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/crt-availability.js\");\n\nconst isCrtAvailable = () => {\n    if (_crt_availability__WEBPACK_IMPORTED_MODULE_0__.crtAvailability.isCrtAvailable) {\n        return [\"md/crt-avail\"];\n    }\n    return null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC11c2VyLWFnZW50LW5vZGVAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC11c2VyLWFnZW50LW5vZGUvZGlzdC1lcy9pcy1jcnQtYXZhaWxhYmxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFEO0FBQzlDO0FBQ1AsUUFBUSw4REFBZTtBQUN2QjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3V0aWwtdXNlci1hZ2VudC1ub2RlQDMuODI4LjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3V0aWwtdXNlci1hZ2VudC1ub2RlL2Rpc3QtZXMvaXMtY3J0LWF2YWlsYWJsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcnRBdmFpbGFiaWxpdHkgfSBmcm9tIFwiLi9jcnQtYXZhaWxhYmlsaXR5XCI7XG5leHBvcnQgY29uc3QgaXNDcnRBdmFpbGFibGUgPSAoKSA9PiB7XG4gICAgaWYgKGNydEF2YWlsYWJpbGl0eS5pc0NydEF2YWlsYWJsZSkge1xuICAgICAgICByZXR1cm4gW1wibWQvY3J0LWF2YWlsXCJdO1xuICAgIH1cbiAgICByZXR1cm4gbnVsbDtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/is-crt-available.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/nodeAppIdConfigOptions.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/nodeAppIdConfigOptions.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NODE_APP_ID_CONFIG_OPTIONS: () => (/* binding */ NODE_APP_ID_CONFIG_OPTIONS),\n/* harmony export */   UA_APP_ID_ENV_NAME: () => (/* binding */ UA_APP_ID_ENV_NAME),\n/* harmony export */   UA_APP_ID_INI_NAME: () => (/* binding */ UA_APP_ID_INI_NAME)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/middleware-user-agent */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/index.js\");\n\nconst UA_APP_ID_ENV_NAME = \"AWS_SDK_UA_APP_ID\";\nconst UA_APP_ID_INI_NAME = \"sdk_ua_app_id\";\nconst UA_APP_ID_INI_NAME_DEPRECATED = \"sdk-ua-app-id\";\nconst NODE_APP_ID_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => env[UA_APP_ID_ENV_NAME],\n    configFileSelector: (profile) => profile[UA_APP_ID_INI_NAME] ?? profile[UA_APP_ID_INI_NAME_DEPRECATED],\n    default: _aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_UA_APP_ID,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrdXRpbC11c2VyLWFnZW50LW5vZGVAMy44MjguMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvdXRpbC11c2VyLWFnZW50LW5vZGUvZGlzdC1lcy9ub2RlQXBwSWRDb25maWdPcHRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBbUU7QUFDNUQ7QUFDQTtBQUNQO0FBQ087QUFDUDtBQUNBO0FBQ0EsYUFBYSw2RUFBaUI7QUFDOUIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkayt1dGlsLXVzZXItYWdlbnQtbm9kZUAzLjgyOC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay91dGlsLXVzZXItYWdlbnQtbm9kZS9kaXN0LWVzL25vZGVBcHBJZENvbmZpZ09wdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgREVGQVVMVF9VQV9BUFBfSUQgfSBmcm9tIFwiQGF3cy1zZGsvbWlkZGxld2FyZS11c2VyLWFnZW50XCI7XG5leHBvcnQgY29uc3QgVUFfQVBQX0lEX0VOVl9OQU1FID0gXCJBV1NfU0RLX1VBX0FQUF9JRFwiO1xuZXhwb3J0IGNvbnN0IFVBX0FQUF9JRF9JTklfTkFNRSA9IFwic2RrX3VhX2FwcF9pZFwiO1xuY29uc3QgVUFfQVBQX0lEX0lOSV9OQU1FX0RFUFJFQ0FURUQgPSBcInNkay11YS1hcHAtaWRcIjtcbmV4cG9ydCBjb25zdCBOT0RFX0FQUF9JRF9DT05GSUdfT1BUSU9OUyA9IHtcbiAgICBlbnZpcm9ubWVudFZhcmlhYmxlU2VsZWN0b3I6IChlbnYpID0+IGVudltVQV9BUFBfSURfRU5WX05BTUVdLFxuICAgIGNvbmZpZ0ZpbGVTZWxlY3RvcjogKHByb2ZpbGUpID0+IHByb2ZpbGVbVUFfQVBQX0lEX0lOSV9OQU1FXSA/PyBwcm9maWxlW1VBX0FQUF9JRF9JTklfTkFNRV9ERVBSRUNBVEVEXSxcbiAgICBkZWZhdWx0OiBERUZBVUxUX1VBX0FQUF9JRCxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/nodeAppIdConfigOptions.js\n");

/***/ })

};
;