"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/markdown-table@3.0.3";
exports.ids = ["vendor-chunks/markdown-table@3.0.3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/markdown-table@3.0.3/node_modules/markdown-table/index.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/markdown-table@3.0.3/node_modules/markdown-table/index.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   markdownTable: () => (/* binding */ markdownTable)\n/* harmony export */ });\n/**\n * @typedef Options\n *   Configuration (optional).\n * @property {string|null|ReadonlyArray<string|null|undefined>} [align]\n *   One style for all columns, or styles for their respective columns.\n *   Each style is either `'l'` (left), `'r'` (right), or `'c'` (center).\n *   Other values are treated as `''`, which doesn’t place the colon in the\n *   alignment row but does align left.\n *   *Only the lowercased first character is used, so `Right` is fine.*\n * @property {boolean} [padding=true]\n *   Whether to add a space of padding between delimiters and cells.\n *\n *   When `true`, there is padding:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there is no padding:\n *\n *   ```markdown\n *   |Alpha|B    |\n *   |-----|-----|\n *   |C    |Delta|\n *   ```\n * @property {boolean} [delimiterStart=true]\n *   Whether to begin each row with the delimiter.\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are starting delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no starting delimiters:\n *\n *   ```markdown\n *   Alpha | B     |\n *   ----- | ----- |\n *   C     | Delta |\n *   ```\n * @property {boolean} [delimiterEnd=true]\n *   Whether to end each row with the delimiter.\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B\n *   | ----- | -----\n *   | C     | Delta\n *   ```\n * @property {boolean} [alignDelimiters=true]\n *   Whether to align the delimiters.\n *   By default, they are aligned:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   Pass `false` to make them staggered:\n *\n *   ```markdown\n *   | Alpha | B |\n *   | - | - |\n *   | C | Delta |\n *   ```\n * @property {(value: string) => number} [stringLength]\n *   Function to detect the length of table cell content.\n *   This is used when aligning the delimiters (`|`) between table cells.\n *   Full-width characters and emoji mess up delimiter alignment when viewing\n *   the markdown source.\n *   To fix this, you can pass this function, which receives the cell content\n *   and returns its “visible” size.\n *   Note that what is and isn’t visible depends on where the text is displayed.\n *\n *   Without such a function, the following:\n *\n *   ```js\n *   markdownTable([\n *     ['Alpha', 'Bravo'],\n *     ['中文', 'Charlie'],\n *     ['👩‍❤️‍👩', 'Delta']\n *   ])\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo |\n *   | - | - |\n *   | 中文 | Charlie |\n *   | 👩‍❤️‍👩 | Delta |\n *   ```\n *\n *   With [`string-width`](https://github.com/sindresorhus/string-width):\n *\n *   ```js\n *   import stringWidth from 'string-width'\n *\n *   markdownTable(\n *     [\n *       ['Alpha', 'Bravo'],\n *       ['中文', 'Charlie'],\n *       ['👩‍❤️‍👩', 'Delta']\n *     ],\n *     {stringLength: stringWidth}\n *   )\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo   |\n *   | ----- | ------- |\n *   | 中文  | Charlie |\n *   | 👩‍❤️‍👩    | Delta   |\n *   ```\n */\n\n/**\n * @typedef {Options} MarkdownTableOptions\n * @todo\n *   Remove next major.\n */\n\n/**\n * Generate a markdown ([GFM](https://docs.github.com/en/github/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables)) table..\n *\n * @param {ReadonlyArray<ReadonlyArray<string|null|undefined>>} table\n *   Table data (matrix of strings).\n * @param {Options} [options]\n *   Configuration (optional).\n * @returns {string}\n */\nfunction markdownTable(table, options = {}) {\n  const align = (options.align || []).concat()\n  const stringLength = options.stringLength || defaultStringLength\n  /** @type {Array<number>} Character codes as symbols for alignment per column. */\n  const alignments = []\n  /** @type {Array<Array<string>>} Cells per row. */\n  const cellMatrix = []\n  /** @type {Array<Array<number>>} Sizes of each cell per row. */\n  const sizeMatrix = []\n  /** @type {Array<number>} */\n  const longestCellByColumn = []\n  let mostCellsPerRow = 0\n  let rowIndex = -1\n\n  // This is a superfluous loop if we don’t align delimiters, but otherwise we’d\n  // do superfluous work when aligning, so optimize for aligning.\n  while (++rowIndex < table.length) {\n    /** @type {Array<string>} */\n    const row = []\n    /** @type {Array<number>} */\n    const sizes = []\n    let columnIndex = -1\n\n    if (table[rowIndex].length > mostCellsPerRow) {\n      mostCellsPerRow = table[rowIndex].length\n    }\n\n    while (++columnIndex < table[rowIndex].length) {\n      const cell = serialize(table[rowIndex][columnIndex])\n\n      if (options.alignDelimiters !== false) {\n        const size = stringLength(cell)\n        sizes[columnIndex] = size\n\n        if (\n          longestCellByColumn[columnIndex] === undefined ||\n          size > longestCellByColumn[columnIndex]\n        ) {\n          longestCellByColumn[columnIndex] = size\n        }\n      }\n\n      row.push(cell)\n    }\n\n    cellMatrix[rowIndex] = row\n    sizeMatrix[rowIndex] = sizes\n  }\n\n  // Figure out which alignments to use.\n  let columnIndex = -1\n\n  if (typeof align === 'object' && 'length' in align) {\n    while (++columnIndex < mostCellsPerRow) {\n      alignments[columnIndex] = toAlignment(align[columnIndex])\n    }\n  } else {\n    const code = toAlignment(align)\n\n    while (++columnIndex < mostCellsPerRow) {\n      alignments[columnIndex] = code\n    }\n  }\n\n  // Inject the alignment row.\n  columnIndex = -1\n  /** @type {Array<string>} */\n  const row = []\n  /** @type {Array<number>} */\n  const sizes = []\n\n  while (++columnIndex < mostCellsPerRow) {\n    const code = alignments[columnIndex]\n    let before = ''\n    let after = ''\n\n    if (code === 99 /* `c` */) {\n      before = ':'\n      after = ':'\n    } else if (code === 108 /* `l` */) {\n      before = ':'\n    } else if (code === 114 /* `r` */) {\n      after = ':'\n    }\n\n    // There *must* be at least one hyphen-minus in each alignment cell.\n    let size =\n      options.alignDelimiters === false\n        ? 1\n        : Math.max(\n            1,\n            longestCellByColumn[columnIndex] - before.length - after.length\n          )\n\n    const cell = before + '-'.repeat(size) + after\n\n    if (options.alignDelimiters !== false) {\n      size = before.length + size + after.length\n\n      if (size > longestCellByColumn[columnIndex]) {\n        longestCellByColumn[columnIndex] = size\n      }\n\n      sizes[columnIndex] = size\n    }\n\n    row[columnIndex] = cell\n  }\n\n  // Inject the alignment row.\n  cellMatrix.splice(1, 0, row)\n  sizeMatrix.splice(1, 0, sizes)\n\n  rowIndex = -1\n  /** @type {Array<string>} */\n  const lines = []\n\n  while (++rowIndex < cellMatrix.length) {\n    const row = cellMatrix[rowIndex]\n    const sizes = sizeMatrix[rowIndex]\n    columnIndex = -1\n    /** @type {Array<string>} */\n    const line = []\n\n    while (++columnIndex < mostCellsPerRow) {\n      const cell = row[columnIndex] || ''\n      let before = ''\n      let after = ''\n\n      if (options.alignDelimiters !== false) {\n        const size =\n          longestCellByColumn[columnIndex] - (sizes[columnIndex] || 0)\n        const code = alignments[columnIndex]\n\n        if (code === 114 /* `r` */) {\n          before = ' '.repeat(size)\n        } else if (code === 99 /* `c` */) {\n          if (size % 2) {\n            before = ' '.repeat(size / 2 + 0.5)\n            after = ' '.repeat(size / 2 - 0.5)\n          } else {\n            before = ' '.repeat(size / 2)\n            after = before\n          }\n        } else {\n          after = ' '.repeat(size)\n        }\n      }\n\n      if (options.delimiterStart !== false && !columnIndex) {\n        line.push('|')\n      }\n\n      if (\n        options.padding !== false &&\n        // Don’t add the opening space if we’re not aligning and the cell is\n        // empty: there will be a closing space.\n        !(options.alignDelimiters === false && cell === '') &&\n        (options.delimiterStart !== false || columnIndex)\n      ) {\n        line.push(' ')\n      }\n\n      if (options.alignDelimiters !== false) {\n        line.push(before)\n      }\n\n      line.push(cell)\n\n      if (options.alignDelimiters !== false) {\n        line.push(after)\n      }\n\n      if (options.padding !== false) {\n        line.push(' ')\n      }\n\n      if (\n        options.delimiterEnd !== false ||\n        columnIndex !== mostCellsPerRow - 1\n      ) {\n        line.push('|')\n      }\n    }\n\n    lines.push(\n      options.delimiterEnd === false\n        ? line.join('').replace(/ +$/, '')\n        : line.join('')\n    )\n  }\n\n  return lines.join('\\n')\n}\n\n/**\n * @param {string|null|undefined} [value]\n * @returns {string}\n */\nfunction serialize(value) {\n  return value === null || value === undefined ? '' : String(value)\n}\n\n/**\n * @param {string} value\n * @returns {number}\n */\nfunction defaultStringLength(value) {\n  return value.length\n}\n\n/**\n * @param {string|null|undefined} value\n * @returns {number}\n */\nfunction toAlignment(value) {\n  const code = typeof value === 'string' ? value.codePointAt(0) : 0\n\n  return code === 67 /* `C` */ || code === 99 /* `c` */\n    ? 99 /* `c` */\n    : code === 76 /* `L` */ || code === 108 /* `l` */\n    ? 108 /* `l` */\n    : code === 82 /* `R` */ || code === 114 /* `r` */\n    ? 114 /* `r` */\n    : 0\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/markdown-table@3.0.3/node_modules/markdown-table/index.js\n");

/***/ })

};
;