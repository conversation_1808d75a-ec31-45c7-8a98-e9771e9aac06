"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+region-config-resolver@3.821.0";
exports.ids = ["vendor-chunks/@aws-sdk+region-config-resolver@3.821.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/extensions/index.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/extensions/index.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAwsRegionExtensionConfiguration: () => (/* binding */ getAwsRegionExtensionConfiguration),\n/* harmony export */   resolveAwsRegionExtensionConfiguration: () => (/* binding */ resolveAwsRegionExtensionConfiguration)\n/* harmony export */ });\nconst getAwsRegionExtensionConfiguration = (runtimeConfig) => {\n    return {\n        setRegion(region) {\n            runtimeConfig.region = region;\n        },\n        region() {\n            return runtimeConfig.region;\n        },\n    };\n};\nconst resolveAwsRegionExtensionConfiguration = (awsRegionExtensionConfiguration) => {\n    return {\n        region: awsRegionExtensionConfiguration.region(),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcmVnaW9uLWNvbmZpZy1yZXNvbHZlckAzLjgyMS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9yZWdpb24tY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvZXh0ZW5zaW9ucy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytyZWdpb24tY29uZmlnLXJlc29sdmVyQDMuODIxLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3JlZ2lvbi1jb25maWctcmVzb2x2ZXIvZGlzdC1lcy9leHRlbnNpb25zL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBnZXRBd3NSZWdpb25FeHRlbnNpb25Db25maWd1cmF0aW9uID0gKHJ1bnRpbWVDb25maWcpID0+IHtcbiAgICByZXR1cm4ge1xuICAgICAgICBzZXRSZWdpb24ocmVnaW9uKSB7XG4gICAgICAgICAgICBydW50aW1lQ29uZmlnLnJlZ2lvbiA9IHJlZ2lvbjtcbiAgICAgICAgfSxcbiAgICAgICAgcmVnaW9uKCkge1xuICAgICAgICAgICAgcmV0dXJuIHJ1bnRpbWVDb25maWcucmVnaW9uO1xuICAgICAgICB9LFxuICAgIH07XG59O1xuZXhwb3J0IGNvbnN0IHJlc29sdmVBd3NSZWdpb25FeHRlbnNpb25Db25maWd1cmF0aW9uID0gKGF3c1JlZ2lvbkV4dGVuc2lvbkNvbmZpZ3VyYXRpb24pID0+IHtcbiAgICByZXR1cm4ge1xuICAgICAgICByZWdpb246IGF3c1JlZ2lvbkV4dGVuc2lvbkNvbmZpZ3VyYXRpb24ucmVnaW9uKCksXG4gICAgfTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/extensions/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/index.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/index.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NODE_REGION_CONFIG_FILE_OPTIONS: () => (/* reexport safe */ _regionConfig__WEBPACK_IMPORTED_MODULE_1__.NODE_REGION_CONFIG_FILE_OPTIONS),\n/* harmony export */   NODE_REGION_CONFIG_OPTIONS: () => (/* reexport safe */ _regionConfig__WEBPACK_IMPORTED_MODULE_1__.NODE_REGION_CONFIG_OPTIONS),\n/* harmony export */   REGION_ENV_NAME: () => (/* reexport safe */ _regionConfig__WEBPACK_IMPORTED_MODULE_1__.REGION_ENV_NAME),\n/* harmony export */   REGION_INI_NAME: () => (/* reexport safe */ _regionConfig__WEBPACK_IMPORTED_MODULE_1__.REGION_INI_NAME),\n/* harmony export */   getAwsRegionExtensionConfiguration: () => (/* reexport safe */ _extensions__WEBPACK_IMPORTED_MODULE_0__.getAwsRegionExtensionConfiguration),\n/* harmony export */   resolveAwsRegionExtensionConfiguration: () => (/* reexport safe */ _extensions__WEBPACK_IMPORTED_MODULE_0__.resolveAwsRegionExtensionConfiguration),\n/* harmony export */   resolveRegionConfig: () => (/* reexport safe */ _regionConfig__WEBPACK_IMPORTED_MODULE_1__.resolveRegionConfig)\n/* harmony export */ });\n/* harmony import */ var _extensions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./extensions */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/extensions/index.js\");\n/* harmony import */ var _regionConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./regionConfig */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/index.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcmVnaW9uLWNvbmZpZy1yZXNvbHZlckAzLjgyMS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9yZWdpb24tY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQTZCO0FBQ0UiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytyZWdpb24tY29uZmlnLXJlc29sdmVyQDMuODIxLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3JlZ2lvbi1jb25maWctcmVzb2x2ZXIvZGlzdC1lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9leHRlbnNpb25zXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9yZWdpb25Db25maWdcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/config.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/config.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NODE_REGION_CONFIG_FILE_OPTIONS: () => (/* binding */ NODE_REGION_CONFIG_FILE_OPTIONS),\n/* harmony export */   NODE_REGION_CONFIG_OPTIONS: () => (/* binding */ NODE_REGION_CONFIG_OPTIONS),\n/* harmony export */   REGION_ENV_NAME: () => (/* binding */ REGION_ENV_NAME),\n/* harmony export */   REGION_INI_NAME: () => (/* binding */ REGION_INI_NAME)\n/* harmony export */ });\nconst REGION_ENV_NAME = \"AWS_REGION\";\nconst REGION_INI_NAME = \"region\";\nconst NODE_REGION_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => env[REGION_ENV_NAME],\n    configFileSelector: (profile) => profile[REGION_INI_NAME],\n    default: () => {\n        throw new Error(\"Region is missing\");\n    },\n};\nconst NODE_REGION_CONFIG_FILE_OPTIONS = {\n    preferredFile: \"credentials\",\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcmVnaW9uLWNvbmZpZy1yZXNvbHZlckAzLjgyMS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9yZWdpb24tY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uQ29uZmlnL2NvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytyZWdpb24tY29uZmlnLXJlc29sdmVyQDMuODIxLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3JlZ2lvbi1jb25maWctcmVzb2x2ZXIvZGlzdC1lcy9yZWdpb25Db25maWcvY29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBSRUdJT05fRU5WX05BTUUgPSBcIkFXU19SRUdJT05cIjtcbmV4cG9ydCBjb25zdCBSRUdJT05fSU5JX05BTUUgPSBcInJlZ2lvblwiO1xuZXhwb3J0IGNvbnN0IE5PREVfUkVHSU9OX0NPTkZJR19PUFRJT05TID0ge1xuICAgIGVudmlyb25tZW50VmFyaWFibGVTZWxlY3RvcjogKGVudikgPT4gZW52W1JFR0lPTl9FTlZfTkFNRV0sXG4gICAgY29uZmlnRmlsZVNlbGVjdG9yOiAocHJvZmlsZSkgPT4gcHJvZmlsZVtSRUdJT05fSU5JX05BTUVdLFxuICAgIGRlZmF1bHQ6ICgpID0+IHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiUmVnaW9uIGlzIG1pc3NpbmdcIik7XG4gICAgfSxcbn07XG5leHBvcnQgY29uc3QgTk9ERV9SRUdJT05fQ09ORklHX0ZJTEVfT1BUSU9OUyA9IHtcbiAgICBwcmVmZXJyZWRGaWxlOiBcImNyZWRlbnRpYWxzXCIsXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/config.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/getRealRegion.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/getRealRegion.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRealRegion: () => (/* binding */ getRealRegion)\n/* harmony export */ });\n/* harmony import */ var _isFipsRegion__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isFipsRegion */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/isFipsRegion.js\");\n\nconst getRealRegion = (region) => (0,_isFipsRegion__WEBPACK_IMPORTED_MODULE_0__.isFipsRegion)(region)\n    ? [\"fips-aws-global\", \"aws-fips\"].includes(region)\n        ? \"us-east-1\"\n        : region.replace(/fips-(dkr-|prod-)?|-fips/, \"\")\n    : region;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcmVnaW9uLWNvbmZpZy1yZXNvbHZlckAzLjgyMS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9yZWdpb24tY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uQ29uZmlnL2dldFJlYWxSZWdpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEM7QUFDdkMsa0NBQWtDLDJEQUFZO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcmVnaW9uLWNvbmZpZy1yZXNvbHZlckAzLjgyMS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9yZWdpb24tY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uQ29uZmlnL2dldFJlYWxSZWdpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNGaXBzUmVnaW9uIH0gZnJvbSBcIi4vaXNGaXBzUmVnaW9uXCI7XG5leHBvcnQgY29uc3QgZ2V0UmVhbFJlZ2lvbiA9IChyZWdpb24pID0+IGlzRmlwc1JlZ2lvbihyZWdpb24pXG4gICAgPyBbXCJmaXBzLWF3cy1nbG9iYWxcIiwgXCJhd3MtZmlwc1wiXS5pbmNsdWRlcyhyZWdpb24pXG4gICAgICAgID8gXCJ1cy1lYXN0LTFcIlxuICAgICAgICA6IHJlZ2lvbi5yZXBsYWNlKC9maXBzLShka3ItfHByb2QtKT98LWZpcHMvLCBcIlwiKVxuICAgIDogcmVnaW9uO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/getRealRegion.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/index.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/index.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NODE_REGION_CONFIG_FILE_OPTIONS: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_0__.NODE_REGION_CONFIG_FILE_OPTIONS),\n/* harmony export */   NODE_REGION_CONFIG_OPTIONS: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_0__.NODE_REGION_CONFIG_OPTIONS),\n/* harmony export */   REGION_ENV_NAME: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_0__.REGION_ENV_NAME),\n/* harmony export */   REGION_INI_NAME: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_0__.REGION_INI_NAME),\n/* harmony export */   resolveRegionConfig: () => (/* reexport safe */ _resolveRegionConfig__WEBPACK_IMPORTED_MODULE_1__.resolveRegionConfig)\n/* harmony export */ });\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/config.js\");\n/* harmony import */ var _resolveRegionConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./resolveRegionConfig */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/resolveRegionConfig.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcmVnaW9uLWNvbmZpZy1yZXNvbHZlckAzLjgyMS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9yZWdpb24tY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uQ29uZmlnL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBeUI7QUFDYSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK3JlZ2lvbi1jb25maWctcmVzb2x2ZXJAMy44MjEuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvcmVnaW9uLWNvbmZpZy1yZXNvbHZlci9kaXN0LWVzL3JlZ2lvbkNvbmZpZy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9jb25maWdcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3Jlc29sdmVSZWdpb25Db25maWdcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/isFipsRegion.js":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/isFipsRegion.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isFipsRegion: () => (/* binding */ isFipsRegion)\n/* harmony export */ });\nconst isFipsRegion = (region) => typeof region === \"string\" && (region.startsWith(\"fips-\") || region.endsWith(\"-fips\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrcmVnaW9uLWNvbmZpZy1yZXNvbHZlckAzLjgyMS4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9yZWdpb24tY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uQ29uZmlnL2lzRmlwc1JlZ2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8iLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytyZWdpb24tY29uZmlnLXJlc29sdmVyQDMuODIxLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL3JlZ2lvbi1jb25maWctcmVzb2x2ZXIvZGlzdC1lcy9yZWdpb25Db25maWcvaXNGaXBzUmVnaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBpc0ZpcHNSZWdpb24gPSAocmVnaW9uKSA9PiB0eXBlb2YgcmVnaW9uID09PSBcInN0cmluZ1wiICYmIChyZWdpb24uc3RhcnRzV2l0aChcImZpcHMtXCIpIHx8IHJlZ2lvbi5lbmRzV2l0aChcIi1maXBzXCIpKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/isFipsRegion.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/resolveRegionConfig.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/resolveRegionConfig.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveRegionConfig: () => (/* binding */ resolveRegionConfig)\n/* harmony export */ });\n/* harmony import */ var _getRealRegion__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getRealRegion */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/getRealRegion.js\");\n/* harmony import */ var _isFipsRegion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./isFipsRegion */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/isFipsRegion.js\");\n\n\nconst resolveRegionConfig = (input) => {\n    const { region, useFipsEndpoint } = input;\n    if (!region) {\n        throw new Error(\"Region is missing\");\n    }\n    return Object.assign(input, {\n        region: async () => {\n            if (typeof region === \"string\") {\n                return (0,_getRealRegion__WEBPACK_IMPORTED_MODULE_0__.getRealRegion)(region);\n            }\n            const providedRegion = await region();\n            return (0,_getRealRegion__WEBPACK_IMPORTED_MODULE_0__.getRealRegion)(providedRegion);\n        },\n        useFipsEndpoint: async () => {\n            const providedRegion = typeof region === \"string\" ? region : await region();\n            if ((0,_isFipsRegion__WEBPACK_IMPORTED_MODULE_1__.isFipsRegion)(providedRegion)) {\n                return true;\n            }\n            return typeof useFipsEndpoint !== \"function\" ? Promise.resolve(!!useFipsEndpoint) : useFipsEndpoint();\n        },\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/regionConfig/resolveRegionConfig.js\n");

/***/ })

};
;