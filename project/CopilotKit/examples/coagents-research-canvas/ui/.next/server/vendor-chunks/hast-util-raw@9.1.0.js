"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-raw@9.1.0";
exports.ids = ["vendor-chunks/hast-util-raw@9.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/hast-util-raw@9.1.0/node_modules/hast-util-raw/lib/index.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/hast-util-raw@9.1.0/node_modules/hast-util-raw/lib/index.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   raw: () => (/* binding */ raw)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-from-parse5 */ \"(ssr)/./node_modules/.pnpm/hast-util-from-parse5@8.0.3/node_modules/hast-util-from-parse5/lib/index.js\");\n/* harmony import */ var hast_util_to_parse5__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! hast-util-to-parse5 */ \"(ssr)/./node_modules/.pnpm/hast-util-to-parse5@8.0.0/node_modules/hast-util-to-parse5/lib/index.js\");\n/* harmony import */ var html_void_elements__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! html-void-elements */ \"(ssr)/./node_modules/.pnpm/html-void-elements@3.0.0/node_modules/html-void-elements/index.js\");\n/* harmony import */ var parse5__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! parse5 */ \"(ssr)/./node_modules/.pnpm/parse5@7.3.0/node_modules/parse5/dist/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/.pnpm/unist-util-position@5.0.0/node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/.pnpm/unist-util-visit@5.0.0/node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var web_namespaces__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! web-namespaces */ \"(ssr)/./node_modules/.pnpm/web-namespaces@2.0.1/node_modules/web-namespaces/index.js\");\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zwitch */ \"(ssr)/./node_modules/.pnpm/zwitch@2.0.4/node_modules/zwitch/index.js\");\n/**\n * @import {Options} from 'hast-util-raw'\n * @import {Comment, Doctype, Element, Nodes, RootContent, Root, Text} from 'hast'\n * @import {Raw} from 'mdast-util-to-hast'\n * @import {DefaultTreeAdapterMap, ParserOptions} from 'parse5'\n * @import {Point} from 'unist'\n */\n\n/**\n * @typedef State\n *   Info passed around about the current state.\n * @property {(node: Nodes) => undefined} handle\n *   Add a hast node to the parser.\n * @property {Options} options\n *   User configuration.\n * @property {Parser<DefaultTreeAdapterMap>} parser\n *   Current parser.\n * @property {boolean} stitches\n *   Whether there are stitches.\n */\n\n/**\n * @typedef Stitch\n *   Custom comment-like value we pass through parse5, which contains a\n *   replacement node that we’ll swap back in afterwards.\n * @property {'comment'} type\n *   Node type.\n * @property {{stitch: Nodes}} value\n *   Replacement value.\n */\n\n\n\n\n\n\n\n\n\n\n\nconst gfmTagfilterExpression =\n  /<(\\/?)(iframe|noembed|noframes|plaintext|script|style|textarea|title|xmp)(?=[\\t\\n\\f\\r />])/gi\n\n// Node types associated with MDX.\n// <https://github.com/mdx-js/mdx/blob/8a56312/packages/mdx/lib/node-types.js>\nconst knownMdxNames = new Set([\n  'mdxFlowExpression',\n  'mdxJsxFlowElement',\n  'mdxJsxTextElement',\n  'mdxTextExpression',\n  'mdxjsEsm'\n])\n\n/** @type {ParserOptions<DefaultTreeAdapterMap>} */\nconst parseOptions = {sourceCodeLocationInfo: true, scriptingEnabled: false}\n\n/**\n * Pass a hast tree through an HTML parser, which will fix nesting, and turn\n * raw nodes into actual nodes.\n *\n * @param {Nodes} tree\n *   Original hast tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Nodes}\n *   Parsed again tree.\n */\nfunction raw(tree, options) {\n  const document = documentMode(tree)\n  /** @type {(node: Nodes, state: State) => undefined} */\n  const one = (0,zwitch__WEBPACK_IMPORTED_MODULE_1__.zwitch)('type', {\n    handlers: {root, element, text, comment, doctype, raw: handleRaw},\n    unknown\n  })\n\n  /** @type {State} */\n  const state = {\n    parser: document\n      ? new parse5__WEBPACK_IMPORTED_MODULE_0__.Parser(parseOptions)\n      : parse5__WEBPACK_IMPORTED_MODULE_0__.Parser.getFragmentParser(undefined, parseOptions),\n    handle(node) {\n      one(node, state)\n    },\n    stitches: false,\n    options: options || {}\n  }\n\n  one(tree, state)\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)())\n\n  const p5 = document ? state.parser.document : state.parser.getFragment()\n  const result = (0,hast_util_from_parse5__WEBPACK_IMPORTED_MODULE_3__.fromParse5)(p5, {\n    // To do: support `space`?\n    file: state.options.file\n  })\n\n  if (state.stitches) {\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_4__.visit)(result, 'comment', function (node, index, parent) {\n      const stitch = /** @type {Stitch} */ (/** @type {unknown} */ (node))\n      if (stitch.value.stitch && parent && index !== undefined) {\n        /** @type {Array<RootContent>} */\n        const siblings = parent.children\n        // @ts-expect-error: assume the stitch is allowed.\n        siblings[index] = stitch.value.stitch\n        return index\n      }\n    })\n  }\n\n  // Unpack if possible and when not given a `root`.\n  if (\n    result.type === 'root' &&\n    result.children.length === 1 &&\n    result.children[0].type === tree.type\n  ) {\n    return result.children[0]\n  }\n\n  return result\n}\n\n/**\n * Transform all nodes\n *\n * @param {Array<RootContent>} nodes\n *   hast content.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction all(nodes, state) {\n  let index = -1\n\n  /* istanbul ignore else - invalid nodes, see rehypejs/rehype-raw#7. */\n  if (nodes) {\n    while (++index < nodes.length) {\n      state.handle(nodes[index])\n    }\n  }\n}\n\n/**\n * Transform a root.\n *\n * @param {Root} node\n *   hast root node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction root(node, state) {\n  all(node.children, state)\n}\n\n/**\n * Transform an element.\n *\n * @param {Element} node\n *   hast element node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction element(node, state) {\n  startTag(node, state)\n\n  all(node.children, state)\n\n  endTag(node, state)\n}\n\n/**\n * Transform a text.\n *\n * @param {Text} node\n *   hast text node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction text(node, state) {\n  // Allow `DATA` through `PLAINTEXT`,\n  // but when hanging in a tag for example,\n  // switch back to `DATA`.\n  // Note: `State` is not exposed by `parse5`, so these numbers are fragile.\n  // See: <https://github.com/inikulin/parse5/blob/46cba43/packages/parse5/lib/tokenizer/index.ts#L58>\n  if (state.parser.tokenizer.state > 4) {\n    state.parser.tokenizer.state = 0\n  }\n\n  /** @type {Token.CharacterToken} */\n  const token = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.CHARACTER,\n    chars: node.value,\n    location: createParse5Location(node)\n  }\n\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n  // @ts-expect-error: private.\n  state.parser.currentToken = token\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n}\n\n/**\n * Transform a doctype.\n *\n * @param {Doctype} node\n *   hast doctype node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction doctype(node, state) {\n  /** @type {Token.DoctypeToken} */\n  const token = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.DOCTYPE,\n    name: 'html',\n    forceQuirks: false,\n    publicId: '',\n    systemId: '',\n    location: createParse5Location(node)\n  }\n\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n  // @ts-expect-error: private.\n  state.parser.currentToken = token\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n}\n\n/**\n * Transform a stitch.\n *\n * @param {Nodes} node\n *   unknown node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction stitch(node, state) {\n  // Mark that there are stitches, so we need to walk the tree and revert them.\n  state.stitches = true\n\n  /** @type {Nodes} */\n  const clone = cloneWithoutChildren(node)\n\n  // Recurse, because to somewhat handle `[<x>]</x>` (where `[]` denotes the\n  // passed through node).\n  if ('children' in node && 'children' in clone) {\n    // Root in root out.\n    const fakeRoot = /** @type {Root} */ (\n      raw({type: 'root', children: node.children}, state.options)\n    )\n    clone.children = fakeRoot.children\n  }\n\n  // Hack: `value` is supposed to be a string, but as none of the tools\n  // (`parse5` or `hast-util-from-parse5`) looks at it, we can pass nodes\n  // through.\n  comment({type: 'comment', value: {stitch: clone}}, state)\n}\n\n/**\n * Transform a comment (or stitch).\n *\n * @param {Comment | Stitch} node\n *   hast comment node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction comment(node, state) {\n  /** @type {string} */\n  // @ts-expect-error: we pass stitches through.\n  const data = node.value\n\n  /** @type {Token.CommentToken} */\n  const token = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.COMMENT,\n    data,\n    location: createParse5Location(node)\n  }\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n  // @ts-expect-error: private.\n  state.parser.currentToken = token\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n}\n\n/**\n * Transform a raw node.\n *\n * @param {Raw} node\n *   hast raw node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction handleRaw(node, state) {\n  // Reset preprocessor:\n  // See: <https://github.com/inikulin/parse5/blob/6f7ca60/packages/parse5/lib/tokenizer/preprocessor.ts#L18-L31>.\n  state.parser.tokenizer.preprocessor.html = ''\n  state.parser.tokenizer.preprocessor.pos = -1\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.lastGapPos = -2\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.gapStack = []\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.skipNextNewLine = false\n  state.parser.tokenizer.preprocessor.lastChunkWritten = false\n  state.parser.tokenizer.preprocessor.endOfChunkHit = false\n  // @ts-expect-error: private.\n  // type-coverage:ignore-next-line\n  state.parser.tokenizer.preprocessor.isEol = false\n\n  // Now pass `node.value`.\n  setPoint(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n\n  state.parser.tokenizer.write(\n    state.options.tagfilter\n      ? node.value.replace(gfmTagfilterExpression, '&lt;$1$2')\n      : node.value,\n    false\n  )\n  // @ts-expect-error: private.\n  state.parser.tokenizer._runParsingLoop()\n\n  // Character references hang, so if we ended there, we need to flush\n  // those too.\n  // We reset the preprocessor as if the document ends here.\n  // Then one single call to the relevant state does the trick, parse5\n  // consumes the whole token.\n\n  // Note: `State` is not exposed by `parse5`, so these numbers are fragile.\n  // See: <https://github.com/inikulin/parse5/blob/46cba43/packages/parse5/lib/tokenizer/index.ts#L58>\n  // Note: a change to `parse5`, which breaks this, was merged but not released.\n  // Investigate when it is.\n  // To do: remove next major.\n  /* c8 ignore next 12 -- removed in <https://github.com/inikulin/parse5/pull/897> */\n  if (\n    state.parser.tokenizer.state === 72 /* NAMED_CHARACTER_REFERENCE */ ||\n    // @ts-expect-error: removed.\n    state.parser.tokenizer.state === 78 /* NUMERIC_CHARACTER_REFERENCE_END */\n  ) {\n    state.parser.tokenizer.preprocessor.lastChunkWritten = true\n    /** @type {number} */\n    // @ts-expect-error: private.\n    const cp = state.parser.tokenizer._consume()\n    // @ts-expect-error: private.\n    state.parser.tokenizer._callState(cp)\n  }\n}\n\n/**\n * Crash on an unknown node.\n *\n * @param {unknown} node_\n *   unknown node.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Never.\n */\nfunction unknown(node_, state) {\n  const node = /** @type {Nodes} */ (node_)\n\n  if (\n    state.options.passThrough &&\n    state.options.passThrough.includes(node.type)\n  ) {\n    stitch(node, state)\n  } else {\n    let extra = ''\n\n    if (knownMdxNames.has(node.type)) {\n      extra =\n        \". It looks like you are using MDX nodes with `hast-util-raw` (or `rehype-raw`). If you use this because you are using remark or rehype plugins that inject `'html'` nodes, then please raise an issue with that plugin, as its a bad and slow idea. If you use this because you are using markdown syntax, then you have to configure this utility (or plugin) to pass through these nodes (see `passThrough` in docs), but you can also migrate to use the MDX syntax\"\n    }\n\n    throw new Error('Cannot compile `' + node.type + '` node' + extra)\n  }\n}\n\n/**\n * Reset the tokenizer of a parser.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Point | undefined} point\n *   Point.\n * @returns {undefined}\n *   Nothing.\n */\nfunction resetTokenizer(state, point) {\n  setPoint(state, point)\n\n  // Process final characters if they’re still there after hibernating.\n  /** @type {Token.CharacterToken} */\n  // @ts-expect-error: private.\n  const token = state.parser.tokenizer.currentCharacterToken\n\n  if (token && token.location) {\n    token.location.endLine = state.parser.tokenizer.preprocessor.line\n    token.location.endCol = state.parser.tokenizer.preprocessor.col + 1\n    token.location.endOffset = state.parser.tokenizer.preprocessor.offset + 1\n    // @ts-expect-error: private.\n    state.parser.currentToken = token\n    // @ts-expect-error: private.\n    state.parser._processToken(state.parser.currentToken)\n  }\n\n  // Reset tokenizer:\n  // See: <https://github.com/inikulin/parse5/blob/6f7ca60/packages/parse5/lib/tokenizer/index.ts#L187-L223>.\n  // Especially putting it back in the `data` state is useful: some elements,\n  // like textareas and iframes, change the state.\n  // See GH-7.\n  // But also if broken HTML is in `raw`, and then a correct element is given.\n  // See GH-11.\n  // @ts-expect-error: private.\n  state.parser.tokenizer.paused = false\n  // @ts-expect-error: private.\n  state.parser.tokenizer.inLoop = false\n\n  // Note: don’t reset `state`, `inForeignNode`, or `lastStartTagName`, we\n  // manually update those when needed.\n  state.parser.tokenizer.active = false\n  // @ts-expect-error: private.\n  state.parser.tokenizer.returnState = parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.DATA\n  // @ts-expect-error: private.\n  state.parser.tokenizer.charRefCode = -1\n  // @ts-expect-error: private.\n  state.parser.tokenizer.consumedAfterSnapshot = -1\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentLocation = null\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentCharacterToken = null\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentToken = null\n  // @ts-expect-error: private.\n  state.parser.tokenizer.currentAttr = {name: '', value: ''}\n}\n\n/**\n * Set current location.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Point | undefined} point\n *   Point.\n * @returns {undefined}\n *   Nothing.\n */\nfunction setPoint(state, point) {\n  if (point && point.offset !== undefined) {\n    /** @type {Token.Location} */\n    const location = {\n      startLine: point.line,\n      startCol: point.column,\n      startOffset: point.offset,\n      endLine: -1,\n      endCol: -1,\n      endOffset: -1\n    }\n\n    // @ts-expect-error: private.\n    // type-coverage:ignore-next-line\n    state.parser.tokenizer.preprocessor.lineStartPos = -point.column + 1 // Looks weird, but ensures we get correct positional info.\n    state.parser.tokenizer.preprocessor.droppedBufferSize = point.offset\n    state.parser.tokenizer.preprocessor.line = point.line\n    // @ts-expect-error: private.\n    state.parser.tokenizer.currentLocation = location\n  }\n}\n\n/**\n * Emit a start tag.\n *\n * @param {Element} node\n *   Element.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction startTag(node, state) {\n  const tagName = node.tagName.toLowerCase()\n\n  // Ignore tags if we’re in plain text.\n  if (state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.PLAINTEXT) return\n\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node))\n\n  const current = state.parser.openElements.current\n  let ns = 'namespaceURI' in current ? current.namespaceURI : web_namespaces__WEBPACK_IMPORTED_MODULE_5__.webNamespaces.html\n\n  if (ns === web_namespaces__WEBPACK_IMPORTED_MODULE_5__.webNamespaces.html && tagName === 'svg') {\n    ns = web_namespaces__WEBPACK_IMPORTED_MODULE_5__.webNamespaces.svg\n  }\n\n  const result = (0,hast_util_to_parse5__WEBPACK_IMPORTED_MODULE_6__.toParse5)(\n    // Shallow clone to not delve into `children`: we only need the attributes.\n    {...node, children: []},\n    {space: ns === web_namespaces__WEBPACK_IMPORTED_MODULE_5__.webNamespaces.svg ? 'svg' : 'html'}\n  )\n\n  /** @type {Token.TagToken} */\n  const tag = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.START_TAG,\n    tagName,\n    tagID: parse5__WEBPACK_IMPORTED_MODULE_0__.html.getTagID(tagName),\n    // We always send start and end tags.\n    selfClosing: false,\n    ackSelfClosing: false,\n    // Always element.\n    /* c8 ignore next */\n    attrs: 'attrs' in result ? result.attrs : [],\n    location: createParse5Location(node)\n  }\n\n  // The HTML parsing algorithm works by doing half of the state management in\n  // the tokenizer and half in the parser.\n  // We can’t use the tokenizer here, as we don’t have strings.\n  // So we act *as if* the tokenizer emits tokens:\n\n  // @ts-expect-error: private.\n  state.parser.currentToken = tag\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n\n  // …but then we still need a bunch of work that the tokenizer would normally\n  // do, such as:\n\n  // Set a tag name, similar to how the tokenizer would do it.\n  state.parser.tokenizer.lastStartTagName = tagName\n\n  // `inForeignNode` is correctly set by the parser.\n}\n\n/**\n * Emit an end tag.\n *\n * @param {Element} node\n *   Element.\n * @param {State} state\n *   Info passed around about the current state.\n * @returns {undefined}\n *   Nothing.\n */\nfunction endTag(node, state) {\n  const tagName = node.tagName.toLowerCase()\n  // Do not emit closing tags for HTML void elements.\n  if (\n    !state.parser.tokenizer.inForeignNode &&\n    html_void_elements__WEBPACK_IMPORTED_MODULE_7__.htmlVoidElements.includes(tagName)\n  ) {\n    return\n  }\n\n  // Ignore tags if we’re in plain text.\n  if (state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.PLAINTEXT) return\n\n  resetTokenizer(state, (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointEnd)(node))\n\n  /** @type {Token.TagToken} */\n  const tag = {\n    type: parse5__WEBPACK_IMPORTED_MODULE_0__.Token.TokenType.END_TAG,\n    tagName,\n    tagID: parse5__WEBPACK_IMPORTED_MODULE_0__.html.getTagID(tagName),\n    selfClosing: false,\n    ackSelfClosing: false,\n    attrs: [],\n    location: createParse5Location(node)\n  }\n\n  // The HTML parsing algorithm works by doing half of the state management in\n  // the tokenizer and half in the parser.\n  // We can’t use the tokenizer here, as we don’t have strings.\n  // So we act *as if* the tokenizer emits tokens:\n\n  // @ts-expect-error: private.\n  state.parser.currentToken = tag\n  // @ts-expect-error: private.\n  state.parser._processToken(state.parser.currentToken)\n\n  // …but then we still need a bunch of work that the tokenizer would normally\n  // do, such as:\n\n  // Switch back to the data state after alternative states that don’t accept\n  // tags:\n  if (\n    // Current element is closed.\n    tagName === state.parser.tokenizer.lastStartTagName &&\n    // `<textarea>` and `<title>`\n    (state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.RCDATA ||\n      // `<iframe>`, `<noembed>`, `<noframes>`, `<style>`, `<xmp>`\n      state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.RAWTEXT ||\n      // `<script>`\n      state.parser.tokenizer.state === parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.SCRIPT_DATA)\n    // Note: `<plaintext>` not needed, as it’s the last element.\n  ) {\n    state.parser.tokenizer.state = parse5__WEBPACK_IMPORTED_MODULE_0__.TokenizerMode.DATA\n  }\n}\n\n/**\n * Check if `node` represents a whole document or a fragment.\n *\n * @param {Nodes} node\n *   hast node.\n * @returns {boolean}\n *   Whether this represents a whole document or a fragment.\n */\nfunction documentMode(node) {\n  const head = node.type === 'root' ? node.children[0] : node\n  return Boolean(\n    head &&\n      (head.type === 'doctype' ||\n        (head.type === 'element' && head.tagName.toLowerCase() === 'html'))\n  )\n}\n\n/**\n * Get a `parse5` location from a node.\n *\n * @param {Nodes | Stitch} node\n *   hast node.\n * @returns {Token.Location}\n *   `parse5` location.\n */\nfunction createParse5Location(node) {\n  const start = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointStart)(node) || {\n    line: undefined,\n    column: undefined,\n    offset: undefined\n  }\n  const end = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_2__.pointEnd)(node) || {\n    line: undefined,\n    column: undefined,\n    offset: undefined\n  }\n\n  /** @type {Record<keyof Token.Location, number | undefined>} */\n  const location = {\n    startLine: start.line,\n    startCol: start.column,\n    startOffset: start.offset,\n    endLine: end.line,\n    endCol: end.column,\n    endOffset: end.offset\n  }\n\n  // @ts-expect-error: unist point values can be `undefined` in hast, which\n  // `parse5` types don’t want.\n  return location\n}\n\n/**\n * @template {Nodes} NodeType\n *   Node type.\n * @param {NodeType} node\n *   Node to clone.\n * @returns {NodeType}\n *   Cloned node, without children.\n */\nfunction cloneWithoutChildren(node) {\n  return 'children' in node\n    ? (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_8__[\"default\"])({...node, children: []})\n    : (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(node)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vaGFzdC11dGlsLXJhd0A5LjEuMC9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXJhdy9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0EsWUFBWSxTQUFTO0FBQ3JCLFlBQVksMkRBQTJEO0FBQ3ZFLFlBQVksS0FBSztBQUNqQixZQUFZLHNDQUFzQztBQUNsRCxZQUFZLE9BQU87QUFDbkI7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsY0FBYyw0QkFBNEI7QUFDMUM7QUFDQSxjQUFjLFNBQVM7QUFDdkI7QUFDQSxjQUFjLCtCQUErQjtBQUM3QztBQUNBLGNBQWMsU0FBUztBQUN2QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxXQUFXO0FBQ3pCO0FBQ0EsZUFBZSxnQkFBZ0I7QUFDL0I7QUFDQTs7QUFFcUQ7QUFDTDtBQUNKO0FBQ087QUFDTTtBQUNEO0FBQ2xCO0FBQ007QUFDZjs7QUFFN0I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsV0FBVyxzQ0FBc0M7QUFDakQsc0JBQXNCOztBQUV0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsNEJBQTRCO0FBQ3ZDO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDTztBQUNQO0FBQ0EsYUFBYSwwQ0FBMEM7QUFDdkQsY0FBYyw4Q0FBTTtBQUNwQixlQUFlLHNEQUFzRDtBQUNyRTtBQUNBLEdBQUc7O0FBRUgsYUFBYSxPQUFPO0FBQ3BCO0FBQ0E7QUFDQSxZQUFZLDBDQUFNO0FBQ2xCLFFBQVEsMENBQU07QUFDZDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHdCQUF3QiwrREFBVTs7QUFFbEM7QUFDQSxpQkFBaUIsaUVBQVU7QUFDM0I7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSxJQUFJLHVEQUFLO0FBQ1QsZ0NBQWdDLFFBQVEsZUFBZSxTQUFTO0FBQ2hFO0FBQ0EsbUJBQW1CLG9CQUFvQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLG9CQUFvQjtBQUMvQjtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE1BQU07QUFDakI7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE1BQU07QUFDakI7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxhQUFhLHNCQUFzQjtBQUNuQztBQUNBLFVBQVUsbURBQWU7QUFDekI7QUFDQTtBQUNBOztBQUVBLHdCQUF3QiwrREFBVTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxhQUFhLG9CQUFvQjtBQUNqQztBQUNBLFVBQVUsbURBQWU7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHdCQUF3QiwrREFBVTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGFBQWEsT0FBTztBQUNwQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxNQUFNO0FBQ3RDLFdBQVcsc0NBQXNDO0FBQ2pEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLHlCQUF5QixlQUFlO0FBQ25EOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsa0JBQWtCO0FBQzdCO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBOztBQUVBLGFBQWEsb0JBQW9CO0FBQ2pDO0FBQ0EsVUFBVSxtREFBZTtBQUN6QjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsK0RBQVU7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLEtBQUs7QUFDaEI7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0Esa0JBQWtCLCtEQUFVOztBQUU1QjtBQUNBO0FBQ0Esd0RBQXdEO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsT0FBTzs7QUFFakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxtQkFBbUI7QUFDOUI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxhQUFhLHNCQUFzQjtBQUNuQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLGlEQUFhO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0M7QUFDeEM7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsV0FBVyxtQkFBbUI7QUFDOUI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGdCQUFnQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSx1Q0FBdUMsaURBQWE7O0FBRXBELHdCQUF3QiwrREFBVTs7QUFFbEM7QUFDQSw4REFBOEQseURBQWE7O0FBRTNFLGFBQWEseURBQWE7QUFDMUIsU0FBUyx5REFBYTtBQUN0Qjs7QUFFQSxpQkFBaUIsNkRBQVE7QUFDekI7QUFDQSxLQUFLLHNCQUFzQjtBQUMzQixLQUFLLGNBQWMseURBQWE7QUFDaEM7O0FBRUEsYUFBYSxnQkFBZ0I7QUFDN0I7QUFDQSxVQUFVLG1EQUFlO0FBQ3pCO0FBQ0EsV0FBVyxpREFBYTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksZ0VBQWdCO0FBQ3BCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHVDQUF1QyxpREFBYTs7QUFFcEQsd0JBQXdCLDZEQUFROztBQUVoQyxhQUFhLGdCQUFnQjtBQUM3QjtBQUNBLFVBQVUsbURBQWU7QUFDekI7QUFDQSxXQUFXLGlEQUFhO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsaURBQWE7QUFDbkQ7QUFDQSx1Q0FBdUMsaURBQWE7QUFDcEQ7QUFDQSx1Q0FBdUMsaURBQWE7QUFDcEQ7QUFDQTtBQUNBLG1DQUFtQyxpREFBYTtBQUNoRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGdCQUFnQjtBQUMzQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsK0RBQVU7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLDZEQUFRO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGFBQWEsa0RBQWtEO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxjQUFjLE9BQU87QUFDckI7QUFDQSxXQUFXLFVBQVU7QUFDckI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLG1FQUFlLEVBQUUsc0JBQXNCO0FBQzdDLE1BQU0sbUVBQWU7QUFDckIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9oYXN0LXV0aWwtcmF3QDkuMS4wL25vZGVfbW9kdWxlcy9oYXN0LXV0aWwtcmF3L2xpYi9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnN9IGZyb20gJ2hhc3QtdXRpbC1yYXcnXG4gKiBAaW1wb3J0IHtDb21tZW50LCBEb2N0eXBlLCBFbGVtZW50LCBOb2RlcywgUm9vdENvbnRlbnQsIFJvb3QsIFRleHR9IGZyb20gJ2hhc3QnXG4gKiBAaW1wb3J0IHtSYXd9IGZyb20gJ21kYXN0LXV0aWwtdG8taGFzdCdcbiAqIEBpbXBvcnQge0RlZmF1bHRUcmVlQWRhcHRlck1hcCwgUGFyc2VyT3B0aW9uc30gZnJvbSAncGFyc2U1J1xuICogQGltcG9ydCB7UG9pbnR9IGZyb20gJ3VuaXN0J1xuICovXG5cbi8qKlxuICogQHR5cGVkZWYgU3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHByb3BlcnR5IHsobm9kZTogTm9kZXMpID0+IHVuZGVmaW5lZH0gaGFuZGxlXG4gKiAgIEFkZCBhIGhhc3Qgbm9kZSB0byB0aGUgcGFyc2VyLlxuICogQHByb3BlcnR5IHtPcHRpb25zfSBvcHRpb25zXG4gKiAgIFVzZXIgY29uZmlndXJhdGlvbi5cbiAqIEBwcm9wZXJ0eSB7UGFyc2VyPERlZmF1bHRUcmVlQWRhcHRlck1hcD59IHBhcnNlclxuICogICBDdXJyZW50IHBhcnNlci5cbiAqIEBwcm9wZXJ0eSB7Ym9vbGVhbn0gc3RpdGNoZXNcbiAqICAgV2hldGhlciB0aGVyZSBhcmUgc3RpdGNoZXMuXG4gKi9cblxuLyoqXG4gKiBAdHlwZWRlZiBTdGl0Y2hcbiAqICAgQ3VzdG9tIGNvbW1lbnQtbGlrZSB2YWx1ZSB3ZSBwYXNzIHRocm91Z2ggcGFyc2U1LCB3aGljaCBjb250YWlucyBhXG4gKiAgIHJlcGxhY2VtZW50IG5vZGUgdGhhdCB3ZeKAmWxsIHN3YXAgYmFjayBpbiBhZnRlcndhcmRzLlxuICogQHByb3BlcnR5IHsnY29tbWVudCd9IHR5cGVcbiAqICAgTm9kZSB0eXBlLlxuICogQHByb3BlcnR5IHt7c3RpdGNoOiBOb2Rlc319IHZhbHVlXG4gKiAgIFJlcGxhY2VtZW50IHZhbHVlLlxuICovXG5cbmltcG9ydCBzdHJ1Y3R1cmVkQ2xvbmUgZnJvbSAnQHVuZ2FwL3N0cnVjdHVyZWQtY2xvbmUnXG5pbXBvcnQge2Zyb21QYXJzZTV9IGZyb20gJ2hhc3QtdXRpbC1mcm9tLXBhcnNlNSdcbmltcG9ydCB7dG9QYXJzZTV9IGZyb20gJ2hhc3QtdXRpbC10by1wYXJzZTUnXG5pbXBvcnQge2h0bWxWb2lkRWxlbWVudHN9IGZyb20gJ2h0bWwtdm9pZC1lbGVtZW50cydcbmltcG9ydCB7UGFyc2VyLCBUb2tlbiwgVG9rZW5pemVyTW9kZSwgaHRtbH0gZnJvbSAncGFyc2U1J1xuaW1wb3J0IHtwb2ludEVuZCwgcG9pbnRTdGFydH0gZnJvbSAndW5pc3QtdXRpbC1wb3NpdGlvbidcbmltcG9ydCB7dmlzaXR9IGZyb20gJ3VuaXN0LXV0aWwtdmlzaXQnXG5pbXBvcnQge3dlYk5hbWVzcGFjZXN9IGZyb20gJ3dlYi1uYW1lc3BhY2VzJ1xuaW1wb3J0IHt6d2l0Y2h9IGZyb20gJ3p3aXRjaCdcblxuY29uc3QgZ2ZtVGFnZmlsdGVyRXhwcmVzc2lvbiA9XG4gIC88KFxcLz8pKGlmcmFtZXxub2VtYmVkfG5vZnJhbWVzfHBsYWludGV4dHxzY3JpcHR8c3R5bGV8dGV4dGFyZWF8dGl0bGV8eG1wKSg/PVtcXHRcXG5cXGZcXHIgLz5dKS9naVxuXG4vLyBOb2RlIHR5cGVzIGFzc29jaWF0ZWQgd2l0aCBNRFguXG4vLyA8aHR0cHM6Ly9naXRodWIuY29tL21keC1qcy9tZHgvYmxvYi84YTU2MzEyL3BhY2thZ2VzL21keC9saWIvbm9kZS10eXBlcy5qcz5cbmNvbnN0IGtub3duTWR4TmFtZXMgPSBuZXcgU2V0KFtcbiAgJ21keEZsb3dFeHByZXNzaW9uJyxcbiAgJ21keEpzeEZsb3dFbGVtZW50JyxcbiAgJ21keEpzeFRleHRFbGVtZW50JyxcbiAgJ21keFRleHRFeHByZXNzaW9uJyxcbiAgJ21keGpzRXNtJ1xuXSlcblxuLyoqIEB0eXBlIHtQYXJzZXJPcHRpb25zPERlZmF1bHRUcmVlQWRhcHRlck1hcD59ICovXG5jb25zdCBwYXJzZU9wdGlvbnMgPSB7c291cmNlQ29kZUxvY2F0aW9uSW5mbzogdHJ1ZSwgc2NyaXB0aW5nRW5hYmxlZDogZmFsc2V9XG5cbi8qKlxuICogUGFzcyBhIGhhc3QgdHJlZSB0aHJvdWdoIGFuIEhUTUwgcGFyc2VyLCB3aGljaCB3aWxsIGZpeCBuZXN0aW5nLCBhbmQgdHVyblxuICogcmF3IG5vZGVzIGludG8gYWN0dWFsIG5vZGVzLlxuICpcbiAqIEBwYXJhbSB7Tm9kZXN9IHRyZWVcbiAqICAgT3JpZ2luYWwgaGFzdCB0cmVlIHRvIHRyYW5zZm9ybS5cbiAqIEBwYXJhbSB7T3B0aW9ucyB8IG51bGwgfCB1bmRlZmluZWR9IFtvcHRpb25zXVxuICogICBDb25maWd1cmF0aW9uIChvcHRpb25hbCkuXG4gKiBAcmV0dXJucyB7Tm9kZXN9XG4gKiAgIFBhcnNlZCBhZ2FpbiB0cmVlLlxuICovXG5leHBvcnQgZnVuY3Rpb24gcmF3KHRyZWUsIG9wdGlvbnMpIHtcbiAgY29uc3QgZG9jdW1lbnQgPSBkb2N1bWVudE1vZGUodHJlZSlcbiAgLyoqIEB0eXBlIHsobm9kZTogTm9kZXMsIHN0YXRlOiBTdGF0ZSkgPT4gdW5kZWZpbmVkfSAqL1xuICBjb25zdCBvbmUgPSB6d2l0Y2goJ3R5cGUnLCB7XG4gICAgaGFuZGxlcnM6IHtyb290LCBlbGVtZW50LCB0ZXh0LCBjb21tZW50LCBkb2N0eXBlLCByYXc6IGhhbmRsZVJhd30sXG4gICAgdW5rbm93blxuICB9KVxuXG4gIC8qKiBAdHlwZSB7U3RhdGV9ICovXG4gIGNvbnN0IHN0YXRlID0ge1xuICAgIHBhcnNlcjogZG9jdW1lbnRcbiAgICAgID8gbmV3IFBhcnNlcihwYXJzZU9wdGlvbnMpXG4gICAgICA6IFBhcnNlci5nZXRGcmFnbWVudFBhcnNlcih1bmRlZmluZWQsIHBhcnNlT3B0aW9ucyksXG4gICAgaGFuZGxlKG5vZGUpIHtcbiAgICAgIG9uZShub2RlLCBzdGF0ZSlcbiAgICB9LFxuICAgIHN0aXRjaGVzOiBmYWxzZSxcbiAgICBvcHRpb25zOiBvcHRpb25zIHx8IHt9XG4gIH1cblxuICBvbmUodHJlZSwgc3RhdGUpXG4gIHJlc2V0VG9rZW5pemVyKHN0YXRlLCBwb2ludFN0YXJ0KCkpXG5cbiAgY29uc3QgcDUgPSBkb2N1bWVudCA/IHN0YXRlLnBhcnNlci5kb2N1bWVudCA6IHN0YXRlLnBhcnNlci5nZXRGcmFnbWVudCgpXG4gIGNvbnN0IHJlc3VsdCA9IGZyb21QYXJzZTUocDUsIHtcbiAgICAvLyBUbyBkbzogc3VwcG9ydCBgc3BhY2VgP1xuICAgIGZpbGU6IHN0YXRlLm9wdGlvbnMuZmlsZVxuICB9KVxuXG4gIGlmIChzdGF0ZS5zdGl0Y2hlcykge1xuICAgIHZpc2l0KHJlc3VsdCwgJ2NvbW1lbnQnLCBmdW5jdGlvbiAobm9kZSwgaW5kZXgsIHBhcmVudCkge1xuICAgICAgY29uc3Qgc3RpdGNoID0gLyoqIEB0eXBlIHtTdGl0Y2h9ICovICgvKiogQHR5cGUge3Vua25vd259ICovIChub2RlKSlcbiAgICAgIGlmIChzdGl0Y2gudmFsdWUuc3RpdGNoICYmIHBhcmVudCAmJiBpbmRleCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIC8qKiBAdHlwZSB7QXJyYXk8Um9vdENvbnRlbnQ+fSAqL1xuICAgICAgICBjb25zdCBzaWJsaW5ncyA9IHBhcmVudC5jaGlsZHJlblxuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBhc3N1bWUgdGhlIHN0aXRjaCBpcyBhbGxvd2VkLlxuICAgICAgICBzaWJsaW5nc1tpbmRleF0gPSBzdGl0Y2gudmFsdWUuc3RpdGNoXG4gICAgICAgIHJldHVybiBpbmRleFxuICAgICAgfVxuICAgIH0pXG4gIH1cblxuICAvLyBVbnBhY2sgaWYgcG9zc2libGUgYW5kIHdoZW4gbm90IGdpdmVuIGEgYHJvb3RgLlxuICBpZiAoXG4gICAgcmVzdWx0LnR5cGUgPT09ICdyb290JyAmJlxuICAgIHJlc3VsdC5jaGlsZHJlbi5sZW5ndGggPT09IDEgJiZcbiAgICByZXN1bHQuY2hpbGRyZW5bMF0udHlwZSA9PT0gdHJlZS50eXBlXG4gICkge1xuICAgIHJldHVybiByZXN1bHQuY2hpbGRyZW5bMF1cbiAgfVxuXG4gIHJldHVybiByZXN1bHRcbn1cblxuLyoqXG4gKiBUcmFuc2Zvcm0gYWxsIG5vZGVzXG4gKlxuICogQHBhcmFtIHtBcnJheTxSb290Q29udGVudD59IG5vZGVzXG4gKiAgIGhhc3QgY29udGVudC5cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZCBhYm91dCB0aGUgY3VycmVudCBzdGF0ZS5cbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gKiAgIE5vdGhpbmcuXG4gKi9cbmZ1bmN0aW9uIGFsbChub2Rlcywgc3RhdGUpIHtcbiAgbGV0IGluZGV4ID0gLTFcblxuICAvKiBpc3RhbmJ1bCBpZ25vcmUgZWxzZSAtIGludmFsaWQgbm9kZXMsIHNlZSByZWh5cGVqcy9yZWh5cGUtcmF3IzcuICovXG4gIGlmIChub2Rlcykge1xuICAgIHdoaWxlICgrK2luZGV4IDwgbm9kZXMubGVuZ3RoKSB7XG4gICAgICBzdGF0ZS5oYW5kbGUobm9kZXNbaW5kZXhdKVxuICAgIH1cbiAgfVxufVxuXG4vKipcbiAqIFRyYW5zZm9ybSBhIHJvb3QuXG4gKlxuICogQHBhcmFtIHtSb290fSBub2RlXG4gKiAgIGhhc3Qgcm9vdCBub2RlLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAqICAgTm90aGluZy5cbiAqL1xuZnVuY3Rpb24gcm9vdChub2RlLCBzdGF0ZSkge1xuICBhbGwobm9kZS5jaGlsZHJlbiwgc3RhdGUpXG59XG5cbi8qKlxuICogVHJhbnNmb3JtIGFuIGVsZW1lbnQuXG4gKlxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKiAgIGhhc3QgZWxlbWVudCBub2RlLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAqICAgTm90aGluZy5cbiAqL1xuZnVuY3Rpb24gZWxlbWVudChub2RlLCBzdGF0ZSkge1xuICBzdGFydFRhZyhub2RlLCBzdGF0ZSlcblxuICBhbGwobm9kZS5jaGlsZHJlbiwgc3RhdGUpXG5cbiAgZW5kVGFnKG5vZGUsIHN0YXRlKVxufVxuXG4vKipcbiAqIFRyYW5zZm9ybSBhIHRleHQuXG4gKlxuICogQHBhcmFtIHtUZXh0fSBub2RlXG4gKiAgIGhhc3QgdGV4dCBub2RlLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAqICAgTm90aGluZy5cbiAqL1xuZnVuY3Rpb24gdGV4dChub2RlLCBzdGF0ZSkge1xuICAvLyBBbGxvdyBgREFUQWAgdGhyb3VnaCBgUExBSU5URVhUYCxcbiAgLy8gYnV0IHdoZW4gaGFuZ2luZyBpbiBhIHRhZyBmb3IgZXhhbXBsZSxcbiAgLy8gc3dpdGNoIGJhY2sgdG8gYERBVEFgLlxuICAvLyBOb3RlOiBgU3RhdGVgIGlzIG5vdCBleHBvc2VkIGJ5IGBwYXJzZTVgLCBzbyB0aGVzZSBudW1iZXJzIGFyZSBmcmFnaWxlLlxuICAvLyBTZWU6IDxodHRwczovL2dpdGh1Yi5jb20vaW5pa3VsaW4vcGFyc2U1L2Jsb2IvNDZjYmE0My9wYWNrYWdlcy9wYXJzZTUvbGliL3Rva2VuaXplci9pbmRleC50cyNMNTg+XG4gIGlmIChzdGF0ZS5wYXJzZXIudG9rZW5pemVyLnN0YXRlID4gNCkge1xuICAgIHN0YXRlLnBhcnNlci50b2tlbml6ZXIuc3RhdGUgPSAwXG4gIH1cblxuICAvKiogQHR5cGUge1Rva2VuLkNoYXJhY3RlclRva2VufSAqL1xuICBjb25zdCB0b2tlbiA9IHtcbiAgICB0eXBlOiBUb2tlbi5Ub2tlblR5cGUuQ0hBUkFDVEVSLFxuICAgIGNoYXJzOiBub2RlLnZhbHVlLFxuICAgIGxvY2F0aW9uOiBjcmVhdGVQYXJzZTVMb2NhdGlvbihub2RlKVxuICB9XG5cbiAgcmVzZXRUb2tlbml6ZXIoc3RhdGUsIHBvaW50U3RhcnQobm9kZSkpXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IHByaXZhdGUuXG4gIHN0YXRlLnBhcnNlci5jdXJyZW50VG9rZW4gPSB0b2tlblxuICAvLyBAdHMtZXhwZWN0LWVycm9yOiBwcml2YXRlLlxuICBzdGF0ZS5wYXJzZXIuX3Byb2Nlc3NUb2tlbihzdGF0ZS5wYXJzZXIuY3VycmVudFRva2VuKVxufVxuXG4vKipcbiAqIFRyYW5zZm9ybSBhIGRvY3R5cGUuXG4gKlxuICogQHBhcmFtIHtEb2N0eXBlfSBub2RlXG4gKiAgIGhhc3QgZG9jdHlwZSBub2RlLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAqICAgTm90aGluZy5cbiAqL1xuZnVuY3Rpb24gZG9jdHlwZShub2RlLCBzdGF0ZSkge1xuICAvKiogQHR5cGUge1Rva2VuLkRvY3R5cGVUb2tlbn0gKi9cbiAgY29uc3QgdG9rZW4gPSB7XG4gICAgdHlwZTogVG9rZW4uVG9rZW5UeXBlLkRPQ1RZUEUsXG4gICAgbmFtZTogJ2h0bWwnLFxuICAgIGZvcmNlUXVpcmtzOiBmYWxzZSxcbiAgICBwdWJsaWNJZDogJycsXG4gICAgc3lzdGVtSWQ6ICcnLFxuICAgIGxvY2F0aW9uOiBjcmVhdGVQYXJzZTVMb2NhdGlvbihub2RlKVxuICB9XG5cbiAgcmVzZXRUb2tlbml6ZXIoc3RhdGUsIHBvaW50U3RhcnQobm9kZSkpXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IHByaXZhdGUuXG4gIHN0YXRlLnBhcnNlci5jdXJyZW50VG9rZW4gPSB0b2tlblxuICAvLyBAdHMtZXhwZWN0LWVycm9yOiBwcml2YXRlLlxuICBzdGF0ZS5wYXJzZXIuX3Byb2Nlc3NUb2tlbihzdGF0ZS5wYXJzZXIuY3VycmVudFRva2VuKVxufVxuXG4vKipcbiAqIFRyYW5zZm9ybSBhIHN0aXRjaC5cbiAqXG4gKiBAcGFyYW0ge05vZGVzfSBub2RlXG4gKiAgIHVua25vd24gbm9kZS5cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZCBhYm91dCB0aGUgY3VycmVudCBzdGF0ZS5cbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gKiAgIE5vdGhpbmcuXG4gKi9cbmZ1bmN0aW9uIHN0aXRjaChub2RlLCBzdGF0ZSkge1xuICAvLyBNYXJrIHRoYXQgdGhlcmUgYXJlIHN0aXRjaGVzLCBzbyB3ZSBuZWVkIHRvIHdhbGsgdGhlIHRyZWUgYW5kIHJldmVydCB0aGVtLlxuICBzdGF0ZS5zdGl0Y2hlcyA9IHRydWVcblxuICAvKiogQHR5cGUge05vZGVzfSAqL1xuICBjb25zdCBjbG9uZSA9IGNsb25lV2l0aG91dENoaWxkcmVuKG5vZGUpXG5cbiAgLy8gUmVjdXJzZSwgYmVjYXVzZSB0byBzb21ld2hhdCBoYW5kbGUgYFs8eD5dPC94PmAgKHdoZXJlIGBbXWAgZGVub3RlcyB0aGVcbiAgLy8gcGFzc2VkIHRocm91Z2ggbm9kZSkuXG4gIGlmICgnY2hpbGRyZW4nIGluIG5vZGUgJiYgJ2NoaWxkcmVuJyBpbiBjbG9uZSkge1xuICAgIC8vIFJvb3QgaW4gcm9vdCBvdXQuXG4gICAgY29uc3QgZmFrZVJvb3QgPSAvKiogQHR5cGUge1Jvb3R9ICovIChcbiAgICAgIHJhdyh7dHlwZTogJ3Jvb3QnLCBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbn0sIHN0YXRlLm9wdGlvbnMpXG4gICAgKVxuICAgIGNsb25lLmNoaWxkcmVuID0gZmFrZVJvb3QuY2hpbGRyZW5cbiAgfVxuXG4gIC8vIEhhY2s6IGB2YWx1ZWAgaXMgc3VwcG9zZWQgdG8gYmUgYSBzdHJpbmcsIGJ1dCBhcyBub25lIG9mIHRoZSB0b29sc1xuICAvLyAoYHBhcnNlNWAgb3IgYGhhc3QtdXRpbC1mcm9tLXBhcnNlNWApIGxvb2tzIGF0IGl0LCB3ZSBjYW4gcGFzcyBub2Rlc1xuICAvLyB0aHJvdWdoLlxuICBjb21tZW50KHt0eXBlOiAnY29tbWVudCcsIHZhbHVlOiB7c3RpdGNoOiBjbG9uZX19LCBzdGF0ZSlcbn1cblxuLyoqXG4gKiBUcmFuc2Zvcm0gYSBjb21tZW50IChvciBzdGl0Y2gpLlxuICpcbiAqIEBwYXJhbSB7Q29tbWVudCB8IFN0aXRjaH0gbm9kZVxuICogICBoYXN0IGNvbW1lbnQgbm9kZS5cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZCBhYm91dCB0aGUgY3VycmVudCBzdGF0ZS5cbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gKiAgIE5vdGhpbmcuXG4gKi9cbmZ1bmN0aW9uIGNvbW1lbnQobm9kZSwgc3RhdGUpIHtcbiAgLyoqIEB0eXBlIHtzdHJpbmd9ICovXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IHdlIHBhc3Mgc3RpdGNoZXMgdGhyb3VnaC5cbiAgY29uc3QgZGF0YSA9IG5vZGUudmFsdWVcblxuICAvKiogQHR5cGUge1Rva2VuLkNvbW1lbnRUb2tlbn0gKi9cbiAgY29uc3QgdG9rZW4gPSB7XG4gICAgdHlwZTogVG9rZW4uVG9rZW5UeXBlLkNPTU1FTlQsXG4gICAgZGF0YSxcbiAgICBsb2NhdGlvbjogY3JlYXRlUGFyc2U1TG9jYXRpb24obm9kZSlcbiAgfVxuICByZXNldFRva2VuaXplcihzdGF0ZSwgcG9pbnRTdGFydChub2RlKSlcbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogcHJpdmF0ZS5cbiAgc3RhdGUucGFyc2VyLmN1cnJlbnRUb2tlbiA9IHRva2VuXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IHByaXZhdGUuXG4gIHN0YXRlLnBhcnNlci5fcHJvY2Vzc1Rva2VuKHN0YXRlLnBhcnNlci5jdXJyZW50VG9rZW4pXG59XG5cbi8qKlxuICogVHJhbnNmb3JtIGEgcmF3IG5vZGUuXG4gKlxuICogQHBhcmFtIHtSYXd9IG5vZGVcbiAqICAgaGFzdCByYXcgbm9kZS5cbiAqIEBwYXJhbSB7U3RhdGV9IHN0YXRlXG4gKiAgIEluZm8gcGFzc2VkIGFyb3VuZCBhYm91dCB0aGUgY3VycmVudCBzdGF0ZS5cbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gKiAgIE5vdGhpbmcuXG4gKi9cbmZ1bmN0aW9uIGhhbmRsZVJhdyhub2RlLCBzdGF0ZSkge1xuICAvLyBSZXNldCBwcmVwcm9jZXNzb3I6XG4gIC8vIFNlZTogPGh0dHBzOi8vZ2l0aHViLmNvbS9pbmlrdWxpbi9wYXJzZTUvYmxvYi82ZjdjYTYwL3BhY2thZ2VzL3BhcnNlNS9saWIvdG9rZW5pemVyL3ByZXByb2Nlc3Nvci50cyNMMTgtTDMxPi5cbiAgc3RhdGUucGFyc2VyLnRva2VuaXplci5wcmVwcm9jZXNzb3IuaHRtbCA9ICcnXG4gIHN0YXRlLnBhcnNlci50b2tlbml6ZXIucHJlcHJvY2Vzc29yLnBvcyA9IC0xXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IHByaXZhdGUuXG4gIC8vIHR5cGUtY292ZXJhZ2U6aWdub3JlLW5leHQtbGluZVxuICBzdGF0ZS5wYXJzZXIudG9rZW5pemVyLnByZXByb2Nlc3Nvci5sYXN0R2FwUG9zID0gLTJcbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogcHJpdmF0ZS5cbiAgLy8gdHlwZS1jb3ZlcmFnZTppZ25vcmUtbmV4dC1saW5lXG4gIHN0YXRlLnBhcnNlci50b2tlbml6ZXIucHJlcHJvY2Vzc29yLmdhcFN0YWNrID0gW11cbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogcHJpdmF0ZS5cbiAgLy8gdHlwZS1jb3ZlcmFnZTppZ25vcmUtbmV4dC1saW5lXG4gIHN0YXRlLnBhcnNlci50b2tlbml6ZXIucHJlcHJvY2Vzc29yLnNraXBOZXh0TmV3TGluZSA9IGZhbHNlXG4gIHN0YXRlLnBhcnNlci50b2tlbml6ZXIucHJlcHJvY2Vzc29yLmxhc3RDaHVua1dyaXR0ZW4gPSBmYWxzZVxuICBzdGF0ZS5wYXJzZXIudG9rZW5pemVyLnByZXByb2Nlc3Nvci5lbmRPZkNodW5rSGl0ID0gZmFsc2VcbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogcHJpdmF0ZS5cbiAgLy8gdHlwZS1jb3ZlcmFnZTppZ25vcmUtbmV4dC1saW5lXG4gIHN0YXRlLnBhcnNlci50b2tlbml6ZXIucHJlcHJvY2Vzc29yLmlzRW9sID0gZmFsc2VcblxuICAvLyBOb3cgcGFzcyBgbm9kZS52YWx1ZWAuXG4gIHNldFBvaW50KHN0YXRlLCBwb2ludFN0YXJ0KG5vZGUpKVxuXG4gIHN0YXRlLnBhcnNlci50b2tlbml6ZXIud3JpdGUoXG4gICAgc3RhdGUub3B0aW9ucy50YWdmaWx0ZXJcbiAgICAgID8gbm9kZS52YWx1ZS5yZXBsYWNlKGdmbVRhZ2ZpbHRlckV4cHJlc3Npb24sICcmbHQ7JDEkMicpXG4gICAgICA6IG5vZGUudmFsdWUsXG4gICAgZmFsc2VcbiAgKVxuICAvLyBAdHMtZXhwZWN0LWVycm9yOiBwcml2YXRlLlxuICBzdGF0ZS5wYXJzZXIudG9rZW5pemVyLl9ydW5QYXJzaW5nTG9vcCgpXG5cbiAgLy8gQ2hhcmFjdGVyIHJlZmVyZW5jZXMgaGFuZywgc28gaWYgd2UgZW5kZWQgdGhlcmUsIHdlIG5lZWQgdG8gZmx1c2hcbiAgLy8gdGhvc2UgdG9vLlxuICAvLyBXZSByZXNldCB0aGUgcHJlcHJvY2Vzc29yIGFzIGlmIHRoZSBkb2N1bWVudCBlbmRzIGhlcmUuXG4gIC8vIFRoZW4gb25lIHNpbmdsZSBjYWxsIHRvIHRoZSByZWxldmFudCBzdGF0ZSBkb2VzIHRoZSB0cmljaywgcGFyc2U1XG4gIC8vIGNvbnN1bWVzIHRoZSB3aG9sZSB0b2tlbi5cblxuICAvLyBOb3RlOiBgU3RhdGVgIGlzIG5vdCBleHBvc2VkIGJ5IGBwYXJzZTVgLCBzbyB0aGVzZSBudW1iZXJzIGFyZSBmcmFnaWxlLlxuICAvLyBTZWU6IDxodHRwczovL2dpdGh1Yi5jb20vaW5pa3VsaW4vcGFyc2U1L2Jsb2IvNDZjYmE0My9wYWNrYWdlcy9wYXJzZTUvbGliL3Rva2VuaXplci9pbmRleC50cyNMNTg+XG4gIC8vIE5vdGU6IGEgY2hhbmdlIHRvIGBwYXJzZTVgLCB3aGljaCBicmVha3MgdGhpcywgd2FzIG1lcmdlZCBidXQgbm90IHJlbGVhc2VkLlxuICAvLyBJbnZlc3RpZ2F0ZSB3aGVuIGl0IGlzLlxuICAvLyBUbyBkbzogcmVtb3ZlIG5leHQgbWFqb3IuXG4gIC8qIGM4IGlnbm9yZSBuZXh0IDEyIC0tIHJlbW92ZWQgaW4gPGh0dHBzOi8vZ2l0aHViLmNvbS9pbmlrdWxpbi9wYXJzZTUvcHVsbC84OTc+ICovXG4gIGlmIChcbiAgICBzdGF0ZS5wYXJzZXIudG9rZW5pemVyLnN0YXRlID09PSA3MiAvKiBOQU1FRF9DSEFSQUNURVJfUkVGRVJFTkNFICovIHx8XG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogcmVtb3ZlZC5cbiAgICBzdGF0ZS5wYXJzZXIudG9rZW5pemVyLnN0YXRlID09PSA3OCAvKiBOVU1FUklDX0NIQVJBQ1RFUl9SRUZFUkVOQ0VfRU5EICovXG4gICkge1xuICAgIHN0YXRlLnBhcnNlci50b2tlbml6ZXIucHJlcHJvY2Vzc29yLmxhc3RDaHVua1dyaXR0ZW4gPSB0cnVlXG4gICAgLyoqIEB0eXBlIHtudW1iZXJ9ICovXG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogcHJpdmF0ZS5cbiAgICBjb25zdCBjcCA9IHN0YXRlLnBhcnNlci50b2tlbml6ZXIuX2NvbnN1bWUoKVxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IHByaXZhdGUuXG4gICAgc3RhdGUucGFyc2VyLnRva2VuaXplci5fY2FsbFN0YXRlKGNwKVxuICB9XG59XG5cbi8qKlxuICogQ3Jhc2ggb24gYW4gdW5rbm93biBub2RlLlxuICpcbiAqIEBwYXJhbSB7dW5rbm93bn0gbm9kZV9cbiAqICAgdW5rbm93biBub2RlLlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAqICAgTmV2ZXIuXG4gKi9cbmZ1bmN0aW9uIHVua25vd24obm9kZV8sIHN0YXRlKSB7XG4gIGNvbnN0IG5vZGUgPSAvKiogQHR5cGUge05vZGVzfSAqLyAobm9kZV8pXG5cbiAgaWYgKFxuICAgIHN0YXRlLm9wdGlvbnMucGFzc1Rocm91Z2ggJiZcbiAgICBzdGF0ZS5vcHRpb25zLnBhc3NUaHJvdWdoLmluY2x1ZGVzKG5vZGUudHlwZSlcbiAgKSB7XG4gICAgc3RpdGNoKG5vZGUsIHN0YXRlKVxuICB9IGVsc2Uge1xuICAgIGxldCBleHRyYSA9ICcnXG5cbiAgICBpZiAoa25vd25NZHhOYW1lcy5oYXMobm9kZS50eXBlKSkge1xuICAgICAgZXh0cmEgPVxuICAgICAgICBcIi4gSXQgbG9va3MgbGlrZSB5b3UgYXJlIHVzaW5nIE1EWCBub2RlcyB3aXRoIGBoYXN0LXV0aWwtcmF3YCAob3IgYHJlaHlwZS1yYXdgKS4gSWYgeW91IHVzZSB0aGlzIGJlY2F1c2UgeW91IGFyZSB1c2luZyByZW1hcmsgb3IgcmVoeXBlIHBsdWdpbnMgdGhhdCBpbmplY3QgYCdodG1sJ2Agbm9kZXMsIHRoZW4gcGxlYXNlIHJhaXNlIGFuIGlzc3VlIHdpdGggdGhhdCBwbHVnaW4sIGFzIGl0cyBhIGJhZCBhbmQgc2xvdyBpZGVhLiBJZiB5b3UgdXNlIHRoaXMgYmVjYXVzZSB5b3UgYXJlIHVzaW5nIG1hcmtkb3duIHN5bnRheCwgdGhlbiB5b3UgaGF2ZSB0byBjb25maWd1cmUgdGhpcyB1dGlsaXR5IChvciBwbHVnaW4pIHRvIHBhc3MgdGhyb3VnaCB0aGVzZSBub2RlcyAoc2VlIGBwYXNzVGhyb3VnaGAgaW4gZG9jcyksIGJ1dCB5b3UgY2FuIGFsc28gbWlncmF0ZSB0byB1c2UgdGhlIE1EWCBzeW50YXhcIlxuICAgIH1cblxuICAgIHRocm93IG5ldyBFcnJvcignQ2Fubm90IGNvbXBpbGUgYCcgKyBub2RlLnR5cGUgKyAnYCBub2RlJyArIGV4dHJhKVxuICB9XG59XG5cbi8qKlxuICogUmVzZXQgdGhlIHRva2VuaXplciBvZiBhIHBhcnNlci5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQgYWJvdXQgdGhlIGN1cnJlbnQgc3RhdGUuXG4gKiBAcGFyYW0ge1BvaW50IHwgdW5kZWZpbmVkfSBwb2ludFxuICogICBQb2ludC5cbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gKiAgIE5vdGhpbmcuXG4gKi9cbmZ1bmN0aW9uIHJlc2V0VG9rZW5pemVyKHN0YXRlLCBwb2ludCkge1xuICBzZXRQb2ludChzdGF0ZSwgcG9pbnQpXG5cbiAgLy8gUHJvY2VzcyBmaW5hbCBjaGFyYWN0ZXJzIGlmIHRoZXnigJlyZSBzdGlsbCB0aGVyZSBhZnRlciBoaWJlcm5hdGluZy5cbiAgLyoqIEB0eXBlIHtUb2tlbi5DaGFyYWN0ZXJUb2tlbn0gKi9cbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogcHJpdmF0ZS5cbiAgY29uc3QgdG9rZW4gPSBzdGF0ZS5wYXJzZXIudG9rZW5pemVyLmN1cnJlbnRDaGFyYWN0ZXJUb2tlblxuXG4gIGlmICh0b2tlbiAmJiB0b2tlbi5sb2NhdGlvbikge1xuICAgIHRva2VuLmxvY2F0aW9uLmVuZExpbmUgPSBzdGF0ZS5wYXJzZXIudG9rZW5pemVyLnByZXByb2Nlc3Nvci5saW5lXG4gICAgdG9rZW4ubG9jYXRpb24uZW5kQ29sID0gc3RhdGUucGFyc2VyLnRva2VuaXplci5wcmVwcm9jZXNzb3IuY29sICsgMVxuICAgIHRva2VuLmxvY2F0aW9uLmVuZE9mZnNldCA9IHN0YXRlLnBhcnNlci50b2tlbml6ZXIucHJlcHJvY2Vzc29yLm9mZnNldCArIDFcbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBwcml2YXRlLlxuICAgIHN0YXRlLnBhcnNlci5jdXJyZW50VG9rZW4gPSB0b2tlblxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IHByaXZhdGUuXG4gICAgc3RhdGUucGFyc2VyLl9wcm9jZXNzVG9rZW4oc3RhdGUucGFyc2VyLmN1cnJlbnRUb2tlbilcbiAgfVxuXG4gIC8vIFJlc2V0IHRva2VuaXplcjpcbiAgLy8gU2VlOiA8aHR0cHM6Ly9naXRodWIuY29tL2luaWt1bGluL3BhcnNlNS9ibG9iLzZmN2NhNjAvcGFja2FnZXMvcGFyc2U1L2xpYi90b2tlbml6ZXIvaW5kZXgudHMjTDE4Ny1MMjIzPi5cbiAgLy8gRXNwZWNpYWxseSBwdXR0aW5nIGl0IGJhY2sgaW4gdGhlIGBkYXRhYCBzdGF0ZSBpcyB1c2VmdWw6IHNvbWUgZWxlbWVudHMsXG4gIC8vIGxpa2UgdGV4dGFyZWFzIGFuZCBpZnJhbWVzLCBjaGFuZ2UgdGhlIHN0YXRlLlxuICAvLyBTZWUgR0gtNy5cbiAgLy8gQnV0IGFsc28gaWYgYnJva2VuIEhUTUwgaXMgaW4gYHJhd2AsIGFuZCB0aGVuIGEgY29ycmVjdCBlbGVtZW50IGlzIGdpdmVuLlxuICAvLyBTZWUgR0gtMTEuXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IHByaXZhdGUuXG4gIHN0YXRlLnBhcnNlci50b2tlbml6ZXIucGF1c2VkID0gZmFsc2VcbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogcHJpdmF0ZS5cbiAgc3RhdGUucGFyc2VyLnRva2VuaXplci5pbkxvb3AgPSBmYWxzZVxuXG4gIC8vIE5vdGU6IGRvbuKAmXQgcmVzZXQgYHN0YXRlYCwgYGluRm9yZWlnbk5vZGVgLCBvciBgbGFzdFN0YXJ0VGFnTmFtZWAsIHdlXG4gIC8vIG1hbnVhbGx5IHVwZGF0ZSB0aG9zZSB3aGVuIG5lZWRlZC5cbiAgc3RhdGUucGFyc2VyLnRva2VuaXplci5hY3RpdmUgPSBmYWxzZVxuICAvLyBAdHMtZXhwZWN0LWVycm9yOiBwcml2YXRlLlxuICBzdGF0ZS5wYXJzZXIudG9rZW5pemVyLnJldHVyblN0YXRlID0gVG9rZW5pemVyTW9kZS5EQVRBXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IHByaXZhdGUuXG4gIHN0YXRlLnBhcnNlci50b2tlbml6ZXIuY2hhclJlZkNvZGUgPSAtMVxuICAvLyBAdHMtZXhwZWN0LWVycm9yOiBwcml2YXRlLlxuICBzdGF0ZS5wYXJzZXIudG9rZW5pemVyLmNvbnN1bWVkQWZ0ZXJTbmFwc2hvdCA9IC0xXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IHByaXZhdGUuXG4gIHN0YXRlLnBhcnNlci50b2tlbml6ZXIuY3VycmVudExvY2F0aW9uID0gbnVsbFxuICAvLyBAdHMtZXhwZWN0LWVycm9yOiBwcml2YXRlLlxuICBzdGF0ZS5wYXJzZXIudG9rZW5pemVyLmN1cnJlbnRDaGFyYWN0ZXJUb2tlbiA9IG51bGxcbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogcHJpdmF0ZS5cbiAgc3RhdGUucGFyc2VyLnRva2VuaXplci5jdXJyZW50VG9rZW4gPSBudWxsXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IHByaXZhdGUuXG4gIHN0YXRlLnBhcnNlci50b2tlbml6ZXIuY3VycmVudEF0dHIgPSB7bmFtZTogJycsIHZhbHVlOiAnJ31cbn1cblxuLyoqXG4gKiBTZXQgY3VycmVudCBsb2NhdGlvbi5cbiAqXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQgYWJvdXQgdGhlIGN1cnJlbnQgc3RhdGUuXG4gKiBAcGFyYW0ge1BvaW50IHwgdW5kZWZpbmVkfSBwb2ludFxuICogICBQb2ludC5cbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XG4gKiAgIE5vdGhpbmcuXG4gKi9cbmZ1bmN0aW9uIHNldFBvaW50KHN0YXRlLCBwb2ludCkge1xuICBpZiAocG9pbnQgJiYgcG9pbnQub2Zmc2V0ICE9PSB1bmRlZmluZWQpIHtcbiAgICAvKiogQHR5cGUge1Rva2VuLkxvY2F0aW9ufSAqL1xuICAgIGNvbnN0IGxvY2F0aW9uID0ge1xuICAgICAgc3RhcnRMaW5lOiBwb2ludC5saW5lLFxuICAgICAgc3RhcnRDb2w6IHBvaW50LmNvbHVtbixcbiAgICAgIHN0YXJ0T2Zmc2V0OiBwb2ludC5vZmZzZXQsXG4gICAgICBlbmRMaW5lOiAtMSxcbiAgICAgIGVuZENvbDogLTEsXG4gICAgICBlbmRPZmZzZXQ6IC0xXG4gICAgfVxuXG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogcHJpdmF0ZS5cbiAgICAvLyB0eXBlLWNvdmVyYWdlOmlnbm9yZS1uZXh0LWxpbmVcbiAgICBzdGF0ZS5wYXJzZXIudG9rZW5pemVyLnByZXByb2Nlc3Nvci5saW5lU3RhcnRQb3MgPSAtcG9pbnQuY29sdW1uICsgMSAvLyBMb29rcyB3ZWlyZCwgYnV0IGVuc3VyZXMgd2UgZ2V0IGNvcnJlY3QgcG9zaXRpb25hbCBpbmZvLlxuICAgIHN0YXRlLnBhcnNlci50b2tlbml6ZXIucHJlcHJvY2Vzc29yLmRyb3BwZWRCdWZmZXJTaXplID0gcG9pbnQub2Zmc2V0XG4gICAgc3RhdGUucGFyc2VyLnRva2VuaXplci5wcmVwcm9jZXNzb3IubGluZSA9IHBvaW50LmxpbmVcbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBwcml2YXRlLlxuICAgIHN0YXRlLnBhcnNlci50b2tlbml6ZXIuY3VycmVudExvY2F0aW9uID0gbG9jYXRpb25cbiAgfVxufVxuXG4vKipcbiAqIEVtaXQgYSBzdGFydCB0YWcuXG4gKlxuICogQHBhcmFtIHtFbGVtZW50fSBub2RlXG4gKiAgIEVsZW1lbnQuXG4gKiBAcGFyYW0ge1N0YXRlfSBzdGF0ZVxuICogICBJbmZvIHBhc3NlZCBhcm91bmQgYWJvdXQgdGhlIGN1cnJlbnQgc3RhdGUuXG4gKiBAcmV0dXJucyB7dW5kZWZpbmVkfVxuICogICBOb3RoaW5nLlxuICovXG5mdW5jdGlvbiBzdGFydFRhZyhub2RlLCBzdGF0ZSkge1xuICBjb25zdCB0YWdOYW1lID0gbm9kZS50YWdOYW1lLnRvTG93ZXJDYXNlKClcblxuICAvLyBJZ25vcmUgdGFncyBpZiB3ZeKAmXJlIGluIHBsYWluIHRleHQuXG4gIGlmIChzdGF0ZS5wYXJzZXIudG9rZW5pemVyLnN0YXRlID09PSBUb2tlbml6ZXJNb2RlLlBMQUlOVEVYVCkgcmV0dXJuXG5cbiAgcmVzZXRUb2tlbml6ZXIoc3RhdGUsIHBvaW50U3RhcnQobm9kZSkpXG5cbiAgY29uc3QgY3VycmVudCA9IHN0YXRlLnBhcnNlci5vcGVuRWxlbWVudHMuY3VycmVudFxuICBsZXQgbnMgPSAnbmFtZXNwYWNlVVJJJyBpbiBjdXJyZW50ID8gY3VycmVudC5uYW1lc3BhY2VVUkkgOiB3ZWJOYW1lc3BhY2VzLmh0bWxcblxuICBpZiAobnMgPT09IHdlYk5hbWVzcGFjZXMuaHRtbCAmJiB0YWdOYW1lID09PSAnc3ZnJykge1xuICAgIG5zID0gd2ViTmFtZXNwYWNlcy5zdmdcbiAgfVxuXG4gIGNvbnN0IHJlc3VsdCA9IHRvUGFyc2U1KFxuICAgIC8vIFNoYWxsb3cgY2xvbmUgdG8gbm90IGRlbHZlIGludG8gYGNoaWxkcmVuYDogd2Ugb25seSBuZWVkIHRoZSBhdHRyaWJ1dGVzLlxuICAgIHsuLi5ub2RlLCBjaGlsZHJlbjogW119LFxuICAgIHtzcGFjZTogbnMgPT09IHdlYk5hbWVzcGFjZXMuc3ZnID8gJ3N2ZycgOiAnaHRtbCd9XG4gIClcblxuICAvKiogQHR5cGUge1Rva2VuLlRhZ1Rva2VufSAqL1xuICBjb25zdCB0YWcgPSB7XG4gICAgdHlwZTogVG9rZW4uVG9rZW5UeXBlLlNUQVJUX1RBRyxcbiAgICB0YWdOYW1lLFxuICAgIHRhZ0lEOiBodG1sLmdldFRhZ0lEKHRhZ05hbWUpLFxuICAgIC8vIFdlIGFsd2F5cyBzZW5kIHN0YXJ0IGFuZCBlbmQgdGFncy5cbiAgICBzZWxmQ2xvc2luZzogZmFsc2UsXG4gICAgYWNrU2VsZkNsb3Npbmc6IGZhbHNlLFxuICAgIC8vIEFsd2F5cyBlbGVtZW50LlxuICAgIC8qIGM4IGlnbm9yZSBuZXh0ICovXG4gICAgYXR0cnM6ICdhdHRycycgaW4gcmVzdWx0ID8gcmVzdWx0LmF0dHJzIDogW10sXG4gICAgbG9jYXRpb246IGNyZWF0ZVBhcnNlNUxvY2F0aW9uKG5vZGUpXG4gIH1cblxuICAvLyBUaGUgSFRNTCBwYXJzaW5nIGFsZ29yaXRobSB3b3JrcyBieSBkb2luZyBoYWxmIG9mIHRoZSBzdGF0ZSBtYW5hZ2VtZW50IGluXG4gIC8vIHRoZSB0b2tlbml6ZXIgYW5kIGhhbGYgaW4gdGhlIHBhcnNlci5cbiAgLy8gV2UgY2Fu4oCZdCB1c2UgdGhlIHRva2VuaXplciBoZXJlLCBhcyB3ZSBkb27igJl0IGhhdmUgc3RyaW5ncy5cbiAgLy8gU28gd2UgYWN0ICphcyBpZiogdGhlIHRva2VuaXplciBlbWl0cyB0b2tlbnM6XG5cbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogcHJpdmF0ZS5cbiAgc3RhdGUucGFyc2VyLmN1cnJlbnRUb2tlbiA9IHRhZ1xuICAvLyBAdHMtZXhwZWN0LWVycm9yOiBwcml2YXRlLlxuICBzdGF0ZS5wYXJzZXIuX3Byb2Nlc3NUb2tlbihzdGF0ZS5wYXJzZXIuY3VycmVudFRva2VuKVxuXG4gIC8vIOKApmJ1dCB0aGVuIHdlIHN0aWxsIG5lZWQgYSBidW5jaCBvZiB3b3JrIHRoYXQgdGhlIHRva2VuaXplciB3b3VsZCBub3JtYWxseVxuICAvLyBkbywgc3VjaCBhczpcblxuICAvLyBTZXQgYSB0YWcgbmFtZSwgc2ltaWxhciB0byBob3cgdGhlIHRva2VuaXplciB3b3VsZCBkbyBpdC5cbiAgc3RhdGUucGFyc2VyLnRva2VuaXplci5sYXN0U3RhcnRUYWdOYW1lID0gdGFnTmFtZVxuXG4gIC8vIGBpbkZvcmVpZ25Ob2RlYCBpcyBjb3JyZWN0bHkgc2V0IGJ5IHRoZSBwYXJzZXIuXG59XG5cbi8qKlxuICogRW1pdCBhbiBlbmQgdGFnLlxuICpcbiAqIEBwYXJhbSB7RWxlbWVudH0gbm9kZVxuICogICBFbGVtZW50LlxuICogQHBhcmFtIHtTdGF0ZX0gc3RhdGVcbiAqICAgSW5mbyBwYXNzZWQgYXJvdW5kIGFib3V0IHRoZSBjdXJyZW50IHN0YXRlLlxuICogQHJldHVybnMge3VuZGVmaW5lZH1cbiAqICAgTm90aGluZy5cbiAqL1xuZnVuY3Rpb24gZW5kVGFnKG5vZGUsIHN0YXRlKSB7XG4gIGNvbnN0IHRhZ05hbWUgPSBub2RlLnRhZ05hbWUudG9Mb3dlckNhc2UoKVxuICAvLyBEbyBub3QgZW1pdCBjbG9zaW5nIHRhZ3MgZm9yIEhUTUwgdm9pZCBlbGVtZW50cy5cbiAgaWYgKFxuICAgICFzdGF0ZS5wYXJzZXIudG9rZW5pemVyLmluRm9yZWlnbk5vZGUgJiZcbiAgICBodG1sVm9pZEVsZW1lbnRzLmluY2x1ZGVzKHRhZ05hbWUpXG4gICkge1xuICAgIHJldHVyblxuICB9XG5cbiAgLy8gSWdub3JlIHRhZ3MgaWYgd2XigJlyZSBpbiBwbGFpbiB0ZXh0LlxuICBpZiAoc3RhdGUucGFyc2VyLnRva2VuaXplci5zdGF0ZSA9PT0gVG9rZW5pemVyTW9kZS5QTEFJTlRFWFQpIHJldHVyblxuXG4gIHJlc2V0VG9rZW5pemVyKHN0YXRlLCBwb2ludEVuZChub2RlKSlcblxuICAvKiogQHR5cGUge1Rva2VuLlRhZ1Rva2VufSAqL1xuICBjb25zdCB0YWcgPSB7XG4gICAgdHlwZTogVG9rZW4uVG9rZW5UeXBlLkVORF9UQUcsXG4gICAgdGFnTmFtZSxcbiAgICB0YWdJRDogaHRtbC5nZXRUYWdJRCh0YWdOYW1lKSxcbiAgICBzZWxmQ2xvc2luZzogZmFsc2UsXG4gICAgYWNrU2VsZkNsb3Npbmc6IGZhbHNlLFxuICAgIGF0dHJzOiBbXSxcbiAgICBsb2NhdGlvbjogY3JlYXRlUGFyc2U1TG9jYXRpb24obm9kZSlcbiAgfVxuXG4gIC8vIFRoZSBIVE1MIHBhcnNpbmcgYWxnb3JpdGhtIHdvcmtzIGJ5IGRvaW5nIGhhbGYgb2YgdGhlIHN0YXRlIG1hbmFnZW1lbnQgaW5cbiAgLy8gdGhlIHRva2VuaXplciBhbmQgaGFsZiBpbiB0aGUgcGFyc2VyLlxuICAvLyBXZSBjYW7igJl0IHVzZSB0aGUgdG9rZW5pemVyIGhlcmUsIGFzIHdlIGRvbuKAmXQgaGF2ZSBzdHJpbmdzLlxuICAvLyBTbyB3ZSBhY3QgKmFzIGlmKiB0aGUgdG9rZW5pemVyIGVtaXRzIHRva2VuczpcblxuICAvLyBAdHMtZXhwZWN0LWVycm9yOiBwcml2YXRlLlxuICBzdGF0ZS5wYXJzZXIuY3VycmVudFRva2VuID0gdGFnXG4gIC8vIEB0cy1leHBlY3QtZXJyb3I6IHByaXZhdGUuXG4gIHN0YXRlLnBhcnNlci5fcHJvY2Vzc1Rva2VuKHN0YXRlLnBhcnNlci5jdXJyZW50VG9rZW4pXG5cbiAgLy8g4oCmYnV0IHRoZW4gd2Ugc3RpbGwgbmVlZCBhIGJ1bmNoIG9mIHdvcmsgdGhhdCB0aGUgdG9rZW5pemVyIHdvdWxkIG5vcm1hbGx5XG4gIC8vIGRvLCBzdWNoIGFzOlxuXG4gIC8vIFN3aXRjaCBiYWNrIHRvIHRoZSBkYXRhIHN0YXRlIGFmdGVyIGFsdGVybmF0aXZlIHN0YXRlcyB0aGF0IGRvbuKAmXQgYWNjZXB0XG4gIC8vIHRhZ3M6XG4gIGlmIChcbiAgICAvLyBDdXJyZW50IGVsZW1lbnQgaXMgY2xvc2VkLlxuICAgIHRhZ05hbWUgPT09IHN0YXRlLnBhcnNlci50b2tlbml6ZXIubGFzdFN0YXJ0VGFnTmFtZSAmJlxuICAgIC8vIGA8dGV4dGFyZWE+YCBhbmQgYDx0aXRsZT5gXG4gICAgKHN0YXRlLnBhcnNlci50b2tlbml6ZXIuc3RhdGUgPT09IFRva2VuaXplck1vZGUuUkNEQVRBIHx8XG4gICAgICAvLyBgPGlmcmFtZT5gLCBgPG5vZW1iZWQ+YCwgYDxub2ZyYW1lcz5gLCBgPHN0eWxlPmAsIGA8eG1wPmBcbiAgICAgIHN0YXRlLnBhcnNlci50b2tlbml6ZXIuc3RhdGUgPT09IFRva2VuaXplck1vZGUuUkFXVEVYVCB8fFxuICAgICAgLy8gYDxzY3JpcHQ+YFxuICAgICAgc3RhdGUucGFyc2VyLnRva2VuaXplci5zdGF0ZSA9PT0gVG9rZW5pemVyTW9kZS5TQ1JJUFRfREFUQSlcbiAgICAvLyBOb3RlOiBgPHBsYWludGV4dD5gIG5vdCBuZWVkZWQsIGFzIGl04oCZcyB0aGUgbGFzdCBlbGVtZW50LlxuICApIHtcbiAgICBzdGF0ZS5wYXJzZXIudG9rZW5pemVyLnN0YXRlID0gVG9rZW5pemVyTW9kZS5EQVRBXG4gIH1cbn1cblxuLyoqXG4gKiBDaGVjayBpZiBgbm9kZWAgcmVwcmVzZW50cyBhIHdob2xlIGRvY3VtZW50IG9yIGEgZnJhZ21lbnQuXG4gKlxuICogQHBhcmFtIHtOb2Rlc30gbm9kZVxuICogICBoYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciB0aGlzIHJlcHJlc2VudHMgYSB3aG9sZSBkb2N1bWVudCBvciBhIGZyYWdtZW50LlxuICovXG5mdW5jdGlvbiBkb2N1bWVudE1vZGUobm9kZSkge1xuICBjb25zdCBoZWFkID0gbm9kZS50eXBlID09PSAncm9vdCcgPyBub2RlLmNoaWxkcmVuWzBdIDogbm9kZVxuICByZXR1cm4gQm9vbGVhbihcbiAgICBoZWFkICYmXG4gICAgICAoaGVhZC50eXBlID09PSAnZG9jdHlwZScgfHxcbiAgICAgICAgKGhlYWQudHlwZSA9PT0gJ2VsZW1lbnQnICYmIGhlYWQudGFnTmFtZS50b0xvd2VyQ2FzZSgpID09PSAnaHRtbCcpKVxuICApXG59XG5cbi8qKlxuICogR2V0IGEgYHBhcnNlNWAgbG9jYXRpb24gZnJvbSBhIG5vZGUuXG4gKlxuICogQHBhcmFtIHtOb2RlcyB8IFN0aXRjaH0gbm9kZVxuICogICBoYXN0IG5vZGUuXG4gKiBAcmV0dXJucyB7VG9rZW4uTG9jYXRpb259XG4gKiAgIGBwYXJzZTVgIGxvY2F0aW9uLlxuICovXG5mdW5jdGlvbiBjcmVhdGVQYXJzZTVMb2NhdGlvbihub2RlKSB7XG4gIGNvbnN0IHN0YXJ0ID0gcG9pbnRTdGFydChub2RlKSB8fCB7XG4gICAgbGluZTogdW5kZWZpbmVkLFxuICAgIGNvbHVtbjogdW5kZWZpbmVkLFxuICAgIG9mZnNldDogdW5kZWZpbmVkXG4gIH1cbiAgY29uc3QgZW5kID0gcG9pbnRFbmQobm9kZSkgfHwge1xuICAgIGxpbmU6IHVuZGVmaW5lZCxcbiAgICBjb2x1bW46IHVuZGVmaW5lZCxcbiAgICBvZmZzZXQ6IHVuZGVmaW5lZFxuICB9XG5cbiAgLyoqIEB0eXBlIHtSZWNvcmQ8a2V5b2YgVG9rZW4uTG9jYXRpb24sIG51bWJlciB8IHVuZGVmaW5lZD59ICovXG4gIGNvbnN0IGxvY2F0aW9uID0ge1xuICAgIHN0YXJ0TGluZTogc3RhcnQubGluZSxcbiAgICBzdGFydENvbDogc3RhcnQuY29sdW1uLFxuICAgIHN0YXJ0T2Zmc2V0OiBzdGFydC5vZmZzZXQsXG4gICAgZW5kTGluZTogZW5kLmxpbmUsXG4gICAgZW5kQ29sOiBlbmQuY29sdW1uLFxuICAgIGVuZE9mZnNldDogZW5kLm9mZnNldFxuICB9XG5cbiAgLy8gQHRzLWV4cGVjdC1lcnJvcjogdW5pc3QgcG9pbnQgdmFsdWVzIGNhbiBiZSBgdW5kZWZpbmVkYCBpbiBoYXN0LCB3aGljaFxuICAvLyBgcGFyc2U1YCB0eXBlcyBkb27igJl0IHdhbnQuXG4gIHJldHVybiBsb2NhdGlvblxufVxuXG4vKipcbiAqIEB0ZW1wbGF0ZSB7Tm9kZXN9IE5vZGVUeXBlXG4gKiAgIE5vZGUgdHlwZS5cbiAqIEBwYXJhbSB7Tm9kZVR5cGV9IG5vZGVcbiAqICAgTm9kZSB0byBjbG9uZS5cbiAqIEByZXR1cm5zIHtOb2RlVHlwZX1cbiAqICAgQ2xvbmVkIG5vZGUsIHdpdGhvdXQgY2hpbGRyZW4uXG4gKi9cbmZ1bmN0aW9uIGNsb25lV2l0aG91dENoaWxkcmVuKG5vZGUpIHtcbiAgcmV0dXJuICdjaGlsZHJlbicgaW4gbm9kZVxuICAgID8gc3RydWN0dXJlZENsb25lKHsuLi5ub2RlLCBjaGlsZHJlbjogW119KVxuICAgIDogc3RydWN0dXJlZENsb25lKG5vZGUpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/hast-util-raw@9.1.0/node_modules/hast-util-raw/lib/index.js\n");

/***/ })

};
;