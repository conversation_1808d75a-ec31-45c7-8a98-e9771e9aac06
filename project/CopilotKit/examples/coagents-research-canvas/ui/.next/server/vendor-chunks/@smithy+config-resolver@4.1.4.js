"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+config-resolver@4.1.4";
exports.ids = ["vendor-chunks/@smithy+config-resolver@4.1.4"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/NodeUseDualstackEndpointConfigOptions.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/NodeUseDualstackEndpointConfigOptions.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONFIG_USE_DUALSTACK_ENDPOINT: () => (/* binding */ CONFIG_USE_DUALSTACK_ENDPOINT),\n/* harmony export */   DEFAULT_USE_DUALSTACK_ENDPOINT: () => (/* binding */ DEFAULT_USE_DUALSTACK_ENDPOINT),\n/* harmony export */   ENV_USE_DUALSTACK_ENDPOINT: () => (/* binding */ ENV_USE_DUALSTACK_ENDPOINT),\n/* harmony export */   NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS: () => (/* binding */ NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-config-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+util-config-provider@4.0.0/node_modules/@smithy/util-config-provider/dist-es/index.js\");\n\nconst ENV_USE_DUALSTACK_ENDPOINT = \"AWS_USE_DUALSTACK_ENDPOINT\";\nconst CONFIG_USE_DUALSTACK_ENDPOINT = \"use_dualstack_endpoint\";\nconst DEFAULT_USE_DUALSTACK_ENDPOINT = false;\nconst NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => (0,_smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__.booleanSelector)(env, ENV_USE_DUALSTACK_ENDPOINT, _smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__.SelectorType.ENV),\n    configFileSelector: (profile) => (0,_smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__.booleanSelector)(profile, CONFIG_USE_DUALSTACK_ENDPOINT, _smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__.SelectorType.CONFIG),\n    default: false,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvZW5kcG9pbnRzQ29uZmlnL05vZGVVc2VEdWFsc3RhY2tFbmRwb2ludENvbmZpZ09wdGlvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNkU7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDUCwwQ0FBMEMsNkVBQWUsa0NBQWtDLHNFQUFZO0FBQ3ZHLHFDQUFxQyw2RUFBZSx5Q0FBeUMsc0VBQVk7QUFDekc7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29uZmlnLXJlc29sdmVyQDQuMS40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvbmZpZy1yZXNvbHZlci9kaXN0LWVzL2VuZHBvaW50c0NvbmZpZy9Ob2RlVXNlRHVhbHN0YWNrRW5kcG9pbnRDb25maWdPcHRpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGJvb2xlYW5TZWxlY3RvciwgU2VsZWN0b3JUeXBlIH0gZnJvbSBcIkBzbWl0aHkvdXRpbC1jb25maWctcHJvdmlkZXJcIjtcbmV4cG9ydCBjb25zdCBFTlZfVVNFX0RVQUxTVEFDS19FTkRQT0lOVCA9IFwiQVdTX1VTRV9EVUFMU1RBQ0tfRU5EUE9JTlRcIjtcbmV4cG9ydCBjb25zdCBDT05GSUdfVVNFX0RVQUxTVEFDS19FTkRQT0lOVCA9IFwidXNlX2R1YWxzdGFja19lbmRwb2ludFwiO1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfVVNFX0RVQUxTVEFDS19FTkRQT0lOVCA9IGZhbHNlO1xuZXhwb3J0IGNvbnN0IE5PREVfVVNFX0RVQUxTVEFDS19FTkRQT0lOVF9DT05GSUdfT1BUSU9OUyA9IHtcbiAgICBlbnZpcm9ubWVudFZhcmlhYmxlU2VsZWN0b3I6IChlbnYpID0+IGJvb2xlYW5TZWxlY3RvcihlbnYsIEVOVl9VU0VfRFVBTFNUQUNLX0VORFBPSU5ULCBTZWxlY3RvclR5cGUuRU5WKSxcbiAgICBjb25maWdGaWxlU2VsZWN0b3I6IChwcm9maWxlKSA9PiBib29sZWFuU2VsZWN0b3IocHJvZmlsZSwgQ09ORklHX1VTRV9EVUFMU1RBQ0tfRU5EUE9JTlQsIFNlbGVjdG9yVHlwZS5DT05GSUcpLFxuICAgIGRlZmF1bHQ6IGZhbHNlLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/NodeUseDualstackEndpointConfigOptions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/NodeUseFipsEndpointConfigOptions.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/NodeUseFipsEndpointConfigOptions.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONFIG_USE_FIPS_ENDPOINT: () => (/* binding */ CONFIG_USE_FIPS_ENDPOINT),\n/* harmony export */   DEFAULT_USE_FIPS_ENDPOINT: () => (/* binding */ DEFAULT_USE_FIPS_ENDPOINT),\n/* harmony export */   ENV_USE_FIPS_ENDPOINT: () => (/* binding */ ENV_USE_FIPS_ENDPOINT),\n/* harmony export */   NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS: () => (/* binding */ NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-config-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+util-config-provider@4.0.0/node_modules/@smithy/util-config-provider/dist-es/index.js\");\n\nconst ENV_USE_FIPS_ENDPOINT = \"AWS_USE_FIPS_ENDPOINT\";\nconst CONFIG_USE_FIPS_ENDPOINT = \"use_fips_endpoint\";\nconst DEFAULT_USE_FIPS_ENDPOINT = false;\nconst NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => (0,_smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__.booleanSelector)(env, ENV_USE_FIPS_ENDPOINT, _smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__.SelectorType.ENV),\n    configFileSelector: (profile) => (0,_smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__.booleanSelector)(profile, CONFIG_USE_FIPS_ENDPOINT, _smithy_util_config_provider__WEBPACK_IMPORTED_MODULE_0__.SelectorType.CONFIG),\n    default: false,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvZW5kcG9pbnRzQ29uZmlnL05vZGVVc2VGaXBzRW5kcG9pbnRDb25maWdPcHRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZFO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBO0FBQ1AsMENBQTBDLDZFQUFlLDZCQUE2QixzRUFBWTtBQUNsRyxxQ0FBcUMsNkVBQWUsb0NBQW9DLHNFQUFZO0FBQ3BHO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvbmZpZy1yZXNvbHZlckA0LjEuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb25maWctcmVzb2x2ZXIvZGlzdC1lcy9lbmRwb2ludHNDb25maWcvTm9kZVVzZUZpcHNFbmRwb2ludENvbmZpZ09wdGlvbnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYm9vbGVhblNlbGVjdG9yLCBTZWxlY3RvclR5cGUgfSBmcm9tIFwiQHNtaXRoeS91dGlsLWNvbmZpZy1wcm92aWRlclwiO1xuZXhwb3J0IGNvbnN0IEVOVl9VU0VfRklQU19FTkRQT0lOVCA9IFwiQVdTX1VTRV9GSVBTX0VORFBPSU5UXCI7XG5leHBvcnQgY29uc3QgQ09ORklHX1VTRV9GSVBTX0VORFBPSU5UID0gXCJ1c2VfZmlwc19lbmRwb2ludFwiO1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfVVNFX0ZJUFNfRU5EUE9JTlQgPSBmYWxzZTtcbmV4cG9ydCBjb25zdCBOT0RFX1VTRV9GSVBTX0VORFBPSU5UX0NPTkZJR19PUFRJT05TID0ge1xuICAgIGVudmlyb25tZW50VmFyaWFibGVTZWxlY3RvcjogKGVudikgPT4gYm9vbGVhblNlbGVjdG9yKGVudiwgRU5WX1VTRV9GSVBTX0VORFBPSU5ULCBTZWxlY3RvclR5cGUuRU5WKSxcbiAgICBjb25maWdGaWxlU2VsZWN0b3I6IChwcm9maWxlKSA9PiBib29sZWFuU2VsZWN0b3IocHJvZmlsZSwgQ09ORklHX1VTRV9GSVBTX0VORFBPSU5ULCBTZWxlY3RvclR5cGUuQ09ORklHKSxcbiAgICBkZWZhdWx0OiBmYWxzZSxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/NodeUseFipsEndpointConfigOptions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/index.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/index.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONFIG_USE_DUALSTACK_ENDPOINT: () => (/* reexport safe */ _NodeUseDualstackEndpointConfigOptions__WEBPACK_IMPORTED_MODULE_0__.CONFIG_USE_DUALSTACK_ENDPOINT),\n/* harmony export */   CONFIG_USE_FIPS_ENDPOINT: () => (/* reexport safe */ _NodeUseFipsEndpointConfigOptions__WEBPACK_IMPORTED_MODULE_1__.CONFIG_USE_FIPS_ENDPOINT),\n/* harmony export */   DEFAULT_USE_DUALSTACK_ENDPOINT: () => (/* reexport safe */ _NodeUseDualstackEndpointConfigOptions__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_USE_DUALSTACK_ENDPOINT),\n/* harmony export */   DEFAULT_USE_FIPS_ENDPOINT: () => (/* reexport safe */ _NodeUseFipsEndpointConfigOptions__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_USE_FIPS_ENDPOINT),\n/* harmony export */   ENV_USE_DUALSTACK_ENDPOINT: () => (/* reexport safe */ _NodeUseDualstackEndpointConfigOptions__WEBPACK_IMPORTED_MODULE_0__.ENV_USE_DUALSTACK_ENDPOINT),\n/* harmony export */   ENV_USE_FIPS_ENDPOINT: () => (/* reexport safe */ _NodeUseFipsEndpointConfigOptions__WEBPACK_IMPORTED_MODULE_1__.ENV_USE_FIPS_ENDPOINT),\n/* harmony export */   NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS: () => (/* reexport safe */ _NodeUseDualstackEndpointConfigOptions__WEBPACK_IMPORTED_MODULE_0__.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS),\n/* harmony export */   NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS: () => (/* reexport safe */ _NodeUseFipsEndpointConfigOptions__WEBPACK_IMPORTED_MODULE_1__.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS),\n/* harmony export */   resolveCustomEndpointsConfig: () => (/* reexport safe */ _resolveCustomEndpointsConfig__WEBPACK_IMPORTED_MODULE_2__.resolveCustomEndpointsConfig),\n/* harmony export */   resolveEndpointsConfig: () => (/* reexport safe */ _resolveEndpointsConfig__WEBPACK_IMPORTED_MODULE_3__.resolveEndpointsConfig)\n/* harmony export */ });\n/* harmony import */ var _NodeUseDualstackEndpointConfigOptions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./NodeUseDualstackEndpointConfigOptions */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/NodeUseDualstackEndpointConfigOptions.js\");\n/* harmony import */ var _NodeUseFipsEndpointConfigOptions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NodeUseFipsEndpointConfigOptions */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/NodeUseFipsEndpointConfigOptions.js\");\n/* harmony import */ var _resolveCustomEndpointsConfig__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resolveCustomEndpointsConfig */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/resolveCustomEndpointsConfig.js\");\n/* harmony import */ var _resolveEndpointsConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resolveEndpointsConfig */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/resolveEndpointsConfig.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvZW5kcG9pbnRzQ29uZmlnL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdEO0FBQ0w7QUFDSjtBQUNOIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvZW5kcG9pbnRzQ29uZmlnL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL05vZGVVc2VEdWFsc3RhY2tFbmRwb2ludENvbmZpZ09wdGlvbnNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL05vZGVVc2VGaXBzRW5kcG9pbnRDb25maWdPcHRpb25zXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9yZXNvbHZlQ3VzdG9tRW5kcG9pbnRzQ29uZmlnXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9yZXNvbHZlRW5kcG9pbnRzQ29uZmlnXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/resolveCustomEndpointsConfig.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/resolveCustomEndpointsConfig.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveCustomEndpointsConfig: () => (/* binding */ resolveCustomEndpointsConfig)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n\nconst resolveCustomEndpointsConfig = (input) => {\n    const { tls, endpoint, urlParser, useDualstackEndpoint } = input;\n    return Object.assign(input, {\n        tls: tls ?? true,\n        endpoint: (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(typeof endpoint === \"string\" ? urlParser(endpoint) : endpoint),\n        isCustomEndpoint: true,\n        useDualstackEndpoint: (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(useDualstackEndpoint ?? false),\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvZW5kcG9pbnRzQ29uZmlnL3Jlc29sdmVDdXN0b21FbmRwb2ludHNDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEQ7QUFDckQ7QUFDUCxZQUFZLGlEQUFpRDtBQUM3RDtBQUNBO0FBQ0Esa0JBQWtCLDBFQUFpQjtBQUNuQztBQUNBLDhCQUE4QiwwRUFBaUI7QUFDL0MsS0FBSztBQUNMIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvZW5kcG9pbnRzQ29uZmlnL3Jlc29sdmVDdXN0b21FbmRwb2ludHNDb25maWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbm9ybWFsaXplUHJvdmlkZXIgfSBmcm9tIFwiQHNtaXRoeS91dGlsLW1pZGRsZXdhcmVcIjtcbmV4cG9ydCBjb25zdCByZXNvbHZlQ3VzdG9tRW5kcG9pbnRzQ29uZmlnID0gKGlucHV0KSA9PiB7XG4gICAgY29uc3QgeyB0bHMsIGVuZHBvaW50LCB1cmxQYXJzZXIsIHVzZUR1YWxzdGFja0VuZHBvaW50IH0gPSBpbnB1dDtcbiAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihpbnB1dCwge1xuICAgICAgICB0bHM6IHRscyA/PyB0cnVlLFxuICAgICAgICBlbmRwb2ludDogbm9ybWFsaXplUHJvdmlkZXIodHlwZW9mIGVuZHBvaW50ID09PSBcInN0cmluZ1wiID8gdXJsUGFyc2VyKGVuZHBvaW50KSA6IGVuZHBvaW50KSxcbiAgICAgICAgaXNDdXN0b21FbmRwb2ludDogdHJ1ZSxcbiAgICAgICAgdXNlRHVhbHN0YWNrRW5kcG9pbnQ6IG5vcm1hbGl6ZVByb3ZpZGVyKHVzZUR1YWxzdGFja0VuZHBvaW50ID8/IGZhbHNlKSxcbiAgICB9KTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/resolveCustomEndpointsConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/resolveEndpointsConfig.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/resolveEndpointsConfig.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveEndpointsConfig: () => (/* binding */ resolveEndpointsConfig)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n/* harmony import */ var _utils_getEndpointFromRegion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/getEndpointFromRegion */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/utils/getEndpointFromRegion.js\");\n\n\nconst resolveEndpointsConfig = (input) => {\n    const useDualstackEndpoint = (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(input.useDualstackEndpoint ?? false);\n    const { endpoint, useFipsEndpoint, urlParser, tls } = input;\n    return Object.assign(input, {\n        tls: tls ?? true,\n        endpoint: endpoint\n            ? (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(typeof endpoint === \"string\" ? urlParser(endpoint) : endpoint)\n            : () => (0,_utils_getEndpointFromRegion__WEBPACK_IMPORTED_MODULE_1__.getEndpointFromRegion)({ ...input, useDualstackEndpoint, useFipsEndpoint }),\n        isCustomEndpoint: !!endpoint,\n        useDualstackEndpoint,\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvZW5kcG9pbnRzQ29uZmlnL3Jlc29sdmVFbmRwb2ludHNDb25maWcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTREO0FBQ1U7QUFDL0Q7QUFDUCxpQ0FBaUMsMEVBQWlCO0FBQ2xELFlBQVksNENBQTRDO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBLGNBQWMsMEVBQWlCO0FBQy9CLG9CQUFvQixtRkFBcUIsR0FBRyxpREFBaUQ7QUFDN0Y7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29uZmlnLXJlc29sdmVyQDQuMS40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvbmZpZy1yZXNvbHZlci9kaXN0LWVzL2VuZHBvaW50c0NvbmZpZy9yZXNvbHZlRW5kcG9pbnRzQ29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG5vcm1hbGl6ZVByb3ZpZGVyIH0gZnJvbSBcIkBzbWl0aHkvdXRpbC1taWRkbGV3YXJlXCI7XG5pbXBvcnQgeyBnZXRFbmRwb2ludEZyb21SZWdpb24gfSBmcm9tIFwiLi91dGlscy9nZXRFbmRwb2ludEZyb21SZWdpb25cIjtcbmV4cG9ydCBjb25zdCByZXNvbHZlRW5kcG9pbnRzQ29uZmlnID0gKGlucHV0KSA9PiB7XG4gICAgY29uc3QgdXNlRHVhbHN0YWNrRW5kcG9pbnQgPSBub3JtYWxpemVQcm92aWRlcihpbnB1dC51c2VEdWFsc3RhY2tFbmRwb2ludCA/PyBmYWxzZSk7XG4gICAgY29uc3QgeyBlbmRwb2ludCwgdXNlRmlwc0VuZHBvaW50LCB1cmxQYXJzZXIsIHRscyB9ID0gaW5wdXQ7XG4gICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oaW5wdXQsIHtcbiAgICAgICAgdGxzOiB0bHMgPz8gdHJ1ZSxcbiAgICAgICAgZW5kcG9pbnQ6IGVuZHBvaW50XG4gICAgICAgICAgICA/IG5vcm1hbGl6ZVByb3ZpZGVyKHR5cGVvZiBlbmRwb2ludCA9PT0gXCJzdHJpbmdcIiA/IHVybFBhcnNlcihlbmRwb2ludCkgOiBlbmRwb2ludClcbiAgICAgICAgICAgIDogKCkgPT4gZ2V0RW5kcG9pbnRGcm9tUmVnaW9uKHsgLi4uaW5wdXQsIHVzZUR1YWxzdGFja0VuZHBvaW50LCB1c2VGaXBzRW5kcG9pbnQgfSksXG4gICAgICAgIGlzQ3VzdG9tRW5kcG9pbnQ6ICEhZW5kcG9pbnQsXG4gICAgICAgIHVzZUR1YWxzdGFja0VuZHBvaW50LFxuICAgIH0pO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/resolveEndpointsConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/utils/getEndpointFromRegion.js":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/utils/getEndpointFromRegion.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEndpointFromRegion: () => (/* binding */ getEndpointFromRegion)\n/* harmony export */ });\nconst getEndpointFromRegion = async (input) => {\n    const { tls = true } = input;\n    const region = await input.region();\n    const dnsHostRegex = new RegExp(/^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])$/);\n    if (!dnsHostRegex.test(region)) {\n        throw new Error(\"Invalid region in client config\");\n    }\n    const useDualstackEndpoint = await input.useDualstackEndpoint();\n    const useFipsEndpoint = await input.useFipsEndpoint();\n    const { hostname } = (await input.regionInfoProvider(region, { useDualstackEndpoint, useFipsEndpoint })) ?? {};\n    if (!hostname) {\n        throw new Error(\"Cannot resolve hostname from client config\");\n    }\n    return input.urlParser(`${tls ? \"https:\" : \"http:\"}//${hostname}`);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvZW5kcG9pbnRzQ29uZmlnL3V0aWxzL2dldEVuZHBvaW50RnJvbVJlZ2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCxZQUFZLGFBQWE7QUFDekI7QUFDQSwyRUFBMkUsS0FBSztBQUNoRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxXQUFXLDRDQUE0Qyx1Q0FBdUM7QUFDMUc7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHlCQUF5QixJQUFJLFNBQVM7QUFDcEUiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvbmZpZy1yZXNvbHZlckA0LjEuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb25maWctcmVzb2x2ZXIvZGlzdC1lcy9lbmRwb2ludHNDb25maWcvdXRpbHMvZ2V0RW5kcG9pbnRGcm9tUmVnaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBnZXRFbmRwb2ludEZyb21SZWdpb24gPSBhc3luYyAoaW5wdXQpID0+IHtcbiAgICBjb25zdCB7IHRscyA9IHRydWUgfSA9IGlucHV0O1xuICAgIGNvbnN0IHJlZ2lvbiA9IGF3YWl0IGlucHV0LnJlZ2lvbigpO1xuICAgIGNvbnN0IGRuc0hvc3RSZWdleCA9IG5ldyBSZWdFeHAoL14oW2EtekEtWjAtOV18W2EtekEtWjAtOV1bYS16QS1aMC05LV17MCw2MX1bYS16QS1aMC05XSkkLyk7XG4gICAgaWYgKCFkbnNIb3N0UmVnZXgudGVzdChyZWdpb24pKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgcmVnaW9uIGluIGNsaWVudCBjb25maWdcIik7XG4gICAgfVxuICAgIGNvbnN0IHVzZUR1YWxzdGFja0VuZHBvaW50ID0gYXdhaXQgaW5wdXQudXNlRHVhbHN0YWNrRW5kcG9pbnQoKTtcbiAgICBjb25zdCB1c2VGaXBzRW5kcG9pbnQgPSBhd2FpdCBpbnB1dC51c2VGaXBzRW5kcG9pbnQoKTtcbiAgICBjb25zdCB7IGhvc3RuYW1lIH0gPSAoYXdhaXQgaW5wdXQucmVnaW9uSW5mb1Byb3ZpZGVyKHJlZ2lvbiwgeyB1c2VEdWFsc3RhY2tFbmRwb2ludCwgdXNlRmlwc0VuZHBvaW50IH0pKSA/PyB7fTtcbiAgICBpZiAoIWhvc3RuYW1lKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkNhbm5vdCByZXNvbHZlIGhvc3RuYW1lIGZyb20gY2xpZW50IGNvbmZpZ1wiKTtcbiAgICB9XG4gICAgcmV0dXJuIGlucHV0LnVybFBhcnNlcihgJHt0bHMgPyBcImh0dHBzOlwiIDogXCJodHRwOlwifS8vJHtob3N0bmFtZX1gKTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/utils/getEndpointFromRegion.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/index.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/index.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CONFIG_USE_DUALSTACK_ENDPOINT: () => (/* reexport safe */ _endpointsConfig__WEBPACK_IMPORTED_MODULE_0__.CONFIG_USE_DUALSTACK_ENDPOINT),\n/* harmony export */   CONFIG_USE_FIPS_ENDPOINT: () => (/* reexport safe */ _endpointsConfig__WEBPACK_IMPORTED_MODULE_0__.CONFIG_USE_FIPS_ENDPOINT),\n/* harmony export */   DEFAULT_USE_DUALSTACK_ENDPOINT: () => (/* reexport safe */ _endpointsConfig__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_USE_DUALSTACK_ENDPOINT),\n/* harmony export */   DEFAULT_USE_FIPS_ENDPOINT: () => (/* reexport safe */ _endpointsConfig__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_USE_FIPS_ENDPOINT),\n/* harmony export */   ENV_USE_DUALSTACK_ENDPOINT: () => (/* reexport safe */ _endpointsConfig__WEBPACK_IMPORTED_MODULE_0__.ENV_USE_DUALSTACK_ENDPOINT),\n/* harmony export */   ENV_USE_FIPS_ENDPOINT: () => (/* reexport safe */ _endpointsConfig__WEBPACK_IMPORTED_MODULE_0__.ENV_USE_FIPS_ENDPOINT),\n/* harmony export */   NODE_REGION_CONFIG_FILE_OPTIONS: () => (/* reexport safe */ _regionConfig__WEBPACK_IMPORTED_MODULE_1__.NODE_REGION_CONFIG_FILE_OPTIONS),\n/* harmony export */   NODE_REGION_CONFIG_OPTIONS: () => (/* reexport safe */ _regionConfig__WEBPACK_IMPORTED_MODULE_1__.NODE_REGION_CONFIG_OPTIONS),\n/* harmony export */   NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS: () => (/* reexport safe */ _endpointsConfig__WEBPACK_IMPORTED_MODULE_0__.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS),\n/* harmony export */   NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS: () => (/* reexport safe */ _endpointsConfig__WEBPACK_IMPORTED_MODULE_0__.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS),\n/* harmony export */   REGION_ENV_NAME: () => (/* reexport safe */ _regionConfig__WEBPACK_IMPORTED_MODULE_1__.REGION_ENV_NAME),\n/* harmony export */   REGION_INI_NAME: () => (/* reexport safe */ _regionConfig__WEBPACK_IMPORTED_MODULE_1__.REGION_INI_NAME),\n/* harmony export */   getRegionInfo: () => (/* reexport safe */ _regionInfo__WEBPACK_IMPORTED_MODULE_2__.getRegionInfo),\n/* harmony export */   resolveCustomEndpointsConfig: () => (/* reexport safe */ _endpointsConfig__WEBPACK_IMPORTED_MODULE_0__.resolveCustomEndpointsConfig),\n/* harmony export */   resolveEndpointsConfig: () => (/* reexport safe */ _endpointsConfig__WEBPACK_IMPORTED_MODULE_0__.resolveEndpointsConfig),\n/* harmony export */   resolveRegionConfig: () => (/* reexport safe */ _regionConfig__WEBPACK_IMPORTED_MODULE_1__.resolveRegionConfig)\n/* harmony export */ });\n/* harmony import */ var _endpointsConfig__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./endpointsConfig */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/endpointsConfig/index.js\");\n/* harmony import */ var _regionConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./regionConfig */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/index.js\");\n/* harmony import */ var _regionInfo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./regionInfo */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/index.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFrQztBQUNIO0FBQ0YiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvbmZpZy1yZXNvbHZlckA0LjEuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb25maWctcmVzb2x2ZXIvZGlzdC1lcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9lbmRwb2ludHNDb25maWdcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3JlZ2lvbkNvbmZpZ1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vcmVnaW9uSW5mb1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/config.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/config.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NODE_REGION_CONFIG_FILE_OPTIONS: () => (/* binding */ NODE_REGION_CONFIG_FILE_OPTIONS),\n/* harmony export */   NODE_REGION_CONFIG_OPTIONS: () => (/* binding */ NODE_REGION_CONFIG_OPTIONS),\n/* harmony export */   REGION_ENV_NAME: () => (/* binding */ REGION_ENV_NAME),\n/* harmony export */   REGION_INI_NAME: () => (/* binding */ REGION_INI_NAME)\n/* harmony export */ });\nconst REGION_ENV_NAME = \"AWS_REGION\";\nconst REGION_INI_NAME = \"region\";\nconst NODE_REGION_CONFIG_OPTIONS = {\n    environmentVariableSelector: (env) => env[REGION_ENV_NAME],\n    configFileSelector: (profile) => profile[REGION_INI_NAME],\n    default: () => {\n        throw new Error(\"Region is missing\");\n    },\n};\nconst NODE_REGION_CONFIG_FILE_OPTIONS = {\n    preferredFile: \"credentials\",\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uQ29uZmlnL2NvbmZpZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvbmZpZy1yZXNvbHZlckA0LjEuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb25maWctcmVzb2x2ZXIvZGlzdC1lcy9yZWdpb25Db25maWcvY29uZmlnLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBSRUdJT05fRU5WX05BTUUgPSBcIkFXU19SRUdJT05cIjtcbmV4cG9ydCBjb25zdCBSRUdJT05fSU5JX05BTUUgPSBcInJlZ2lvblwiO1xuZXhwb3J0IGNvbnN0IE5PREVfUkVHSU9OX0NPTkZJR19PUFRJT05TID0ge1xuICAgIGVudmlyb25tZW50VmFyaWFibGVTZWxlY3RvcjogKGVudikgPT4gZW52W1JFR0lPTl9FTlZfTkFNRV0sXG4gICAgY29uZmlnRmlsZVNlbGVjdG9yOiAocHJvZmlsZSkgPT4gcHJvZmlsZVtSRUdJT05fSU5JX05BTUVdLFxuICAgIGRlZmF1bHQ6ICgpID0+IHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiUmVnaW9uIGlzIG1pc3NpbmdcIik7XG4gICAgfSxcbn07XG5leHBvcnQgY29uc3QgTk9ERV9SRUdJT05fQ09ORklHX0ZJTEVfT1BUSU9OUyA9IHtcbiAgICBwcmVmZXJyZWRGaWxlOiBcImNyZWRlbnRpYWxzXCIsXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/config.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/getRealRegion.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/getRealRegion.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRealRegion: () => (/* binding */ getRealRegion)\n/* harmony export */ });\n/* harmony import */ var _isFipsRegion__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isFipsRegion */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/isFipsRegion.js\");\n\nconst getRealRegion = (region) => (0,_isFipsRegion__WEBPACK_IMPORTED_MODULE_0__.isFipsRegion)(region)\n    ? [\"fips-aws-global\", \"aws-fips\"].includes(region)\n        ? \"us-east-1\"\n        : region.replace(/fips-(dkr-|prod-)?|-fips/, \"\")\n    : region;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uQ29uZmlnL2dldFJlYWxSZWdpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBOEM7QUFDdkMsa0NBQWtDLDJEQUFZO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uQ29uZmlnL2dldFJlYWxSZWdpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNGaXBzUmVnaW9uIH0gZnJvbSBcIi4vaXNGaXBzUmVnaW9uXCI7XG5leHBvcnQgY29uc3QgZ2V0UmVhbFJlZ2lvbiA9IChyZWdpb24pID0+IGlzRmlwc1JlZ2lvbihyZWdpb24pXG4gICAgPyBbXCJmaXBzLWF3cy1nbG9iYWxcIiwgXCJhd3MtZmlwc1wiXS5pbmNsdWRlcyhyZWdpb24pXG4gICAgICAgID8gXCJ1cy1lYXN0LTFcIlxuICAgICAgICA6IHJlZ2lvbi5yZXBsYWNlKC9maXBzLShka3ItfHByb2QtKT98LWZpcHMvLCBcIlwiKVxuICAgIDogcmVnaW9uO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/getRealRegion.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/index.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/index.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NODE_REGION_CONFIG_FILE_OPTIONS: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_0__.NODE_REGION_CONFIG_FILE_OPTIONS),\n/* harmony export */   NODE_REGION_CONFIG_OPTIONS: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_0__.NODE_REGION_CONFIG_OPTIONS),\n/* harmony export */   REGION_ENV_NAME: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_0__.REGION_ENV_NAME),\n/* harmony export */   REGION_INI_NAME: () => (/* reexport safe */ _config__WEBPACK_IMPORTED_MODULE_0__.REGION_INI_NAME),\n/* harmony export */   resolveRegionConfig: () => (/* reexport safe */ _resolveRegionConfig__WEBPACK_IMPORTED_MODULE_1__.resolveRegionConfig)\n/* harmony export */ });\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/config.js\");\n/* harmony import */ var _resolveRegionConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./resolveRegionConfig */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/resolveRegionConfig.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uQ29uZmlnL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBeUI7QUFDYSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29uZmlnLXJlc29sdmVyQDQuMS40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvbmZpZy1yZXNvbHZlci9kaXN0LWVzL3JlZ2lvbkNvbmZpZy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9jb25maWdcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3Jlc29sdmVSZWdpb25Db25maWdcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/isFipsRegion.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/isFipsRegion.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isFipsRegion: () => (/* binding */ isFipsRegion)\n/* harmony export */ });\nconst isFipsRegion = (region) => typeof region === \"string\" && (region.startsWith(\"fips-\") || region.endsWith(\"-fips\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uQ29uZmlnL2lzRmlwc1JlZ2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8iLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K2NvbmZpZy1yZXNvbHZlckA0LjEuNC9ub2RlX21vZHVsZXMvQHNtaXRoeS9jb25maWctcmVzb2x2ZXIvZGlzdC1lcy9yZWdpb25Db25maWcvaXNGaXBzUmVnaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBpc0ZpcHNSZWdpb24gPSAocmVnaW9uKSA9PiB0eXBlb2YgcmVnaW9uID09PSBcInN0cmluZ1wiICYmIChyZWdpb24uc3RhcnRzV2l0aChcImZpcHMtXCIpIHx8IHJlZ2lvbi5lbmRzV2l0aChcIi1maXBzXCIpKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/isFipsRegion.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/resolveRegionConfig.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/resolveRegionConfig.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveRegionConfig: () => (/* binding */ resolveRegionConfig)\n/* harmony export */ });\n/* harmony import */ var _getRealRegion__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getRealRegion */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/getRealRegion.js\");\n/* harmony import */ var _isFipsRegion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./isFipsRegion */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/isFipsRegion.js\");\n\n\nconst resolveRegionConfig = (input) => {\n    const { region, useFipsEndpoint } = input;\n    if (!region) {\n        throw new Error(\"Region is missing\");\n    }\n    return Object.assign(input, {\n        region: async () => {\n            if (typeof region === \"string\") {\n                return (0,_getRealRegion__WEBPACK_IMPORTED_MODULE_0__.getRealRegion)(region);\n            }\n            const providedRegion = await region();\n            return (0,_getRealRegion__WEBPACK_IMPORTED_MODULE_0__.getRealRegion)(providedRegion);\n        },\n        useFipsEndpoint: async () => {\n            const providedRegion = typeof region === \"string\" ? region : await region();\n            if ((0,_isFipsRegion__WEBPACK_IMPORTED_MODULE_1__.isFipsRegion)(providedRegion)) {\n                return true;\n            }\n            return typeof useFipsEndpoint !== \"function\" ? Promise.resolve(!!useFipsEndpoint) : useFipsEndpoint();\n        },\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionConfig/resolveRegionConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/PartitionHash.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/PartitionHash.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uSW5mby9QYXJ0aXRpb25IYXNoLmpzIiwibWFwcGluZ3MiOiI7QUFBVSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29uZmlnLXJlc29sdmVyQDQuMS40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvbmZpZy1yZXNvbHZlci9kaXN0LWVzL3JlZ2lvbkluZm8vUGFydGl0aW9uSGFzaC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge307XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/PartitionHash.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/RegionHash.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/RegionHash.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uSW5mby9SZWdpb25IYXNoLmpzIiwibWFwcGluZ3MiOiI7QUFBVSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29uZmlnLXJlc29sdmVyQDQuMS40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvbmZpZy1yZXNvbHZlci9kaXN0LWVzL3JlZ2lvbkluZm8vUmVnaW9uSGFzaC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQge307XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/RegionHash.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getHostnameFromVariants.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getHostnameFromVariants.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHostnameFromVariants: () => (/* binding */ getHostnameFromVariants)\n/* harmony export */ });\nconst getHostnameFromVariants = (variants = [], { useFipsEndpoint, useDualstackEndpoint }) => variants.find(({ tags }) => useFipsEndpoint === tags.includes(\"fips\") && useDualstackEndpoint === tags.includes(\"dualstack\"))?.hostname;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uSW5mby9nZXRIb3N0bmFtZUZyb21WYXJpYW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8sa0RBQWtELHVDQUF1QyxzQkFBc0IsTUFBTSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29uZmlnLXJlc29sdmVyQDQuMS40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvbmZpZy1yZXNvbHZlci9kaXN0LWVzL3JlZ2lvbkluZm8vZ2V0SG9zdG5hbWVGcm9tVmFyaWFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGdldEhvc3RuYW1lRnJvbVZhcmlhbnRzID0gKHZhcmlhbnRzID0gW10sIHsgdXNlRmlwc0VuZHBvaW50LCB1c2VEdWFsc3RhY2tFbmRwb2ludCB9KSA9PiB2YXJpYW50cy5maW5kKCh7IHRhZ3MgfSkgPT4gdXNlRmlwc0VuZHBvaW50ID09PSB0YWdzLmluY2x1ZGVzKFwiZmlwc1wiKSAmJiB1c2VEdWFsc3RhY2tFbmRwb2ludCA9PT0gdGFncy5pbmNsdWRlcyhcImR1YWxzdGFja1wiKSk/Lmhvc3RuYW1lO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getHostnameFromVariants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getRegionInfo.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getRegionInfo.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRegionInfo: () => (/* binding */ getRegionInfo)\n/* harmony export */ });\n/* harmony import */ var _getHostnameFromVariants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getHostnameFromVariants */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getHostnameFromVariants.js\");\n/* harmony import */ var _getResolvedHostname__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getResolvedHostname */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedHostname.js\");\n/* harmony import */ var _getResolvedPartition__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getResolvedPartition */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedPartition.js\");\n/* harmony import */ var _getResolvedSigningRegion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./getResolvedSigningRegion */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedSigningRegion.js\");\n\n\n\n\nconst getRegionInfo = (region, { useFipsEndpoint = false, useDualstackEndpoint = false, signingService, regionHash, partitionHash, }) => {\n    const partition = (0,_getResolvedPartition__WEBPACK_IMPORTED_MODULE_2__.getResolvedPartition)(region, { partitionHash });\n    const resolvedRegion = region in regionHash ? region : partitionHash[partition]?.endpoint ?? region;\n    const hostnameOptions = { useFipsEndpoint, useDualstackEndpoint };\n    const regionHostname = (0,_getHostnameFromVariants__WEBPACK_IMPORTED_MODULE_0__.getHostnameFromVariants)(regionHash[resolvedRegion]?.variants, hostnameOptions);\n    const partitionHostname = (0,_getHostnameFromVariants__WEBPACK_IMPORTED_MODULE_0__.getHostnameFromVariants)(partitionHash[partition]?.variants, hostnameOptions);\n    const hostname = (0,_getResolvedHostname__WEBPACK_IMPORTED_MODULE_1__.getResolvedHostname)(resolvedRegion, { regionHostname, partitionHostname });\n    if (hostname === undefined) {\n        throw new Error(`Endpoint resolution failed for: ${{ resolvedRegion, useFipsEndpoint, useDualstackEndpoint }}`);\n    }\n    const signingRegion = (0,_getResolvedSigningRegion__WEBPACK_IMPORTED_MODULE_3__.getResolvedSigningRegion)(hostname, {\n        signingRegion: regionHash[resolvedRegion]?.signingRegion,\n        regionRegex: partitionHash[partition].regionRegex,\n        useFipsEndpoint,\n    });\n    return {\n        partition,\n        signingService,\n        hostname,\n        ...(signingRegion && { signingRegion }),\n        ...(regionHash[resolvedRegion]?.signingService && {\n            signingService: regionHash[resolvedRegion].signingService,\n        }),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getRegionInfo.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedHostname.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedHostname.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getResolvedHostname: () => (/* binding */ getResolvedHostname)\n/* harmony export */ });\nconst getResolvedHostname = (resolvedRegion, { regionHostname, partitionHostname }) => regionHostname\n    ? regionHostname\n    : partitionHostname\n        ? partitionHostname.replace(\"{region}\", resolvedRegion)\n        : undefined;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uSW5mby9nZXRSZXNvbHZlZEhvc3RuYW1lLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTywrQ0FBK0MsbUNBQW1DO0FBQ3pGO0FBQ0E7QUFDQSxzQ0FBc0MsT0FBTztBQUM3QyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29uZmlnLXJlc29sdmVyQDQuMS40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvbmZpZy1yZXNvbHZlci9kaXN0LWVzL3JlZ2lvbkluZm8vZ2V0UmVzb2x2ZWRIb3N0bmFtZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgZ2V0UmVzb2x2ZWRIb3N0bmFtZSA9IChyZXNvbHZlZFJlZ2lvbiwgeyByZWdpb25Ib3N0bmFtZSwgcGFydGl0aW9uSG9zdG5hbWUgfSkgPT4gcmVnaW9uSG9zdG5hbWVcbiAgICA/IHJlZ2lvbkhvc3RuYW1lXG4gICAgOiBwYXJ0aXRpb25Ib3N0bmFtZVxuICAgICAgICA/IHBhcnRpdGlvbkhvc3RuYW1lLnJlcGxhY2UoXCJ7cmVnaW9ufVwiLCByZXNvbHZlZFJlZ2lvbilcbiAgICAgICAgOiB1bmRlZmluZWQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedHostname.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedPartition.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedPartition.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getResolvedPartition: () => (/* binding */ getResolvedPartition)\n/* harmony export */ });\nconst getResolvedPartition = (region, { partitionHash }) => Object.keys(partitionHash || {}).find((key) => partitionHash[key].regions.includes(region)) ?? \"aws\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uSW5mby9nZXRSZXNvbHZlZFBhcnRpdGlvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8sd0NBQXdDLGVBQWUsb0NBQW9DIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uSW5mby9nZXRSZXNvbHZlZFBhcnRpdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgZ2V0UmVzb2x2ZWRQYXJ0aXRpb24gPSAocmVnaW9uLCB7IHBhcnRpdGlvbkhhc2ggfSkgPT4gT2JqZWN0LmtleXMocGFydGl0aW9uSGFzaCB8fCB7fSkuZmluZCgoa2V5KSA9PiBwYXJ0aXRpb25IYXNoW2tleV0ucmVnaW9ucy5pbmNsdWRlcyhyZWdpb24pKSA/PyBcImF3c1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedPartition.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedSigningRegion.js":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedSigningRegion.js ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getResolvedSigningRegion: () => (/* binding */ getResolvedSigningRegion)\n/* harmony export */ });\nconst getResolvedSigningRegion = (hostname, { signingRegion, regionRegex, useFipsEndpoint }) => {\n    if (signingRegion) {\n        return signingRegion;\n    }\n    else if (useFipsEndpoint) {\n        const regionRegexJs = regionRegex.replace(\"\\\\\\\\\", \"\\\\\").replace(/^\\^/g, \"\\\\.\").replace(/\\$$/g, \"\\\\.\");\n        const regionRegexmatchArray = hostname.match(regionRegexJs);\n        if (regionRegexmatchArray) {\n            return regionRegexmatchArray[0].slice(1, -1);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uSW5mby9nZXRSZXNvbHZlZFNpZ25pbmdSZWdpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLDhDQUE4Qyw2Q0FBNkM7QUFDbEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29uZmlnLXJlc29sdmVyQDQuMS40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvbmZpZy1yZXNvbHZlci9kaXN0LWVzL3JlZ2lvbkluZm8vZ2V0UmVzb2x2ZWRTaWduaW5nUmVnaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBnZXRSZXNvbHZlZFNpZ25pbmdSZWdpb24gPSAoaG9zdG5hbWUsIHsgc2lnbmluZ1JlZ2lvbiwgcmVnaW9uUmVnZXgsIHVzZUZpcHNFbmRwb2ludCB9KSA9PiB7XG4gICAgaWYgKHNpZ25pbmdSZWdpb24pIHtcbiAgICAgICAgcmV0dXJuIHNpZ25pbmdSZWdpb247XG4gICAgfVxuICAgIGVsc2UgaWYgKHVzZUZpcHNFbmRwb2ludCkge1xuICAgICAgICBjb25zdCByZWdpb25SZWdleEpzID0gcmVnaW9uUmVnZXgucmVwbGFjZShcIlxcXFxcXFxcXCIsIFwiXFxcXFwiKS5yZXBsYWNlKC9eXFxeL2csIFwiXFxcXC5cIikucmVwbGFjZSgvXFwkJC9nLCBcIlxcXFwuXCIpO1xuICAgICAgICBjb25zdCByZWdpb25SZWdleG1hdGNoQXJyYXkgPSBob3N0bmFtZS5tYXRjaChyZWdpb25SZWdleEpzKTtcbiAgICAgICAgaWYgKHJlZ2lvblJlZ2V4bWF0Y2hBcnJheSkge1xuICAgICAgICAgICAgcmV0dXJuIHJlZ2lvblJlZ2V4bWF0Y2hBcnJheVswXS5zbGljZSgxLCAtMSk7XG4gICAgICAgIH1cbiAgICB9XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getResolvedSigningRegion.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/index.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/index.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRegionInfo: () => (/* reexport safe */ _getRegionInfo__WEBPACK_IMPORTED_MODULE_2__.getRegionInfo)\n/* harmony export */ });\n/* harmony import */ var _PartitionHash__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PartitionHash */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/PartitionHash.js\");\n/* harmony import */ var _RegionHash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./RegionHash */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/RegionHash.js\");\n/* harmony import */ var _getRegionInfo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getRegionInfo */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/getRegionInfo.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeStjb25maWctcmVzb2x2ZXJANC4xLjQvbm9kZV9tb2R1bGVzL0BzbWl0aHkvY29uZmlnLXJlc29sdmVyL2Rpc3QtZXMvcmVnaW9uSW5mby9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdDO0FBQ0g7QUFDRyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrY29uZmlnLXJlc29sdmVyQDQuMS40L25vZGVfbW9kdWxlcy9Ac21pdGh5L2NvbmZpZy1yZXNvbHZlci9kaXN0LWVzL3JlZ2lvbkluZm8vaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vUGFydGl0aW9uSGFzaFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vUmVnaW9uSGFzaFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vZ2V0UmVnaW9uSW5mb1wiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/regionInfo/index.js\n");

/***/ })

};
;