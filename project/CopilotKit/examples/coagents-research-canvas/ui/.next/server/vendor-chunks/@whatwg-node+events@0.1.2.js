"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@whatwg-node+events@0.1.2";
exports.ids = ["vendor-chunks/@whatwg-node+events@0.1.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@whatwg-node+events@0.1.2/node_modules/@whatwg-node/events/cjs/index.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@whatwg-node+events@0.1.2/node_modules/@whatwg-node/events/cjs/index.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CustomEvent = void 0;\nexports.CustomEvent = globalThis.CustomEvent ||\n    class PonyfillCustomEvent extends Event {\n        detail = null;\n        constructor(type, eventInitDict) {\n            super(type, eventInitDict);\n            if (eventInitDict?.detail != null) {\n                this.detail = eventInitDict.detail;\n            }\n        }\n        initCustomEvent(type, bubbles, cancelable, detail) {\n            this.initEvent(type, bubbles, cancelable);\n            if (detail != null) {\n                this.detail = detail;\n            }\n        }\n    };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHdoYXR3Zy1ub2RlK2V2ZW50c0AwLjEuMi9ub2RlX21vZHVsZXMvQHdoYXR3Zy1ub2RlL2V2ZW50cy9janMvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsbUJBQW1CO0FBQ25CLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ad2hhdHdnLW5vZGUrZXZlbnRzQDAuMS4yL25vZGVfbW9kdWxlcy9Ad2hhdHdnLW5vZGUvZXZlbnRzL2Nqcy9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuQ3VzdG9tRXZlbnQgPSB2b2lkIDA7XG5leHBvcnRzLkN1c3RvbUV2ZW50ID0gZ2xvYmFsVGhpcy5DdXN0b21FdmVudCB8fFxuICAgIGNsYXNzIFBvbnlmaWxsQ3VzdG9tRXZlbnQgZXh0ZW5kcyBFdmVudCB7XG4gICAgICAgIGRldGFpbCA9IG51bGw7XG4gICAgICAgIGNvbnN0cnVjdG9yKHR5cGUsIGV2ZW50SW5pdERpY3QpIHtcbiAgICAgICAgICAgIHN1cGVyKHR5cGUsIGV2ZW50SW5pdERpY3QpO1xuICAgICAgICAgICAgaWYgKGV2ZW50SW5pdERpY3Q/LmRldGFpbCAhPSBudWxsKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5kZXRhaWwgPSBldmVudEluaXREaWN0LmRldGFpbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpbml0Q3VzdG9tRXZlbnQodHlwZSwgYnViYmxlcywgY2FuY2VsYWJsZSwgZGV0YWlsKSB7XG4gICAgICAgICAgICB0aGlzLmluaXRFdmVudCh0eXBlLCBidWJibGVzLCBjYW5jZWxhYmxlKTtcbiAgICAgICAgICAgIGlmIChkZXRhaWwgIT0gbnVsbCkge1xuICAgICAgICAgICAgICAgIHRoaXMuZGV0YWlsID0gZGV0YWlsO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@whatwg-node+events@0.1.2/node_modules/@whatwg-node/events/cjs/index.js\n");

/***/ })

};
;