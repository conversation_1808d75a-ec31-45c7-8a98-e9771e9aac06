"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/agent-base@6.0.2";
exports.ids = ["vendor-chunks/agent-base@6.0.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/agent-base@6.0.2/node_modules/agent-base/dist/src/index.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/agent-base@6.0.2/node_modules/agent-base/dist/src/index.js ***!
  \***************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst debug_1 = __importDefault(__webpack_require__(/*! debug */ \"(rsc)/./node_modules/.pnpm/debug@4.3.7/node_modules/debug/src/index.js\"));\nconst promisify_1 = __importDefault(__webpack_require__(/*! ./promisify */ \"(rsc)/./node_modules/.pnpm/agent-base@6.0.2/node_modules/agent-base/dist/src/promisify.js\"));\nconst debug = debug_1.default('agent-base');\nfunction isAgent(v) {\n    return Boolean(v) && typeof v.addRequest === 'function';\n}\nfunction isSecureEndpoint() {\n    const { stack } = new Error();\n    if (typeof stack !== 'string')\n        return false;\n    return stack.split('\\n').some(l => l.indexOf('(https.js:') !== -1 || l.indexOf('node:https:') !== -1);\n}\nfunction createAgent(callback, opts) {\n    return new createAgent.Agent(callback, opts);\n}\n(function (createAgent) {\n    /**\n     * Base `http.Agent` implementation.\n     * No pooling/keep-alive is implemented by default.\n     *\n     * @param {Function} callback\n     * @api public\n     */\n    class Agent extends events_1.EventEmitter {\n        constructor(callback, _opts) {\n            super();\n            let opts = _opts;\n            if (typeof callback === 'function') {\n                this.callback = callback;\n            }\n            else if (callback) {\n                opts = callback;\n            }\n            // Timeout for the socket to be returned from the callback\n            this.timeout = null;\n            if (opts && typeof opts.timeout === 'number') {\n                this.timeout = opts.timeout;\n            }\n            // These aren't actually used by `agent-base`, but are required\n            // for the TypeScript definition files in `@types/node` :/\n            this.maxFreeSockets = 1;\n            this.maxSockets = 1;\n            this.maxTotalSockets = Infinity;\n            this.sockets = {};\n            this.freeSockets = {};\n            this.requests = {};\n            this.options = {};\n        }\n        get defaultPort() {\n            if (typeof this.explicitDefaultPort === 'number') {\n                return this.explicitDefaultPort;\n            }\n            return isSecureEndpoint() ? 443 : 80;\n        }\n        set defaultPort(v) {\n            this.explicitDefaultPort = v;\n        }\n        get protocol() {\n            if (typeof this.explicitProtocol === 'string') {\n                return this.explicitProtocol;\n            }\n            return isSecureEndpoint() ? 'https:' : 'http:';\n        }\n        set protocol(v) {\n            this.explicitProtocol = v;\n        }\n        callback(req, opts, fn) {\n            throw new Error('\"agent-base\" has no default implementation, you must subclass and override `callback()`');\n        }\n        /**\n         * Called by node-core's \"_http_client.js\" module when creating\n         * a new HTTP request with this Agent instance.\n         *\n         * @api public\n         */\n        addRequest(req, _opts) {\n            const opts = Object.assign({}, _opts);\n            if (typeof opts.secureEndpoint !== 'boolean') {\n                opts.secureEndpoint = isSecureEndpoint();\n            }\n            if (opts.host == null) {\n                opts.host = 'localhost';\n            }\n            if (opts.port == null) {\n                opts.port = opts.secureEndpoint ? 443 : 80;\n            }\n            if (opts.protocol == null) {\n                opts.protocol = opts.secureEndpoint ? 'https:' : 'http:';\n            }\n            if (opts.host && opts.path) {\n                // If both a `host` and `path` are specified then it's most\n                // likely the result of a `url.parse()` call... we need to\n                // remove the `path` portion so that `net.connect()` doesn't\n                // attempt to open that as a unix socket file.\n                delete opts.path;\n            }\n            delete opts.agent;\n            delete opts.hostname;\n            delete opts._defaultAgent;\n            delete opts.defaultPort;\n            delete opts.createConnection;\n            // Hint to use \"Connection: close\"\n            // XXX: non-documented `http` module API :(\n            req._last = true;\n            req.shouldKeepAlive = false;\n            let timedOut = false;\n            let timeoutId = null;\n            const timeoutMs = opts.timeout || this.timeout;\n            const onerror = (err) => {\n                if (req._hadError)\n                    return;\n                req.emit('error', err);\n                // For Safety. Some additional errors might fire later on\n                // and we need to make sure we don't double-fire the error event.\n                req._hadError = true;\n            };\n            const ontimeout = () => {\n                timeoutId = null;\n                timedOut = true;\n                const err = new Error(`A \"socket\" was not created for HTTP request before ${timeoutMs}ms`);\n                err.code = 'ETIMEOUT';\n                onerror(err);\n            };\n            const callbackError = (err) => {\n                if (timedOut)\n                    return;\n                if (timeoutId !== null) {\n                    clearTimeout(timeoutId);\n                    timeoutId = null;\n                }\n                onerror(err);\n            };\n            const onsocket = (socket) => {\n                if (timedOut)\n                    return;\n                if (timeoutId != null) {\n                    clearTimeout(timeoutId);\n                    timeoutId = null;\n                }\n                if (isAgent(socket)) {\n                    // `socket` is actually an `http.Agent` instance, so\n                    // relinquish responsibility for this `req` to the Agent\n                    // from here on\n                    debug('Callback returned another Agent instance %o', socket.constructor.name);\n                    socket.addRequest(req, opts);\n                    return;\n                }\n                if (socket) {\n                    socket.once('free', () => {\n                        this.freeSocket(socket, opts);\n                    });\n                    req.onSocket(socket);\n                    return;\n                }\n                const err = new Error(`no Duplex stream was returned to agent-base for \\`${req.method} ${req.path}\\``);\n                onerror(err);\n            };\n            if (typeof this.callback !== 'function') {\n                onerror(new Error('`callback` is not defined'));\n                return;\n            }\n            if (!this.promisifiedCallback) {\n                if (this.callback.length >= 3) {\n                    debug('Converting legacy callback function to promise');\n                    this.promisifiedCallback = promisify_1.default(this.callback);\n                }\n                else {\n                    this.promisifiedCallback = this.callback;\n                }\n            }\n            if (typeof timeoutMs === 'number' && timeoutMs > 0) {\n                timeoutId = setTimeout(ontimeout, timeoutMs);\n            }\n            if ('port' in opts && typeof opts.port !== 'number') {\n                opts.port = Number(opts.port);\n            }\n            try {\n                debug('Resolving socket for %o request: %o', opts.protocol, `${req.method} ${req.path}`);\n                Promise.resolve(this.promisifiedCallback(req, opts)).then(onsocket, callbackError);\n            }\n            catch (err) {\n                Promise.reject(err).catch(callbackError);\n            }\n        }\n        freeSocket(socket, opts) {\n            debug('Freeing socket %o %o', socket.constructor.name, opts);\n            socket.destroy();\n        }\n        destroy() {\n            debug('Destroying agent %o', this.constructor.name);\n        }\n    }\n    createAgent.Agent = Agent;\n    // So that `instanceof` works correctly\n    createAgent.prototype = createAgent.Agent.prototype;\n})(createAgent || (createAgent = {}));\nmodule.exports = createAgent;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/agent-base@6.0.2/node_modules/agent-base/dist/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/agent-base@6.0.2/node_modules/agent-base/dist/src/promisify.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/agent-base@6.0.2/node_modules/agent-base/dist/src/promisify.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nfunction promisify(fn) {\n    return function (req, opts) {\n        return new Promise((resolve, reject) => {\n            fn.call(this, req, opts, (err, rtn) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(rtn);\n                }\n            });\n        });\n    };\n}\nexports[\"default\"] = promisify;\n//# sourceMappingURL=promisify.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vYWdlbnQtYmFzZUA2LjAuMi9ub2RlX21vZHVsZXMvYWdlbnQtYmFzZS9kaXN0L3NyYy9wcm9taXNpZnkuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQSxrQkFBZTtBQUNmIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vYWdlbnQtYmFzZUA2LjAuMi9ub2RlX21vZHVsZXMvYWdlbnQtYmFzZS9kaXN0L3NyYy9wcm9taXNpZnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5mdW5jdGlvbiBwcm9taXNpZnkoZm4pIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKHJlcSwgb3B0cykge1xuICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgZm4uY2FsbCh0aGlzLCByZXEsIG9wdHMsIChlcnIsIHJ0bikgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVqZWN0KGVycik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICByZXNvbHZlKHJ0bik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuICAgIH07XG59XG5leHBvcnRzLmRlZmF1bHQgPSBwcm9taXNpZnk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wcm9taXNpZnkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/agent-base@6.0.2/node_modules/agent-base/dist/src/promisify.js\n");

/***/ })

};
;