/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8";
exports.ids = ["vendor-chunks/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/auth.cjs":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/auth.cjs ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ensureAuthOptionScopes = exports.aiPlatformScope = exports.ApiKeyGoogleAuth = exports.GoogleAbstractedFetchClient = void 0;\nconst stream_js_1 = __webpack_require__(/*! ./utils/stream.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/stream.cjs\");\nclass GoogleAbstractedFetchClient {\n    async _buildData(res, opts) {\n        switch (opts.responseType) {\n            case \"json\":\n                return res.json();\n            case \"stream\":\n                return new stream_js_1.ReadableJsonStream(res.body);\n            default:\n                return res.blob();\n        }\n    }\n    async _request(url, opts, additionalHeaders) {\n        if (url == null)\n            throw new Error(\"Missing URL\");\n        const fetchOptions = {\n            method: opts.method,\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...(opts.headers ?? {}),\n                ...(additionalHeaders ?? {}),\n            },\n        };\n        if (opts.data !== undefined) {\n            if (typeof opts.data === \"string\") {\n                fetchOptions.body = opts.data;\n            }\n            else {\n                fetchOptions.body = JSON.stringify(opts.data);\n            }\n        }\n        const res = await fetch(url, fetchOptions);\n        if (!res.ok) {\n            const resText = await res.text();\n            const error = new Error(`Google request failed with status code ${res.status}: ${resText}`);\n            /* eslint-disable @typescript-eslint/no-explicit-any */\n            error.response = res;\n            error.details = {\n                url,\n                opts,\n                fetchOptions,\n                result: res,\n            };\n            /* eslint-enable @typescript-eslint/no-explicit-any */\n            throw error;\n        }\n        const data = await this._buildData(res, opts);\n        return {\n            data,\n            config: {},\n            status: res.status,\n            statusText: res.statusText,\n            headers: res.headers,\n            request: { responseURL: res.url },\n        };\n    }\n}\nexports.GoogleAbstractedFetchClient = GoogleAbstractedFetchClient;\nclass ApiKeyGoogleAuth extends GoogleAbstractedFetchClient {\n    constructor(apiKey) {\n        super();\n        Object.defineProperty(this, \"apiKey\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.apiKey = apiKey;\n    }\n    get clientType() {\n        return \"apiKey\";\n    }\n    getProjectId() {\n        throw new Error(\"APIs that require a project ID cannot use an API key\");\n        // Perhaps we could implement this if needed:\n        // https://cloud.google.com/docs/authentication/api-keys#get-info\n    }\n    request(opts) {\n        const authHeader = {\n            \"X-Goog-Api-Key\": this.apiKey,\n        };\n        return this._request(opts.url, opts, authHeader);\n    }\n}\nexports.ApiKeyGoogleAuth = ApiKeyGoogleAuth;\nfunction aiPlatformScope(platform) {\n    switch (platform) {\n        case \"gai\":\n            return [\"https://www.googleapis.com/auth/generative-language\"];\n        default:\n            return [\"https://www.googleapis.com/auth/cloud-platform\"];\n    }\n}\nexports.aiPlatformScope = aiPlatformScope;\nfunction ensureAuthOptionScopes(authOption, scopeProperty, scopesOrPlatform) {\n    // If the property is already set, return it\n    if (authOption && Object.hasOwn(authOption, scopeProperty)) {\n        return authOption;\n    }\n    // Otherwise add it\n    const scopes = Array.isArray(scopesOrPlatform)\n        ? scopesOrPlatform\n        : aiPlatformScope(scopesOrPlatform ?? \"gcp\");\n    return {\n        [scopeProperty]: scopes,\n        ...(authOption ?? {}),\n    };\n}\nexports.ensureAuthOptionScopes = ensureAuthOptionScopes;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/auth.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/chat_models.cjs":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/chat_models.cjs ***!
  \*********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ChatGoogleBase = exports.ChatConnection = void 0;\nconst env_1 = __webpack_require__(/*! @langchain/core/utils/env */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/env.cjs\");\nconst chat_models_1 = __webpack_require__(/*! @langchain/core/language_models/chat_models */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/language_models/chat_models.cjs\");\nconst outputs_1 = __webpack_require__(/*! @langchain/core/outputs */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/outputs.cjs\");\nconst messages_1 = __webpack_require__(/*! @langchain/core/messages */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/messages.cjs\");\nconst runnables_1 = __webpack_require__(/*! @langchain/core/runnables */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/runnables.cjs\");\nconst openai_tools_1 = __webpack_require__(/*! @langchain/core/output_parsers/openai_tools */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/output_parsers/openai_tools.cjs\");\nconst stream_1 = __webpack_require__(/*! @langchain/core/utils/stream */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/stream.cjs\");\nconst common_js_1 = __webpack_require__(/*! ./utils/common.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/common.cjs\");\nconst connection_js_1 = __webpack_require__(/*! ./connection.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/connection.cjs\");\nconst gemini_js_1 = __webpack_require__(/*! ./utils/gemini.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/gemini.cjs\");\nconst auth_js_1 = __webpack_require__(/*! ./auth.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/auth.cjs\");\nconst failed_handler_js_1 = __webpack_require__(/*! ./utils/failed_handler.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/failed_handler.cjs\");\nconst zod_to_gemini_parameters_js_1 = __webpack_require__(/*! ./utils/zod_to_gemini_parameters.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/zod_to_gemini_parameters.cjs\");\nclass ChatConnection extends connection_js_1.AbstractGoogleLLMConnection {\n    constructor(fields, caller, client, streaming) {\n        super(fields, caller, client, streaming);\n        Object.defineProperty(this, \"convertSystemMessageToHumanContent\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.convertSystemMessageToHumanContent =\n            fields?.convertSystemMessageToHumanContent;\n    }\n    get useSystemInstruction() {\n        return typeof this.convertSystemMessageToHumanContent === \"boolean\"\n            ? !this.convertSystemMessageToHumanContent\n            : this.computeUseSystemInstruction;\n    }\n    get computeUseSystemInstruction() {\n        // This works on models from April 2024 and later\n        //   Vertex AI: gemini-1.5-pro and gemini-1.0-002 and later\n        //   AI Studio: gemini-1.5-pro-latest\n        if (this.modelFamily === \"palm\") {\n            return false;\n        }\n        else if (this.modelName === \"gemini-1.0-pro-001\") {\n            return false;\n        }\n        else if (this.modelName.startsWith(\"gemini-pro-vision\")) {\n            return false;\n        }\n        else if (this.modelName.startsWith(\"gemini-1.0-pro-vision\")) {\n            return false;\n        }\n        else if (this.modelName === \"gemini-pro\" && this.platform === \"gai\") {\n            // on AI Studio gemini-pro is still pointing at gemini-1.0-pro-001\n            return false;\n        }\n        return true;\n    }\n    async formatContents(input, _parameters) {\n        const inputPromises = input.map((msg, i) => this.api.baseMessageToContent(msg, input[i - 1], this.useSystemInstruction));\n        const inputs = await Promise.all(inputPromises);\n        return inputs.reduce((acc, cur) => {\n            // Filter out the system content\n            if (cur.every((content) => content.role === \"system\")) {\n                return acc;\n            }\n            // Combine adjacent function messages\n            if (cur[0]?.role === \"function\" &&\n                acc.length > 0 &&\n                acc[acc.length - 1].role === \"function\") {\n                acc[acc.length - 1].parts = [\n                    ...acc[acc.length - 1].parts,\n                    ...cur[0].parts,\n                ];\n            }\n            else {\n                acc.push(...cur);\n            }\n            return acc;\n        }, []);\n    }\n    async formatSystemInstruction(input, _parameters) {\n        if (!this.useSystemInstruction) {\n            return {};\n        }\n        let ret = {};\n        for (let index = 0; index < input.length; index += 1) {\n            const message = input[index];\n            if (message._getType() === \"system\") {\n                // For system types, we only want it if it is the first message,\n                // if it appears anywhere else, it should be an error.\n                if (index === 0) {\n                    // eslint-disable-next-line prefer-destructuring\n                    ret = (await this.api.baseMessageToContent(message, undefined, true))[0];\n                }\n                else {\n                    throw new Error(\"System messages are only permitted as the first passed message.\");\n                }\n            }\n        }\n        return ret;\n    }\n}\nexports.ChatConnection = ChatConnection;\n/**\n * Integration with a Google chat model.\n */\nclass ChatGoogleBase extends chat_models_1.BaseChatModel {\n    // Used for tracing, replace with the same name as your class\n    static lc_name() {\n        return \"ChatGoogle\";\n    }\n    get lc_secrets() {\n        return {\n            authOptions: \"GOOGLE_AUTH_OPTIONS\",\n        };\n    }\n    constructor(fields) {\n        super((0, failed_handler_js_1.ensureParams)(fields));\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        // Set based on modelName\n        Object.defineProperty(this, \"model\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"modelName\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"gemini-pro\"\n        });\n        Object.defineProperty(this, \"temperature\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 0.7\n        });\n        Object.defineProperty(this, \"maxOutputTokens\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 1024\n        });\n        Object.defineProperty(this, \"topP\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 0.8\n        });\n        Object.defineProperty(this, \"topK\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 40\n        });\n        Object.defineProperty(this, \"stopSequences\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: []\n        });\n        Object.defineProperty(this, \"safetySettings\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: []\n        });\n        // May intentionally be undefined, meaning to compute this.\n        Object.defineProperty(this, \"convertSystemMessageToHumanContent\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"safetyHandler\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"streamUsage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"streaming\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: false\n        });\n        Object.defineProperty(this, \"connection\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"streamedConnection\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        (0, common_js_1.copyAndValidateModelParamsInto)(fields, this);\n        this.safetyHandler =\n            fields?.safetyHandler ?? new gemini_js_1.DefaultGeminiSafetyHandler();\n        this.streamUsage = fields?.streamUsage ?? this.streamUsage;\n        const client = this.buildClient(fields);\n        this.buildConnection(fields ?? {}, client);\n    }\n    getLsParams(options) {\n        const params = this.invocationParams(options);\n        return {\n            ls_provider: \"google_vertexai\",\n            ls_model_name: this.model,\n            ls_model_type: \"chat\",\n            ls_temperature: params.temperature ?? undefined,\n            ls_max_tokens: params.maxOutputTokens ?? undefined,\n            ls_stop: options.stop,\n        };\n    }\n    buildApiKeyClient(apiKey) {\n        return new auth_js_1.ApiKeyGoogleAuth(apiKey);\n    }\n    buildApiKey(fields) {\n        return fields?.apiKey ?? (0, env_1.getEnvironmentVariable)(\"GOOGLE_API_KEY\");\n    }\n    buildClient(fields) {\n        const apiKey = this.buildApiKey(fields);\n        if (apiKey) {\n            return this.buildApiKeyClient(apiKey);\n        }\n        else {\n            return this.buildAbstractedClient(fields);\n        }\n    }\n    buildConnection(fields, client) {\n        this.connection = new ChatConnection({ ...fields, ...this }, this.caller, client, false);\n        this.streamedConnection = new ChatConnection({ ...fields, ...this }, this.caller, client, true);\n    }\n    get platform() {\n        return this.connection.platform;\n    }\n    bindTools(tools, kwargs) {\n        return this.bind({ tools: (0, common_js_1.convertToGeminiTools)(tools), ...kwargs });\n    }\n    // Replace\n    _llmType() {\n        return \"chat_integration\";\n    }\n    /**\n     * Get the parameters used to invoke the model\n     */\n    invocationParams(options) {\n        return (0, common_js_1.copyAIModelParams)(this, options);\n    }\n    async _generate(messages, options, runManager) {\n        const parameters = this.invocationParams(options);\n        if (this.streaming) {\n            const stream = this._streamResponseChunks(messages, options, runManager);\n            let finalChunk = null;\n            for await (const chunk of stream) {\n                finalChunk = !finalChunk ? chunk : (0, stream_1.concat)(finalChunk, chunk);\n            }\n            if (!finalChunk) {\n                throw new Error(\"No chunks were returned from the stream.\");\n            }\n            return {\n                generations: [finalChunk],\n            };\n        }\n        const response = await this.connection.request(messages, parameters, options);\n        const ret = this.connection.api.safeResponseToChatResult(response, this.safetyHandler);\n        await runManager?.handleLLMNewToken(ret.generations[0].text);\n        return ret;\n    }\n    async *_streamResponseChunks(_messages, options, runManager) {\n        // Make the call as a streaming request\n        const parameters = this.invocationParams(options);\n        const response = await this.streamedConnection.request(_messages, parameters, options);\n        // Get the streaming parser of the response\n        const stream = response.data;\n        let usageMetadata;\n        // Loop until the end of the stream\n        // During the loop, yield each time we get a chunk from the streaming parser\n        // that is either available or added to the queue\n        while (!stream.streamDone) {\n            const output = await stream.nextChunk();\n            if (output &&\n                output.usageMetadata &&\n                this.streamUsage !== false &&\n                options.streamUsage !== false) {\n                usageMetadata = {\n                    input_tokens: output.usageMetadata.promptTokenCount,\n                    output_tokens: output.usageMetadata.candidatesTokenCount,\n                    total_tokens: output.usageMetadata.totalTokenCount,\n                };\n            }\n            const chunk = output !== null\n                ? this.connection.api.safeResponseToChatGeneration({ data: output }, this.safetyHandler)\n                : new outputs_1.ChatGenerationChunk({\n                    text: \"\",\n                    generationInfo: { finishReason: \"stop\" },\n                    message: new messages_1.AIMessageChunk({\n                        content: \"\",\n                        usage_metadata: usageMetadata,\n                    }),\n                });\n            yield chunk;\n            await runManager?.handleLLMNewToken(chunk.text);\n        }\n    }\n    /** @ignore */\n    _combineLLMOutput() {\n        return [];\n    }\n    withStructuredOutput(outputSchema, config) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const schema = outputSchema;\n        const name = config?.name;\n        const method = config?.method;\n        const includeRaw = config?.includeRaw;\n        if (method === \"jsonMode\") {\n            throw new Error(`Google only supports \"functionCalling\" as a method.`);\n        }\n        let functionName = name ?? \"extract\";\n        let outputParser;\n        let tools;\n        if (isZodSchema(schema)) {\n            const jsonSchema = (0, zod_to_gemini_parameters_js_1.zodToGeminiParameters)(schema);\n            tools = [\n                {\n                    functionDeclarations: [\n                        {\n                            name: functionName,\n                            description: jsonSchema.description ?? \"A function available to call.\",\n                            parameters: jsonSchema,\n                        },\n                    ],\n                },\n            ];\n            outputParser = new openai_tools_1.JsonOutputKeyToolsParser({\n                returnSingle: true,\n                keyName: functionName,\n                zodSchema: schema,\n            });\n        }\n        else {\n            let geminiFunctionDefinition;\n            if (typeof schema.name === \"string\" &&\n                typeof schema.parameters === \"object\" &&\n                schema.parameters != null) {\n                geminiFunctionDefinition = schema;\n                functionName = schema.name;\n            }\n            else {\n                geminiFunctionDefinition = {\n                    name: functionName,\n                    description: schema.description ?? \"\",\n                    parameters: schema,\n                };\n            }\n            tools = [\n                {\n                    functionDeclarations: [geminiFunctionDefinition],\n                },\n            ];\n            outputParser = new openai_tools_1.JsonOutputKeyToolsParser({\n                returnSingle: true,\n                keyName: functionName,\n            });\n        }\n        const llm = this.bind({\n            tools,\n        });\n        if (!includeRaw) {\n            return llm.pipe(outputParser).withConfig({\n                runName: \"ChatGoogleStructuredOutput\",\n            });\n        }\n        const parserAssign = runnables_1.RunnablePassthrough.assign({\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            parsed: (input, config) => outputParser.invoke(input.raw, config),\n        });\n        const parserNone = runnables_1.RunnablePassthrough.assign({\n            parsed: () => null,\n        });\n        const parsedWithFallback = parserAssign.withFallbacks({\n            fallbacks: [parserNone],\n        });\n        return runnables_1.RunnableSequence.from([\n            {\n                raw: llm,\n            },\n            parsedWithFallback,\n        ]).withConfig({\n            runName: \"StructuredOutputRunnable\",\n        });\n    }\n}\nexports.ChatGoogleBase = ChatGoogleBase;\nfunction isZodSchema(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ninput) {\n    // Check for a characteristic method of Zod schemas\n    return typeof input?.parse === \"function\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/chat_models.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/connection.cjs":
/*!********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/connection.cjs ***!
  \********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AbstractGoogleLLMConnection = exports.GoogleAIConnection = exports.GoogleRawConnection = exports.GoogleHostConnection = exports.GoogleConnection = void 0;\nconst env_1 = __webpack_require__(/*! @langchain/core/utils/env */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/env.cjs\");\nconst function_calling_1 = __webpack_require__(/*! @langchain/core/utils/function_calling */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/function_calling.cjs\");\nconst zod_to_gemini_parameters_js_1 = __webpack_require__(/*! ./utils/zod_to_gemini_parameters.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/zod_to_gemini_parameters.cjs\");\nconst index_js_1 = __webpack_require__(/*! ./utils/index.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/index.cjs\");\nclass GoogleConnection {\n    constructor(caller, client, streaming) {\n        Object.defineProperty(this, \"caller\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"client\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"streaming\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.caller = caller;\n        this.client = client;\n        this.streaming = streaming ?? false;\n    }\n    async _clientInfoHeaders() {\n        const { userAgent, clientLibraryVersion } = await this._getClientInfo();\n        return {\n            \"User-Agent\": userAgent,\n            \"Client-Info\": clientLibraryVersion,\n        };\n    }\n    async _getClientInfo() {\n        const env = await (0, env_1.getRuntimeEnvironment)();\n        const langchain = env?.library ?? \"langchain-js\";\n        // TODO: Add an API for getting the current LangChain version\n        const langchainVersion = \"0\";\n        const moduleName = await this._moduleName();\n        let clientLibraryVersion = `${langchain}/${langchainVersion}`;\n        if (moduleName && moduleName.length) {\n            clientLibraryVersion = `${clientLibraryVersion}-${moduleName}`;\n        }\n        return {\n            userAgent: clientLibraryVersion,\n            clientLibraryVersion: `${langchainVersion}-${moduleName}`,\n        };\n    }\n    async _moduleName() {\n        return this.constructor.name;\n    }\n    async additionalHeaders() {\n        return {};\n    }\n    async _buildOpts(data, _options, requestHeaders = {}) {\n        const url = await this.buildUrl();\n        const method = this.buildMethod();\n        const infoHeaders = (await this._clientInfoHeaders()) ?? {};\n        const additionalHeaders = (await this.additionalHeaders()) ?? {};\n        const headers = {\n            ...infoHeaders,\n            ...additionalHeaders,\n            ...requestHeaders,\n        };\n        const opts = {\n            url,\n            method,\n            headers,\n        };\n        if (data && method === \"POST\") {\n            opts.data = data;\n        }\n        if (this.streaming) {\n            opts.responseType = \"stream\";\n        }\n        else {\n            opts.responseType = \"json\";\n        }\n        return opts;\n    }\n    async _request(data, options, requestHeaders = {}) {\n        const opts = await this._buildOpts(data, options, requestHeaders);\n        const callResponse = await this.caller.callWithOptions({ signal: options?.signal }, async () => this.client.request(opts));\n        const response = callResponse; // Done for typecast safety, I guess\n        return response;\n    }\n}\nexports.GoogleConnection = GoogleConnection;\nclass GoogleHostConnection extends GoogleConnection {\n    constructor(fields, caller, client, streaming) {\n        super(caller, client, streaming);\n        // This does not default to a value intentionally.\n        // Use the \"platform\" getter if you need this.\n        Object.defineProperty(this, \"platformType\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"endpoint\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"us-central1-aiplatform.googleapis.com\"\n        });\n        Object.defineProperty(this, \"location\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"us-central1\"\n        });\n        Object.defineProperty(this, \"apiVersion\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"v1\"\n        });\n        this.caller = caller;\n        this.platformType = fields?.platformType;\n        this.endpoint = fields?.endpoint ?? this.endpoint;\n        this.location = fields?.location ?? this.location;\n        this.apiVersion = fields?.apiVersion ?? this.apiVersion;\n        this.client = client;\n    }\n    get platform() {\n        return this.platformType ?? this.computedPlatformType;\n    }\n    get computedPlatformType() {\n        return \"gcp\";\n    }\n    buildMethod() {\n        return \"POST\";\n    }\n}\nexports.GoogleHostConnection = GoogleHostConnection;\nclass GoogleRawConnection extends GoogleHostConnection {\n    async _buildOpts(data, _options, requestHeaders = {}) {\n        const opts = await super._buildOpts(data, _options, requestHeaders);\n        opts.responseType = \"blob\";\n        return opts;\n    }\n}\nexports.GoogleRawConnection = GoogleRawConnection;\nclass GoogleAIConnection extends GoogleHostConnection {\n    constructor(fields, caller, client, streaming) {\n        super(fields, caller, client, streaming);\n        Object.defineProperty(this, \"model\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"modelName\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"client\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Object.defineProperty(this, \"api\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        }); // FIXME: Make this a real type\n        this.client = client;\n        this.modelName = fields?.model ?? fields?.modelName ?? this.model;\n        this.model = this.modelName;\n        this.api = (0, index_js_1.getGeminiAPI)(fields);\n    }\n    get modelFamily() {\n        if (this.model.startsWith(\"gemini\")) {\n            return \"gemini\";\n        }\n        else {\n            return null;\n        }\n    }\n    get computedPlatformType() {\n        if (this.client.clientType === \"apiKey\") {\n            return \"gai\";\n        }\n        else {\n            return \"gcp\";\n        }\n    }\n    async buildUrlGenerativeLanguage() {\n        const method = await this.buildUrlMethod();\n        const url = `https://generativelanguage.googleapis.com/${this.apiVersion}/models/${this.model}:${method}`;\n        return url;\n    }\n    async buildUrlVertex() {\n        const projectId = await this.client.getProjectId();\n        const method = await this.buildUrlMethod();\n        const url = `https://${this.endpoint}/${this.apiVersion}/projects/${projectId}/locations/${this.location}/publishers/google/models/${this.model}:${method}`;\n        return url;\n    }\n    async buildUrl() {\n        switch (this.platform) {\n            case \"gai\":\n                return this.buildUrlGenerativeLanguage();\n            default:\n                return this.buildUrlVertex();\n        }\n    }\n    async request(input, parameters, options) {\n        const data = await this.formatData(input, parameters);\n        const response = await this._request(data, options);\n        return response;\n    }\n}\nexports.GoogleAIConnection = GoogleAIConnection;\nclass AbstractGoogleLLMConnection extends GoogleAIConnection {\n    async buildUrlMethodGemini() {\n        return this.streaming ? \"streamGenerateContent\" : \"generateContent\";\n    }\n    async buildUrlMethod() {\n        switch (this.modelFamily) {\n            case \"gemini\":\n                return this.buildUrlMethodGemini();\n            default:\n                throw new Error(`Unknown model family: ${this.modelFamily}`);\n        }\n    }\n    formatGenerationConfig(_input, parameters) {\n        return {\n            temperature: parameters.temperature,\n            topK: parameters.topK,\n            topP: parameters.topP,\n            maxOutputTokens: parameters.maxOutputTokens,\n            stopSequences: parameters.stopSequences,\n            responseMimeType: parameters.responseMimeType,\n        };\n    }\n    formatSafetySettings(_input, parameters) {\n        return parameters.safetySettings ?? [];\n    }\n    async formatSystemInstruction(_input, _parameters) {\n        return {};\n    }\n    structuredToolToFunctionDeclaration(tool) {\n        const jsonSchema = (0, zod_to_gemini_parameters_js_1.zodToGeminiParameters)(tool.schema);\n        return {\n            name: tool.name,\n            description: tool.description ?? `A function available to call.`,\n            parameters: jsonSchema,\n        };\n    }\n    structuredToolsToGeminiTools(tools) {\n        return [\n            {\n                functionDeclarations: tools.map(this.structuredToolToFunctionDeclaration),\n            },\n        ];\n    }\n    formatTools(_input, parameters) {\n        const tools = parameters?.tools;\n        if (!tools || tools.length === 0) {\n            return [];\n        }\n        if (tools.every(function_calling_1.isLangChainTool)) {\n            return this.structuredToolsToGeminiTools(tools);\n        }\n        else {\n            if (tools.length === 1 &&\n                (!(\"functionDeclarations\" in tools[0]) ||\n                    !tools[0].functionDeclarations?.length)) {\n                return [];\n            }\n            return tools;\n        }\n    }\n    formatToolConfig(parameters) {\n        if (!parameters.tool_choice || typeof parameters.tool_choice !== \"string\") {\n            return undefined;\n        }\n        return {\n            functionCallingConfig: {\n                mode: parameters.tool_choice,\n                allowedFunctionNames: parameters.allowed_function_names,\n            },\n        };\n    }\n    async formatData(input, parameters) {\n        const contents = await this.formatContents(input, parameters);\n        const generationConfig = this.formatGenerationConfig(input, parameters);\n        const tools = this.formatTools(input, parameters);\n        const toolConfig = this.formatToolConfig(parameters);\n        const safetySettings = this.formatSafetySettings(input, parameters);\n        const systemInstruction = await this.formatSystemInstruction(input, parameters);\n        const ret = {\n            contents,\n            generationConfig,\n        };\n        if (tools && tools.length) {\n            ret.tools = tools;\n        }\n        if (toolConfig) {\n            ret.toolConfig = toolConfig;\n        }\n        if (safetySettings && safetySettings.length) {\n            ret.safetySettings = safetySettings;\n        }\n        if (systemInstruction?.role &&\n            systemInstruction?.parts &&\n            systemInstruction?.parts?.length) {\n            ret.systemInstruction = systemInstruction;\n        }\n        return ret;\n    }\n}\nexports.AbstractGoogleLLMConnection = AbstractGoogleLLMConnection;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/connection.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/embeddings.cjs":
/*!********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/embeddings.cjs ***!
  \********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BaseGoogleEmbeddings = void 0;\nconst embeddings_1 = __webpack_require__(/*! @langchain/core/embeddings */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/embeddings.cjs\");\nconst chunk_array_1 = __webpack_require__(/*! @langchain/core/utils/chunk_array */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/chunk_array.cjs\");\nconst env_1 = __webpack_require__(/*! @langchain/core/utils/env */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/env.cjs\");\nconst connection_js_1 = __webpack_require__(/*! ./connection.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/connection.cjs\");\nconst auth_js_1 = __webpack_require__(/*! ./auth.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/auth.cjs\");\nclass EmbeddingsConnection extends connection_js_1.GoogleAIConnection {\n    constructor(fields, caller, client, streaming) {\n        super(fields, caller, client, streaming);\n        Object.defineProperty(this, \"convertSystemMessageToHumanContent\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n    }\n    async buildUrlMethod() {\n        return \"predict\";\n    }\n    async formatData(input, parameters) {\n        return {\n            instances: input,\n            parameters,\n        };\n    }\n}\n/**\n * Enables calls to Google APIs for generating\n * text embeddings.\n */\nclass BaseGoogleEmbeddings extends embeddings_1.Embeddings {\n    constructor(fields) {\n        super(fields);\n        Object.defineProperty(this, \"model\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"connection\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.model = fields.model;\n        this.connection = new EmbeddingsConnection({ ...fields, ...this }, this.caller, this.buildClient(fields), false);\n    }\n    buildApiKeyClient(apiKey) {\n        return new auth_js_1.ApiKeyGoogleAuth(apiKey);\n    }\n    buildApiKey(fields) {\n        return fields?.apiKey ?? (0, env_1.getEnvironmentVariable)(\"GOOGLE_API_KEY\");\n    }\n    buildClient(fields) {\n        const apiKey = this.buildApiKey(fields);\n        if (apiKey) {\n            return this.buildApiKeyClient(apiKey);\n        }\n        else {\n            return this.buildAbstractedClient(fields);\n        }\n    }\n    /**\n     * Takes an array of documents as input and returns a promise that\n     * resolves to a 2D array of embeddings for each document. It splits the\n     * documents into chunks and makes requests to the Google Vertex AI API to\n     * generate embeddings.\n     * @param documents An array of documents to be embedded.\n     * @returns A promise that resolves to a 2D array of embeddings for each document.\n     */\n    async embedDocuments(documents) {\n        const instanceChunks = (0, chunk_array_1.chunkArray)(documents.map((document) => ({\n            content: document,\n        })), 5); // Vertex AI accepts max 5 instances per prediction\n        const parameters = {};\n        const options = {};\n        const responses = await Promise.all(instanceChunks.map((instances) => this.connection.request(instances, parameters, options)));\n        const result = responses\n            ?.map((response) => response?.data?.predictions?.map(\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (result) => result.embeddings?.values) ?? [])\n            .flat() ?? [];\n        return result;\n    }\n    /**\n     * Takes a document as input and returns a promise that resolves to an\n     * embedding for the document. It calls the embedDocuments method with the\n     * document as the input.\n     * @param document A document to be embedded.\n     * @returns A promise that resolves to an embedding for the document.\n     */\n    async embedQuery(document) {\n        const data = await this.embedDocuments([document]);\n        return data[0];\n    }\n}\nexports.BaseGoogleEmbeddings = BaseGoogleEmbeddings;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/embeddings.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/experimental/media.cjs":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/experimental/media.cjs ***!
  \****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BlobStoreAIStudioFileBase = exports.AIStudioFileDownloadConnection = exports.AIStudioFileUploadConnection = exports.AIStudioMediaBlob = exports.BlobStoreGoogleCloudStorageBase = exports.GoogleCloudStorageRawConnection = exports.GoogleCloudStorageDownloadConnection = exports.GoogleCloudStorageUploadConnection = exports.GoogleCloudStorageUri = exports.BlobStoreGoogle = exports.GoogleDownloadRawConnection = exports.GoogleDownloadConnection = exports.GoogleMultipartUploadConnection = void 0;\nconst async_caller_1 = __webpack_require__(/*! @langchain/core/utils/async_caller */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/async_caller.cjs\");\nconst env_1 = __webpack_require__(/*! @langchain/core/utils/env */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/env.cjs\");\nconst media_core_js_1 = __webpack_require__(/*! ./utils/media_core.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/experimental/utils/media_core.cjs\");\nconst connection_js_1 = __webpack_require__(/*! ../connection.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/connection.cjs\");\nconst auth_js_1 = __webpack_require__(/*! ../auth.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/auth.cjs\");\nclass GoogleMultipartUploadConnection extends connection_js_1.GoogleHostConnection {\n    constructor(fields, caller, client) {\n        super(fields, caller, client);\n    }\n    async _body(separator, data, metadata) {\n        const contentType = data.mimetype;\n        const { encoded, encoding } = await data.encode();\n        const body = [\n            `--${separator}`,\n            \"Content-Type: application/json; charset=UTF-8\",\n            \"\",\n            JSON.stringify(metadata),\n            \"\",\n            `--${separator}`,\n            `Content-Type: ${contentType}`,\n            `Content-Transfer-Encoding: ${encoding}`,\n            \"\",\n            encoded,\n            `--${separator}--`,\n        ];\n        return body.join(\"\\n\");\n    }\n    async request(data, metadata, options) {\n        const separator = `separator-${Date.now()}`;\n        const body = await this._body(separator, data, metadata);\n        const requestHeaders = {\n            \"Content-Type\": `multipart/related; boundary=${separator}`,\n            \"X-Goog-Upload-Protocol\": \"multipart\",\n        };\n        const response = this._request(body, options, requestHeaders);\n        return response;\n    }\n}\nexports.GoogleMultipartUploadConnection = GoogleMultipartUploadConnection;\nclass GoogleDownloadConnection extends connection_js_1.GoogleHostConnection {\n    async request(options) {\n        return this._request(undefined, options);\n    }\n}\nexports.GoogleDownloadConnection = GoogleDownloadConnection;\nclass GoogleDownloadRawConnection extends connection_js_1.GoogleRawConnection {\n    buildMethod() {\n        return \"GET\";\n    }\n    async request(options) {\n        return this._request(undefined, options);\n    }\n}\nexports.GoogleDownloadRawConnection = GoogleDownloadRawConnection;\nclass BlobStoreGoogle extends media_core_js_1.BlobStore {\n    constructor(fields) {\n        super(fields);\n        Object.defineProperty(this, \"caller\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"client\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.caller = new async_caller_1.AsyncCaller(fields ?? {});\n        this.client = this.buildClient(fields);\n    }\n    async _set(keyValuePair) {\n        const [, blob] = keyValuePair;\n        const setMetadata = this.buildSetMetadata(keyValuePair);\n        const metadata = setMetadata;\n        const options = {};\n        const connection = this.buildSetConnection(keyValuePair);\n        const response = await connection.request(blob, metadata, options);\n        return response;\n    }\n    async mset(keyValuePairs) {\n        const ret = keyValuePairs.map((keyValue) => this._set(keyValue));\n        await Promise.all(ret);\n    }\n    async _getMetadata(key) {\n        const connection = this.buildGetMetadataConnection(key);\n        const options = {};\n        const response = await connection.request(options);\n        return response.data;\n    }\n    async _getData(key) {\n        const connection = this.buildGetDataConnection(key);\n        const options = {};\n        const response = await connection.request(options);\n        return response.data;\n    }\n    _getMimetypeFromMetadata(metadata) {\n        return metadata.contentType;\n    }\n    async _get(key) {\n        const metadata = await this._getMetadata(key);\n        const data = await this._getData(key);\n        if (data && metadata) {\n            const ret = await media_core_js_1.MediaBlob.fromBlob(data, { metadata, path: key });\n            return ret;\n        }\n        else {\n            return undefined;\n        }\n    }\n    async mget(keys) {\n        const ret = keys.map((key) => this._get(key));\n        return await Promise.all(ret);\n    }\n    async _del(key) {\n        const connection = this.buildDeleteConnection(key);\n        const options = {};\n        await connection.request(options);\n    }\n    async mdelete(keys) {\n        const ret = keys.map((key) => this._del(key));\n        await Promise.all(ret);\n    }\n    // eslint-disable-next-line require-yield\n    async *yieldKeys(_prefix) {\n        // TODO: Implement. Most have an implementation that uses nextToken.\n        throw new Error(\"yieldKeys is not implemented\");\n    }\n}\nexports.BlobStoreGoogle = BlobStoreGoogle;\nclass GoogleCloudStorageUri {\n    constructor(uri) {\n        Object.defineProperty(this, \"bucket\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"path\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        const bucketAndPath = GoogleCloudStorageUri.uriToBucketAndPath(uri);\n        this.bucket = bucketAndPath.bucket;\n        this.path = bucketAndPath.path;\n    }\n    get uri() {\n        return `gs://${this.bucket}/${this.path}`;\n    }\n    get isValid() {\n        return (typeof this.bucket !== \"undefined\" && typeof this.path !== \"undefined\");\n    }\n    static uriToBucketAndPath(uri) {\n        const match = this.uriRegexp.exec(uri);\n        if (!match) {\n            throw new Error(`Invalid gs:// URI: ${uri}`);\n        }\n        return {\n            bucket: match[1],\n            path: match[2],\n        };\n    }\n    static isValidUri(uri) {\n        return this.uriRegexp.test(uri);\n    }\n}\nexports.GoogleCloudStorageUri = GoogleCloudStorageUri;\nObject.defineProperty(GoogleCloudStorageUri, \"uriRegexp\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: /gs:\\/\\/([a-z0-9][a-z0-9._-]+[a-z0-9])\\/(.*)/\n});\nclass GoogleCloudStorageUploadConnection extends GoogleMultipartUploadConnection {\n    constructor(fields, caller, client) {\n        super(fields, caller, client);\n        Object.defineProperty(this, \"uri\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.uri = new GoogleCloudStorageUri(fields.uri);\n    }\n    async buildUrl() {\n        return `https://storage.googleapis.com/upload/storage/${this.apiVersion}/b/${this.uri.bucket}/o?uploadType=multipart`;\n    }\n}\nexports.GoogleCloudStorageUploadConnection = GoogleCloudStorageUploadConnection;\nclass GoogleCloudStorageDownloadConnection extends GoogleDownloadConnection {\n    constructor(fields, caller, client) {\n        super(fields, caller, client);\n        Object.defineProperty(this, \"uri\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"method\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"alt\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.uri = new GoogleCloudStorageUri(fields.uri);\n        this.method = fields.method;\n        this.alt = fields.alt;\n    }\n    buildMethod() {\n        return this.method;\n    }\n    async buildUrl() {\n        const path = encodeURIComponent(this.uri.path);\n        const ret = `https://storage.googleapis.com/storage/${this.apiVersion}/b/${this.uri.bucket}/o/${path}`;\n        return this.alt ? `${ret}?alt=${this.alt}` : ret;\n    }\n}\nexports.GoogleCloudStorageDownloadConnection = GoogleCloudStorageDownloadConnection;\nclass GoogleCloudStorageRawConnection extends GoogleDownloadRawConnection {\n    constructor(fields, caller, client) {\n        super(fields, caller, client);\n        Object.defineProperty(this, \"uri\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.uri = new GoogleCloudStorageUri(fields.uri);\n    }\n    async buildUrl() {\n        const path = encodeURIComponent(this.uri.path);\n        const ret = `https://storage.googleapis.com/storage/${this.apiVersion}/b/${this.uri.bucket}/o/${path}?alt=media`;\n        return ret;\n    }\n}\nexports.GoogleCloudStorageRawConnection = GoogleCloudStorageRawConnection;\nclass BlobStoreGoogleCloudStorageBase extends BlobStoreGoogle {\n    constructor(fields) {\n        super(fields);\n        Object.defineProperty(this, \"params\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.params = fields;\n        this.defaultStoreOptions = {\n            ...this.defaultStoreOptions,\n            pathPrefix: fields.uriPrefix.uri,\n        };\n    }\n    buildSetConnection([key, _blob]) {\n        const params = {\n            ...this.params,\n            uri: key,\n        };\n        return new GoogleCloudStorageUploadConnection(params, this.caller, this.client);\n    }\n    buildSetMetadata([key, blob]) {\n        const uri = new GoogleCloudStorageUri(key);\n        const ret = {\n            name: uri.path,\n            metadata: blob.metadata,\n            contentType: blob.mimetype,\n        };\n        return ret;\n    }\n    buildGetMetadataConnection(key) {\n        const params = {\n            uri: key,\n            method: \"GET\",\n            alt: undefined,\n        };\n        return new GoogleCloudStorageDownloadConnection(params, this.caller, this.client);\n    }\n    buildGetDataConnection(key) {\n        const params = {\n            uri: key,\n        };\n        return new GoogleCloudStorageRawConnection(params, this.caller, this.client);\n    }\n    buildDeleteConnection(key) {\n        const params = {\n            uri: key,\n            method: \"DELETE\",\n            alt: undefined,\n        };\n        return new GoogleCloudStorageDownloadConnection(params, this.caller, this.client);\n    }\n}\nexports.BlobStoreGoogleCloudStorageBase = BlobStoreGoogleCloudStorageBase;\nclass AIStudioMediaBlob extends media_core_js_1.MediaBlob {\n    _valueAsDate(value) {\n        if (!value) {\n            return new Date(0);\n        }\n        return new Date(value);\n    }\n    _metadataFieldAsDate(field) {\n        return this._valueAsDate(this.metadata?.[field]);\n    }\n    get createDate() {\n        return this._metadataFieldAsDate(\"createTime\");\n    }\n    get updateDate() {\n        return this._metadataFieldAsDate(\"updateTime\");\n    }\n    get expirationDate() {\n        return this._metadataFieldAsDate(\"expirationTime\");\n    }\n    get isExpired() {\n        const now = new Date().toISOString();\n        const exp = this.metadata?.expirationTime ?? now;\n        return exp <= now;\n    }\n}\nexports.AIStudioMediaBlob = AIStudioMediaBlob;\nclass AIStudioFileUploadConnection extends GoogleMultipartUploadConnection {\n    constructor() {\n        super(...arguments);\n        Object.defineProperty(this, \"apiVersion\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"v1beta\"\n        });\n    }\n    async buildUrl() {\n        return `https://generativelanguage.googleapis.com/upload/${this.apiVersion}/files`;\n    }\n}\nexports.AIStudioFileUploadConnection = AIStudioFileUploadConnection;\nclass AIStudioFileDownloadConnection extends GoogleDownloadConnection {\n    constructor(fields, caller, client) {\n        super(fields, caller, client);\n        Object.defineProperty(this, \"method\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"apiVersion\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"v1beta\"\n        });\n        this.method = fields.method;\n        this.name = fields.name;\n    }\n    buildMethod() {\n        return this.method;\n    }\n    async buildUrl() {\n        return `https://generativelanguage.googleapis.com/${this.apiVersion}/files/${this.name}`;\n    }\n}\nexports.AIStudioFileDownloadConnection = AIStudioFileDownloadConnection;\nclass BlobStoreAIStudioFileBase extends BlobStoreGoogle {\n    constructor(fields) {\n        const params = {\n            defaultStoreOptions: {\n                pathPrefix: \"https://generativelanguage.googleapis.com/v1beta/files/\",\n                actionIfInvalid: \"removePath\",\n            },\n            ...fields,\n        };\n        super(params);\n        Object.defineProperty(this, \"params\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"retryTime\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 1000\n        });\n        this.params = params;\n        this.retryTime = params?.retryTime ?? this.retryTime ?? 1000;\n    }\n    _pathToName(path) {\n        return path.split(\"/\").pop() ?? path;\n    }\n    buildApiKeyClient(apiKey) {\n        return new auth_js_1.ApiKeyGoogleAuth(apiKey);\n    }\n    buildApiKey(fields) {\n        return fields?.apiKey ?? (0, env_1.getEnvironmentVariable)(\"GOOGLE_API_KEY\");\n    }\n    buildClient(fields) {\n        const apiKey = this.buildApiKey(fields);\n        if (apiKey) {\n            return this.buildApiKeyClient(apiKey);\n        }\n        else {\n            // TODO: Test that you can use OAuth to access\n            return this.buildAbstractedClient(fields);\n        }\n    }\n    async _regetMetadata(key) {\n        // Sleep for some time period\n        // eslint-disable-next-line no-promise-executor-return\n        await new Promise((resolve) => setTimeout(resolve, this.retryTime));\n        // Fetch the latest metadata\n        return this._getMetadata(key);\n    }\n    async _set([key, blob]) {\n        const response = (await super._set([\n            key,\n            blob,\n        ]));\n        let file = response.data?.file ?? { state: \"FAILED\" };\n        while (file.state === \"PROCESSING\" && file.uri && this.retryTime > 0) {\n            file = await this._regetMetadata(file.uri);\n        }\n        // The response should contain the name (and valid URI), so we need to\n        // update the blob with this. We can't return a new blob, since mset()\n        // doesn't return anything.\n        /* eslint-disable no-param-reassign */\n        blob.path = file.uri;\n        blob.metadata = {\n            ...blob.metadata,\n            ...file,\n        };\n        /* eslint-enable no-param-reassign */\n        return response;\n    }\n    buildSetConnection([_key, _blob]) {\n        return new AIStudioFileUploadConnection(this.params, this.caller, this.client);\n    }\n    buildSetMetadata([_key, _blob]) {\n        return {};\n    }\n    buildGetMetadataConnection(key) {\n        const params = {\n            ...this.params,\n            method: \"GET\",\n            name: this._pathToName(key),\n        };\n        return new AIStudioFileDownloadConnection(params, this.caller, this.client);\n    }\n    buildGetDataConnection(_key) {\n        throw new Error(\"AI Studio File API does not provide data\");\n    }\n    async _get(key) {\n        const metadata = await this._getMetadata(key);\n        if (metadata) {\n            const contentType = metadata?.mimeType ?? \"application/octet-stream\";\n            // TODO - Get the actual data (and other metadata) from an optional backing store\n            const data = {\n                value: \"\",\n                type: contentType,\n            };\n            return new media_core_js_1.MediaBlob({\n                path: key,\n                data,\n                metadata,\n            });\n        }\n        else {\n            return undefined;\n        }\n    }\n    buildDeleteConnection(key) {\n        const params = {\n            ...this.params,\n            method: \"DELETE\",\n            name: this._pathToName(key),\n        };\n        return new AIStudioFileDownloadConnection(params, this.caller, this.client);\n    }\n}\nexports.BlobStoreAIStudioFileBase = BlobStoreAIStudioFileBase;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/experimental/media.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/experimental/utils/media_core.cjs":
/*!***************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/experimental/utils/media_core.cjs ***!
  \***************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MediaManager = exports.DataBlobStore = exports.SimpleWebBlobStore = exports.ReadThroughBlobStore = exports.BackedBlobStore = exports.BlobStore = exports.MediaBlob = void 0;\nconst uuid_1 = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@10.0.0/node_modules/uuid/dist/esm-node/index.js\"); // FIXME - it is importing the wrong uuid, so v6 and v7 aren't implemented\nconst stores_1 = __webpack_require__(/*! @langchain/core/stores */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/stores.cjs\");\nconst serializable_1 = __webpack_require__(/*! @langchain/core/load/serializable */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/load/serializable.cjs\");\nfunction bytesToString(dataArray) {\n    // Need to handle the array in smaller chunks to deal with stack size limits\n    let ret = \"\";\n    const chunkSize = 102400;\n    for (let i = 0; i < dataArray.length; i += chunkSize) {\n        const chunk = dataArray.subarray(i, i + chunkSize);\n        ret += String.fromCharCode(...chunk);\n    }\n    return ret;\n}\n/**\n * Represents a chunk of data that can be identified by the path where the\n * data is (or will be) located, along with optional metadata about the data.\n */\nclass MediaBlob extends serializable_1.Serializable {\n    constructor(params) {\n        super(params);\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"lc_namespace\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: [\n                \"langchain\",\n                \"google_common\",\n                \"experimental\",\n                \"utils\",\n                \"media_core\",\n            ]\n        });\n        Object.defineProperty(this, \"data\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: {\n                value: \"\",\n                type: \"text/plain\",\n            }\n        });\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Object.defineProperty(this, \"metadata\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"path\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.data = params.data ?? this.data;\n        this.metadata = params.metadata;\n        this.path = params.path;\n    }\n    get size() {\n        return this.asBytes.length;\n    }\n    get dataType() {\n        return this.data?.type ?? \"\";\n    }\n    get encoding() {\n        const charsetEquals = this.dataType.indexOf(\"charset=\");\n        return charsetEquals === -1\n            ? \"utf-8\"\n            : this.dataType.substring(charsetEquals + 8);\n    }\n    get mimetype() {\n        const semicolon = this.dataType.indexOf(\";\");\n        return semicolon === -1\n            ? this.dataType\n            : this.dataType.substring(0, semicolon);\n    }\n    get asBytes() {\n        if (!this.data) {\n            return Uint8Array.from([]);\n        }\n        const binString = atob(this.data?.value);\n        const ret = new Uint8Array(binString.length);\n        for (let co = 0; co < binString.length; co += 1) {\n            ret[co] = binString.charCodeAt(co);\n        }\n        return ret;\n    }\n    async asString() {\n        return bytesToString(this.asBytes);\n    }\n    async asBase64() {\n        return this.data?.value ?? \"\";\n    }\n    async asDataUrl() {\n        return `data:${this.mimetype};base64,${await this.asBase64()}`;\n    }\n    async asUri() {\n        return this.path ?? (await this.asDataUrl());\n    }\n    async encode() {\n        const dataUrl = await this.asDataUrl();\n        const comma = dataUrl.indexOf(\",\");\n        const encoded = dataUrl.substring(comma + 1);\n        const encoding = dataUrl.indexOf(\"base64\") > -1 ? \"base64\" : \"8bit\";\n        return {\n            encoded,\n            encoding,\n        };\n    }\n    static fromDataUrl(url) {\n        if (!url.startsWith(\"data:\")) {\n            throw new Error(\"Not a data: URL\");\n        }\n        const colon = url.indexOf(\":\");\n        const semicolon = url.indexOf(\";\");\n        const mimeType = url.substring(colon + 1, semicolon);\n        const comma = url.indexOf(\",\");\n        const base64Data = url.substring(comma + 1);\n        const data = {\n            type: mimeType,\n            value: base64Data,\n        };\n        return new MediaBlob({\n            data,\n            path: url,\n        });\n    }\n    static async fromBlob(blob, other) {\n        const valueBuffer = await blob.arrayBuffer();\n        const valueArray = new Uint8Array(valueBuffer);\n        const valueStr = bytesToString(valueArray);\n        const value = btoa(valueStr);\n        return new MediaBlob({\n            ...other,\n            data: {\n                value,\n                type: blob.type,\n            },\n        });\n    }\n}\nexports.MediaBlob = MediaBlob;\n/**\n * A specialized Store that is designed to handle MediaBlobs and use the\n * key that is included in the blob to determine exactly how it is stored.\n *\n * The full details of a MediaBlob may be changed when it is stored.\n * For example, it may get additional or different Metadata. This should be\n * what is returned when the store() method is called.\n *\n * Although BlobStore extends BaseStore, not all of the methods from\n * BaseStore may be implemented (or even possible). Those that are not\n * implemented should be documented and throw an Error if called.\n */\nclass BlobStore extends stores_1.BaseStore {\n    constructor(opts) {\n        super(opts);\n        Object.defineProperty(this, \"lc_namespace\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: [\"langchain\", \"google-common\"]\n        }); // FIXME - What should this be? And why?\n        Object.defineProperty(this, \"defaultStoreOptions\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"defaultFetchOptions\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.defaultStoreOptions = opts?.defaultStoreOptions ?? {};\n        this.defaultFetchOptions = opts?.defaultFetchOptions ?? {};\n    }\n    async _realKey(key) {\n        return typeof key === \"string\" ? key : await key.asUri();\n    }\n    /**\n     * Is the path supported by this BlobStore?\n     *\n     * Although this is async, this is expected to be a relatively fast operation\n     * (ie - you shouldn't make network calls).\n     *\n     * @param path The path to check\n     * @param opts Any options (if needed) that may be used to determine if it is valid\n     * @return If the path is supported\n     */\n    hasValidPath(path, opts) {\n        const prefix = opts?.pathPrefix ?? \"\";\n        const isPrefixed = typeof path !== \"undefined\" && path.startsWith(prefix);\n        return Promise.resolve(isPrefixed);\n    }\n    _blobPathSuffix(blob) {\n        // Get the path currently set and make sure we treat it as a string\n        const blobPath = `${blob.path}`;\n        // Advance past the first set of /\n        let pathStart = blobPath.indexOf(\"/\") + 1;\n        while (blobPath.charAt(pathStart) === \"/\") {\n            pathStart += 1;\n        }\n        // We will use the rest as the path for a replacement\n        return blobPath.substring(pathStart);\n    }\n    async _newBlob(oldBlob, newPath) {\n        const oldPath = oldBlob.path;\n        const metadata = oldBlob?.metadata ?? {};\n        metadata.langchainOldPath = oldPath;\n        const newBlob = new MediaBlob({\n            ...oldBlob,\n            metadata,\n        });\n        if (newPath) {\n            newBlob.path = newPath;\n        }\n        else if (newBlob.path) {\n            delete newBlob.path;\n        }\n        return newBlob;\n    }\n    async _validBlobPrefixPath(blob, opts) {\n        const prefix = opts?.pathPrefix ?? \"\";\n        const suffix = this._blobPathSuffix(blob);\n        const newPath = `${prefix}${suffix}`;\n        return this._newBlob(blob, newPath);\n    }\n    _validBlobPrefixUuidFunction(name) {\n        switch (name) {\n            case \"prefixUuid1\":\n                return (0, uuid_1.v1)();\n            case \"prefixUuid4\":\n                return (0, uuid_1.v4)();\n            // case \"prefixUuid6\": return v6();\n            // case \"prefixUuid7\": return v7();\n            default:\n                throw new Error(`Unknown uuid function: ${name}`);\n        }\n    }\n    async _validBlobPrefixUuid(blob, opts) {\n        const prefix = opts?.pathPrefix ?? \"\";\n        const suffix = this._validBlobPrefixUuidFunction(opts?.actionIfInvalid ?? \"prefixUuid4\");\n        const newPath = `${prefix}${suffix}`;\n        return this._newBlob(blob, newPath);\n    }\n    async _validBlobRemovePath(blob, _opts) {\n        return this._newBlob(blob, undefined);\n    }\n    /**\n     * Based on the blob and options, return a blob that has a valid path\n     * that can be saved.\n     * @param blob\n     * @param opts\n     */\n    async _validStoreBlob(blob, opts) {\n        if (await this.hasValidPath(blob.path, opts)) {\n            return blob;\n        }\n        switch (opts?.actionIfInvalid) {\n            case \"ignore\":\n                return blob;\n            case \"prefixPath\":\n                return this._validBlobPrefixPath(blob, opts);\n            case \"prefixUuid1\":\n            case \"prefixUuid4\":\n            case \"prefixUuid6\":\n            case \"prefixUuid7\":\n                return this._validBlobPrefixUuid(blob, opts);\n            case \"removePath\":\n                return this._validBlobRemovePath(blob, opts);\n            default:\n                return undefined;\n        }\n    }\n    async store(blob, opts = {}) {\n        const allOpts = {\n            ...this.defaultStoreOptions,\n            ...opts,\n        };\n        const validBlob = await this._validStoreBlob(blob, allOpts);\n        if (typeof validBlob !== \"undefined\") {\n            const validKey = await validBlob.asUri();\n            await this.mset([[validKey, validBlob]]);\n            const savedKey = await validBlob.asUri();\n            return await this.fetch(savedKey);\n        }\n        return undefined;\n    }\n    async _missingFetchBlobEmpty(path, _opts) {\n        return new MediaBlob({ path });\n    }\n    async _missingFetchBlob(path, opts) {\n        switch (opts?.actionIfBlobMissing) {\n            case \"emptyBlob\":\n                return this._missingFetchBlobEmpty(path, opts);\n            default:\n                return undefined;\n        }\n    }\n    async fetch(key, opts = {}) {\n        const allOpts = {\n            ...this.defaultFetchOptions,\n            ...opts,\n        };\n        const realKey = await this._realKey(key);\n        const ret = await this.mget([realKey]);\n        return ret?.[0] ?? (await this._missingFetchBlob(realKey, allOpts));\n    }\n}\nexports.BlobStore = BlobStore;\nclass BackedBlobStore extends BlobStore {\n    constructor(opts) {\n        super(opts);\n        Object.defineProperty(this, \"backingStore\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.backingStore = opts.backingStore;\n    }\n    mdelete(keys) {\n        return this.backingStore.mdelete(keys);\n    }\n    mget(keys) {\n        return this.backingStore.mget(keys);\n    }\n    mset(keyValuePairs) {\n        return this.backingStore.mset(keyValuePairs);\n    }\n    yieldKeys(prefix) {\n        return this.backingStore.yieldKeys(prefix);\n    }\n}\nexports.BackedBlobStore = BackedBlobStore;\nclass ReadThroughBlobStore extends BlobStore {\n    constructor(opts) {\n        super(opts);\n        Object.defineProperty(this, \"baseStore\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"backingStore\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.baseStore = opts.baseStore;\n        this.backingStore = opts.backingStore;\n    }\n    async store(blob, opts = {}) {\n        const originalUri = await blob.asUri();\n        const newBlob = await this.backingStore.store(blob, opts);\n        if (newBlob) {\n            await this.baseStore.mset([[originalUri, newBlob]]);\n        }\n        return newBlob;\n    }\n    mdelete(keys) {\n        return this.baseStore.mdelete(keys);\n    }\n    mget(keys) {\n        return this.baseStore.mget(keys);\n    }\n    mset(_keyValuePairs) {\n        throw new Error(\"Do not call ReadThroughBlobStore.mset directly\");\n    }\n    yieldKeys(prefix) {\n        return this.baseStore.yieldKeys(prefix);\n    }\n}\nexports.ReadThroughBlobStore = ReadThroughBlobStore;\nclass SimpleWebBlobStore extends BlobStore {\n    _notImplementedException() {\n        throw new Error(\"Not implemented for SimpleWebBlobStore\");\n    }\n    async hasValidPath(path, _opts) {\n        return ((await super.hasValidPath(path, { pathPrefix: \"https://\" })) ||\n            (await super.hasValidPath(path, { pathPrefix: \"http://\" })));\n    }\n    async _fetch(url) {\n        const ret = new MediaBlob({\n            path: url,\n        });\n        const metadata = {};\n        const fetchOptions = {\n            method: \"GET\",\n        };\n        const res = await fetch(url, fetchOptions);\n        metadata.status = res.status;\n        const headers = {};\n        for (const [key, value] of res.headers.entries()) {\n            headers[key] = value;\n        }\n        metadata.headers = headers;\n        metadata.ok = res.ok;\n        if (res.ok) {\n            const resMediaBlob = await MediaBlob.fromBlob(await res.blob());\n            ret.data = resMediaBlob.data;\n        }\n        ret.metadata = metadata;\n        return ret;\n    }\n    async mget(keys) {\n        const blobMap = keys.map(this._fetch);\n        return await Promise.all(blobMap);\n    }\n    async mdelete(_keys) {\n        this._notImplementedException();\n    }\n    async mset(_keyValuePairs) {\n        this._notImplementedException();\n    }\n    async *yieldKeys(_prefix) {\n        this._notImplementedException();\n        yield \"\";\n    }\n}\nexports.SimpleWebBlobStore = SimpleWebBlobStore;\n/**\n * A blob \"store\" that works with data: URLs that will turn the URL into\n * a blob.\n */\nclass DataBlobStore extends BlobStore {\n    _notImplementedException() {\n        throw new Error(\"Not implemented for DataBlobStore\");\n    }\n    hasValidPath(path, _opts) {\n        return super.hasValidPath(path, { pathPrefix: \"data:\" });\n    }\n    _fetch(url) {\n        return MediaBlob.fromDataUrl(url);\n    }\n    async mget(keys) {\n        const blobMap = keys.map(this._fetch);\n        return blobMap;\n    }\n    async mdelete(_keys) {\n        this._notImplementedException();\n    }\n    async mset(_keyValuePairs) {\n        this._notImplementedException();\n    }\n    async *yieldKeys(_prefix) {\n        this._notImplementedException();\n        yield \"\";\n    }\n}\nexports.DataBlobStore = DataBlobStore;\n/**\n * Responsible for converting a URI (typically a web URL) into a MediaBlob.\n * Allows for aliasing / caching of the requested URI and what it resolves to.\n * This MediaBlob is expected to be usable to provide to an LLM, either\n * through the Base64 of the media or through a canonical URI that the LLM\n * supports.\n */\nclass MediaManager {\n    constructor(config) {\n        Object.defineProperty(this, \"store\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"resolvers\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.store = config.store;\n        this.resolvers = config.resolvers;\n    }\n    defaultResolvers() {\n        return [new DataBlobStore({}), new SimpleWebBlobStore({})];\n    }\n    async _isInvalid(blob) {\n        return typeof blob === \"undefined\";\n    }\n    /**\n     * Given the public URI, load what is at this URI and save it\n     * in the store.\n     * @param uri The URI to resolve using the resolver\n     * @return A canonical MediaBlob for this URI\n     */\n    async _resolveAndSave(uri) {\n        let resolvedBlob;\n        const resolvers = this.resolvers || this.defaultResolvers();\n        for (let co = 0; co < resolvers.length; co += 1) {\n            const resolver = resolvers[co];\n            if (await resolver.hasValidPath(uri)) {\n                resolvedBlob = await resolver.fetch(uri);\n            }\n        }\n        if (resolvedBlob) {\n            return await this.store.store(resolvedBlob);\n        }\n        else {\n            return new MediaBlob({});\n        }\n    }\n    async getMediaBlob(uri) {\n        const aliasBlob = await this.store.fetch(uri);\n        const ret = (await this._isInvalid(aliasBlob))\n            ? await this._resolveAndSave(uri)\n            : aliasBlob;\n        return ret;\n    }\n}\nexports.MediaManager = MediaManager;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/experimental/utils/media_core.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/index.cjs":
/*!***************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/index.cjs ***!
  \***************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./chat_models.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/chat_models.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./llms.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/llms.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./embeddings.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/embeddings.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./auth.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/auth.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./connection.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/connection.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./types.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/types.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./utils/stream.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/stream.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./utils/common.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/common.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./utils/zod_to_gemini_parameters.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/zod_to_gemini_parameters.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./utils/safety.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/safety.cjs\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/index.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/llms.cjs":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/llms.cjs ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GoogleBaseLLM = void 0;\nconst manager_1 = __webpack_require__(/*! @langchain/core/callbacks/manager */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/callbacks/manager.cjs\");\nconst llms_1 = __webpack_require__(/*! @langchain/core/language_models/llms */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/language_models/llms.cjs\");\nconst outputs_1 = __webpack_require__(/*! @langchain/core/outputs */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/outputs.cjs\");\nconst env_1 = __webpack_require__(/*! @langchain/core/utils/env */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/env.cjs\");\nconst connection_js_1 = __webpack_require__(/*! ./connection.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/connection.cjs\");\nconst common_js_1 = __webpack_require__(/*! ./utils/common.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/common.cjs\");\nconst gemini_js_1 = __webpack_require__(/*! ./utils/gemini.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/gemini.cjs\");\nconst auth_js_1 = __webpack_require__(/*! ./auth.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/auth.cjs\");\nconst failed_handler_js_1 = __webpack_require__(/*! ./utils/failed_handler.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/failed_handler.cjs\");\nconst chat_models_js_1 = __webpack_require__(/*! ./chat_models.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/chat_models.cjs\");\nclass GoogleLLMConnection extends connection_js_1.AbstractGoogleLLMConnection {\n    async formatContents(input, _parameters) {\n        const parts = await this.api.messageContentToParts(input);\n        const contents = [\n            {\n                role: \"user\",\n                parts,\n            },\n        ];\n        return contents;\n    }\n}\nclass ProxyChatGoogle extends chat_models_js_1.ChatGoogleBase {\n    constructor(fields) {\n        super(fields);\n    }\n    buildAbstractedClient(fields) {\n        return fields.connection.client;\n    }\n}\n/**\n * Integration with an LLM.\n */\nclass GoogleBaseLLM extends llms_1.LLM {\n    // Used for tracing, replace with the same name as your class\n    static lc_name() {\n        return \"GoogleLLM\";\n    }\n    get lc_secrets() {\n        return {\n            authOptions: \"GOOGLE_AUTH_OPTIONS\",\n        };\n    }\n    constructor(fields) {\n        super((0, failed_handler_js_1.ensureParams)(fields));\n        Object.defineProperty(this, \"originalFields\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"lc_serializable\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"modelName\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"gemini-pro\"\n        });\n        Object.defineProperty(this, \"model\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"gemini-pro\"\n        });\n        Object.defineProperty(this, \"temperature\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 0.7\n        });\n        Object.defineProperty(this, \"maxOutputTokens\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 1024\n        });\n        Object.defineProperty(this, \"topP\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 0.8\n        });\n        Object.defineProperty(this, \"topK\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 40\n        });\n        Object.defineProperty(this, \"stopSequences\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: []\n        });\n        Object.defineProperty(this, \"safetySettings\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: []\n        });\n        Object.defineProperty(this, \"safetyHandler\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"responseMimeType\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"text/plain\"\n        });\n        Object.defineProperty(this, \"connection\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"streamedConnection\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.originalFields = fields;\n        (0, common_js_1.copyAndValidateModelParamsInto)(fields, this);\n        this.safetyHandler =\n            fields?.safetyHandler ?? new gemini_js_1.DefaultGeminiSafetyHandler();\n        const client = this.buildClient(fields);\n        this.buildConnection(fields ?? {}, client);\n    }\n    buildApiKeyClient(apiKey) {\n        return new auth_js_1.ApiKeyGoogleAuth(apiKey);\n    }\n    buildApiKey(fields) {\n        return fields?.apiKey ?? (0, env_1.getEnvironmentVariable)(\"GOOGLE_API_KEY\");\n    }\n    buildClient(fields) {\n        const apiKey = this.buildApiKey(fields);\n        if (apiKey) {\n            return this.buildApiKeyClient(apiKey);\n        }\n        else {\n            return this.buildAbstractedClient(fields);\n        }\n    }\n    buildConnection(fields, client) {\n        this.connection = new GoogleLLMConnection({ ...fields, ...this }, this.caller, client, false);\n        this.streamedConnection = new GoogleLLMConnection({ ...fields, ...this }, this.caller, client, true);\n    }\n    get platform() {\n        return this.connection.platform;\n    }\n    // Replace\n    _llmType() {\n        return \"googlellm\";\n    }\n    formatPrompt(prompt) {\n        return prompt;\n    }\n    /**\n     * For some given input string and options, return a string output.\n     *\n     * Despite the fact that `invoke` is overridden below, we still need this\n     * in order to handle public APi calls to `generate()`.\n     */\n    async _call(prompt, options) {\n        const parameters = (0, common_js_1.copyAIModelParams)(this, options);\n        const result = await this.connection.request(prompt, parameters, options);\n        const ret = this.connection.api.safeResponseToString(result, this.safetyHandler);\n        return ret;\n    }\n    // Normally, you should not override this method and instead should override\n    // _streamResponseChunks. We are doing so here to allow for multimodal inputs into\n    // the LLM.\n    async *_streamIterator(input, options) {\n        // TODO: Refactor callback setup and teardown code into core\n        const prompt = llms_1.BaseLLM._convertInputToPromptValue(input);\n        const [runnableConfig, callOptions] = this._separateRunnableConfigFromCallOptions(options);\n        const callbackManager_ = await manager_1.CallbackManager.configure(runnableConfig.callbacks, this.callbacks, runnableConfig.tags, this.tags, runnableConfig.metadata, this.metadata, { verbose: this.verbose });\n        const extra = {\n            options: callOptions,\n            invocation_params: this?.invocationParams(callOptions),\n            batch_size: 1,\n        };\n        const runManagers = await callbackManager_?.handleLLMStart(this.toJSON(), [prompt.toString()], undefined, undefined, extra, undefined, undefined, runnableConfig.runName);\n        let generation = new outputs_1.GenerationChunk({\n            text: \"\",\n        });\n        const proxyChat = this.createProxyChat();\n        try {\n            for await (const chunk of proxyChat._streamIterator(input, options)) {\n                const stringValue = this.connection.api.chunkToString(chunk);\n                const generationChunk = new outputs_1.GenerationChunk({\n                    text: stringValue,\n                });\n                generation = generation.concat(generationChunk);\n                yield stringValue;\n            }\n        }\n        catch (err) {\n            await Promise.all((runManagers ?? []).map((runManager) => runManager?.handleLLMError(err)));\n            throw err;\n        }\n        await Promise.all((runManagers ?? []).map((runManager) => runManager?.handleLLMEnd({\n            generations: [[generation]],\n        })));\n    }\n    async predictMessages(messages, options, _callbacks) {\n        const { content } = messages[0];\n        const result = await this.connection.request(content, {}, options);\n        const ret = this.connection.api.safeResponseToBaseMessage(result, this.safetyHandler);\n        return ret;\n    }\n    /**\n     * Internal implementation detail to allow Google LLMs to support\n     * multimodal input by delegating to the chat model implementation.\n     *\n     * TODO: Replace with something less hacky.\n     */\n    createProxyChat() {\n        return new ProxyChatGoogle({\n            ...this.originalFields,\n            connection: this.connection,\n        });\n    }\n    // TODO: Remove the need to override this - we are doing it to\n    // allow the LLM to handle multimodal types of input.\n    async invoke(input, options) {\n        const stream = await this._streamIterator(input, options);\n        let generatedOutput = \"\";\n        for await (const chunk of stream) {\n            generatedOutput += chunk;\n        }\n        return generatedOutput;\n    }\n}\nexports.GoogleBaseLLM = GoogleBaseLLM;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtY29tbW9uQDAuMS4xX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fX3pvZEAzLjIzLjgvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vZ29vZ2xlLWNvbW1vbi9kaXN0L2xsbXMuY2pzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHFCQUFxQjtBQUNyQixrQkFBa0IsbUJBQU8sQ0FBQyxtTEFBbUM7QUFDN0QsZUFBZSxtQkFBTyxDQUFDLHlMQUFzQztBQUM3RCxrQkFBa0IsbUJBQU8sQ0FBQywrSkFBeUI7QUFDbkQsY0FBYyxtQkFBTyxDQUFDLG1LQUEyQjtBQUNqRCx3QkFBd0IsbUJBQU8sQ0FBQyxvTkFBa0I7QUFDbEQsb0JBQW9CLG1CQUFPLENBQUMsd05BQW9CO0FBQ2hELG9CQUFvQixtQkFBTyxDQUFDLHdOQUFvQjtBQUNoRCxrQkFBa0IsbUJBQU8sQ0FBQyx3TUFBWTtBQUN0Qyw0QkFBNEIsbUJBQU8sQ0FBQyx3T0FBNEI7QUFDaEUseUJBQXlCLG1CQUFPLENBQUMsc05BQW1CO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvREFBb0Qsb0JBQW9CO0FBQ3hFLDREQUE0RCxvQkFBb0I7QUFDaEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0xBQStMLHVCQUF1QjtBQUN0TjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZ0JBQWdCLFVBQVU7QUFDMUIsZ0VBQWdFO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BsYW5nY2hhaW4rZ29vZ2xlLWNvbW1vbkAwLjEuMV9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX196b2RAMy4yMy44L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2dvb2dsZS1jb21tb24vZGlzdC9sbG1zLmNqcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuR29vZ2xlQmFzZUxMTSA9IHZvaWQgMDtcbmNvbnN0IG1hbmFnZXJfMSA9IHJlcXVpcmUoXCJAbGFuZ2NoYWluL2NvcmUvY2FsbGJhY2tzL21hbmFnZXJcIik7XG5jb25zdCBsbG1zXzEgPSByZXF1aXJlKFwiQGxhbmdjaGFpbi9jb3JlL2xhbmd1YWdlX21vZGVscy9sbG1zXCIpO1xuY29uc3Qgb3V0cHV0c18xID0gcmVxdWlyZShcIkBsYW5nY2hhaW4vY29yZS9vdXRwdXRzXCIpO1xuY29uc3QgZW52XzEgPSByZXF1aXJlKFwiQGxhbmdjaGFpbi9jb3JlL3V0aWxzL2VudlwiKTtcbmNvbnN0IGNvbm5lY3Rpb25fanNfMSA9IHJlcXVpcmUoXCIuL2Nvbm5lY3Rpb24uY2pzXCIpO1xuY29uc3QgY29tbW9uX2pzXzEgPSByZXF1aXJlKFwiLi91dGlscy9jb21tb24uY2pzXCIpO1xuY29uc3QgZ2VtaW5pX2pzXzEgPSByZXF1aXJlKFwiLi91dGlscy9nZW1pbmkuY2pzXCIpO1xuY29uc3QgYXV0aF9qc18xID0gcmVxdWlyZShcIi4vYXV0aC5janNcIik7XG5jb25zdCBmYWlsZWRfaGFuZGxlcl9qc18xID0gcmVxdWlyZShcIi4vdXRpbHMvZmFpbGVkX2hhbmRsZXIuY2pzXCIpO1xuY29uc3QgY2hhdF9tb2RlbHNfanNfMSA9IHJlcXVpcmUoXCIuL2NoYXRfbW9kZWxzLmNqc1wiKTtcbmNsYXNzIEdvb2dsZUxMTUNvbm5lY3Rpb24gZXh0ZW5kcyBjb25uZWN0aW9uX2pzXzEuQWJzdHJhY3RHb29nbGVMTE1Db25uZWN0aW9uIHtcbiAgICBhc3luYyBmb3JtYXRDb250ZW50cyhpbnB1dCwgX3BhcmFtZXRlcnMpIHtcbiAgICAgICAgY29uc3QgcGFydHMgPSBhd2FpdCB0aGlzLmFwaS5tZXNzYWdlQ29udGVudFRvUGFydHMoaW5wdXQpO1xuICAgICAgICBjb25zdCBjb250ZW50cyA9IFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICByb2xlOiBcInVzZXJcIixcbiAgICAgICAgICAgICAgICBwYXJ0cyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgIF07XG4gICAgICAgIHJldHVybiBjb250ZW50cztcbiAgICB9XG59XG5jbGFzcyBQcm94eUNoYXRHb29nbGUgZXh0ZW5kcyBjaGF0X21vZGVsc19qc18xLkNoYXRHb29nbGVCYXNlIHtcbiAgICBjb25zdHJ1Y3RvcihmaWVsZHMpIHtcbiAgICAgICAgc3VwZXIoZmllbGRzKTtcbiAgICB9XG4gICAgYnVpbGRBYnN0cmFjdGVkQ2xpZW50KGZpZWxkcykge1xuICAgICAgICByZXR1cm4gZmllbGRzLmNvbm5lY3Rpb24uY2xpZW50O1xuICAgIH1cbn1cbi8qKlxuICogSW50ZWdyYXRpb24gd2l0aCBhbiBMTE0uXG4gKi9cbmNsYXNzIEdvb2dsZUJhc2VMTE0gZXh0ZW5kcyBsbG1zXzEuTExNIHtcbiAgICAvLyBVc2VkIGZvciB0cmFjaW5nLCByZXBsYWNlIHdpdGggdGhlIHNhbWUgbmFtZSBhcyB5b3VyIGNsYXNzXG4gICAgc3RhdGljIGxjX25hbWUoKSB7XG4gICAgICAgIHJldHVybiBcIkdvb2dsZUxMTVwiO1xuICAgIH1cbiAgICBnZXQgbGNfc2VjcmV0cygpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGF1dGhPcHRpb25zOiBcIkdPT0dMRV9BVVRIX09QVElPTlNcIixcbiAgICAgICAgfTtcbiAgICB9XG4gICAgY29uc3RydWN0b3IoZmllbGRzKSB7XG4gICAgICAgIHN1cGVyKCgwLCBmYWlsZWRfaGFuZGxlcl9qc18xLmVuc3VyZVBhcmFtcykoZmllbGRzKSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcIm9yaWdpbmFsRmllbGRzXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiB2b2lkIDBcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcImxjX3NlcmlhbGl6YWJsZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogdHJ1ZVxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibW9kZWxOYW1lXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiBcImdlbWluaS1wcm9cIlxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibW9kZWxcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IFwiZ2VtaW5pLXByb1wiXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJ0ZW1wZXJhdHVyZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogMC43XG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJtYXhPdXRwdXRUb2tlbnNcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IDEwMjRcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcInRvcFBcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IDAuOFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwidG9wS1wiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogNDBcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcInN0b3BTZXF1ZW5jZXNcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IFtdXG4gICAgICAgIH0pO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgXCJzYWZldHlTZXR0aW5nc1wiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogW11cbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcInNhZmV0eUhhbmRsZXJcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IHZvaWQgMFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwicmVzcG9uc2VNaW1lVHlwZVwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgICAgICAgICAgd3JpdGFibGU6IHRydWUsXG4gICAgICAgICAgICB2YWx1ZTogXCJ0ZXh0L3BsYWluXCJcbiAgICAgICAgfSk7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0aGlzLCBcImNvbm5lY3Rpb25cIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IHZvaWQgMFxuICAgICAgICB9KTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwic3RyZWFtZWRDb25uZWN0aW9uXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiB2b2lkIDBcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMub3JpZ2luYWxGaWVsZHMgPSBmaWVsZHM7XG4gICAgICAgICgwLCBjb21tb25fanNfMS5jb3B5QW5kVmFsaWRhdGVNb2RlbFBhcmFtc0ludG8pKGZpZWxkcywgdGhpcyk7XG4gICAgICAgIHRoaXMuc2FmZXR5SGFuZGxlciA9XG4gICAgICAgICAgICBmaWVsZHM/LnNhZmV0eUhhbmRsZXIgPz8gbmV3IGdlbWluaV9qc18xLkRlZmF1bHRHZW1pbmlTYWZldHlIYW5kbGVyKCk7XG4gICAgICAgIGNvbnN0IGNsaWVudCA9IHRoaXMuYnVpbGRDbGllbnQoZmllbGRzKTtcbiAgICAgICAgdGhpcy5idWlsZENvbm5lY3Rpb24oZmllbGRzID8/IHt9LCBjbGllbnQpO1xuICAgIH1cbiAgICBidWlsZEFwaUtleUNsaWVudChhcGlLZXkpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBhdXRoX2pzXzEuQXBpS2V5R29vZ2xlQXV0aChhcGlLZXkpO1xuICAgIH1cbiAgICBidWlsZEFwaUtleShmaWVsZHMpIHtcbiAgICAgICAgcmV0dXJuIGZpZWxkcz8uYXBpS2V5ID8/ICgwLCBlbnZfMS5nZXRFbnZpcm9ubWVudFZhcmlhYmxlKShcIkdPT0dMRV9BUElfS0VZXCIpO1xuICAgIH1cbiAgICBidWlsZENsaWVudChmaWVsZHMpIHtcbiAgICAgICAgY29uc3QgYXBpS2V5ID0gdGhpcy5idWlsZEFwaUtleShmaWVsZHMpO1xuICAgICAgICBpZiAoYXBpS2V5KSB7XG4gICAgICAgICAgICByZXR1cm4gdGhpcy5idWlsZEFwaUtleUNsaWVudChhcGlLZXkpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuYnVpbGRBYnN0cmFjdGVkQ2xpZW50KGZpZWxkcyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgYnVpbGRDb25uZWN0aW9uKGZpZWxkcywgY2xpZW50KSB7XG4gICAgICAgIHRoaXMuY29ubmVjdGlvbiA9IG5ldyBHb29nbGVMTE1Db25uZWN0aW9uKHsgLi4uZmllbGRzLCAuLi50aGlzIH0sIHRoaXMuY2FsbGVyLCBjbGllbnQsIGZhbHNlKTtcbiAgICAgICAgdGhpcy5zdHJlYW1lZENvbm5lY3Rpb24gPSBuZXcgR29vZ2xlTExNQ29ubmVjdGlvbih7IC4uLmZpZWxkcywgLi4udGhpcyB9LCB0aGlzLmNhbGxlciwgY2xpZW50LCB0cnVlKTtcbiAgICB9XG4gICAgZ2V0IHBsYXRmb3JtKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5jb25uZWN0aW9uLnBsYXRmb3JtO1xuICAgIH1cbiAgICAvLyBSZXBsYWNlXG4gICAgX2xsbVR5cGUoKSB7XG4gICAgICAgIHJldHVybiBcImdvb2dsZWxsbVwiO1xuICAgIH1cbiAgICBmb3JtYXRQcm9tcHQocHJvbXB0KSB7XG4gICAgICAgIHJldHVybiBwcm9tcHQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEZvciBzb21lIGdpdmVuIGlucHV0IHN0cmluZyBhbmQgb3B0aW9ucywgcmV0dXJuIGEgc3RyaW5nIG91dHB1dC5cbiAgICAgKlxuICAgICAqIERlc3BpdGUgdGhlIGZhY3QgdGhhdCBgaW52b2tlYCBpcyBvdmVycmlkZGVuIGJlbG93LCB3ZSBzdGlsbCBuZWVkIHRoaXNcbiAgICAgKiBpbiBvcmRlciB0byBoYW5kbGUgcHVibGljIEFQaSBjYWxscyB0byBgZ2VuZXJhdGUoKWAuXG4gICAgICovXG4gICAgYXN5bmMgX2NhbGwocHJvbXB0LCBvcHRpb25zKSB7XG4gICAgICAgIGNvbnN0IHBhcmFtZXRlcnMgPSAoMCwgY29tbW9uX2pzXzEuY29weUFJTW9kZWxQYXJhbXMpKHRoaXMsIG9wdGlvbnMpO1xuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB0aGlzLmNvbm5lY3Rpb24ucmVxdWVzdChwcm9tcHQsIHBhcmFtZXRlcnMsIG9wdGlvbnMpO1xuICAgICAgICBjb25zdCByZXQgPSB0aGlzLmNvbm5lY3Rpb24uYXBpLnNhZmVSZXNwb25zZVRvU3RyaW5nKHJlc3VsdCwgdGhpcy5zYWZldHlIYW5kbGVyKTtcbiAgICAgICAgcmV0dXJuIHJldDtcbiAgICB9XG4gICAgLy8gTm9ybWFsbHksIHlvdSBzaG91bGQgbm90IG92ZXJyaWRlIHRoaXMgbWV0aG9kIGFuZCBpbnN0ZWFkIHNob3VsZCBvdmVycmlkZVxuICAgIC8vIF9zdHJlYW1SZXNwb25zZUNodW5rcy4gV2UgYXJlIGRvaW5nIHNvIGhlcmUgdG8gYWxsb3cgZm9yIG11bHRpbW9kYWwgaW5wdXRzIGludG9cbiAgICAvLyB0aGUgTExNLlxuICAgIGFzeW5jICpfc3RyZWFtSXRlcmF0b3IoaW5wdXQsIG9wdGlvbnMpIHtcbiAgICAgICAgLy8gVE9ETzogUmVmYWN0b3IgY2FsbGJhY2sgc2V0dXAgYW5kIHRlYXJkb3duIGNvZGUgaW50byBjb3JlXG4gICAgICAgIGNvbnN0IHByb21wdCA9IGxsbXNfMS5CYXNlTExNLl9jb252ZXJ0SW5wdXRUb1Byb21wdFZhbHVlKGlucHV0KTtcbiAgICAgICAgY29uc3QgW3J1bm5hYmxlQ29uZmlnLCBjYWxsT3B0aW9uc10gPSB0aGlzLl9zZXBhcmF0ZVJ1bm5hYmxlQ29uZmlnRnJvbUNhbGxPcHRpb25zKG9wdGlvbnMpO1xuICAgICAgICBjb25zdCBjYWxsYmFja01hbmFnZXJfID0gYXdhaXQgbWFuYWdlcl8xLkNhbGxiYWNrTWFuYWdlci5jb25maWd1cmUocnVubmFibGVDb25maWcuY2FsbGJhY2tzLCB0aGlzLmNhbGxiYWNrcywgcnVubmFibGVDb25maWcudGFncywgdGhpcy50YWdzLCBydW5uYWJsZUNvbmZpZy5tZXRhZGF0YSwgdGhpcy5tZXRhZGF0YSwgeyB2ZXJib3NlOiB0aGlzLnZlcmJvc2UgfSk7XG4gICAgICAgIGNvbnN0IGV4dHJhID0ge1xuICAgICAgICAgICAgb3B0aW9uczogY2FsbE9wdGlvbnMsXG4gICAgICAgICAgICBpbnZvY2F0aW9uX3BhcmFtczogdGhpcz8uaW52b2NhdGlvblBhcmFtcyhjYWxsT3B0aW9ucyksXG4gICAgICAgICAgICBiYXRjaF9zaXplOiAxLFxuICAgICAgICB9O1xuICAgICAgICBjb25zdCBydW5NYW5hZ2VycyA9IGF3YWl0IGNhbGxiYWNrTWFuYWdlcl8/LmhhbmRsZUxMTVN0YXJ0KHRoaXMudG9KU09OKCksIFtwcm9tcHQudG9TdHJpbmcoKV0sIHVuZGVmaW5lZCwgdW5kZWZpbmVkLCBleHRyYSwgdW5kZWZpbmVkLCB1bmRlZmluZWQsIHJ1bm5hYmxlQ29uZmlnLnJ1bk5hbWUpO1xuICAgICAgICBsZXQgZ2VuZXJhdGlvbiA9IG5ldyBvdXRwdXRzXzEuR2VuZXJhdGlvbkNodW5rKHtcbiAgICAgICAgICAgIHRleHQ6IFwiXCIsXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBwcm94eUNoYXQgPSB0aGlzLmNyZWF0ZVByb3h5Q2hhdCgpO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgZm9yIGF3YWl0IChjb25zdCBjaHVuayBvZiBwcm94eUNoYXQuX3N0cmVhbUl0ZXJhdG9yKGlucHV0LCBvcHRpb25zKSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHN0cmluZ1ZhbHVlID0gdGhpcy5jb25uZWN0aW9uLmFwaS5jaHVua1RvU3RyaW5nKGNodW5rKTtcbiAgICAgICAgICAgICAgICBjb25zdCBnZW5lcmF0aW9uQ2h1bmsgPSBuZXcgb3V0cHV0c18xLkdlbmVyYXRpb25DaHVuayh7XG4gICAgICAgICAgICAgICAgICAgIHRleHQ6IHN0cmluZ1ZhbHVlLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGdlbmVyYXRpb24gPSBnZW5lcmF0aW9uLmNvbmNhdChnZW5lcmF0aW9uQ2h1bmspO1xuICAgICAgICAgICAgICAgIHlpZWxkIHN0cmluZ1ZhbHVlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgIGF3YWl0IFByb21pc2UuYWxsKChydW5NYW5hZ2VycyA/PyBbXSkubWFwKChydW5NYW5hZ2VyKSA9PiBydW5NYW5hZ2VyPy5oYW5kbGVMTE1FcnJvcihlcnIpKSk7XG4gICAgICAgICAgICB0aHJvdyBlcnI7XG4gICAgICAgIH1cbiAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoKHJ1bk1hbmFnZXJzID8/IFtdKS5tYXAoKHJ1bk1hbmFnZXIpID0+IHJ1bk1hbmFnZXI/LmhhbmRsZUxMTUVuZCh7XG4gICAgICAgICAgICBnZW5lcmF0aW9uczogW1tnZW5lcmF0aW9uXV0sXG4gICAgICAgIH0pKSk7XG4gICAgfVxuICAgIGFzeW5jIHByZWRpY3RNZXNzYWdlcyhtZXNzYWdlcywgb3B0aW9ucywgX2NhbGxiYWNrcykge1xuICAgICAgICBjb25zdCB7IGNvbnRlbnQgfSA9IG1lc3NhZ2VzWzBdO1xuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB0aGlzLmNvbm5lY3Rpb24ucmVxdWVzdChjb250ZW50LCB7fSwgb3B0aW9ucyk7XG4gICAgICAgIGNvbnN0IHJldCA9IHRoaXMuY29ubmVjdGlvbi5hcGkuc2FmZVJlc3BvbnNlVG9CYXNlTWVzc2FnZShyZXN1bHQsIHRoaXMuc2FmZXR5SGFuZGxlcik7XG4gICAgICAgIHJldHVybiByZXQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEludGVybmFsIGltcGxlbWVudGF0aW9uIGRldGFpbCB0byBhbGxvdyBHb29nbGUgTExNcyB0byBzdXBwb3J0XG4gICAgICogbXVsdGltb2RhbCBpbnB1dCBieSBkZWxlZ2F0aW5nIHRvIHRoZSBjaGF0IG1vZGVsIGltcGxlbWVudGF0aW9uLlxuICAgICAqXG4gICAgICogVE9ETzogUmVwbGFjZSB3aXRoIHNvbWV0aGluZyBsZXNzIGhhY2t5LlxuICAgICAqL1xuICAgIGNyZWF0ZVByb3h5Q2hhdCgpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBQcm94eUNoYXRHb29nbGUoe1xuICAgICAgICAgICAgLi4udGhpcy5vcmlnaW5hbEZpZWxkcyxcbiAgICAgICAgICAgIGNvbm5lY3Rpb246IHRoaXMuY29ubmVjdGlvbixcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIC8vIFRPRE86IFJlbW92ZSB0aGUgbmVlZCB0byBvdmVycmlkZSB0aGlzIC0gd2UgYXJlIGRvaW5nIGl0IHRvXG4gICAgLy8gYWxsb3cgdGhlIExMTSB0byBoYW5kbGUgbXVsdGltb2RhbCB0eXBlcyBvZiBpbnB1dC5cbiAgICBhc3luYyBpbnZva2UoaW5wdXQsIG9wdGlvbnMpIHtcbiAgICAgICAgY29uc3Qgc3RyZWFtID0gYXdhaXQgdGhpcy5fc3RyZWFtSXRlcmF0b3IoaW5wdXQsIG9wdGlvbnMpO1xuICAgICAgICBsZXQgZ2VuZXJhdGVkT3V0cHV0ID0gXCJcIjtcbiAgICAgICAgZm9yIGF3YWl0IChjb25zdCBjaHVuayBvZiBzdHJlYW0pIHtcbiAgICAgICAgICAgIGdlbmVyYXRlZE91dHB1dCArPSBjaHVuaztcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZ2VuZXJhdGVkT3V0cHV0O1xuICAgIH1cbn1cbmV4cG9ydHMuR29vZ2xlQmFzZUxMTSA9IEdvb2dsZUJhc2VMTE07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/llms.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/types.cjs":
/*!***************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/types.cjs ***!
  \***************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtY29tbW9uQDAuMS4xX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fX3pvZEAzLjIzLjgvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vZ29vZ2xlLWNvbW1vbi9kaXN0L3R5cGVzLmNqcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BsYW5nY2hhaW4rZ29vZ2xlLWNvbW1vbkAwLjEuMV9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX196b2RAMy4yMy44L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2dvb2dsZS1jb21tb24vZGlzdC90eXBlcy5janMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/types.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/common.cjs":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/common.cjs ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.copyAndValidateModelParamsInto = exports.validateModelParams = exports.modelToFamily = exports.copyAIModelParamsInto = exports.convertToGeminiTools = exports.copyAIModelParams = void 0;\nconst base_1 = __webpack_require__(/*! @langchain/core/language_models/base */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/language_models/base.cjs\");\nconst function_calling_1 = __webpack_require__(/*! @langchain/core/utils/function_calling */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/utils/function_calling.cjs\");\nconst gemini_js_1 = __webpack_require__(/*! ./gemini.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/gemini.cjs\");\nconst zod_to_gemini_parameters_js_1 = __webpack_require__(/*! ./zod_to_gemini_parameters.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/zod_to_gemini_parameters.cjs\");\nfunction copyAIModelParams(params, options) {\n    return copyAIModelParamsInto(params, options, {});\n}\nexports.copyAIModelParams = copyAIModelParams;\nfunction processToolChoice(toolChoice, allowedFunctionNames) {\n    if (!toolChoice) {\n        if (allowedFunctionNames) {\n            // Allowed func names is passed, return 'any' so it forces the model to use a tool.\n            return {\n                tool_choice: \"any\",\n                allowed_function_names: allowedFunctionNames,\n            };\n        }\n        return undefined;\n    }\n    if (toolChoice === \"any\" || toolChoice === \"auto\" || toolChoice === \"none\") {\n        return {\n            tool_choice: toolChoice,\n            allowed_function_names: allowedFunctionNames,\n        };\n    }\n    if (typeof toolChoice === \"string\") {\n        // String representing the function name.\n        // Return any to force the model to predict the specified function call.\n        return {\n            tool_choice: \"any\",\n            allowed_function_names: [...(allowedFunctionNames ?? []), toolChoice],\n        };\n    }\n    throw new Error(\"Object inputs for tool_choice not supported.\");\n}\nfunction convertToGeminiTools(tools) {\n    const geminiTools = [\n        {\n            functionDeclarations: [],\n        },\n    ];\n    tools.forEach((tool) => {\n        if (\"functionDeclarations\" in tool &&\n            Array.isArray(tool.functionDeclarations)) {\n            const funcs = tool.functionDeclarations;\n            geminiTools[0].functionDeclarations?.push(...funcs);\n        }\n        else if ((0, function_calling_1.isLangChainTool)(tool)) {\n            const jsonSchema = (0, zod_to_gemini_parameters_js_1.zodToGeminiParameters)(tool.schema);\n            geminiTools[0].functionDeclarations?.push({\n                name: tool.name,\n                description: tool.description ?? `A function available to call.`,\n                parameters: jsonSchema,\n            });\n        }\n        else if ((0, base_1.isOpenAITool)(tool)) {\n            geminiTools[0].functionDeclarations?.push({\n                name: tool.function.name,\n                description: tool.function.description ?? `A function available to call.`,\n                parameters: (0, zod_to_gemini_parameters_js_1.jsonSchemaToGeminiParameters)(tool.function.parameters),\n            });\n        }\n    });\n    return geminiTools;\n}\nexports.convertToGeminiTools = convertToGeminiTools;\nfunction copyAIModelParamsInto(params, options, target) {\n    const ret = target || {};\n    const model = options?.model ?? params?.model ?? target.model;\n    ret.modelName =\n        model ?? options?.modelName ?? params?.modelName ?? target.modelName;\n    ret.model = model;\n    ret.temperature =\n        options?.temperature ?? params?.temperature ?? target.temperature;\n    ret.maxOutputTokens =\n        options?.maxOutputTokens ??\n            params?.maxOutputTokens ??\n            target.maxOutputTokens;\n    ret.topP = options?.topP ?? params?.topP ?? target.topP;\n    ret.topK = options?.topK ?? params?.topK ?? target.topK;\n    ret.stopSequences =\n        options?.stopSequences ?? params?.stopSequences ?? target.stopSequences;\n    ret.safetySettings =\n        options?.safetySettings ?? params?.safetySettings ?? target.safetySettings;\n    ret.convertSystemMessageToHumanContent =\n        options?.convertSystemMessageToHumanContent ??\n            params?.convertSystemMessageToHumanContent ??\n            target?.convertSystemMessageToHumanContent;\n    ret.responseMimeType =\n        options?.responseMimeType ??\n            params?.responseMimeType ??\n            target?.responseMimeType;\n    ret.streaming = options?.streaming ?? params?.streaming ?? target?.streaming;\n    const toolChoice = processToolChoice(options?.tool_choice, options?.allowed_function_names);\n    if (toolChoice) {\n        ret.tool_choice = toolChoice.tool_choice;\n        ret.allowed_function_names = toolChoice.allowed_function_names;\n    }\n    const tools = options?.tools;\n    if (tools) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        ret.tools = convertToGeminiTools(tools);\n    }\n    return ret;\n}\nexports.copyAIModelParamsInto = copyAIModelParamsInto;\nfunction modelToFamily(modelName) {\n    if (!modelName) {\n        return null;\n    }\n    else if ((0, gemini_js_1.isModelGemini)(modelName)) {\n        return \"gemini\";\n    }\n    else {\n        return null;\n    }\n}\nexports.modelToFamily = modelToFamily;\nfunction validateModelParams(params) {\n    const testParams = params ?? {};\n    const model = testParams.model ?? testParams.modelName;\n    switch (modelToFamily(model)) {\n        case \"gemini\":\n            return (0, gemini_js_1.validateGeminiParams)(testParams);\n        default:\n            throw new Error(`Unable to verify model params: ${JSON.stringify(params)}`);\n    }\n}\nexports.validateModelParams = validateModelParams;\nfunction copyAndValidateModelParamsInto(params, target) {\n    copyAIModelParamsInto(params, undefined, target);\n    validateModelParams(target);\n    return target;\n}\nexports.copyAndValidateModelParamsInto = copyAndValidateModelParamsInto;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/common.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/failed_handler.cjs":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/failed_handler.cjs ***!
  \******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ensureParams = exports.failedAttemptHandler = void 0;\nconst STATUS_NO_RETRY = [\n    400,\n    401,\n    402,\n    403,\n    404,\n    405,\n    406,\n    407,\n    408,\n    409, // Conflict\n];\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction failedAttemptHandler(error) {\n    const status = error?.response?.status ?? 0;\n    if (status === 0) {\n        // What is this?\n        console.error(\"failedAttemptHandler\", error);\n    }\n    // What errors shouldn't be retried?\n    if (STATUS_NO_RETRY.includes(+status)) {\n        throw error;\n    }\n    throw error;\n}\nexports.failedAttemptHandler = failedAttemptHandler;\nfunction ensureParams(params) {\n    const base = params ?? {};\n    return {\n        onFailedAttempt: failedAttemptHandler,\n        ...base,\n    };\n}\nexports.ensureParams = ensureParams;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/failed_handler.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/gemini.cjs":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/gemini.cjs ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MessageGeminiSafetyHandler = exports.DefaultGeminiSafetyHandler = exports.isModelGemini = exports.validateGeminiParams = exports.getGeminiAPI = void 0;\nconst uuid_1 = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@10.0.0/node_modules/uuid/dist/esm-node/index.js\");\nconst messages_1 = __webpack_require__(/*! @langchain/core/messages */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/messages.cjs\");\nconst outputs_1 = __webpack_require__(/*! @langchain/core/outputs */ \"(rsc)/./node_modules/.pnpm/@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8_/node_modules/@langchain/core/outputs.cjs\");\nconst safety_js_1 = __webpack_require__(/*! ./safety.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/safety.cjs\");\nconst extractMimeType = (str) => {\n    if (str.startsWith(\"data:\")) {\n        return {\n            mimeType: str.split(\":\")[1].split(\";\")[0],\n            data: str.split(\",\")[1],\n        };\n    }\n    return null;\n};\nfunction getGeminiAPI(config) {\n    function messageContentText(content) {\n        if (content?.text && content?.text.length > 0) {\n            return {\n                text: content.text,\n            };\n        }\n        else {\n            return null;\n        }\n    }\n    function messageContentImageUrl(content) {\n        const url = typeof content.image_url === \"string\"\n            ? content.image_url\n            : content.image_url.url;\n        if (!url) {\n            throw new Error(\"Missing Image URL\");\n        }\n        const mineTypeAndData = extractMimeType(url);\n        if (mineTypeAndData) {\n            return {\n                inlineData: mineTypeAndData,\n            };\n        }\n        else {\n            // FIXME - need some way to get mime type\n            return {\n                fileData: {\n                    mimeType: \"image/png\",\n                    fileUri: url,\n                },\n            };\n        }\n    }\n    async function blobToFileData(blob) {\n        return {\n            fileData: {\n                fileUri: blob.path,\n                mimeType: blob.mimetype,\n            },\n        };\n    }\n    async function fileUriContentToBlob(uri) {\n        return config?.mediaManager?.getMediaBlob(uri);\n    }\n    async function messageContentMedia(\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    content) {\n        if (\"mimeType\" in content && \"data\" in content) {\n            return {\n                inlineData: {\n                    mimeType: content.mimeType,\n                    data: content.data,\n                },\n            };\n        }\n        else if (\"mimeType\" in content && \"fileUri\" in content) {\n            return {\n                fileData: {\n                    mimeType: content.mimeType,\n                    fileUri: content.fileUri,\n                },\n            };\n        }\n        else {\n            const uri = content.fileUri;\n            const blob = await fileUriContentToBlob(uri);\n            if (blob) {\n                return await blobToFileData(blob);\n            }\n        }\n        throw new Error(\"Invalid media content\");\n    }\n    async function messageContentComplexToPart(content) {\n        switch (content.type) {\n            case \"text\":\n                if (\"text\" in content) {\n                    return messageContentText(content);\n                }\n                break;\n            case \"image_url\":\n                if (\"image_url\" in content) {\n                    // Type guard for MessageContentImageUrl\n                    return messageContentImageUrl(content);\n                }\n                break;\n            case \"media\":\n                return await messageContentMedia(content);\n            default:\n                throw new Error(`Unsupported type received while converting message to message parts`);\n        }\n        throw new Error(`Cannot coerce \"${content.type}\" message part into a string.`);\n    }\n    async function messageContentComplexToParts(content) {\n        const contents = content.map(messageContentComplexToPart);\n        return Promise.all(contents);\n    }\n    async function messageContentToParts(content) {\n        // Convert a string to a text type MessageContent if needed\n        const messageContent = typeof content === \"string\"\n            ? [\n                {\n                    type: \"text\",\n                    text: content,\n                },\n            ]\n            : content;\n        // Get all of the parts, even those that don't correctly resolve\n        const allParts = await messageContentComplexToParts(messageContent);\n        // Remove any invalid parts\n        const parts = allParts.reduce((acc, val) => {\n            if (val) {\n                return [...acc, val];\n            }\n            else {\n                return acc;\n            }\n        }, []);\n        return parts;\n    }\n    function messageToolCallsToParts(toolCalls) {\n        if (!toolCalls || toolCalls.length === 0) {\n            return [];\n        }\n        return toolCalls.map((tool) => {\n            let args = {};\n            if (tool?.function?.arguments) {\n                const argStr = tool.function.arguments;\n                args = JSON.parse(argStr);\n            }\n            return {\n                functionCall: {\n                    name: tool.function.name,\n                    args,\n                },\n            };\n        });\n    }\n    function messageKwargsToParts(kwargs) {\n        const ret = [];\n        if (kwargs?.tool_calls) {\n            ret.push(...messageToolCallsToParts(kwargs.tool_calls));\n        }\n        return ret;\n    }\n    async function roleMessageToContent(role, message) {\n        const contentParts = await messageContentToParts(message.content);\n        let toolParts;\n        if ((0, messages_1.isAIMessage)(message) && !!message.tool_calls?.length) {\n            toolParts = message.tool_calls.map((toolCall) => ({\n                functionCall: {\n                    name: toolCall.name,\n                    args: toolCall.args,\n                },\n            }));\n        }\n        else {\n            toolParts = messageKwargsToParts(message.additional_kwargs);\n        }\n        const parts = [...contentParts, ...toolParts];\n        return [\n            {\n                role,\n                parts,\n            },\n        ];\n    }\n    async function systemMessageToContent(message, useSystemInstruction) {\n        return useSystemInstruction\n            ? roleMessageToContent(\"system\", message)\n            : [\n                ...(await roleMessageToContent(\"user\", message)),\n                ...(await roleMessageToContent(\"model\", new messages_1.AIMessage(\"Ok\"))),\n            ];\n    }\n    function toolMessageToContent(message, prevMessage) {\n        const contentStr = typeof message.content === \"string\"\n            ? message.content\n            : message.content.reduce((acc, content) => {\n                if (content.type === \"text\") {\n                    return acc + content.text;\n                }\n                else {\n                    return acc;\n                }\n            }, \"\");\n        // Hacky :(\n        const responseName = ((0, messages_1.isAIMessage)(prevMessage) && !!prevMessage.tool_calls?.length\n            ? prevMessage.tool_calls[0].name\n            : prevMessage.name) ?? message.tool_call_id;\n        try {\n            const content = JSON.parse(contentStr);\n            return [\n                {\n                    role: \"function\",\n                    parts: [\n                        {\n                            functionResponse: {\n                                name: responseName,\n                                response: { content },\n                            },\n                        },\n                    ],\n                },\n            ];\n        }\n        catch (_) {\n            return [\n                {\n                    role: \"function\",\n                    parts: [\n                        {\n                            functionResponse: {\n                                name: responseName,\n                                response: { content: contentStr },\n                            },\n                        },\n                    ],\n                },\n            ];\n        }\n    }\n    async function baseMessageToContent(message, prevMessage, useSystemInstruction) {\n        const type = message._getType();\n        switch (type) {\n            case \"system\":\n                return systemMessageToContent(message, useSystemInstruction);\n            case \"human\":\n                return roleMessageToContent(\"user\", message);\n            case \"ai\":\n                return roleMessageToContent(\"model\", message);\n            case \"tool\":\n                if (!prevMessage) {\n                    throw new Error(\"Tool messages cannot be the first message passed to the model.\");\n                }\n                return toolMessageToContent(message, prevMessage);\n            default:\n                console.log(`Unsupported message type: ${type}`);\n                return [];\n        }\n    }\n    function textPartToMessageContent(part) {\n        return {\n            type: \"text\",\n            text: part.text,\n        };\n    }\n    function inlineDataPartToMessageContent(part) {\n        return {\n            type: \"image_url\",\n            image_url: `data:${part.inlineData.mimeType};base64,${part.inlineData.data}`,\n        };\n    }\n    function fileDataPartToMessageContent(part) {\n        return {\n            type: \"image_url\",\n            image_url: part.fileData.fileUri,\n        };\n    }\n    function partsToMessageContent(parts) {\n        return parts\n            .map((part) => {\n            if (part === undefined || part === null) {\n                return null;\n            }\n            else if (\"text\" in part) {\n                return textPartToMessageContent(part);\n            }\n            else if (\"inlineData\" in part) {\n                return inlineDataPartToMessageContent(part);\n            }\n            else if (\"fileData\" in part) {\n                return fileDataPartToMessageContent(part);\n            }\n            else {\n                return null;\n            }\n        })\n            .reduce((acc, content) => {\n            if (content) {\n                acc.push(content);\n            }\n            return acc;\n        }, []);\n    }\n    function toolRawToTool(raw) {\n        return {\n            id: raw.id,\n            type: raw.type,\n            function: {\n                name: raw.function.name,\n                arguments: JSON.stringify(raw.function.arguments),\n            },\n        };\n    }\n    function functionCallPartToToolRaw(part) {\n        return {\n            id: (0, uuid_1.v4)().replace(/-/g, \"\"),\n            type: \"function\",\n            function: {\n                name: part.functionCall.name,\n                arguments: part.functionCall.args ?? {},\n            },\n        };\n    }\n    function partsToToolsRaw(parts) {\n        return parts\n            .map((part) => {\n            if (part === undefined || part === null) {\n                return null;\n            }\n            else if (\"functionCall\" in part) {\n                return functionCallPartToToolRaw(part);\n            }\n            else {\n                return null;\n            }\n        })\n            .reduce((acc, content) => {\n            if (content) {\n                acc.push(content);\n            }\n            return acc;\n        }, []);\n    }\n    function toolsRawToTools(raws) {\n        return raws.map((raw) => toolRawToTool(raw));\n    }\n    function responseToGenerateContentResponseData(response) {\n        if (\"nextChunk\" in response.data) {\n            throw new Error(\"Cannot convert Stream to GenerateContentResponseData\");\n        }\n        else if (Array.isArray(response.data)) {\n            // Collapse the array of response data as if it was a single one\n            return response.data.reduce((acc, val) => {\n                // Add all the parts\n                // FIXME: Handle other candidates?\n                const valParts = val?.candidates?.[0]?.content?.parts ?? [];\n                acc.candidates[0].content.parts.push(...valParts);\n                // FIXME: Merge promptFeedback and safety settings\n                acc.promptFeedback = val.promptFeedback;\n                return acc;\n            });\n        }\n        else {\n            return response.data;\n        }\n    }\n    function responseToParts(response) {\n        const responseData = responseToGenerateContentResponseData(response);\n        const parts = responseData?.candidates?.[0]?.content?.parts ?? [];\n        return parts;\n    }\n    function partToText(part) {\n        return \"text\" in part ? part.text : \"\";\n    }\n    function responseToString(response) {\n        const parts = responseToParts(response);\n        const ret = parts.reduce((acc, part) => {\n            const val = partToText(part);\n            return acc + val;\n        }, \"\");\n        return ret;\n    }\n    function safeResponseTo(response, safetyHandler, responseTo) {\n        try {\n            const safeResponse = safetyHandler.handle(response);\n            return responseTo(safeResponse);\n        }\n        catch (xx) {\n            // eslint-disable-next-line no-instanceof/no-instanceof\n            if (xx instanceof safety_js_1.GoogleAISafetyError) {\n                const ret = responseTo(xx.response);\n                xx.reply = ret;\n            }\n            throw xx;\n        }\n    }\n    function safeResponseToString(response, safetyHandler) {\n        return safeResponseTo(response, safetyHandler, responseToString);\n    }\n    function responseToGenerationInfo(response) {\n        if (!Array.isArray(response.data)) {\n            return {};\n        }\n        const data = response.data[0];\n        return {\n            usage_metadata: {\n                prompt_token_count: data.usageMetadata?.promptTokenCount,\n                candidates_token_count: data.usageMetadata?.candidatesTokenCount,\n                total_token_count: data.usageMetadata?.totalTokenCount,\n            },\n            safety_ratings: data.candidates[0]?.safetyRatings?.map((rating) => ({\n                category: rating.category,\n                probability: rating.probability,\n                probability_score: rating.probabilityScore,\n                severity: rating.severity,\n                severity_score: rating.severityScore,\n            })),\n            finish_reason: data.candidates[0]?.finishReason,\n        };\n    }\n    function responseToChatGeneration(response) {\n        return new outputs_1.ChatGenerationChunk({\n            text: responseToString(response),\n            message: partToMessageChunk(responseToParts(response)[0]),\n            generationInfo: responseToGenerationInfo(response),\n        });\n    }\n    function safeResponseToChatGeneration(response, safetyHandler) {\n        return safeResponseTo(response, safetyHandler, responseToChatGeneration);\n    }\n    function chunkToString(chunk) {\n        if (chunk === null) {\n            return \"\";\n        }\n        else if (typeof chunk.content === \"string\") {\n            return chunk.content;\n        }\n        else if (chunk.content.length === 0) {\n            return \"\";\n        }\n        else if (chunk.content[0].type === \"text\") {\n            return chunk.content[0].text;\n        }\n        else {\n            throw new Error(`Unexpected chunk: ${chunk}`);\n        }\n    }\n    function partToMessageChunk(part) {\n        const fields = partsToBaseMessageChunkFields([part]);\n        if (typeof fields.content === \"string\") {\n            return new messages_1.AIMessageChunk(fields);\n        }\n        else if (fields.content.every((item) => item.type === \"text\")) {\n            const newContent = fields.content\n                .map((item) => (\"text\" in item ? item.text : \"\"))\n                .join(\"\");\n            return new messages_1.AIMessageChunk({\n                ...fields,\n                content: newContent,\n            });\n        }\n        return new messages_1.AIMessageChunk(fields);\n    }\n    function partToChatGeneration(part) {\n        const message = partToMessageChunk(part);\n        const text = partToText(part);\n        return new outputs_1.ChatGenerationChunk({\n            text,\n            message,\n        });\n    }\n    function responseToChatGenerations(response) {\n        const parts = responseToParts(response);\n        if (parts.length === 0) {\n            return [];\n        }\n        let ret = parts.map((part) => partToChatGeneration(part));\n        if (ret.every((item) => typeof item.message.content === \"string\")) {\n            const combinedContent = ret.map((item) => item.message.content).join(\"\");\n            const combinedText = ret.map((item) => item.text).join(\"\");\n            const toolCallChunks = ret[ret.length - 1]?.message.additional_kwargs?.tool_calls?.map((toolCall, i) => ({\n                name: toolCall.function.name,\n                args: toolCall.function.arguments,\n                id: toolCall.id,\n                index: i,\n                type: \"tool_call_chunk\",\n            }));\n            let usageMetadata;\n            if (\"usageMetadata\" in response.data) {\n                usageMetadata = {\n                    input_tokens: response.data.usageMetadata.promptTokenCount,\n                    output_tokens: response.data.usageMetadata\n                        .candidatesTokenCount,\n                    total_tokens: response.data.usageMetadata.totalTokenCount,\n                };\n            }\n            ret = [\n                new outputs_1.ChatGenerationChunk({\n                    message: new messages_1.AIMessageChunk({\n                        content: combinedContent,\n                        additional_kwargs: ret[ret.length - 1]?.message.additional_kwargs,\n                        tool_call_chunks: toolCallChunks,\n                        usage_metadata: usageMetadata,\n                    }),\n                    text: combinedText,\n                    generationInfo: ret[ret.length - 1].generationInfo,\n                }),\n            ];\n        }\n        return ret;\n    }\n    function responseToBaseMessageFields(response) {\n        const parts = responseToParts(response);\n        return partsToBaseMessageChunkFields(parts);\n    }\n    function partsToBaseMessageChunkFields(parts) {\n        const fields = {\n            content: partsToMessageContent(parts),\n            tool_call_chunks: [],\n            tool_calls: [],\n            invalid_tool_calls: [],\n        };\n        const rawTools = partsToToolsRaw(parts);\n        if (rawTools.length > 0) {\n            const tools = toolsRawToTools(rawTools);\n            for (const tool of tools) {\n                fields.tool_call_chunks?.push({\n                    name: tool.function.name,\n                    args: tool.function.arguments,\n                    id: tool.id,\n                    type: \"tool_call_chunk\",\n                });\n                try {\n                    fields.tool_calls?.push({\n                        name: tool.function.name,\n                        args: JSON.parse(tool.function.arguments),\n                        id: tool.id,\n                    });\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                }\n                catch (e) {\n                    fields.invalid_tool_calls?.push({\n                        name: tool.function.name,\n                        args: tool.function.arguments,\n                        id: tool.id,\n                        error: e.message,\n                        type: \"invalid_tool_call\",\n                    });\n                }\n            }\n            fields.additional_kwargs = {\n                tool_calls: tools,\n            };\n        }\n        return fields;\n    }\n    function responseToBaseMessage(response) {\n        const fields = responseToBaseMessageFields(response);\n        return new messages_1.AIMessage(fields);\n    }\n    function safeResponseToBaseMessage(response, safetyHandler) {\n        return safeResponseTo(response, safetyHandler, responseToBaseMessage);\n    }\n    function responseToChatResult(response) {\n        const generations = responseToChatGenerations(response);\n        return {\n            generations,\n            llmOutput: responseToGenerationInfo(response),\n        };\n    }\n    function safeResponseToChatResult(response, safetyHandler) {\n        return safeResponseTo(response, safetyHandler, responseToChatResult);\n    }\n    return {\n        messageContentToParts,\n        baseMessageToContent,\n        safeResponseToString,\n        safeResponseToChatGeneration,\n        chunkToString,\n        safeResponseToBaseMessage,\n        safeResponseToChatResult,\n    };\n}\nexports.getGeminiAPI = getGeminiAPI;\nfunction validateGeminiParams(params) {\n    if (params.maxOutputTokens && params.maxOutputTokens < 0) {\n        throw new Error(\"`maxOutputTokens` must be a positive integer\");\n    }\n    if (params.temperature &&\n        (params.temperature < 0 || params.temperature > 2)) {\n        throw new Error(\"`temperature` must be in the range of [0.0,2.0]\");\n    }\n    if (params.topP && (params.topP < 0 || params.topP > 1)) {\n        throw new Error(\"`topP` must be in the range of [0.0,1.0]\");\n    }\n    if (params.topK && params.topK < 0) {\n        throw new Error(\"`topK` must be a positive integer\");\n    }\n}\nexports.validateGeminiParams = validateGeminiParams;\nfunction isModelGemini(modelName) {\n    return modelName.toLowerCase().startsWith(\"gemini\");\n}\nexports.isModelGemini = isModelGemini;\nclass DefaultGeminiSafetyHandler {\n    constructor(settings) {\n        Object.defineProperty(this, \"errorFinish\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: [\"SAFETY\", \"RECITATION\", \"OTHER\"]\n        });\n        this.errorFinish = settings?.errorFinish ?? this.errorFinish;\n    }\n    handleDataPromptFeedback(response, data) {\n        // Check to see if our prompt was blocked in the first place\n        const promptFeedback = data?.promptFeedback;\n        const blockReason = promptFeedback?.blockReason;\n        if (blockReason) {\n            throw new safety_js_1.GoogleAISafetyError(response, `Prompt blocked: ${blockReason}`);\n        }\n        return data;\n    }\n    handleDataFinishReason(response, data) {\n        const firstCandidate = data?.candidates?.[0];\n        const finishReason = firstCandidate?.finishReason;\n        if (this.errorFinish.includes(finishReason)) {\n            throw new safety_js_1.GoogleAISafetyError(response, `Finish reason: ${finishReason}`);\n        }\n        return data;\n    }\n    handleData(response, data) {\n        let ret = data;\n        ret = this.handleDataPromptFeedback(response, ret);\n        ret = this.handleDataFinishReason(response, ret);\n        return ret;\n    }\n    handle(response) {\n        let newdata;\n        if (\"nextChunk\" in response.data) {\n            // TODO: This is a stream. How to handle?\n            newdata = response.data;\n        }\n        else if (Array.isArray(response.data)) {\n            // If it is an array, try to handle every item in the array\n            try {\n                newdata = response.data.map((item) => this.handleData(response, item));\n            }\n            catch (xx) {\n                // eslint-disable-next-line no-instanceof/no-instanceof\n                if (xx instanceof safety_js_1.GoogleAISafetyError) {\n                    throw new safety_js_1.GoogleAISafetyError(response, xx.message);\n                }\n                else {\n                    throw xx;\n                }\n            }\n        }\n        else {\n            const data = response.data;\n            newdata = this.handleData(response, data);\n        }\n        return {\n            ...response,\n            data: newdata,\n        };\n    }\n}\nexports.DefaultGeminiSafetyHandler = DefaultGeminiSafetyHandler;\nclass MessageGeminiSafetyHandler extends DefaultGeminiSafetyHandler {\n    constructor(settings) {\n        super(settings);\n        Object.defineProperty(this, \"msg\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\"\n        });\n        Object.defineProperty(this, \"forceNewMessage\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: false\n        });\n        this.msg = settings?.msg ?? this.msg;\n        this.forceNewMessage = settings?.forceNewMessage ?? this.forceNewMessage;\n    }\n    setMessage(data) {\n        const ret = data;\n        if (this.forceNewMessage ||\n            !data?.candidates?.[0]?.content?.parts?.length) {\n            ret.candidates = data.candidates ?? [];\n            ret.candidates[0] = data.candidates[0] ?? {};\n            ret.candidates[0].content = data.candidates[0].content ?? {};\n            ret.candidates[0].content = {\n                role: \"model\",\n                parts: [{ text: this.msg }],\n            };\n        }\n        return ret;\n    }\n    handleData(response, data) {\n        try {\n            return super.handleData(response, data);\n        }\n        catch (xx) {\n            return this.setMessage(data);\n        }\n    }\n}\nexports.MessageGeminiSafetyHandler = MessageGeminiSafetyHandler;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/gemini.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/index.cjs":
/*!*********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/index.cjs ***!
  \*********************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./common.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/common.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./failed_handler.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/failed_handler.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./gemini.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/gemini.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./zod_to_gemini_parameters.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/zod_to_gemini_parameters.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./palm.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/palm.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./safety.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/safety.cjs\"), exports);\n__exportStar(__webpack_require__(/*! ./stream.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/stream.cjs\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/index.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/palm.cjs":
/*!********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/palm.cjs ***!
  \********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtY29tbW9uQDAuMS4xX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fX3pvZEAzLjIzLjgvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vZ29vZ2xlLWNvbW1vbi9kaXN0L3V0aWxzL3BhbG0uY2pzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtY29tbW9uQDAuMS4xX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fX3pvZEAzLjIzLjgvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vZ29vZ2xlLWNvbW1vbi9kaXN0L3V0aWxzL3BhbG0uY2pzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/palm.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/safety.cjs":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/safety.cjs ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GoogleAISafetyError = void 0;\nclass GoogleAISafetyError extends Error {\n    constructor(response, message) {\n        super(message);\n        Object.defineProperty(this, \"response\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Object.defineProperty(this, \"reply\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\"\n        });\n        this.response = response;\n    }\n}\nexports.GoogleAISafetyError = GoogleAISafetyError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtY29tbW9uQDAuMS4xX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fX3pvZEAzLjIzLjgvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vZ29vZ2xlLWNvbW1vbi9kaXN0L3V0aWxzL3NhZmV0eS5janMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BsYW5nY2hhaW4rZ29vZ2xlLWNvbW1vbkAwLjEuMV9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX196b2RAMy4yMy44L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2dvb2dsZS1jb21tb24vZGlzdC91dGlscy9zYWZldHkuY2pzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5Hb29nbGVBSVNhZmV0eUVycm9yID0gdm9pZCAwO1xuY2xhc3MgR29vZ2xlQUlTYWZldHlFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcihyZXNwb25zZSwgbWVzc2FnZSkge1xuICAgICAgICBzdXBlcihtZXNzYWdlKTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwicmVzcG9uc2VcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IHZvaWQgMFxuICAgICAgICB9KTtcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby1leHBsaWNpdC1hbnlcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwicmVwbHlcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgICAgICAgdmFsdWU6IFwiXCJcbiAgICAgICAgfSk7XG4gICAgICAgIHRoaXMucmVzcG9uc2UgPSByZXNwb25zZTtcbiAgICB9XG59XG5leHBvcnRzLkdvb2dsZUFJU2FmZXR5RXJyb3IgPSBHb29nbGVBSVNhZmV0eUVycm9yO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/safety.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/stream.cjs":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/stream.cjs ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ReadableJsonStream = exports.ComplexJsonStream = exports.JsonStream = exports.simpleValue = exports.complexValue = void 0;\nfunction complexValue(value) {\n    if (value === null || typeof value === \"undefined\") {\n        // I dunno what to put here. An error, probably\n        return undefined;\n    }\n    else if (typeof value === \"object\") {\n        if (Array.isArray(value)) {\n            return {\n                list_val: value.map((avalue) => complexValue(avalue)),\n            };\n        }\n        else {\n            const ret = {};\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const v = value;\n            Object.keys(v).forEach((key) => {\n                ret[key] = complexValue(v[key]);\n            });\n            return { struct_val: ret };\n        }\n    }\n    else if (typeof value === \"number\") {\n        if (Number.isInteger(value)) {\n            return { int_val: value };\n        }\n        else {\n            return { float_val: value };\n        }\n    }\n    else {\n        return {\n            string_val: [value],\n        };\n    }\n}\nexports.complexValue = complexValue;\nfunction simpleValue(val) {\n    if (val && typeof val === \"object\" && !Array.isArray(val)) {\n        // eslint-disable-next-line no-prototype-builtins\n        if (val.hasOwnProperty(\"stringVal\")) {\n            return val.stringVal[0];\n            // eslint-disable-next-line no-prototype-builtins\n        }\n        else if (val.hasOwnProperty(\"boolVal\")) {\n            return val.boolVal[0];\n            // eslint-disable-next-line no-prototype-builtins\n        }\n        else if (val.hasOwnProperty(\"listVal\")) {\n            const { listVal } = val;\n            return listVal.map((aval) => simpleValue(aval));\n            // eslint-disable-next-line no-prototype-builtins\n        }\n        else if (val.hasOwnProperty(\"structVal\")) {\n            const ret = {};\n            const struct = val.structVal;\n            Object.keys(struct).forEach((key) => {\n                ret[key] = simpleValue(struct[key]);\n            });\n            return ret;\n        }\n        else {\n            const ret = {};\n            const struct = val;\n            Object.keys(struct).forEach((key) => {\n                ret[key] = simpleValue(struct[key]);\n            });\n            return ret;\n        }\n    }\n    else if (Array.isArray(val)) {\n        return val.map((aval) => simpleValue(aval));\n    }\n    else {\n        return val;\n    }\n}\nexports.simpleValue = simpleValue;\nclass JsonStream {\n    constructor() {\n        Object.defineProperty(this, \"_buffer\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: \"\"\n        });\n        Object.defineProperty(this, \"_bufferOpen\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"_firstRun\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        // Set up a potential Promise that the handler can resolve.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Object.defineProperty(this, \"_chunkResolution\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        // If there is no Promise (it is null), the handler must add it to the queue\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Object.defineProperty(this, \"_chunkPending\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: null\n        });\n        // A queue that will collect chunks while there is no Promise\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Object.defineProperty(this, \"_chunkQueue\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: []\n        });\n    }\n    /**\n     * Add data to the buffer. This may cause chunks to be generated, if available.\n     * @param data\n     */\n    appendBuffer(data) {\n        this._buffer += data;\n        // Our first time, skip to the opening of the array\n        if (this._firstRun) {\n            this._skipTo(\"[\");\n            this._firstRun = false;\n        }\n        this._parseBuffer();\n    }\n    /**\n     * Indicate there is no more data that will be added to the text buffer.\n     * This should be called when all the data has been read and added to indicate\n     * that we should process everything remaining in the buffer.\n     */\n    closeBuffer() {\n        this._bufferOpen = false;\n        this._parseBuffer();\n    }\n    /**\n     * Skip characters in the buffer till we get to the start of an object.\n     * Then attempt to read a full object.\n     * If we do read a full object, turn it into a chunk and send it to the chunk handler.\n     * Repeat this for as much as we can.\n     */\n    _parseBuffer() {\n        let obj = null;\n        do {\n            this._skipTo(\"{\");\n            obj = this._getFullObject();\n            if (obj !== null) {\n                const chunk = this._simplifyObject(obj);\n                this._handleChunk(chunk);\n            }\n        } while (obj !== null);\n        if (!this._bufferOpen) {\n            // No more data will be added, and we have parsed everything we could,\n            // so everything else is garbage.\n            this._handleChunk(null);\n            this._buffer = \"\";\n        }\n    }\n    /**\n     * If the string is present, move the start of the buffer to the first occurrence\n     * of that string. This is useful for skipping over elements or parts that we're not\n     * really interested in parsing. (ie - the opening characters, comma separators, etc.)\n     * @param start The string to start the buffer with\n     */\n    _skipTo(start) {\n        const index = this._buffer.indexOf(start);\n        if (index > 0) {\n            this._buffer = this._buffer.slice(index);\n        }\n    }\n    /**\n     * Given what is in the buffer, parse a single object out of it.\n     * If a complete object isn't available, return null.\n     * Assumes that we are at the start of an object to parse.\n     */\n    _getFullObject() {\n        let ret = null;\n        // Loop while we don't have something to return AND we have something in the buffer\n        let index = 0;\n        while (ret === null && this._buffer.length > index) {\n            // Advance to the next close bracket after our current index\n            index = this._buffer.indexOf(\"}\", index + 1);\n            // If we don't find one, exit with null\n            if (index === -1) {\n                return null;\n            }\n            // If we have one, try to turn it into an object to return\n            try {\n                const objStr = this._buffer.substring(0, index + 1);\n                ret = JSON.parse(objStr);\n                // We only get here if it parsed it ok\n                // If we did turn it into an object, remove it from the buffer\n                this._buffer = this._buffer.slice(index + 1);\n            }\n            catch (xx) {\n                // It didn't parse it correctly, so we swallow the exception and continue\n            }\n        }\n        return ret;\n    }\n    _simplifyObject(obj) {\n        return obj;\n    }\n    /**\n     * Register that we have another chunk available for consumption.\n     * If we are waiting for a chunk, resolve the promise waiting for it immediately.\n     * If not, then add it to the queue.\n     * @param chunk\n     */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    _handleChunk(chunk) {\n        if (this._chunkPending) {\n            this._chunkResolution(chunk);\n            this._chunkPending = null;\n        }\n        else {\n            this._chunkQueue.push(chunk);\n        }\n    }\n    /**\n     * Get the next chunk that is coming from the stream.\n     * This chunk may be null, usually indicating the last chunk in the stream.\n     */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    async nextChunk() {\n        if (this._chunkQueue.length > 0) {\n            // If there is data in the queue, return the next queue chunk\n            return this._chunkQueue.shift();\n        }\n        else {\n            // Otherwise, set up a promise that handleChunk will cause to be resolved\n            this._chunkPending = new Promise((resolve) => {\n                this._chunkResolution = resolve;\n            });\n            return this._chunkPending;\n        }\n    }\n    /**\n     * Is the stream done?\n     * A stream is only done if all of the following are true:\n     * - There is no more data to be added to the text buffer\n     * - There is no more data in the text buffer\n     * - There are no chunks that are waiting to be consumed\n     */\n    get streamDone() {\n        return (!this._bufferOpen &&\n            this._buffer.length === 0 &&\n            this._chunkQueue.length === 0 &&\n            this._chunkPending === null);\n    }\n}\nexports.JsonStream = JsonStream;\nclass ComplexJsonStream extends JsonStream {\n    _simplifyObject(obj) {\n        return simpleValue(obj);\n    }\n}\nexports.ComplexJsonStream = ComplexJsonStream;\nclass ReadableJsonStream extends JsonStream {\n    constructor(body) {\n        super();\n        Object.defineProperty(this, \"decoder\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.decoder = new TextDecoder(\"utf-8\");\n        if (body) {\n            void this.run(body);\n        }\n        else {\n            console.error(\"Unexpected empty body while streaming\");\n        }\n    }\n    async run(body) {\n        const reader = body.getReader();\n        let isDone = false;\n        while (!isDone) {\n            const { value, done } = await reader.read();\n            if (!done) {\n                const svalue = this.decoder.decode(value, { stream: true });\n                this.appendBuffer(svalue);\n            }\n            else {\n                isDone = done;\n                this.closeBuffer();\n            }\n        }\n    }\n}\nexports.ReadableJsonStream = ReadableJsonStream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/stream.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/zod_to_gemini_parameters.cjs":
/*!****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/zod_to_gemini_parameters.cjs ***!
  \****************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n/* eslint-disable @typescript-eslint/no-unused-vars */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.jsonSchemaToGeminiParameters = exports.zodToGeminiParameters = exports.removeAdditionalProperties = void 0;\nconst zod_to_json_schema_1 = __webpack_require__(/*! zod-to-json-schema */ \"(rsc)/./node_modules/.pnpm/zod-to-json-schema@3.23.5_zod@3.23.8/node_modules/zod-to-json-schema/dist/cjs/index.js\");\nfunction removeAdditionalProperties(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nobj) {\n    if (typeof obj === \"object\" && obj !== null) {\n        const newObj = { ...obj };\n        if (\"additionalProperties\" in newObj) {\n            delete newObj.additionalProperties;\n        }\n        for (const key in newObj) {\n            if (key in newObj) {\n                if (Array.isArray(newObj[key])) {\n                    newObj[key] = newObj[key].map(removeAdditionalProperties);\n                }\n                else if (typeof newObj[key] === \"object\" && newObj[key] !== null) {\n                    newObj[key] = removeAdditionalProperties(newObj[key]);\n                }\n            }\n        }\n        return newObj;\n    }\n    return obj;\n}\nexports.removeAdditionalProperties = removeAdditionalProperties;\nfunction zodToGeminiParameters(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nzodObj) {\n    // Gemini doesn't accept either the $schema or additionalProperties\n    // attributes, so we need to explicitly remove them.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const jsonSchema = removeAdditionalProperties((0, zod_to_json_schema_1.zodToJsonSchema)(zodObj));\n    const { $schema, ...rest } = jsonSchema;\n    return rest;\n}\nexports.zodToGeminiParameters = zodToGeminiParameters;\nfunction jsonSchemaToGeminiParameters(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nschema) {\n    // Gemini doesn't accept either the $schema or additionalProperties\n    // attributes, so we need to explicitly remove them.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const jsonSchema = removeAdditionalProperties(schema);\n    const { $schema, ...rest } = jsonSchema;\n    return rest;\n}\nexports.jsonSchemaToGeminiParameters = jsonSchemaToGeminiParameters;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtY29tbW9uQDAuMS4xX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fX3pvZEAzLjIzLjgvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vZ29vZ2xlLWNvbW1vbi9kaXN0L3V0aWxzL3pvZF90b19nZW1pbmlfcGFyYW1ldGVycy5janMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxvQ0FBb0MsR0FBRyw2QkFBNkIsR0FBRyxrQ0FBa0M7QUFDekcsNkJBQTZCLG1CQUFPLENBQUMsNklBQW9CO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLG1CQUFtQjtBQUMvQjtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxtQkFBbUI7QUFDL0I7QUFDQTtBQUNBLG9DQUFvQyIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BsYW5nY2hhaW4rZ29vZ2xlLWNvbW1vbkAwLjEuMV9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX196b2RAMy4yMy44L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2dvb2dsZS1jb21tb24vZGlzdC91dGlscy96b2RfdG9fZ2VtaW5pX3BhcmFtZXRlcnMuY2pzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLyogZXNsaW50LWRpc2FibGUgQHR5cGVzY3JpcHQtZXNsaW50L25vLXVudXNlZC12YXJzICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmpzb25TY2hlbWFUb0dlbWluaVBhcmFtZXRlcnMgPSBleHBvcnRzLnpvZFRvR2VtaW5pUGFyYW1ldGVycyA9IGV4cG9ydHMucmVtb3ZlQWRkaXRpb25hbFByb3BlcnRpZXMgPSB2b2lkIDA7XG5jb25zdCB6b2RfdG9fanNvbl9zY2hlbWFfMSA9IHJlcXVpcmUoXCJ6b2QtdG8tanNvbi1zY2hlbWFcIik7XG5mdW5jdGlvbiByZW1vdmVBZGRpdGlvbmFsUHJvcGVydGllcyhcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZXhwbGljaXQtYW55XG5vYmopIHtcbiAgICBpZiAodHlwZW9mIG9iaiA9PT0gXCJvYmplY3RcIiAmJiBvYmogIT09IG51bGwpIHtcbiAgICAgICAgY29uc3QgbmV3T2JqID0geyAuLi5vYmogfTtcbiAgICAgICAgaWYgKFwiYWRkaXRpb25hbFByb3BlcnRpZXNcIiBpbiBuZXdPYmopIHtcbiAgICAgICAgICAgIGRlbGV0ZSBuZXdPYmouYWRkaXRpb25hbFByb3BlcnRpZXM7XG4gICAgICAgIH1cbiAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gbmV3T2JqKSB7XG4gICAgICAgICAgICBpZiAoa2V5IGluIG5ld09iaikge1xuICAgICAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KG5ld09ialtrZXldKSkge1xuICAgICAgICAgICAgICAgICAgICBuZXdPYmpba2V5XSA9IG5ld09ialtrZXldLm1hcChyZW1vdmVBZGRpdGlvbmFsUHJvcGVydGllcyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKHR5cGVvZiBuZXdPYmpba2V5XSA9PT0gXCJvYmplY3RcIiAmJiBuZXdPYmpba2V5XSAhPT0gbnVsbCkge1xuICAgICAgICAgICAgICAgICAgICBuZXdPYmpba2V5XSA9IHJlbW92ZUFkZGl0aW9uYWxQcm9wZXJ0aWVzKG5ld09ialtrZXldKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG5ld09iajtcbiAgICB9XG4gICAgcmV0dXJuIG9iajtcbn1cbmV4cG9ydHMucmVtb3ZlQWRkaXRpb25hbFByb3BlcnRpZXMgPSByZW1vdmVBZGRpdGlvbmFsUHJvcGVydGllcztcbmZ1bmN0aW9uIHpvZFRvR2VtaW5pUGFyYW1ldGVycyhcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZXhwbGljaXQtYW55XG56b2RPYmopIHtcbiAgICAvLyBHZW1pbmkgZG9lc24ndCBhY2NlcHQgZWl0aGVyIHRoZSAkc2NoZW1hIG9yIGFkZGl0aW9uYWxQcm9wZXJ0aWVzXG4gICAgLy8gYXR0cmlidXRlcywgc28gd2UgbmVlZCB0byBleHBsaWNpdGx5IHJlbW92ZSB0aGVtLlxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZXhwbGljaXQtYW55XG4gICAgY29uc3QganNvblNjaGVtYSA9IHJlbW92ZUFkZGl0aW9uYWxQcm9wZXJ0aWVzKCgwLCB6b2RfdG9fanNvbl9zY2hlbWFfMS56b2RUb0pzb25TY2hlbWEpKHpvZE9iaikpO1xuICAgIGNvbnN0IHsgJHNjaGVtYSwgLi4ucmVzdCB9ID0ganNvblNjaGVtYTtcbiAgICByZXR1cm4gcmVzdDtcbn1cbmV4cG9ydHMuem9kVG9HZW1pbmlQYXJhbWV0ZXJzID0gem9kVG9HZW1pbmlQYXJhbWV0ZXJzO1xuZnVuY3Rpb24ganNvblNjaGVtYVRvR2VtaW5pUGFyYW1ldGVycyhcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZXhwbGljaXQtYW55XG5zY2hlbWEpIHtcbiAgICAvLyBHZW1pbmkgZG9lc24ndCBhY2NlcHQgZWl0aGVyIHRoZSAkc2NoZW1hIG9yIGFkZGl0aW9uYWxQcm9wZXJ0aWVzXG4gICAgLy8gYXR0cmlidXRlcywgc28gd2UgbmVlZCB0byBleHBsaWNpdGx5IHJlbW92ZSB0aGVtLlxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tZXhwbGljaXQtYW55XG4gICAgY29uc3QganNvblNjaGVtYSA9IHJlbW92ZUFkZGl0aW9uYWxQcm9wZXJ0aWVzKHNjaGVtYSk7XG4gICAgY29uc3QgeyAkc2NoZW1hLCAuLi5yZXN0IH0gPSBqc29uU2NoZW1hO1xuICAgIHJldHVybiByZXN0O1xufVxuZXhwb3J0cy5qc29uU2NoZW1hVG9HZW1pbmlQYXJhbWV0ZXJzID0ganNvblNjaGVtYVRvR2VtaW5pUGFyYW1ldGVycztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/utils/zod_to_gemini_parameters.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/experimental/media.cjs":
/*!***********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/experimental/media.cjs ***!
  \***********************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ../dist/experimental/media.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/experimental/media.cjs\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtY29tbW9uQDAuMS4xX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fX3pvZEAzLjIzLjgvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vZ29vZ2xlLWNvbW1vbi9leHBlcmltZW50YWwvbWVkaWEuY2pzIiwibWFwcGluZ3MiOiJBQUFBLGdSQUEwRCIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BsYW5nY2hhaW4rZ29vZ2xlLWNvbW1vbkAwLjEuMV9AbGFuZ2NoYWluK2NvcmVAMC4zLjM5X29wZW5haUA0Ljg1LjFfd3NAOC4xOC4wX3pvZEAzLjIzLjhfX196b2RAMy4yMy44L25vZGVfbW9kdWxlcy9AbGFuZ2NoYWluL2dvb2dsZS1jb21tb24vZXhwZXJpbWVudGFsL21lZGlhLmNqcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4uL2Rpc3QvZXhwZXJpbWVudGFsL21lZGlhLmNqcycpOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/experimental/media.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/index.cjs":
/*!**********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/index.cjs ***!
  \**********************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/index.cjs */ \"(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/dist/index.cjs\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtY29tbW9uQDAuMS4xX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fX3pvZEAzLjIzLjgvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vZ29vZ2xlLWNvbW1vbi9pbmRleC5janMiLCJtYXBwaW5ncyI6IkFBQUEscVBBQTRDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGxhbmdjaGFpbitnb29nbGUtY29tbW9uQDAuMS4xX0BsYW5nY2hhaW4rY29yZUAwLjMuMzlfb3BlbmFpQDQuODUuMV93c0A4LjE4LjBfem9kQDMuMjMuOF9fX3pvZEAzLjIzLjgvbm9kZV9tb2R1bGVzL0BsYW5nY2hhaW4vZ29vZ2xlLWNvbW1vbi9pbmRleC5janMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvaW5kZXguY2pzJyk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@langchain+google-common@0.1.1_@langchain+core@0.3.39_openai@4.85.1_ws@8.18.0_zod@3.23.8___zod@3.23.8/node_modules/@langchain/google-common/index.cjs\n");

/***/ })

};
;