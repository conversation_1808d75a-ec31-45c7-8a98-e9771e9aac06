"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/camelcase@6.3.0";
exports.ids = ["vendor-chunks/camelcase@6.3.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/camelcase@6.3.0/node_modules/camelcase/index.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/camelcase@6.3.0/node_modules/camelcase/index.js ***!
  \****************************************************************************/
/***/ ((module) => {

eval("\n\nconst UPPERCASE = /[\\p{Lu}]/u;\nconst LOWERCASE = /[\\p{Ll}]/u;\nconst LEADING_CAPITAL = /^[\\p{Lu}](?![\\p{Lu}])/gu;\nconst IDENTIFIER = /([\\p{Alpha}\\p{N}_]|$)/u;\nconst SEPARATORS = /[_.\\- ]+/;\n\nconst LEADING_SEPARATORS = new RegExp('^' + SEPARATORS.source);\nconst SEPARATORS_AND_IDENTIFIER = new RegExp(SEPARATORS.source + IDENTIFIER.source, 'gu');\nconst NUMBERS_AND_IDENTIFIER = new RegExp('\\\\d+' + IDENTIFIER.source, 'gu');\n\nconst preserveCamelCase = (string, toLowerCase, toUpperCase) => {\n\tlet isLastCharLower = false;\n\tlet isLastCharUpper = false;\n\tlet isLastLastCharUpper = false;\n\n\tfor (let i = 0; i < string.length; i++) {\n\t\tconst character = string[i];\n\n\t\tif (isLastCharLower && UPPERCASE.test(character)) {\n\t\t\tstring = string.slice(0, i) + '-' + string.slice(i);\n\t\t\tisLastCharLower = false;\n\t\t\tisLastLastCharUpper = isLastCharUpper;\n\t\t\tisLastCharUpper = true;\n\t\t\ti++;\n\t\t} else if (isLastCharUpper && isLastLastCharUpper && LOWERCASE.test(character)) {\n\t\t\tstring = string.slice(0, i - 1) + '-' + string.slice(i - 1);\n\t\t\tisLastLastCharUpper = isLastCharUpper;\n\t\t\tisLastCharUpper = false;\n\t\t\tisLastCharLower = true;\n\t\t} else {\n\t\t\tisLastCharLower = toLowerCase(character) === character && toUpperCase(character) !== character;\n\t\t\tisLastLastCharUpper = isLastCharUpper;\n\t\t\tisLastCharUpper = toUpperCase(character) === character && toLowerCase(character) !== character;\n\t\t}\n\t}\n\n\treturn string;\n};\n\nconst preserveConsecutiveUppercase = (input, toLowerCase) => {\n\tLEADING_CAPITAL.lastIndex = 0;\n\n\treturn input.replace(LEADING_CAPITAL, m1 => toLowerCase(m1));\n};\n\nconst postProcess = (input, toUpperCase) => {\n\tSEPARATORS_AND_IDENTIFIER.lastIndex = 0;\n\tNUMBERS_AND_IDENTIFIER.lastIndex = 0;\n\n\treturn input.replace(SEPARATORS_AND_IDENTIFIER, (_, identifier) => toUpperCase(identifier))\n\t\t.replace(NUMBERS_AND_IDENTIFIER, m => toUpperCase(m));\n};\n\nconst camelCase = (input, options) => {\n\tif (!(typeof input === 'string' || Array.isArray(input))) {\n\t\tthrow new TypeError('Expected the input to be `string | string[]`');\n\t}\n\n\toptions = {\n\t\tpascalCase: false,\n\t\tpreserveConsecutiveUppercase: false,\n\t\t...options\n\t};\n\n\tif (Array.isArray(input)) {\n\t\tinput = input.map(x => x.trim())\n\t\t\t.filter(x => x.length)\n\t\t\t.join('-');\n\t} else {\n\t\tinput = input.trim();\n\t}\n\n\tif (input.length === 0) {\n\t\treturn '';\n\t}\n\n\tconst toLowerCase = options.locale === false ?\n\t\tstring => string.toLowerCase() :\n\t\tstring => string.toLocaleLowerCase(options.locale);\n\tconst toUpperCase = options.locale === false ?\n\t\tstring => string.toUpperCase() :\n\t\tstring => string.toLocaleUpperCase(options.locale);\n\n\tif (input.length === 1) {\n\t\treturn options.pascalCase ? toUpperCase(input) : toLowerCase(input);\n\t}\n\n\tconst hasUpperCase = input !== toLowerCase(input);\n\n\tif (hasUpperCase) {\n\t\tinput = preserveCamelCase(input, toLowerCase, toUpperCase);\n\t}\n\n\tinput = input.replace(LEADING_SEPARATORS, '');\n\n\tif (options.preserveConsecutiveUppercase) {\n\t\tinput = preserveConsecutiveUppercase(input, toLowerCase);\n\t} else {\n\t\tinput = toLowerCase(input);\n\t}\n\n\tif (options.pascalCase) {\n\t\tinput = toUpperCase(input.charAt(0)) + input.slice(1);\n\t}\n\n\treturn postProcess(input, toUpperCase);\n};\n\nmodule.exports = camelCase;\n// TODO: Remove this for the next major release\nmodule.exports[\"default\"] = camelCase;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/camelcase@6.3.0/node_modules/camelcase/index.js\n");

/***/ })

};
;