"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@smithy+middleware-endpoint@4.1.11";
exports.ids = ["vendor-chunks/@smithy+middleware-endpoint@4.1.11"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/createConfigValueProvider.js":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/createConfigValueProvider.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createConfigValueProvider: () => (/* binding */ createConfigValueProvider)\n/* harmony export */ });\nconst createConfigValueProvider = (configKey, canonicalEndpointParamKey, config) => {\n    const configProvider = async () => {\n        const configValue = config[configKey] ?? config[canonicalEndpointParamKey];\n        if (typeof configValue === \"function\") {\n            return configValue();\n        }\n        return configValue;\n    };\n    if (configKey === \"credentialScope\" || canonicalEndpointParamKey === \"CredentialScope\") {\n        return async () => {\n            const credentials = typeof config.credentials === \"function\" ? await config.credentials() : config.credentials;\n            const configValue = credentials?.credentialScope ?? credentials?.CredentialScope;\n            return configValue;\n        };\n    }\n    if (configKey === \"accountId\" || canonicalEndpointParamKey === \"AccountId\") {\n        return async () => {\n            const credentials = typeof config.credentials === \"function\" ? await config.credentials() : config.credentials;\n            const configValue = credentials?.accountId ?? credentials?.AccountId;\n            return configValue;\n        };\n    }\n    if (configKey === \"endpoint\" || canonicalEndpointParamKey === \"endpoint\") {\n        return async () => {\n            const endpoint = await configProvider();\n            if (endpoint && typeof endpoint === \"object\") {\n                if (\"url\" in endpoint) {\n                    return endpoint.url.href;\n                }\n                if (\"hostname\" in endpoint) {\n                    const { protocol, hostname, port, path } = endpoint;\n                    return `${protocol}//${hostname}${port ? \":\" + port : \"\"}${path}`;\n                }\n            }\n            return endpoint;\n        };\n    }\n    return configProvider;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/createConfigValueProvider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointFromConfig.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointFromConfig.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEndpointFromConfig: () => (/* binding */ getEndpointFromConfig)\n/* harmony export */ });\n/* harmony import */ var _smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/node-config-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+node-config-provider@4.1.3/node_modules/@smithy/node-config-provider/dist-es/index.js\");\n/* harmony import */ var _getEndpointUrlConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getEndpointUrlConfig */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointUrlConfig.js\");\n\n\nconst getEndpointFromConfig = async (serviceId) => (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_0__.loadConfig)((0,_getEndpointUrlConfig__WEBPACK_IMPORTED_MODULE_1__.getEndpointUrlConfig)(serviceId ?? \"\"))();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLWVuZHBvaW50QDQuMS4xMS9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvYWRhcHRvcnMvZ2V0RW5kcG9pbnRGcm9tQ29uZmlnLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEwRDtBQUNJO0FBQ3ZELG1EQUFtRCx3RUFBVSxDQUFDLDJFQUFvQiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrbWlkZGxld2FyZS1lbmRwb2ludEA0LjEuMTEvbm9kZV9tb2R1bGVzL0BzbWl0aHkvbWlkZGxld2FyZS1lbmRwb2ludC9kaXN0LWVzL2FkYXB0b3JzL2dldEVuZHBvaW50RnJvbUNvbmZpZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBsb2FkQ29uZmlnIH0gZnJvbSBcIkBzbWl0aHkvbm9kZS1jb25maWctcHJvdmlkZXJcIjtcbmltcG9ydCB7IGdldEVuZHBvaW50VXJsQ29uZmlnIH0gZnJvbSBcIi4vZ2V0RW5kcG9pbnRVcmxDb25maWdcIjtcbmV4cG9ydCBjb25zdCBnZXRFbmRwb2ludEZyb21Db25maWcgPSBhc3luYyAoc2VydmljZUlkKSA9PiBsb2FkQ29uZmlnKGdldEVuZHBvaW50VXJsQ29uZmlnKHNlcnZpY2VJZCA/PyBcIlwiKSkoKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointFromConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEndpointFromInstructions: () => (/* binding */ getEndpointFromInstructions),\n/* harmony export */   resolveParams: () => (/* binding */ resolveParams)\n/* harmony export */ });\n/* harmony import */ var _service_customizations__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../service-customizations */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/service-customizations/index.js\");\n/* harmony import */ var _createConfigValueProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createConfigValueProvider */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/createConfigValueProvider.js\");\n/* harmony import */ var _getEndpointFromConfig__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getEndpointFromConfig */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointFromConfig.js\");\n/* harmony import */ var _toEndpointV1__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./toEndpointV1 */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/toEndpointV1.js\");\n\n\n\n\nconst getEndpointFromInstructions = async (commandInput, instructionsSupplier, clientConfig, context) => {\n    if (!clientConfig.endpoint) {\n        let endpointFromConfig;\n        if (clientConfig.serviceConfiguredEndpoint) {\n            endpointFromConfig = await clientConfig.serviceConfiguredEndpoint();\n        }\n        else {\n            endpointFromConfig = await (0,_getEndpointFromConfig__WEBPACK_IMPORTED_MODULE_2__.getEndpointFromConfig)(clientConfig.serviceId);\n        }\n        if (endpointFromConfig) {\n            clientConfig.endpoint = () => Promise.resolve((0,_toEndpointV1__WEBPACK_IMPORTED_MODULE_3__.toEndpointV1)(endpointFromConfig));\n        }\n    }\n    const endpointParams = await resolveParams(commandInput, instructionsSupplier, clientConfig);\n    if (typeof clientConfig.endpointProvider !== \"function\") {\n        throw new Error(\"config.endpointProvider is not set.\");\n    }\n    const endpoint = clientConfig.endpointProvider(endpointParams, context);\n    return endpoint;\n};\nconst resolveParams = async (commandInput, instructionsSupplier, clientConfig) => {\n    const endpointParams = {};\n    const instructions = instructionsSupplier?.getEndpointParameterInstructions?.() || {};\n    for (const [name, instruction] of Object.entries(instructions)) {\n        switch (instruction.type) {\n            case \"staticContextParams\":\n                endpointParams[name] = instruction.value;\n                break;\n            case \"contextParams\":\n                endpointParams[name] = commandInput[instruction.name];\n                break;\n            case \"clientContextParams\":\n            case \"builtInParams\":\n                endpointParams[name] = await (0,_createConfigValueProvider__WEBPACK_IMPORTED_MODULE_1__.createConfigValueProvider)(instruction.name, name, clientConfig)();\n                break;\n            case \"operationContextParams\":\n                endpointParams[name] = instruction.get(commandInput);\n                break;\n            default:\n                throw new Error(\"Unrecognized endpoint parameter instruction: \" + JSON.stringify(instruction));\n        }\n    }\n    if (Object.keys(instructions).length === 0) {\n        Object.assign(endpointParams, clientConfig);\n    }\n    if (String(clientConfig.serviceId).toLowerCase() === \"s3\") {\n        await (0,_service_customizations__WEBPACK_IMPORTED_MODULE_0__.resolveParamsForS3)(endpointParams);\n    }\n    return endpointParams;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointUrlConfig.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointUrlConfig.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEndpointUrlConfig: () => (/* binding */ getEndpointUrlConfig)\n/* harmony export */ });\n/* harmony import */ var _smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/shared-ini-file-loader */ \"(rsc)/./node_modules/.pnpm/@smithy+shared-ini-file-loader@4.0.4/node_modules/@smithy/shared-ini-file-loader/dist-es/index.js\");\n\nconst ENV_ENDPOINT_URL = \"AWS_ENDPOINT_URL\";\nconst CONFIG_ENDPOINT_URL = \"endpoint_url\";\nconst getEndpointUrlConfig = (serviceId) => ({\n    environmentVariableSelector: (env) => {\n        const serviceSuffixParts = serviceId.split(\" \").map((w) => w.toUpperCase());\n        const serviceEndpointUrl = env[[ENV_ENDPOINT_URL, ...serviceSuffixParts].join(\"_\")];\n        if (serviceEndpointUrl)\n            return serviceEndpointUrl;\n        const endpointUrl = env[ENV_ENDPOINT_URL];\n        if (endpointUrl)\n            return endpointUrl;\n        return undefined;\n    },\n    configFileSelector: (profile, config) => {\n        if (config && profile.services) {\n            const servicesSection = config[[\"services\", profile.services].join(_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_0__.CONFIG_PREFIX_SEPARATOR)];\n            if (servicesSection) {\n                const servicePrefixParts = serviceId.split(\" \").map((w) => w.toLowerCase());\n                const endpointUrl = servicesSection[[servicePrefixParts.join(\"_\"), CONFIG_ENDPOINT_URL].join(_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_0__.CONFIG_PREFIX_SEPARATOR)];\n                if (endpointUrl)\n                    return endpointUrl;\n            }\n        }\n        const endpointUrl = profile[CONFIG_ENDPOINT_URL];\n        if (endpointUrl)\n            return endpointUrl;\n        return undefined;\n    },\n    default: undefined,\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointUrlConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/index.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/index.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEndpointFromInstructions: () => (/* reexport safe */ _getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_0__.getEndpointFromInstructions),\n/* harmony export */   resolveParams: () => (/* reexport safe */ _getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_0__.resolveParams),\n/* harmony export */   toEndpointV1: () => (/* reexport safe */ _toEndpointV1__WEBPACK_IMPORTED_MODULE_1__.toEndpointV1)\n/* harmony export */ });\n/* harmony import */ var _getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./getEndpointFromInstructions */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js\");\n/* harmony import */ var _toEndpointV1__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./toEndpointV1 */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/toEndpointV1.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLWVuZHBvaW50QDQuMS4xMS9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvYWRhcHRvcnMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBOEM7QUFDZiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzbWl0aHkrbWlkZGxld2FyZS1lbmRwb2ludEA0LjEuMTEvbm9kZV9tb2R1bGVzL0BzbWl0aHkvbWlkZGxld2FyZS1lbmRwb2ludC9kaXN0LWVzL2FkYXB0b3JzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2dldEVuZHBvaW50RnJvbUluc3RydWN0aW9uc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdG9FbmRwb2ludFYxXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/toEndpointV1.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/toEndpointV1.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toEndpointV1: () => (/* binding */ toEndpointV1)\n/* harmony export */ });\n/* harmony import */ var _smithy_url_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/url-parser */ \"(rsc)/./node_modules/.pnpm/@smithy+url-parser@4.0.4/node_modules/@smithy/url-parser/dist-es/index.js\");\n\nconst toEndpointV1 = (endpoint) => {\n    if (typeof endpoint === \"object\") {\n        if (\"url\" in endpoint) {\n            return (0,_smithy_url_parser__WEBPACK_IMPORTED_MODULE_0__.parseUrl)(endpoint.url);\n        }\n        return endpoint;\n    }\n    return (0,_smithy_url_parser__WEBPACK_IMPORTED_MODULE_0__.parseUrl)(endpoint);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLWVuZHBvaW50QDQuMS4xMS9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvYWRhcHRvcnMvdG9FbmRwb2ludFYxLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQThDO0FBQ3ZDO0FBQ1A7QUFDQTtBQUNBLG1CQUFtQiw0REFBUTtBQUMzQjtBQUNBO0FBQ0E7QUFDQSxXQUFXLDREQUFRO0FBQ25CIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLWVuZHBvaW50QDQuMS4xMS9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvYWRhcHRvcnMvdG9FbmRwb2ludFYxLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHBhcnNlVXJsIH0gZnJvbSBcIkBzbWl0aHkvdXJsLXBhcnNlclwiO1xuZXhwb3J0IGNvbnN0IHRvRW5kcG9pbnRWMSA9IChlbmRwb2ludCkgPT4ge1xuICAgIGlmICh0eXBlb2YgZW5kcG9pbnQgPT09IFwib2JqZWN0XCIpIHtcbiAgICAgICAgaWYgKFwidXJsXCIgaW4gZW5kcG9pbnQpIHtcbiAgICAgICAgICAgIHJldHVybiBwYXJzZVVybChlbmRwb2ludC51cmwpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBlbmRwb2ludDtcbiAgICB9XG4gICAgcmV0dXJuIHBhcnNlVXJsKGVuZHBvaW50KTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/toEndpointV1.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/endpointMiddleware.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/endpointMiddleware.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   endpointMiddleware: () => (/* binding */ endpointMiddleware)\n/* harmony export */ });\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n/* harmony import */ var _adaptors_getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./adaptors/getEndpointFromInstructions */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointFromInstructions.js\");\n\n\n\nconst endpointMiddleware = ({ config, instructions, }) => {\n    return (next, context) => async (args) => {\n        if (config.endpoint) {\n            (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.setFeature)(context, \"ENDPOINT_OVERRIDE\", \"N\");\n        }\n        const endpoint = await (0,_adaptors_getEndpointFromInstructions__WEBPACK_IMPORTED_MODULE_2__.getEndpointFromInstructions)(args.input, {\n            getEndpointParameterInstructions() {\n                return instructions;\n            },\n        }, { ...config }, context);\n        context.endpointV2 = endpoint;\n        context.authSchemes = endpoint.properties?.authSchemes;\n        const authScheme = context.authSchemes?.[0];\n        if (authScheme) {\n            context[\"signing_region\"] = authScheme.signingRegion;\n            context[\"signing_service\"] = authScheme.signingName;\n            const smithyContext = (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_1__.getSmithyContext)(context);\n            const httpAuthOption = smithyContext?.selectedHttpAuthScheme?.httpAuthOption;\n            if (httpAuthOption) {\n                httpAuthOption.signingProperties = Object.assign(httpAuthOption.signingProperties || {}, {\n                    signing_region: authScheme.signingRegion,\n                    signingRegion: authScheme.signingRegion,\n                    signing_service: authScheme.signingName,\n                    signingName: authScheme.signingName,\n                    signingRegionSet: authScheme.signingRegionSet,\n                }, authScheme.properties);\n            }\n        }\n        return next({\n            ...args,\n        });\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/endpointMiddleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/getEndpointPlugin.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/getEndpointPlugin.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   endpointMiddlewareOptions: () => (/* binding */ endpointMiddlewareOptions),\n/* harmony export */   getEndpointPlugin: () => (/* binding */ getEndpointPlugin)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _endpointMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./endpointMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/endpointMiddleware.js\");\n\n\nconst endpointMiddlewareOptions = {\n    step: \"serialize\",\n    tags: [\"ENDPOINT_PARAMETERS\", \"ENDPOINT_V2\", \"ENDPOINT\"],\n    name: \"endpointV2Middleware\",\n    override: true,\n    relation: \"before\",\n    toMiddleware: _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_0__.serializerMiddlewareOption.name,\n};\nconst getEndpointPlugin = (config, instructions) => ({\n    applyToStack: (clientStack) => {\n        clientStack.addRelativeTo((0,_endpointMiddleware__WEBPACK_IMPORTED_MODULE_1__.endpointMiddleware)({\n            config,\n            instructions,\n        }), endpointMiddlewareOptions);\n    },\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLWVuZHBvaW50QDQuMS4xMS9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvZ2V0RW5kcG9pbnRQbHVnaW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFzRTtBQUNaO0FBQ25EO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixnRkFBMEI7QUFDNUM7QUFDTztBQUNQO0FBQ0Esa0NBQWtDLHVFQUFrQjtBQUNwRDtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTCxDQUFDIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLWVuZHBvaW50QDQuMS4xMS9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvZ2V0RW5kcG9pbnRQbHVnaW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgc2VyaWFsaXplck1pZGRsZXdhcmVPcHRpb24gfSBmcm9tIFwiQHNtaXRoeS9taWRkbGV3YXJlLXNlcmRlXCI7XG5pbXBvcnQgeyBlbmRwb2ludE1pZGRsZXdhcmUgfSBmcm9tIFwiLi9lbmRwb2ludE1pZGRsZXdhcmVcIjtcbmV4cG9ydCBjb25zdCBlbmRwb2ludE1pZGRsZXdhcmVPcHRpb25zID0ge1xuICAgIHN0ZXA6IFwic2VyaWFsaXplXCIsXG4gICAgdGFnczogW1wiRU5EUE9JTlRfUEFSQU1FVEVSU1wiLCBcIkVORFBPSU5UX1YyXCIsIFwiRU5EUE9JTlRcIl0sXG4gICAgbmFtZTogXCJlbmRwb2ludFYyTWlkZGxld2FyZVwiLFxuICAgIG92ZXJyaWRlOiB0cnVlLFxuICAgIHJlbGF0aW9uOiBcImJlZm9yZVwiLFxuICAgIHRvTWlkZGxld2FyZTogc2VyaWFsaXplck1pZGRsZXdhcmVPcHRpb24ubmFtZSxcbn07XG5leHBvcnQgY29uc3QgZ2V0RW5kcG9pbnRQbHVnaW4gPSAoY29uZmlnLCBpbnN0cnVjdGlvbnMpID0+ICh7XG4gICAgYXBwbHlUb1N0YWNrOiAoY2xpZW50U3RhY2spID0+IHtcbiAgICAgICAgY2xpZW50U3RhY2suYWRkUmVsYXRpdmVUbyhlbmRwb2ludE1pZGRsZXdhcmUoe1xuICAgICAgICAgICAgY29uZmlnLFxuICAgICAgICAgICAgaW5zdHJ1Y3Rpb25zLFxuICAgICAgICB9KSwgZW5kcG9pbnRNaWRkbGV3YXJlT3B0aW9ucyk7XG4gICAgfSxcbn0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/getEndpointPlugin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   endpointMiddleware: () => (/* reexport safe */ _endpointMiddleware__WEBPACK_IMPORTED_MODULE_1__.endpointMiddleware),\n/* harmony export */   endpointMiddlewareOptions: () => (/* reexport safe */ _getEndpointPlugin__WEBPACK_IMPORTED_MODULE_2__.endpointMiddlewareOptions),\n/* harmony export */   getEndpointFromInstructions: () => (/* reexport safe */ _adaptors__WEBPACK_IMPORTED_MODULE_0__.getEndpointFromInstructions),\n/* harmony export */   getEndpointPlugin: () => (/* reexport safe */ _getEndpointPlugin__WEBPACK_IMPORTED_MODULE_2__.getEndpointPlugin),\n/* harmony export */   resolveEndpointConfig: () => (/* reexport safe */ _resolveEndpointConfig__WEBPACK_IMPORTED_MODULE_3__.resolveEndpointConfig),\n/* harmony export */   resolveParams: () => (/* reexport safe */ _adaptors__WEBPACK_IMPORTED_MODULE_0__.resolveParams),\n/* harmony export */   toEndpointV1: () => (/* reexport safe */ _adaptors__WEBPACK_IMPORTED_MODULE_0__.toEndpointV1)\n/* harmony export */ });\n/* harmony import */ var _adaptors__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./adaptors */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/index.js\");\n/* harmony import */ var _endpointMiddleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./endpointMiddleware */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/endpointMiddleware.js\");\n/* harmony import */ var _getEndpointPlugin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./getEndpointPlugin */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/getEndpointPlugin.js\");\n/* harmony import */ var _resolveEndpointConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resolveEndpointConfig */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/resolveEndpointConfig.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/types.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLWVuZHBvaW50QDQuMS4xMS9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQTJCO0FBQ1U7QUFDRDtBQUNJO0FBQ2hCIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLWVuZHBvaW50QDQuMS4xMS9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vYWRhcHRvcnNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2VuZHBvaW50TWlkZGxld2FyZVwiO1xuZXhwb3J0ICogZnJvbSBcIi4vZ2V0RW5kcG9pbnRQbHVnaW5cIjtcbmV4cG9ydCAqIGZyb20gXCIuL3Jlc29sdmVFbmRwb2ludENvbmZpZ1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHlwZXNcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/resolveEndpointConfig.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/resolveEndpointConfig.js ***!
  \*****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveEndpointConfig: () => (/* binding */ resolveEndpointConfig)\n/* harmony export */ });\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n/* harmony import */ var _adaptors_getEndpointFromConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./adaptors/getEndpointFromConfig */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/getEndpointFromConfig.js\");\n/* harmony import */ var _adaptors_toEndpointV1__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./adaptors/toEndpointV1 */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/adaptors/toEndpointV1.js\");\n\n\n\nconst resolveEndpointConfig = (input) => {\n    const tls = input.tls ?? true;\n    const { endpoint, useDualstackEndpoint, useFipsEndpoint } = input;\n    const customEndpointProvider = endpoint != null ? async () => (0,_adaptors_toEndpointV1__WEBPACK_IMPORTED_MODULE_2__.toEndpointV1)(await (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(endpoint)()) : undefined;\n    const isCustomEndpoint = !!endpoint;\n    const resolvedConfig = Object.assign(input, {\n        endpoint: customEndpointProvider,\n        tls,\n        isCustomEndpoint,\n        useDualstackEndpoint: (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(useDualstackEndpoint ?? false),\n        useFipsEndpoint: (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(useFipsEndpoint ?? false),\n    });\n    let configuredEndpointPromise = undefined;\n    resolvedConfig.serviceConfiguredEndpoint = async () => {\n        if (input.serviceId && !configuredEndpointPromise) {\n            configuredEndpointPromise = (0,_adaptors_getEndpointFromConfig__WEBPACK_IMPORTED_MODULE_1__.getEndpointFromConfig)(input.serviceId);\n        }\n        return configuredEndpointPromise;\n    };\n    return resolvedConfig;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/resolveEndpointConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/service-customizations/index.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/service-customizations/index.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOT_PATTERN: () => (/* reexport safe */ _s3__WEBPACK_IMPORTED_MODULE_0__.DOT_PATTERN),\n/* harmony export */   S3_HOSTNAME_PATTERN: () => (/* reexport safe */ _s3__WEBPACK_IMPORTED_MODULE_0__.S3_HOSTNAME_PATTERN),\n/* harmony export */   isArnBucketName: () => (/* reexport safe */ _s3__WEBPACK_IMPORTED_MODULE_0__.isArnBucketName),\n/* harmony export */   isDnsCompatibleBucketName: () => (/* reexport safe */ _s3__WEBPACK_IMPORTED_MODULE_0__.isDnsCompatibleBucketName),\n/* harmony export */   resolveParamsForS3: () => (/* reexport safe */ _s3__WEBPACK_IMPORTED_MODULE_0__.resolveParamsForS3)\n/* harmony export */ });\n/* harmony import */ var _s3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./s3 */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/service-customizations/s3.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLWVuZHBvaW50QDQuMS4xMS9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvc2VydmljZS1jdXN0b21pemF0aW9ucy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBcUIiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac21pdGh5K21pZGRsZXdhcmUtZW5kcG9pbnRANC4xLjExL25vZGVfbW9kdWxlcy9Ac21pdGh5L21pZGRsZXdhcmUtZW5kcG9pbnQvZGlzdC1lcy9zZXJ2aWNlLWN1c3RvbWl6YXRpb25zL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL3MzXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/service-customizations/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/service-customizations/s3.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/service-customizations/s3.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOT_PATTERN: () => (/* binding */ DOT_PATTERN),\n/* harmony export */   S3_HOSTNAME_PATTERN: () => (/* binding */ S3_HOSTNAME_PATTERN),\n/* harmony export */   isArnBucketName: () => (/* binding */ isArnBucketName),\n/* harmony export */   isDnsCompatibleBucketName: () => (/* binding */ isDnsCompatibleBucketName),\n/* harmony export */   resolveParamsForS3: () => (/* binding */ resolveParamsForS3)\n/* harmony export */ });\nconst resolveParamsForS3 = async (endpointParams) => {\n    const bucket = endpointParams?.Bucket || \"\";\n    if (typeof endpointParams.Bucket === \"string\") {\n        endpointParams.Bucket = bucket.replace(/#/g, encodeURIComponent(\"#\")).replace(/\\?/g, encodeURIComponent(\"?\"));\n    }\n    if (isArnBucketName(bucket)) {\n        if (endpointParams.ForcePathStyle === true) {\n            throw new Error(\"Path-style addressing cannot be used with ARN buckets\");\n        }\n    }\n    else if (!isDnsCompatibleBucketName(bucket) ||\n        (bucket.indexOf(\".\") !== -1 && !String(endpointParams.Endpoint).startsWith(\"http:\")) ||\n        bucket.toLowerCase() !== bucket ||\n        bucket.length < 3) {\n        endpointParams.ForcePathStyle = true;\n    }\n    if (endpointParams.DisableMultiRegionAccessPoints) {\n        endpointParams.disableMultiRegionAccessPoints = true;\n        endpointParams.DisableMRAP = true;\n    }\n    return endpointParams;\n};\nconst DOMAIN_PATTERN = /^[a-z0-9][a-z0-9\\.\\-]{1,61}[a-z0-9]$/;\nconst IP_ADDRESS_PATTERN = /(\\d+\\.){3}\\d+/;\nconst DOTS_PATTERN = /\\.\\./;\nconst DOT_PATTERN = /\\./;\nconst S3_HOSTNAME_PATTERN = /^(.+\\.)?s3(-fips)?(\\.dualstack)?[.-]([a-z0-9-]+)\\./;\nconst isDnsCompatibleBucketName = (bucketName) => DOMAIN_PATTERN.test(bucketName) && !IP_ADDRESS_PATTERN.test(bucketName) && !DOTS_PATTERN.test(bucketName);\nconst isArnBucketName = (bucketName) => {\n    const [arn, partition, service, , , bucket] = bucketName.split(\":\");\n    const isArn = arn === \"arn\" && bucketName.split(\":\").length >= 6;\n    const isValidArn = Boolean(isArn && partition && service && bucket);\n    if (isArn && !isValidArn) {\n        throw new Error(`Invalid ARN: ${bucketName} was an invalid ARN.`);\n    }\n    return isValidArn;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/service-customizations/s3.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/types.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/types.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLWVuZHBvaW50QDQuMS4xMS9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IjtBQUFVIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNtaXRoeSttaWRkbGV3YXJlLWVuZHBvaW50QDQuMS4xMS9ub2RlX21vZHVsZXMvQHNtaXRoeS9taWRkbGV3YXJlLWVuZHBvaW50L2Rpc3QtZXMvdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/types.js\n");

/***/ })

};
;