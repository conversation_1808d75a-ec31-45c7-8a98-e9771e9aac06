"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0";
exports.ids = ["vendor-chunks/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-4CEQJ2X6.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-4CEQJ2X6.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   processActionsForRuntimeRequest: () => (/* binding */ processActionsForRuntimeRequest)\n/* harmony export */ });\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-WM3ARNBD.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-2KQ6HEWZ.mjs\");\n// src/types/frontend-action.ts\n\n\nfunction processActionsForRuntimeRequest(actions) {\n  const filteredActions = actions.filter(\n    (action) => action.available !== _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_0__.ActionInputAvailability.Disabled && action.disabled !== true && action.name !== \"*\" && action.available != \"frontend\" && !action.pairedAction\n  ).map((action) => {\n    let available = _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_0__.ActionInputAvailability.Enabled;\n    if (action.disabled) {\n      available = _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_0__.ActionInputAvailability.Disabled;\n    } else if (action.available === \"disabled\") {\n      available = _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_0__.ActionInputAvailability.Disabled;\n    } else if (action.available === \"remote\") {\n      available = _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_0__.ActionInputAvailability.Remote;\n    }\n    return {\n      name: action.name,\n      description: action.description || \"\",\n      jsonSchema: JSON.stringify((0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.actionParametersToJsonSchema)(action.parameters || [])),\n      available\n    };\n  });\n  return filteredActions;\n}\n\n\n//# sourceMappingURL=chunk-4CEQJ2X6.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-4CEQJ2X6.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-5FHSUKQL.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-5FHSUKQL.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   use_flat_category_store_default: () => (/* binding */ use_flat_category_store_default)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-VNNKZIFB.mjs\");\n// src/hooks/use-flat-category-store.ts\n\n\nvar useFlatCategoryStore = () => {\n  const [elements, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(flatCategoryStoreReducer, /* @__PURE__ */ new Map());\n  const addElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((value, categories) => {\n    const newId = (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.randomId)();\n    dispatch({\n      type: \"ADD_ELEMENT\",\n      value,\n      id: newId,\n      categories\n    });\n    return newId;\n  }, []);\n  const removeElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((id) => {\n    dispatch({ type: \"REMOVE_ELEMENT\", id });\n  }, []);\n  const allElements = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (categories) => {\n      const categoriesSet = new Set(categories);\n      const result = [];\n      elements.forEach((element) => {\n        if (setsHaveIntersection(categoriesSet, element.categories)) {\n          result.push(element.value);\n        }\n      });\n      return result;\n    },\n    [elements]\n  );\n  return { addElement, removeElement, allElements };\n};\nvar use_flat_category_store_default = useFlatCategoryStore;\nfunction flatCategoryStoreReducer(state, action) {\n  switch (action.type) {\n    case \"ADD_ELEMENT\": {\n      const { value, id, categories } = action;\n      const newElement = {\n        id,\n        value,\n        categories: new Set(categories)\n      };\n      const newState = new Map(state);\n      newState.set(id, newElement);\n      return newState;\n    }\n    case \"REMOVE_ELEMENT\": {\n      const newState = new Map(state);\n      newState.delete(action.id);\n      return newState;\n    }\n    default:\n      return state;\n  }\n}\nfunction setsHaveIntersection(setA, setB) {\n  const [smallerSet, largerSet] = setA.size <= setB.size ? [setA, setB] : [setB, setA];\n  for (let item of smallerSet) {\n    if (largerSet.has(item)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n\n//# sourceMappingURL=chunk-5FHSUKQL.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-5FHSUKQL.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-CCESTGAM.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-CCESTGAM.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorToast: () => (/* binding */ ErrorToast),\n/* harmony export */   useAsyncCallback: () => (/* binding */ useAsyncCallback),\n/* harmony export */   useErrorToast: () => (/* binding */ useErrorToast)\n/* harmony export */ });\n/* harmony import */ var _chunk_O7ARI5CV_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-O7ARI5CV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-O7ARI5CV.mjs\");\n/* harmony import */ var _chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-VRXANACV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VRXANACV.mjs\");\n/* harmony import */ var _chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-SKC7AJIV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/.pnpm/react-markdown@8.0.7_@types+react@19.0.1_react@19.0.0/node_modules/react-markdown/lib/react-markdown.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n// src/components/error-boundary/error-utils.tsx\n\n\n\nfunction ErrorToast({ errors }) {\n  const errorsToRender = errors.map((error, idx) => {\n    var _a, _b, _c;\n    const originalError = \"extensions\" in error ? (_a = error.extensions) == null ? void 0 : _a.originalError : {};\n    const message = (_b = originalError == null ? void 0 : originalError.message) != null ? _b : error.message;\n    const code = \"extensions\" in error ? (_c = error.extensions) == null ? void 0 : _c.code : null;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\n      \"div\",\n      {\n        style: {\n          marginTop: idx === 0 ? 0 : 10,\n          marginBottom: 14\n        },\n        children: [\n          /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_chunk_O7ARI5CV_mjs__WEBPACK_IMPORTED_MODULE_2__.ExclamationMarkIcon, { style: { marginBottom: 4 } }),\n          code && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\n            \"div\",\n            {\n              style: {\n                fontWeight: \"600\",\n                marginBottom: 4\n              },\n              children: [\n                \"Copilot Runtime Error:\",\n                \" \",\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", { style: { fontFamily: \"monospace\", fontWeight: \"normal\" }, children: code })\n              ]\n            }\n          ),\n          /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.ReactMarkdown, { children: message })\n        ]\n      },\n      idx\n    );\n  });\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\n    \"div\",\n    {\n      style: {\n        fontSize: \"13px\",\n        maxWidth: \"600px\"\n      },\n      children: [\n        errorsToRender,\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", { style: { fontSize: \"11px\", opacity: 0.75 }, children: \"NOTE: This error only displays during local development.\" })\n      ]\n    }\n  );\n}\nfunction useErrorToast() {\n  const { addToast } = (0,_chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (error) => {\n      const errorId = error.map((err) => {\n        var _a, _b;\n        const message = \"extensions\" in err ? ((_b = (_a = err.extensions) == null ? void 0 : _a.originalError) == null ? void 0 : _b.message) || err.message : err.message;\n        const stack = err.stack || \"\";\n        return btoa(message + stack).slice(0, 32);\n      }).join(\"|\");\n      addToast({\n        type: \"error\",\n        id: errorId,\n        // Toast libraries typically dedupe by id\n        message: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ErrorToast, { errors: error })\n      });\n    },\n    [addToast]\n  );\n}\nfunction useAsyncCallback(callback, deps) {\n  const addErrorToast = useErrorToast();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((...args) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__async)(this, null, function* () {\n    try {\n      return yield callback(...args);\n    } catch (error) {\n      console.error(\"Error in async callback:\", error);\n      addErrorToast([error]);\n      throw error;\n    }\n  }), deps);\n}\n\n\n//# sourceMappingURL=chunk-CCESTGAM.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-CCESTGAM.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-CMQV4XNY.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-CMQV4XNY.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLangGraphInterruptRender: () => (/* binding */ useLangGraphInterruptRender)\n/* harmony export */ });\n/* harmony import */ var _chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-XFOTNHYA.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n// src/hooks/use-langgraph-interrupt-render.ts\n\nvar InterruptRenderer = ({ event, result, render, resolve }) => {\n  return render({ event, result, resolve });\n};\nfunction useLangGraphInterruptRender() {\n  const { langGraphInterruptAction, setLangGraphInterruptAction, agentSession } = (0,_chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_1__.useCopilotContext)();\n  const responseRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  const resolveInterrupt = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (response) => {\n      responseRef.current = response;\n      setTimeout(() => {\n        setLangGraphInterruptAction({ event: { response } });\n      }, 0);\n    },\n    [setLangGraphInterruptAction]\n  );\n  if (!langGraphInterruptAction || !langGraphInterruptAction.event || !langGraphInterruptAction.render)\n    return null;\n  const { render, handler, event, enabled } = langGraphInterruptAction;\n  const conditionsMet = !agentSession || !enabled ? true : enabled({ eventValue: event.value, agentMetadata: agentSession });\n  if (!conditionsMet) {\n    return null;\n  }\n  let result = null;\n  if (handler) {\n    result = handler({\n      event,\n      resolve: resolveInterrupt\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.createElement(InterruptRenderer, {\n    event,\n    result,\n    render,\n    resolve: resolveInterrupt\n  });\n}\n\n\n//# sourceMappingURL=chunk-CMQV4XNY.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-CMQV4XNY.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-DCTJZ742.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-DCTJZ742.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CopilotMessagesContext: () => (/* binding */ CopilotMessagesContext),\n/* harmony export */   useCopilotMessagesContext: () => (/* binding */ useCopilotMessagesContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/context/copilot-messages-context.tsx\n\nvar emptyCopilotContext = {\n  messages: [],\n  setMessages: () => []\n};\nvar CopilotMessagesContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(emptyCopilotContext);\nfunction useCopilotMessagesContext() {\n  const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(CopilotMessagesContext);\n  if (context === emptyCopilotContext) {\n    throw new Error(\n      \"A messages consuming component was not wrapped with `<CopilotMessages> {...} </CopilotMessages>`\"\n    );\n  }\n  return context;\n}\n\n\n//# sourceMappingURL=chunk-DCTJZ742.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtY29yZUAxLjkuMi1uZXh0LjdfQHR5cGVzK3JlYWN0QDE5LjAuMV9ncmFwaHFsQDE2LjkuMF9yZWFjdC1kb21AMTkuMC4wX3JlYWN0QDE5LjAuMF9fcmVhY3RAMTkuMC4wL25vZGVfbW9kdWxlcy9AY29waWxvdGtpdC9yZWFjdC1jb3JlL2Rpc3QvY2h1bmstRENUSlo3NDIubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQzBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLGdEQUFtQjtBQUNoRDtBQUNBLGtCQUFrQiw2Q0FBZ0I7QUFDbEM7QUFDQTtBQUNBLCtFQUErRSxLQUFLO0FBQ3BGO0FBQ0E7QUFDQTtBQUNBOztBQUtFO0FBQ0YiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AY29waWxvdGtpdCtyZWFjdC1jb3JlQDEuOS4yLW5leHQuN19AdHlwZXMrcmVhY3RAMTkuMC4xX2dyYXBocWxAMTYuOS4wX3JlYWN0LWRvbUAxOS4wLjBfcmVhY3RAMTkuMC4wX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0Bjb3BpbG90a2l0L3JlYWN0LWNvcmUvZGlzdC9jaHVuay1EQ1RKWjc0Mi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2NvbnRleHQvY29waWxvdC1tZXNzYWdlcy1jb250ZXh0LnRzeFxuaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xudmFyIGVtcHR5Q29waWxvdENvbnRleHQgPSB7XG4gIG1lc3NhZ2VzOiBbXSxcbiAgc2V0TWVzc2FnZXM6ICgpID0+IFtdXG59O1xudmFyIENvcGlsb3RNZXNzYWdlc0NvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KGVtcHR5Q29waWxvdENvbnRleHQpO1xuZnVuY3Rpb24gdXNlQ29waWxvdE1lc3NhZ2VzQ29udGV4dCgpIHtcbiAgY29uc3QgY29udGV4dCA9IFJlYWN0LnVzZUNvbnRleHQoQ29waWxvdE1lc3NhZ2VzQ29udGV4dCk7XG4gIGlmIChjb250ZXh0ID09PSBlbXB0eUNvcGlsb3RDb250ZXh0KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgXCJBIG1lc3NhZ2VzIGNvbnN1bWluZyBjb21wb25lbnQgd2FzIG5vdCB3cmFwcGVkIHdpdGggYDxDb3BpbG90TWVzc2FnZXM+IHsuLi59IDwvQ29waWxvdE1lc3NhZ2VzPmBcIlxuICAgICk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59XG5cbmV4cG9ydCB7XG4gIENvcGlsb3RNZXNzYWdlc0NvbnRleHQsXG4gIHVzZUNvcGlsb3RNZXNzYWdlc0NvbnRleHRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jaHVuay1EQ1RKWjc0Mi5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-DCTJZ742.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-GFJW4RIM.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-GFJW4RIM.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shouldShowDevConsole: () => (/* binding */ shouldShowDevConsole)\n/* harmony export */ });\n// src/utils/dev-console.ts\nfunction shouldShowDevConsole(showDevConsole) {\n  return showDevConsole;\n}\n\n\n//# sourceMappingURL=chunk-GFJW4RIM.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtY29yZUAxLjkuMi1uZXh0LjdfQHR5cGVzK3JlYWN0QDE5LjAuMV9ncmFwaHFsQDE2LjkuMF9yZWFjdC1kb21AMTkuMC4wX3JlYWN0QDE5LjAuMF9fcmVhY3RAMTkuMC4wL25vZGVfbW9kdWxlcy9AY29waWxvdGtpdC9yZWFjdC1jb3JlL2Rpc3QvY2h1bmstR0ZKVzRSSU0ubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFJRTtBQUNGIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtY29yZUAxLjkuMi1uZXh0LjdfQHR5cGVzK3JlYWN0QDE5LjAuMV9ncmFwaHFsQDE2LjkuMF9yZWFjdC1kb21AMTkuMC4wX3JlYWN0QDE5LjAuMF9fcmVhY3RAMTkuMC4wL25vZGVfbW9kdWxlcy9AY29waWxvdGtpdC9yZWFjdC1jb3JlL2Rpc3QvY2h1bmstR0ZKVzRSSU0ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy91dGlscy9kZXYtY29uc29sZS50c1xuZnVuY3Rpb24gc2hvdWxkU2hvd0RldkNvbnNvbGUoc2hvd0RldkNvbnNvbGUpIHtcbiAgcmV0dXJuIHNob3dEZXZDb25zb2xlO1xufVxuXG5leHBvcnQge1xuICBzaG91bGRTaG93RGV2Q29uc29sZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNodW5rLUdGSlc0UklNLm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-GFJW4RIM.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-HD2GE3DK.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-HD2GE3DK.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UsageBanner: () => (/* binding */ UsageBanner),\n/* harmony export */   renderCopilotKitUsage: () => (/* binding */ renderCopilotKitUsage)\n/* harmony export */ });\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/components/usage-banner.tsx\n\n\nvar defaultIcons = {\n  [_copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.Severity.CRITICAL]: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\n    \"svg\",\n    {\n      viewBox: \"0 0 24 24\",\n      width: \"18\",\n      height: \"18\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2.5\",\n      fill: \"none\",\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      children: [\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"circle\", { cx: \"12\", cy: \"12\", r: \"10\" }),\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"line\", { x1: \"15\", y1: \"9\", x2: \"9\", y2: \"15\" }),\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"line\", { x1: \"9\", y1: \"9\", x2: \"15\", y2: \"15\" })\n      ]\n    }\n  ),\n  [_copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.Severity.WARNING]: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\n    \"svg\",\n    {\n      viewBox: \"0 0 24 24\",\n      width: \"18\",\n      height: \"18\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2.5\",\n      fill: \"none\",\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      children: [\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", { d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\" }),\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"line\", { x1: \"12\", y1: \"9\", x2: \"12\", y2: \"13\" }),\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"line\", { x1: \"12\", y1: \"17\", x2: \"12.01\", y2: \"17\" })\n      ]\n    }\n  ),\n  [_copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.Severity.INFO]: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\n    \"svg\",\n    {\n      viewBox: \"0 0 24 24\",\n      width: \"18\",\n      height: \"18\",\n      stroke: \"currentColor\",\n      strokeWidth: \"2.5\",\n      fill: \"none\",\n      strokeLinecap: \"round\",\n      strokeLinejoin: \"round\",\n      children: [\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"circle\", { cx: \"12\", cy: \"12\", r: \"10\" }),\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"line\", { x1: \"12\", y1: \"16\", x2: \"12\", y2: \"12\" }),\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"line\", { x1: \"12\", y1: \"8\", x2: \"12.01\", y2: \"8\" })\n      ]\n    }\n  )\n};\nfunction UsageBanner({\n  severity = _copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.Severity.CRITICAL,\n  message = \"\",\n  icon,\n  onClose,\n  actions\n}) {\n  if (!message || !severity) {\n    return null;\n  }\n  const parseMessage = (rawMessage) => {\n    const linkRegex = /\\[([^\\]]+)\\]\\(([^)]+)\\)/g;\n    const matches = Array.from(rawMessage.matchAll(linkRegex));\n    if (matches.length > 0) {\n      let cleanMessage2 = rawMessage.replace(/\\.\\s*See more:\\s*\\[([^\\]]+)\\]\\(([^)]+)\\)/g, \".\").replace(/See more:\\s*\\[([^\\]]+)\\]\\(([^)]+)\\)/g, \"\").trim();\n      return cleanMessage2;\n    }\n    return rawMessage;\n  };\n  const cleanMessage = parseMessage(message);\n  const Icon = icon || defaultIcons[severity];\n  const themeConfigs = {\n    [_copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.Severity.INFO]: {\n      bg: \"linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%)\",\n      border: \"#93c5fd\",\n      text: \"#1e40af\",\n      icon: \"#3b82f6\",\n      primaryBtn: \"#3b82f6\",\n      primaryBtnHover: \"#2563eb\"\n    },\n    [_copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.Severity.WARNING]: {\n      bg: \"linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)\",\n      border: \"#fbbf24\",\n      text: \"#92400e\",\n      icon: \"#f59e0b\",\n      primaryBtn: \"#f59e0b\",\n      primaryBtnHover: \"#d97706\"\n    },\n    [_copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.Severity.CRITICAL]: {\n      bg: \"linear-gradient(135deg, #fef2f2 0%, #fecaca 100%)\",\n      border: \"#f87171\",\n      text: \"#991b1b\",\n      icon: \"#ef4444\",\n      primaryBtn: \"#ef4444\",\n      primaryBtnHover: \"#dc2626\"\n    }\n  };\n  const themeConfig = themeConfigs[severity] || themeConfigs[_copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.Severity.CRITICAL];\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\n    \"div\",\n    {\n      style: {\n        position: \"fixed\",\n        bottom: \"20px\",\n        left: \"50%\",\n        transform: \"translateX(-50%)\",\n        maxWidth: \"min(95vw, 680px)\",\n        width: \"100%\",\n        zIndex: 1e4,\n        animation: \"bannerSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1)\"\n      },\n      children: [\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"style\", { children: `\n          @keyframes bannerSlideIn {\n            from {\n              opacity: 0;\n              transform: translateX(-50%) translateY(10px);\n            }\n            to {\n              opacity: 1;\n              transform: translateX(-50%) translateY(0);\n            }\n          }\n        ` }),\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\n          \"div\",\n          {\n            style: {\n              display: \"flex\",\n              alignItems: \"flex-start\",\n              gap: \"14px\",\n              borderRadius: \"16px\",\n              border: `1px solid ${themeConfig.border}`,\n              background: themeConfig.bg,\n              padding: \"18px 20px\",\n              boxShadow: \"0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)\",\n              position: \"relative\",\n              backdropFilter: \"blur(10px)\",\n              WebkitBackdropFilter: \"blur(10px)\"\n            },\n            children: [\n              onClose && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n                \"button\",\n                {\n                  onClick: onClose,\n                  style: {\n                    position: \"absolute\",\n                    top: \"12px\",\n                    right: \"12px\",\n                    background: \"rgba(255, 255, 255, 0.8)\",\n                    border: \"none\",\n                    color: themeConfig.text,\n                    cursor: \"pointer\",\n                    fontSize: \"18px\",\n                    lineHeight: \"1\",\n                    padding: \"6px\",\n                    borderRadius: \"8px\",\n                    opacity: 0.7,\n                    transition: \"all 0.2s ease\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    width: \"28px\",\n                    height: \"28px\"\n                  },\n                  title: \"Close\",\n                  onMouseOver: (e) => {\n                    e.currentTarget.style.opacity = \"1\";\n                    e.currentTarget.style.background = \"rgba(255, 255, 255, 1)\";\n                    e.currentTarget.style.transform = \"scale(1.05)\";\n                  },\n                  onMouseOut: (e) => {\n                    e.currentTarget.style.opacity = \"0.7\";\n                    e.currentTarget.style.background = \"rgba(255, 255, 255, 0.8)\";\n                    e.currentTarget.style.transform = \"scale(1)\";\n                  },\n                  children: \"\\xD7\"\n                }\n              ),\n              /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n                \"div\",\n                {\n                  style: {\n                    color: themeConfig.icon,\n                    flexShrink: 0,\n                    marginTop: \"1px\",\n                    padding: \"6px\",\n                    borderRadius: \"10px\",\n                    background: \"rgba(255, 255, 255, 0.7)\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\"\n                  },\n                  children: Icon\n                }\n              ),\n              /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", { style: { flex: 1, paddingRight: onClose ? \"40px\" : \"0\" }, children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n                  \"div\",\n                  {\n                    style: {\n                      fontSize: \"15px\",\n                      fontWeight: 600,\n                      color: themeConfig.text,\n                      lineHeight: \"1.5\",\n                      marginBottom: actions ? \"12px\" : \"0\",\n                      fontFamily: \"-apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif\"\n                    },\n                    children: cleanMessage\n                  }\n                ),\n                actions && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\n                  \"div\",\n                  {\n                    style: {\n                      display: \"flex\",\n                      gap: \"10px\",\n                      flexWrap: \"wrap\"\n                    },\n                    children: [\n                      actions.secondary && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n                        \"button\",\n                        {\n                          onClick: actions.secondary.onClick,\n                          style: {\n                            borderRadius: \"10px\",\n                            padding: \"8px 16px\",\n                            fontSize: \"14px\",\n                            fontWeight: 500,\n                            color: themeConfig.text,\n                            backgroundColor: \"rgba(255, 255, 255, 0.8)\",\n                            border: `1.5px solid ${themeConfig.border}`,\n                            cursor: \"pointer\",\n                            transition: \"all 0.2s ease\",\n                            fontFamily: \"-apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif\"\n                          },\n                          onMouseOver: (e) => {\n                            e.currentTarget.style.backgroundColor = \"rgba(255, 255, 255, 1)\";\n                            e.currentTarget.style.transform = \"translateY(-1px)\";\n                            e.currentTarget.style.boxShadow = \"0 4px 12px rgba(0, 0, 0, 0.15)\";\n                          },\n                          onMouseOut: (e) => {\n                            e.currentTarget.style.backgroundColor = \"rgba(255, 255, 255, 0.8)\";\n                            e.currentTarget.style.transform = \"translateY(0)\";\n                            e.currentTarget.style.boxShadow = \"none\";\n                          },\n                          children: actions.secondary.label\n                        }\n                      ),\n                      actions.primary && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n                        \"button\",\n                        {\n                          onClick: actions.primary.onClick,\n                          style: {\n                            borderRadius: \"10px\",\n                            padding: \"8px 16px\",\n                            fontSize: \"14px\",\n                            fontWeight: 600,\n                            color: \"#fff\",\n                            backgroundColor: themeConfig.primaryBtn,\n                            border: \"none\",\n                            cursor: \"pointer\",\n                            transition: \"all 0.2s ease\",\n                            boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\",\n                            fontFamily: \"-apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif\"\n                          },\n                          onMouseOver: (e) => {\n                            e.currentTarget.style.backgroundColor = themeConfig.primaryBtnHover;\n                            e.currentTarget.style.transform = \"translateY(-1px)\";\n                            e.currentTarget.style.boxShadow = \"0 6px 16px rgba(0, 0, 0, 0.2)\";\n                          },\n                          onMouseOut: (e) => {\n                            e.currentTarget.style.backgroundColor = themeConfig.primaryBtn;\n                            e.currentTarget.style.transform = \"translateY(0)\";\n                            e.currentTarget.style.boxShadow = \"0 4px 12px rgba(0, 0, 0, 0.15)\";\n                          },\n                          children: actions.primary.label\n                        }\n                      )\n                    ]\n                  }\n                )\n              ] })\n            ]\n          }\n        )\n      ]\n    }\n  );\n}\nfunction renderCopilotKitUsage(error, onClose) {\n  if (error.visibility !== _copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.ErrorVisibility.BANNER) {\n    return null;\n  }\n  const extractUrlFromMessage = (message) => {\n    const linkRegex = /\\[([^\\]]+)\\]\\(([^)]+)\\)/g;\n    const match = linkRegex.exec(message);\n    return match ? match[2] : null;\n  };\n  const getErrorActions = (error2) => {\n    switch (error2.name) {\n      case _copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.ERROR_NAMES.MISSING_PUBLIC_API_KEY_ERROR:\n        return {\n          primary: {\n            label: \"Sign In\",\n            onClick: () => window.location.href = \"https://cloud.copilotkit.ai\"\n          }\n        };\n      case _copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.ERROR_NAMES.UPGRADE_REQUIRED_ERROR:\n        return {\n          primary: {\n            label: \"Upgrade\",\n            onClick: () => window.location.href = \"https://copilotkit.ai/\"\n          }\n        };\n      case _copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.ERROR_NAMES.COPILOT_API_DISCOVERY_ERROR:\n      case _copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.ERROR_NAMES.COPILOT_REMOTE_ENDPOINT_DISCOVERY_ERROR:\n      case _copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.ERROR_NAMES.COPILOT_KIT_AGENT_DISCOVERY_ERROR:\n        return {\n          primary: {\n            label: \"View Docs\",\n            onClick: () => {\n              var _a;\n              const urlFromMessage = extractUrlFromMessage(error2.message);\n              const urlFromExtensions = (_a = error2.extensions) == null ? void 0 : _a.troubleshootingUrl;\n              const url = urlFromMessage || urlFromExtensions || \"https://docs.copilotkit.ai/troubleshooting/common-issues\";\n              window.open(url, \"_blank\");\n            }\n          }\n        };\n      default:\n        return void 0;\n    }\n  };\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n    UsageBanner,\n    {\n      severity: error.severity || _copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.Severity.CRITICAL,\n      message: error.message,\n      onClose,\n      actions: getErrorActions(error)\n    }\n  );\n}\n\n\n//# sourceMappingURL=chunk-HD2GE3DK.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtY29yZUAxLjkuMi1uZXh0LjdfQHR5cGVzK3JlYWN0QDE5LjAuMV9ncmFwaHFsQDE2LjkuMF9yZWFjdC1kb21AMTkuMC4wX3JlYWN0QDE5LjAuMF9fcmVhY3RAMTkuMC4wL25vZGVfbW9kdWxlcy9AY29waWxvdGtpdC9yZWFjdC1jb3JlL2Rpc3QvY2h1bmstSEQyR0UzREsubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUM0RTtBQUM5QjtBQUM5QztBQUNBLEdBQUcsd0RBQVEsNEJBQTRCLHVEQUFJO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isc0RBQUcsYUFBYSw2QkFBNkI7QUFDckUsd0JBQXdCLHNEQUFHLFdBQVcsc0NBQXNDO0FBQzVFLHdCQUF3QixzREFBRyxXQUFXLHNDQUFzQztBQUM1RTtBQUNBO0FBQ0E7QUFDQSxHQUFHLHdEQUFRLDJCQUEyQix1REFBSTtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHNEQUFHLFdBQVcsZ0ZBQWdGO0FBQ3RILHdCQUF3QixzREFBRyxXQUFXLHVDQUF1QztBQUM3RSx3QkFBd0Isc0RBQUcsV0FBVywyQ0FBMkM7QUFDakY7QUFDQTtBQUNBO0FBQ0EsR0FBRyx3REFBUSx3QkFBd0IsdURBQUk7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixzREFBRyxhQUFhLDZCQUE2QjtBQUNyRSx3QkFBd0Isc0RBQUcsV0FBVyx3Q0FBd0M7QUFDOUUsd0JBQXdCLHNEQUFHLFdBQVcseUNBQXlDO0FBQy9FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLHdEQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUssd0RBQVE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsS0FBSyx3REFBUTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxLQUFLLHdEQUFRO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZEQUE2RCx3REFBUTtBQUNyRSx5QkFBeUIsdURBQUk7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLHdCQUF3QixzREFBRyxZQUFZO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYLHdCQUF3Qix1REFBSTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxtQkFBbUI7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EseUNBQXlDLHNEQUFHO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHNEQUFHO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsdURBQUksVUFBVSxTQUFTLCtDQUErQztBQUNwRyxnQ0FBZ0Msc0RBQUc7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyx1REFBSTtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSwyREFBMkQsc0RBQUc7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQsbUJBQW1CO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSx5REFBeUQsc0RBQUc7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLCtEQUFlO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsMkRBQVc7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVywyREFBVztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLDJEQUFXO0FBQ3RCLFdBQVcsMkRBQVc7QUFDdEIsV0FBVywyREFBVztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixzREFBRztBQUM1QjtBQUNBO0FBQ0Esa0NBQWtDLHdEQUFRO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFLRTtBQUNGIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtY29yZUAxLjkuMi1uZXh0LjdfQHR5cGVzK3JlYWN0QDE5LjAuMV9ncmFwaHFsQDE2LjkuMF9yZWFjdC1kb21AMTkuMC4wX3JlYWN0QDE5LjAuMF9fcmVhY3RAMTkuMC4wL25vZGVfbW9kdWxlcy9AY29waWxvdGtpdC9yZWFjdC1jb3JlL2Rpc3QvY2h1bmstSEQyR0UzREsubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9jb21wb25lbnRzL3VzYWdlLWJhbm5lci50c3hcbmltcG9ydCB7IFNldmVyaXR5LCBFUlJPUl9OQU1FUywgRXJyb3JWaXNpYmlsaXR5IH0gZnJvbSBcIkBjb3BpbG90a2l0L3NoYXJlZFwiO1xuaW1wb3J0IHsganN4LCBqc3hzIH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgZGVmYXVsdEljb25zID0ge1xuICBbU2V2ZXJpdHkuQ1JJVElDQUxdOiAvKiBAX19QVVJFX18gKi8ganN4cyhcbiAgICBcInN2Z1wiLFxuICAgIHtcbiAgICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgICB3aWR0aDogXCIxOFwiLFxuICAgICAgaGVpZ2h0OiBcIjE4XCIsXG4gICAgICBzdHJva2U6IFwiY3VycmVudENvbG9yXCIsXG4gICAgICBzdHJva2VXaWR0aDogXCIyLjVcIixcbiAgICAgIGZpbGw6IFwibm9uZVwiLFxuICAgICAgc3Ryb2tlTGluZWNhcDogXCJyb3VuZFwiLFxuICAgICAgc3Ryb2tlTGluZWpvaW46IFwicm91bmRcIixcbiAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgIC8qIEBfX1BVUkVfXyAqLyBqc3goXCJjaXJjbGVcIiwgeyBjeDogXCIxMlwiLCBjeTogXCIxMlwiLCByOiBcIjEwXCIgfSksXG4gICAgICAgIC8qIEBfX1BVUkVfXyAqLyBqc3goXCJsaW5lXCIsIHsgeDE6IFwiMTVcIiwgeTE6IFwiOVwiLCB4MjogXCI5XCIsIHkyOiBcIjE1XCIgfSksXG4gICAgICAgIC8qIEBfX1BVUkVfXyAqLyBqc3goXCJsaW5lXCIsIHsgeDE6IFwiOVwiLCB5MTogXCI5XCIsIHgyOiBcIjE1XCIsIHkyOiBcIjE1XCIgfSlcbiAgICAgIF1cbiAgICB9XG4gICksXG4gIFtTZXZlcml0eS5XQVJOSU5HXTogLyogQF9fUFVSRV9fICovIGpzeHMoXG4gICAgXCJzdmdcIixcbiAgICB7XG4gICAgICB2aWV3Qm94OiBcIjAgMCAyNCAyNFwiLFxuICAgICAgd2lkdGg6IFwiMThcIixcbiAgICAgIGhlaWdodDogXCIxOFwiLFxuICAgICAgc3Ryb2tlOiBcImN1cnJlbnRDb2xvclwiLFxuICAgICAgc3Ryb2tlV2lkdGg6IFwiMi41XCIsXG4gICAgICBmaWxsOiBcIm5vbmVcIixcbiAgICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICAgIHN0cm9rZUxpbmVqb2luOiBcInJvdW5kXCIsXG4gICAgICBjaGlsZHJlbjogW1xuICAgICAgICAvKiBAX19QVVJFX18gKi8ganN4KFwicGF0aFwiLCB7IGQ6IFwibTIxLjczIDE4LTgtMTRhMiAyIDAgMCAwLTMuNDggMGwtOCAxNEEyIDIgMCAwIDAgNCAyMWgxNmEyIDIgMCAwIDAgMS43My0zWlwiIH0pLFxuICAgICAgICAvKiBAX19QVVJFX18gKi8ganN4KFwibGluZVwiLCB7IHgxOiBcIjEyXCIsIHkxOiBcIjlcIiwgeDI6IFwiMTJcIiwgeTI6IFwiMTNcIiB9KSxcbiAgICAgICAgLyogQF9fUFVSRV9fICovIGpzeChcImxpbmVcIiwgeyB4MTogXCIxMlwiLCB5MTogXCIxN1wiLCB4MjogXCIxMi4wMVwiLCB5MjogXCIxN1wiIH0pXG4gICAgICBdXG4gICAgfVxuICApLFxuICBbU2V2ZXJpdHkuSU5GT106IC8qIEBfX1BVUkVfXyAqLyBqc3hzKFxuICAgIFwic3ZnXCIsXG4gICAge1xuICAgICAgdmlld0JveDogXCIwIDAgMjQgMjRcIixcbiAgICAgIHdpZHRoOiBcIjE4XCIsXG4gICAgICBoZWlnaHQ6IFwiMThcIixcbiAgICAgIHN0cm9rZTogXCJjdXJyZW50Q29sb3JcIixcbiAgICAgIHN0cm9rZVdpZHRoOiBcIjIuNVwiLFxuICAgICAgZmlsbDogXCJub25lXCIsXG4gICAgICBzdHJva2VMaW5lY2FwOiBcInJvdW5kXCIsXG4gICAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiLFxuICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgLyogQF9fUFVSRV9fICovIGpzeChcImNpcmNsZVwiLCB7IGN4OiBcIjEyXCIsIGN5OiBcIjEyXCIsIHI6IFwiMTBcIiB9KSxcbiAgICAgICAgLyogQF9fUFVSRV9fICovIGpzeChcImxpbmVcIiwgeyB4MTogXCIxMlwiLCB5MTogXCIxNlwiLCB4MjogXCIxMlwiLCB5MjogXCIxMlwiIH0pLFxuICAgICAgICAvKiBAX19QVVJFX18gKi8ganN4KFwibGluZVwiLCB7IHgxOiBcIjEyXCIsIHkxOiBcIjhcIiwgeDI6IFwiMTIuMDFcIiwgeTI6IFwiOFwiIH0pXG4gICAgICBdXG4gICAgfVxuICApXG59O1xuZnVuY3Rpb24gVXNhZ2VCYW5uZXIoe1xuICBzZXZlcml0eSA9IFNldmVyaXR5LkNSSVRJQ0FMLFxuICBtZXNzYWdlID0gXCJcIixcbiAgaWNvbixcbiAgb25DbG9zZSxcbiAgYWN0aW9uc1xufSkge1xuICBpZiAoIW1lc3NhZ2UgfHwgIXNldmVyaXR5KSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbiAgY29uc3QgcGFyc2VNZXNzYWdlID0gKHJhd01lc3NhZ2UpID0+IHtcbiAgICBjb25zdCBsaW5rUmVnZXggPSAvXFxbKFteXFxdXSspXFxdXFwoKFteKV0rKVxcKS9nO1xuICAgIGNvbnN0IG1hdGNoZXMgPSBBcnJheS5mcm9tKHJhd01lc3NhZ2UubWF0Y2hBbGwobGlua1JlZ2V4KSk7XG4gICAgaWYgKG1hdGNoZXMubGVuZ3RoID4gMCkge1xuICAgICAgbGV0IGNsZWFuTWVzc2FnZTIgPSByYXdNZXNzYWdlLnJlcGxhY2UoL1xcLlxccypTZWUgbW9yZTpcXHMqXFxbKFteXFxdXSspXFxdXFwoKFteKV0rKVxcKS9nLCBcIi5cIikucmVwbGFjZSgvU2VlIG1vcmU6XFxzKlxcWyhbXlxcXV0rKVxcXVxcKChbXildKylcXCkvZywgXCJcIikudHJpbSgpO1xuICAgICAgcmV0dXJuIGNsZWFuTWVzc2FnZTI7XG4gICAgfVxuICAgIHJldHVybiByYXdNZXNzYWdlO1xuICB9O1xuICBjb25zdCBjbGVhbk1lc3NhZ2UgPSBwYXJzZU1lc3NhZ2UobWVzc2FnZSk7XG4gIGNvbnN0IEljb24gPSBpY29uIHx8IGRlZmF1bHRJY29uc1tzZXZlcml0eV07XG4gIGNvbnN0IHRoZW1lQ29uZmlncyA9IHtcbiAgICBbU2V2ZXJpdHkuSU5GT106IHtcbiAgICAgIGJnOiBcImxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNlZmY2ZmYgMCUsICNkYmVhZmUgMTAwJSlcIixcbiAgICAgIGJvcmRlcjogXCIjOTNjNWZkXCIsXG4gICAgICB0ZXh0OiBcIiMxZTQwYWZcIixcbiAgICAgIGljb246IFwiIzNiODJmNlwiLFxuICAgICAgcHJpbWFyeUJ0bjogXCIjM2I4MmY2XCIsXG4gICAgICBwcmltYXJ5QnRuSG92ZXI6IFwiIzI1NjNlYlwiXG4gICAgfSxcbiAgICBbU2V2ZXJpdHkuV0FSTklOR106IHtcbiAgICAgIGJnOiBcImxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmZmZiZWIgMCUsICNmZWYzYzcgMTAwJSlcIixcbiAgICAgIGJvcmRlcjogXCIjZmJiZjI0XCIsXG4gICAgICB0ZXh0OiBcIiM5MjQwMGVcIixcbiAgICAgIGljb246IFwiI2Y1OWUwYlwiLFxuICAgICAgcHJpbWFyeUJ0bjogXCIjZjU5ZTBiXCIsXG4gICAgICBwcmltYXJ5QnRuSG92ZXI6IFwiI2Q5NzcwNlwiXG4gICAgfSxcbiAgICBbU2V2ZXJpdHkuQ1JJVElDQUxdOiB7XG4gICAgICBiZzogXCJsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmVmMmYyIDAlLCAjZmVjYWNhIDEwMCUpXCIsXG4gICAgICBib3JkZXI6IFwiI2Y4NzE3MVwiLFxuICAgICAgdGV4dDogXCIjOTkxYjFiXCIsXG4gICAgICBpY29uOiBcIiNlZjQ0NDRcIixcbiAgICAgIHByaW1hcnlCdG46IFwiI2VmNDQ0NFwiLFxuICAgICAgcHJpbWFyeUJ0bkhvdmVyOiBcIiNkYzI2MjZcIlxuICAgIH1cbiAgfTtcbiAgY29uc3QgdGhlbWVDb25maWcgPSB0aGVtZUNvbmZpZ3Nbc2V2ZXJpdHldIHx8IHRoZW1lQ29uZmlnc1tTZXZlcml0eS5DUklUSUNBTF07XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4cyhcbiAgICBcImRpdlwiLFxuICAgIHtcbiAgICAgIHN0eWxlOiB7XG4gICAgICAgIHBvc2l0aW9uOiBcImZpeGVkXCIsXG4gICAgICAgIGJvdHRvbTogXCIyMHB4XCIsXG4gICAgICAgIGxlZnQ6IFwiNTAlXCIsXG4gICAgICAgIHRyYW5zZm9ybTogXCJ0cmFuc2xhdGVYKC01MCUpXCIsXG4gICAgICAgIG1heFdpZHRoOiBcIm1pbig5NXZ3LCA2ODBweClcIixcbiAgICAgICAgd2lkdGg6IFwiMTAwJVwiLFxuICAgICAgICB6SW5kZXg6IDFlNCxcbiAgICAgICAgYW5pbWF0aW9uOiBcImJhbm5lclNsaWRlSW4gMC40cyBjdWJpYy1iZXppZXIoMC4xNiwgMSwgMC4zLCAxKVwiXG4gICAgICB9LFxuICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgLyogQF9fUFVSRV9fICovIGpzeChcInN0eWxlXCIsIHsgY2hpbGRyZW46IGBcbiAgICAgICAgICBAa2V5ZnJhbWVzIGJhbm5lclNsaWRlSW4ge1xuICAgICAgICAgICAgZnJvbSB7XG4gICAgICAgICAgICAgIG9wYWNpdHk6IDA7XG4gICAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKSB0cmFuc2xhdGVZKDEwcHgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdG8ge1xuICAgICAgICAgICAgICBvcGFjaXR5OiAxO1xuICAgICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTUwJSkgdHJhbnNsYXRlWSgwKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIGAgfSksXG4gICAgICAgIC8qIEBfX1BVUkVfXyAqLyBqc3hzKFxuICAgICAgICAgIFwiZGl2XCIsXG4gICAgICAgICAge1xuICAgICAgICAgICAgc3R5bGU6IHtcbiAgICAgICAgICAgICAgZGlzcGxheTogXCJmbGV4XCIsXG4gICAgICAgICAgICAgIGFsaWduSXRlbXM6IFwiZmxleC1zdGFydFwiLFxuICAgICAgICAgICAgICBnYXA6IFwiMTRweFwiLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IFwiMTZweFwiLFxuICAgICAgICAgICAgICBib3JkZXI6IGAxcHggc29saWQgJHt0aGVtZUNvbmZpZy5ib3JkZXJ9YCxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogdGhlbWVDb25maWcuYmcsXG4gICAgICAgICAgICAgIHBhZGRpbmc6IFwiMThweCAyMHB4XCIsXG4gICAgICAgICAgICAgIGJveFNoYWRvdzogXCIwIDEwcHggMjVweCAtNXB4IHJnYmEoMCwgMCwgMCwgMC4xKSwgMCA4cHggMTBweCAtNnB4IHJnYmEoMCwgMCwgMCwgMC4xKVwiLFxuICAgICAgICAgICAgICBwb3NpdGlvbjogXCJyZWxhdGl2ZVwiLFxuICAgICAgICAgICAgICBiYWNrZHJvcEZpbHRlcjogXCJibHVyKDEwcHgpXCIsXG4gICAgICAgICAgICAgIFdlYmtpdEJhY2tkcm9wRmlsdGVyOiBcImJsdXIoMTBweClcIlxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICAgICAgIG9uQ2xvc2UgJiYgLyogQF9fUFVSRV9fICovIGpzeChcbiAgICAgICAgICAgICAgICBcImJ1dHRvblwiLFxuICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s6IG9uQ2xvc2UsXG4gICAgICAgICAgICAgICAgICBzdHlsZToge1xuICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogXCJhYnNvbHV0ZVwiLFxuICAgICAgICAgICAgICAgICAgICB0b3A6IFwiMTJweFwiLFxuICAgICAgICAgICAgICAgICAgICByaWdodDogXCIxMnB4XCIsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IFwicmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpXCIsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlcjogXCJub25lXCIsXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiB0aGVtZUNvbmZpZy50ZXh0LFxuICAgICAgICAgICAgICAgICAgICBjdXJzb3I6IFwicG9pbnRlclwiLFxuICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogXCIxOHB4XCIsXG4gICAgICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IFwiMVwiLFxuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiBcIjZweFwiLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IFwiOHB4XCIsXG4gICAgICAgICAgICAgICAgICAgIG9wYWNpdHk6IDAuNyxcbiAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogXCJhbGwgMC4ycyBlYXNlXCIsXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IFwiZmxleFwiLFxuICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiBcImNlbnRlclwiLFxuICAgICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogXCJjZW50ZXJcIixcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6IFwiMjhweFwiLFxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IFwiMjhweFwiXG4gICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgdGl0bGU6IFwiQ2xvc2VcIixcbiAgICAgICAgICAgICAgICAgIG9uTW91c2VPdmVyOiAoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUub3BhY2l0eSA9IFwiMVwiO1xuICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9IFwicmdiYSgyNTUsIDI1NSwgMjU1LCAxKVwiO1xuICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUudHJhbnNmb3JtID0gXCJzY2FsZSgxLjA1KVwiO1xuICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgIG9uTW91c2VPdXQ6IChlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5vcGFjaXR5ID0gXCIwLjdcIjtcbiAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSBcInJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KVwiO1xuICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUudHJhbnNmb3JtID0gXCJzY2FsZSgxKVwiO1xuICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBcIlxceEQ3XCJcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICksXG4gICAgICAgICAgICAgIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgICAgICAgICAgICAgXCJkaXZcIixcbiAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICBzdHlsZToge1xuICAgICAgICAgICAgICAgICAgICBjb2xvcjogdGhlbWVDb25maWcuaWNvbixcbiAgICAgICAgICAgICAgICAgICAgZmxleFNocmluazogMCxcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luVG9wOiBcIjFweFwiLFxuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiBcIjZweFwiLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IFwiMTBweFwiLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBcInJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KVwiLFxuICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcbiAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogXCJjZW50ZXJcIixcbiAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6IFwiY2VudGVyXCJcbiAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICBjaGlsZHJlbjogSWNvblxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgKSxcbiAgICAgICAgICAgICAgLyogQF9fUFVSRV9fICovIGpzeHMoXCJkaXZcIiwgeyBzdHlsZTogeyBmbGV4OiAxLCBwYWRkaW5nUmlnaHQ6IG9uQ2xvc2UgPyBcIjQwcHhcIiA6IFwiMFwiIH0sIGNoaWxkcmVuOiBbXG4gICAgICAgICAgICAgICAgLyogQF9fUFVSRV9fICovIGpzeChcbiAgICAgICAgICAgICAgICAgIFwiZGl2XCIsXG4gICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgIHN0eWxlOiB7XG4gICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6IFwiMTVweFwiLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcbiAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogdGhlbWVDb25maWcudGV4dCxcbiAgICAgICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiBcIjEuNVwiLFxuICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogYWN0aW9ucyA/IFwiMTJweFwiIDogXCIwXCIsXG4gICAgICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogXCItYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICdTZWdvZSBVSScsIHNhbnMtc2VyaWZcIlxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICBjaGlsZHJlbjogY2xlYW5NZXNzYWdlXG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgKSxcbiAgICAgICAgICAgICAgICBhY3Rpb25zICYmIC8qIEBfX1BVUkVfXyAqLyBqc3hzKFxuICAgICAgICAgICAgICAgICAgXCJkaXZcIixcbiAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgc3R5bGU6IHtcbiAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBcImZsZXhcIixcbiAgICAgICAgICAgICAgICAgICAgICBnYXA6IFwiMTBweFwiLFxuICAgICAgICAgICAgICAgICAgICAgIGZsZXhXcmFwOiBcIndyYXBcIlxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAgICAgICAgICAgICAgIGFjdGlvbnMuc2Vjb25kYXJ5ICYmIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgICAgICAgICAgICAgICAgICAgICBcImJ1dHRvblwiLFxuICAgICAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrOiBhY3Rpb25zLnNlY29uZGFyeS5vbkNsaWNrLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZToge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogXCIxMHB4XCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogXCI4cHggMTZweFwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjE0cHhcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiA1MDAsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IHRoZW1lQ29uZmlnLnRleHQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KVwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogYDEuNXB4IHNvbGlkICR7dGhlbWVDb25maWcuYm9yZGVyfWAsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiBcInBvaW50ZXJcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiBcImFsbCAwLjJzIGVhc2VcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250RmFtaWx5OiBcIi1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgJ1NlZ29lIFVJJywgc2Fucy1zZXJpZlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uTW91c2VPdmVyOiAoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kQ29sb3IgPSBcInJnYmEoMjU1LCAyNTUsIDI1NSwgMSlcIjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUudHJhbnNmb3JtID0gXCJ0cmFuc2xhdGVZKC0xcHgpXCI7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJveFNoYWRvdyA9IFwiMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpXCI7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uTW91c2VPdXQ6IChlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJhY2tncm91bmRDb2xvciA9IFwicmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpXCI7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLnRyYW5zZm9ybSA9IFwidHJhbnNsYXRlWSgwKVwiO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5ib3hTaGFkb3cgPSBcIm5vbmVcIjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2hpbGRyZW46IGFjdGlvbnMuc2Vjb25kYXJ5LmxhYmVsXG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgKSxcbiAgICAgICAgICAgICAgICAgICAgICBhY3Rpb25zLnByaW1hcnkgJiYgLyogQF9fUFVSRV9fICovIGpzeChcbiAgICAgICAgICAgICAgICAgICAgICAgIFwiYnV0dG9uXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s6IGFjdGlvbnMucHJpbWFyeS5vbkNsaWNrLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZToge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogXCIxMHB4XCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogXCI4cHggMTZweFwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiBcIjE0cHhcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiA2MDAsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwiI2ZmZlwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogdGhlbWVDb25maWcucHJpbWFyeUJ0bixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6IFwibm9uZVwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogXCJwb2ludGVyXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogXCJhbGwgMC4ycyBlYXNlXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYm94U2hhZG93OiBcIjAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjE1KVwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRGYW1pbHk6IFwiLWFwcGxlLXN5c3RlbSwgQmxpbmtNYWNTeXN0ZW1Gb250LCAnU2Vnb2UgVUknLCBzYW5zLXNlcmlmXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZU92ZXI6IChlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJhY2tncm91bmRDb2xvciA9IHRoZW1lQ29uZmlnLnByaW1hcnlCdG5Ib3ZlcjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUudHJhbnNmb3JtID0gXCJ0cmFuc2xhdGVZKC0xcHgpXCI7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJveFNoYWRvdyA9IFwiMCA2cHggMTZweCByZ2JhKDAsIDAsIDAsIDAuMilcIjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZU91dDogKGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUuYmFja2dyb3VuZENvbG9yID0gdGhlbWVDb25maWcucHJpbWFyeUJ0bjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUudHJhbnNmb3JtID0gXCJ0cmFuc2xhdGVZKDApXCI7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJveFNoYWRvdyA9IFwiMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpXCI7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiBhY3Rpb25zLnByaW1hcnkubGFiZWxcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgIF1cbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgIF0gfSlcbiAgICAgICAgICAgIF1cbiAgICAgICAgICB9XG4gICAgICAgIClcbiAgICAgIF1cbiAgICB9XG4gICk7XG59XG5mdW5jdGlvbiByZW5kZXJDb3BpbG90S2l0VXNhZ2UoZXJyb3IsIG9uQ2xvc2UpIHtcbiAgaWYgKGVycm9yLnZpc2liaWxpdHkgIT09IEVycm9yVmlzaWJpbGl0eS5CQU5ORVIpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuICBjb25zdCBleHRyYWN0VXJsRnJvbU1lc3NhZ2UgPSAobWVzc2FnZSkgPT4ge1xuICAgIGNvbnN0IGxpbmtSZWdleCA9IC9cXFsoW15cXF1dKylcXF1cXCgoW14pXSspXFwpL2c7XG4gICAgY29uc3QgbWF0Y2ggPSBsaW5rUmVnZXguZXhlYyhtZXNzYWdlKTtcbiAgICByZXR1cm4gbWF0Y2ggPyBtYXRjaFsyXSA6IG51bGw7XG4gIH07XG4gIGNvbnN0IGdldEVycm9yQWN0aW9ucyA9IChlcnJvcjIpID0+IHtcbiAgICBzd2l0Y2ggKGVycm9yMi5uYW1lKSB7XG4gICAgICBjYXNlIEVSUk9SX05BTUVTLk1JU1NJTkdfUFVCTElDX0FQSV9LRVlfRVJST1I6XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgcHJpbWFyeToge1xuICAgICAgICAgICAgbGFiZWw6IFwiU2lnbiBJblwiLFxuICAgICAgICAgICAgb25DbGljazogKCkgPT4gd2luZG93LmxvY2F0aW9uLmhyZWYgPSBcImh0dHBzOi8vY2xvdWQuY29waWxvdGtpdC5haVwiXG4gICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgY2FzZSBFUlJPUl9OQU1FUy5VUEdSQURFX1JFUVVJUkVEX0VSUk9SOlxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHByaW1hcnk6IHtcbiAgICAgICAgICAgIGxhYmVsOiBcIlVwZ3JhZGVcIixcbiAgICAgICAgICAgIG9uQ2xpY2s6ICgpID0+IHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gXCJodHRwczovL2NvcGlsb3RraXQuYWkvXCJcbiAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICBjYXNlIEVSUk9SX05BTUVTLkNPUElMT1RfQVBJX0RJU0NPVkVSWV9FUlJPUjpcbiAgICAgIGNhc2UgRVJST1JfTkFNRVMuQ09QSUxPVF9SRU1PVEVfRU5EUE9JTlRfRElTQ09WRVJZX0VSUk9SOlxuICAgICAgY2FzZSBFUlJPUl9OQU1FUy5DT1BJTE9UX0tJVF9BR0VOVF9ESVNDT1ZFUllfRVJST1I6XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgcHJpbWFyeToge1xuICAgICAgICAgICAgbGFiZWw6IFwiVmlldyBEb2NzXCIsXG4gICAgICAgICAgICBvbkNsaWNrOiAoKSA9PiB7XG4gICAgICAgICAgICAgIHZhciBfYTtcbiAgICAgICAgICAgICAgY29uc3QgdXJsRnJvbU1lc3NhZ2UgPSBleHRyYWN0VXJsRnJvbU1lc3NhZ2UoZXJyb3IyLm1lc3NhZ2UpO1xuICAgICAgICAgICAgICBjb25zdCB1cmxGcm9tRXh0ZW5zaW9ucyA9IChfYSA9IGVycm9yMi5leHRlbnNpb25zKSA9PSBudWxsID8gdm9pZCAwIDogX2EudHJvdWJsZXNob290aW5nVXJsO1xuICAgICAgICAgICAgICBjb25zdCB1cmwgPSB1cmxGcm9tTWVzc2FnZSB8fCB1cmxGcm9tRXh0ZW5zaW9ucyB8fCBcImh0dHBzOi8vZG9jcy5jb3BpbG90a2l0LmFpL3Ryb3VibGVzaG9vdGluZy9jb21tb24taXNzdWVzXCI7XG4gICAgICAgICAgICAgIHdpbmRvdy5vcGVuKHVybCwgXCJfYmxhbmtcIik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIHZvaWQgMDtcbiAgICB9XG4gIH07XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgIFVzYWdlQmFubmVyLFxuICAgIHtcbiAgICAgIHNldmVyaXR5OiBlcnJvci5zZXZlcml0eSB8fCBTZXZlcml0eS5DUklUSUNBTCxcbiAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICBvbkNsb3NlLFxuICAgICAgYWN0aW9uczogZ2V0RXJyb3JBY3Rpb25zKGVycm9yKVxuICAgIH1cbiAgKTtcbn1cblxuZXhwb3J0IHtcbiAgVXNhZ2VCYW5uZXIsXG4gIHJlbmRlckNvcGlsb3RLaXRVc2FnZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNodW5rLUhEMkdFM0RLLm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-HD2GE3DK.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-I4JPQECN.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-I4JPQECN.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultSystemMessage: () => (/* binding */ defaultSystemMessage),\n/* harmony export */   useCopilotChat: () => (/* binding */ useCopilotChat)\n/* harmony export */ });\n/* harmony import */ var _chunk_RN3ZRHI7_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-RN3ZRHI7.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-RN3ZRHI7.mjs\");\n/* harmony import */ var _chunk_DCTJZ742_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-DCTJZ742.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-DCTJZ742.mjs\");\n/* harmony import */ var _chunk_JXF732XG_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./chunk-JXF732XG.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-JXF732XG.mjs\");\n/* harmony import */ var _chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-CCESTGAM.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-CCESTGAM.mjs\");\n/* harmony import */ var _chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-XFOTNHYA.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var _chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-SKC7AJIV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-ROUIRR4B.mjs\");\n\n\n\n\n\n\n\n// src/hooks/use-copilot-chat.ts\n\n\nfunction useCopilotChat(_a = {}) {\n  var _b = _a, {\n    makeSystemMessage\n  } = _b, options = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__objRest)(_b, [\n    \"makeSystemMessage\"\n  ]);\n  const {\n    getContextString,\n    getFunctionCallHandler,\n    copilotApiConfig,\n    isLoading,\n    setIsLoading,\n    chatInstructions,\n    actions,\n    coagentStatesRef,\n    setCoagentStatesWithRef,\n    coAgentStateRenders,\n    agentSession,\n    setAgentSession,\n    forwardedParameters,\n    agentLock,\n    threadId,\n    setThreadId,\n    runId,\n    setRunId,\n    chatAbortControllerRef,\n    extensions,\n    setExtensions,\n    langGraphInterruptAction,\n    setLangGraphInterruptAction\n  } = (0,_chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_2__.useCopilotContext)();\n  const { messages, setMessages } = (0,_chunk_DCTJZ742_mjs__WEBPACK_IMPORTED_MODULE_3__.useCopilotMessagesContext)();\n  const [mcpServers, setLocalMcpServers] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (mcpServers.length > 0) {\n      const serversCopy = [...mcpServers];\n      copilotApiConfig.mcpServers = serversCopy;\n      if (!copilotApiConfig.properties) {\n        copilotApiConfig.properties = {};\n      }\n      copilotApiConfig.properties.mcpServers = serversCopy;\n    }\n  }, [mcpServers, copilotApiConfig]);\n  const setMcpServers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((servers) => {\n    setLocalMcpServers(servers);\n  }, []);\n  const onCoAgentStateRender = (0,_chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_4__.useAsyncCallback)(\n    (args) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__async)(this, null, function* () {\n      var _a2;\n      const { name, nodeName, state } = args;\n      let action = Object.values(coAgentStateRenders).find(\n        (action2) => action2.name === name && action2.nodeName === nodeName\n      );\n      if (!action) {\n        action = Object.values(coAgentStateRenders).find(\n          (action2) => action2.name === name && !action2.nodeName\n        );\n      }\n      if (action) {\n        yield (_a2 = action.handler) == null ? void 0 : _a2.call(action, { state, nodeName });\n      }\n    }),\n    [coAgentStateRenders]\n  );\n  const makeSystemMessageCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const systemMessageMaker = makeSystemMessage || defaultSystemMessage;\n    const contextString = getContextString([], _chunk_RN3ZRHI7_mjs__WEBPACK_IMPORTED_MODULE_5__.defaultCopilotContextCategories);\n    return new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__.TextMessage({\n      content: systemMessageMaker(contextString, chatInstructions),\n      role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__.Role.System\n    });\n  }, [getContextString, makeSystemMessage, chatInstructions]);\n  const deleteMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (messageId) => {\n      setMessages((prev) => prev.filter((message) => message.id !== messageId));\n    },\n    [setMessages]\n  );\n  const { append, reload, stop, runChatCompletion } = (0,_chunk_JXF732XG_mjs__WEBPACK_IMPORTED_MODULE_7__.useChat)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__spreadValues)({}, options), {\n    actions: Object.values(actions),\n    copilotConfig: copilotApiConfig,\n    initialMessages: options.initialMessages || [],\n    onFunctionCall: getFunctionCallHandler(),\n    onCoAgentStateRender,\n    messages,\n    setMessages,\n    makeSystemMessageCallback,\n    isLoading,\n    setIsLoading,\n    coagentStatesRef,\n    setCoagentStatesWithRef,\n    agentSession,\n    setAgentSession,\n    forwardedParameters,\n    threadId,\n    setThreadId,\n    runId,\n    setRunId,\n    chatAbortControllerRef,\n    agentLock,\n    extensions,\n    setExtensions,\n    langGraphInterruptAction,\n    setLangGraphInterruptAction\n  }));\n  const latestAppend = useUpdatedRef(append);\n  const latestAppendFunc = (0,_chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_4__.useAsyncCallback)(\n    (message, options2) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__async)(this, null, function* () {\n      return yield latestAppend.current(message, options2);\n    }),\n    [latestAppend]\n  );\n  const latestReload = useUpdatedRef(reload);\n  const latestReloadFunc = (0,_chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_4__.useAsyncCallback)(\n    (messageId) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__async)(this, null, function* () {\n      return yield latestReload.current(messageId);\n    }),\n    [latestReload]\n  );\n  const latestStop = useUpdatedRef(stop);\n  const latestStopFunc = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    return latestStop.current();\n  }, [latestStop]);\n  const latestDelete = useUpdatedRef(deleteMessage);\n  const latestDeleteFunc = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (messageId) => {\n      return latestDelete.current(messageId);\n    },\n    [latestDelete]\n  );\n  const latestSetMessages = useUpdatedRef(setMessages);\n  const latestSetMessagesFunc = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (messages2) => {\n      return latestSetMessages.current(messages2);\n    },\n    [latestSetMessages]\n  );\n  const latestRunChatCompletion = useUpdatedRef(runChatCompletion);\n  const latestRunChatCompletionFunc = (0,_chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_4__.useAsyncCallback)(() => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__async)(this, null, function* () {\n    return yield latestRunChatCompletion.current();\n  }), [latestRunChatCompletion]);\n  const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    latestStopFunc();\n    setMessages([]);\n    setRunId(null);\n    setCoagentStatesWithRef({});\n    let initialAgentSession = null;\n    if (agentLock) {\n      initialAgentSession = {\n        agentName: agentLock\n      };\n    }\n    setAgentSession(initialAgentSession);\n  }, [\n    latestStopFunc,\n    setMessages,\n    setThreadId,\n    setCoagentStatesWithRef,\n    setAgentSession,\n    agentLock\n  ]);\n  const latestReset = useUpdatedRef(reset);\n  const latestResetFunc = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    return latestReset.current();\n  }, [latestReset]);\n  return {\n    visibleMessages: messages,\n    appendMessage: latestAppendFunc,\n    setMessages: latestSetMessagesFunc,\n    reloadMessages: latestReloadFunc,\n    stopGeneration: latestStopFunc,\n    reset: latestResetFunc,\n    deleteMessage: latestDeleteFunc,\n    runChatCompletion: latestRunChatCompletionFunc,\n    isLoading,\n    mcpServers,\n    setMcpServers\n  };\n}\nfunction useUpdatedRef(value) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = value;\n  }, [value]);\n  return ref;\n}\nfunction defaultSystemMessage(contextString, additionalInstructions) {\n  return `\nPlease act as an efficient, competent, conscientious, and industrious professional assistant.\n\nHelp the user achieve their goals, and you do so in a way that is as efficient as possible, without unnecessary fluff, but also without sacrificing professionalism.\nAlways be polite and respectful, and prefer brevity over verbosity.\n\nThe user has provided you with the following context:\n\\`\\`\\`\n${contextString}\n\\`\\`\\`\n\nThey have also provided you with functions you can call to initiate actions on their behalf, or functions you can call to receive more information.\n\nPlease assist them as best you can.\n\nYou can ask them for clarifying questions if needed, but don't be annoying about it. If you can reasonably 'fill in the blanks' yourself, do so.\n\nIf you would like to call a function, call it without saying anything else.\nIn case of a function error:\n- If this error stems from incorrect function parameters or syntax, you may retry with corrected arguments.\n- If the error's source is unclear or seems unrelated to your input, do not attempt further retries.\n` + (additionalInstructions ? `\n\n${additionalInstructions}` : \"\");\n}\n\n\n//# sourceMappingURL=chunk-I4JPQECN.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-I4JPQECN.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-ISYBUDL4.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-ISYBUDL4.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCopilotRuntimeClient: () => (/* binding */ useCopilotRuntimeClient)\n/* harmony export */ });\n/* harmony import */ var _chunk_GFJW4RIM_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-GFJW4RIM.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-GFJW4RIM.mjs\");\n/* harmony import */ var _chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-VRXANACV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VRXANACV.mjs\");\n/* harmony import */ var _chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-SKC7AJIV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-EI72UE2B.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs\");\n\n\n\n\n// src/hooks/use-copilot-runtime-client.ts\n\n\n\nvar useCopilotRuntimeClient = (options) => {\n  const { setBannerError } = (0,_chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n  const _a = options, { showDevConsole, onTrace } = _a, runtimeOptions = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__objRest)(_a, [\"showDevConsole\", \"onTrace\"]);\n  const lastStructuredErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const traceUIError = (error, originalError) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__async)(void 0, null, function* () {\n    if (!onTrace || !runtimeOptions.publicApiKey)\n      return;\n    try {\n      const traceEvent = {\n        type: \"error\",\n        timestamp: Date.now(),\n        context: {\n          source: \"ui\",\n          request: {\n            operation: \"runtimeClient\",\n            url: runtimeOptions.url,\n            startTime: Date.now()\n          },\n          technical: {\n            environment: \"development\",\n            userAgent: typeof navigator !== \"undefined\" ? navigator.userAgent : void 0,\n            stackTrace: originalError instanceof Error ? originalError.stack : void 0\n          }\n        },\n        error\n      };\n      yield onTrace(traceEvent);\n    } catch (traceError) {\n      console.error(\"Error in onTrace handler:\", traceError);\n    }\n  });\n  const runtimeClient = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_3__.CopilotRuntimeClient((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({}, runtimeOptions), {\n      handleGQLErrors: (error) => {\n        var _a2;\n        if ((_a2 = error.graphQLErrors) == null ? void 0 : _a2.length) {\n          const graphQLErrors = error.graphQLErrors;\n          const routeError = (gqlError) => {\n            const extensions = gqlError.extensions;\n            const visibility = extensions == null ? void 0 : extensions.visibility;\n            const isDev = (0,_chunk_GFJW4RIM_mjs__WEBPACK_IMPORTED_MODULE_4__.shouldShowDevConsole)(showDevConsole != null ? showDevConsole : false);\n            if (visibility === _copilotkit_shared__WEBPACK_IMPORTED_MODULE_5__.ErrorVisibility.SILENT) {\n              console.error(\"CopilotKit Silent Error:\", gqlError.message);\n              return;\n            }\n            if (!isDev) {\n              console.error(\"CopilotKit Error (hidden in production):\", gqlError.message);\n              return;\n            }\n            const now = Date.now();\n            const errorMessage = gqlError.message;\n            if (lastStructuredErrorRef.current && lastStructuredErrorRef.current.message === errorMessage && now - lastStructuredErrorRef.current.timestamp < 150) {\n              return;\n            }\n            lastStructuredErrorRef.current = { message: errorMessage, timestamp: now };\n            const ckError = createStructuredError(gqlError);\n            if (ckError) {\n              setBannerError(ckError);\n              traceUIError(ckError, gqlError);\n            } else {\n              const fallbackError = new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_5__.CopilotKitError({\n                message: gqlError.message,\n                code: _copilotkit_shared__WEBPACK_IMPORTED_MODULE_5__.CopilotKitErrorCode.UNKNOWN\n              });\n              setBannerError(fallbackError);\n              traceUIError(fallbackError, gqlError);\n            }\n          };\n          graphQLErrors.forEach(routeError);\n        } else {\n          const isDev = (0,_chunk_GFJW4RIM_mjs__WEBPACK_IMPORTED_MODULE_4__.shouldShowDevConsole)(showDevConsole != null ? showDevConsole : false);\n          if (!isDev) {\n            console.error(\"CopilotKit Error (hidden in production):\", error);\n          } else {\n            const fallbackError = new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_5__.CopilotKitError({\n              message: (error == null ? void 0 : error.message) || String(error),\n              code: _copilotkit_shared__WEBPACK_IMPORTED_MODULE_5__.CopilotKitErrorCode.UNKNOWN\n            });\n            setBannerError(fallbackError);\n            traceUIError(fallbackError, error);\n          }\n        }\n      },\n      handleGQLWarning: (message) => {\n        console.warn(message);\n        const warningError = new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_5__.CopilotKitError({\n          message,\n          code: _copilotkit_shared__WEBPACK_IMPORTED_MODULE_5__.CopilotKitErrorCode.UNKNOWN\n        });\n        setBannerError(warningError);\n      }\n    }));\n  }, [runtimeOptions, setBannerError, showDevConsole, onTrace]);\n  return runtimeClient;\n};\nfunction createStructuredError(gqlError) {\n  var _a, _b, _c;\n  const extensions = gqlError.extensions;\n  const originalError = extensions == null ? void 0 : extensions.originalError;\n  const message = (originalError == null ? void 0 : originalError.message) || gqlError.message;\n  const code = extensions == null ? void 0 : extensions.code;\n  if (code) {\n    return new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_5__.CopilotKitError({ message, code });\n  }\n  if ((_a = originalError == null ? void 0 : originalError.stack) == null ? void 0 : _a.includes(\"CopilotApiDiscoveryError\")) {\n    return new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_5__.CopilotKitApiDiscoveryError({ message });\n  }\n  if ((_b = originalError == null ? void 0 : originalError.stack) == null ? void 0 : _b.includes(\"CopilotKitRemoteEndpointDiscoveryError\")) {\n    return new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_5__.CopilotKitRemoteEndpointDiscoveryError({ message });\n  }\n  if ((_c = originalError == null ? void 0 : originalError.stack) == null ? void 0 : _c.includes(\"CopilotKitAgentDiscoveryError\")) {\n    return new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_5__.CopilotKitAgentDiscoveryError({\n      agentName: \"\",\n      availableAgents: []\n    });\n  }\n  return null;\n}\n\n\n//# sourceMappingURL=chunk-ISYBUDL4.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-ISYBUDL4.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-JHIZ5HAI.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-JHIZ5HAI.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCopilotAction: () => (/* binding */ useCopilotAction)\n/* harmony export */ });\n/* harmony import */ var _chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-CCESTGAM.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-CCESTGAM.mjs\");\n/* harmony import */ var _chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-VRXANACV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VRXANACV.mjs\");\n/* harmony import */ var _chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-XFOTNHYA.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var _chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-SKC7AJIV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-VNNKZIFB.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n// src/hooks/use-copilot-action.ts\n\n\nfunction useCopilotAction(action, dependencies) {\n  const { setAction, removeAction, actions, chatComponentsCache } = (0,_chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_1__.useCopilotContext)();\n  const idRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)((0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__.randomId)());\n  const renderAndWaitRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const { addToast } = (0,_chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n  action = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_4__.__spreadValues)({}, action);\n  if (\n    // renderAndWaitForResponse is not available for catch all actions\n    isFrontendAction(action) && // check if renderAndWaitForResponse is set\n    (action.renderAndWait || action.renderAndWaitForResponse)\n  ) {\n    const renderAndWait = action.renderAndWait || action.renderAndWaitForResponse;\n    action.renderAndWait = void 0;\n    action.renderAndWaitForResponse = void 0;\n    action.handler = (0,_chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_5__.useAsyncCallback)(() => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_4__.__async)(this, null, function* () {\n      let resolve;\n      let reject;\n      const promise = new Promise((resolvePromise, rejectPromise) => {\n        resolve = resolvePromise;\n        reject = rejectPromise;\n      });\n      renderAndWaitRef.current = { promise, resolve, reject };\n      return yield promise;\n    }), []);\n    action.render = (props) => {\n      let status = props.status;\n      if (props.status === \"executing\" && !renderAndWaitRef.current) {\n        status = \"inProgress\";\n      }\n      const waitProps = {\n        status,\n        args: props.args,\n        result: props.result,\n        handler: status === \"executing\" ? renderAndWaitRef.current.resolve : void 0,\n        respond: status === \"executing\" ? renderAndWaitRef.current.resolve : void 0\n      };\n      const isNoArgsRenderWait = (_fn) => {\n        var _a;\n        return ((_a = action.parameters) == null ? void 0 : _a.length) === 0;\n      };\n      if (renderAndWait) {\n        if (isNoArgsRenderWait(renderAndWait)) {\n          return renderAndWait(waitProps);\n        } else {\n          return renderAndWait(waitProps);\n        }\n      }\n      return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment);\n    };\n  }\n  if (dependencies === void 0) {\n    if (actions[idRef.current]) {\n      if (isFrontendAction(action)) {\n        actions[idRef.current].handler = action.handler;\n      }\n      if (typeof action.render === \"function\") {\n        if (chatComponentsCache.current !== null) {\n          chatComponentsCache.current.actions[action.name] = action.render;\n        }\n      }\n    }\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const hasDuplicate = Object.values(actions).some(\n      (otherAction) => otherAction.name === action.name && otherAction !== actions[idRef.current]\n    );\n    if (hasDuplicate) {\n      addToast({\n        type: \"warning\",\n        message: `Found an already registered action with name ${action.name}.`,\n        id: `dup-action-${action.name}`\n      });\n    }\n  }, [actions]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setAction(idRef.current, action);\n    if (chatComponentsCache.current !== null && action.render !== void 0) {\n      chatComponentsCache.current.actions[action.name] = action.render;\n    }\n    return () => {\n      removeAction(idRef.current);\n    };\n  }, [\n    setAction,\n    removeAction,\n    isFrontendAction(action) ? action.description : void 0,\n    action.name,\n    isFrontendAction(action) ? action.disabled : void 0,\n    isFrontendAction(action) ? action.available : void 0,\n    // This should be faster than deep equality checking\n    // In addition, all major JS engines guarantee the order of object keys\n    JSON.stringify(isFrontendAction(action) ? action.parameters : []),\n    // include render only if it's a string\n    typeof action.render === \"string\" ? action.render : void 0,\n    // dependencies set by the developer\n    ...dependencies || []\n  ]);\n}\nfunction isFrontendAction(action) {\n  return action.name !== \"*\";\n}\n\n\n//# sourceMappingURL=chunk-JHIZ5HAI.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrcmVhY3QtY29yZUAxLjkuMi1uZXh0LjdfQHR5cGVzK3JlYWN0QDE5LjAuMV9ncmFwaHFsQDE2LjkuMF9yZWFjdC1kb21AMTkuMC4wX3JlYWN0QDE5LjAuMF9fcmVhY3RAMTkuMC4wL25vZGVfbW9kdWxlcy9AY29waWxvdGtpdC9yZWFjdC1jb3JlL2Rpc3QvY2h1bmstSkhJWjVIQUkubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFOEI7QUFHQTtBQUdBO0FBSUE7O0FBRTlCO0FBQzhDO0FBQ3FCO0FBQ25FO0FBQ0EsVUFBVSx3REFBd0QsRUFBRSxzRUFBaUI7QUFDckYsZ0JBQWdCLDZDQUFNLENBQUMsNERBQVE7QUFDL0IsMkJBQTJCLDZDQUFNO0FBQ2pDLFVBQVUsV0FBVyxFQUFFLDZEQUFRO0FBQy9CLFdBQVcsbUVBQWMsR0FBRztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLHFFQUFnQixPQUFPLDREQUFPO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsbUNBQW1DO0FBQ25DO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLGFBQWEsb0RBQWEsQ0FBQywyQ0FBUTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSxZQUFZO0FBQzdFLDBCQUEwQixZQUFZO0FBQ3RDLE9BQU87QUFDUDtBQUNBLEdBQUc7QUFDSCxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AY29waWxvdGtpdCtyZWFjdC1jb3JlQDEuOS4yLW5leHQuN19AdHlwZXMrcmVhY3RAMTkuMC4xX2dyYXBocWxAMTYuOS4wX3JlYWN0LWRvbUAxOS4wLjBfcmVhY3RAMTkuMC4wX19yZWFjdEAxOS4wLjAvbm9kZV9tb2R1bGVzL0Bjb3BpbG90a2l0L3JlYWN0LWNvcmUvZGlzdC9jaHVuay1KSElaNUhBSS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtcbiAgdXNlQXN5bmNDYWxsYmFja1xufSBmcm9tIFwiLi9jaHVuay1DQ0VTVEdBTS5tanNcIjtcbmltcG9ydCB7XG4gIHVzZVRvYXN0XG59IGZyb20gXCIuL2NodW5rLVZSWEFOQUNWLm1qc1wiO1xuaW1wb3J0IHtcbiAgdXNlQ29waWxvdENvbnRleHRcbn0gZnJvbSBcIi4vY2h1bmstWEZPVE5IWUEubWpzXCI7XG5pbXBvcnQge1xuICBfX2FzeW5jLFxuICBfX3NwcmVhZFZhbHVlc1xufSBmcm9tIFwiLi9jaHVuay1TS0M3QUpJVi5tanNcIjtcblxuLy8gc3JjL2hvb2tzL3VzZS1jb3BpbG90LWFjdGlvbi50c1xuaW1wb3J0IHsgcmFuZG9tSWQgfSBmcm9tIFwiQGNvcGlsb3RraXQvc2hhcmVkXCI7XG5pbXBvcnQgeyBjcmVhdGVFbGVtZW50LCBGcmFnbWVudCwgdXNlRWZmZWN0LCB1c2VSZWYgfSBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZUNvcGlsb3RBY3Rpb24oYWN0aW9uLCBkZXBlbmRlbmNpZXMpIHtcbiAgY29uc3QgeyBzZXRBY3Rpb24sIHJlbW92ZUFjdGlvbiwgYWN0aW9ucywgY2hhdENvbXBvbmVudHNDYWNoZSB9ID0gdXNlQ29waWxvdENvbnRleHQoKTtcbiAgY29uc3QgaWRSZWYgPSB1c2VSZWYocmFuZG9tSWQoKSk7XG4gIGNvbnN0IHJlbmRlckFuZFdhaXRSZWYgPSB1c2VSZWYobnVsbCk7XG4gIGNvbnN0IHsgYWRkVG9hc3QgfSA9IHVzZVRvYXN0KCk7XG4gIGFjdGlvbiA9IF9fc3ByZWFkVmFsdWVzKHt9LCBhY3Rpb24pO1xuICBpZiAoXG4gICAgLy8gcmVuZGVyQW5kV2FpdEZvclJlc3BvbnNlIGlzIG5vdCBhdmFpbGFibGUgZm9yIGNhdGNoIGFsbCBhY3Rpb25zXG4gICAgaXNGcm9udGVuZEFjdGlvbihhY3Rpb24pICYmIC8vIGNoZWNrIGlmIHJlbmRlckFuZFdhaXRGb3JSZXNwb25zZSBpcyBzZXRcbiAgICAoYWN0aW9uLnJlbmRlckFuZFdhaXQgfHwgYWN0aW9uLnJlbmRlckFuZFdhaXRGb3JSZXNwb25zZSlcbiAgKSB7XG4gICAgY29uc3QgcmVuZGVyQW5kV2FpdCA9IGFjdGlvbi5yZW5kZXJBbmRXYWl0IHx8IGFjdGlvbi5yZW5kZXJBbmRXYWl0Rm9yUmVzcG9uc2U7XG4gICAgYWN0aW9uLnJlbmRlckFuZFdhaXQgPSB2b2lkIDA7XG4gICAgYWN0aW9uLnJlbmRlckFuZFdhaXRGb3JSZXNwb25zZSA9IHZvaWQgMDtcbiAgICBhY3Rpb24uaGFuZGxlciA9IHVzZUFzeW5jQ2FsbGJhY2soKCkgPT4gX19hc3luYyh0aGlzLCBudWxsLCBmdW5jdGlvbiogKCkge1xuICAgICAgbGV0IHJlc29sdmU7XG4gICAgICBsZXQgcmVqZWN0O1xuICAgICAgY29uc3QgcHJvbWlzZSA9IG5ldyBQcm9taXNlKChyZXNvbHZlUHJvbWlzZSwgcmVqZWN0UHJvbWlzZSkgPT4ge1xuICAgICAgICByZXNvbHZlID0gcmVzb2x2ZVByb21pc2U7XG4gICAgICAgIHJlamVjdCA9IHJlamVjdFByb21pc2U7XG4gICAgICB9KTtcbiAgICAgIHJlbmRlckFuZFdhaXRSZWYuY3VycmVudCA9IHsgcHJvbWlzZSwgcmVzb2x2ZSwgcmVqZWN0IH07XG4gICAgICByZXR1cm4geWllbGQgcHJvbWlzZTtcbiAgICB9KSwgW10pO1xuICAgIGFjdGlvbi5yZW5kZXIgPSAocHJvcHMpID0+IHtcbiAgICAgIGxldCBzdGF0dXMgPSBwcm9wcy5zdGF0dXM7XG4gICAgICBpZiAocHJvcHMuc3RhdHVzID09PSBcImV4ZWN1dGluZ1wiICYmICFyZW5kZXJBbmRXYWl0UmVmLmN1cnJlbnQpIHtcbiAgICAgICAgc3RhdHVzID0gXCJpblByb2dyZXNzXCI7XG4gICAgICB9XG4gICAgICBjb25zdCB3YWl0UHJvcHMgPSB7XG4gICAgICAgIHN0YXR1cyxcbiAgICAgICAgYXJnczogcHJvcHMuYXJncyxcbiAgICAgICAgcmVzdWx0OiBwcm9wcy5yZXN1bHQsXG4gICAgICAgIGhhbmRsZXI6IHN0YXR1cyA9PT0gXCJleGVjdXRpbmdcIiA/IHJlbmRlckFuZFdhaXRSZWYuY3VycmVudC5yZXNvbHZlIDogdm9pZCAwLFxuICAgICAgICByZXNwb25kOiBzdGF0dXMgPT09IFwiZXhlY3V0aW5nXCIgPyByZW5kZXJBbmRXYWl0UmVmLmN1cnJlbnQucmVzb2x2ZSA6IHZvaWQgMFxuICAgICAgfTtcbiAgICAgIGNvbnN0IGlzTm9BcmdzUmVuZGVyV2FpdCA9IChfZm4pID0+IHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICByZXR1cm4gKChfYSA9IGFjdGlvbi5wYXJhbWV0ZXJzKSA9PSBudWxsID8gdm9pZCAwIDogX2EubGVuZ3RoKSA9PT0gMDtcbiAgICAgIH07XG4gICAgICBpZiAocmVuZGVyQW5kV2FpdCkge1xuICAgICAgICBpZiAoaXNOb0FyZ3NSZW5kZXJXYWl0KHJlbmRlckFuZFdhaXQpKSB7XG4gICAgICAgICAgcmV0dXJuIHJlbmRlckFuZFdhaXQod2FpdFByb3BzKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZXR1cm4gcmVuZGVyQW5kV2FpdCh3YWl0UHJvcHMpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICByZXR1cm4gY3JlYXRlRWxlbWVudChGcmFnbWVudCk7XG4gICAgfTtcbiAgfVxuICBpZiAoZGVwZW5kZW5jaWVzID09PSB2b2lkIDApIHtcbiAgICBpZiAoYWN0aW9uc1tpZFJlZi5jdXJyZW50XSkge1xuICAgICAgaWYgKGlzRnJvbnRlbmRBY3Rpb24oYWN0aW9uKSkge1xuICAgICAgICBhY3Rpb25zW2lkUmVmLmN1cnJlbnRdLmhhbmRsZXIgPSBhY3Rpb24uaGFuZGxlcjtcbiAgICAgIH1cbiAgICAgIGlmICh0eXBlb2YgYWN0aW9uLnJlbmRlciA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgIGlmIChjaGF0Q29tcG9uZW50c0NhY2hlLmN1cnJlbnQgIT09IG51bGwpIHtcbiAgICAgICAgICBjaGF0Q29tcG9uZW50c0NhY2hlLmN1cnJlbnQuYWN0aW9uc1thY3Rpb24ubmFtZV0gPSBhY3Rpb24ucmVuZGVyO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFzRHVwbGljYXRlID0gT2JqZWN0LnZhbHVlcyhhY3Rpb25zKS5zb21lKFxuICAgICAgKG90aGVyQWN0aW9uKSA9PiBvdGhlckFjdGlvbi5uYW1lID09PSBhY3Rpb24ubmFtZSAmJiBvdGhlckFjdGlvbiAhPT0gYWN0aW9uc1tpZFJlZi5jdXJyZW50XVxuICAgICk7XG4gICAgaWYgKGhhc0R1cGxpY2F0ZSkge1xuICAgICAgYWRkVG9hc3Qoe1xuICAgICAgICB0eXBlOiBcIndhcm5pbmdcIixcbiAgICAgICAgbWVzc2FnZTogYEZvdW5kIGFuIGFscmVhZHkgcmVnaXN0ZXJlZCBhY3Rpb24gd2l0aCBuYW1lICR7YWN0aW9uLm5hbWV9LmAsXG4gICAgICAgIGlkOiBgZHVwLWFjdGlvbi0ke2FjdGlvbi5uYW1lfWBcbiAgICAgIH0pO1xuICAgIH1cbiAgfSwgW2FjdGlvbnNdKTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRBY3Rpb24oaWRSZWYuY3VycmVudCwgYWN0aW9uKTtcbiAgICBpZiAoY2hhdENvbXBvbmVudHNDYWNoZS5jdXJyZW50ICE9PSBudWxsICYmIGFjdGlvbi5yZW5kZXIgIT09IHZvaWQgMCkge1xuICAgICAgY2hhdENvbXBvbmVudHNDYWNoZS5jdXJyZW50LmFjdGlvbnNbYWN0aW9uLm5hbWVdID0gYWN0aW9uLnJlbmRlcjtcbiAgICB9XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHJlbW92ZUFjdGlvbihpZFJlZi5jdXJyZW50KTtcbiAgICB9O1xuICB9LCBbXG4gICAgc2V0QWN0aW9uLFxuICAgIHJlbW92ZUFjdGlvbixcbiAgICBpc0Zyb250ZW5kQWN0aW9uKGFjdGlvbikgPyBhY3Rpb24uZGVzY3JpcHRpb24gOiB2b2lkIDAsXG4gICAgYWN0aW9uLm5hbWUsXG4gICAgaXNGcm9udGVuZEFjdGlvbihhY3Rpb24pID8gYWN0aW9uLmRpc2FibGVkIDogdm9pZCAwLFxuICAgIGlzRnJvbnRlbmRBY3Rpb24oYWN0aW9uKSA/IGFjdGlvbi5hdmFpbGFibGUgOiB2b2lkIDAsXG4gICAgLy8gVGhpcyBzaG91bGQgYmUgZmFzdGVyIHRoYW4gZGVlcCBlcXVhbGl0eSBjaGVja2luZ1xuICAgIC8vIEluIGFkZGl0aW9uLCBhbGwgbWFqb3IgSlMgZW5naW5lcyBndWFyYW50ZWUgdGhlIG9yZGVyIG9mIG9iamVjdCBrZXlzXG4gICAgSlNPTi5zdHJpbmdpZnkoaXNGcm9udGVuZEFjdGlvbihhY3Rpb24pID8gYWN0aW9uLnBhcmFtZXRlcnMgOiBbXSksXG4gICAgLy8gaW5jbHVkZSByZW5kZXIgb25seSBpZiBpdCdzIGEgc3RyaW5nXG4gICAgdHlwZW9mIGFjdGlvbi5yZW5kZXIgPT09IFwic3RyaW5nXCIgPyBhY3Rpb24ucmVuZGVyIDogdm9pZCAwLFxuICAgIC8vIGRlcGVuZGVuY2llcyBzZXQgYnkgdGhlIGRldmVsb3BlclxuICAgIC4uLmRlcGVuZGVuY2llcyB8fCBbXVxuICBdKTtcbn1cbmZ1bmN0aW9uIGlzRnJvbnRlbmRBY3Rpb24oYWN0aW9uKSB7XG4gIHJldHVybiBhY3Rpb24ubmFtZSAhPT0gXCIqXCI7XG59XG5cbmV4cG9ydCB7XG4gIHVzZUNvcGlsb3RBY3Rpb25cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jaHVuay1KSElaNUhBSS5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-JHIZ5HAI.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-JXF732XG.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-JXF732XG.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useChat: () => (/* binding */ useChat)\n/* harmony export */ });\n/* harmony import */ var _chunk_4CEQJ2X6_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./chunk-4CEQJ2X6.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-4CEQJ2X6.mjs\");\n/* harmony import */ var _chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-CCESTGAM.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-CCESTGAM.mjs\");\n/* harmony import */ var _chunk_ISYBUDL4_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-ISYBUDL4.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-ISYBUDL4.mjs\");\n/* harmony import */ var _chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-XFOTNHYA.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var _chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-SKC7AJIV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-GYZIHHE6.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-VNNKZIFB.mjs\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-WM3ARNBD.mjs\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-ROUIRR4B.mjs\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-P2AUSQOK.mjs\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-EI72UE2B.mjs\");\n\n\n\n\n\n\n// src/hooks/use-chat.ts\n\n\n\nfunction useChat(options) {\n  const {\n    messages,\n    setMessages,\n    makeSystemMessageCallback,\n    copilotConfig,\n    setIsLoading,\n    initialMessages,\n    isLoading,\n    actions,\n    onFunctionCall,\n    onCoAgentStateRender,\n    setCoagentStatesWithRef,\n    coagentStatesRef,\n    agentSession,\n    setAgentSession,\n    threadId,\n    setThreadId,\n    runId,\n    setRunId,\n    chatAbortControllerRef,\n    agentLock,\n    extensions,\n    setExtensions,\n    langGraphInterruptAction,\n    setLangGraphInterruptAction\n  } = options;\n  const runChatCompletionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const addErrorToast = (0,_chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_1__.useErrorToast)();\n  const agentSessionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(agentSession);\n  agentSessionRef.current = agentSession;\n  const runIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(runId);\n  runIdRef.current = runId;\n  const extensionsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(extensions);\n  extensionsRef.current = extensions;\n  const publicApiKey = copilotConfig.publicApiKey;\n  const headers = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({}, copilotConfig.headers || {}), publicApiKey ? { [_copilotkit_shared__WEBPACK_IMPORTED_MODULE_3__.COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: publicApiKey } : {});\n  const { showDevConsole } = (0,_chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_4__.useCopilotContext)();\n  const runtimeClient = (0,_chunk_ISYBUDL4_mjs__WEBPACK_IMPORTED_MODULE_5__.useCopilotRuntimeClient)({\n    url: copilotConfig.chatApiEndpoint,\n    publicApiKey: copilotConfig.publicApiKey,\n    headers,\n    credentials: copilotConfig.credentials,\n    showDevConsole\n  });\n  const pendingAppendsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n  const runChatCompletion = (0,_chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_1__.useAsyncCallback)(\n    (previousMessages) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__async)(this, null, function* () {\n      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o;\n      setIsLoading(true);\n      const interruptEvent = langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.event;\n      if ((interruptEvent == null ? void 0 : interruptEvent.name) === _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__.MetaEventName.LangGraphInterruptEvent && (interruptEvent == null ? void 0 : interruptEvent.value) && !(interruptEvent == null ? void 0 : interruptEvent.response) && agentSessionRef.current) {\n        addErrorToast([\n          new Error(\n            \"A message was sent while interrupt is active. This will cause failure on the agent side\"\n          )\n        ]);\n      }\n      let newMessages = [\n        new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n          content: \"\",\n          role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.Role.Assistant\n        })\n      ];\n      chatAbortControllerRef.current = new AbortController();\n      setMessages([...previousMessages, ...newMessages]);\n      const systemMessage = makeSystemMessageCallback();\n      const messagesWithContext = [systemMessage, ...initialMessages || [], ...previousMessages];\n      const finalProperties = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({}, copilotConfig.properties || {});\n      let mcpServersToUse = null;\n      if (copilotConfig.mcpServers && Array.isArray(copilotConfig.mcpServers) && copilotConfig.mcpServers.length > 0) {\n        mcpServersToUse = copilotConfig.mcpServers;\n      } else if (((_a = copilotConfig.properties) == null ? void 0 : _a.mcpServers) && Array.isArray(copilotConfig.properties.mcpServers) && copilotConfig.properties.mcpServers.length > 0) {\n        mcpServersToUse = copilotConfig.properties.mcpServers;\n      }\n      if (mcpServersToUse) {\n        finalProperties.mcpServers = mcpServersToUse;\n        copilotConfig.mcpServers = mcpServersToUse;\n      }\n      const isAgentRun = agentSessionRef.current !== null;\n      const stream = runtimeClient.asStream(\n        runtimeClient.generateCopilotResponse({\n          data: (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({\n            frontend: {\n              actions: (0,_chunk_4CEQJ2X6_mjs__WEBPACK_IMPORTED_MODULE_8__.processActionsForRuntimeRequest)(actions),\n              url: window.location.href\n            },\n            threadId,\n            runId: runIdRef.current,\n            extensions: extensionsRef.current,\n            metaEvents: composeAndFlushMetaEventsInput([langGraphInterruptAction == null ? void 0 : langGraphInterruptAction.event]),\n            messages: (0,_copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_9__.convertMessagesToGqlInput)((0,_copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_9__.filterAgentStateMessages)(messagesWithContext))\n          }, copilotConfig.cloud ? {\n            cloud: (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({}, ((_d = (_c = (_b = copilotConfig.cloud.guardrails) == null ? void 0 : _b.input) == null ? void 0 : _c.restrictToTopic) == null ? void 0 : _d.enabled) ? {\n              guardrails: {\n                inputValidationRules: {\n                  allowList: copilotConfig.cloud.guardrails.input.restrictToTopic.validTopics,\n                  denyList: copilotConfig.cloud.guardrails.input.restrictToTopic.invalidTopics\n                }\n              }\n            } : {})\n          } : {}), {\n            metadata: {\n              requestType: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__.CopilotRequestType.Chat\n            }\n          }), agentSessionRef.current ? {\n            agentSession: agentSessionRef.current\n          } : {}), {\n            agentStates: Object.values(coagentStatesRef.current).map((state) => {\n              const stateObject = {\n                agentName: state.name,\n                state: JSON.stringify(state.state)\n              };\n              if (state.config !== void 0) {\n                stateObject.config = JSON.stringify(state.config);\n              }\n              return stateObject;\n            }),\n            forwardedParameters: options.forwardedParameters || {}\n          }),\n          properties: finalProperties,\n          signal: (_e = chatAbortControllerRef.current) == null ? void 0 : _e.signal\n        })\n      );\n      const guardrailsEnabled = ((_h = (_g = (_f = copilotConfig.cloud) == null ? void 0 : _f.guardrails) == null ? void 0 : _g.input) == null ? void 0 : _h.restrictToTopic.enabled) || false;\n      const reader = stream.getReader();\n      let executedCoAgentStateRenders = [];\n      let followUp = void 0;\n      let messages2 = [];\n      let syncedMessages = [];\n      let interruptMessages = [];\n      try {\n        while (true) {\n          let done, value;\n          try {\n            const readResult = yield reader.read();\n            done = readResult.done;\n            value = readResult.value;\n          } catch (readError) {\n            break;\n          }\n          if (done) {\n            if (chatAbortControllerRef.current.signal.aborted) {\n              return [];\n            }\n            break;\n          }\n          if (!(value == null ? void 0 : value.generateCopilotResponse)) {\n            continue;\n          }\n          runIdRef.current = value.generateCopilotResponse.runId || null;\n          extensionsRef.current = _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_10__.CopilotRuntimeClient.removeGraphQLTypename(\n            value.generateCopilotResponse.extensions || {}\n          );\n          setRunId(runIdRef.current);\n          setExtensions(extensionsRef.current);\n          let rawMessagesResponse = value.generateCopilotResponse.messages;\n          const metaEvents = (_j = (_i = value.generateCopilotResponse) == null ? void 0 : _i.metaEvents) != null ? _j : [];\n          (metaEvents != null ? metaEvents : []).forEach((ev) => {\n            if (ev.name === _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__.MetaEventName.LangGraphInterruptEvent) {\n              let eventValue = (0,_copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.langGraphInterruptEvent)(ev).value;\n              eventValue = (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_11__.parseJson)(eventValue, eventValue);\n              setLangGraphInterruptAction({\n                event: (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({}, (0,_copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.langGraphInterruptEvent)(ev)), {\n                  value: eventValue\n                })\n              });\n            }\n            if (ev.name === _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__.MetaEventName.CopilotKitLangGraphInterruptEvent) {\n              const data = ev.data;\n              rawMessagesResponse = [...rawMessagesResponse, ...data.messages];\n              interruptMessages = (0,_copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_9__.convertGqlOutputToMessages)(\n                // @ts-ignore\n                (0,_copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_9__.filterAdjacentAgentStateMessages)(data.messages)\n              );\n            }\n          });\n          messages2 = (0,_copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_9__.convertGqlOutputToMessages)(\n            (0,_copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_9__.filterAdjacentAgentStateMessages)(rawMessagesResponse)\n          );\n          if (messages2.length === 0) {\n            continue;\n          }\n          newMessages = [];\n          if (((_k = value.generateCopilotResponse.status) == null ? void 0 : _k.__typename) === \"FailedResponseStatus\" && value.generateCopilotResponse.status.reason === \"GUARDRAILS_VALIDATION_FAILED\") {\n            newMessages = [\n              new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.TextMessage({\n                role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__.MessageRole.Assistant,\n                content: ((_l = value.generateCopilotResponse.status.details) == null ? void 0 : _l.guardrailsReason) || \"\"\n              })\n            ];\n            setMessages([...previousMessages, ...newMessages]);\n            break;\n          } else {\n            newMessages = [...messages2];\n            for (const message of messages2) {\n              if (message.isAgentStateMessage() && !message.active && !executedCoAgentStateRenders.includes(message.id) && onCoAgentStateRender) {\n                if (guardrailsEnabled && value.generateCopilotResponse.status === void 0) {\n                  break;\n                }\n                yield onCoAgentStateRender({\n                  name: message.agentName,\n                  nodeName: message.nodeName,\n                  state: message.state\n                });\n                executedCoAgentStateRenders.push(message.id);\n              }\n            }\n            const lastAgentStateMessage = [...messages2].reverse().find((message) => message.isAgentStateMessage());\n            if (lastAgentStateMessage) {\n              if (lastAgentStateMessage.state.messages && lastAgentStateMessage.state.messages.length > 0) {\n                syncedMessages = (0,_copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_9__.loadMessagesFromJsonRepresentation)(\n                  lastAgentStateMessage.state.messages\n                );\n              }\n              setCoagentStatesWithRef((prevAgentStates) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__spreadValues)({}, prevAgentStates), {\n                [lastAgentStateMessage.agentName]: {\n                  name: lastAgentStateMessage.agentName,\n                  state: lastAgentStateMessage.state,\n                  running: lastAgentStateMessage.running,\n                  active: lastAgentStateMessage.active,\n                  threadId: lastAgentStateMessage.threadId,\n                  nodeName: lastAgentStateMessage.nodeName,\n                  runId: lastAgentStateMessage.runId\n                }\n              }));\n              if (lastAgentStateMessage.running) {\n                setAgentSession({\n                  threadId: lastAgentStateMessage.threadId,\n                  agentName: lastAgentStateMessage.agentName,\n                  nodeName: lastAgentStateMessage.nodeName\n                });\n              } else {\n                if (agentLock) {\n                  setAgentSession({\n                    threadId: (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_12__.randomId)(),\n                    agentName: agentLock,\n                    nodeName: void 0\n                  });\n                } else {\n                  setAgentSession(null);\n                }\n              }\n            }\n          }\n          if (newMessages.length > 0) {\n            setMessages([...previousMessages, ...newMessages]);\n          }\n        }\n        let finalMessages = constructFinalMessages(\n          [...syncedMessages, ...interruptMessages],\n          previousMessages,\n          newMessages\n        );\n        let didExecuteAction = false;\n        if (onFunctionCall) {\n          const lastMessages = [];\n          for (let i = finalMessages.length - 1; i >= 0; i--) {\n            const message = finalMessages[i];\n            if ((message.isActionExecutionMessage() || message.isResultMessage()) && message.status.code !== _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__.MessageStatusCode.Pending) {\n              lastMessages.unshift(message);\n            } else if (!message.isAgentStateMessage()) {\n              break;\n            }\n          }\n          for (const message of lastMessages) {\n            setMessages(finalMessages);\n            const action = actions.find(\n              (action2) => action2.name === message.name\n            );\n            const currentResultMessagePairedFeAction = message.isResultMessage() ? getPairedFeAction(actions, message) : null;\n            const executeActionFromMessage = (action2, message2) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__async)(this, null, function* () {\n              var _a2;\n              const isInterruptAction = interruptMessages.find((m) => m.id === message2.id);\n              followUp = (_a2 = action2 == null ? void 0 : action2.followUp) != null ? _a2 : !isInterruptAction;\n              const resultMessage = yield executeAction({\n                onFunctionCall,\n                previousMessages,\n                message: message2,\n                chatAbortControllerRef,\n                onError: (error) => {\n                  addErrorToast([error]);\n                  console.error(`Failed to execute action ${message2.name}: ${error}`);\n                }\n              });\n              didExecuteAction = true;\n              const messageIndex = finalMessages.findIndex((msg) => msg.id === message2.id);\n              finalMessages.splice(messageIndex + 1, 0, resultMessage);\n              return resultMessage;\n            });\n            if (action && message.isActionExecutionMessage()) {\n              const resultMessage = yield executeActionFromMessage(action, message);\n              const pairedFeAction = getPairedFeAction(actions, resultMessage);\n              if (pairedFeAction) {\n                const newExecutionMessage = new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.ActionExecutionMessage({\n                  name: pairedFeAction.name,\n                  arguments: (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_11__.parseJson)(resultMessage.result, resultMessage.result),\n                  status: message.status,\n                  createdAt: message.createdAt,\n                  parentMessageId: message.parentMessageId\n                });\n                yield executeActionFromMessage(pairedFeAction, newExecutionMessage);\n              }\n            } else if (message.isResultMessage() && currentResultMessagePairedFeAction) {\n              const newExecutionMessage = new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.ActionExecutionMessage({\n                name: currentResultMessagePairedFeAction.name,\n                arguments: (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_11__.parseJson)(message.result, message.result),\n                status: message.status,\n                createdAt: message.createdAt\n              });\n              finalMessages.push(newExecutionMessage);\n              yield executeActionFromMessage(\n                currentResultMessagePairedFeAction,\n                newExecutionMessage\n              );\n            }\n          }\n          setMessages(finalMessages);\n        }\n        if (\n          // if followUp is not explicitly false\n          followUp !== false && // and we executed an action\n          (didExecuteAction || // the last message is a server side result\n          !isAgentRun && finalMessages.length && finalMessages[finalMessages.length - 1].isResultMessage()) && // the user did not stop generation\n          !((_m = chatAbortControllerRef.current) == null ? void 0 : _m.signal.aborted)\n        ) {\n          yield new Promise((resolve) => setTimeout(resolve, 10));\n          return yield runChatCompletionRef.current(finalMessages);\n        } else if ((_n = chatAbortControllerRef.current) == null ? void 0 : _n.signal.aborted) {\n          const repairedMessages = finalMessages.filter((message, actionExecutionIndex) => {\n            if (message.isActionExecutionMessage()) {\n              return finalMessages.find(\n                (msg, resultIndex) => msg.isResultMessage() && msg.actionExecutionId === message.id && resultIndex === actionExecutionIndex + 1\n              );\n            }\n            return true;\n          });\n          const repairedMessageIds = repairedMessages.map((message) => message.id);\n          setMessages(repairedMessages);\n          if ((_o = agentSessionRef.current) == null ? void 0 : _o.nodeName) {\n            setAgentSession({\n              threadId: agentSessionRef.current.threadId,\n              agentName: agentSessionRef.current.agentName,\n              nodeName: \"__end__\"\n            });\n          }\n          return newMessages.filter((message) => repairedMessageIds.includes(message.id));\n        } else {\n          return newMessages.slice();\n        }\n      } finally {\n        setIsLoading(false);\n      }\n    }),\n    [\n      messages,\n      setMessages,\n      makeSystemMessageCallback,\n      copilotConfig,\n      setIsLoading,\n      initialMessages,\n      isLoading,\n      actions,\n      onFunctionCall,\n      onCoAgentStateRender,\n      setCoagentStatesWithRef,\n      coagentStatesRef,\n      agentSession,\n      setAgentSession\n    ]\n  );\n  runChatCompletionRef.current = runChatCompletion;\n  const runChatCompletionAndHandleFunctionCall = (0,_chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_1__.useAsyncCallback)(\n    (messages2) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__async)(this, null, function* () {\n      yield runChatCompletionRef.current(messages2);\n    }),\n    [messages]\n  );\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!isLoading && pendingAppendsRef.current.length > 0) {\n      const pending = pendingAppendsRef.current.splice(0);\n      const followUp = pending.some((p) => p.followUp);\n      const newMessages = [...messages, ...pending.map((p) => p.message)];\n      setMessages(newMessages);\n      if (followUp) {\n        runChatCompletionAndHandleFunctionCall(newMessages);\n      }\n    }\n  }, [isLoading, messages, setMessages, runChatCompletionAndHandleFunctionCall]);\n  const composeAndFlushMetaEventsInput = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (metaEvents) => {\n      return metaEvents.reduce((acc, event) => {\n        if (!event)\n          return acc;\n        switch (event.name) {\n          case _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_6__.MetaEventName.LangGraphInterruptEvent:\n            if (event.response) {\n              setLangGraphInterruptAction(null);\n              const value = event.value;\n              return [\n                ...acc,\n                {\n                  name: event.name,\n                  value: typeof value === \"string\" ? value : JSON.stringify(value),\n                  response: typeof event.response === \"string\" ? event.response : JSON.stringify(event.response)\n                }\n              ];\n            }\n            return acc;\n          default:\n            return acc;\n        }\n      }, []);\n    },\n    [setLangGraphInterruptAction]\n  );\n  const append = (0,_chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_1__.useAsyncCallback)(\n    (message, options2) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__async)(this, null, function* () {\n      var _a;\n      const followUp = (_a = options2 == null ? void 0 : options2.followUp) != null ? _a : true;\n      if (isLoading) {\n        pendingAppendsRef.current.push({ message, followUp });\n        return;\n      }\n      const newMessages = [...messages, message];\n      setMessages(newMessages);\n      if (followUp) {\n        return runChatCompletionAndHandleFunctionCall(newMessages);\n      }\n    }),\n    [isLoading, messages, setMessages, runChatCompletionAndHandleFunctionCall]\n  );\n  const reload = (0,_chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_1__.useAsyncCallback)(\n    (messageId) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__async)(this, null, function* () {\n      if (isLoading || messages.length === 0) {\n        return;\n      }\n      const index = messages.findIndex((msg) => msg.id === messageId);\n      if (index === -1) {\n        console.warn(`Message with id ${messageId} not found`);\n        return;\n      }\n      let newMessages = messages.slice(0, index);\n      if (newMessages.length > 0 && newMessages[newMessages.length - 1].isAgentStateMessage()) {\n        newMessages = newMessages.slice(0, newMessages.length - 1);\n      }\n      setMessages(newMessages);\n      return runChatCompletionAndHandleFunctionCall(newMessages);\n    }),\n    [isLoading, messages, setMessages, runChatCompletionAndHandleFunctionCall]\n  );\n  const stop = () => {\n    var _a;\n    (_a = chatAbortControllerRef.current) == null ? void 0 : _a.abort(\"Stop was called\");\n  };\n  return {\n    append,\n    reload,\n    stop,\n    runChatCompletion: () => runChatCompletionRef.current(messages)\n  };\n}\nfunction constructFinalMessages(syncedMessages, previousMessages, newMessages) {\n  const finalMessages = syncedMessages.length > 0 ? [...syncedMessages] : [...previousMessages, ...newMessages];\n  if (syncedMessages.length > 0) {\n    const messagesWithAgentState = [...previousMessages, ...newMessages];\n    let previousMessageId = void 0;\n    for (const message of messagesWithAgentState) {\n      if (message.isAgentStateMessage()) {\n        const index = finalMessages.findIndex((msg) => msg.id === previousMessageId);\n        if (index !== -1) {\n          finalMessages.splice(index + 1, 0, message);\n        }\n      }\n      previousMessageId = message.id;\n    }\n  }\n  return finalMessages;\n}\nfunction executeAction(_0) {\n  return (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_2__.__async)(this, arguments, function* ({\n    onFunctionCall,\n    previousMessages,\n    message,\n    chatAbortControllerRef,\n    onError\n  }) {\n    let result;\n    let error = null;\n    try {\n      result = yield Promise.race([\n        onFunctionCall({\n          messages: previousMessages,\n          name: message.name,\n          args: message.arguments\n        }),\n        new Promise(\n          (resolve) => {\n            var _a;\n            return (_a = chatAbortControllerRef.current) == null ? void 0 : _a.signal.addEventListener(\n              \"abort\",\n              () => resolve(\"Operation was aborted by the user\")\n            );\n          }\n        ),\n        // if the user stopped generation, we also abort consecutive actions\n        new Promise((resolve) => {\n          var _a;\n          if ((_a = chatAbortControllerRef.current) == null ? void 0 : _a.signal.aborted) {\n            resolve(\"Operation was aborted by the user\");\n          }\n        })\n      ]);\n    } catch (e) {\n      onError(e);\n    }\n    return new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.ResultMessage({\n      id: \"result-\" + message.id,\n      result: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.ResultMessage.encodeResult(\n        error ? {\n          content: result,\n          error: JSON.parse(JSON.stringify(error, Object.getOwnPropertyNames(error)))\n        } : result\n      ),\n      actionExecutionId: message.id,\n      actionName: message.name\n    });\n  });\n}\nfunction getPairedFeAction(actions, message) {\n  let actionName = null;\n  if (message.isActionExecutionMessage()) {\n    actionName = message.name;\n  } else if (message.isResultMessage()) {\n    actionName = message.actionName;\n  }\n  return actions.find(\n    (action) => action.name === actionName && action.available === \"frontend\" || action.pairedAction === actionName\n  );\n}\n\n\n//# sourceMappingURL=chunk-JXF732XG.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-JXF732XG.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-O7ARI5CV.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-O7ARI5CV.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExclamationMarkIcon: () => (/* binding */ ExclamationMarkIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/components/toast/exclamation-mark-icon.tsx\n\nvar ExclamationMarkIcon = ({\n  className,\n  style\n}) => /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\n  \"svg\",\n  {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"24\",\n    height: \"24\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    className: `lucide lucide-circle-alert ${className ? className : \"\"}`,\n    style,\n    children: [\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"circle\", { cx: \"12\", cy: \"12\", r: \"10\" }),\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"line\", { x1: \"12\", x2: \"12\", y1: \"8\", y2: \"12\" }),\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"line\", { x1: \"12\", x2: \"12.01\", y1: \"16\", y2: \"16\" })\n    ]\n  }\n);\n\n\n//# sourceMappingURL=chunk-O7ARI5CV.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-O7ARI5CV.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-PMAFHQ7P.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-PMAFHQ7P.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatusChecker: () => (/* binding */ StatusChecker)\n/* harmony export */ });\n/* harmony import */ var _chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-SKC7AJIV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-GYZIHHE6.mjs\");\n\n\n// src/lib/status-checker.ts\n\nvar STATUS_CHECK_INTERVAL = 1e3 * 60 * 5;\nvar StatusChecker = class {\n  constructor() {\n    this.activeKey = null;\n    this.intervalId = null;\n    this.instanceCount = 0;\n    this.lastResponse = null;\n  }\n  start(publicApiKey, onUpdate) {\n    return (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_0__.__async)(this, null, function* () {\n      this.instanceCount++;\n      if (this.activeKey === publicApiKey)\n        return;\n      if (this.intervalId)\n        clearInterval(this.intervalId);\n      const checkStatus = () => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_0__.__async)(this, null, function* () {\n        try {\n          const response = yield fetch(`${_copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.COPILOT_CLOUD_API_URL}/ciu`, {\n            method: \"GET\",\n            headers: {\n              [_copilotkit_shared__WEBPACK_IMPORTED_MODULE_1__.COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: publicApiKey\n            }\n          }).then((response2) => response2.json());\n          this.lastResponse = response;\n          onUpdate == null ? void 0 : onUpdate(response);\n          return response;\n        } catch (error) {\n          return null;\n        }\n      });\n      const initialResponse = yield checkStatus();\n      this.intervalId = setInterval(checkStatus, STATUS_CHECK_INTERVAL);\n      this.activeKey = publicApiKey;\n      return initialResponse;\n    });\n  }\n  getLastResponse() {\n    return this.lastResponse;\n  }\n  stop() {\n    this.instanceCount--;\n    if (this.instanceCount === 0) {\n      if (this.intervalId) {\n        clearInterval(this.intervalId);\n        this.intervalId = null;\n        this.activeKey = null;\n        this.lastResponse = null;\n      }\n    }\n  }\n};\n\n\n//# sourceMappingURL=chunk-PMAFHQ7P.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-PMAFHQ7P.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-QQZLIEXK.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-QQZLIEXK.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CopilotErrorBoundary: () => (/* binding */ CopilotErrorBoundary),\n/* harmony export */   ErrorToast: () => (/* binding */ ErrorToast)\n/* harmony export */ });\n/* harmony import */ var _chunk_PMAFHQ7P_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-PMAFHQ7P.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-PMAFHQ7P.mjs\");\n/* harmony import */ var _chunk_HD2GE3DK_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-HD2GE3DK.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-HD2GE3DK.mjs\");\n/* harmony import */ var _chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-CCESTGAM.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-CCESTGAM.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n// src/components/error-boundary/error-boundary.tsx\n\n\n\nvar statusChecker = new _chunk_PMAFHQ7P_mjs__WEBPACK_IMPORTED_MODULE_2__.StatusChecker();\nvar CopilotErrorBoundary = class extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return { hasError: true, error };\n  }\n  componentDidMount() {\n    if (this.props.publicApiKey) {\n      statusChecker.start(this.props.publicApiKey, (newStatus) => {\n        this.setState((prevState) => {\n          var _a;\n          if ((newStatus == null ? void 0 : newStatus.severity) !== ((_a = prevState.status) == null ? void 0 : _a.severity)) {\n            return { status: newStatus != null ? newStatus : void 0 };\n          }\n          return null;\n        });\n      });\n    }\n  }\n  componentWillUnmount() {\n    statusChecker.stop();\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"CopilotKit Error:\", error, errorInfo);\n  }\n  render() {\n    var _a, _b;\n    if (this.state.hasError) {\n      if (this.state.error instanceof _copilotkit_shared__WEBPACK_IMPORTED_MODULE_3__.CopilotKitError) {\n        if (this.state.error.visibility === _copilotkit_shared__WEBPACK_IMPORTED_MODULE_3__.ErrorVisibility.BANNER) {\n          return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ErrorToast, { error: this.state.error, children: (0,_chunk_HD2GE3DK_mjs__WEBPACK_IMPORTED_MODULE_4__.renderCopilotKitUsage)(\n            this.state.error,\n            () => this.setState({ hasError: false, error: void 0 })\n          ) });\n        }\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children: [\n          this.props.children,\n          this.props.showUsageBanner && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n            _chunk_HD2GE3DK_mjs__WEBPACK_IMPORTED_MODULE_4__.UsageBanner,\n            {\n              severity: (_a = this.state.status) == null ? void 0 : _a.severity,\n              message: (_b = this.state.status) == null ? void 0 : _b.message\n            }\n          )\n        ] });\n      }\n      throw this.state.error;\n    }\n    return this.props.children;\n  }\n};\nfunction ErrorToast({ error, children }) {\n  const addErrorToast = (0,_chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_5__.useErrorToast)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (error) {\n      addErrorToast([error]);\n    }\n  }, [error, addErrorToast]);\n  if (!error)\n    throw error;\n  return children;\n}\n\n\n//# sourceMappingURL=chunk-QQZLIEXK.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-QQZLIEXK.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-RKTVJRK7.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-RKTVJRK7.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   use_tree_default: () => (/* binding */ use_tree_default)\n/* harmony export */ });\n/* harmony import */ var _chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-SKC7AJIV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-VNNKZIFB.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n// src/hooks/use-tree.ts\n\n\nvar removeNode = (nodes, id) => {\n  return nodes.reduce((result, node) => {\n    if (node.id !== id) {\n      const newNode = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__spreadValues)({}, node), { children: removeNode(node.children, id) });\n      result.push(newNode);\n    }\n    return result;\n  }, []);\n};\nvar addNode = (nodes, newNode, parentId) => {\n  if (!parentId) {\n    return [...nodes, newNode];\n  }\n  return nodes.map((node) => {\n    if (node.id === parentId) {\n      return (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__spreadValues)({}, node), { children: [...node.children, newNode] });\n    } else if (node.children.length) {\n      return (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__spreadValues)({}, node), { children: addNode(node.children, newNode, parentId) });\n    }\n    return node;\n  });\n};\nvar treeIndentationRepresentation = (index, indentLevel) => {\n  if (indentLevel === 0) {\n    return (index + 1).toString();\n  } else if (indentLevel === 1) {\n    return String.fromCharCode(65 + index);\n  } else if (indentLevel === 2) {\n    return String.fromCharCode(97 + index);\n  } else {\n    return \"-\";\n  }\n};\nvar printNode = (node, prefix = \"\", indentLevel = 0) => {\n  const indent = \" \".repeat(3).repeat(indentLevel);\n  const prefixPlusIndentLength = prefix.length + indent.length;\n  const subsequentLinesPrefix = \" \".repeat(prefixPlusIndentLength);\n  const valueLines = node.value.split(\"\\n\");\n  const outputFirstLine = `${indent}${prefix}${valueLines[0]}`;\n  const outputSubsequentLines = valueLines.slice(1).map((line) => `${subsequentLinesPrefix}${line}`).join(\"\\n\");\n  let output = `${outputFirstLine}\n`;\n  if (outputSubsequentLines) {\n    output += `${outputSubsequentLines}\n`;\n  }\n  const childPrePrefix = \" \".repeat(prefix.length);\n  node.children.forEach(\n    (child, index) => output += printNode(\n      child,\n      `${childPrePrefix}${treeIndentationRepresentation(index, indentLevel + 1)}. `,\n      indentLevel + 1\n    )\n  );\n  return output;\n};\nfunction treeReducer(state, action) {\n  switch (action.type) {\n    case \"ADD_NODE\": {\n      const { value, parentId, id: newNodeId } = action;\n      const newNode = {\n        id: newNodeId,\n        value,\n        children: [],\n        categories: new Set(action.categories)\n      };\n      try {\n        return addNode(state, newNode, parentId);\n      } catch (error) {\n        console.error(`Error while adding node with id ${newNodeId}: ${error}`);\n        return state;\n      }\n    }\n    case \"REMOVE_NODE\":\n      return removeNode(state, action.id);\n    default:\n      return state;\n  }\n}\nvar useTree = () => {\n  const [tree, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(treeReducer, []);\n  const addElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (value, categories, parentId) => {\n      const newNodeId = (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__.randomId)();\n      dispatch({\n        type: \"ADD_NODE\",\n        value,\n        parentId,\n        id: newNodeId,\n        categories\n      });\n      return newNodeId;\n    },\n    []\n  );\n  const removeElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((id) => {\n    dispatch({ type: \"REMOVE_NODE\", id });\n  }, []);\n  const getAllElements = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    return tree;\n  }, [tree]);\n  const printTree = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (categories) => {\n      const categoriesSet = new Set(categories);\n      let output = \"\";\n      tree.forEach((node, index) => {\n        if (!setsHaveIntersection(categoriesSet, node.categories)) {\n          return;\n        }\n        if (index !== 0) {\n          output += \"\\n\";\n        }\n        output += printNode(node, `${treeIndentationRepresentation(index, 0)}. `);\n      });\n      return output;\n    },\n    [tree]\n  );\n  return { tree, addElement, printTree, removeElement, getAllElements };\n};\nvar use_tree_default = useTree;\nfunction setsHaveIntersection(setA, setB) {\n  const [smallerSet, largerSet] = setA.size <= setB.size ? [setA, setB] : [setB, setA];\n  for (let item of smallerSet) {\n    if (largerSet.has(item)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n\n//# sourceMappingURL=chunk-RKTVJRK7.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-RKTVJRK7.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-RN3ZRHI7.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-RN3ZRHI7.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CopilotKit: () => (/* binding */ CopilotKit),\n/* harmony export */   CopilotKitInternal: () => (/* binding */ CopilotKitInternal),\n/* harmony export */   defaultCopilotContextCategories: () => (/* binding */ defaultCopilotContextCategories),\n/* harmony export */   extract: () => (/* binding */ extract)\n/* harmony export */ });\n/* harmony import */ var _chunk_QQZLIEXK_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./chunk-QQZLIEXK.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-QQZLIEXK.mjs\");\n/* harmony import */ var _chunk_VJCHRQ7Q_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./chunk-VJCHRQ7Q.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VJCHRQ7Q.mjs\");\n/* harmony import */ var _chunk_5FHSUKQL_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./chunk-5FHSUKQL.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-5FHSUKQL.mjs\");\n/* harmony import */ var _chunk_RKTVJRK7_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./chunk-RKTVJRK7.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-RKTVJRK7.mjs\");\n/* harmony import */ var _chunk_PMAFHQ7P_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./chunk-PMAFHQ7P.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-PMAFHQ7P.mjs\");\n/* harmony import */ var _chunk_ISYBUDL4_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./chunk-ISYBUDL4.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-ISYBUDL4.mjs\");\n/* harmony import */ var _chunk_GFJW4RIM_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-GFJW4RIM.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-GFJW4RIM.mjs\");\n/* harmony import */ var _chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-VRXANACV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VRXANACV.mjs\");\n/* harmony import */ var _chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./chunk-XFOTNHYA.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var _chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-SKC7AJIV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-2KQ6HEWZ.mjs\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-WM3ARNBD.mjs\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-ROUIRR4B.mjs\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-P2AUSQOK.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-GYZIHHE6.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-VNNKZIFB.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\n\n\n\n\n\n\n// src/utils/extract.ts\n\n\n\n// src/components/copilot-provider/copilotkit.tsx\n\n\n\n\nfunction CopilotKit(_a) {\n  var _b = _a, { children } = _b, props = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__objRest)(_b, [\"children\"]);\n  var _a2;\n  const showDevConsole = (_a2 = props.showDevConsole) != null ? _a2 : false;\n  const enabled = (0,_chunk_GFJW4RIM_mjs__WEBPACK_IMPORTED_MODULE_4__.shouldShowDevConsole)(showDevConsole);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_5__.ToastProvider, { enabled, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_chunk_QQZLIEXK_mjs__WEBPACK_IMPORTED_MODULE_6__.CopilotErrorBoundary, { publicApiKey: props.publicApiKey, showUsageBanner: enabled, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(CopilotKitInternal, (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({}, props), { children })) }) });\n}\nfunction CopilotKitInternal(cpkProps) {\n  var _b, _c;\n  const _a = cpkProps, { children } = _a, props = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__objRest)(_a, [\"children\"]);\n  validateProps(cpkProps);\n  const chatApiEndpoint = props.runtimeUrl || _copilotkit_shared__WEBPACK_IMPORTED_MODULE_7__.COPILOT_CLOUD_CHAT_URL;\n  const [actions, setActions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n  const [coAgentStateRenders, setCoAgentStateRenders] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n  const chatComponentsCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    actions: {},\n    coAgentStateRenders: {}\n  });\n  const { addElement, removeElement, printTree, getAllElements } = (0,_chunk_RKTVJRK7_mjs__WEBPACK_IMPORTED_MODULE_8__.use_tree_default)();\n  const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [chatInstructions, setChatInstructions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [authStates, setAuthStates] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n  const [extensions, setExtensions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n  const [additionalInstructions, setAdditionalInstructions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const {\n    addElement: addDocument,\n    removeElement: removeDocument,\n    allElements: allDocuments\n  } = (0,_chunk_5FHSUKQL_mjs__WEBPACK_IMPORTED_MODULE_9__.use_flat_category_store_default)();\n  const statusChecker = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => new _chunk_PMAFHQ7P_mjs__WEBPACK_IMPORTED_MODULE_10__.StatusChecker(), []);\n  const [usageBannerStatus, setUsageBannerStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const setAction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((id, action) => {\n    setActions((prevPoints) => {\n      return (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({}, prevPoints), {\n        [id]: action\n      });\n    });\n  }, []);\n  const removeAction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((id) => {\n    setActions((prevPoints) => {\n      const newPoints = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({}, prevPoints);\n      delete newPoints[id];\n      return newPoints;\n    });\n  }, []);\n  const setCoAgentStateRender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((id, stateRender) => {\n    setCoAgentStateRenders((prevPoints) => {\n      return (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({}, prevPoints), {\n        [id]: stateRender\n      });\n    });\n  }, []);\n  const removeCoAgentStateRender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((id) => {\n    setCoAgentStateRenders((prevPoints) => {\n      const newPoints = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({}, prevPoints);\n      delete newPoints[id];\n      return newPoints;\n    });\n  }, []);\n  const getContextString = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (documents, categories) => {\n      const documentsString = documents.map((document) => {\n        return `${document.name} (${document.sourceApplication}):\n${document.getContents()}`;\n      }).join(\"\\n\\n\");\n      const nonDocumentStrings = printTree(categories);\n      return `${documentsString}\n\n${nonDocumentStrings}`;\n    },\n    [printTree]\n  );\n  const addContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (context, parentId, categories = defaultCopilotContextCategories) => {\n      return addElement(context, categories, parentId);\n    },\n    [addElement]\n  );\n  const removeContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (id) => {\n      removeElement(id);\n    },\n    [removeElement]\n  );\n  const getAllContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    return getAllElements();\n  }, [getAllElements]);\n  const getFunctionCallHandler = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (customEntryPoints) => {\n      return entryPointsToFunctionCallHandler(Object.values(customEntryPoints || actions));\n    },\n    [actions]\n  );\n  const getDocumentsContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (categories) => {\n      return allDocuments(categories);\n    },\n    [allDocuments]\n  );\n  const addDocumentContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (documentPointer, categories = defaultCopilotContextCategories) => {\n      return addDocument(documentPointer, categories);\n    },\n    [addDocument]\n  );\n  const removeDocumentContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (documentId) => {\n      removeDocument(documentId);\n    },\n    [removeDocument]\n  );\n  const copilotApiConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    var _a2, _b2;\n    let cloud = void 0;\n    if (props.publicApiKey) {\n      cloud = {\n        guardrails: {\n          input: {\n            restrictToTopic: {\n              enabled: Boolean(props.guardrails_c),\n              validTopics: ((_a2 = props.guardrails_c) == null ? void 0 : _a2.validTopics) || [],\n              invalidTopics: ((_b2 = props.guardrails_c) == null ? void 0 : _b2.invalidTopics) || []\n            }\n          }\n        }\n      };\n    }\n    return (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({\n      publicApiKey: props.publicApiKey\n    }, cloud ? { cloud } : {}), {\n      chatApiEndpoint,\n      headers: props.headers || {},\n      properties: props.properties || {},\n      transcribeAudioUrl: props.transcribeAudioUrl,\n      textToSpeechUrl: props.textToSpeechUrl,\n      credentials: props.credentials\n    });\n  }, [\n    props.publicApiKey,\n    props.headers,\n    props.properties,\n    props.transcribeAudioUrl,\n    props.textToSpeechUrl,\n    props.credentials,\n    props.cloudRestrictToTopic,\n    props.guardrails_c\n  ]);\n  const headers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const authHeaders = Object.values(authStates || {}).reduce((acc, state) => {\n      if (state.status === \"authenticated\" && state.authHeaders) {\n        return (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({}, acc), Object.entries(state.authHeaders).reduce(\n          (headers2, [key, value]) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({}, headers2), {\n            [key.startsWith(\"X-Custom-\") ? key : `X-Custom-${key}`]: value\n          }),\n          {}\n        ));\n      }\n      return acc;\n    }, {});\n    return (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({}, copilotApiConfig.headers || {}), copilotApiConfig.publicApiKey ? { [_copilotkit_shared__WEBPACK_IMPORTED_MODULE_7__.COPILOT_CLOUD_PUBLIC_API_KEY_HEADER]: copilotApiConfig.publicApiKey } : {}), authHeaders);\n  }, [copilotApiConfig.headers, copilotApiConfig.publicApiKey, authStates]);\n  const runtimeClient = (0,_chunk_ISYBUDL4_mjs__WEBPACK_IMPORTED_MODULE_11__.useCopilotRuntimeClient)({\n    url: copilotApiConfig.chatApiEndpoint,\n    publicApiKey: copilotApiConfig.publicApiKey,\n    headers,\n    credentials: copilotApiConfig.credentials,\n    showDevConsole: (_b = props.showDevConsole) != null ? _b : false,\n    onTrace: props.onTrace\n  });\n  const [chatSuggestionConfiguration, setChatSuggestionConfiguration] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n  const addChatSuggestionConfiguration = (id, suggestion) => {\n    setChatSuggestionConfiguration((prev) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({}, prev), { [id]: suggestion }));\n  };\n  const removeChatSuggestionConfiguration = (id) => {\n    setChatSuggestionConfiguration((prev) => {\n      const _a2 = prev, { [id]: _ } = _a2, rest = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__objRest)(_a2, [(0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__restKey)(id)]);\n      return rest;\n    });\n  };\n  const [availableAgents, setAvailableAgents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [coagentStates, setCoagentStates] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n  const coagentStatesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n  const setCoagentStatesWithRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (value) => {\n      const newValue = typeof value === \"function\" ? value(coagentStatesRef.current) : value;\n      coagentStatesRef.current = newValue;\n      setCoagentStates((prev) => {\n        return newValue;\n      });\n    },\n    []\n  );\n  const hasLoadedAgents = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (hasLoadedAgents.current)\n      return;\n    const fetchData = () => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__async)(this, null, function* () {\n      var _a2;\n      const result = yield runtimeClient.availableAgents();\n      if ((_a2 = result.data) == null ? void 0 : _a2.availableAgents) {\n        setAvailableAgents(result.data.availableAgents.agents);\n      }\n      hasLoadedAgents.current = true;\n    });\n    void fetchData();\n  }, []);\n  let initialAgentSession = null;\n  if (props.agent) {\n    initialAgentSession = {\n      agentName: props.agent\n    };\n  }\n  const [agentSession, setAgentSession] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialAgentSession);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (props.agent) {\n      setAgentSession({\n        agentName: props.agent\n      });\n    } else {\n      setAgentSession(null);\n    }\n  }, [props.agent]);\n  const [internalThreadId, setInternalThreadId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(props.threadId || (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_12__.randomUUID)());\n  const setThreadId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (value) => {\n      if (props.threadId) {\n        throw new Error(\"Cannot call setThreadId() when threadId is provided via props.\");\n      }\n      setInternalThreadId(value);\n    },\n    [props.threadId]\n  );\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (props.threadId !== void 0) {\n      setInternalThreadId(props.threadId);\n    }\n  }, [props.threadId]);\n  const [runId, setRunId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const chatAbortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const showDevConsole = (_c = props.showDevConsole) != null ? _c : false;\n  const [langGraphInterruptAction, _setLangGraphInterruptAction] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const setLangGraphInterruptAction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((action) => {\n    _setLangGraphInterruptAction((prev) => {\n      if (prev == null)\n        return action;\n      if (action == null)\n        return null;\n      let event = prev.event;\n      if (action.event) {\n        event = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({}, prev.event), action.event);\n      }\n      return (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({}, prev), action), { event });\n    });\n  }, []);\n  const removeLangGraphInterruptAction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setLangGraphInterruptAction(null);\n  }, []);\n  const memoizedChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => children, [children]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n    _chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_13__.CopilotContext.Provider,\n    {\n      value: {\n        actions,\n        chatComponentsCache,\n        getFunctionCallHandler,\n        setAction,\n        removeAction,\n        coAgentStateRenders,\n        setCoAgentStateRender,\n        removeCoAgentStateRender,\n        getContextString,\n        addContext,\n        removeContext,\n        getAllContext,\n        getDocumentsContext,\n        addDocumentContext,\n        removeDocumentContext,\n        copilotApiConfig,\n        isLoading,\n        setIsLoading,\n        chatSuggestionConfiguration,\n        addChatSuggestionConfiguration,\n        removeChatSuggestionConfiguration,\n        chatInstructions,\n        setChatInstructions,\n        additionalInstructions,\n        setAdditionalInstructions,\n        showDevConsole,\n        coagentStates,\n        setCoagentStates,\n        coagentStatesRef,\n        setCoagentStatesWithRef,\n        agentSession,\n        setAgentSession,\n        runtimeClient,\n        forwardedParameters: props.forwardedParameters || {},\n        agentLock: props.agent || null,\n        threadId: internalThreadId,\n        setThreadId,\n        runId,\n        setRunId,\n        chatAbortControllerRef,\n        availableAgents,\n        authConfig_c: props.authConfig_c,\n        authStates_c: authStates,\n        setAuthStates_c: setAuthStates,\n        extensions,\n        setExtensions,\n        langGraphInterruptAction,\n        setLangGraphInterruptAction,\n        removeLangGraphInterruptAction,\n        onTrace: props.onTrace\n      },\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_chunk_VJCHRQ7Q_mjs__WEBPACK_IMPORTED_MODULE_14__.CopilotMessages, { children: memoizedChildren })\n    }\n  );\n}\nvar defaultCopilotContextCategories = [\"global\"];\nfunction entryPointsToFunctionCallHandler(actions) {\n  return (_0) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__async)(this, [_0], function* ({ name, args }) {\n    let actionsByFunctionName = {};\n    for (let action2 of actions) {\n      actionsByFunctionName[action2.name] = action2;\n    }\n    const action = actionsByFunctionName[name];\n    let result = void 0;\n    if (action) {\n      yield new Promise((resolve, reject) => {\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__async)(this, null, function* () {\n          var _a;\n          try {\n            result = yield (_a = action.handler) == null ? void 0 : _a.call(action, args);\n            resolve();\n          } catch (error) {\n            reject(error);\n          }\n        }));\n      });\n      yield new Promise((resolve) => setTimeout(resolve, 20));\n    }\n    return result;\n  });\n}\nfunction formatFeatureName(featureName) {\n  return featureName.replace(/_c$/, \"\").split(\"_\").map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(\" \");\n}\nfunction validateProps(props) {\n  const cloudFeatures = Object.keys(props).filter((key) => key.endsWith(\"_c\"));\n  if (!props.runtimeUrl && !props.publicApiKey) {\n    throw new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_15__.ConfigurationError(\"Missing required prop: 'runtimeUrl' or 'publicApiKey'\");\n  }\n  if (cloudFeatures.length > 0 && !props.publicApiKey) {\n    throw new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_15__.MissingPublicApiKeyError(\n      `Missing required prop: 'publicApiKey' to use cloud features: ${cloudFeatures.map(formatFeatureName).join(\", \")}`\n    );\n  }\n}\n\n// src/utils/extract.ts\n\nfunction extract(_0) {\n  return (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__async)(this, arguments, function* ({\n    context,\n    instructions,\n    parameters,\n    include,\n    data,\n    abortSignal,\n    stream,\n    requestType = _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_16__.CopilotRequestType.Task,\n    forwardedParameters\n  }) {\n    var _a, _b;\n    const { messages } = context;\n    const action = {\n      name: \"extract\",\n      description: instructions,\n      parameters,\n      handler: (args) => {\n      }\n    };\n    const includeReadable = (_a = include == null ? void 0 : include.readable) != null ? _a : false;\n    const includeMessages = (_b = include == null ? void 0 : include.messages) != null ? _b : false;\n    let contextString = \"\";\n    if (data) {\n      contextString = (typeof data === \"string\" ? data : JSON.stringify(data)) + \"\\n\\n\";\n    }\n    if (includeReadable) {\n      contextString += context.getContextString([], defaultCopilotContextCategories);\n    }\n    const systemMessage = new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_17__.TextMessage({\n      content: makeSystemMessage(contextString, instructions),\n      role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_17__.Role.System\n    });\n    const instructionsMessage = new _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_17__.TextMessage({\n      content: makeInstructionsMessage(instructions),\n      role: _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_17__.Role.User\n    });\n    const response = context.runtimeClient.asStream(\n      context.runtimeClient.generateCopilotResponse({\n        data: {\n          frontend: {\n            actions: [\n              {\n                name: action.name,\n                description: action.description || \"\",\n                jsonSchema: JSON.stringify((0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_18__.actionParametersToJsonSchema)(action.parameters || []))\n              }\n            ],\n            url: window.location.href\n          },\n          messages: (0,_copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_19__.convertMessagesToGqlInput)(\n            includeMessages ? [systemMessage, instructionsMessage, ...(0,_copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_19__.filterAgentStateMessages)(messages)] : [systemMessage, instructionsMessage]\n          ),\n          metadata: {\n            requestType\n          },\n          forwardedParameters: (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({}, forwardedParameters != null ? forwardedParameters : {}), {\n            toolChoice: \"function\",\n            toolChoiceFunctionName: action.name\n          })\n        },\n        properties: context.copilotApiConfig.properties,\n        signal: abortSignal\n      })\n    );\n    const reader = response.getReader();\n    let isInitial = true;\n    let actionExecutionMessage = void 0;\n    while (true) {\n      const { done, value } = yield reader.read();\n      if (done) {\n        break;\n      }\n      if (abortSignal == null ? void 0 : abortSignal.aborted) {\n        throw new Error(\"Aborted\");\n      }\n      actionExecutionMessage = (0,_copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_19__.convertGqlOutputToMessages)(\n        value.generateCopilotResponse.messages\n      ).find((msg) => msg.isActionExecutionMessage());\n      if (!actionExecutionMessage) {\n        continue;\n      }\n      stream == null ? void 0 : stream({\n        status: isInitial ? \"initial\" : \"inProgress\",\n        args: actionExecutionMessage.arguments\n      });\n      isInitial = false;\n    }\n    if (!actionExecutionMessage) {\n      throw new Error(\"extract() failed: No function call occurred\");\n    }\n    stream == null ? void 0 : stream({\n      status: \"complete\",\n      args: actionExecutionMessage.arguments\n    });\n    return actionExecutionMessage.arguments;\n  });\n}\nfunction makeInstructionsMessage(instructions) {\n  return `\nThe user has given you the following task to complete:\n\n\\`\\`\\`\n${instructions}\n\\`\\`\\`\n\nAny additional messages provided are for providing context only and should not be used to ask questions or engage in conversation.\n`;\n}\nfunction makeSystemMessage(contextString, instructions) {\n  return `\nPlease act as an efficient, competent, conscientious, and industrious professional assistant.\n\nHelp the user achieve their goals, and you do so in a way that is as efficient as possible, without unnecessary fluff, but also without sacrificing professionalism.\nAlways be polite and respectful, and prefer brevity over verbosity.\n\nThe user has provided you with the following context:\n\\`\\`\\`\n${contextString}\n\\`\\`\\`\n\nThey have also provided you with a function called extract you MUST call to initiate actions on their behalf.\n\nPlease assist them as best you can.\n\nThis is not a conversation, so please do not ask questions. Just call the function without saying anything else.\n`;\n}\n\n\n//# sourceMappingURL=chunk-RN3ZRHI7.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-RN3ZRHI7.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __async: () => (/* binding */ __async),\n/* harmony export */   __objRest: () => (/* binding */ __objRest),\n/* harmony export */   __restKey: () => (/* binding */ __restKey),\n/* harmony export */   __spreadProps: () => (/* binding */ __spreadProps),\n/* harmony export */   __spreadValues: () => (/* binding */ __spreadValues)\n/* harmony export */ });\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __restKey = (key) => typeof key === \"symbol\" ? key : key + \"\";\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n\n//# sourceMappingURL=chunk-SKC7AJIV.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-T4ZKC4X4.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-T4ZKC4X4.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCoAgentStateRender: () => (/* binding */ useCoAgentStateRender)\n/* harmony export */ });\n/* harmony import */ var _chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-VRXANACV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VRXANACV.mjs\");\n/* harmony import */ var _chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-XFOTNHYA.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-VNNKZIFB.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs\");\n\n\n\n// src/hooks/use-coagent-state-render.ts\n\n\nfunction useCoAgentStateRender(action, dependencies) {\n  const {\n    setCoAgentStateRender,\n    removeCoAgentStateRender,\n    coAgentStateRenders,\n    chatComponentsCache,\n    availableAgents\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_1__.CopilotContext);\n  const idRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)((0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__.randomId)());\n  const { setBannerError, addToast } = (0,_chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if ((availableAgents == null ? void 0 : availableAgents.length) && !availableAgents.some((a) => a.name === action.name)) {\n      const message = `(useCoAgentStateRender): Agent \"${action.name}\" not found. Make sure the agent exists and is properly configured.`;\n      const agentError = new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_4__.CopilotKitAgentDiscoveryError({\n        agentName: action.name,\n        availableAgents: availableAgents.map((a) => ({ name: a.name, id: a.id }))\n      });\n      setBannerError(agentError);\n    }\n  }, [availableAgents]);\n  const key = `${action.name}-${action.nodeName || \"global\"}`;\n  if (dependencies === void 0) {\n    if (coAgentStateRenders[idRef.current]) {\n      coAgentStateRenders[idRef.current].handler = action.handler;\n      if (typeof action.render === \"function\") {\n        if (chatComponentsCache.current !== null) {\n          chatComponentsCache.current.coAgentStateRenders[key] = action.render;\n        }\n      }\n    }\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const currentId = idRef.current;\n    const hasDuplicate = Object.entries(coAgentStateRenders).some(([id, otherAction]) => {\n      if (id === currentId)\n        return false;\n      if (otherAction.name !== action.name)\n        return false;\n      const hasNodeName = !!action.nodeName;\n      const hasOtherNodeName = !!otherAction.nodeName;\n      if (!hasNodeName && !hasOtherNodeName)\n        return true;\n      if (hasNodeName !== hasOtherNodeName)\n        return false;\n      return action.nodeName === otherAction.nodeName;\n    });\n    if (hasDuplicate) {\n      const message = action.nodeName ? `Found multiple state renders for agent ${action.name} and node ${action.nodeName}. State renders might get overridden` : `Found multiple state renders for agent ${action.name}. State renders might get overridden`;\n      addToast({\n        type: \"warning\",\n        message,\n        id: `dup-action-${action.name}`\n      });\n    }\n  }, [coAgentStateRenders]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setCoAgentStateRender(idRef.current, action);\n    if (chatComponentsCache.current !== null && action.render !== void 0) {\n      chatComponentsCache.current.coAgentStateRenders[key] = action.render;\n    }\n    return () => {\n      removeCoAgentStateRender(idRef.current);\n    };\n  }, [\n    setCoAgentStateRender,\n    removeCoAgentStateRender,\n    action.name,\n    // include render only if it's a string\n    typeof action.render === \"string\" ? action.render : void 0,\n    // dependencies set by the developer\n    ...dependencies || []\n  ]);\n}\n\n\n//# sourceMappingURL=chunk-T4ZKC4X4.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-T4ZKC4X4.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VJCHRQ7Q.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VJCHRQ7Q.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CopilotMessages: () => (/* binding */ CopilotMessages)\n/* harmony export */ });\n/* harmony import */ var _chunk_DCTJZ742_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./chunk-DCTJZ742.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-DCTJZ742.mjs\");\n/* harmony import */ var _chunk_GFJW4RIM_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-GFJW4RIM.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-GFJW4RIM.mjs\");\n/* harmony import */ var _chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-VRXANACV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VRXANACV.mjs\");\n/* harmony import */ var _chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-XFOTNHYA.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var _chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./chunk-SKC7AJIV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @copilotkit/runtime-client-gql */ \"(ssr)/./node_modules/.pnpm/@copilotkit+runtime-client-gql@1.9.2-next.7_graphql@16.9.0_react@19.0.0/node_modules/@copilotkit/runtime-client-gql/dist/chunk-P2AUSQOK.mjs\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\n\n// src/components/copilot-provider/copilot-messages.tsx\n\n\n\n\nfunction CopilotMessages({ children }) {\n  const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const lastLoadedThreadId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const lastLoadedAgentName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const lastLoadedMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const { threadId, agentSession, runtimeClient, showDevConsole } = (0,_chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_2__.useCopilotContext)();\n  const { setBannerError } = (0,_chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n  const createStructuredError = (gqlError) => {\n    const extensions = gqlError.extensions;\n    const originalError = extensions == null ? void 0 : extensions.originalError;\n    if (originalError == null ? void 0 : originalError.stack) {\n      if (originalError.stack.includes(\"CopilotApiDiscoveryError\")) {\n        return new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_4__.CopilotKitApiDiscoveryError({ message: originalError.message });\n      }\n      if (originalError.stack.includes(\"CopilotKitRemoteEndpointDiscoveryError\")) {\n        return new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_4__.CopilotKitRemoteEndpointDiscoveryError({ message: originalError.message });\n      }\n      if (originalError.stack.includes(\"CopilotKitAgentDiscoveryError\")) {\n        return new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_4__.CopilotKitAgentDiscoveryError({\n          agentName: \"\",\n          availableAgents: []\n        });\n      }\n    }\n    const message = (originalError == null ? void 0 : originalError.message) || gqlError.message;\n    const code = extensions == null ? void 0 : extensions.code;\n    if (code) {\n      return new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_4__.CopilotKitError({ message, code });\n    }\n    return null;\n  };\n  const handleGraphQLErrors = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (error) => {\n      var _a;\n      if ((_a = error.graphQLErrors) == null ? void 0 : _a.length) {\n        const graphQLErrors = error.graphQLErrors;\n        const routeError = (gqlError) => {\n          const extensions = gqlError.extensions;\n          const visibility = extensions == null ? void 0 : extensions.visibility;\n          const isDev = (0,_chunk_GFJW4RIM_mjs__WEBPACK_IMPORTED_MODULE_5__.shouldShowDevConsole)(showDevConsole);\n          if (!isDev) {\n            console.error(\"CopilotKit Error (hidden in production):\", gqlError.message);\n            return;\n          }\n          if (visibility === _copilotkit_shared__WEBPACK_IMPORTED_MODULE_4__.ErrorVisibility.SILENT) {\n            console.error(\"CopilotKit Silent Error:\", gqlError.message);\n            return;\n          }\n          const ckError = createStructuredError(gqlError);\n          if (ckError) {\n            setBannerError(ckError);\n          } else {\n            const fallbackError = new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_4__.CopilotKitError({\n              message: gqlError.message,\n              code: _copilotkit_shared__WEBPACK_IMPORTED_MODULE_4__.CopilotKitErrorCode.UNKNOWN\n            });\n            setBannerError(fallbackError);\n          }\n        };\n        graphQLErrors.forEach(routeError);\n      } else {\n        const isDev = (0,_chunk_GFJW4RIM_mjs__WEBPACK_IMPORTED_MODULE_5__.shouldShowDevConsole)(showDevConsole);\n        if (!isDev) {\n          console.error(\"CopilotKit Error (hidden in production):\", error);\n        } else {\n          const fallbackError = new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_4__.CopilotKitError({\n            message: (error == null ? void 0 : error.message) || String(error),\n            code: _copilotkit_shared__WEBPACK_IMPORTED_MODULE_4__.CopilotKitErrorCode.UNKNOWN\n          });\n          setBannerError(fallbackError);\n        }\n      }\n    },\n    [setBannerError, showDevConsole]\n  );\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!threadId || threadId === lastLoadedThreadId.current)\n      return;\n    if (threadId === lastLoadedThreadId.current && (agentSession == null ? void 0 : agentSession.agentName) === lastLoadedAgentName.current) {\n      return;\n    }\n    const fetchMessages = () => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_6__.__async)(this, null, function* () {\n      var _a, _b, _c, _d;\n      if (!(agentSession == null ? void 0 : agentSession.agentName))\n        return;\n      const result = yield runtimeClient.loadAgentState({\n        threadId,\n        agentName: agentSession == null ? void 0 : agentSession.agentName\n      });\n      if (result.error) {\n        lastLoadedThreadId.current = threadId;\n        lastLoadedAgentName.current = agentSession == null ? void 0 : agentSession.agentName;\n        handleGraphQLErrors(result.error);\n        return;\n      }\n      const newMessages = (_b = (_a = result.data) == null ? void 0 : _a.loadAgentState) == null ? void 0 : _b.messages;\n      if (newMessages === lastLoadedMessages.current)\n        return;\n      if ((_d = (_c = result.data) == null ? void 0 : _c.loadAgentState) == null ? void 0 : _d.threadExists) {\n        lastLoadedMessages.current = newMessages;\n        lastLoadedThreadId.current = threadId;\n        lastLoadedAgentName.current = agentSession == null ? void 0 : agentSession.agentName;\n        const messages2 = (0,_copilotkit_runtime_client_gql__WEBPACK_IMPORTED_MODULE_7__.loadMessagesFromJsonRepresentation)(JSON.parse(newMessages || \"[]\"));\n        setMessages(messages2);\n      }\n    });\n    void fetchMessages();\n  }, [threadId, agentSession == null ? void 0 : agentSession.agentName, runtimeClient]);\n  const memoizedChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => children, [children]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    _chunk_DCTJZ742_mjs__WEBPACK_IMPORTED_MODULE_8__.CopilotMessagesContext.Provider,\n    {\n      value: {\n        messages,\n        setMessages\n      },\n      children: memoizedChildren\n    }\n  );\n}\n\n\n//# sourceMappingURL=chunk-VJCHRQ7Q.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VJCHRQ7Q.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VRXANACV.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VRXANACV.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var _chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-SKC7AJIV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n// src/components/toast/toast-provider.tsx\n\n\n\nvar ToastContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(void 0);\nfunction getErrorSeverity(error) {\n  if (error.severity) {\n    switch (error.severity) {\n      case _copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__.Severity.CRITICAL:\n        return \"critical\";\n      case _copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__.Severity.WARNING:\n        return \"warning\";\n      case _copilotkit_shared__WEBPACK_IMPORTED_MODULE_2__.Severity.INFO:\n        return \"info\";\n      default:\n        return \"info\";\n    }\n  }\n  const message = error.message.toLowerCase();\n  if (message.includes(\"api key\") || message.includes(\"401\") || message.includes(\"unauthorized\") || message.includes(\"authentication\") || message.includes(\"incorrect api key\")) {\n    return \"critical\";\n  }\n  return \"info\";\n}\nfunction getErrorColors(severity) {\n  switch (severity) {\n    case \"critical\":\n      return {\n        background: \"#fee2e2\",\n        border: \"#dc2626\",\n        text: \"#7f1d1d\",\n        icon: \"#dc2626\"\n      };\n    case \"warning\":\n      return {\n        background: \"#fef3c7\",\n        border: \"#d97706\",\n        text: \"#78350f\",\n        icon: \"#d97706\"\n      };\n    case \"info\":\n      return {\n        background: \"#dbeafe\",\n        border: \"#2563eb\",\n        text: \"#1e3a8a\",\n        icon: \"#2563eb\"\n      };\n  }\n}\nfunction useToast() {\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ToastContext);\n  if (!context) {\n    throw new Error(\"useToast must be used within a ToastProvider\");\n  }\n  return context;\n}\nfunction ToastProvider({\n  enabled,\n  children\n}) {\n  const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [bannerError, setBannerErrorState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const removeToast = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((id) => {\n    setToasts((prev) => prev.filter((toast) => toast.id !== id));\n  }, []);\n  const addToast = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (toast) => {\n      var _a;\n      if (!enabled) {\n        return;\n      }\n      const id = (_a = toast.id) != null ? _a : Math.random().toString(36).substring(2, 9);\n      setToasts((currentToasts) => {\n        if (currentToasts.find((toast2) => toast2.id === id))\n          return currentToasts;\n        return [...currentToasts, (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_3__.__spreadValues)({}, toast), { id })];\n      });\n      if (toast.duration) {\n        setTimeout(() => {\n          removeToast(id);\n        }, toast.duration);\n      }\n    },\n    [enabled, removeToast]\n  );\n  const setBannerError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (error) => {\n      if (!enabled && error !== null) {\n        return;\n      }\n      setBannerErrorState(error);\n    },\n    [enabled]\n  );\n  const addGraphQLErrorsToast = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((errors) => {\n    console.warn(\"addGraphQLErrorsToast is deprecated. All errors now show as banners.\");\n  }, []);\n  const value = {\n    toasts,\n    addToast,\n    addGraphQLErrorsToast,\n    removeToast,\n    enabled,\n    bannerError,\n    setBannerError\n  };\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(ToastContext.Provider, { value, children: [\n    bannerError && (() => {\n      const severity = getErrorSeverity(bannerError);\n      const colors = getErrorColors(severity);\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n        \"div\",\n        {\n          style: {\n            position: \"fixed\",\n            bottom: \"20px\",\n            left: \"50%\",\n            transform: \"translateX(-50%)\",\n            zIndex: 9999,\n            backgroundColor: colors.background,\n            border: `1px solid ${colors.border}`,\n            borderLeft: `4px solid ${colors.border}`,\n            borderRadius: \"8px\",\n            padding: \"10px 14px\",\n            fontSize: \"13px\",\n            boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\",\n            backdropFilter: \"blur(8px)\",\n            maxWidth: \"500px\",\n            minWidth: \"350px\"\n          },\n          children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\n            \"div\",\n            {\n              style: {\n                display: \"flex\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                gap: \"10px\"\n              },\n              children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { style: { display: \"flex\", alignItems: \"center\", gap: \"8px\", flex: 1 }, children: [\n                  /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n                    \"div\",\n                    {\n                      style: {\n                        width: \"12px\",\n                        height: \"12px\",\n                        borderRadius: \"50%\",\n                        backgroundColor: colors.border,\n                        flexShrink: 0\n                      }\n                    }\n                  ),\n                  /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", { style: { display: \"flex\", alignItems: \"center\", gap: \"10px\", flex: 1 }, children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n                      \"div\",\n                      {\n                        style: {\n                          color: colors.text,\n                          lineHeight: \"1.4\",\n                          fontWeight: \"400\",\n                          fontSize: \"13px\",\n                          flex: 1,\n                          wordWrap: \"break-word\",\n                          overflowWrap: \"break-word\",\n                          hyphens: \"auto\"\n                        },\n                        children: (() => {\n                          const message = bannerError.message;\n                          const markdownLinkRegex = /\\[([^\\]]+)\\]\\(([^)]+)\\)/g;\n                          const plainUrlRegex = /(https?:\\/\\/[^\\s)]+)/g;\n                          let cleanMessage = message.replace(markdownLinkRegex, \"\").replace(plainUrlRegex, \"\").replace(/See more:\\s*/g, \"\").replace(/\\s+/g, \" \").trim();\n                          if (cleanMessage.length > 120) {\n                            cleanMessage = cleanMessage.substring(0, 117) + \"...\";\n                          }\n                          return cleanMessage;\n                        })()\n                      }\n                    ),\n                    (() => {\n                      const message = bannerError.message;\n                      const markdownLinkRegex = /\\[([^\\]]+)\\]\\(([^)]+)\\)/g;\n                      const plainUrlRegex = /(https?:\\/\\/[^\\s)]+)/g;\n                      let url = null;\n                      let buttonText = \"See More\";\n                      const markdownMatch = markdownLinkRegex.exec(message);\n                      if (markdownMatch) {\n                        url = markdownMatch[2];\n                        buttonText = \"See More\";\n                      } else {\n                        const urlMatch = plainUrlRegex.exec(message);\n                        if (urlMatch) {\n                          url = urlMatch[0];\n                          buttonText = \"See More\";\n                        }\n                      }\n                      if (!url)\n                        return null;\n                      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n                        \"button\",\n                        {\n                          onClick: () => window.open(url, \"_blank\", \"noopener,noreferrer\"),\n                          style: {\n                            background: colors.border,\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"5px\",\n                            padding: \"4px 10px\",\n                            fontSize: \"11px\",\n                            fontWeight: \"500\",\n                            cursor: \"pointer\",\n                            transition: \"all 0.2s ease\",\n                            flexShrink: 0\n                          },\n                          onMouseEnter: (e) => {\n                            e.currentTarget.style.opacity = \"0.9\";\n                            e.currentTarget.style.transform = \"translateY(-1px)\";\n                          },\n                          onMouseLeave: (e) => {\n                            e.currentTarget.style.opacity = \"1\";\n                            e.currentTarget.style.transform = \"translateY(0)\";\n                          },\n                          children: buttonText\n                        }\n                      );\n                    })()\n                  ] })\n                ] }),\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n                  \"button\",\n                  {\n                    onClick: () => setBannerError(null),\n                    style: {\n                      background: \"transparent\",\n                      border: \"none\",\n                      color: colors.text,\n                      cursor: \"pointer\",\n                      padding: \"2px\",\n                      borderRadius: \"3px\",\n                      fontSize: \"14px\",\n                      lineHeight: \"1\",\n                      opacity: 0.6,\n                      transition: \"all 0.2s ease\",\n                      flexShrink: 0\n                    },\n                    title: \"Dismiss\",\n                    onMouseEnter: (e) => {\n                      e.currentTarget.style.opacity = \"1\";\n                      e.currentTarget.style.background = \"rgba(0, 0, 0, 0.05)\";\n                    },\n                    onMouseLeave: (e) => {\n                      e.currentTarget.style.opacity = \"0.6\";\n                      e.currentTarget.style.background = \"transparent\";\n                    },\n                    children: \"\\xD7\"\n                  }\n                )\n              ]\n            }\n          )\n        }\n      );\n    })(),\n    children\n  ] });\n}\n\n\n//# sourceMappingURL=chunk-VRXANACV.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VRXANACV.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CopilotContext: () => (/* binding */ CopilotContext),\n/* harmony export */   useCopilotContext: () => (/* binding */ useCopilotContext)\n/* harmony export */ });\n/* harmony import */ var _chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-SKC7AJIV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n// src/context/copilot-context.tsx\n\nvar emptyCopilotContext = {\n  actions: {},\n  setAction: () => {\n  },\n  removeAction: () => {\n  },\n  coAgentStateRenders: {},\n  setCoAgentStateRender: () => {\n  },\n  removeCoAgentStateRender: () => {\n  },\n  chatComponentsCache: { current: { actions: {}, coAgentStateRenders: {} } },\n  getContextString: (documents, categories) => returnAndThrowInDebug(\"\"),\n  addContext: () => \"\",\n  removeContext: () => {\n  },\n  getAllContext: () => [],\n  getFunctionCallHandler: () => returnAndThrowInDebug(() => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_1__.__async)(void 0, null, function* () {\n  })),\n  isLoading: false,\n  setIsLoading: () => returnAndThrowInDebug(false),\n  chatInstructions: \"\",\n  setChatInstructions: () => returnAndThrowInDebug(\"\"),\n  additionalInstructions: [],\n  setAdditionalInstructions: () => returnAndThrowInDebug([]),\n  getDocumentsContext: (categories) => returnAndThrowInDebug([]),\n  addDocumentContext: () => returnAndThrowInDebug(\"\"),\n  removeDocumentContext: () => {\n  },\n  runtimeClient: {},\n  copilotApiConfig: new class {\n    get chatApiEndpoint() {\n      throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n    }\n    get headers() {\n      return {};\n    }\n    get body() {\n      return {};\n    }\n  }(),\n  chatSuggestionConfiguration: {},\n  addChatSuggestionConfiguration: () => {\n  },\n  removeChatSuggestionConfiguration: () => {\n  },\n  showDevConsole: false,\n  coagentStates: {},\n  setCoagentStates: () => {\n  },\n  coagentStatesRef: { current: {} },\n  setCoagentStatesWithRef: () => {\n  },\n  agentSession: null,\n  setAgentSession: () => {\n  },\n  forwardedParameters: {},\n  agentLock: null,\n  threadId: \"\",\n  setThreadId: () => {\n  },\n  runId: null,\n  setRunId: () => {\n  },\n  chatAbortControllerRef: { current: null },\n  availableAgents: [],\n  extensions: {},\n  setExtensions: () => {\n  },\n  langGraphInterruptAction: null,\n  setLangGraphInterruptAction: () => null,\n  removeLangGraphInterruptAction: () => null,\n  onTrace: void 0\n};\nvar CopilotContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(emptyCopilotContext);\nfunction useCopilotContext() {\n  const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(CopilotContext);\n  if (context === emptyCopilotContext) {\n    throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n  }\n  return context;\n}\nfunction returnAndThrowInDebug(_value) {\n  throw new Error(\"Remember to wrap your app in a `<CopilotKit> {...} </CopilotKit>` !!!\");\n}\n\n\n//# sourceMappingURL=chunk-XFOTNHYA.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-ZHEEHGLS.mjs":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-ZHEEHGLS.mjs ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   runAgent: () => (/* binding */ runAgent),\n/* harmony export */   startAgent: () => (/* binding */ startAgent),\n/* harmony export */   stopAgent: () => (/* binding */ stopAgent),\n/* harmony export */   useCoAgent: () => (/* binding */ useCoAgent)\n/* harmony export */ });\n/* harmony import */ var _chunk_I4JPQECN_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./chunk-I4JPQECN.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-I4JPQECN.mjs\");\n/* harmony import */ var _chunk_DCTJZ742_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./chunk-DCTJZ742.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-DCTJZ742.mjs\");\n/* harmony import */ var _chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./chunk-CCESTGAM.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-CCESTGAM.mjs\");\n/* harmony import */ var _chunk_ISYBUDL4_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./chunk-ISYBUDL4.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-ISYBUDL4.mjs\");\n/* harmony import */ var _chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-VRXANACV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-VRXANACV.mjs\");\n/* harmony import */ var _chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chunk-XFOTNHYA.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-XFOTNHYA.mjs\");\n/* harmony import */ var _chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chunk-SKC7AJIV.mjs */ \"(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-SKC7AJIV.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.1.0_@playwright+test@1.50.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _copilotkit_shared__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @copilotkit/shared */ \"(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs\");\n\n\n\n\n\n\n\n\n// src/hooks/use-coagent.ts\n\n\nfunction useCoAgent(options) {\n  const generalContext = (0,_chunk_XFOTNHYA_mjs__WEBPACK_IMPORTED_MODULE_1__.useCopilotContext)();\n  const { availableAgents } = generalContext;\n  const { setBannerError } = (0,_chunk_VRXANACV_mjs__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n  const lastLoadedThreadId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const lastLoadedState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const { name } = options;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if ((availableAgents == null ? void 0 : availableAgents.length) && !availableAgents.some((a) => a.name === name)) {\n      const message = `(useCoAgent): Agent \"${name}\" not found. Make sure the agent exists and is properly configured.`;\n      console.warn(message);\n      const agentError = new _copilotkit_shared__WEBPACK_IMPORTED_MODULE_3__.CopilotKitAgentDiscoveryError({\n        agentName: name,\n        availableAgents: availableAgents.map((a) => ({ name: a.name, id: a.id }))\n      });\n      setBannerError(agentError);\n    }\n  }, [availableAgents]);\n  const messagesContext = (0,_chunk_DCTJZ742_mjs__WEBPACK_IMPORTED_MODULE_4__.useCopilotMessagesContext)();\n  const context = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__spreadValues)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__spreadValues)({}, generalContext), messagesContext);\n  const { coagentStates, coagentStatesRef, setCoagentStatesWithRef, threadId, copilotApiConfig } = context;\n  const { appendMessage, runChatCompletion } = (0,_chunk_I4JPQECN_mjs__WEBPACK_IMPORTED_MODULE_6__.useCopilotChat)();\n  const headers = (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__spreadValues)({}, copilotApiConfig.headers || {});\n  const runtimeClient = (0,_chunk_ISYBUDL4_mjs__WEBPACK_IMPORTED_MODULE_7__.useCopilotRuntimeClient)({\n    url: copilotApiConfig.chatApiEndpoint,\n    publicApiKey: copilotApiConfig.publicApiKey,\n    headers,\n    credentials: copilotApiConfig.credentials,\n    showDevConsole: context.showDevConsole\n  });\n  const setState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (newState) => {\n      let coagentState = getCoagentState({ coagentStates, name, options });\n      const updatedState = typeof newState === \"function\" ? newState(coagentState.state) : newState;\n      setCoagentStatesWithRef((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__spreadValues)({}, coagentStatesRef.current), {\n        [name]: (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__spreadValues)({}, coagentState), {\n          state: updatedState\n        })\n      }));\n    },\n    [coagentStates, name]\n  );\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const fetchAgentState = () => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__async)(this, null, function* () {\n      var _a, _b, _c, _d;\n      if (!threadId || threadId === lastLoadedThreadId.current)\n        return;\n      const result = yield runtimeClient.loadAgentState({\n        threadId,\n        agentName: name\n      });\n      const newState = (_b = (_a = result.data) == null ? void 0 : _a.loadAgentState) == null ? void 0 : _b.state;\n      if (newState === lastLoadedState.current)\n        return;\n      if (((_d = (_c = result.data) == null ? void 0 : _c.loadAgentState) == null ? void 0 : _d.threadExists) && newState && newState != \"{}\") {\n        lastLoadedState.current = newState;\n        lastLoadedThreadId.current = threadId;\n        const fetchedState = (0,_copilotkit_shared__WEBPACK_IMPORTED_MODULE_3__.parseJson)(newState, {});\n        isExternalStateManagement(options) ? options.setState(fetchedState) : setState(fetchedState);\n      }\n    });\n    void fetchAgentState();\n  }, [threadId]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (isExternalStateManagement(options)) {\n      setState(options.state);\n    } else if (coagentStates[name] === void 0) {\n      setState(options.initialState === void 0 ? {} : options.initialState);\n    }\n  }, [\n    isExternalStateManagement(options) ? JSON.stringify(options.state) : void 0,\n    // reset initialstate on reset\n    coagentStates[name] === void 0\n  ]);\n  const runAgentCallback = (0,_chunk_CCESTGAM_mjs__WEBPACK_IMPORTED_MODULE_8__.useAsyncCallback)(\n    (hint) => (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__async)(this, null, function* () {\n      yield runAgent(name, context, appendMessage, runChatCompletion, hint);\n    }),\n    [name, context, appendMessage, runChatCompletion]\n  );\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const coagentState = getCoagentState({ coagentStates, name, options });\n    return {\n      name,\n      nodeName: coagentState.nodeName,\n      threadId: coagentState.threadId,\n      running: coagentState.running,\n      state: coagentState.state,\n      setState: isExternalStateManagement(options) ? options.setState : setState,\n      start: () => startAgent(name, context),\n      stop: () => stopAgent(name, context),\n      run: runAgentCallback\n    };\n  }, [name, coagentStates, options, setState, runAgentCallback]);\n}\nfunction startAgent(name, context) {\n  const { setAgentSession } = context;\n  setAgentSession({\n    agentName: name\n  });\n}\nfunction stopAgent(name, context) {\n  const { agentSession, setAgentSession } = context;\n  if (agentSession && agentSession.agentName === name) {\n    setAgentSession(null);\n    context.setCoagentStates((prevAgentStates) => {\n      return (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__spreadValues)({}, prevAgentStates), {\n        [name]: (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__spreadProps)((0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__spreadValues)({}, prevAgentStates[name]), {\n          running: false,\n          active: false,\n          threadId: void 0,\n          nodeName: void 0,\n          runId: void 0\n        })\n      });\n    });\n  } else {\n    console.warn(`No agent session found for ${name}`);\n  }\n}\nfunction runAgent(name, context, appendMessage, runChatCompletion, hint) {\n  return (0,_chunk_SKC7AJIV_mjs__WEBPACK_IMPORTED_MODULE_5__.__async)(this, null, function* () {\n    var _a, _b;\n    const { agentSession, setAgentSession } = context;\n    if (!agentSession || agentSession.agentName !== name) {\n      setAgentSession({\n        agentName: name\n      });\n    }\n    let previousState = null;\n    for (let i = context.messages.length - 1; i >= 0; i--) {\n      const message = context.messages[i];\n      if (message.isAgentStateMessage() && message.agentName === name) {\n        previousState = message.state;\n      }\n    }\n    let state = ((_b = (_a = context.coagentStatesRef.current) == null ? void 0 : _a[name]) == null ? void 0 : _b.state) || {};\n    if (hint) {\n      const hintMessage = hint({ previousState, currentState: state });\n      if (hintMessage) {\n        yield appendMessage(hintMessage);\n      } else {\n        yield runChatCompletion();\n      }\n    } else {\n      yield runChatCompletion();\n    }\n  });\n}\nvar isExternalStateManagement = (options) => {\n  return \"state\" in options && \"setState\" in options;\n};\nvar isInternalStateManagementWithInitial = (options) => {\n  return \"initialState\" in options;\n};\nvar getCoagentState = ({\n  coagentStates,\n  name,\n  options\n}) => {\n  if (coagentStates[name]) {\n    return coagentStates[name];\n  } else {\n    return {\n      name,\n      state: isInternalStateManagementWithInitial(options) ? options.initialState : {},\n      config: options.config ? options.config : options.configurable ? { configurable: options.configurable } : {},\n      running: false,\n      active: false,\n      threadId: void 0,\n      nodeName: void 0,\n      runId: void 0\n    };\n  }\n};\n\n\n//# sourceMappingURL=chunk-ZHEEHGLS.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+react-core@1.9.2-next.7_@types+react@19.0.1_graphql@16.9.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/@copilotkit/react-core/dist/chunk-ZHEEHGLS.mjs\n");

/***/ })

};
;