"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+client-sso@3.830.0";
exports.ids = ["vendor-chunks/@aws-sdk+client-sso@3.830.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/SSOClient.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/SSOClient.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SSOClient: () => (/* binding */ SSOClient),\n/* harmony export */   __Client: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_9__.Client)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_middleware_host_header__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/middleware-host-header */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-host-header@3.821.0/node_modules/@aws-sdk/middleware-host-header/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_middleware_logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/middleware-logger */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-logger@3.821.0/node_modules/@aws-sdk/middleware-logger/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_middleware_recursion_detection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/middleware-recursion-detection */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-recursion-detection@3.821.0/node_modules/@aws-sdk/middleware-recursion-detection/dist-es/index.js\");\n/* harmony import */ var _aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @aws-sdk/middleware-user-agent */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+middleware-user-agent@3.828.0/node_modules/@aws-sdk/middleware-user-agent/dist-es/index.js\");\n/* harmony import */ var _smithy_config_resolver__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @smithy/config-resolver */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_content_length__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @smithy/middleware-content-length */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-content-length@4.0.4/node_modules/@smithy/middleware-content-length/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @smithy/middleware-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./auth/httpAuthSchemeProvider */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/auth/httpAuthSchemeProvider.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/endpoint/EndpointParameters.js\");\n/* harmony import */ var _runtimeConfig__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./runtimeConfig */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/runtimeConfig.js\");\n/* harmony import */ var _runtimeExtensions__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./runtimeExtensions */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/runtimeExtensions.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nclass SSOClient extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_9__.Client {\n    config;\n    constructor(...[configuration]) {\n        const _config_0 = (0,_runtimeConfig__WEBPACK_IMPORTED_MODULE_10__.getRuntimeConfig)(configuration || {});\n        super(_config_0);\n        this.initConfig = _config_0;\n        const _config_1 = (0,_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_11__.resolveClientEndpointParameters)(_config_0);\n        const _config_2 = (0,_aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_3__.resolveUserAgentConfig)(_config_1);\n        const _config_3 = (0,_smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_8__.resolveRetryConfig)(_config_2);\n        const _config_4 = (0,_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_4__.resolveRegionConfig)(_config_3);\n        const _config_5 = (0,_aws_sdk_middleware_host_header__WEBPACK_IMPORTED_MODULE_0__.resolveHostHeaderConfig)(_config_4);\n        const _config_6 = (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_7__.resolveEndpointConfig)(_config_5);\n        const _config_7 = (0,_auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_12__.resolveHttpAuthSchemeConfig)(_config_6);\n        const _config_8 = (0,_runtimeExtensions__WEBPACK_IMPORTED_MODULE_13__.resolveRuntimeExtensions)(_config_7, configuration?.extensions || []);\n        this.config = _config_8;\n        this.middlewareStack.use((0,_aws_sdk_middleware_user_agent__WEBPACK_IMPORTED_MODULE_3__.getUserAgentPlugin)(this.config));\n        this.middlewareStack.use((0,_smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_8__.getRetryPlugin)(this.config));\n        this.middlewareStack.use((0,_smithy_middleware_content_length__WEBPACK_IMPORTED_MODULE_6__.getContentLengthPlugin)(this.config));\n        this.middlewareStack.use((0,_aws_sdk_middleware_host_header__WEBPACK_IMPORTED_MODULE_0__.getHostHeaderPlugin)(this.config));\n        this.middlewareStack.use((0,_aws_sdk_middleware_logger__WEBPACK_IMPORTED_MODULE_1__.getLoggerPlugin)(this.config));\n        this.middlewareStack.use((0,_aws_sdk_middleware_recursion_detection__WEBPACK_IMPORTED_MODULE_2__.getRecursionDetectionPlugin)(this.config));\n        this.middlewareStack.use((0,_smithy_core__WEBPACK_IMPORTED_MODULE_5__.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config, {\n            httpAuthSchemeParametersProvider: _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_12__.defaultSSOHttpAuthSchemeParametersProvider,\n            identityProviderConfigProvider: async (config) => new _smithy_core__WEBPACK_IMPORTED_MODULE_5__.DefaultIdentityProviderConfig({\n                \"aws.auth#sigv4\": config.credentials,\n            }),\n        }));\n        this.middlewareStack.use((0,_smithy_core__WEBPACK_IMPORTED_MODULE_5__.getHttpSigningPlugin)(this.config));\n    }\n    destroy() {\n        super.destroy();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/SSOClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/auth/httpAuthExtensionConfiguration.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/auth/httpAuthExtensionConfiguration.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getHttpAuthExtensionConfiguration: () => (/* binding */ getHttpAuthExtensionConfiguration),\n/* harmony export */   resolveHttpAuthRuntimeConfig: () => (/* binding */ resolveHttpAuthRuntimeConfig)\n/* harmony export */ });\nconst getHttpAuthExtensionConfiguration = (runtimeConfig) => {\n    const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;\n    let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;\n    let _credentials = runtimeConfig.credentials;\n    return {\n        setHttpAuthScheme(httpAuthScheme) {\n            const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);\n            if (index === -1) {\n                _httpAuthSchemes.push(httpAuthScheme);\n            }\n            else {\n                _httpAuthSchemes.splice(index, 1, httpAuthScheme);\n            }\n        },\n        httpAuthSchemes() {\n            return _httpAuthSchemes;\n        },\n        setHttpAuthSchemeProvider(httpAuthSchemeProvider) {\n            _httpAuthSchemeProvider = httpAuthSchemeProvider;\n        },\n        httpAuthSchemeProvider() {\n            return _httpAuthSchemeProvider;\n        },\n        setCredentials(credentials) {\n            _credentials = credentials;\n        },\n        credentials() {\n            return _credentials;\n        },\n    };\n};\nconst resolveHttpAuthRuntimeConfig = (config) => {\n    return {\n        httpAuthSchemes: config.httpAuthSchemes(),\n        httpAuthSchemeProvider: config.httpAuthSchemeProvider(),\n        credentials: config.credentials(),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/auth/httpAuthExtensionConfiguration.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/auth/httpAuthSchemeProvider.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/auth/httpAuthSchemeProvider.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultSSOHttpAuthSchemeParametersProvider: () => (/* binding */ defaultSSOHttpAuthSchemeParametersProvider),\n/* harmony export */   defaultSSOHttpAuthSchemeProvider: () => (/* binding */ defaultSSOHttpAuthSchemeProvider),\n/* harmony export */   resolveHttpAuthSchemeConfig: () => (/* binding */ resolveHttpAuthSchemeConfig)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/resolveAwsSdkSigV4Config.js\");\n/* harmony import */ var _smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/util-middleware */ \"(rsc)/./node_modules/.pnpm/@smithy+util-middleware@4.0.4/node_modules/@smithy/util-middleware/dist-es/index.js\");\n\n\nconst defaultSSOHttpAuthSchemeParametersProvider = async (config, context, input) => {\n    return {\n        operation: (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.getSmithyContext)(context).operation,\n        region: (await (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(config.region)()) ||\n            (() => {\n                throw new Error(\"expected `region` to be configured for `aws.auth#sigv4`\");\n            })(),\n    };\n};\nfunction createAwsAuthSigv4HttpAuthOption(authParameters) {\n    return {\n        schemeId: \"aws.auth#sigv4\",\n        signingProperties: {\n            name: \"awsssoportal\",\n            region: authParameters.region,\n        },\n        propertiesExtractor: (config, context) => ({\n            signingProperties: {\n                config,\n                context,\n            },\n        }),\n    };\n}\nfunction createSmithyApiNoAuthHttpAuthOption(authParameters) {\n    return {\n        schemeId: \"smithy.api#noAuth\",\n    };\n}\nconst defaultSSOHttpAuthSchemeProvider = (authParameters) => {\n    const options = [];\n    switch (authParameters.operation) {\n        case \"GetRoleCredentials\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        case \"ListAccountRoles\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        case \"ListAccounts\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        case \"Logout\": {\n            options.push(createSmithyApiNoAuthHttpAuthOption(authParameters));\n            break;\n        }\n        default: {\n            options.push(createAwsAuthSigv4HttpAuthOption(authParameters));\n        }\n    }\n    return options;\n};\nconst resolveHttpAuthSchemeConfig = (config) => {\n    const config_0 = (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_1__.resolveAwsSdkSigV4Config)(config);\n    return Object.assign(config_0, {\n        authSchemePreference: (0,_smithy_util_middleware__WEBPACK_IMPORTED_MODULE_0__.normalizeProvider)(config.authSchemePreference ?? []),\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LXNzb0AzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtc3NvL2Rpc3QtZXMvYXV0aC9odHRwQXV0aFNjaGVtZVByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTBEO0FBQ29CO0FBQ3ZFO0FBQ1A7QUFDQSxtQkFBbUIseUVBQWdCO0FBQ25DLHVCQUF1QiwwRUFBaUI7QUFDeEM7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxxQkFBcUIsdUVBQXdCO0FBQzdDO0FBQ0EsOEJBQThCLDBFQUFpQjtBQUMvQyxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytjbGllbnQtc3NvQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NsaWVudC1zc28vZGlzdC1lcy9hdXRoL2h0dHBBdXRoU2NoZW1lUHJvdmlkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmVzb2x2ZUF3c1Nka1NpZ1Y0Q29uZmlnLCB9IGZyb20gXCJAYXdzLXNkay9jb3JlXCI7XG5pbXBvcnQgeyBnZXRTbWl0aHlDb250ZXh0LCBub3JtYWxpemVQcm92aWRlciB9IGZyb20gXCJAc21pdGh5L3V0aWwtbWlkZGxld2FyZVwiO1xuZXhwb3J0IGNvbnN0IGRlZmF1bHRTU09IdHRwQXV0aFNjaGVtZVBhcmFtZXRlcnNQcm92aWRlciA9IGFzeW5jIChjb25maWcsIGNvbnRleHQsIGlucHV0KSA9PiB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgb3BlcmF0aW9uOiBnZXRTbWl0aHlDb250ZXh0KGNvbnRleHQpLm9wZXJhdGlvbixcbiAgICAgICAgcmVnaW9uOiAoYXdhaXQgbm9ybWFsaXplUHJvdmlkZXIoY29uZmlnLnJlZ2lvbikoKSkgfHxcbiAgICAgICAgICAgICgoKSA9PiB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiZXhwZWN0ZWQgYHJlZ2lvbmAgdG8gYmUgY29uZmlndXJlZCBmb3IgYGF3cy5hdXRoI3NpZ3Y0YFwiKTtcbiAgICAgICAgICAgIH0pKCksXG4gICAgfTtcbn07XG5mdW5jdGlvbiBjcmVhdGVBd3NBdXRoU2lndjRIdHRwQXV0aE9wdGlvbihhdXRoUGFyYW1ldGVycykge1xuICAgIHJldHVybiB7XG4gICAgICAgIHNjaGVtZUlkOiBcImF3cy5hdXRoI3NpZ3Y0XCIsXG4gICAgICAgIHNpZ25pbmdQcm9wZXJ0aWVzOiB7XG4gICAgICAgICAgICBuYW1lOiBcImF3c3Nzb3BvcnRhbFwiLFxuICAgICAgICAgICAgcmVnaW9uOiBhdXRoUGFyYW1ldGVycy5yZWdpb24sXG4gICAgICAgIH0sXG4gICAgICAgIHByb3BlcnRpZXNFeHRyYWN0b3I6IChjb25maWcsIGNvbnRleHQpID0+ICh7XG4gICAgICAgICAgICBzaWduaW5nUHJvcGVydGllczoge1xuICAgICAgICAgICAgICAgIGNvbmZpZyxcbiAgICAgICAgICAgICAgICBjb250ZXh0LFxuICAgICAgICAgICAgfSxcbiAgICAgICAgfSksXG4gICAgfTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZVNtaXRoeUFwaU5vQXV0aEh0dHBBdXRoT3B0aW9uKGF1dGhQYXJhbWV0ZXJzKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgc2NoZW1lSWQ6IFwic21pdGh5LmFwaSNub0F1dGhcIixcbiAgICB9O1xufVxuZXhwb3J0IGNvbnN0IGRlZmF1bHRTU09IdHRwQXV0aFNjaGVtZVByb3ZpZGVyID0gKGF1dGhQYXJhbWV0ZXJzKSA9PiB7XG4gICAgY29uc3Qgb3B0aW9ucyA9IFtdO1xuICAgIHN3aXRjaCAoYXV0aFBhcmFtZXRlcnMub3BlcmF0aW9uKSB7XG4gICAgICAgIGNhc2UgXCJHZXRSb2xlQ3JlZGVudGlhbHNcIjoge1xuICAgICAgICAgICAgb3B0aW9ucy5wdXNoKGNyZWF0ZVNtaXRoeUFwaU5vQXV0aEh0dHBBdXRoT3B0aW9uKGF1dGhQYXJhbWV0ZXJzKSk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICBjYXNlIFwiTGlzdEFjY291bnRSb2xlc1wiOiB7XG4gICAgICAgICAgICBvcHRpb25zLnB1c2goY3JlYXRlU21pdGh5QXBpTm9BdXRoSHR0cEF1dGhPcHRpb24oYXV0aFBhcmFtZXRlcnMpKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgXCJMaXN0QWNjb3VudHNcIjoge1xuICAgICAgICAgICAgb3B0aW9ucy5wdXNoKGNyZWF0ZVNtaXRoeUFwaU5vQXV0aEh0dHBBdXRoT3B0aW9uKGF1dGhQYXJhbWV0ZXJzKSk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICBjYXNlIFwiTG9nb3V0XCI6IHtcbiAgICAgICAgICAgIG9wdGlvbnMucHVzaChjcmVhdGVTbWl0aHlBcGlOb0F1dGhIdHRwQXV0aE9wdGlvbihhdXRoUGFyYW1ldGVycykpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgZGVmYXVsdDoge1xuICAgICAgICAgICAgb3B0aW9ucy5wdXNoKGNyZWF0ZUF3c0F1dGhTaWd2NEh0dHBBdXRoT3B0aW9uKGF1dGhQYXJhbWV0ZXJzKSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG9wdGlvbnM7XG59O1xuZXhwb3J0IGNvbnN0IHJlc29sdmVIdHRwQXV0aFNjaGVtZUNvbmZpZyA9IChjb25maWcpID0+IHtcbiAgICBjb25zdCBjb25maWdfMCA9IHJlc29sdmVBd3NTZGtTaWdWNENvbmZpZyhjb25maWcpO1xuICAgIHJldHVybiBPYmplY3QuYXNzaWduKGNvbmZpZ18wLCB7XG4gICAgICAgIGF1dGhTY2hlbWVQcmVmZXJlbmNlOiBub3JtYWxpemVQcm92aWRlcihjb25maWcuYXV0aFNjaGVtZVByZWZlcmVuY2UgPz8gW10pLFxuICAgIH0pO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/auth/httpAuthSchemeProvider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/commands/GetRoleCredentialsCommand.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/commands/GetRoleCredentialsCommand.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $Command: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command),\n/* harmony export */   GetRoleCredentialsCommand: () => (/* binding */ GetRoleCredentialsCommand)\n/* harmony export */ });\n/* harmony import */ var _smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/middleware-endpoint */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-endpoint@4.1.11/node_modules/@smithy/middleware-endpoint/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/middleware-serde */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-serde@4.0.8/node_modules/@smithy/middleware-serde/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../endpoint/EndpointParameters */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/endpoint/EndpointParameters.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/models/models_0.js\");\n/* harmony import */ var _protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../protocols/Aws_restJson1 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/protocols/Aws_restJson1.js\");\n\n\n\n\n\n\n\nclass GetRoleCredentialsCommand extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.Command\n    .classBuilder()\n    .ep(_endpoint_EndpointParameters__WEBPACK_IMPORTED_MODULE_3__.commonParams)\n    .m(function (Command, cs, config, o) {\n    return [\n        (0,_smithy_middleware_serde__WEBPACK_IMPORTED_MODULE_1__.getSerdePlugin)(config, this.serialize, this.deserialize),\n        (0,_smithy_middleware_endpoint__WEBPACK_IMPORTED_MODULE_0__.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),\n    ];\n})\n    .s(\"SWBPortalService\", \"GetRoleCredentials\", {})\n    .n(\"SSOClient\", \"GetRoleCredentialsCommand\")\n    .f(_models_models_0__WEBPACK_IMPORTED_MODULE_4__.GetRoleCredentialsRequestFilterSensitiveLog, _models_models_0__WEBPACK_IMPORTED_MODULE_4__.GetRoleCredentialsResponseFilterSensitiveLog)\n    .ser(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.se_GetRoleCredentialsCommand)\n    .de(_protocols_Aws_restJson1__WEBPACK_IMPORTED_MODULE_5__.de_GetRoleCredentialsCommand)\n    .build() {\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/commands/GetRoleCredentialsCommand.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/endpoint/EndpointParameters.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/endpoint/EndpointParameters.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   commonParams: () => (/* binding */ commonParams),\n/* harmony export */   resolveClientEndpointParameters: () => (/* binding */ resolveClientEndpointParameters)\n/* harmony export */ });\nconst resolveClientEndpointParameters = (options) => {\n    return Object.assign(options, {\n        useDualstackEndpoint: options.useDualstackEndpoint ?? false,\n        useFipsEndpoint: options.useFipsEndpoint ?? false,\n        defaultSigningName: \"awsssoportal\",\n    });\n};\nconst commonParams = {\n    UseFIPS: { type: \"builtInParams\", name: \"useFipsEndpoint\" },\n    Endpoint: { type: \"builtInParams\", name: \"endpoint\" },\n    Region: { type: \"builtInParams\", name: \"region\" },\n    UseDualStack: { type: \"builtInParams\", name: \"useDualstackEndpoint\" },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LXNzb0AzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtc3NvL2Rpc3QtZXMvZW5kcG9pbnQvRW5kcG9pbnRQYXJhbWV0ZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNPO0FBQ1AsZUFBZSxnREFBZ0Q7QUFDL0QsZ0JBQWdCLHlDQUF5QztBQUN6RCxjQUFjLHVDQUF1QztBQUNyRCxvQkFBb0IscURBQXFEO0FBQ3pFIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LXNzb0AzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtc3NvL2Rpc3QtZXMvZW5kcG9pbnQvRW5kcG9pbnRQYXJhbWV0ZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCByZXNvbHZlQ2xpZW50RW5kcG9pbnRQYXJhbWV0ZXJzID0gKG9wdGlvbnMpID0+IHtcbiAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihvcHRpb25zLCB7XG4gICAgICAgIHVzZUR1YWxzdGFja0VuZHBvaW50OiBvcHRpb25zLnVzZUR1YWxzdGFja0VuZHBvaW50ID8/IGZhbHNlLFxuICAgICAgICB1c2VGaXBzRW5kcG9pbnQ6IG9wdGlvbnMudXNlRmlwc0VuZHBvaW50ID8/IGZhbHNlLFxuICAgICAgICBkZWZhdWx0U2lnbmluZ05hbWU6IFwiYXdzc3NvcG9ydGFsXCIsXG4gICAgfSk7XG59O1xuZXhwb3J0IGNvbnN0IGNvbW1vblBhcmFtcyA9IHtcbiAgICBVc2VGSVBTOiB7IHR5cGU6IFwiYnVpbHRJblBhcmFtc1wiLCBuYW1lOiBcInVzZUZpcHNFbmRwb2ludFwiIH0sXG4gICAgRW5kcG9pbnQ6IHsgdHlwZTogXCJidWlsdEluUGFyYW1zXCIsIG5hbWU6IFwiZW5kcG9pbnRcIiB9LFxuICAgIFJlZ2lvbjogeyB0eXBlOiBcImJ1aWx0SW5QYXJhbXNcIiwgbmFtZTogXCJyZWdpb25cIiB9LFxuICAgIFVzZUR1YWxTdGFjazogeyB0eXBlOiBcImJ1aWx0SW5QYXJhbXNcIiwgbmFtZTogXCJ1c2VEdWFsc3RhY2tFbmRwb2ludFwiIH0sXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/endpoint/EndpointParameters.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/endpoint/endpointResolver.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/endpoint/endpointResolver.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultEndpointResolver: () => (/* binding */ defaultEndpointResolver)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_util_endpoints__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-endpoints@3.828.0/node_modules/@aws-sdk/util-endpoints/dist-es/index.js\");\n/* harmony import */ var _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/util-endpoints */ \"(rsc)/./node_modules/.pnpm/@smithy+util-endpoints@3.0.6/node_modules/@smithy/util-endpoints/dist-es/index.js\");\n/* harmony import */ var _ruleset__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ruleset */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/endpoint/ruleset.js\");\n\n\n\nconst cache = new _smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__.EndpointCache({\n    size: 50,\n    params: [\"Endpoint\", \"Region\", \"UseDualStack\", \"UseFIPS\"],\n});\nconst defaultEndpointResolver = (endpointParams, context = {}) => {\n    return cache.get(endpointParams, () => (0,_smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__.resolveEndpoint)(_ruleset__WEBPACK_IMPORTED_MODULE_2__.ruleSet, {\n        endpointParams: endpointParams,\n        logger: context.logger,\n    }));\n};\n_smithy_util_endpoints__WEBPACK_IMPORTED_MODULE_1__.customEndpointFunctions.aws = _aws_sdk_util_endpoints__WEBPACK_IMPORTED_MODULE_0__.awsEndpointFunctions;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LXNzb0AzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtc3NvL2Rpc3QtZXMvZW5kcG9pbnQvZW5kcG9pbnRSZXNvbHZlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStEO0FBQ2tDO0FBQzdEO0FBQ3BDLGtCQUFrQixpRUFBYTtBQUMvQjtBQUNBO0FBQ0EsQ0FBQztBQUNNLDZEQUE2RDtBQUNwRSwyQ0FBMkMsdUVBQWUsQ0FBQyw2Q0FBTztBQUNsRTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsMkVBQXVCLE9BQU8seUVBQW9CIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LXNzb0AzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtc3NvL2Rpc3QtZXMvZW5kcG9pbnQvZW5kcG9pbnRSZXNvbHZlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBhd3NFbmRwb2ludEZ1bmN0aW9ucyB9IGZyb20gXCJAYXdzLXNkay91dGlsLWVuZHBvaW50c1wiO1xuaW1wb3J0IHsgY3VzdG9tRW5kcG9pbnRGdW5jdGlvbnMsIEVuZHBvaW50Q2FjaGUsIHJlc29sdmVFbmRwb2ludCB9IGZyb20gXCJAc21pdGh5L3V0aWwtZW5kcG9pbnRzXCI7XG5pbXBvcnQgeyBydWxlU2V0IH0gZnJvbSBcIi4vcnVsZXNldFwiO1xuY29uc3QgY2FjaGUgPSBuZXcgRW5kcG9pbnRDYWNoZSh7XG4gICAgc2l6ZTogNTAsXG4gICAgcGFyYW1zOiBbXCJFbmRwb2ludFwiLCBcIlJlZ2lvblwiLCBcIlVzZUR1YWxTdGFja1wiLCBcIlVzZUZJUFNcIl0sXG59KTtcbmV4cG9ydCBjb25zdCBkZWZhdWx0RW5kcG9pbnRSZXNvbHZlciA9IChlbmRwb2ludFBhcmFtcywgY29udGV4dCA9IHt9KSA9PiB7XG4gICAgcmV0dXJuIGNhY2hlLmdldChlbmRwb2ludFBhcmFtcywgKCkgPT4gcmVzb2x2ZUVuZHBvaW50KHJ1bGVTZXQsIHtcbiAgICAgICAgZW5kcG9pbnRQYXJhbXM6IGVuZHBvaW50UGFyYW1zLFxuICAgICAgICBsb2dnZXI6IGNvbnRleHQubG9nZ2VyLFxuICAgIH0pKTtcbn07XG5jdXN0b21FbmRwb2ludEZ1bmN0aW9ucy5hd3MgPSBhd3NFbmRwb2ludEZ1bmN0aW9ucztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/endpoint/endpointResolver.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/endpoint/ruleset.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/endpoint/ruleset.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ruleSet: () => (/* binding */ ruleSet)\n/* harmony export */ });\nconst u = \"required\", v = \"fn\", w = \"argv\", x = \"ref\";\nconst a = true, b = \"isSet\", c = \"booleanEquals\", d = \"error\", e = \"endpoint\", f = \"tree\", g = \"PartitionResult\", h = \"getAttr\", i = { [u]: false, \"type\": \"String\" }, j = { [u]: true, \"default\": false, \"type\": \"Boolean\" }, k = { [x]: \"Endpoint\" }, l = { [v]: c, [w]: [{ [x]: \"UseFIPS\" }, true] }, m = { [v]: c, [w]: [{ [x]: \"UseDualStack\" }, true] }, n = {}, o = { [v]: h, [w]: [{ [x]: g }, \"supportsFIPS\"] }, p = { [x]: g }, q = { [v]: c, [w]: [true, { [v]: h, [w]: [p, \"supportsDualStack\"] }] }, r = [l], s = [m], t = [{ [x]: \"Region\" }];\nconst _data = { version: \"1.0\", parameters: { Region: i, UseDualStack: j, UseFIPS: j, Endpoint: i }, rules: [{ conditions: [{ [v]: b, [w]: [k] }], rules: [{ conditions: r, error: \"Invalid Configuration: FIPS and custom endpoint are not supported\", type: d }, { conditions: s, error: \"Invalid Configuration: Dualstack and custom endpoint are not supported\", type: d }, { endpoint: { url: k, properties: n, headers: n }, type: e }], type: f }, { conditions: [{ [v]: b, [w]: t }], rules: [{ conditions: [{ [v]: \"aws.partition\", [w]: t, assign: g }], rules: [{ conditions: [l, m], rules: [{ conditions: [{ [v]: c, [w]: [a, o] }, q], rules: [{ endpoint: { url: \"https://portal.sso-fips.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: n, headers: n }, type: e }], type: f }, { error: \"FIPS and DualStack are enabled, but this partition does not support one or both\", type: d }], type: f }, { conditions: r, rules: [{ conditions: [{ [v]: c, [w]: [o, a] }], rules: [{ conditions: [{ [v]: \"stringEquals\", [w]: [{ [v]: h, [w]: [p, \"name\"] }, \"aws-us-gov\"] }], endpoint: { url: \"https://portal.sso.{Region}.amazonaws.com\", properties: n, headers: n }, type: e }, { endpoint: { url: \"https://portal.sso-fips.{Region}.{PartitionResult#dnsSuffix}\", properties: n, headers: n }, type: e }], type: f }, { error: \"FIPS is enabled but this partition does not support FIPS\", type: d }], type: f }, { conditions: s, rules: [{ conditions: [q], rules: [{ endpoint: { url: \"https://portal.sso.{Region}.{PartitionResult#dualStackDnsSuffix}\", properties: n, headers: n }, type: e }], type: f }, { error: \"DualStack is enabled but this partition does not support DualStack\", type: d }], type: f }, { endpoint: { url: \"https://portal.sso.{Region}.{PartitionResult#dnsSuffix}\", properties: n, headers: n }, type: e }], type: f }], type: f }, { error: \"Invalid Configuration: Missing Region\", type: d }] };\nconst ruleSet = _data;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/endpoint/ruleset.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/models/SSOServiceException.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/models/SSOServiceException.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SSOServiceException: () => (/* binding */ SSOServiceException),\n/* harmony export */   __ServiceException: () => (/* reexport safe */ _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.ServiceException)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n\n\nclass SSOServiceException extends _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.ServiceException {\n    constructor(options) {\n        super(options);\n        Object.setPrototypeOf(this, SSOServiceException.prototype);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY2xpZW50LXNzb0AzLjgzMC4wL25vZGVfbW9kdWxlcy9AYXdzLXNkay9jbGllbnQtc3NvL2Rpc3QtZXMvbW9kZWxzL1NTT1NlcnZpY2VFeGNlcHRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdGO0FBQ2xEO0FBQ3ZCLGtDQUFrQyxtRUFBa0I7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bhd3Mtc2RrK2NsaWVudC1zc29AMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY2xpZW50LXNzby9kaXN0LWVzL21vZGVscy9TU09TZXJ2aWNlRXhjZXB0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFNlcnZpY2VFeGNlcHRpb24gYXMgX19TZXJ2aWNlRXhjZXB0aW9uLCB9IGZyb20gXCJAc21pdGh5L3NtaXRoeS1jbGllbnRcIjtcbmV4cG9ydCB7IF9fU2VydmljZUV4Y2VwdGlvbiB9O1xuZXhwb3J0IGNsYXNzIFNTT1NlcnZpY2VFeGNlcHRpb24gZXh0ZW5kcyBfX1NlcnZpY2VFeGNlcHRpb24ge1xuICAgIGNvbnN0cnVjdG9yKG9wdGlvbnMpIHtcbiAgICAgICAgc3VwZXIob3B0aW9ucyk7XG4gICAgICAgIE9iamVjdC5zZXRQcm90b3R5cGVPZih0aGlzLCBTU09TZXJ2aWNlRXhjZXB0aW9uLnByb3RvdHlwZSk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/models/SSOServiceException.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/models/models_0.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/models/models_0.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GetRoleCredentialsRequestFilterSensitiveLog: () => (/* binding */ GetRoleCredentialsRequestFilterSensitiveLog),\n/* harmony export */   GetRoleCredentialsResponseFilterSensitiveLog: () => (/* binding */ GetRoleCredentialsResponseFilterSensitiveLog),\n/* harmony export */   InvalidRequestException: () => (/* binding */ InvalidRequestException),\n/* harmony export */   ListAccountRolesRequestFilterSensitiveLog: () => (/* binding */ ListAccountRolesRequestFilterSensitiveLog),\n/* harmony export */   ListAccountsRequestFilterSensitiveLog: () => (/* binding */ ListAccountsRequestFilterSensitiveLog),\n/* harmony export */   LogoutRequestFilterSensitiveLog: () => (/* binding */ LogoutRequestFilterSensitiveLog),\n/* harmony export */   ResourceNotFoundException: () => (/* binding */ ResourceNotFoundException),\n/* harmony export */   RoleCredentialsFilterSensitiveLog: () => (/* binding */ RoleCredentialsFilterSensitiveLog),\n/* harmony export */   TooManyRequestsException: () => (/* binding */ TooManyRequestsException),\n/* harmony export */   UnauthorizedException: () => (/* binding */ UnauthorizedException)\n/* harmony export */ });\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _SSOServiceException__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SSOServiceException */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/models/SSOServiceException.js\");\n\n\nclass InvalidRequestException extends _SSOServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOServiceException {\n    name = \"InvalidRequestException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"InvalidRequestException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, InvalidRequestException.prototype);\n    }\n}\nclass ResourceNotFoundException extends _SSOServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOServiceException {\n    name = \"ResourceNotFoundException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"ResourceNotFoundException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, ResourceNotFoundException.prototype);\n    }\n}\nclass TooManyRequestsException extends _SSOServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOServiceException {\n    name = \"TooManyRequestsException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"TooManyRequestsException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, TooManyRequestsException.prototype);\n    }\n}\nclass UnauthorizedException extends _SSOServiceException__WEBPACK_IMPORTED_MODULE_1__.SSOServiceException {\n    name = \"UnauthorizedException\";\n    $fault = \"client\";\n    constructor(opts) {\n        super({\n            name: \"UnauthorizedException\",\n            $fault: \"client\",\n            ...opts,\n        });\n        Object.setPrototypeOf(this, UnauthorizedException.prototype);\n    }\n}\nconst GetRoleCredentialsRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.accessToken && { accessToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst RoleCredentialsFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.secretAccessKey && { secretAccessKey: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n    ...(obj.sessionToken && { sessionToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst GetRoleCredentialsResponseFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.roleCredentials && { roleCredentials: RoleCredentialsFilterSensitiveLog(obj.roleCredentials) }),\n});\nconst ListAccountRolesRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.accessToken && { accessToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst ListAccountsRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.accessToken && { accessToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\nconst LogoutRequestFilterSensitiveLog = (obj) => ({\n    ...obj,\n    ...(obj.accessToken && { accessToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_0__.SENSITIVE_STRING }),\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/models/models_0.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/protocols/Aws_restJson1.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/protocols/Aws_restJson1.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   de_GetRoleCredentialsCommand: () => (/* binding */ de_GetRoleCredentialsCommand),\n/* harmony export */   de_ListAccountRolesCommand: () => (/* binding */ de_ListAccountRolesCommand),\n/* harmony export */   de_ListAccountsCommand: () => (/* binding */ de_ListAccountsCommand),\n/* harmony export */   de_LogoutCommand: () => (/* binding */ de_LogoutCommand),\n/* harmony export */   se_GetRoleCredentialsCommand: () => (/* binding */ se_GetRoleCredentialsCommand),\n/* harmony export */   se_ListAccountRolesCommand: () => (/* binding */ se_ListAccountRolesCommand),\n/* harmony export */   se_ListAccountsCommand: () => (/* binding */ se_ListAccountsCommand),\n/* harmony export */   se_LogoutCommand: () => (/* binding */ se_LogoutCommand)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/protocols/json/parseJsonBody.js\");\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _models_models_0__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../models/models_0 */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/models/models_0.js\");\n/* harmony import */ var _models_SSOServiceException__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../models/SSOServiceException */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/models/SSOServiceException.js\");\n\n\n\n\n\nconst se_GetRoleCredentialsCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({}, _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.isSerializableHeaderValue, {\n        [_xasbt]: input[_aT],\n    });\n    b.bp(\"/federation/credentials\");\n    const query = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        [_rn]: [, (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)(input[_rN], `roleName`)],\n        [_ai]: [, (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)(input[_aI], `accountId`)],\n    });\n    let body;\n    b.m(\"GET\").h(headers).q(query).b(body);\n    return b.build();\n};\nconst se_ListAccountRolesCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({}, _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.isSerializableHeaderValue, {\n        [_xasbt]: input[_aT],\n    });\n    b.bp(\"/assignment/roles\");\n    const query = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        [_nt]: [, input[_nT]],\n        [_mr]: [() => input.maxResults !== void 0, () => input[_mR].toString()],\n        [_ai]: [, (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)(input[_aI], `accountId`)],\n    });\n    let body;\n    b.m(\"GET\").h(headers).q(query).b(body);\n    return b.build();\n};\nconst se_ListAccountsCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({}, _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.isSerializableHeaderValue, {\n        [_xasbt]: input[_aT],\n    });\n    b.bp(\"/assignment/accounts\");\n    const query = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        [_nt]: [, input[_nT]],\n        [_mr]: [() => input.maxResults !== void 0, () => input[_mR].toString()],\n    });\n    let body;\n    b.m(\"GET\").h(headers).q(query).b(body);\n    return b.build();\n};\nconst se_LogoutCommand = async (input, context) => {\n    const b = (0,_smithy_core__WEBPACK_IMPORTED_MODULE_0__.requestBuilder)(input, context);\n    const headers = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({}, _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.isSerializableHeaderValue, {\n        [_xasbt]: input[_aT],\n    });\n    b.bp(\"/logout\");\n    let body;\n    b.m(\"POST\").h(headers).b(body);\n    return b.build();\n};\nconst de_GetRoleCredentialsCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectObject)(await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_2__.parseJsonBody)(output.body, context)), \"body\");\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        roleCredentials: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nconst de_ListAccountRolesCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectObject)(await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_2__.parseJsonBody)(output.body, context)), \"body\");\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        nextToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n        roleList: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nconst de_ListAccountsCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n    });\n    const data = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectNonNull)((0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectObject)(await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_2__.parseJsonBody)(output.body, context)), \"body\");\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        accountList: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__._json,\n        nextToken: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    return contents;\n};\nconst de_LogoutCommand = async (output, context) => {\n    if (output.statusCode !== 200 && output.statusCode >= 300) {\n        return de_CommandError(output, context);\n    }\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({\n        $metadata: deserializeMetadata(output),\n    });\n    await (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.collectBody)(output.body, context);\n    return contents;\n};\nconst de_CommandError = async (output, context) => {\n    const parsedOutput = {\n        ...output,\n        body: await (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_2__.parseJsonErrorBody)(output.body, context),\n    };\n    const errorCode = (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_2__.loadRestJsonErrorCode)(output, parsedOutput.body);\n    switch (errorCode) {\n        case \"InvalidRequestException\":\n        case \"com.amazonaws.sso#InvalidRequestException\":\n            throw await de_InvalidRequestExceptionRes(parsedOutput, context);\n        case \"ResourceNotFoundException\":\n        case \"com.amazonaws.sso#ResourceNotFoundException\":\n            throw await de_ResourceNotFoundExceptionRes(parsedOutput, context);\n        case \"TooManyRequestsException\":\n        case \"com.amazonaws.sso#TooManyRequestsException\":\n            throw await de_TooManyRequestsExceptionRes(parsedOutput, context);\n        case \"UnauthorizedException\":\n        case \"com.amazonaws.sso#UnauthorizedException\":\n            throw await de_UnauthorizedExceptionRes(parsedOutput, context);\n        default:\n            const parsedBody = parsedOutput.body;\n            return throwDefaultError({\n                output,\n                parsedBody,\n                errorCode,\n            });\n    }\n};\nconst throwDefaultError = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.withBaseException)(_models_SSOServiceException__WEBPACK_IMPORTED_MODULE_3__.SSOServiceException);\nconst de_InvalidRequestExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_4__.InvalidRequestException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_ResourceNotFoundExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_4__.ResourceNotFoundException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_TooManyRequestsExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_4__.TooManyRequestsException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst de_UnauthorizedExceptionRes = async (parsedOutput, context) => {\n    const contents = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.map)({});\n    const data = parsedOutput.body;\n    const doc = (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.take)(data, {\n        message: _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.expectString,\n    });\n    Object.assign(contents, doc);\n    const exception = new _models_models_0__WEBPACK_IMPORTED_MODULE_4__.UnauthorizedException({\n        $metadata: deserializeMetadata(parsedOutput),\n        ...contents,\n    });\n    return (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.decorateServiceException)(exception, parsedOutput.body);\n};\nconst deserializeMetadata = (output) => ({\n    httpStatusCode: output.statusCode,\n    requestId: output.headers[\"x-amzn-requestid\"] ?? output.headers[\"x-amzn-request-id\"] ?? output.headers[\"x-amz-request-id\"],\n    extendedRequestId: output.headers[\"x-amz-id-2\"],\n    cfId: output.headers[\"x-amz-cf-id\"],\n});\nconst collectBodyString = (streamBody, context) => (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.collectBody)(streamBody, context).then((body) => context.utf8Encoder(body));\nconst _aI = \"accountId\";\nconst _aT = \"accessToken\";\nconst _ai = \"account_id\";\nconst _mR = \"maxResults\";\nconst _mr = \"max_result\";\nconst _nT = \"nextToken\";\nconst _nt = \"next_token\";\nconst _rN = \"roleName\";\nconst _rn = \"role_name\";\nconst _xasbt = \"x-amz-sso_bearer_token\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/protocols/Aws_restJson1.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/runtimeConfig.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/runtimeConfig.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRuntimeConfig: () => (/* binding */ getRuntimeConfig)\n/* harmony export */ });\n/* harmony import */ var _package_json__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../package.json */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/package.json\");\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/client/emitWarningIfUnsupportedVersion.js\");\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/NODE_AUTH_SCHEME_PREFERENCE_OPTIONS.js\");\n/* harmony import */ var _aws_sdk_util_user_agent_node__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/util-user-agent-node */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+util-user-agent-node@3.828.0/node_modules/@aws-sdk/util-user-agent-node/dist-es/index.js\");\n/* harmony import */ var _smithy_config_resolver__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/config-resolver */ \"(rsc)/./node_modules/.pnpm/@smithy+config-resolver@4.1.4/node_modules/@smithy/config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_hash_node__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/hash-node */ \"(rsc)/./node_modules/.pnpm/@smithy+hash-node@4.0.4/node_modules/@smithy/hash-node/dist-es/index.js\");\n/* harmony import */ var _smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/middleware-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+middleware-retry@4.1.12/node_modules/@smithy/middleware-retry/dist-es/index.js\");\n/* harmony import */ var _smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @smithy/node-config-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+node-config-provider@4.1.3/node_modules/@smithy/node-config-provider/dist-es/index.js\");\n/* harmony import */ var _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @smithy/node-http-handler */ \"(rsc)/./node_modules/.pnpm/@smithy+node-http-handler@4.0.6/node_modules/@smithy/node-http-handler/dist-es/index.js\");\n/* harmony import */ var _smithy_util_body_length_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @smithy/util-body-length-node */ \"(rsc)/./node_modules/.pnpm/@smithy+util-body-length-node@4.0.0/node_modules/@smithy/util-body-length-node/dist-es/index.js\");\n/* harmony import */ var _smithy_util_retry__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @smithy/util-retry */ \"(rsc)/./node_modules/.pnpm/@smithy+util-retry@4.0.5/node_modules/@smithy/util-retry/dist-es/index.js\");\n/* harmony import */ var _runtimeConfig_shared__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./runtimeConfig.shared */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/runtimeConfig.shared.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _smithy_util_defaults_mode_node__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @smithy/util-defaults-mode-node */ \"(rsc)/./node_modules/.pnpm/@smithy+util-defaults-mode-node@4.0.19/node_modules/@smithy/util-defaults-mode-node/dist-es/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst getRuntimeConfig = (config) => {\n    (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_8__.emitWarningIfUnsupportedVersion)(process.version);\n    const defaultsMode = (0,_smithy_util_defaults_mode_node__WEBPACK_IMPORTED_MODULE_9__.resolveDefaultsModeConfig)(config);\n    const defaultConfigProvider = () => defaultsMode().then(_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_8__.loadConfigsForDefaultMode);\n    const clientSharedValues = (0,_runtimeConfig_shared__WEBPACK_IMPORTED_MODULE_10__.getRuntimeConfig)(config);\n    (0,_aws_sdk_core__WEBPACK_IMPORTED_MODULE_11__.emitWarningIfUnsupportedVersion)(process.version);\n    const loaderConfig = {\n        profile: config?.profile,\n        logger: clientSharedValues.logger,\n    };\n    return {\n        ...clientSharedValues,\n        ...config,\n        runtime: \"node\",\n        defaultsMode,\n        authSchemePreference: config?.authSchemePreference ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_4__.loadConfig)(_aws_sdk_core__WEBPACK_IMPORTED_MODULE_12__.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS, loaderConfig),\n        bodyLengthChecker: config?.bodyLengthChecker ?? _smithy_util_body_length_node__WEBPACK_IMPORTED_MODULE_6__.calculateBodyLength,\n        defaultUserAgentProvider: config?.defaultUserAgentProvider ??\n            (0,_aws_sdk_util_user_agent_node__WEBPACK_IMPORTED_MODULE_0__.createDefaultUserAgentProvider)({ serviceId: clientSharedValues.serviceId, clientVersion: _package_json__WEBPACK_IMPORTED_MODULE_13__.version }),\n        maxAttempts: config?.maxAttempts ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_4__.loadConfig)(_smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_3__.NODE_MAX_ATTEMPT_CONFIG_OPTIONS, config),\n        region: config?.region ??\n            (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_4__.loadConfig)(_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_1__.NODE_REGION_CONFIG_OPTIONS, { ..._smithy_config_resolver__WEBPACK_IMPORTED_MODULE_1__.NODE_REGION_CONFIG_FILE_OPTIONS, ...loaderConfig }),\n        requestHandler: _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_5__.NodeHttpHandler.create(config?.requestHandler ?? defaultConfigProvider),\n        retryMode: config?.retryMode ??\n            (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_4__.loadConfig)({\n                ..._smithy_middleware_retry__WEBPACK_IMPORTED_MODULE_3__.NODE_RETRY_MODE_CONFIG_OPTIONS,\n                default: async () => (await defaultConfigProvider()).retryMode || _smithy_util_retry__WEBPACK_IMPORTED_MODULE_7__.DEFAULT_RETRY_MODE,\n            }, config),\n        sha256: config?.sha256 ?? _smithy_hash_node__WEBPACK_IMPORTED_MODULE_2__.Hash.bind(null, \"sha256\"),\n        streamCollector: config?.streamCollector ?? _smithy_node_http_handler__WEBPACK_IMPORTED_MODULE_5__.streamCollector,\n        useDualstackEndpoint: config?.useDualstackEndpoint ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_4__.loadConfig)(_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_1__.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS, loaderConfig),\n        useFipsEndpoint: config?.useFipsEndpoint ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_4__.loadConfig)(_smithy_config_resolver__WEBPACK_IMPORTED_MODULE_1__.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS, loaderConfig),\n        userAgentAppId: config?.userAgentAppId ?? (0,_smithy_node_config_provider__WEBPACK_IMPORTED_MODULE_4__.loadConfig)(_aws_sdk_util_user_agent_node__WEBPACK_IMPORTED_MODULE_0__.NODE_APP_ID_CONFIG_OPTIONS, loaderConfig),\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/runtimeConfig.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/runtimeConfig.shared.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/runtimeConfig.shared.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRuntimeConfig: () => (/* binding */ getRuntimeConfig)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @aws-sdk/core */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+core@3.826.0/node_modules/@aws-sdk/core/dist-es/submodules/httpAuthSchemes/aws_sdk/AwsSdkSigV4Signer.js\");\n/* harmony import */ var _smithy_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/core */ \"(rsc)/./node_modules/.pnpm/@smithy+core@3.5.3/node_modules/@smithy/core/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _smithy_url_parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/url-parser */ \"(rsc)/./node_modules/.pnpm/@smithy+url-parser@4.0.4/node_modules/@smithy/url-parser/dist-es/index.js\");\n/* harmony import */ var _smithy_util_base64__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @smithy/util-base64 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-base64@4.0.0/node_modules/@smithy/util-base64/dist-es/index.js\");\n/* harmony import */ var _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @smithy/util-utf8 */ \"(rsc)/./node_modules/.pnpm/@smithy+util-utf8@4.0.0/node_modules/@smithy/util-utf8/dist-es/index.js\");\n/* harmony import */ var _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./auth/httpAuthSchemeProvider */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/auth/httpAuthSchemeProvider.js\");\n/* harmony import */ var _endpoint_endpointResolver__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./endpoint/endpointResolver */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/endpoint/endpointResolver.js\");\n\n\n\n\n\n\n\n\nconst getRuntimeConfig = (config) => {\n    return {\n        apiVersion: \"2019-06-10\",\n        base64Decoder: config?.base64Decoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_3__.fromBase64,\n        base64Encoder: config?.base64Encoder ?? _smithy_util_base64__WEBPACK_IMPORTED_MODULE_3__.toBase64,\n        disableHostPrefix: config?.disableHostPrefix ?? false,\n        endpointProvider: config?.endpointProvider ?? _endpoint_endpointResolver__WEBPACK_IMPORTED_MODULE_5__.defaultEndpointResolver,\n        extensions: config?.extensions ?? [],\n        httpAuthSchemeProvider: config?.httpAuthSchemeProvider ?? _auth_httpAuthSchemeProvider__WEBPACK_IMPORTED_MODULE_6__.defaultSSOHttpAuthSchemeProvider,\n        httpAuthSchemes: config?.httpAuthSchemes ?? [\n            {\n                schemeId: \"aws.auth#sigv4\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"aws.auth#sigv4\"),\n                signer: new _aws_sdk_core__WEBPACK_IMPORTED_MODULE_7__.AwsSdkSigV4Signer(),\n            },\n            {\n                schemeId: \"smithy.api#noAuth\",\n                identityProvider: (ipc) => ipc.getIdentityProvider(\"smithy.api#noAuth\") || (async () => ({})),\n                signer: new _smithy_core__WEBPACK_IMPORTED_MODULE_0__.NoAuthSigner(),\n            },\n        ],\n        logger: config?.logger ?? new _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_1__.NoOpLogger(),\n        serviceId: config?.serviceId ?? \"SSO\",\n        urlParser: config?.urlParser ?? _smithy_url_parser__WEBPACK_IMPORTED_MODULE_2__.parseUrl,\n        utf8Decoder: config?.utf8Decoder ?? _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_4__.fromUtf8,\n        utf8Encoder: config?.utf8Encoder ?? _smithy_util_utf8__WEBPACK_IMPORTED_MODULE_4__.toUtf8,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/runtimeConfig.shared.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/runtimeExtensions.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/runtimeExtensions.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveRuntimeExtensions: () => (/* binding */ resolveRuntimeExtensions)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_region_config_resolver__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/region-config-resolver */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+region-config-resolver@3.821.0/node_modules/@aws-sdk/region-config-resolver/dist-es/index.js\");\n/* harmony import */ var _smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/protocol-http */ \"(rsc)/./node_modules/.pnpm/@smithy+protocol-http@5.1.2/node_modules/@smithy/protocol-http/dist-es/index.js\");\n/* harmony import */ var _smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/smithy-client */ \"(rsc)/./node_modules/.pnpm/@smithy+smithy-client@4.4.3/node_modules/@smithy/smithy-client/dist-es/index.js\");\n/* harmony import */ var _auth_httpAuthExtensionConfiguration__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./auth/httpAuthExtensionConfiguration */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/auth/httpAuthExtensionConfiguration.js\");\n\n\n\n\nconst resolveRuntimeExtensions = (runtimeConfig, extensions) => {\n    const extensionConfiguration = Object.assign((0,_aws_sdk_region_config_resolver__WEBPACK_IMPORTED_MODULE_0__.getAwsRegionExtensionConfiguration)(runtimeConfig), (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.getDefaultExtensionConfiguration)(runtimeConfig), (0,_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__.getHttpHandlerExtensionConfiguration)(runtimeConfig), (0,_auth_httpAuthExtensionConfiguration__WEBPACK_IMPORTED_MODULE_3__.getHttpAuthExtensionConfiguration)(runtimeConfig));\n    extensions.forEach((extension) => extension.configure(extensionConfiguration));\n    return Object.assign(runtimeConfig, (0,_aws_sdk_region_config_resolver__WEBPACK_IMPORTED_MODULE_0__.resolveAwsRegionExtensionConfiguration)(extensionConfiguration), (0,_smithy_smithy_client__WEBPACK_IMPORTED_MODULE_2__.resolveDefaultRuntimeConfig)(extensionConfiguration), (0,_smithy_protocol_http__WEBPACK_IMPORTED_MODULE_1__.resolveHttpHandlerRuntimeConfig)(extensionConfiguration), (0,_auth_httpAuthExtensionConfiguration__WEBPACK_IMPORTED_MODULE_3__.resolveHttpAuthRuntimeConfig)(extensionConfiguration));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/dist-es/runtimeExtensions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/package.json":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+client-sso@3.830.0/node_modules/@aws-sdk/client-sso/package.json ***!
  \******************************************************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"@aws-sdk/client-sso","description":"AWS SDK for JavaScript Sso Client for Node.js, Browser and React Native","version":"3.830.0","scripts":{"build":"concurrently \'yarn:build:cjs\' \'yarn:build:es\' \'yarn:build:types\'","build:cjs":"node ../../scripts/compilation/inline client-sso","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4","clean":"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo sso"},"main":"./dist-cjs/index.js","types":"./dist-types/index.d.ts","module":"./dist-es/index.js","sideEffects":false,"dependencies":{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.826.0","@aws-sdk/middleware-host-header":"3.821.0","@aws-sdk/middleware-logger":"3.821.0","@aws-sdk/middleware-recursion-detection":"3.821.0","@aws-sdk/middleware-user-agent":"3.828.0","@aws-sdk/region-config-resolver":"3.821.0","@aws-sdk/types":"3.821.0","@aws-sdk/util-endpoints":"3.828.0","@aws-sdk/util-user-agent-browser":"3.821.0","@aws-sdk/util-user-agent-node":"3.828.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.5.3","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.11","@smithy/middleware-retry":"^4.1.12","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.3","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.19","@smithy/util-defaults-mode-node":"^4.0.19","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.5","@smithy/util-utf8":"^4.0.0","tslib":"^2.6.2"},"devDependencies":{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69","concurrently":"7.0.0","downlevel-dts":"0.10.1","rimraf":"3.0.2","typescript":"~5.8.3"},"engines":{"node":">=18.0.0"},"typesVersions":{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},"files":["dist-*/**"],"author":{"name":"AWS SDK for JavaScript Team","url":"https://aws.amazon.com/javascript/"},"license":"Apache-2.0","browser":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},"homepage":"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sso","repository":{"type":"git","url":"https://github.com/aws/aws-sdk-js-v3.git","directory":"clients/client-sso"}}');

/***/ })

};
;