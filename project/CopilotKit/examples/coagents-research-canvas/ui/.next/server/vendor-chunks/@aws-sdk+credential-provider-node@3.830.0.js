"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@aws-sdk+credential-provider-node@3.830.0";
exports.ids = ["vendor-chunks/@aws-sdk+credential-provider-node@3.830.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-node@3.830.0/node_modules/@aws-sdk/credential-provider-node/dist-es/defaultProvider.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-node@3.830.0/node_modules/@aws-sdk/credential-provider-node/dist-es/defaultProvider.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   credentialsTreatedAsExpired: () => (/* binding */ credentialsTreatedAsExpired),\n/* harmony export */   credentialsWillNeedRefresh: () => (/* binding */ credentialsWillNeedRefresh),\n/* harmony export */   defaultProvider: () => (/* binding */ defaultProvider)\n/* harmony export */ });\n/* harmony import */ var _aws_sdk_credential_provider_env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @aws-sdk/credential-provider-env */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-env@3.826.0/node_modules/@aws-sdk/credential-provider-env/dist-es/index.js\");\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n/* harmony import */ var _smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @smithy/shared-ini-file-loader */ \"(rsc)/./node_modules/.pnpm/@smithy+shared-ini-file-loader@4.0.4/node_modules/@smithy/shared-ini-file-loader/dist-es/index.js\");\n/* harmony import */ var _remoteProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./remoteProvider */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-node@3.830.0/node_modules/@aws-sdk/credential-provider-node/dist-es/remoteProvider.js\");\n\n\n\n\nlet multipleCredentialSourceWarningEmitted = false;\nconst defaultProvider = (init = {}) => (0,_smithy_property_provider__WEBPACK_IMPORTED_MODULE_1__.memoize)((0,_smithy_property_provider__WEBPACK_IMPORTED_MODULE_1__.chain)(async () => {\n    const profile = init.profile ?? process.env[_smithy_shared_ini_file_loader__WEBPACK_IMPORTED_MODULE_2__.ENV_PROFILE];\n    if (profile) {\n        const envStaticCredentialsAreSet = process.env[_aws_sdk_credential_provider_env__WEBPACK_IMPORTED_MODULE_0__.ENV_KEY] && process.env[_aws_sdk_credential_provider_env__WEBPACK_IMPORTED_MODULE_0__.ENV_SECRET];\n        if (envStaticCredentialsAreSet) {\n            if (!multipleCredentialSourceWarningEmitted) {\n                const warnFn = init.logger?.warn && init.logger?.constructor?.name !== \"NoOpLogger\" ? init.logger.warn : console.warn;\n                warnFn(`@aws-sdk/credential-provider-node - defaultProvider::fromEnv WARNING:\n    Multiple credential sources detected: \n    Both AWS_PROFILE and the pair AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY static credentials are set.\n    This SDK will proceed with the AWS_PROFILE value.\n    \n    However, a future version may change this behavior to prefer the ENV static credentials.\n    Please ensure that your environment only sets either the AWS_PROFILE or the\n    AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY pair.\n`);\n                multipleCredentialSourceWarningEmitted = true;\n            }\n        }\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_1__.CredentialsProviderError(\"AWS_PROFILE is set, skipping fromEnv provider.\", {\n            logger: init.logger,\n            tryNextLink: true,\n        });\n    }\n    init.logger?.debug(\"@aws-sdk/credential-provider-node - defaultProvider::fromEnv\");\n    return (0,_aws_sdk_credential_provider_env__WEBPACK_IMPORTED_MODULE_0__.fromEnv)(init)();\n}, async () => {\n    init.logger?.debug(\"@aws-sdk/credential-provider-node - defaultProvider::fromSSO\");\n    const { ssoStartUrl, ssoAccountId, ssoRegion, ssoRoleName, ssoSession } = init;\n    if (!ssoStartUrl && !ssoAccountId && !ssoRegion && !ssoRoleName && !ssoSession) {\n        throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_1__.CredentialsProviderError(\"Skipping SSO provider in default chain (inputs do not include SSO fields).\", { logger: init.logger });\n    }\n    const { fromSSO } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@aws-sdk+credential-provider-sso@3.830.0\"), __webpack_require__.e(\"vendor-chunks/@aws-sdk+token-providers@3.830.0\")]).then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/credential-provider-sso */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-sso@3.830.0/node_modules/@aws-sdk/credential-provider-sso/dist-es/index.js\"));\n    return fromSSO(init)();\n}, async () => {\n    init.logger?.debug(\"@aws-sdk/credential-provider-node - defaultProvider::fromIni\");\n    const { fromIni } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@aws-sdk+credential-provider-ini@3.830.0\").then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/credential-provider-ini */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-ini@3.830.0/node_modules/@aws-sdk/credential-provider-ini/dist-es/index.js\"));\n    return fromIni(init)();\n}, async () => {\n    init.logger?.debug(\"@aws-sdk/credential-provider-node - defaultProvider::fromProcess\");\n    const { fromProcess } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@aws-sdk+credential-provider-process@3.826.0\").then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/credential-provider-process */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-process@3.826.0/node_modules/@aws-sdk/credential-provider-process/dist-es/index.js\"));\n    return fromProcess(init)();\n}, async () => {\n    init.logger?.debug(\"@aws-sdk/credential-provider-node - defaultProvider::fromTokenFile\");\n    const { fromTokenFile } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@aws-sdk+credential-provider-web-identity@3.830.0\").then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/credential-provider-web-identity */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-web-identity@3.830.0/node_modules/@aws-sdk/credential-provider-web-identity/dist-es/index.js\"));\n    return fromTokenFile(init)();\n}, async () => {\n    init.logger?.debug(\"@aws-sdk/credential-provider-node - defaultProvider::remoteProvider\");\n    return (await (0,_remoteProvider__WEBPACK_IMPORTED_MODULE_3__.remoteProvider)(init))();\n}, async () => {\n    throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_1__.CredentialsProviderError(\"Could not load credentials from any providers\", {\n        tryNextLink: false,\n        logger: init.logger,\n    });\n}), credentialsTreatedAsExpired, credentialsWillNeedRefresh);\nconst credentialsWillNeedRefresh = (credentials) => credentials?.expiration !== undefined;\nconst credentialsTreatedAsExpired = (credentials) => credentials?.expiration !== undefined && credentials.expiration.getTime() - Date.now() < 300000;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-node@3.830.0/node_modules/@aws-sdk/credential-provider-node/dist-es/defaultProvider.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-node@3.830.0/node_modules/@aws-sdk/credential-provider-node/dist-es/index.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-node@3.830.0/node_modules/@aws-sdk/credential-provider-node/dist-es/index.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   credentialsTreatedAsExpired: () => (/* reexport safe */ _defaultProvider__WEBPACK_IMPORTED_MODULE_0__.credentialsTreatedAsExpired),\n/* harmony export */   credentialsWillNeedRefresh: () => (/* reexport safe */ _defaultProvider__WEBPACK_IMPORTED_MODULE_0__.credentialsWillNeedRefresh),\n/* harmony export */   defaultProvider: () => (/* reexport safe */ _defaultProvider__WEBPACK_IMPORTED_MODULE_0__.defaultProvider)\n/* harmony export */ });\n/* harmony import */ var _defaultProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaultProvider */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-node@3.830.0/node_modules/@aws-sdk/credential-provider-node/dist-es/defaultProvider.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGF3cy1zZGsrY3JlZGVudGlhbC1wcm92aWRlci1ub2RlQDMuODMwLjAvbm9kZV9tb2R1bGVzL0Bhd3Mtc2RrL2NyZWRlbnRpYWwtcHJvdmlkZXItbm9kZS9kaXN0LWVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBa0MiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AYXdzLXNkaytjcmVkZW50aWFsLXByb3ZpZGVyLW5vZGVAMy44MzAuMC9ub2RlX21vZHVsZXMvQGF3cy1zZGsvY3JlZGVudGlhbC1wcm92aWRlci1ub2RlL2Rpc3QtZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vZGVmYXVsdFByb3ZpZGVyXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-node@3.830.0/node_modules/@aws-sdk/credential-provider-node/dist-es/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-node@3.830.0/node_modules/@aws-sdk/credential-provider-node/dist-es/remoteProvider.js":
/*!***********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@aws-sdk+credential-provider-node@3.830.0/node_modules/@aws-sdk/credential-provider-node/dist-es/remoteProvider.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ENV_IMDS_DISABLED: () => (/* binding */ ENV_IMDS_DISABLED),\n/* harmony export */   remoteProvider: () => (/* binding */ remoteProvider)\n/* harmony export */ });\n/* harmony import */ var _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @smithy/property-provider */ \"(rsc)/./node_modules/.pnpm/@smithy+property-provider@4.0.4/node_modules/@smithy/property-provider/dist-es/index.js\");\n\nconst ENV_IMDS_DISABLED = \"AWS_EC2_METADATA_DISABLED\";\nconst remoteProvider = async (init) => {\n    const { ENV_CMDS_FULL_URI, ENV_CMDS_RELATIVE_URI, fromContainerMetadata, fromInstanceMetadata } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@smithy+credential-provider-imds@4.0.6\").then(__webpack_require__.bind(__webpack_require__, /*! @smithy/credential-provider-imds */ \"(rsc)/./node_modules/.pnpm/@smithy+credential-provider-imds@4.0.6/node_modules/@smithy/credential-provider-imds/dist-es/index.js\"));\n    if (process.env[ENV_CMDS_RELATIVE_URI] || process.env[ENV_CMDS_FULL_URI]) {\n        init.logger?.debug(\"@aws-sdk/credential-provider-node - remoteProvider::fromHttp/fromContainerMetadata\");\n        const { fromHttp } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/@aws-sdk+credential-provider-http@3.826.0\").then(__webpack_require__.bind(__webpack_require__, /*! @aws-sdk/credential-provider-http */ \"(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-http@3.826.0/node_modules/@aws-sdk/credential-provider-http/dist-es/index.js\"));\n        return (0,_smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.chain)(fromHttp(init), fromContainerMetadata(init));\n    }\n    if (process.env[ENV_IMDS_DISABLED] && process.env[ENV_IMDS_DISABLED] !== \"false\") {\n        return async () => {\n            throw new _smithy_property_provider__WEBPACK_IMPORTED_MODULE_0__.CredentialsProviderError(\"EC2 Instance Metadata Service access disabled\", { logger: init.logger });\n        };\n    }\n    init.logger?.debug(\"@aws-sdk/credential-provider-node - remoteProvider::fromInstanceMetadata\");\n    return fromInstanceMetadata(init);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@aws-sdk+credential-provider-node@3.830.0/node_modules/@aws-sdk/credential-provider-node/dist-es/remoteProvider.js\n");

/***/ })

};
;