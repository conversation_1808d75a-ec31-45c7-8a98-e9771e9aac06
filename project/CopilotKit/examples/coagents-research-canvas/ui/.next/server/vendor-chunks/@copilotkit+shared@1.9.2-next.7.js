"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@copilotkit+shared@1.9.2-next.7";
exports.ids = ["vendor-chunks/@copilotkit+shared@1.9.2-next.7"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/index.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/index.js ***!
  \**********************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  BANNER_ERROR_NAMES: () => BANNER_ERROR_NAMES,\n  COPILOTKIT_VERSION: () => COPILOTKIT_VERSION,\n  COPILOT_CLOUD_API_URL: () => COPILOT_CLOUD_API_URL,\n  COPILOT_CLOUD_CHAT_URL: () => COPILOT_CLOUD_CHAT_URL,\n  COPILOT_CLOUD_ERROR_NAMES: () => COPILOT_CLOUD_ERROR_NAMES,\n  COPILOT_CLOUD_PUBLIC_API_KEY_HEADER: () => COPILOT_CLOUD_PUBLIC_API_KEY_HEADER,\n  COPILOT_CLOUD_VERSION: () => COPILOT_CLOUD_VERSION,\n  ConfigurationError: () => ConfigurationError,\n  CopilotKitAgentDiscoveryError: () => CopilotKitAgentDiscoveryError,\n  CopilotKitApiDiscoveryError: () => CopilotKitApiDiscoveryError,\n  CopilotKitError: () => CopilotKitError,\n  CopilotKitErrorCode: () => CopilotKitErrorCode,\n  CopilotKitLowLevelError: () => CopilotKitLowLevelError,\n  CopilotKitMisuseError: () => CopilotKitMisuseError,\n  CopilotKitRemoteEndpointDiscoveryError: () => CopilotKitRemoteEndpointDiscoveryError,\n  CopilotKitVersionMismatchError: () => CopilotKitVersionMismatchError,\n  ERROR_CONFIG: () => ERROR_CONFIG,\n  ERROR_NAMES: () => ERROR_NAMES,\n  ErrorVisibility: () => ErrorVisibility,\n  MissingPublicApiKeyError: () => MissingPublicApiKeyError,\n  ResolvedCopilotKitError: () => ResolvedCopilotKitError,\n  Severity: () => Severity,\n  TelemetryClient: () => TelemetryClient,\n  UpgradeRequiredError: () => UpgradeRequiredError,\n  actionParametersToJsonSchema: () => actionParametersToJsonSchema,\n  convertJsonSchemaToZodSchema: () => convertJsonSchemaToZodSchema,\n  dataToUUID: () => dataToUUID,\n  executeConditions: () => executeConditions,\n  getPossibleVersionMismatch: () => getPossibleVersionMismatch,\n  isMacOS: () => isMacOS,\n  isValidUUID: () => isValidUUID,\n  jsonSchemaToActionParameters: () => jsonSchemaToActionParameters,\n  parseJson: () => parseJson,\n  randomId: () => randomId,\n  randomUUID: () => randomUUID,\n  tryMap: () => tryMap\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/utils/conditions.ts\nfunction executeConditions({\n  conditions,\n  value\n}) {\n  if (!(conditions == null ? void 0 : conditions.length))\n    return true;\n  return conditions.every((condition) => executeCondition(condition, value));\n}\nfunction executeCondition(condition, value) {\n  const targetValue = condition.path ? getValueFromPath(value, condition.path) : value;\n  switch (condition.rule) {\n    case \"AND\":\n      return condition.conditions.every((c) => executeCondition(c, value));\n    case \"OR\":\n      return condition.conditions.some((c) => executeCondition(c, value));\n    case \"NOT\":\n      return !condition.conditions.every((c) => executeCondition(c, value));\n    case \"EQUALS\":\n      return targetValue === condition.value;\n    case \"NOT_EQUALS\":\n      return targetValue !== condition.value;\n    case \"GREATER_THAN\":\n      return targetValue > condition.value;\n    case \"LESS_THAN\":\n      return targetValue < condition.value;\n    case \"CONTAINS\":\n      return Array.isArray(targetValue) && targetValue.includes(condition.value);\n    case \"NOT_CONTAINS\":\n      return Array.isArray(targetValue) && !targetValue.includes(condition.value);\n    case \"MATCHES\":\n      return new RegExp(condition.value).test(String(targetValue));\n    case \"STARTS_WITH\":\n      return String(targetValue).startsWith(condition.value);\n    case \"ENDS_WITH\":\n      return String(targetValue).endsWith(condition.value);\n    case \"EXISTS\":\n      return targetValue !== void 0 && targetValue !== null;\n    case \"NOT_EXISTS\":\n      return targetValue === void 0 || targetValue === null;\n  }\n}\nfunction getValueFromPath(obj, path) {\n  return path.split(\".\").reduce((acc, part) => acc == null ? void 0 : acc[part], obj);\n}\n\n// src/utils/errors.ts\nvar import_graphql = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/.pnpm/graphql@16.9.0/node_modules/graphql/index.mjs\");\nvar Severity = /* @__PURE__ */ ((Severity2) => {\n  Severity2[\"CRITICAL\"] = \"critical\";\n  Severity2[\"WARNING\"] = \"warning\";\n  Severity2[\"INFO\"] = \"info\";\n  return Severity2;\n})(Severity || {});\nvar ErrorVisibility = /* @__PURE__ */ ((ErrorVisibility2) => {\n  ErrorVisibility2[\"BANNER\"] = \"banner\";\n  ErrorVisibility2[\"TOAST\"] = \"toast\";\n  ErrorVisibility2[\"SILENT\"] = \"silent\";\n  ErrorVisibility2[\"DEV_ONLY\"] = \"dev_only\";\n  return ErrorVisibility2;\n})(ErrorVisibility || {});\nvar ERROR_NAMES = {\n  COPILOT_ERROR: \"CopilotError\",\n  COPILOT_API_DISCOVERY_ERROR: \"CopilotApiDiscoveryError\",\n  COPILOT_REMOTE_ENDPOINT_DISCOVERY_ERROR: \"CopilotKitRemoteEndpointDiscoveryError\",\n  COPILOT_KIT_AGENT_DISCOVERY_ERROR: \"CopilotKitAgentDiscoveryError\",\n  COPILOT_KIT_LOW_LEVEL_ERROR: \"CopilotKitLowLevelError\",\n  COPILOT_KIT_VERSION_MISMATCH_ERROR: \"CopilotKitVersionMismatchError\",\n  RESOLVED_COPILOT_KIT_ERROR: \"ResolvedCopilotKitError\",\n  CONFIGURATION_ERROR: \"ConfigurationError\",\n  MISSING_PUBLIC_API_KEY_ERROR: \"MissingPublicApiKeyError\",\n  UPGRADE_REQUIRED_ERROR: \"UpgradeRequiredError\"\n};\nvar BANNER_ERROR_NAMES = [\n  ERROR_NAMES.CONFIGURATION_ERROR,\n  ERROR_NAMES.MISSING_PUBLIC_API_KEY_ERROR,\n  ERROR_NAMES.UPGRADE_REQUIRED_ERROR,\n  ERROR_NAMES.COPILOT_API_DISCOVERY_ERROR,\n  ERROR_NAMES.COPILOT_REMOTE_ENDPOINT_DISCOVERY_ERROR,\n  ERROR_NAMES.COPILOT_KIT_AGENT_DISCOVERY_ERROR\n];\nvar COPILOT_CLOUD_ERROR_NAMES = BANNER_ERROR_NAMES;\nvar CopilotKitErrorCode = /* @__PURE__ */ ((CopilotKitErrorCode2) => {\n  CopilotKitErrorCode2[\"NETWORK_ERROR\"] = \"NETWORK_ERROR\";\n  CopilotKitErrorCode2[\"NOT_FOUND\"] = \"NOT_FOUND\";\n  CopilotKitErrorCode2[\"AGENT_NOT_FOUND\"] = \"AGENT_NOT_FOUND\";\n  CopilotKitErrorCode2[\"API_NOT_FOUND\"] = \"API_NOT_FOUND\";\n  CopilotKitErrorCode2[\"REMOTE_ENDPOINT_NOT_FOUND\"] = \"REMOTE_ENDPOINT_NOT_FOUND\";\n  CopilotKitErrorCode2[\"MISUSE\"] = \"MISUSE\";\n  CopilotKitErrorCode2[\"UNKNOWN\"] = \"UNKNOWN\";\n  CopilotKitErrorCode2[\"VERSION_MISMATCH\"] = \"VERSION_MISMATCH\";\n  CopilotKitErrorCode2[\"CONFIGURATION_ERROR\"] = \"CONFIGURATION_ERROR\";\n  CopilotKitErrorCode2[\"MISSING_PUBLIC_API_KEY_ERROR\"] = \"MISSING_PUBLIC_API_KEY_ERROR\";\n  CopilotKitErrorCode2[\"UPGRADE_REQUIRED_ERROR\"] = \"UPGRADE_REQUIRED_ERROR\";\n  return CopilotKitErrorCode2;\n})(CopilotKitErrorCode || {});\nvar BASE_URL = \"https://docs.copilotkit.ai\";\nvar getSeeMoreMarkdown = (link) => `See more: [${link}](${link})`;\nvar ERROR_CONFIG = {\n  [\"NETWORK_ERROR\" /* NETWORK_ERROR */]: {\n    statusCode: 503,\n    troubleshootingUrl: `${BASE_URL}/troubleshooting/common-issues#i-am-getting-a-network-errors--api-not-found`,\n    visibility: \"toast\" /* TOAST */,\n    severity: \"info\" /* INFO */\n  },\n  [\"NOT_FOUND\" /* NOT_FOUND */]: {\n    statusCode: 404,\n    troubleshootingUrl: `${BASE_URL}/troubleshooting/common-issues#i-am-getting-a-network-errors--api-not-found`,\n    visibility: \"toast\" /* TOAST */,\n    severity: \"info\" /* INFO */\n  },\n  [\"AGENT_NOT_FOUND\" /* AGENT_NOT_FOUND */]: {\n    statusCode: 500,\n    troubleshootingUrl: `${BASE_URL}/coagents/troubleshooting/common-issues#i-am-getting-agent-not-found-error`,\n    visibility: \"banner\" /* BANNER */,\n    severity: \"critical\" /* CRITICAL */\n  },\n  [\"API_NOT_FOUND\" /* API_NOT_FOUND */]: {\n    statusCode: 404,\n    troubleshootingUrl: `${BASE_URL}/troubleshooting/common-issues#i-am-getting-a-network-errors--api-not-found`,\n    visibility: \"banner\" /* BANNER */,\n    severity: \"critical\" /* CRITICAL */\n  },\n  [\"REMOTE_ENDPOINT_NOT_FOUND\" /* REMOTE_ENDPOINT_NOT_FOUND */]: {\n    statusCode: 404,\n    troubleshootingUrl: `${BASE_URL}/troubleshooting/common-issues#i-am-getting-copilotkits-remote-endpoint-not-found-error`,\n    visibility: \"banner\" /* BANNER */,\n    severity: \"critical\" /* CRITICAL */\n  },\n  [\"MISUSE\" /* MISUSE */]: {\n    statusCode: 400,\n    troubleshootingUrl: null,\n    visibility: \"dev_only\" /* DEV_ONLY */,\n    severity: \"warning\" /* WARNING */\n  },\n  [\"UNKNOWN\" /* UNKNOWN */]: {\n    statusCode: 500,\n    visibility: \"toast\" /* TOAST */,\n    severity: \"info\" /* INFO */\n  },\n  [\"CONFIGURATION_ERROR\" /* CONFIGURATION_ERROR */]: {\n    statusCode: 400,\n    troubleshootingUrl: null,\n    severity: \"warning\" /* WARNING */,\n    visibility: \"banner\" /* BANNER */\n  },\n  [\"MISSING_PUBLIC_API_KEY_ERROR\" /* MISSING_PUBLIC_API_KEY_ERROR */]: {\n    statusCode: 400,\n    troubleshootingUrl: null,\n    severity: \"critical\" /* CRITICAL */,\n    visibility: \"banner\" /* BANNER */\n  },\n  [\"UPGRADE_REQUIRED_ERROR\" /* UPGRADE_REQUIRED_ERROR */]: {\n    statusCode: 402,\n    troubleshootingUrl: null,\n    severity: \"warning\" /* WARNING */,\n    visibility: \"banner\" /* BANNER */\n  },\n  [\"VERSION_MISMATCH\" /* VERSION_MISMATCH */]: {\n    statusCode: 400,\n    troubleshootingUrl: null,\n    visibility: \"dev_only\" /* DEV_ONLY */,\n    severity: \"info\" /* INFO */\n  }\n};\nvar CopilotKitError = class extends import_graphql.GraphQLError {\n  constructor({\n    message = \"Unknown error occurred\",\n    code,\n    severity,\n    visibility\n  }) {\n    const name = ERROR_NAMES.COPILOT_ERROR;\n    const config = ERROR_CONFIG[code];\n    const { statusCode } = config;\n    const resolvedVisibility = visibility ?? config.visibility ?? \"toast\" /* TOAST */;\n    const resolvedSeverity = severity ?? (\"severity\" in config ? config.severity : void 0);\n    super(message, {\n      extensions: {\n        name,\n        statusCode,\n        code,\n        visibility: resolvedVisibility,\n        severity: resolvedSeverity,\n        troubleshootingUrl: \"troubleshootingUrl\" in config ? config.troubleshootingUrl : null,\n        originalError: {\n          message,\n          stack: new Error().stack\n        }\n      }\n    });\n    this.code = code;\n    this.name = name;\n    this.statusCode = statusCode;\n    this.severity = resolvedSeverity;\n    this.visibility = resolvedVisibility;\n  }\n};\nvar CopilotKitMisuseError = class extends CopilotKitError {\n  constructor({\n    message,\n    code = \"MISUSE\" /* MISUSE */\n  }) {\n    const docsLink = \"troubleshootingUrl\" in ERROR_CONFIG[code] && ERROR_CONFIG[code].troubleshootingUrl ? getSeeMoreMarkdown(ERROR_CONFIG[code].troubleshootingUrl) : null;\n    const finalMessage = docsLink ? `${message}.\n\n${docsLink}` : message;\n    super({ message: finalMessage, code });\n    this.name = ERROR_NAMES.COPILOT_API_DISCOVERY_ERROR;\n  }\n};\nvar getVersionMismatchErrorMessage = ({\n  reactCoreVersion,\n  runtimeVersion,\n  runtimeClientGqlVersion\n}) => `Version mismatch detected: @copilotkit/runtime@${runtimeVersion ?? \"\"} is not compatible with @copilotkit/react-core@${reactCoreVersion} and @copilotkit/runtime-client-gql@${runtimeClientGqlVersion}. Please ensure all installed copilotkit packages are on the same version.`;\nvar CopilotKitVersionMismatchError = class extends CopilotKitError {\n  constructor({\n    reactCoreVersion,\n    runtimeVersion,\n    runtimeClientGqlVersion\n  }) {\n    const code = \"VERSION_MISMATCH\" /* VERSION_MISMATCH */;\n    super({\n      message: getVersionMismatchErrorMessage({\n        reactCoreVersion,\n        runtimeVersion,\n        runtimeClientGqlVersion\n      }),\n      code\n    });\n    this.name = ERROR_NAMES.COPILOT_KIT_VERSION_MISMATCH_ERROR;\n  }\n};\nvar CopilotKitApiDiscoveryError = class extends CopilotKitError {\n  constructor(params = {}) {\n    const url = params.url ?? \"\";\n    let operationSuffix = \"\";\n    if (url == null ? void 0 : url.includes(\"/info\"))\n      operationSuffix = `when fetching CopilotKit info`;\n    else if (url.includes(\"/actions/execute\"))\n      operationSuffix = `when attempting to execute actions.`;\n    else if (url.includes(\"/agents/state\"))\n      operationSuffix = `when attempting to get agent state.`;\n    else if (url.includes(\"/agents/execute\"))\n      operationSuffix = `when attempting to execute agent(s).`;\n    const message = params.message ?? (params.url ? `Failed to find CopilotKit API endpoint at url ${params.url} ${operationSuffix}` : `Failed to find CopilotKit API endpoint.`);\n    const code = params.code ?? \"API_NOT_FOUND\" /* API_NOT_FOUND */;\n    const errorMessage = `${message}.\n\n${getSeeMoreMarkdown(ERROR_CONFIG[code].troubleshootingUrl)}`;\n    super({ message: errorMessage, code });\n    this.name = ERROR_NAMES.COPILOT_API_DISCOVERY_ERROR;\n  }\n};\nvar CopilotKitRemoteEndpointDiscoveryError = class extends CopilotKitApiDiscoveryError {\n  constructor(params) {\n    const message = (params == null ? void 0 : params.message) ?? ((params == null ? void 0 : params.url) ? `Failed to find or contact remote endpoint at url ${params.url}` : \"Failed to find or contact remote endpoint\");\n    const code = \"REMOTE_ENDPOINT_NOT_FOUND\" /* REMOTE_ENDPOINT_NOT_FOUND */;\n    super({ message, code });\n    this.name = ERROR_NAMES.COPILOT_REMOTE_ENDPOINT_DISCOVERY_ERROR;\n  }\n};\nvar CopilotKitAgentDiscoveryError = class extends CopilotKitError {\n  constructor(params) {\n    const { agentName, availableAgents } = params;\n    const code = \"AGENT_NOT_FOUND\" /* AGENT_NOT_FOUND */;\n    let message = \"Failed to find any agents.\";\n    const configMessage = \"Please verify the agent name exists and is properly configured.\";\n    const seeMore = getSeeMoreMarkdown(ERROR_CONFIG[code].troubleshootingUrl);\n    if (availableAgents.length) {\n      message = agentName ? `Failed to find agent '${agentName}'. ${configMessage}` : `Failed to find agent. ${configMessage}`;\n      const bulletList = availableAgents.map((agent) => `\\u2022 ${agent.name} (ID: \\`${agent.id}\\`)`).join(\"\\n\");\n      message += `\n\nThe available agents are:\n\n${bulletList}\n\n${seeMore}`;\n    } else {\n      message += `\n\n${seeMore}`;\n    }\n    super({ message, code });\n    this.name = ERROR_NAMES.COPILOT_KIT_AGENT_DISCOVERY_ERROR;\n  }\n};\nvar CopilotKitLowLevelError = class extends CopilotKitError {\n  constructor({ error, url, message }) {\n    let code = \"NETWORK_ERROR\" /* NETWORK_ERROR */;\n    const errorCode = error.code;\n    const errorMessage = message ?? resolveLowLevelErrorMessage({ errorCode, url });\n    super({ message: errorMessage, code });\n    this.name = ERROR_NAMES.COPILOT_KIT_LOW_LEVEL_ERROR;\n  }\n};\nvar ResolvedCopilotKitError = class extends CopilotKitError {\n  constructor({\n    status,\n    message,\n    code,\n    isRemoteEndpoint,\n    url\n  }) {\n    let resolvedCode = code;\n    if (!resolvedCode) {\n      switch (status) {\n        case 400:\n          throw new CopilotKitApiDiscoveryError({ message, url });\n        case 404:\n          throw isRemoteEndpoint ? new CopilotKitRemoteEndpointDiscoveryError({ message, url }) : new CopilotKitApiDiscoveryError({ message, url });\n        default:\n          resolvedCode = \"UNKNOWN\" /* UNKNOWN */;\n          super({ message, code: resolvedCode, visibility: \"banner\" /* BANNER */ });\n      }\n    } else {\n      super({ message, code: resolvedCode });\n    }\n    this.name = ERROR_NAMES.RESOLVED_COPILOT_KIT_ERROR;\n  }\n};\nvar ConfigurationError = class extends CopilotKitError {\n  constructor(message) {\n    super({ message, code: \"CONFIGURATION_ERROR\" /* CONFIGURATION_ERROR */ });\n    this.name = ERROR_NAMES.CONFIGURATION_ERROR;\n    this.severity = \"warning\" /* WARNING */;\n  }\n};\nvar MissingPublicApiKeyError = class extends ConfigurationError {\n  constructor(message) {\n    super(message);\n    this.name = ERROR_NAMES.MISSING_PUBLIC_API_KEY_ERROR;\n    this.severity = \"critical\" /* CRITICAL */;\n  }\n};\nvar UpgradeRequiredError = class extends ConfigurationError {\n  constructor(message) {\n    super(message);\n    this.name = ERROR_NAMES.UPGRADE_REQUIRED_ERROR;\n    this.severity = \"warning\" /* WARNING */;\n  }\n};\nasync function getPossibleVersionMismatch({\n  runtimeVersion,\n  runtimeClientGqlVersion\n}) {\n  if (!runtimeVersion || runtimeVersion === \"\" || !runtimeClientGqlVersion)\n    return;\n  if (COPILOTKIT_VERSION !== runtimeVersion || COPILOTKIT_VERSION !== runtimeClientGqlVersion || runtimeVersion !== runtimeClientGqlVersion) {\n    return {\n      runtimeVersion,\n      runtimeClientGqlVersion,\n      reactCoreVersion: COPILOTKIT_VERSION,\n      message: getVersionMismatchErrorMessage({\n        runtimeVersion,\n        runtimeClientGqlVersion,\n        reactCoreVersion: COPILOTKIT_VERSION\n      })\n    };\n  }\n  return;\n}\nvar resolveLowLevelErrorMessage = ({ errorCode, url }) => {\n  const troubleshootingLink = ERROR_CONFIG[\"NETWORK_ERROR\" /* NETWORK_ERROR */].troubleshootingUrl;\n  const genericMessage = (description = `Failed to fetch from url ${url}.`) => `${description}.\n\nPossible reasons:\n- -The server may have an error preventing it from returning a response (Check the server logs for more info).\n- -The server might be down or unreachable\n- -There might be a network issue (e.g., DNS failure, connection timeout) \n- -The URL might be incorrect\n- -The server is not running on the specified port\n\n${getSeeMoreMarkdown(troubleshootingLink)}`;\n  if (url.includes(\"/info\"))\n    return genericMessage(`Failed to fetch CopilotKit agents/action information from url ${url}.`);\n  if (url.includes(\"/actions/execute\"))\n    return genericMessage(`Fetch call to ${url} to execute actions failed.`);\n  if (url.includes(\"/agents/state\"))\n    return genericMessage(`Fetch call to ${url} to get agent state failed.`);\n  if (url.includes(\"/agents/execute\"))\n    return genericMessage(`Fetch call to ${url} to execute agent(s) failed.`);\n  switch (errorCode) {\n    case \"ECONNREFUSED\":\n      return `Connection to ${url} was refused. Ensure the server is running and accessible.\n\n${getSeeMoreMarkdown(troubleshootingLink)}`;\n    case \"ENOTFOUND\":\n      return `The server on ${url} could not be found. Check the URL or your network configuration.\n\n${getSeeMoreMarkdown(ERROR_CONFIG[\"NOT_FOUND\" /* NOT_FOUND */].troubleshootingUrl)}`;\n    case \"ETIMEDOUT\":\n      return `The connection to ${url} timed out. The server might be overloaded or taking too long to respond.\n\n${getSeeMoreMarkdown(troubleshootingLink)}`;\n    default:\n      return;\n  }\n};\n\n// src/utils/json-schema.ts\nvar import_zod = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/.pnpm/zod@3.23.8/node_modules/zod/lib/index.js\");\nfunction actionParametersToJsonSchema(actionParameters) {\n  let parameters = {};\n  for (let parameter of actionParameters || []) {\n    parameters[parameter.name] = convertAttribute(parameter);\n  }\n  let requiredParameterNames = [];\n  for (let arg of actionParameters || []) {\n    if (arg.required !== false) {\n      requiredParameterNames.push(arg.name);\n    }\n  }\n  return {\n    type: \"object\",\n    properties: parameters,\n    required: requiredParameterNames\n  };\n}\nfunction jsonSchemaToActionParameters(jsonSchema) {\n  if (jsonSchema.type !== \"object\" || !jsonSchema.properties) {\n    return [];\n  }\n  const parameters = [];\n  const requiredFields = jsonSchema.required || [];\n  for (const [name, schema] of Object.entries(jsonSchema.properties)) {\n    const parameter = convertJsonSchemaToParameter(name, schema, requiredFields.includes(name));\n    parameters.push(parameter);\n  }\n  return parameters;\n}\nfunction convertJsonSchemaToParameter(name, schema, isRequired) {\n  const baseParameter = {\n    name,\n    description: schema.description\n  };\n  if (!isRequired) {\n    baseParameter.required = false;\n  }\n  switch (schema.type) {\n    case \"string\":\n      return {\n        ...baseParameter,\n        type: \"string\",\n        ...schema.enum && { enum: schema.enum }\n      };\n    case \"number\":\n    case \"boolean\":\n      return {\n        ...baseParameter,\n        type: schema.type\n      };\n    case \"object\":\n      if (schema.properties) {\n        const attributes = [];\n        const requiredFields = schema.required || [];\n        for (const [propName, propSchema] of Object.entries(schema.properties)) {\n          attributes.push(\n            convertJsonSchemaToParameter(propName, propSchema, requiredFields.includes(propName))\n          );\n        }\n        return {\n          ...baseParameter,\n          type: \"object\",\n          attributes\n        };\n      }\n      return {\n        ...baseParameter,\n        type: \"object\"\n      };\n    case \"array\":\n      if (schema.items.type === \"object\" && \"properties\" in schema.items) {\n        const attributes = [];\n        const requiredFields = schema.items.required || [];\n        for (const [propName, propSchema] of Object.entries(schema.items.properties || {})) {\n          attributes.push(\n            convertJsonSchemaToParameter(propName, propSchema, requiredFields.includes(propName))\n          );\n        }\n        return {\n          ...baseParameter,\n          type: \"object[]\",\n          attributes\n        };\n      } else if (schema.items.type === \"array\") {\n        throw new Error(\"Nested arrays are not supported\");\n      } else {\n        return {\n          ...baseParameter,\n          type: `${schema.items.type}[]`\n        };\n      }\n    default:\n      return {\n        ...baseParameter,\n        type: \"string\"\n      };\n  }\n}\nfunction convertAttribute(attribute) {\n  var _a, _b, _c;\n  switch (attribute.type) {\n    case \"string\":\n      return {\n        type: \"string\",\n        description: attribute.description,\n        ...attribute.enum && { enum: attribute.enum }\n      };\n    case \"number\":\n    case \"boolean\":\n      return {\n        type: attribute.type,\n        description: attribute.description\n      };\n    case \"object\":\n    case \"object[]\":\n      const properties = (_a = attribute.attributes) == null ? void 0 : _a.reduce(\n        (acc, attr) => {\n          acc[attr.name] = convertAttribute(attr);\n          return acc;\n        },\n        {}\n      );\n      const required = (_b = attribute.attributes) == null ? void 0 : _b.filter((attr) => attr.required !== false).map((attr) => attr.name);\n      if (attribute.type === \"object[]\") {\n        return {\n          type: \"array\",\n          items: {\n            type: \"object\",\n            ...properties && { properties },\n            ...required && required.length > 0 && { required }\n          },\n          description: attribute.description\n        };\n      }\n      return {\n        type: \"object\",\n        description: attribute.description,\n        ...properties && { properties },\n        ...required && required.length > 0 && { required }\n      };\n    default:\n      if ((_c = attribute.type) == null ? void 0 : _c.endsWith(\"[]\")) {\n        const itemType = attribute.type.slice(0, -2);\n        return {\n          type: \"array\",\n          items: { type: itemType },\n          description: attribute.description\n        };\n      }\n      return {\n        type: \"string\",\n        description: attribute.description\n      };\n  }\n}\nfunction convertJsonSchemaToZodSchema(jsonSchema, required) {\n  if (jsonSchema.type === \"object\") {\n    const spec = {};\n    if (!jsonSchema.properties || !Object.keys(jsonSchema.properties).length) {\n      return !required ? import_zod.z.object(spec).optional() : import_zod.z.object(spec);\n    }\n    for (const [key, value] of Object.entries(jsonSchema.properties)) {\n      spec[key] = convertJsonSchemaToZodSchema(\n        value,\n        jsonSchema.required ? jsonSchema.required.includes(key) : false\n      );\n    }\n    let schema = import_zod.z.object(spec).describe(jsonSchema.description);\n    return required ? schema : schema.optional();\n  } else if (jsonSchema.type === \"string\") {\n    let schema = import_zod.z.string().describe(jsonSchema.description);\n    return required ? schema : schema.optional();\n  } else if (jsonSchema.type === \"number\") {\n    let schema = import_zod.z.number().describe(jsonSchema.description);\n    return required ? schema : schema.optional();\n  } else if (jsonSchema.type === \"boolean\") {\n    let schema = import_zod.z.boolean().describe(jsonSchema.description);\n    return required ? schema : schema.optional();\n  } else if (jsonSchema.type === \"array\") {\n    let itemSchema = convertJsonSchemaToZodSchema(jsonSchema.items, true);\n    let schema = import_zod.z.array(itemSchema).describe(jsonSchema.description);\n    return required ? schema : schema.optional();\n  }\n  throw new Error(\"Invalid JSON schema\");\n}\n\n// src/utils/random-id.ts\nvar import_uuid = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@10.0.0/node_modules/uuid/dist/esm-node/index.js\");\nfunction randomId() {\n  return \"ck-\" + (0, import_uuid.v4)();\n}\nfunction randomUUID() {\n  return (0, import_uuid.v4)();\n}\nfunction dataToUUID(input, namespace) {\n  const BASE_NAMESPACE = \"e4b01160-ff74-4c6e-9b27-d53cd930fe8e\";\n  const boundNamespace = namespace ? (0, import_uuid.v5)(namespace, BASE_NAMESPACE) : BASE_NAMESPACE;\n  return (0, import_uuid.v5)(input, boundNamespace);\n}\nfunction isValidUUID(uuid) {\n  return (0, import_uuid.validate)(uuid);\n}\n\n// src/utils/index.ts\nfunction parseJson(json, fallback = \"unset\") {\n  try {\n    return JSON.parse(json);\n  } catch (e) {\n    return fallback === \"unset\" ? null : fallback;\n  }\n}\nfunction tryMap(items, callback) {\n  return items.reduce((acc, item, index, array) => {\n    try {\n      acc.push(callback(item, index, array));\n    } catch (error) {\n      console.error(error);\n    }\n    return acc;\n  }, []);\n}\nfunction isMacOS() {\n  return /Mac|iMac|Macintosh/i.test(navigator.userAgent);\n}\n\n// src/constants/index.ts\nvar COPILOT_CLOUD_API_URL = \"https://api.cloud.copilotkit.ai\";\nvar COPILOT_CLOUD_VERSION = \"v1\";\nvar COPILOT_CLOUD_CHAT_URL = `${COPILOT_CLOUD_API_URL}/copilotkit/${COPILOT_CLOUD_VERSION}`;\nvar COPILOT_CLOUD_PUBLIC_API_KEY_HEADER = \"X-CopilotCloud-Public-Api-Key\";\n\n// src/telemetry/telemetry-client.ts\nvar import_analytics_node = __webpack_require__(/*! @segment/analytics-node */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-node@2.2.0/node_modules/@segment/analytics-node/dist/esm/index.js\");\n\n// src/telemetry/utils.ts\nvar import_chalk = __toESM(__webpack_require__(/*! chalk */ \"(rsc)/./node_modules/.pnpm/chalk@4.1.2/node_modules/chalk/source/index.js\"));\nfunction flattenObject(obj, parentKey = \"\", res = {}) {\n  for (let key in obj) {\n    const propName = parentKey ? `${parentKey}.${key}` : key;\n    if (typeof obj[key] === \"object\" && obj[key] !== null) {\n      flattenObject(obj[key], propName, res);\n    } else {\n      res[propName] = obj[key];\n    }\n  }\n  return res;\n}\n\n// src/telemetry/telemetry-client.ts\nvar import_uuid2 = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@10.0.0/node_modules/uuid/dist/esm-node/index.js\");\nvar TelemetryClient = class {\n  constructor({\n    packageName,\n    packageVersion,\n    telemetryDisabled,\n    telemetryBaseUrl,\n    sampleRate\n  }) {\n    this.globalProperties = {};\n    this.cloudConfiguration = null;\n    this.telemetryDisabled = false;\n    this.sampleRate = 0.05;\n    this.anonymousId = `anon_${(0, import_uuid2.v4)()}`;\n    this.packageName = packageName;\n    this.packageVersion = packageVersion;\n    this.telemetryDisabled = telemetryDisabled || process.env.COPILOTKIT_TELEMETRY_DISABLED === \"true\" || process.env.COPILOTKIT_TELEMETRY_DISABLED === \"1\" || process.env.DO_NOT_TRACK === \"true\" || process.env.DO_NOT_TRACK === \"1\";\n    if (this.telemetryDisabled) {\n      return;\n    }\n    this.setSampleRate(sampleRate);\n    const writeKey = process.env.COPILOTKIT_SEGMENT_WRITE_KEY || \"n7XAZtQCGS2v1vvBy3LgBCv2h3Y8whja\";\n    this.segment = new import_analytics_node.Analytics({\n      writeKey\n    });\n    this.setGlobalProperties({\n      \"copilotkit.package.name\": packageName,\n      \"copilotkit.package.version\": packageVersion\n    });\n  }\n  shouldSendEvent() {\n    const randomNumber = Math.random();\n    return randomNumber < this.sampleRate;\n  }\n  async capture(event, properties) {\n    if (!this.shouldSendEvent() || !this.segment) {\n      return;\n    }\n    const flattenedProperties = flattenObject(properties);\n    const propertiesWithGlobal = {\n      ...this.globalProperties,\n      ...flattenedProperties\n    };\n    const orderedPropertiesWithGlobal = Object.keys(propertiesWithGlobal).sort().reduce(\n      (obj, key) => {\n        obj[key] = propertiesWithGlobal[key];\n        return obj;\n      },\n      {}\n    );\n    this.segment.track({\n      anonymousId: this.anonymousId,\n      event,\n      properties: { ...orderedPropertiesWithGlobal }\n    });\n  }\n  setGlobalProperties(properties) {\n    const flattenedProperties = flattenObject(properties);\n    this.globalProperties = { ...this.globalProperties, ...flattenedProperties };\n  }\n  setCloudConfiguration(properties) {\n    this.cloudConfiguration = properties;\n    this.setGlobalProperties({\n      cloud: {\n        publicApiKey: properties.publicApiKey,\n        baseUrl: properties.baseUrl\n      }\n    });\n  }\n  setSampleRate(sampleRate) {\n    let _sampleRate;\n    _sampleRate = sampleRate ?? 0.05;\n    if (process.env.COPILOTKIT_TELEMETRY_SAMPLE_RATE) {\n      _sampleRate = parseFloat(process.env.COPILOTKIT_TELEMETRY_SAMPLE_RATE);\n    }\n    if (_sampleRate < 0 || _sampleRate > 1) {\n      throw new Error(\"Sample rate must be between 0 and 1\");\n    }\n    this.sampleRate = _sampleRate;\n    this.setGlobalProperties({\n      sampleRate: this.sampleRate,\n      sampleRateAdjustmentFactor: 1 - this.sampleRate,\n      sampleWeight: 1 / this.sampleRate\n    });\n  }\n};\n\n// package.json\nvar version = \"1.9.2-next.7\";\n\n// src/index.ts\nvar COPILOTKIT_VERSION = version;\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-2KQ6HEWZ.mjs":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-2KQ6HEWZ.mjs ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   actionParametersToJsonSchema: () => (/* binding */ actionParametersToJsonSchema),\n/* harmony export */   convertJsonSchemaToZodSchema: () => (/* binding */ convertJsonSchemaToZodSchema),\n/* harmony export */   jsonSchemaToActionParameters: () => (/* binding */ jsonSchemaToActionParameters)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/.pnpm/zod@3.23.8/node_modules/zod/lib/index.mjs\");\n// src/utils/json-schema.ts\n\nfunction actionParametersToJsonSchema(actionParameters) {\n  let parameters = {};\n  for (let parameter of actionParameters || []) {\n    parameters[parameter.name] = convertAttribute(parameter);\n  }\n  let requiredParameterNames = [];\n  for (let arg of actionParameters || []) {\n    if (arg.required !== false) {\n      requiredParameterNames.push(arg.name);\n    }\n  }\n  return {\n    type: \"object\",\n    properties: parameters,\n    required: requiredParameterNames\n  };\n}\nfunction jsonSchemaToActionParameters(jsonSchema) {\n  if (jsonSchema.type !== \"object\" || !jsonSchema.properties) {\n    return [];\n  }\n  const parameters = [];\n  const requiredFields = jsonSchema.required || [];\n  for (const [name, schema] of Object.entries(jsonSchema.properties)) {\n    const parameter = convertJsonSchemaToParameter(name, schema, requiredFields.includes(name));\n    parameters.push(parameter);\n  }\n  return parameters;\n}\nfunction convertJsonSchemaToParameter(name, schema, isRequired) {\n  const baseParameter = {\n    name,\n    description: schema.description\n  };\n  if (!isRequired) {\n    baseParameter.required = false;\n  }\n  switch (schema.type) {\n    case \"string\":\n      return {\n        ...baseParameter,\n        type: \"string\",\n        ...schema.enum && { enum: schema.enum }\n      };\n    case \"number\":\n    case \"boolean\":\n      return {\n        ...baseParameter,\n        type: schema.type\n      };\n    case \"object\":\n      if (schema.properties) {\n        const attributes = [];\n        const requiredFields = schema.required || [];\n        for (const [propName, propSchema] of Object.entries(schema.properties)) {\n          attributes.push(\n            convertJsonSchemaToParameter(propName, propSchema, requiredFields.includes(propName))\n          );\n        }\n        return {\n          ...baseParameter,\n          type: \"object\",\n          attributes\n        };\n      }\n      return {\n        ...baseParameter,\n        type: \"object\"\n      };\n    case \"array\":\n      if (schema.items.type === \"object\" && \"properties\" in schema.items) {\n        const attributes = [];\n        const requiredFields = schema.items.required || [];\n        for (const [propName, propSchema] of Object.entries(schema.items.properties || {})) {\n          attributes.push(\n            convertJsonSchemaToParameter(propName, propSchema, requiredFields.includes(propName))\n          );\n        }\n        return {\n          ...baseParameter,\n          type: \"object[]\",\n          attributes\n        };\n      } else if (schema.items.type === \"array\") {\n        throw new Error(\"Nested arrays are not supported\");\n      } else {\n        return {\n          ...baseParameter,\n          type: `${schema.items.type}[]`\n        };\n      }\n    default:\n      return {\n        ...baseParameter,\n        type: \"string\"\n      };\n  }\n}\nfunction convertAttribute(attribute) {\n  var _a, _b, _c;\n  switch (attribute.type) {\n    case \"string\":\n      return {\n        type: \"string\",\n        description: attribute.description,\n        ...attribute.enum && { enum: attribute.enum }\n      };\n    case \"number\":\n    case \"boolean\":\n      return {\n        type: attribute.type,\n        description: attribute.description\n      };\n    case \"object\":\n    case \"object[]\":\n      const properties = (_a = attribute.attributes) == null ? void 0 : _a.reduce(\n        (acc, attr) => {\n          acc[attr.name] = convertAttribute(attr);\n          return acc;\n        },\n        {}\n      );\n      const required = (_b = attribute.attributes) == null ? void 0 : _b.filter((attr) => attr.required !== false).map((attr) => attr.name);\n      if (attribute.type === \"object[]\") {\n        return {\n          type: \"array\",\n          items: {\n            type: \"object\",\n            ...properties && { properties },\n            ...required && required.length > 0 && { required }\n          },\n          description: attribute.description\n        };\n      }\n      return {\n        type: \"object\",\n        description: attribute.description,\n        ...properties && { properties },\n        ...required && required.length > 0 && { required }\n      };\n    default:\n      if ((_c = attribute.type) == null ? void 0 : _c.endsWith(\"[]\")) {\n        const itemType = attribute.type.slice(0, -2);\n        return {\n          type: \"array\",\n          items: { type: itemType },\n          description: attribute.description\n        };\n      }\n      return {\n        type: \"string\",\n        description: attribute.description\n      };\n  }\n}\nfunction convertJsonSchemaToZodSchema(jsonSchema, required) {\n  if (jsonSchema.type === \"object\") {\n    const spec = {};\n    if (!jsonSchema.properties || !Object.keys(jsonSchema.properties).length) {\n      return !required ? zod__WEBPACK_IMPORTED_MODULE_0__.z.object(spec).optional() : zod__WEBPACK_IMPORTED_MODULE_0__.z.object(spec);\n    }\n    for (const [key, value] of Object.entries(jsonSchema.properties)) {\n      spec[key] = convertJsonSchemaToZodSchema(\n        value,\n        jsonSchema.required ? jsonSchema.required.includes(key) : false\n      );\n    }\n    let schema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object(spec).describe(jsonSchema.description);\n    return required ? schema : schema.optional();\n  } else if (jsonSchema.type === \"string\") {\n    let schema = zod__WEBPACK_IMPORTED_MODULE_0__.z.string().describe(jsonSchema.description);\n    return required ? schema : schema.optional();\n  } else if (jsonSchema.type === \"number\") {\n    let schema = zod__WEBPACK_IMPORTED_MODULE_0__.z.number().describe(jsonSchema.description);\n    return required ? schema : schema.optional();\n  } else if (jsonSchema.type === \"boolean\") {\n    let schema = zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().describe(jsonSchema.description);\n    return required ? schema : schema.optional();\n  } else if (jsonSchema.type === \"array\") {\n    let itemSchema = convertJsonSchemaToZodSchema(jsonSchema.items, true);\n    let schema = zod__WEBPACK_IMPORTED_MODULE_0__.z.array(itemSchema).describe(jsonSchema.description);\n    return required ? schema : schema.optional();\n  }\n  throw new Error(\"Invalid JSON schema\");\n}\n\n\n//# sourceMappingURL=chunk-2KQ6HEWZ.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-2KQ6HEWZ.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-GYZIHHE6.mjs":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-GYZIHHE6.mjs ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COPILOT_CLOUD_API_URL: () => (/* binding */ COPILOT_CLOUD_API_URL),\n/* harmony export */   COPILOT_CLOUD_CHAT_URL: () => (/* binding */ COPILOT_CLOUD_CHAT_URL),\n/* harmony export */   COPILOT_CLOUD_PUBLIC_API_KEY_HEADER: () => (/* binding */ COPILOT_CLOUD_PUBLIC_API_KEY_HEADER),\n/* harmony export */   COPILOT_CLOUD_VERSION: () => (/* binding */ COPILOT_CLOUD_VERSION)\n/* harmony export */ });\n// src/constants/index.ts\nvar COPILOT_CLOUD_API_URL = \"https://api.cloud.copilotkit.ai\";\nvar COPILOT_CLOUD_VERSION = \"v1\";\nvar COPILOT_CLOUD_CHAT_URL = `${COPILOT_CLOUD_API_URL}/copilotkit/${COPILOT_CLOUD_VERSION}`;\nvar COPILOT_CLOUD_PUBLIC_API_KEY_HEADER = \"X-CopilotCloud-Public-Api-Key\";\n\n\n//# sourceMappingURL=chunk-GYZIHHE6.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrc2hhcmVkQDEuOS4yLW5leHQuNy9ub2RlX21vZHVsZXMvQGNvcGlsb3RraXQvc2hhcmVkL2Rpc3QvY2h1bmstR1laSUhIRTYubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0Msc0JBQXNCLGNBQWMsc0JBQXNCO0FBQzFGOztBQU9FO0FBQ0YiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9AY29waWxvdGtpdCtzaGFyZWRAMS45LjItbmV4dC43L25vZGVfbW9kdWxlcy9AY29waWxvdGtpdC9zaGFyZWQvZGlzdC9jaHVuay1HWVpJSEhFNi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2NvbnN0YW50cy9pbmRleC50c1xudmFyIENPUElMT1RfQ0xPVURfQVBJX1VSTCA9IFwiaHR0cHM6Ly9hcGkuY2xvdWQuY29waWxvdGtpdC5haVwiO1xudmFyIENPUElMT1RfQ0xPVURfVkVSU0lPTiA9IFwidjFcIjtcbnZhciBDT1BJTE9UX0NMT1VEX0NIQVRfVVJMID0gYCR7Q09QSUxPVF9DTE9VRF9BUElfVVJMfS9jb3BpbG90a2l0LyR7Q09QSUxPVF9DTE9VRF9WRVJTSU9OfWA7XG52YXIgQ09QSUxPVF9DTE9VRF9QVUJMSUNfQVBJX0tFWV9IRUFERVIgPSBcIlgtQ29waWxvdENsb3VkLVB1YmxpYy1BcGktS2V5XCI7XG5cbmV4cG9ydCB7XG4gIENPUElMT1RfQ0xPVURfQVBJX1VSTCxcbiAgQ09QSUxPVF9DTE9VRF9WRVJTSU9OLFxuICBDT1BJTE9UX0NMT1VEX0NIQVRfVVJMLFxuICBDT1BJTE9UX0NMT1VEX1BVQkxJQ19BUElfS0VZX0hFQURFUlxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNodW5rLUdZWklISEU2Lm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-GYZIHHE6.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BANNER_ERROR_NAMES: () => (/* binding */ BANNER_ERROR_NAMES),\n/* harmony export */   COPILOTKIT_VERSION: () => (/* binding */ COPILOTKIT_VERSION),\n/* harmony export */   COPILOT_CLOUD_ERROR_NAMES: () => (/* binding */ COPILOT_CLOUD_ERROR_NAMES),\n/* harmony export */   ConfigurationError: () => (/* binding */ ConfigurationError),\n/* harmony export */   CopilotKitAgentDiscoveryError: () => (/* binding */ CopilotKitAgentDiscoveryError),\n/* harmony export */   CopilotKitApiDiscoveryError: () => (/* binding */ CopilotKitApiDiscoveryError),\n/* harmony export */   CopilotKitError: () => (/* binding */ CopilotKitError),\n/* harmony export */   CopilotKitErrorCode: () => (/* binding */ CopilotKitErrorCode),\n/* harmony export */   CopilotKitLowLevelError: () => (/* binding */ CopilotKitLowLevelError),\n/* harmony export */   CopilotKitMisuseError: () => (/* binding */ CopilotKitMisuseError),\n/* harmony export */   CopilotKitRemoteEndpointDiscoveryError: () => (/* binding */ CopilotKitRemoteEndpointDiscoveryError),\n/* harmony export */   CopilotKitVersionMismatchError: () => (/* binding */ CopilotKitVersionMismatchError),\n/* harmony export */   ERROR_CONFIG: () => (/* binding */ ERROR_CONFIG),\n/* harmony export */   ERROR_NAMES: () => (/* binding */ ERROR_NAMES),\n/* harmony export */   ErrorVisibility: () => (/* binding */ ErrorVisibility),\n/* harmony export */   MissingPublicApiKeyError: () => (/* binding */ MissingPublicApiKeyError),\n/* harmony export */   ResolvedCopilotKitError: () => (/* binding */ ResolvedCopilotKitError),\n/* harmony export */   Severity: () => (/* binding */ Severity),\n/* harmony export */   UpgradeRequiredError: () => (/* binding */ UpgradeRequiredError),\n/* harmony export */   getPossibleVersionMismatch: () => (/* binding */ getPossibleVersionMismatch),\n/* harmony export */   isMacOS: () => (/* binding */ isMacOS),\n/* harmony export */   parseJson: () => (/* binding */ parseJson),\n/* harmony export */   tryMap: () => (/* binding */ tryMap)\n/* harmony export */ });\n/* harmony import */ var graphql__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! graphql */ \"(ssr)/./node_modules/.pnpm/graphql@16.9.0/node_modules/graphql/error/GraphQLError.mjs\");\n// src/utils/errors.ts\n\n\n// src/utils/index.ts\nfunction parseJson(json, fallback = \"unset\") {\n  try {\n    return JSON.parse(json);\n  } catch (e) {\n    return fallback === \"unset\" ? null : fallback;\n  }\n}\nfunction tryMap(items, callback) {\n  return items.reduce((acc, item, index, array) => {\n    try {\n      acc.push(callback(item, index, array));\n    } catch (error) {\n      console.error(error);\n    }\n    return acc;\n  }, []);\n}\nfunction isMacOS() {\n  return /Mac|iMac|Macintosh/i.test(navigator.userAgent);\n}\n\n// package.json\nvar version = \"1.9.2-next.7\";\n\n// src/index.ts\nvar COPILOTKIT_VERSION = version;\n\n// src/utils/errors.ts\nvar Severity = /* @__PURE__ */ ((Severity2) => {\n  Severity2[\"CRITICAL\"] = \"critical\";\n  Severity2[\"WARNING\"] = \"warning\";\n  Severity2[\"INFO\"] = \"info\";\n  return Severity2;\n})(Severity || {});\nvar ErrorVisibility = /* @__PURE__ */ ((ErrorVisibility2) => {\n  ErrorVisibility2[\"BANNER\"] = \"banner\";\n  ErrorVisibility2[\"TOAST\"] = \"toast\";\n  ErrorVisibility2[\"SILENT\"] = \"silent\";\n  ErrorVisibility2[\"DEV_ONLY\"] = \"dev_only\";\n  return ErrorVisibility2;\n})(ErrorVisibility || {});\nvar ERROR_NAMES = {\n  COPILOT_ERROR: \"CopilotError\",\n  COPILOT_API_DISCOVERY_ERROR: \"CopilotApiDiscoveryError\",\n  COPILOT_REMOTE_ENDPOINT_DISCOVERY_ERROR: \"CopilotKitRemoteEndpointDiscoveryError\",\n  COPILOT_KIT_AGENT_DISCOVERY_ERROR: \"CopilotKitAgentDiscoveryError\",\n  COPILOT_KIT_LOW_LEVEL_ERROR: \"CopilotKitLowLevelError\",\n  COPILOT_KIT_VERSION_MISMATCH_ERROR: \"CopilotKitVersionMismatchError\",\n  RESOLVED_COPILOT_KIT_ERROR: \"ResolvedCopilotKitError\",\n  CONFIGURATION_ERROR: \"ConfigurationError\",\n  MISSING_PUBLIC_API_KEY_ERROR: \"MissingPublicApiKeyError\",\n  UPGRADE_REQUIRED_ERROR: \"UpgradeRequiredError\"\n};\nvar BANNER_ERROR_NAMES = [\n  ERROR_NAMES.CONFIGURATION_ERROR,\n  ERROR_NAMES.MISSING_PUBLIC_API_KEY_ERROR,\n  ERROR_NAMES.UPGRADE_REQUIRED_ERROR,\n  ERROR_NAMES.COPILOT_API_DISCOVERY_ERROR,\n  ERROR_NAMES.COPILOT_REMOTE_ENDPOINT_DISCOVERY_ERROR,\n  ERROR_NAMES.COPILOT_KIT_AGENT_DISCOVERY_ERROR\n];\nvar COPILOT_CLOUD_ERROR_NAMES = BANNER_ERROR_NAMES;\nvar CopilotKitErrorCode = /* @__PURE__ */ ((CopilotKitErrorCode2) => {\n  CopilotKitErrorCode2[\"NETWORK_ERROR\"] = \"NETWORK_ERROR\";\n  CopilotKitErrorCode2[\"NOT_FOUND\"] = \"NOT_FOUND\";\n  CopilotKitErrorCode2[\"AGENT_NOT_FOUND\"] = \"AGENT_NOT_FOUND\";\n  CopilotKitErrorCode2[\"API_NOT_FOUND\"] = \"API_NOT_FOUND\";\n  CopilotKitErrorCode2[\"REMOTE_ENDPOINT_NOT_FOUND\"] = \"REMOTE_ENDPOINT_NOT_FOUND\";\n  CopilotKitErrorCode2[\"MISUSE\"] = \"MISUSE\";\n  CopilotKitErrorCode2[\"UNKNOWN\"] = \"UNKNOWN\";\n  CopilotKitErrorCode2[\"VERSION_MISMATCH\"] = \"VERSION_MISMATCH\";\n  CopilotKitErrorCode2[\"CONFIGURATION_ERROR\"] = \"CONFIGURATION_ERROR\";\n  CopilotKitErrorCode2[\"MISSING_PUBLIC_API_KEY_ERROR\"] = \"MISSING_PUBLIC_API_KEY_ERROR\";\n  CopilotKitErrorCode2[\"UPGRADE_REQUIRED_ERROR\"] = \"UPGRADE_REQUIRED_ERROR\";\n  return CopilotKitErrorCode2;\n})(CopilotKitErrorCode || {});\nvar BASE_URL = \"https://docs.copilotkit.ai\";\nvar getSeeMoreMarkdown = (link) => `See more: [${link}](${link})`;\nvar ERROR_CONFIG = {\n  [\"NETWORK_ERROR\" /* NETWORK_ERROR */]: {\n    statusCode: 503,\n    troubleshootingUrl: `${BASE_URL}/troubleshooting/common-issues#i-am-getting-a-network-errors--api-not-found`,\n    visibility: \"toast\" /* TOAST */,\n    severity: \"info\" /* INFO */\n  },\n  [\"NOT_FOUND\" /* NOT_FOUND */]: {\n    statusCode: 404,\n    troubleshootingUrl: `${BASE_URL}/troubleshooting/common-issues#i-am-getting-a-network-errors--api-not-found`,\n    visibility: \"toast\" /* TOAST */,\n    severity: \"info\" /* INFO */\n  },\n  [\"AGENT_NOT_FOUND\" /* AGENT_NOT_FOUND */]: {\n    statusCode: 500,\n    troubleshootingUrl: `${BASE_URL}/coagents/troubleshooting/common-issues#i-am-getting-agent-not-found-error`,\n    visibility: \"banner\" /* BANNER */,\n    severity: \"critical\" /* CRITICAL */\n  },\n  [\"API_NOT_FOUND\" /* API_NOT_FOUND */]: {\n    statusCode: 404,\n    troubleshootingUrl: `${BASE_URL}/troubleshooting/common-issues#i-am-getting-a-network-errors--api-not-found`,\n    visibility: \"banner\" /* BANNER */,\n    severity: \"critical\" /* CRITICAL */\n  },\n  [\"REMOTE_ENDPOINT_NOT_FOUND\" /* REMOTE_ENDPOINT_NOT_FOUND */]: {\n    statusCode: 404,\n    troubleshootingUrl: `${BASE_URL}/troubleshooting/common-issues#i-am-getting-copilotkits-remote-endpoint-not-found-error`,\n    visibility: \"banner\" /* BANNER */,\n    severity: \"critical\" /* CRITICAL */\n  },\n  [\"MISUSE\" /* MISUSE */]: {\n    statusCode: 400,\n    troubleshootingUrl: null,\n    visibility: \"dev_only\" /* DEV_ONLY */,\n    severity: \"warning\" /* WARNING */\n  },\n  [\"UNKNOWN\" /* UNKNOWN */]: {\n    statusCode: 500,\n    visibility: \"toast\" /* TOAST */,\n    severity: \"info\" /* INFO */\n  },\n  [\"CONFIGURATION_ERROR\" /* CONFIGURATION_ERROR */]: {\n    statusCode: 400,\n    troubleshootingUrl: null,\n    severity: \"warning\" /* WARNING */,\n    visibility: \"banner\" /* BANNER */\n  },\n  [\"MISSING_PUBLIC_API_KEY_ERROR\" /* MISSING_PUBLIC_API_KEY_ERROR */]: {\n    statusCode: 400,\n    troubleshootingUrl: null,\n    severity: \"critical\" /* CRITICAL */,\n    visibility: \"banner\" /* BANNER */\n  },\n  [\"UPGRADE_REQUIRED_ERROR\" /* UPGRADE_REQUIRED_ERROR */]: {\n    statusCode: 402,\n    troubleshootingUrl: null,\n    severity: \"warning\" /* WARNING */,\n    visibility: \"banner\" /* BANNER */\n  },\n  [\"VERSION_MISMATCH\" /* VERSION_MISMATCH */]: {\n    statusCode: 400,\n    troubleshootingUrl: null,\n    visibility: \"dev_only\" /* DEV_ONLY */,\n    severity: \"info\" /* INFO */\n  }\n};\nvar CopilotKitError = class extends graphql__WEBPACK_IMPORTED_MODULE_0__.GraphQLError {\n  constructor({\n    message = \"Unknown error occurred\",\n    code,\n    severity,\n    visibility\n  }) {\n    const name = ERROR_NAMES.COPILOT_ERROR;\n    const config = ERROR_CONFIG[code];\n    const { statusCode } = config;\n    const resolvedVisibility = visibility ?? config.visibility ?? \"toast\" /* TOAST */;\n    const resolvedSeverity = severity ?? (\"severity\" in config ? config.severity : void 0);\n    super(message, {\n      extensions: {\n        name,\n        statusCode,\n        code,\n        visibility: resolvedVisibility,\n        severity: resolvedSeverity,\n        troubleshootingUrl: \"troubleshootingUrl\" in config ? config.troubleshootingUrl : null,\n        originalError: {\n          message,\n          stack: new Error().stack\n        }\n      }\n    });\n    this.code = code;\n    this.name = name;\n    this.statusCode = statusCode;\n    this.severity = resolvedSeverity;\n    this.visibility = resolvedVisibility;\n  }\n};\nvar CopilotKitMisuseError = class extends CopilotKitError {\n  constructor({\n    message,\n    code = \"MISUSE\" /* MISUSE */\n  }) {\n    const docsLink = \"troubleshootingUrl\" in ERROR_CONFIG[code] && ERROR_CONFIG[code].troubleshootingUrl ? getSeeMoreMarkdown(ERROR_CONFIG[code].troubleshootingUrl) : null;\n    const finalMessage = docsLink ? `${message}.\n\n${docsLink}` : message;\n    super({ message: finalMessage, code });\n    this.name = ERROR_NAMES.COPILOT_API_DISCOVERY_ERROR;\n  }\n};\nvar getVersionMismatchErrorMessage = ({\n  reactCoreVersion,\n  runtimeVersion,\n  runtimeClientGqlVersion\n}) => `Version mismatch detected: @copilotkit/runtime@${runtimeVersion ?? \"\"} is not compatible with @copilotkit/react-core@${reactCoreVersion} and @copilotkit/runtime-client-gql@${runtimeClientGqlVersion}. Please ensure all installed copilotkit packages are on the same version.`;\nvar CopilotKitVersionMismatchError = class extends CopilotKitError {\n  constructor({\n    reactCoreVersion,\n    runtimeVersion,\n    runtimeClientGqlVersion\n  }) {\n    const code = \"VERSION_MISMATCH\" /* VERSION_MISMATCH */;\n    super({\n      message: getVersionMismatchErrorMessage({\n        reactCoreVersion,\n        runtimeVersion,\n        runtimeClientGqlVersion\n      }),\n      code\n    });\n    this.name = ERROR_NAMES.COPILOT_KIT_VERSION_MISMATCH_ERROR;\n  }\n};\nvar CopilotKitApiDiscoveryError = class extends CopilotKitError {\n  constructor(params = {}) {\n    const url = params.url ?? \"\";\n    let operationSuffix = \"\";\n    if (url == null ? void 0 : url.includes(\"/info\"))\n      operationSuffix = `when fetching CopilotKit info`;\n    else if (url.includes(\"/actions/execute\"))\n      operationSuffix = `when attempting to execute actions.`;\n    else if (url.includes(\"/agents/state\"))\n      operationSuffix = `when attempting to get agent state.`;\n    else if (url.includes(\"/agents/execute\"))\n      operationSuffix = `when attempting to execute agent(s).`;\n    const message = params.message ?? (params.url ? `Failed to find CopilotKit API endpoint at url ${params.url} ${operationSuffix}` : `Failed to find CopilotKit API endpoint.`);\n    const code = params.code ?? \"API_NOT_FOUND\" /* API_NOT_FOUND */;\n    const errorMessage = `${message}.\n\n${getSeeMoreMarkdown(ERROR_CONFIG[code].troubleshootingUrl)}`;\n    super({ message: errorMessage, code });\n    this.name = ERROR_NAMES.COPILOT_API_DISCOVERY_ERROR;\n  }\n};\nvar CopilotKitRemoteEndpointDiscoveryError = class extends CopilotKitApiDiscoveryError {\n  constructor(params) {\n    const message = (params == null ? void 0 : params.message) ?? ((params == null ? void 0 : params.url) ? `Failed to find or contact remote endpoint at url ${params.url}` : \"Failed to find or contact remote endpoint\");\n    const code = \"REMOTE_ENDPOINT_NOT_FOUND\" /* REMOTE_ENDPOINT_NOT_FOUND */;\n    super({ message, code });\n    this.name = ERROR_NAMES.COPILOT_REMOTE_ENDPOINT_DISCOVERY_ERROR;\n  }\n};\nvar CopilotKitAgentDiscoveryError = class extends CopilotKitError {\n  constructor(params) {\n    const { agentName, availableAgents } = params;\n    const code = \"AGENT_NOT_FOUND\" /* AGENT_NOT_FOUND */;\n    let message = \"Failed to find any agents.\";\n    const configMessage = \"Please verify the agent name exists and is properly configured.\";\n    const seeMore = getSeeMoreMarkdown(ERROR_CONFIG[code].troubleshootingUrl);\n    if (availableAgents.length) {\n      message = agentName ? `Failed to find agent '${agentName}'. ${configMessage}` : `Failed to find agent. ${configMessage}`;\n      const bulletList = availableAgents.map((agent) => `\\u2022 ${agent.name} (ID: \\`${agent.id}\\`)`).join(\"\\n\");\n      message += `\n\nThe available agents are:\n\n${bulletList}\n\n${seeMore}`;\n    } else {\n      message += `\n\n${seeMore}`;\n    }\n    super({ message, code });\n    this.name = ERROR_NAMES.COPILOT_KIT_AGENT_DISCOVERY_ERROR;\n  }\n};\nvar CopilotKitLowLevelError = class extends CopilotKitError {\n  constructor({ error, url, message }) {\n    let code = \"NETWORK_ERROR\" /* NETWORK_ERROR */;\n    const errorCode = error.code;\n    const errorMessage = message ?? resolveLowLevelErrorMessage({ errorCode, url });\n    super({ message: errorMessage, code });\n    this.name = ERROR_NAMES.COPILOT_KIT_LOW_LEVEL_ERROR;\n  }\n};\nvar ResolvedCopilotKitError = class extends CopilotKitError {\n  constructor({\n    status,\n    message,\n    code,\n    isRemoteEndpoint,\n    url\n  }) {\n    let resolvedCode = code;\n    if (!resolvedCode) {\n      switch (status) {\n        case 400:\n          throw new CopilotKitApiDiscoveryError({ message, url });\n        case 404:\n          throw isRemoteEndpoint ? new CopilotKitRemoteEndpointDiscoveryError({ message, url }) : new CopilotKitApiDiscoveryError({ message, url });\n        default:\n          resolvedCode = \"UNKNOWN\" /* UNKNOWN */;\n          super({ message, code: resolvedCode, visibility: \"banner\" /* BANNER */ });\n      }\n    } else {\n      super({ message, code: resolvedCode });\n    }\n    this.name = ERROR_NAMES.RESOLVED_COPILOT_KIT_ERROR;\n  }\n};\nvar ConfigurationError = class extends CopilotKitError {\n  constructor(message) {\n    super({ message, code: \"CONFIGURATION_ERROR\" /* CONFIGURATION_ERROR */ });\n    this.name = ERROR_NAMES.CONFIGURATION_ERROR;\n    this.severity = \"warning\" /* WARNING */;\n  }\n};\nvar MissingPublicApiKeyError = class extends ConfigurationError {\n  constructor(message) {\n    super(message);\n    this.name = ERROR_NAMES.MISSING_PUBLIC_API_KEY_ERROR;\n    this.severity = \"critical\" /* CRITICAL */;\n  }\n};\nvar UpgradeRequiredError = class extends ConfigurationError {\n  constructor(message) {\n    super(message);\n    this.name = ERROR_NAMES.UPGRADE_REQUIRED_ERROR;\n    this.severity = \"warning\" /* WARNING */;\n  }\n};\nasync function getPossibleVersionMismatch({\n  runtimeVersion,\n  runtimeClientGqlVersion\n}) {\n  if (!runtimeVersion || runtimeVersion === \"\" || !runtimeClientGqlVersion)\n    return;\n  if (COPILOTKIT_VERSION !== runtimeVersion || COPILOTKIT_VERSION !== runtimeClientGqlVersion || runtimeVersion !== runtimeClientGqlVersion) {\n    return {\n      runtimeVersion,\n      runtimeClientGqlVersion,\n      reactCoreVersion: COPILOTKIT_VERSION,\n      message: getVersionMismatchErrorMessage({\n        runtimeVersion,\n        runtimeClientGqlVersion,\n        reactCoreVersion: COPILOTKIT_VERSION\n      })\n    };\n  }\n  return;\n}\nvar resolveLowLevelErrorMessage = ({ errorCode, url }) => {\n  const troubleshootingLink = ERROR_CONFIG[\"NETWORK_ERROR\" /* NETWORK_ERROR */].troubleshootingUrl;\n  const genericMessage = (description = `Failed to fetch from url ${url}.`) => `${description}.\n\nPossible reasons:\n- -The server may have an error preventing it from returning a response (Check the server logs for more info).\n- -The server might be down or unreachable\n- -There might be a network issue (e.g., DNS failure, connection timeout) \n- -The URL might be incorrect\n- -The server is not running on the specified port\n\n${getSeeMoreMarkdown(troubleshootingLink)}`;\n  if (url.includes(\"/info\"))\n    return genericMessage(`Failed to fetch CopilotKit agents/action information from url ${url}.`);\n  if (url.includes(\"/actions/execute\"))\n    return genericMessage(`Fetch call to ${url} to execute actions failed.`);\n  if (url.includes(\"/agents/state\"))\n    return genericMessage(`Fetch call to ${url} to get agent state failed.`);\n  if (url.includes(\"/agents/execute\"))\n    return genericMessage(`Fetch call to ${url} to execute agent(s) failed.`);\n  switch (errorCode) {\n    case \"ECONNREFUSED\":\n      return `Connection to ${url} was refused. Ensure the server is running and accessible.\n\n${getSeeMoreMarkdown(troubleshootingLink)}`;\n    case \"ENOTFOUND\":\n      return `The server on ${url} could not be found. Check the URL or your network configuration.\n\n${getSeeMoreMarkdown(ERROR_CONFIG[\"NOT_FOUND\" /* NOT_FOUND */].troubleshootingUrl)}`;\n    case \"ETIMEDOUT\":\n      return `The connection to ${url} timed out. The server might be overloaded or taking too long to respond.\n\n${getSeeMoreMarkdown(troubleshootingLink)}`;\n    default:\n      return;\n  }\n};\n\n\n//# sourceMappingURL=chunk-IZQALLRR.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-IZQALLRR.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-VNNKZIFB.mjs":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-VNNKZIFB.mjs ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dataToUUID: () => (/* binding */ dataToUUID),\n/* harmony export */   isValidUUID: () => (/* binding */ isValidUUID),\n/* harmony export */   randomId: () => (/* binding */ randomId),\n/* harmony export */   randomUUID: () => (/* binding */ randomUUID)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/.pnpm/uuid@10.0.0/node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/.pnpm/uuid@10.0.0/node_modules/uuid/dist/esm-node/v5.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/.pnpm/uuid@10.0.0/node_modules/uuid/dist/esm-node/validate.js\");\n// src/utils/random-id.ts\n\nfunction randomId() {\n  return \"ck-\" + (0,uuid__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n}\nfunction randomUUID() {\n  return (0,uuid__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n}\nfunction dataToUUID(input, namespace) {\n  const BASE_NAMESPACE = \"e4b01160-ff74-4c6e-9b27-d53cd930fe8e\";\n  const boundNamespace = namespace ? (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(namespace, BASE_NAMESPACE) : BASE_NAMESPACE;\n  return (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input, boundNamespace);\n}\nfunction isValidUUID(uuid) {\n  return (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(uuid);\n}\n\n\n//# sourceMappingURL=chunk-VNNKZIFB.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvcGlsb3RraXQrc2hhcmVkQDEuOS4yLW5leHQuNy9ub2RlX21vZHVsZXMvQGNvcGlsb3RraXQvc2hhcmVkL2Rpc3QvY2h1bmstVk5OS1pJRkIubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTtBQUM0RDtBQUM1RDtBQUNBLGlCQUFpQixnREFBTTtBQUN2QjtBQUNBO0FBQ0EsU0FBUyxnREFBTTtBQUNmO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxnREFBTTtBQUMzQyxTQUFTLGdEQUFNO0FBQ2Y7QUFDQTtBQUNBLFNBQVMsZ0RBQVE7QUFDakI7O0FBT0U7QUFDRiIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0Bjb3BpbG90a2l0K3NoYXJlZEAxLjkuMi1uZXh0Ljcvbm9kZV9tb2R1bGVzL0Bjb3BpbG90a2l0L3NoYXJlZC9kaXN0L2NodW5rLVZOTktaSUZCLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdXRpbHMvcmFuZG9tLWlkLnRzXG5pbXBvcnQgeyB2NCBhcyB1dWlkdjQsIHZhbGlkYXRlLCB2NSBhcyB1dWlkdjUgfSBmcm9tIFwidXVpZFwiO1xuZnVuY3Rpb24gcmFuZG9tSWQoKSB7XG4gIHJldHVybiBcImNrLVwiICsgdXVpZHY0KCk7XG59XG5mdW5jdGlvbiByYW5kb21VVUlEKCkge1xuICByZXR1cm4gdXVpZHY0KCk7XG59XG5mdW5jdGlvbiBkYXRhVG9VVUlEKGlucHV0LCBuYW1lc3BhY2UpIHtcbiAgY29uc3QgQkFTRV9OQU1FU1BBQ0UgPSBcImU0YjAxMTYwLWZmNzQtNGM2ZS05YjI3LWQ1M2NkOTMwZmU4ZVwiO1xuICBjb25zdCBib3VuZE5hbWVzcGFjZSA9IG5hbWVzcGFjZSA/IHV1aWR2NShuYW1lc3BhY2UsIEJBU0VfTkFNRVNQQUNFKSA6IEJBU0VfTkFNRVNQQUNFO1xuICByZXR1cm4gdXVpZHY1KGlucHV0LCBib3VuZE5hbWVzcGFjZSk7XG59XG5mdW5jdGlvbiBpc1ZhbGlkVVVJRCh1dWlkKSB7XG4gIHJldHVybiB2YWxpZGF0ZSh1dWlkKTtcbn1cblxuZXhwb3J0IHtcbiAgcmFuZG9tSWQsXG4gIHJhbmRvbVVVSUQsXG4gIGRhdGFUb1VVSUQsXG4gIGlzVmFsaWRVVUlEXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y2h1bmstVk5OS1pJRkIubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@copilotkit+shared@1.9.2-next.7/node_modules/@copilotkit/shared/dist/chunk-VNNKZIFB.mjs\n");

/***/ })

};
;