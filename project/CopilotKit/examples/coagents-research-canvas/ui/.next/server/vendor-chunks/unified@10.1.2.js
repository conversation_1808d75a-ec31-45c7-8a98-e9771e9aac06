"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unified@10.1.2";
exports.ids = ["vendor-chunks/unified@10.1.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/unified@10.1.2/node_modules/unified/lib/index.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/unified@10.1.2/node_modules/unified/lib/index.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unified: () => (/* binding */ unified)\n/* harmony export */ });\n/* harmony import */ var bail__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bail */ \"(ssr)/./node_modules/.pnpm/bail@2.0.2/node_modules/bail/index.js\");\n/* harmony import */ var is_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-buffer */ \"(ssr)/./node_modules/.pnpm/is-buffer@2.0.5/node_modules/is-buffer/index.js\");\n/* harmony import */ var extend__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! extend */ \"(ssr)/./node_modules/.pnpm/extend@3.0.2/node_modules/extend/index.js\");\n/* harmony import */ var is_plain_obj__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! is-plain-obj */ \"(ssr)/./node_modules/.pnpm/is-plain-obj@4.1.0/node_modules/is-plain-obj/index.js\");\n/* harmony import */ var trough__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! trough */ \"(ssr)/./node_modules/.pnpm/trough@2.2.0/node_modules/trough/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/.pnpm/vfile@5.3.7/node_modules/vfile/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('vfile').VFileCompatible} VFileCompatible\n * @typedef {import('vfile').VFileValue} VFileValue\n * @typedef {import('..').Processor} Processor\n * @typedef {import('..').Plugin} Plugin\n * @typedef {import('..').Preset} Preset\n * @typedef {import('..').Pluggable} Pluggable\n * @typedef {import('..').PluggableList} PluggableList\n * @typedef {import('..').Transformer} Transformer\n * @typedef {import('..').Parser} Parser\n * @typedef {import('..').Compiler} Compiler\n * @typedef {import('..').RunCallback} RunCallback\n * @typedef {import('..').ProcessCallback} ProcessCallback\n *\n * @typedef Context\n * @property {Node} tree\n * @property {VFile} file\n */\n\n\n\n\n\n\n\n\n// Expose a frozen processor.\nconst unified = base().freeze()\n\nconst own = {}.hasOwnProperty\n\n// Function to create the first processor.\n/**\n * @returns {Processor}\n */\nfunction base() {\n  const transformers = (0,trough__WEBPACK_IMPORTED_MODULE_3__.trough)()\n  /** @type {Processor['attachers']} */\n  const attachers = []\n  /** @type {Record<string, unknown>} */\n  let namespace = {}\n  /** @type {boolean|undefined} */\n  let frozen\n  let freezeIndex = -1\n\n  // Data management.\n  // @ts-expect-error: overloads are handled.\n  processor.data = data\n  processor.Parser = undefined\n  processor.Compiler = undefined\n\n  // Lock.\n  processor.freeze = freeze\n\n  // Plugins.\n  processor.attachers = attachers\n  // @ts-expect-error: overloads are handled.\n  processor.use = use\n\n  // API.\n  processor.parse = parse\n  processor.stringify = stringify\n  // @ts-expect-error: overloads are handled.\n  processor.run = run\n  processor.runSync = runSync\n  // @ts-expect-error: overloads are handled.\n  processor.process = process\n  processor.processSync = processSync\n\n  // Expose.\n  return processor\n\n  // Create a new processor based on the processor in the current scope.\n  /** @type {Processor} */\n  function processor() {\n    const destination = base()\n    let index = -1\n\n    while (++index < attachers.length) {\n      destination.use(...attachers[index])\n    }\n\n    destination.data(extend__WEBPACK_IMPORTED_MODULE_1__(true, {}, namespace))\n\n    return destination\n  }\n\n  /**\n   * @param {string|Record<string, unknown>} [key]\n   * @param {unknown} [value]\n   * @returns {unknown}\n   */\n  function data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', frozen)\n        namespace[key] = value\n        return processor\n      }\n\n      // Get `key`.\n      return (own.call(namespace, key) && namespace[key]) || null\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', frozen)\n      namespace = key\n      return processor\n    }\n\n    // Get space.\n    return namespace\n  }\n\n  /** @type {Processor['freeze']} */\n  function freeze() {\n    if (frozen) {\n      return processor\n    }\n\n    while (++freezeIndex < attachers.length) {\n      const [attacher, ...options] = attachers[freezeIndex]\n\n      if (options[0] === false) {\n        continue\n      }\n\n      if (options[0] === true) {\n        options[0] = undefined\n      }\n\n      /** @type {Transformer|void} */\n      const transformer = attacher.call(processor, ...options)\n\n      if (typeof transformer === 'function') {\n        transformers.use(transformer)\n      }\n    }\n\n    frozen = true\n    freezeIndex = Number.POSITIVE_INFINITY\n\n    return processor\n  }\n\n  /**\n   * @param {Pluggable|null|undefined} [value]\n   * @param {...unknown} options\n   * @returns {Processor}\n   */\n  function use(value, ...options) {\n    /** @type {Record<string, unknown>|undefined} */\n    let settings\n\n    assertUnfrozen('use', frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, ...options)\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`')\n    }\n\n    if (settings) {\n      namespace.settings = Object.assign(namespace.settings || {}, settings)\n    }\n\n    return processor\n\n    /**\n     * @param {import('..').Pluggable<unknown[]>} value\n     * @returns {void}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value)\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...options] = value\n          addPlugin(plugin, ...options)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {void}\n     */\n    function addPreset(result) {\n      addList(result.plugins)\n\n      if (result.settings) {\n        settings = Object.assign(settings || {}, result.settings)\n      }\n    }\n\n    /**\n     * @param {PluggableList|null|undefined} [plugins]\n     * @returns {void}\n     */\n    function addList(plugins) {\n      let index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index]\n          add(thing)\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {...unknown} [value]\n     * @returns {void}\n     */\n    function addPlugin(plugin, value) {\n      let index = -1\n      /** @type {Processor['attachers'][number]|undefined} */\n      let entry\n\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entry = attachers[index]\n          break\n        }\n      }\n\n      if (entry) {\n        if ((0,is_plain_obj__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(entry[1]) && (0,is_plain_obj__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(value)) {\n          value = extend__WEBPACK_IMPORTED_MODULE_1__(true, entry[1], value)\n        }\n\n        entry[1] = value\n      } else {\n        // @ts-expect-error: fine.\n        attachers.push([...arguments])\n      }\n    }\n  }\n\n  /** @type {Processor['parse']} */\n  function parse(doc) {\n    processor.freeze()\n    const file = vfile(doc)\n    const Parser = processor.Parser\n    assertParser('parse', Parser)\n\n    if (newable(Parser, 'parse')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Parser(String(file), file).parse()\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Parser(String(file), file) // eslint-disable-line new-cap\n  }\n\n  /** @type {Processor['stringify']} */\n  function stringify(node, doc) {\n    processor.freeze()\n    const file = vfile(doc)\n    const Compiler = processor.Compiler\n    assertCompiler('stringify', Compiler)\n    assertNode(node)\n\n    if (newable(Compiler, 'compile')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Compiler(node, file).compile()\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Compiler(node, file) // eslint-disable-line new-cap\n  }\n\n  /**\n   * @param {Node} node\n   * @param {VFileCompatible|RunCallback} [doc]\n   * @param {RunCallback} [callback]\n   * @returns {Promise<Node>|void}\n   */\n  function run(node, doc, callback) {\n    assertNode(node)\n    processor.freeze()\n\n    if (!callback && typeof doc === 'function') {\n      callback = doc\n      doc = undefined\n    }\n\n    if (!callback) {\n      return new Promise(executor)\n    }\n\n    executor(null, callback)\n\n    /**\n     * @param {null|((node: Node) => void)} resolve\n     * @param {(error: Error) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      // @ts-expect-error: `doc` can’t be a callback anymore, we checked.\n      transformers.run(node, vfile(doc), done)\n\n      /**\n       * @param {Error|null} error\n       * @param {Node} tree\n       * @param {VFile} file\n       * @returns {void}\n       */\n      function done(error, tree, file) {\n        tree = tree || node\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(tree)\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, tree, file)\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['runSync']} */\n  function runSync(node, file) {\n    /** @type {Node|undefined} */\n    let result\n    /** @type {boolean|undefined} */\n    let complete\n\n    processor.run(node, file, done)\n\n    assertDone('runSync', 'run', complete)\n\n    // @ts-expect-error: we either bailed on an error or have a tree.\n    return result\n\n    /**\n     * @param {Error|null} [error]\n     * @param {Node} [tree]\n     * @returns {void}\n     */\n    function done(error, tree) {\n      ;(0,bail__WEBPACK_IMPORTED_MODULE_4__.bail)(error)\n      result = tree\n      complete = true\n    }\n  }\n\n  /**\n   * @param {VFileCompatible} doc\n   * @param {ProcessCallback} [callback]\n   * @returns {Promise<VFile>|undefined}\n   */\n  function process(doc, callback) {\n    processor.freeze()\n    assertParser('process', processor.Parser)\n    assertCompiler('process', processor.Compiler)\n\n    if (!callback) {\n      return new Promise(executor)\n    }\n\n    executor(null, callback)\n\n    /**\n     * @param {null|((file: VFile) => void)} resolve\n     * @param {(error?: Error|null|undefined) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      const file = vfile(doc)\n\n      processor.run(processor.parse(file), file, (error, tree, file) => {\n        if (error || !tree || !file) {\n          done(error)\n        } else {\n          /** @type {unknown} */\n          const result = processor.stringify(tree, file)\n\n          if (result === undefined || result === null) {\n            // Empty.\n          } else if (looksLikeAVFileValue(result)) {\n            file.value = result\n          } else {\n            file.result = result\n          }\n\n          done(error, file)\n        }\n      })\n\n      /**\n       * @param {Error|null|undefined} [error]\n       * @param {VFile|undefined} [file]\n       * @returns {void}\n       */\n      function done(error, file) {\n        if (error || !file) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, file)\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['processSync']} */\n  function processSync(doc) {\n    /** @type {boolean|undefined} */\n    let complete\n\n    processor.freeze()\n    assertParser('processSync', processor.Parser)\n    assertCompiler('processSync', processor.Compiler)\n\n    const file = vfile(doc)\n\n    processor.process(file, done)\n\n    assertDone('processSync', 'process', complete)\n\n    return file\n\n    /**\n     * @param {Error|null|undefined} [error]\n     * @returns {void}\n     */\n    function done(error) {\n      complete = true\n      ;(0,bail__WEBPACK_IMPORTED_MODULE_4__.bail)(error)\n    }\n  }\n}\n\n/**\n * Check if `value` is a constructor.\n *\n * @param {unknown} value\n * @param {string} name\n * @returns {boolean}\n */\nfunction newable(value, name) {\n  return (\n    typeof value === 'function' &&\n    // Prototypes do exist.\n    // type-coverage:ignore-next-line\n    value.prototype &&\n    // A function with keys in its prototype is probably a constructor.\n    // Classes’ prototype methods are not enumerable, so we check if some value\n    // exists in the prototype.\n    // type-coverage:ignore-next-line\n    (keys(value.prototype) || name in value.prototype)\n  )\n}\n\n/**\n * Check if `value` is an object with keys.\n *\n * @param {Record<string, unknown>} value\n * @returns {boolean}\n */\nfunction keys(value) {\n  /** @type {string} */\n  let key\n\n  for (key in value) {\n    if (own.call(value, key)) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Parser`')\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Compiler`')\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot call `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!(0,is_plain_obj__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`')\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new vfile__WEBPACK_IMPORTED_MODULE_5__.VFile(value)\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'message' in value &&\n      'messages' in value\n  )\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is VFileValue}\n */\nfunction looksLikeAVFileValue(value) {\n  return typeof value === 'string' || is_buffer__WEBPACK_IMPORTED_MODULE_0__(value)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/unified@10.1.2/node_modules/unified/lib/index.js\n");

/***/ })

};
;