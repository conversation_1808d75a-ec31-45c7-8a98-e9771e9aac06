"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@segment+analytics-core@1.8.0";
exports.ids = ["vendor-chunks/@segment+analytics-core@1.8.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/analytics/dispatch.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/analytics/dispatch.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dispatch: () => (/* binding */ dispatch),\n/* harmony export */   getDelay: () => (/* binding */ getDelay)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _callback__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../callback */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/callback/index.js\");\n\n\n/* The amount of time in ms to wait before invoking the callback. */\nvar getDelay = function (startTimeInEpochMS, timeoutInMS) {\n    var elapsedTime = Date.now() - startTimeInEpochMS;\n    // increasing the timeout increases the delay by almost the same amount -- this is weird legacy behavior.\n    return Math.max((timeoutInMS !== null && timeoutInMS !== void 0 ? timeoutInMS : 300) - elapsedTime, 0);\n};\n/**\n * Push an event into the dispatch queue and invoke any callbacks.\n *\n * @param event - Segment event to enqueue.\n * @param queue - Queue to dispatch against.\n * @param emitter - This is typically an instance of \"Analytics\" -- used for metrics / progress information.\n * @param options\n */\nfunction dispatch(ctx, queue, emitter, options) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n        var startTime, dispatched;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    emitter.emit('dispatch_start', ctx);\n                    startTime = Date.now();\n                    if (!queue.isEmpty()) return [3 /*break*/, 2];\n                    return [4 /*yield*/, queue.dispatchSingle(ctx)];\n                case 1:\n                    dispatched = _a.sent();\n                    return [3 /*break*/, 4];\n                case 2: return [4 /*yield*/, queue.dispatch(ctx)];\n                case 3:\n                    dispatched = _a.sent();\n                    _a.label = 4;\n                case 4:\n                    if (!(options === null || options === void 0 ? void 0 : options.callback)) return [3 /*break*/, 6];\n                    return [4 /*yield*/, (0,_callback__WEBPACK_IMPORTED_MODULE_1__.invokeCallback)(dispatched, options.callback, getDelay(startTime, options.timeout))];\n                case 5:\n                    dispatched = _a.sent();\n                    _a.label = 6;\n                case 6:\n                    if (options === null || options === void 0 ? void 0 : options.debug) {\n                        dispatched.flush();\n                    }\n                    return [2 /*return*/, dispatched];\n            }\n        });\n    });\n}\n//# sourceMappingURL=dispatch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/analytics/dispatch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/callback/index.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/callback/index.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invokeCallback: () => (/* binding */ invokeCallback),\n/* harmony export */   pTimeout: () => (/* binding */ pTimeout),\n/* harmony export */   sleep: () => (/* binding */ sleep)\n/* harmony export */ });\nfunction pTimeout(promise, timeout) {\n    return new Promise(function (resolve, reject) {\n        var timeoutId = setTimeout(function () {\n            reject(Error('Promise timed out'));\n        }, timeout);\n        promise\n            .then(function (val) {\n            clearTimeout(timeoutId);\n            return resolve(val);\n        })\n            .catch(reject);\n    });\n}\nfunction sleep(timeoutInMs) {\n    return new Promise(function (resolve) { return setTimeout(resolve, timeoutInMs); });\n}\n/**\n * @param ctx\n * @param callback - the function to invoke\n * @param delay - aka \"timeout\". The amount of time in ms to wait before invoking the callback.\n */\nfunction invokeCallback(ctx, callback, delay) {\n    var cb = function () {\n        try {\n            return Promise.resolve(callback(ctx));\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    };\n    return (sleep(delay)\n        // pTimeout ensures that the callback can't cause the context to hang\n        .then(function () { return pTimeout(cb(), 1000); })\n        .catch(function (err) {\n        ctx === null || ctx === void 0 ? void 0 : ctx.log('warn', 'Callback Error', { error: err });\n        ctx === null || ctx === void 0 ? void 0 : ctx.stats.increment('callback_error');\n    })\n        .then(function () { return ctx; }));\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/callback/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/context/index.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/context/index.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContextCancelation: () => (/* binding */ ContextCancelation),\n/* harmony export */   CoreContext: () => (/* binding */ CoreContext)\n/* harmony export */ });\n/* harmony import */ var _lukeed_uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lukeed/uuid */ \"(rsc)/./node_modules/.pnpm/@lukeed+uuid@2.0.1/node_modules/@lukeed/uuid/dist/index.mjs\");\n/* harmony import */ var dset__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dset */ \"(rsc)/./node_modules/.pnpm/dset@3.1.4/node_modules/dset/dist/index.mjs\");\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../logger */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/logger/index.js\");\n/* harmony import */ var _stats__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../stats */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/stats/index.js\");\n\n\n\n\nvar ContextCancelation = /** @class */ (function () {\n    function ContextCancelation(options) {\n        var _a, _b, _c;\n        this.retry = (_a = options.retry) !== null && _a !== void 0 ? _a : true;\n        this.type = (_b = options.type) !== null && _b !== void 0 ? _b : 'plugin Error';\n        this.reason = (_c = options.reason) !== null && _c !== void 0 ? _c : '';\n    }\n    return ContextCancelation;\n}());\n\nvar CoreContext = /** @class */ (function () {\n    function CoreContext(event, id, stats, logger) {\n        if (id === void 0) { id = (0,_lukeed_uuid__WEBPACK_IMPORTED_MODULE_0__.v4)(); }\n        if (stats === void 0) { stats = new _stats__WEBPACK_IMPORTED_MODULE_2__.NullStats(); }\n        if (logger === void 0) { logger = new _logger__WEBPACK_IMPORTED_MODULE_3__.CoreLogger(); }\n        this.attempts = 0;\n        this.event = event;\n        this._id = id;\n        this.logger = logger;\n        this.stats = stats;\n    }\n    CoreContext.system = function () {\n        // This should be overridden by the subclass to return an instance of the subclass.\n    };\n    CoreContext.prototype.isSame = function (other) {\n        return other.id === this.id;\n    };\n    CoreContext.prototype.cancel = function (error) {\n        if (error) {\n            throw error;\n        }\n        throw new ContextCancelation({ reason: 'Context Cancel' });\n    };\n    CoreContext.prototype.log = function (level, message, extras) {\n        this.logger.log(level, message, extras);\n    };\n    Object.defineProperty(CoreContext.prototype, \"id\", {\n        get: function () {\n            return this._id;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CoreContext.prototype.updateEvent = function (path, val) {\n        var _a;\n        // Don't allow integrations that are set to false to be overwritten with integration settings.\n        if (path.split('.')[0] === 'integrations') {\n            var integrationName = path.split('.')[1];\n            if (((_a = this.event.integrations) === null || _a === void 0 ? void 0 : _a[integrationName]) === false) {\n                return this.event;\n            }\n        }\n        (0,dset__WEBPACK_IMPORTED_MODULE_1__.dset)(this.event, path, val);\n        return this.event;\n    };\n    CoreContext.prototype.failedDelivery = function () {\n        return this._failedDelivery;\n    };\n    CoreContext.prototype.setFailedDelivery = function (options) {\n        this._failedDelivery = options;\n    };\n    CoreContext.prototype.logs = function () {\n        return this.logger.logs;\n    };\n    CoreContext.prototype.flush = function () {\n        this.logger.flush();\n        this.stats.flush();\n    };\n    CoreContext.prototype.toJSON = function () {\n        return {\n            id: this._id,\n            event: this.event,\n            logs: this.logger.logs,\n            metrics: this.stats.metrics,\n        };\n    };\n    return CoreContext;\n}());\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/context/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/events/index.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/events/index.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreEventFactory: () => (/* binding */ CoreEventFactory)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var dset__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dset */ \"(rsc)/./node_modules/.pnpm/dset@3.1.4/node_modules/dset/dist/index.mjs\");\n/* harmony import */ var _utils_pick__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/pick */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/pick.js\");\n/* harmony import */ var _validation_assertions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../validation/assertions */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/assertions.js\");\n\n\n\n\n\n/**\n * Internal settings object that is used internally by the factory\n */\nvar InternalEventFactorySettings = /** @class */ (function () {\n    function InternalEventFactorySettings(settings) {\n        var _a, _b;\n        this.settings = settings;\n        this.createMessageId = settings.createMessageId;\n        this.onEventMethodCall = (_a = settings.onEventMethodCall) !== null && _a !== void 0 ? _a : (function () { });\n        this.onFinishedEvent = (_b = settings.onFinishedEvent) !== null && _b !== void 0 ? _b : (function () { });\n    }\n    return InternalEventFactorySettings;\n}());\nvar CoreEventFactory = /** @class */ (function () {\n    function CoreEventFactory(settings) {\n        this.settings = new InternalEventFactorySettings(settings);\n    }\n    CoreEventFactory.prototype.track = function (event, properties, options, globalIntegrations) {\n        this.settings.onEventMethodCall({ type: 'track', options: options });\n        return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, this.baseEvent()), { event: event, type: 'track', properties: properties !== null && properties !== void 0 ? properties : {}, options: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, options), integrations: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, globalIntegrations) }));\n    };\n    CoreEventFactory.prototype.page = function (category, page, properties, options, globalIntegrations) {\n        var _a;\n        this.settings.onEventMethodCall({ type: 'page', options: options });\n        var event = {\n            type: 'page',\n            properties: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, properties),\n            options: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, options),\n            integrations: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, globalIntegrations),\n        };\n        if (category !== null) {\n            event.category = category;\n            event.properties = (_a = event.properties) !== null && _a !== void 0 ? _a : {};\n            event.properties.category = category;\n        }\n        if (page !== null) {\n            event.name = page;\n        }\n        return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, this.baseEvent()), event));\n    };\n    CoreEventFactory.prototype.screen = function (category, screen, properties, options, globalIntegrations) {\n        this.settings.onEventMethodCall({ type: 'screen', options: options });\n        var event = {\n            type: 'screen',\n            properties: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, properties),\n            options: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, options),\n            integrations: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, globalIntegrations),\n        };\n        if (category !== null) {\n            event.category = category;\n        }\n        if (screen !== null) {\n            event.name = screen;\n        }\n        return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, this.baseEvent()), event));\n    };\n    CoreEventFactory.prototype.identify = function (userId, traits, options, globalIntegrations) {\n        this.settings.onEventMethodCall({ type: 'identify', options: options });\n        return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, this.baseEvent()), { type: 'identify', userId: userId, traits: traits !== null && traits !== void 0 ? traits : {}, options: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, options), integrations: globalIntegrations }));\n    };\n    CoreEventFactory.prototype.group = function (groupId, traits, options, globalIntegrations) {\n        this.settings.onEventMethodCall({ type: 'group', options: options });\n        return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, this.baseEvent()), { type: 'group', traits: traits !== null && traits !== void 0 ? traits : {}, options: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, options), integrations: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, globalIntegrations), //\n            groupId: groupId }));\n    };\n    CoreEventFactory.prototype.alias = function (to, from, // TODO: can we make this undefined?\n    options, globalIntegrations) {\n        this.settings.onEventMethodCall({ type: 'alias', options: options });\n        var base = {\n            userId: to,\n            type: 'alias',\n            options: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, options),\n            integrations: (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, globalIntegrations),\n        };\n        if (from !== null) {\n            base.previousId = from;\n        }\n        if (to === undefined) {\n            return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, base), this.baseEvent()));\n        }\n        return this.normalize((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, this.baseEvent()), base));\n    };\n    CoreEventFactory.prototype.baseEvent = function () {\n        return {\n            integrations: {},\n            options: {},\n        };\n    };\n    /**\n     * Builds the context part of an event based on \"foreign\" keys that\n     * are provided in the `Options` parameter for an Event\n     */\n    CoreEventFactory.prototype.context = function (options) {\n        var _a;\n        /**\n         * If the event options are known keys from this list, we move them to the top level of the event.\n         * Any other options are moved to context.\n         */\n        var eventOverrideKeys = [\n            'userId',\n            'anonymousId',\n            'timestamp',\n            'messageId',\n        ];\n        delete options['integrations'];\n        var providedOptionsKeys = Object.keys(options);\n        var context = (_a = options.context) !== null && _a !== void 0 ? _a : {};\n        var eventOverrides = {};\n        providedOptionsKeys.forEach(function (key) {\n            if (key === 'context') {\n                return;\n            }\n            if (eventOverrideKeys.includes(key)) {\n                (0,dset__WEBPACK_IMPORTED_MODULE_0__.dset)(eventOverrides, key, options[key]);\n            }\n            else {\n                (0,dset__WEBPACK_IMPORTED_MODULE_0__.dset)(context, key, options[key]);\n            }\n        });\n        return [context, eventOverrides];\n    };\n    CoreEventFactory.prototype.normalize = function (event) {\n        var _a, _b;\n        var integrationBooleans = Object.keys((_a = event.integrations) !== null && _a !== void 0 ? _a : {}).reduce(function (integrationNames, name) {\n            var _a;\n            var _b;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, integrationNames), (_a = {}, _a[name] = Boolean((_b = event.integrations) === null || _b === void 0 ? void 0 : _b[name]), _a));\n        }, {});\n        // filter out any undefined options\n        event.options = (0,_utils_pick__WEBPACK_IMPORTED_MODULE_2__.pickBy)(event.options || {}, function (_, value) {\n            return value !== undefined;\n        });\n        // This is pretty trippy, but here's what's going on:\n        // - a) We don't pass initial integration options as part of the event, only if they're true or false\n        // - b) We do accept per integration overrides (like integrations.Amplitude.sessionId) at the event level\n        // Hence the need to convert base integration options to booleans, but maintain per event integration overrides\n        var allIntegrations = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, integrationBooleans), (_b = event.options) === null || _b === void 0 ? void 0 : _b.integrations);\n        var _c = event.options\n            ? this.context(event.options)\n            : [], context = _c[0], overrides = _c[1];\n        var options = event.options, rest = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__rest)(event, [\"options\"]);\n        var evt = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({ timestamp: new Date() }, rest), { context: context, integrations: allIntegrations }), overrides), { messageId: options.messageId || this.settings.createMessageId() });\n        this.settings.onFinishedEvent(evt);\n        (0,_validation_assertions__WEBPACK_IMPORTED_MODULE_3__.validateEvent)(evt);\n        return evt;\n    };\n    return CoreEventFactory;\n}());\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/events/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/logger/index.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/logger/index.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreLogger: () => (/* binding */ CoreLogger)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n\nvar CoreLogger = /** @class */ (function () {\n    function CoreLogger() {\n        this._logs = [];\n    }\n    CoreLogger.prototype.log = function (level, message, extras) {\n        var time = new Date();\n        this._logs.push({\n            level: level,\n            message: message,\n            time: time,\n            extras: extras,\n        });\n    };\n    Object.defineProperty(CoreLogger.prototype, \"logs\", {\n        get: function () {\n            return this._logs;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CoreLogger.prototype.flush = function () {\n        if (this.logs.length > 1) {\n            var formatted = this._logs.reduce(function (logs, log) {\n                var _a;\n                var _b, _c;\n                var line = (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)({}, log), { json: JSON.stringify(log.extras, null, ' '), extras: log.extras });\n                delete line['time'];\n                var key = (_c = (_b = log.time) === null || _b === void 0 ? void 0 : _b.toISOString()) !== null && _c !== void 0 ? _c : '';\n                if (logs[key]) {\n                    key = \"\".concat(key, \"-\").concat(Math.random());\n                }\n                return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)({}, logs), (_a = {}, _a[key] = line, _a));\n            }, {});\n            // ie doesn't like console.table\n            if (console.table) {\n                console.table(formatted);\n            }\n            else {\n                console.log(formatted);\n            }\n        }\n        else {\n            this.logs.forEach(function (logEntry) {\n                var level = logEntry.level, message = logEntry.message, extras = logEntry.extras;\n                if (level === 'info' || level === 'debug') {\n                    console.log(message, extras !== null && extras !== void 0 ? extras : '');\n                }\n                else {\n                    console[level](message, extras !== null && extras !== void 0 ? extras : '');\n                }\n            });\n        }\n        this._logs = [];\n    };\n    return CoreLogger;\n}());\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/logger/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/priority-queue/backoff.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/priority-queue/backoff.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   backoff: () => (/* binding */ backoff)\n/* harmony export */ });\nfunction backoff(params) {\n    var random = Math.random() + 1;\n    var _a = params.minTimeout, minTimeout = _a === void 0 ? 500 : _a, _b = params.factor, factor = _b === void 0 ? 2 : _b, attempt = params.attempt, _c = params.maxTimeout, maxTimeout = _c === void 0 ? Infinity : _c;\n    return Math.min(random * minTimeout * Math.pow(factor, attempt), maxTimeout);\n}\n//# sourceMappingURL=backoff.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLWNvcmVAMS44LjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1jb3JlL2Rpc3QvZXNtL3ByaW9yaXR5LXF1ZXVlL2JhY2tvZmYuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21lcmdlL3Byb2plY3QvQ29waWxvdEtpdC9leGFtcGxlcy9jb2FnZW50cy1yZXNlYXJjaC1jYW52YXMvdWkvbm9kZV9tb2R1bGVzLy5wbnBtL0BzZWdtZW50K2FuYWx5dGljcy1jb3JlQDEuOC4wL25vZGVfbW9kdWxlcy9Ac2VnbWVudC9hbmFseXRpY3MtY29yZS9kaXN0L2VzbS9wcmlvcml0eS1xdWV1ZS9iYWNrb2ZmLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBiYWNrb2ZmKHBhcmFtcykge1xuICAgIHZhciByYW5kb20gPSBNYXRoLnJhbmRvbSgpICsgMTtcbiAgICB2YXIgX2EgPSBwYXJhbXMubWluVGltZW91dCwgbWluVGltZW91dCA9IF9hID09PSB2b2lkIDAgPyA1MDAgOiBfYSwgX2IgPSBwYXJhbXMuZmFjdG9yLCBmYWN0b3IgPSBfYiA9PT0gdm9pZCAwID8gMiA6IF9iLCBhdHRlbXB0ID0gcGFyYW1zLmF0dGVtcHQsIF9jID0gcGFyYW1zLm1heFRpbWVvdXQsIG1heFRpbWVvdXQgPSBfYyA9PT0gdm9pZCAwID8gSW5maW5pdHkgOiBfYztcbiAgICByZXR1cm4gTWF0aC5taW4ocmFuZG9tICogbWluVGltZW91dCAqIE1hdGgucG93KGZhY3RvciwgYXR0ZW1wdCksIG1heFRpbWVvdXQpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YmFja29mZi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/priority-queue/backoff.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/priority-queue/index.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/priority-queue/index.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ON_REMOVE_FROM_FUTURE: () => (/* binding */ ON_REMOVE_FROM_FUTURE),\n/* harmony export */   PriorityQueue: () => (/* binding */ PriorityQueue)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @segment/analytics-generic-utils */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-generic-utils@1.2.0/node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js\");\n/* harmony import */ var _backoff__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./backoff */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/priority-queue/backoff.js\");\n\n\n\n/**\n * @internal\n */\nvar ON_REMOVE_FROM_FUTURE = 'onRemoveFromFuture';\nvar PriorityQueue = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(PriorityQueue, _super);\n    function PriorityQueue(maxAttempts, queue, seen) {\n        var _this = _super.call(this) || this;\n        _this.future = [];\n        _this.maxAttempts = maxAttempts;\n        _this.queue = queue;\n        _this.seen = seen !== null && seen !== void 0 ? seen : {};\n        return _this;\n    }\n    PriorityQueue.prototype.push = function () {\n        var _this = this;\n        var items = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            items[_i] = arguments[_i];\n        }\n        var accepted = items.map(function (operation) {\n            var attempts = _this.updateAttempts(operation);\n            if (attempts > _this.maxAttempts || _this.includes(operation)) {\n                return false;\n            }\n            _this.queue.push(operation);\n            return true;\n        });\n        this.queue = this.queue.sort(function (a, b) { return _this.getAttempts(a) - _this.getAttempts(b); });\n        return accepted;\n    };\n    PriorityQueue.prototype.pushWithBackoff = function (item, minTimeout) {\n        var _this = this;\n        if (minTimeout === void 0) { minTimeout = 0; }\n        // One immediate retry unless we have a minimum timeout (e.g. for rate limiting)\n        if (minTimeout == 0 && this.getAttempts(item) === 0) {\n            return this.push(item)[0];\n        }\n        var attempt = this.updateAttempts(item);\n        if (attempt > this.maxAttempts || this.includes(item)) {\n            return false;\n        }\n        var timeout = (0,_backoff__WEBPACK_IMPORTED_MODULE_1__.backoff)({ attempt: attempt - 1 });\n        if (minTimeout > 0 && timeout < minTimeout) {\n            timeout = minTimeout;\n        }\n        setTimeout(function () {\n            _this.queue.push(item);\n            // remove from future list\n            _this.future = _this.future.filter(function (f) { return f.id !== item.id; });\n            // Lets listeners know that a 'future' message is now available in the queue\n            _this.emit(ON_REMOVE_FROM_FUTURE);\n        }, timeout);\n        this.future.push(item);\n        return true;\n    };\n    PriorityQueue.prototype.getAttempts = function (item) {\n        var _a;\n        return (_a = this.seen[item.id]) !== null && _a !== void 0 ? _a : 0;\n    };\n    PriorityQueue.prototype.updateAttempts = function (item) {\n        this.seen[item.id] = this.getAttempts(item) + 1;\n        return this.getAttempts(item);\n    };\n    PriorityQueue.prototype.includes = function (item) {\n        return (this.queue.includes(item) ||\n            this.future.includes(item) ||\n            Boolean(this.queue.find(function (i) { return i.id === item.id; })) ||\n            Boolean(this.future.find(function (i) { return i.id === item.id; })));\n    };\n    PriorityQueue.prototype.pop = function () {\n        return this.queue.shift();\n    };\n    Object.defineProperty(PriorityQueue.prototype, \"length\", {\n        get: function () {\n            return this.queue.length;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(PriorityQueue.prototype, \"todo\", {\n        get: function () {\n            return this.queue.length + this.future.length;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return PriorityQueue;\n}(_segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_2__.Emitter));\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/priority-queue/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/queue/delivery.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/queue/delivery.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attempt: () => (/* binding */ attempt),\n/* harmony export */   ensure: () => (/* binding */ ensure)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../context */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/context/index.js\");\n\n\nfunction tryAsync(fn) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n        var err_1;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    _a.trys.push([0, 2, , 3]);\n                    return [4 /*yield*/, fn()];\n                case 1: return [2 /*return*/, _a.sent()];\n                case 2:\n                    err_1 = _a.sent();\n                    return [2 /*return*/, Promise.reject(err_1)];\n                case 3: return [2 /*return*/];\n            }\n        });\n    });\n}\nfunction attempt(ctx, plugin) {\n    ctx.log('debug', 'plugin', { plugin: plugin.name });\n    var start = new Date().getTime();\n    var hook = plugin[ctx.event.type];\n    if (hook === undefined) {\n        return Promise.resolve(ctx);\n    }\n    var newCtx = tryAsync(function () { return hook.apply(plugin, [ctx]); })\n        .then(function (ctx) {\n        var done = new Date().getTime() - start;\n        ctx.stats.gauge('plugin_time', done, [\"plugin:\".concat(plugin.name)]);\n        return ctx;\n    })\n        .catch(function (err) {\n        if (err instanceof _context__WEBPACK_IMPORTED_MODULE_1__.ContextCancelation &&\n            err.type === 'middleware_cancellation') {\n            throw err;\n        }\n        if (err instanceof _context__WEBPACK_IMPORTED_MODULE_1__.ContextCancelation) {\n            ctx.log('warn', err.type, {\n                plugin: plugin.name,\n                error: err,\n            });\n            return err;\n        }\n        ctx.log('error', 'plugin Error', {\n            plugin: plugin.name,\n            error: err,\n        });\n        ctx.stats.increment('plugin_error', 1, [\"plugin:\".concat(plugin.name)]);\n        return err;\n    });\n    return newCtx;\n}\nfunction ensure(ctx, plugin) {\n    return attempt(ctx, plugin).then(function (newContext) {\n        if (newContext instanceof _context__WEBPACK_IMPORTED_MODULE_1__.CoreContext) {\n            return newContext;\n        }\n        ctx.log('debug', 'Context canceled');\n        ctx.stats.increment('context_canceled');\n        ctx.cancel(newContext);\n    });\n}\n//# sourceMappingURL=delivery.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/queue/delivery.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/queue/event-queue.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/queue/event-queue.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreEventQueue: () => (/* binding */ CoreEventQueue)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _utils_group_by__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/group-by */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/group-by.js\");\n/* harmony import */ var _priority_queue__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../priority-queue */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/priority-queue/index.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/context/index.js\");\n/* harmony import */ var _segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @segment/analytics-generic-utils */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-generic-utils@1.2.0/node_modules/@segment/analytics-generic-utils/dist/esm/emitter/emitter.js\");\n/* harmony import */ var _task_task_group__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../task/task-group */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/task/task-group.js\");\n/* harmony import */ var _delivery__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./delivery */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/queue/delivery.js\");\n\n\n\n\n\n\n\nvar CoreEventQueue = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(CoreEventQueue, _super);\n    function CoreEventQueue(priorityQueue) {\n        var _this = _super.call(this) || this;\n        /**\n         * All event deliveries get suspended until all the tasks in this task group are complete.\n         * For example: a middleware that augments the event object should be loaded safely as a\n         * critical task, this way, event queue will wait for it to be ready before sending events.\n         *\n         * This applies to all the events already in the queue, and the upcoming ones\n         */\n        _this.criticalTasks = (0,_task_task_group__WEBPACK_IMPORTED_MODULE_1__.createTaskGroup)();\n        _this.plugins = [];\n        _this.failedInitializations = [];\n        _this.flushing = false;\n        _this.queue = priorityQueue;\n        _this.queue.on(_priority_queue__WEBPACK_IMPORTED_MODULE_2__.ON_REMOVE_FROM_FUTURE, function () {\n            _this.scheduleFlush(0);\n        });\n        return _this;\n    }\n    CoreEventQueue.prototype.register = function (ctx, plugin, instance) {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var handleLoadError, err_1;\n            var _this = this;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this.plugins.push(plugin);\n                        handleLoadError = function (err) {\n                            _this.failedInitializations.push(plugin.name);\n                            _this.emit('initialization_failure', plugin);\n                            console.warn(plugin.name, err);\n                            ctx.log('warn', 'Failed to load destination', {\n                                plugin: plugin.name,\n                                error: err,\n                            });\n                            // Filter out the failed plugin by excluding it from the list\n                            _this.plugins = _this.plugins.filter(function (p) { return p !== plugin; });\n                        };\n                        if (!(plugin.type === 'destination' && plugin.name !== 'Segment.io')) return [3 /*break*/, 1];\n                        plugin.load(ctx, instance).catch(handleLoadError);\n                        return [3 /*break*/, 4];\n                    case 1:\n                        _a.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, plugin.load(ctx, instance)];\n                    case 2:\n                        _a.sent();\n                        return [3 /*break*/, 4];\n                    case 3:\n                        err_1 = _a.sent();\n                        handleLoadError(err_1);\n                        return [3 /*break*/, 4];\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    CoreEventQueue.prototype.deregister = function (ctx, plugin, instance) {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var e_1;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        _a.trys.push([0, 3, , 4]);\n                        if (!plugin.unload) return [3 /*break*/, 2];\n                        return [4 /*yield*/, Promise.resolve(plugin.unload(ctx, instance))];\n                    case 1:\n                        _a.sent();\n                        _a.label = 2;\n                    case 2:\n                        this.plugins = this.plugins.filter(function (p) { return p.name !== plugin.name; });\n                        return [3 /*break*/, 4];\n                    case 3:\n                        e_1 = _a.sent();\n                        ctx.log('warn', 'Failed to unload destination', {\n                            plugin: plugin.name,\n                            error: e_1,\n                        });\n                        return [3 /*break*/, 4];\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    CoreEventQueue.prototype.dispatch = function (ctx) {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var willDeliver;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                ctx.log('debug', 'Dispatching');\n                ctx.stats.increment('message_dispatched');\n                this.queue.push(ctx);\n                willDeliver = this.subscribeToDelivery(ctx);\n                this.scheduleFlush(0);\n                return [2 /*return*/, willDeliver];\n            });\n        });\n    };\n    CoreEventQueue.prototype.subscribeToDelivery = function (ctx) {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var _this = this;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                return [2 /*return*/, new Promise(function (resolve) {\n                        var onDeliver = function (flushed, delivered) {\n                            if (flushed.isSame(ctx)) {\n                                _this.off('flush', onDeliver);\n                                if (delivered) {\n                                    resolve(flushed);\n                                }\n                                else {\n                                    resolve(flushed);\n                                }\n                            }\n                        };\n                        _this.on('flush', onDeliver);\n                    })];\n            });\n        });\n    };\n    CoreEventQueue.prototype.dispatchSingle = function (ctx) {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var _this = this;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                ctx.log('debug', 'Dispatching');\n                ctx.stats.increment('message_dispatched');\n                this.queue.updateAttempts(ctx);\n                ctx.attempts = 1;\n                return [2 /*return*/, this.deliver(ctx).catch(function (err) {\n                        var accepted = _this.enqueuRetry(err, ctx);\n                        if (!accepted) {\n                            ctx.setFailedDelivery({ reason: err });\n                            return ctx;\n                        }\n                        return _this.subscribeToDelivery(ctx);\n                    })];\n            });\n        });\n    };\n    CoreEventQueue.prototype.isEmpty = function () {\n        return this.queue.length === 0;\n    };\n    CoreEventQueue.prototype.scheduleFlush = function (timeout) {\n        var _this = this;\n        if (timeout === void 0) { timeout = 500; }\n        if (this.flushing) {\n            return;\n        }\n        this.flushing = true;\n        setTimeout(function () {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            _this.flush().then(function () {\n                setTimeout(function () {\n                    _this.flushing = false;\n                    if (_this.queue.length) {\n                        _this.scheduleFlush(0);\n                    }\n                }, 0);\n            });\n        }, timeout);\n    };\n    CoreEventQueue.prototype.deliver = function (ctx) {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var start, done, err_2, error;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.criticalTasks.done()];\n                    case 1:\n                        _a.sent();\n                        start = Date.now();\n                        _a.label = 2;\n                    case 2:\n                        _a.trys.push([2, 4, , 5]);\n                        return [4 /*yield*/, this.flushOne(ctx)];\n                    case 3:\n                        ctx = _a.sent();\n                        done = Date.now() - start;\n                        this.emit('delivery_success', ctx);\n                        ctx.stats.gauge('delivered', done);\n                        ctx.log('debug', 'Delivered', ctx.event);\n                        return [2 /*return*/, ctx];\n                    case 4:\n                        err_2 = _a.sent();\n                        error = err_2;\n                        ctx.log('error', 'Failed to deliver', error);\n                        this.emit('delivery_failure', ctx, error);\n                        ctx.stats.increment('delivery_failed');\n                        throw err_2;\n                    case 5: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    CoreEventQueue.prototype.enqueuRetry = function (err, ctx) {\n        var retriable = !(err instanceof _context__WEBPACK_IMPORTED_MODULE_3__.ContextCancelation) || err.retry;\n        if (!retriable) {\n            return false;\n        }\n        return this.queue.pushWithBackoff(ctx);\n    };\n    CoreEventQueue.prototype.flush = function () {\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var ctx, err_3, accepted;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (this.queue.length === 0) {\n                            return [2 /*return*/, []];\n                        }\n                        ctx = this.queue.pop();\n                        if (!ctx) {\n                            return [2 /*return*/, []];\n                        }\n                        ctx.attempts = this.queue.getAttempts(ctx);\n                        _a.label = 1;\n                    case 1:\n                        _a.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, this.deliver(ctx)];\n                    case 2:\n                        ctx = _a.sent();\n                        this.emit('flush', ctx, true);\n                        return [3 /*break*/, 4];\n                    case 3:\n                        err_3 = _a.sent();\n                        accepted = this.enqueuRetry(err_3, ctx);\n                        if (!accepted) {\n                            ctx.setFailedDelivery({ reason: err_3 });\n                            this.emit('flush', ctx, false);\n                        }\n                        return [2 /*return*/, []];\n                    case 4: return [2 /*return*/, [ctx]];\n                }\n            });\n        });\n    };\n    CoreEventQueue.prototype.isReady = function () {\n        // return this.plugins.every((p) => p.isLoaded())\n        // should we wait for every plugin to load?\n        return true;\n    };\n    CoreEventQueue.prototype.availableExtensions = function (denyList) {\n        var available = this.plugins.filter(function (p) {\n            var _a, _b, _c;\n            // Only filter out destination plugins or the Segment.io plugin\n            if (p.type !== 'destination' && p.name !== 'Segment.io') {\n                return true;\n            }\n            var alternativeNameMatch = undefined;\n            (_a = p.alternativeNames) === null || _a === void 0 ? void 0 : _a.forEach(function (name) {\n                if (denyList[name] !== undefined) {\n                    alternativeNameMatch = denyList[name];\n                }\n            });\n            // Explicit integration option takes precedence, `All: false` does not apply to Segment.io\n            return ((_c = (_b = denyList[p.name]) !== null && _b !== void 0 ? _b : alternativeNameMatch) !== null && _c !== void 0 ? _c : (p.name === 'Segment.io' ? true : denyList.All) !== false);\n        });\n        var _a = (0,_utils_group_by__WEBPACK_IMPORTED_MODULE_4__.groupBy)(available, 'type'), _b = _a.before, before = _b === void 0 ? [] : _b, _c = _a.enrichment, enrichment = _c === void 0 ? [] : _c, _d = _a.destination, destination = _d === void 0 ? [] : _d, _e = _a.after, after = _e === void 0 ? [] : _e;\n        return {\n            before: before,\n            enrichment: enrichment,\n            destinations: destination,\n            after: after,\n        };\n    };\n    CoreEventQueue.prototype.flushOne = function (ctx) {\n        var _a, _b;\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__awaiter)(this, void 0, void 0, function () {\n            var _c, before, enrichment, _i, before_1, beforeWare, temp, _d, enrichment_1, enrichmentWare, temp, _e, destinations, after, afterCalls;\n            return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__generator)(this, function (_f) {\n                switch (_f.label) {\n                    case 0:\n                        if (!this.isReady()) {\n                            throw new Error('Not ready');\n                        }\n                        if (ctx.attempts > 1) {\n                            this.emit('delivery_retry', ctx);\n                        }\n                        _c = this.availableExtensions((_a = ctx.event.integrations) !== null && _a !== void 0 ? _a : {}), before = _c.before, enrichment = _c.enrichment;\n                        _i = 0, before_1 = before;\n                        _f.label = 1;\n                    case 1:\n                        if (!(_i < before_1.length)) return [3 /*break*/, 4];\n                        beforeWare = before_1[_i];\n                        return [4 /*yield*/, (0,_delivery__WEBPACK_IMPORTED_MODULE_5__.ensure)(ctx, beforeWare)];\n                    case 2:\n                        temp = _f.sent();\n                        if (temp instanceof _context__WEBPACK_IMPORTED_MODULE_3__.CoreContext) {\n                            ctx = temp;\n                        }\n                        this.emit('message_enriched', ctx, beforeWare);\n                        _f.label = 3;\n                    case 3:\n                        _i++;\n                        return [3 /*break*/, 1];\n                    case 4:\n                        _d = 0, enrichment_1 = enrichment;\n                        _f.label = 5;\n                    case 5:\n                        if (!(_d < enrichment_1.length)) return [3 /*break*/, 8];\n                        enrichmentWare = enrichment_1[_d];\n                        return [4 /*yield*/, (0,_delivery__WEBPACK_IMPORTED_MODULE_5__.attempt)(ctx, enrichmentWare)];\n                    case 6:\n                        temp = _f.sent();\n                        if (temp instanceof _context__WEBPACK_IMPORTED_MODULE_3__.CoreContext) {\n                            ctx = temp;\n                        }\n                        this.emit('message_enriched', ctx, enrichmentWare);\n                        _f.label = 7;\n                    case 7:\n                        _d++;\n                        return [3 /*break*/, 5];\n                    case 8:\n                        _e = this.availableExtensions((_b = ctx.event.integrations) !== null && _b !== void 0 ? _b : {}), destinations = _e.destinations, after = _e.after;\n                        return [4 /*yield*/, new Promise(function (resolve, reject) {\n                                setTimeout(function () {\n                                    var attempts = destinations.map(function (destination) {\n                                        return (0,_delivery__WEBPACK_IMPORTED_MODULE_5__.attempt)(ctx, destination);\n                                    });\n                                    Promise.all(attempts).then(resolve).catch(reject);\n                                }, 0);\n                            })];\n                    case 9:\n                        _f.sent();\n                        ctx.stats.increment('message_delivered');\n                        this.emit('message_delivered', ctx);\n                        afterCalls = after.map(function (after) { return (0,_delivery__WEBPACK_IMPORTED_MODULE_5__.attempt)(ctx, after); });\n                        return [4 /*yield*/, Promise.all(afterCalls)];\n                    case 10:\n                        _f.sent();\n                        return [2 /*return*/, ctx];\n                }\n            });\n        });\n    };\n    return CoreEventQueue;\n}(_segment_analytics_generic_utils__WEBPACK_IMPORTED_MODULE_6__.Emitter));\n\n//# sourceMappingURL=event-queue.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/queue/event-queue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/stats/index.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/stats/index.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CoreStats: () => (/* binding */ CoreStats),\n/* harmony export */   NullStats: () => (/* binding */ NullStats)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n\nvar compactMetricType = function (type) {\n    var enums = {\n        gauge: 'g',\n        counter: 'c',\n    };\n    return enums[type];\n};\nvar CoreStats = /** @class */ (function () {\n    function CoreStats() {\n        this.metrics = [];\n    }\n    CoreStats.prototype.increment = function (metric, by, tags) {\n        if (by === void 0) { by = 1; }\n        this.metrics.push({\n            metric: metric,\n            value: by,\n            tags: tags !== null && tags !== void 0 ? tags : [],\n            type: 'counter',\n            timestamp: Date.now(),\n        });\n    };\n    CoreStats.prototype.gauge = function (metric, value, tags) {\n        this.metrics.push({\n            metric: metric,\n            value: value,\n            tags: tags !== null && tags !== void 0 ? tags : [],\n            type: 'gauge',\n            timestamp: Date.now(),\n        });\n    };\n    CoreStats.prototype.flush = function () {\n        var formatted = this.metrics.map(function (m) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)({}, m), { tags: m.tags.join(',') })); });\n        // ie doesn't like console.table\n        if (console.table) {\n            console.table(formatted);\n        }\n        else {\n            console.log(formatted);\n        }\n        this.metrics = [];\n    };\n    /**\n     * compact keys for smaller payload\n     */\n    CoreStats.prototype.serialize = function () {\n        return this.metrics.map(function (m) {\n            return {\n                m: m.metric,\n                v: m.value,\n                t: m.tags,\n                k: compactMetricType(m.type),\n                e: m.timestamp,\n            };\n        });\n    };\n    return CoreStats;\n}());\n\nvar NullStats = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(NullStats, _super);\n    function NullStats() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    NullStats.prototype.gauge = function () {\n        var _args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            _args[_i] = arguments[_i];\n        }\n    };\n    NullStats.prototype.increment = function () {\n        var _args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            _args[_i] = arguments[_i];\n        }\n    };\n    NullStats.prototype.flush = function () {\n        var _args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            _args[_i] = arguments[_i];\n        }\n    };\n    NullStats.prototype.serialize = function () {\n        var _args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            _args[_i] = arguments[_i];\n        }\n        return [];\n    };\n    return NullStats;\n}(CoreStats));\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/stats/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/task/task-group.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/task/task-group.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTaskGroup: () => (/* binding */ createTaskGroup)\n/* harmony export */ });\n/* harmony import */ var _utils_is_thenable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/is-thenable */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/is-thenable.js\");\n\nvar createTaskGroup = function () {\n    var taskCompletionPromise;\n    var resolvePromise;\n    var count = 0;\n    return {\n        done: function () { return taskCompletionPromise; },\n        run: function (op) {\n            var returnValue = op();\n            if ((0,_utils_is_thenable__WEBPACK_IMPORTED_MODULE_0__.isThenable)(returnValue)) {\n                if (++count === 1) {\n                    taskCompletionPromise = new Promise(function (res) { return (resolvePromise = res); });\n                }\n                returnValue.finally(function () { return --count === 0 && resolvePromise(); });\n            }\n            return returnValue;\n        },\n    };\n};\n//# sourceMappingURL=task-group.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLWNvcmVAMS44LjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1jb3JlL2Rpc3QvZXNtL3Rhc2svdGFzay1ncm91cC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRDtBQUMzQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLCtCQUErQjtBQUMzRDtBQUNBO0FBQ0EsZ0JBQWdCLDhEQUFVO0FBQzFCO0FBQ0EseUVBQXlFLGdDQUFnQztBQUN6RztBQUNBLGtEQUFrRCwyQ0FBMkM7QUFDN0Y7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac2VnbWVudCthbmFseXRpY3MtY29yZUAxLjguMC9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUvZGlzdC9lc20vdGFzay90YXNrLWdyb3VwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzVGhlbmFibGUgfSBmcm9tICcuLi91dGlscy9pcy10aGVuYWJsZSc7XG5leHBvcnQgdmFyIGNyZWF0ZVRhc2tHcm91cCA9IGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgdGFza0NvbXBsZXRpb25Qcm9taXNlO1xuICAgIHZhciByZXNvbHZlUHJvbWlzZTtcbiAgICB2YXIgY291bnQgPSAwO1xuICAgIHJldHVybiB7XG4gICAgICAgIGRvbmU6IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHRhc2tDb21wbGV0aW9uUHJvbWlzZTsgfSxcbiAgICAgICAgcnVuOiBmdW5jdGlvbiAob3ApIHtcbiAgICAgICAgICAgIHZhciByZXR1cm5WYWx1ZSA9IG9wKCk7XG4gICAgICAgICAgICBpZiAoaXNUaGVuYWJsZShyZXR1cm5WYWx1ZSkpIHtcbiAgICAgICAgICAgICAgICBpZiAoKytjb3VudCA9PT0gMSkge1xuICAgICAgICAgICAgICAgICAgICB0YXNrQ29tcGxldGlvblByb21pc2UgPSBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzKSB7IHJldHVybiAocmVzb2x2ZVByb21pc2UgPSByZXMpOyB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuVmFsdWUuZmluYWxseShmdW5jdGlvbiAoKSB7IHJldHVybiAtLWNvdW50ID09PSAwICYmIHJlc29sdmVQcm9taXNlKCk7IH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIHJldHVyblZhbHVlO1xuICAgICAgICB9LFxuICAgIH07XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dGFzay1ncm91cC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/task/task-group.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/bind-all.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/bind-all.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bindAll: () => (/* binding */ bindAll)\n/* harmony export */ });\nfunction bindAll(obj) {\n    var proto = obj.constructor.prototype;\n    for (var _i = 0, _a = Object.getOwnPropertyNames(proto); _i < _a.length; _i++) {\n        var key = _a[_i];\n        if (key !== 'constructor') {\n            var desc = Object.getOwnPropertyDescriptor(obj.constructor.prototype, key);\n            if (!!desc && typeof desc.value === 'function') {\n                obj[key] = obj[key].bind(obj);\n            }\n        }\n    }\n    return obj;\n}\n//# sourceMappingURL=bind-all.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLWNvcmVAMS44LjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1jb3JlL2Rpc3QvZXNtL3V0aWxzL2JpbmQtYWxsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0EsNkRBQTZELGdCQUFnQjtBQUM3RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLWNvcmVAMS44LjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1jb3JlL2Rpc3QvZXNtL3V0aWxzL2JpbmQtYWxsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBiaW5kQWxsKG9iaikge1xuICAgIHZhciBwcm90byA9IG9iai5jb25zdHJ1Y3Rvci5wcm90b3R5cGU7XG4gICAgZm9yICh2YXIgX2kgPSAwLCBfYSA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eU5hbWVzKHByb3RvKTsgX2kgPCBfYS5sZW5ndGg7IF9pKyspIHtcbiAgICAgICAgdmFyIGtleSA9IF9hW19pXTtcbiAgICAgICAgaWYgKGtleSAhPT0gJ2NvbnN0cnVjdG9yJykge1xuICAgICAgICAgICAgdmFyIGRlc2MgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKG9iai5jb25zdHJ1Y3Rvci5wcm90b3R5cGUsIGtleSk7XG4gICAgICAgICAgICBpZiAoISFkZXNjICYmIHR5cGVvZiBkZXNjLnZhbHVlID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgb2JqW2tleV0gPSBvYmpba2V5XS5iaW5kKG9iaik7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG9iajtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWJpbmQtYWxsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/bind-all.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/group-by.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/group-by.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   groupBy: () => (/* binding */ groupBy)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n\nfunction groupBy(collection, grouper) {\n    var results = {};\n    collection.forEach(function (item) {\n        var _a;\n        var key = undefined;\n        if (typeof grouper === 'string') {\n            var suggestedKey = item[grouper];\n            key =\n                typeof suggestedKey !== 'string'\n                    ? JSON.stringify(suggestedKey)\n                    : suggestedKey;\n        }\n        else if (grouper instanceof Function) {\n            key = grouper(item);\n        }\n        if (key === undefined) {\n            return;\n        }\n        results[key] = (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_0__.__spreadArray)([], ((_a = results[key]) !== null && _a !== void 0 ? _a : []), true), [item], false);\n    });\n    return results;\n}\n//# sourceMappingURL=group-by.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLWNvcmVAMS44LjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1jb3JlL2Rpc3QvZXNtL3V0aWxzL2dyb3VwLWJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXNDO0FBQy9CO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixvREFBYSxDQUFDLG9EQUFhO0FBQ2xELEtBQUs7QUFDTDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac2VnbWVudCthbmFseXRpY3MtY29yZUAxLjguMC9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUvZGlzdC9lc20vdXRpbHMvZ3JvdXAtYnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgX19zcHJlYWRBcnJheSB9IGZyb20gXCJ0c2xpYlwiO1xuZXhwb3J0IGZ1bmN0aW9uIGdyb3VwQnkoY29sbGVjdGlvbiwgZ3JvdXBlcikge1xuICAgIHZhciByZXN1bHRzID0ge307XG4gICAgY29sbGVjdGlvbi5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgdmFyIGtleSA9IHVuZGVmaW5lZDtcbiAgICAgICAgaWYgKHR5cGVvZiBncm91cGVyID09PSAnc3RyaW5nJykge1xuICAgICAgICAgICAgdmFyIHN1Z2dlc3RlZEtleSA9IGl0ZW1bZ3JvdXBlcl07XG4gICAgICAgICAgICBrZXkgPVxuICAgICAgICAgICAgICAgIHR5cGVvZiBzdWdnZXN0ZWRLZXkgIT09ICdzdHJpbmcnXG4gICAgICAgICAgICAgICAgICAgID8gSlNPTi5zdHJpbmdpZnkoc3VnZ2VzdGVkS2V5KVxuICAgICAgICAgICAgICAgICAgICA6IHN1Z2dlc3RlZEtleTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChncm91cGVyIGluc3RhbmNlb2YgRnVuY3Rpb24pIHtcbiAgICAgICAgICAgIGtleSA9IGdyb3VwZXIoaXRlbSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGtleSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgcmVzdWx0c1trZXldID0gX19zcHJlYWRBcnJheShfX3NwcmVhZEFycmF5KFtdLCAoKF9hID0gcmVzdWx0c1trZXldKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBbXSksIHRydWUpLCBbaXRlbV0sIGZhbHNlKTtcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzdWx0cztcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdyb3VwLWJ5LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/group-by.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/is-thenable.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/is-thenable.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isThenable: () => (/* binding */ isThenable)\n/* harmony export */ });\n/**\n *  Check if  thenable\n *  (instanceof Promise doesn't respect realms)\n */\nvar isThenable = function (value) {\n    return typeof value === 'object' &&\n        value !== null &&\n        'then' in value &&\n        typeof value.then === 'function';\n};\n//# sourceMappingURL=is-thenable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLWNvcmVAMS44LjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1jb3JlL2Rpc3QvZXNtL3V0aWxzL2lzLXRoZW5hYmxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLWNvcmVAMS44LjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1jb3JlL2Rpc3QvZXNtL3V0aWxzL2lzLXRoZW5hYmxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogIENoZWNrIGlmICB0aGVuYWJsZVxuICogIChpbnN0YW5jZW9mIFByb21pc2UgZG9lc24ndCByZXNwZWN0IHJlYWxtcylcbiAqL1xuZXhwb3J0IHZhciBpc1RoZW5hYmxlID0gZnVuY3Rpb24gKHZhbHVlKSB7XG4gICAgcmV0dXJuIHR5cGVvZiB2YWx1ZSA9PT0gJ29iamVjdCcgJiZcbiAgICAgICAgdmFsdWUgIT09IG51bGwgJiZcbiAgICAgICAgJ3RoZW4nIGluIHZhbHVlICYmXG4gICAgICAgIHR5cGVvZiB2YWx1ZS50aGVuID09PSAnZnVuY3Rpb24nO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWlzLXRoZW5hYmxlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/is-thenable.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/pick.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/pick.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pickBy: () => (/* binding */ pickBy)\n/* harmony export */ });\nvar pickBy = function (obj, fn) {\n    return Object.keys(obj)\n        .filter(function (k) { return fn(k, obj[k]); })\n        .reduce(function (acc, key) { return ((acc[key] = obj[key]), acc); }, {});\n};\n//# sourceMappingURL=pick.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLWNvcmVAMS44LjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1jb3JlL2Rpc3QvZXNtL3V0aWxzL3BpY2suanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQSwrQkFBK0IsdUJBQXVCO0FBQ3RELHNDQUFzQyxzQ0FBc0MsSUFBSTtBQUNoRjtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLWNvcmVAMS44LjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1jb3JlL2Rpc3QvZXNtL3V0aWxzL3BpY2suanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBwaWNrQnkgPSBmdW5jdGlvbiAob2JqLCBmbikge1xuICAgIHJldHVybiBPYmplY3Qua2V5cyhvYmopXG4gICAgICAgIC5maWx0ZXIoZnVuY3Rpb24gKGspIHsgcmV0dXJuIGZuKGssIG9ialtrXSk7IH0pXG4gICAgICAgIC5yZWR1Y2UoZnVuY3Rpb24gKGFjYywga2V5KSB7IHJldHVybiAoKGFjY1trZXldID0gb2JqW2tleV0pLCBhY2MpOyB9LCB7fSk7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGljay5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/utils/pick.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/assertions.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/assertions.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assertEventExists: () => (/* binding */ assertEventExists),\n/* harmony export */   assertEventType: () => (/* binding */ assertEventType),\n/* harmony export */   assertMessageId: () => (/* binding */ assertMessageId),\n/* harmony export */   assertTrackEventName: () => (/* binding */ assertTrackEventName),\n/* harmony export */   assertTrackEventProperties: () => (/* binding */ assertTrackEventProperties),\n/* harmony export */   assertTraits: () => (/* binding */ assertTraits),\n/* harmony export */   assertUserIdentity: () => (/* binding */ assertUserIdentity),\n/* harmony export */   validateEvent: () => (/* binding */ validateEvent)\n/* harmony export */ });\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/errors.js\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/helpers.js\");\n\n\nvar stringError = 'is not a string';\nvar objError = 'is not an object';\nvar nilError = 'is nil';\n// user identity check could hypothetically could be used in the browser event factory, but not 100% sure -- so this is node only for now\nfunction assertUserIdentity(event) {\n    var USER_FIELD_NAME = '.userId/anonymousId/previousId/groupId';\n    var getAnyUserId = function (event) { var _a, _b, _c; return (_c = (_b = (_a = event.userId) !== null && _a !== void 0 ? _a : event.anonymousId) !== null && _b !== void 0 ? _b : event.groupId) !== null && _c !== void 0 ? _c : event.previousId; };\n    var id = getAnyUserId(event);\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.exists)(id)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError(USER_FIELD_NAME, nilError);\n    }\n    else if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isString)(id)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError(USER_FIELD_NAME, stringError);\n    }\n}\nfunction assertEventExists(event) {\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.exists)(event)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('Event', nilError);\n    }\n    if (typeof event !== 'object') {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('Event', objError);\n    }\n}\nfunction assertEventType(event) {\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isString)(event.type)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('.type', stringError);\n    }\n}\nfunction assertTrackEventName(event) {\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isString)(event.event)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('.event', stringError);\n    }\n}\nfunction assertTrackEventProperties(event) {\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(event.properties)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('.properties', objError);\n    }\n}\nfunction assertTraits(event) {\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(event.traits)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('.traits', objError);\n    }\n}\nfunction assertMessageId(event) {\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.isString)(event.messageId)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.ValidationError('.messageId', stringError);\n    }\n}\nfunction validateEvent(event) {\n    assertEventExists(event);\n    assertEventType(event);\n    assertMessageId(event);\n    if (event.type === 'track') {\n        assertTrackEventName(event);\n        assertTrackEventProperties(event);\n    }\n    if (['group', 'identify'].includes(event.type)) {\n        assertTraits(event);\n    }\n}\n//# sourceMappingURL=assertions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/assertions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/errors.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/errors.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n\nvar ValidationError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(ValidationError, _super);\n    function ValidationError(field, message) {\n        var _this = _super.call(this, \"\".concat(field, \" \").concat(message)) || this;\n        _this.field = field;\n        return _this;\n    }\n    return ValidationError;\n}(Error));\n\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLWNvcmVAMS44LjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1jb3JlL2Rpc3QvZXNtL3ZhbGlkYXRpb24vZXJyb3JzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtDO0FBQ2xDO0FBQ0EsSUFBSSxnREFBUztBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDMEI7QUFDM0IiLCJzb3VyY2VzIjpbIi9tZXJnZS9wcm9qZWN0L0NvcGlsb3RLaXQvZXhhbXBsZXMvY29hZ2VudHMtcmVzZWFyY2gtY2FudmFzL3VpL25vZGVfbW9kdWxlcy8ucG5wbS9Ac2VnbWVudCthbmFseXRpY3MtY29yZUAxLjguMC9ub2RlX21vZHVsZXMvQHNlZ21lbnQvYW5hbHl0aWNzLWNvcmUvZGlzdC9lc20vdmFsaWRhdGlvbi9lcnJvcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgX19leHRlbmRzIH0gZnJvbSBcInRzbGliXCI7XG52YXIgVmFsaWRhdGlvbkVycm9yID0gLyoqIEBjbGFzcyAqLyAoZnVuY3Rpb24gKF9zdXBlcikge1xuICAgIF9fZXh0ZW5kcyhWYWxpZGF0aW9uRXJyb3IsIF9zdXBlcik7XG4gICAgZnVuY3Rpb24gVmFsaWRhdGlvbkVycm9yKGZpZWxkLCBtZXNzYWdlKSB7XG4gICAgICAgIHZhciBfdGhpcyA9IF9zdXBlci5jYWxsKHRoaXMsIFwiXCIuY29uY2F0KGZpZWxkLCBcIiBcIikuY29uY2F0KG1lc3NhZ2UpKSB8fCB0aGlzO1xuICAgICAgICBfdGhpcy5maWVsZCA9IGZpZWxkO1xuICAgICAgICByZXR1cm4gX3RoaXM7XG4gICAgfVxuICAgIHJldHVybiBWYWxpZGF0aW9uRXJyb3I7XG59KEVycm9yKSk7XG5leHBvcnQgeyBWYWxpZGF0aW9uRXJyb3IgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWVycm9ycy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/helpers.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/helpers.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exists: () => (/* binding */ exists),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isNumber: () => (/* binding */ isNumber),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isString: () => (/* binding */ isString)\n/* harmony export */ });\nfunction isString(obj) {\n    return typeof obj === 'string';\n}\nfunction isNumber(obj) {\n    return typeof obj === 'number';\n}\nfunction isFunction(obj) {\n    return typeof obj === 'function';\n}\nfunction exists(val) {\n    return val !== undefined && val !== null;\n}\nfunction isPlainObject(obj) {\n    return (Object.prototype.toString.call(obj).slice(8, -1).toLowerCase() === 'object');\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLWNvcmVAMS44LjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1jb3JlL2Rpc3QvZXNtL3ZhbGlkYXRpb24vaGVscGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbWVyZ2UvcHJvamVjdC9Db3BpbG90S2l0L2V4YW1wbGVzL2NvYWdlbnRzLXJlc2VhcmNoLWNhbnZhcy91aS9ub2RlX21vZHVsZXMvLnBucG0vQHNlZ21lbnQrYW5hbHl0aWNzLWNvcmVAMS44LjAvbm9kZV9tb2R1bGVzL0BzZWdtZW50L2FuYWx5dGljcy1jb3JlL2Rpc3QvZXNtL3ZhbGlkYXRpb24vaGVscGVycy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gaXNTdHJpbmcob2JqKSB7XG4gICAgcmV0dXJuIHR5cGVvZiBvYmogPT09ICdzdHJpbmcnO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzTnVtYmVyKG9iaikge1xuICAgIHJldHVybiB0eXBlb2Ygb2JqID09PSAnbnVtYmVyJztcbn1cbmV4cG9ydCBmdW5jdGlvbiBpc0Z1bmN0aW9uKG9iaikge1xuICAgIHJldHVybiB0eXBlb2Ygb2JqID09PSAnZnVuY3Rpb24nO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGV4aXN0cyh2YWwpIHtcbiAgICByZXR1cm4gdmFsICE9PSB1bmRlZmluZWQgJiYgdmFsICE9PSBudWxsO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzUGxhaW5PYmplY3Qob2JqKSB7XG4gICAgcmV0dXJuIChPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwob2JqKS5zbGljZSg4LCAtMSkudG9Mb3dlckNhc2UoKSA9PT0gJ29iamVjdCcpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aGVscGVycy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@segment+analytics-core@1.8.0/node_modules/@segment/analytics-core/dist/esm/validation/helpers.js\n");

/***/ })

};
;